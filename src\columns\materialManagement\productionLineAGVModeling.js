// dictionary: true, isEditCell: true 产线工站库位关系建模
export const productionLineAGVModeling = [
    { text: '序号', value: 'Index', width: '80px' },
    { text: '产线', value: 'LineCode', width: '180px', dictionary: true },
    { text: '工站', value: 'EquipmentCode', width: '160px', dictionary: true },
    // { text: '', value: 'ProductWarehouseCode', width: '200px', dictionary: true },
    // { text: '原材料暂存料仓', value: 'StagingWarehouseCode', width: '200px', dictionary: true },
    // { text: 'MES产出虚拟仓', value: 'MesWarehouseCode', width: '200px', dictionary: true },
    // // { text: 'MES产出虚拟仓编码', value: 'MesWarehouseCode', width: '160px' },
    { text: 'AGV上料库区', value: 'AgvAreaName', width: '220px' },
    // { text: 'AGV下料库区编码', value: 'AGVBelongAreaCode', width: '220px' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '120px'
    }
];

<template>
    <!-- 处理告警 -->
    <v-dialog v-model="dialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                <span style="color: #e84d4d">请仔细查看报警详情, 确认当前操作--{{option.title}}</span>
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="mt-6 alarm-home-pdl">
                <v-form ref="form" v-model="valid">
                <v-row>
                    <!-- 工段 -->
                    <v-col :cols="12" class="activ-style activ-txt alarm-bdmr" :lg="6">
                        工段
                    </v-col>
                    <!-- 设备 -->
                    <v-col :cols="12" class="activ-style activ-txt" :lg="6">
                        设备
                    </v-col>
                    <v-col :cols="12" class="activ-style activ-height activ-background alarm-bdmr" :lg="6">
                        <v-text-field class="white-bk" v-model="form.ProductLineName" disabled dense/>
                    </v-col>
                    <!-- 设备 -->
                    <v-col :cols="12" class="activ-style activ-height activ-background" :lg="6">
                        <v-text-field class="white-bk" v-model="form.EquipmentName" disabled dense />
                    </v-col>
                    <!-- 二级分类 -->
                    <v-col :cols="6" class="activ-style activ-txt alarm-bdmr" :lg="6">
                        二级分类
                    </v-col>
                    <!-- 工单号 -->
                    <v-col :cols="6" class="activ-style activ-txt" :lg="6">
                        工单号
                    </v-col>
                    <!-- 二级分类 -->
                    <v-col :cols="6" :lg="6" class="activ-style activ-height activ-background alarm-bdmr">
                        <v-text-field class="white-bk" v-model="form.SubAlarm" disabled dense/>
                    </v-col>
                    <!-- 工单号 -->
                    <v-col :cols="6" :lg="6" class="activ-style activ-height activ-background">
                        <v-text-field class="white-bk" v-model="form.Wo" disabled dense/>
                    </v-col>
                    <!-- 原因 -->
                    <v-col :cols="6" class="activ-style activ-txt alarm-bdmr" :lg="6">
                        原因
                    </v-col>
                    <!-- 人工数量 -->
                    <v-col :cols="6" class="activ-style activ-txt" :lg="6">
                        人工数量
                    </v-col>
                    <!-- 原因 -->
                    <v-col :cols="6" :lg="6" class="activ-style activ-height alarm-bdmr activ-background">
                        <v-select
                            class="white-bk"
                            v-model="form.ReasonCode"
                            :rules="rules.ReasonCode"
                            :items="reasonList"
                            item-text="ReasontreeName"
                            clearable
                            item-value="ReasontreeCode"
                            dense
                            outlined
                        ></v-select>
                    </v-col>
                    <!-- 人工数量 -->
                    <v-col :cols="6" class="activ-style activ-height activ-background" :lg="6">
                        <v-text-field class="white-bk" :rules="rules.ManQuantity" v-model="form.ManQuantity" dense outlined single-line />
                    </v-col>
                    <!-- 设备数量 -->
                    <v-col :cols="6" class="activ-style alarm-bdmr activ-txt" :lg="6">
                        设备数量
                    </v-col>
                    <!-- 设备数量 -->
                    <v-col :cols="6" class="activ-style activ-txt" :lg="6">
                        单位
                    </v-col>
                    <v-col :cols="6" :lg="6" class="activ-style alarm-bdmr activ-height activ-background">
                        <v-text-field class="white-bk" disabled v-model="form.DEVICE_Quantity" dense outlined single-line />
                    </v-col>
                    <v-col :cols="6" :lg="6" class="activ-style activ-height activ-background">
                        <v-select
                            class="white-bk"
                            v-model="form.Unit"
                            :rules="rules.Unit"
                            :items="unitList"
                            item-text="Name"
                            clearable
                            item-value="Name"
                            dense
                            outlined
                        ></v-select>
                    </v-col>
                    <!-- 描述   -->
                    <v-col :cols="12" :lg="12" class="activ-style activ-txt">
                        告警内容
                    </v-col>
                    <!-- 说明 -->
                    <v-col :cols="7" :lg="7" class="activ-style opear-message alarm-bdmr activ-background">
                        <v-textarea disabled class="white-bk" v-model="form.AlarmContent" rows="4" dense/>
                    </v-col>
                    <!-- 操作 -->
                    <v-col :cols="5" :lg="5" class="activ-style opear-message opear-btns activ-background">
                        <div class="white-bk" @click="closeForm">
                            <span class="iconfont icon-cuowu"></span>
                        </div>
                        <div class="agree-btn" @click="submitForm">
                            <span class="iconfont icon-zhengque"></span>
                        </div>
                    </v-col>
                </v-row>
                </v-form>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script>
import { getReasonInfoList } from '@/api/factoryPlant/reasonInfo.js';
import { AlarmRecordDefectSelect } from '@/api/andonManagement/alarmHome.js';
import Util  from '@/util';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            reasonList: [],
            unitList: [],
            defectSelect: {},
            valid: true,
            dialog: false,
            form: {
                ID: '',
                EquipmentName: '',
                ProductLineName: '',
                SubAlarm: '',
                AlarmContent: '',
                Wo: '',
                ReasonCode: '',
                DEVICE_Quantity: '',
                ManQuantity: '',
                Unit: ''
            },
            rules: {
                // ManQuantity: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            },
            option: {}
        };
    },
    watch: {
        dialog: {
            async handler(curVal) {
                if (curVal) {
                    this.defectSelect =  await this.AlarmRecordDefectSelect(this.operaObj.ID)
                    for (const key in this.form) {
                        if (Object.hasOwnProperty.call(this.form, key)) {
                            this.form[key] = this.operaObj[key];
                        }
                    }
                    if(this.defectSelect){
                        const { ReasonCode, ManQuantity, DEVICE_Quantity, Unit } = this.defectSelect
                        this.form.ReasonCode = ReasonCode
                        this.form.ManQuantity = ManQuantity
                        this.form.DEVICE_Quantity = DEVICE_Quantity
                        this.form.Unit = Unit
                    }
                    this.option = this.operaObj.detailObj
                }
            },
            deep: true,
            immediate: true
        }
    },
    async created(){
        this.getReasonList()
        this.unitList = await Util.getUnitList()
    },
    methods: {
        //
        closeForm() {
            this.$refs.form.resetValidation()
            this.$refs.form.reset() 
            this.dialog = false;
        },
        // 当前是否编辑
        async AlarmRecordDefectSelect(id) {
            let result = null
            try {
                const res = await AlarmRecordDefectSelect({id});
                const { success, response } = res;
                if(success){
                    result = response
                }  
            } catch (error) {
                console.log(error);
            }
            return result 
        },
        // 获取原因树
        async getReasonList() {
             let params = {
                key: "",
                pageIndex: 1,
                pageSize: 999
            };
            const res = await getReasonInfoList(params);
            const { success, response } = res;
            if(success){
                this.reasonList = response.data || []
            }
        },
        // 提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                const params = {
                    AlarmEventId: this.operaObj.ID,
                    WoId: this.operaObj.Wo,
                    DEVICE_Quantity: this.form.DEVICE_Quantity,
                    ReasonCode: this.form.ReasonCode,
                    Unit: this.form.Unit,
                    ManQuantity: this.form.ManQuantity
                }
                const res = await this.option.fn(params);
                const { success, msg } = res;
                if(success){
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    this.$emit('handlePopup', 'refresh');
                    this.$refs.form.reset()
                    this.closeForm()
                }else{
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'error' });
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.activ-txt{
    line-height: 6px;
}
.activ-style{
    border: 1px solid #bdbdbd;
    border-bottom: none;
}
.activ-height{
    height: 60px;
}
.alarm-message{
    height: 80px;
}
.opear-message{
    height: 130px;
    border-bottom: 1px solid #bdbdbd;
}
.opear-btns{
    display: flex;
    justify-content: space-around;
    align-items: center;
    div{
        cursor: pointer;
        border: 1px solid gainsboro;
        height: 100%;
        width: 40%;
        display: flex;
        justify-content: space-around;
        align-items: center;
    }
    .agree-btn{
        background: #f2c85d;
    }
}
.white-bk{
    background: #fff;
}
.alarm-bdmr{
    border-right: none;
}
// .activ-style:last-child{
//     border-bottom: 1px solid;
// }
.col-lg-6.col-12,
.col-lg-6.col-6,
.col-6,
.col-12
.col-lg-6,
.col-lg-12 {
    padding: 6x;
}
</style>
<style lang="scss">
.alarm-home-pdl{
    .v-text-field.v-text-field--enclosed .v-text-field__details{
        margin-bottom: 0;
    }
    .v-text-field__details{
        display: none;
    }
    .activ-background{
        background: #f5f5f5
    }
    .iconfont{
        font-size: 80px;
    }
    .v-input--dense > .v-input__control > .v-input__slot{
        margin-bottom: 1px;
    }
    legend{
        display: none;
    }
    .v-text-field--outlined fieldset{
        top: -1px;
    }
}
</style>
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_30015'


// 列表
export function getInfluxOpcTaglList(data) {
  const api = '/api/InfluxOpcTag/GetPageModelList'
  return getRequestResources(baseURL, api, 'post', data);
}

// 保存
export function influxOpcTagSaveForm(data) {
  const api = '/api/InfluxOpcTag/SaveForm'
  return getRequestResources(baseURL, api, 'post', data);
}

// 删除
export function influxOpcTagDelete(data) {
  const api = '/api/InfluxOpcTag/Delete'
  return getRequestResources(baseURL, api, 'post', data);
}
// 导出
export function InfluxOpcTagExport(data) {
  const api = '/api/InfluxOpcTag/ExportData2'
  return getRequestResources(baseURL, api, 'post', data);
}




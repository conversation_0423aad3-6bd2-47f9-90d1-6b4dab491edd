// 叫料单列表列配置
export const CallMaterialSheetColumn = [
  { text: 'callMaterialSheet.callOrderNo', value: 'RequestSheetNo', width: 150 },
  { text: 'callMaterialSheet.workOrder', value: 'ProductionOrder', width: 150 },
  { text: 'callMaterialSheet.callerId', value: 'CreateUserId', width: 120 },
  { text: 'callMaterialSheet.callTime', value: 'RequestTime', width: 180 },
  { text: 'callMaterialSheet.lineSideWarehouse', value: 'LineCode', width: 120 },
  { text: 'callMaterialSheet.callPoint', value: 'PositionCode', width: 120 },
  { text: 'callMaterialSheet.callStatus', value: 'CallStatus', width: 100 },
  { text: 'GLOBAL._BZ', value: 'remark', width: 150 },
  { text: 'GLOBAL._ACTIONS', value: 'actions', width: 200, align: 'center' }
];

// 叫料单明细列配置
export const CallMaterialSheetDetailColumn = [
  { text: 'material.code', value: 'MaterialCode', width: 120 },
  { text: 'material.name', value: 'MaterialName', width: 150 },
  { text: 'material.specification', value: 'MaterialVersionCode', width: 120 },
  { text: 'material.unit', value: 'Unit', width: 80 },
  { text: 'material.requiredQuantity', value: 'RequestQty', width: 100 },
  { text: 'material.actualQuantity', value: 'ActualQty', width: 100 },
  { text: 'material.batchNo', value: 'BatchNo', width: 120 },
  { text: 'material.palletNo', value: 'PalletNo', width: 120 },
  { text: 'GLOBAL._BZ', value: 'remark', width: 150 }
];
<template>
    <v-dialog v-model="showDialog" max-width="1080px">
        <v-card>
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                <!-- 设备录入 -->
                {{ dialogType == 'add' ? $t('GLOBAL._XZ') : $t('GLOBAL._BJ') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form" v-model="valid">
                    <v-card class="ma-1" outlined>
                        <v-card-text>
                            <v-row class="pt-8">
                                <v-col class="py-0 px-3" cols="12" sm="4" md="4" v-for="(item, index) in SbxxList" :key="index">
                                    <v-text-field v-if="item.type == 'input'" :id="item.id + 'SbxxList'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                                    <v-autocomplete
                                        v-if="item.type == 'select'"
                                        :id="item.id + 'SbxxList'"
                                        clearable
                                        v-model="item.value"
                                        :items="item.options"
                                        item-text="ItemName"
                                        item-value="ItemValue"
                                        :label="item.label"
                                        clear
                                        dense
                                        outlined
                                    ></v-autocomplete>
                                    <el-date-picker v-if="item.type == 'date'" :placeholder="item.label" v-model="item.value" :id="item.id + 'SbxxList'" :type="item.datetype"></el-date-picker>
                                </v-col>
                            </v-row>
                        </v-card-text>
                    </v-card>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { DeviceCategoryPropSaveForm } from '@/api/equipmentManagement/DeviceCategoryProp.js';

import { Message, MessageBox } from 'element-ui';
export default {
    props: {
        dialogType: {
            type: String,
            default: ''
        },
        DeviceCategoryId: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            menu: [],
            SbxxList: [
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.Code'),
                    value: '',
                    id: 'Code',
                    required: true,
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.Name'),
                    value: '',
                    id: 'Name',
                    required: true,
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.GroupName'),
                    value: '',
                    required: true,
                    id: 'GroupName',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.DataType'),
                    value: '',
                    required: true,
                    id: 'DataType',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.DefaultValue'),
                    value: '',
                    required: true,
                    id: 'DefaultValue',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.Remark'),
                    value: '',
                    id: 'Remark',
                    type: 'input'
                }
            ]
        };
    },
    computed: {
        locale() {
            return this.$store.state.app.locale || 'zh';
        }
    },
    watch: {},
    mounted() {
        this.SbxxList.forEach(item => {
            if (item.required) {
                item.label = item.label + '*';
            }
        });
    },
    methods: {
        closeEquip() {
            this.showDialog = false;
            this.SbxxList.forEach(item => {
                item.value = '';
            });
        },
        async addSave() {
            let flag = this.SbxxList.some(item => {
                if (item.required) {
                    return item.value == '' || item.value == null;
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            let params = {};
            this.SbxxList.forEach(item => {
                params[item.id] = item.value;
            });
            params.DeviceCategoryId = this.DeviceCategoryId;
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            if (this.dialogType == 'editFile') {
                params.ID = this.tableItem.ID;
            }
            const res = await DeviceCategoryPropSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$emit('load', this.tableItem);
                // this.$parent.$parent.GetFactorylineTree();
                this.showDialog = this.classcheckbox ? false : true;
                if (this.classcheckbox) {
                    this.SbxxList.forEach(item => {
                        item.value = '';
                    });
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
}

.ant-calendar-picker {
    ::v-deep input {
        border-color: #9e9e9e;
    }

    ::v-deep i {
        color: #757575;
    }
}
</style>

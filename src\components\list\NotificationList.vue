<template>
    <v-card tile class="notes">
        <!-- <v-toolbar tile flat>
            <v-subheader>通知</v-subheader>
            <v-spacer />
            <v-btn text @click="handleClearNotification">清除</v-btn>
        </v-toolbar> -->
        <!-- <v-divider /> -->
        <v-card-text class="pa-2">
            <v-list dense class="pa-0 notes_list">
                <template v-for="(item, index) in items">
                    <v-list-item :key="index" @click="handleClick(item)">
                        <!-- <v-list-item-avatar :color="item.color">
                            <v-icon dark>{{ item.icon }}</v-icon>
                        </v-list-item-avatar> -->
                        <v-list-item-content>
                            <v-list-item-subtitle v-html="item.AlarmName"></v-list-item-subtitle>
                        </v-list-item-content>
                        <v-list-item-action style="" class="caption">
                            {{ item.UNREAD }}
                        </v-list-item-action>
                    </v-list-item>
                    <v-divider :key="'d' + index" />
                </template>
            </v-list>
            <!-- <v-divider></v-divider> -->
            <!-- <v-btn block text class="ma-0">全部</v-btn> -->
            <!-- <v-divider></v-divider> -->
        </v-card-text>
    </v-card>
</template>

<script>
import { readNotice } from '@/api/andonManagement/alarmHome.js'
export default {
    props: {
        items: {
            type: Array,
            default: () => [

            ]
        }
    },
    data() {
        return {
            list: [{ title: '质量', timeLabel: '12121' },
            { title: '生产', timeLabel: '98' },
            { title: '安全', timeLabel: '12121' },
            { title: '设备', timeLabel: '12121' },
            ]
        };
    },
    methods: {
        async handleClick(data) {
            try {
                //let resp = await readNotice({ mainAlarmType: data.AlarmCode })
                let currentPath = this.$route.path
                this.$store.commit('SETANDONCODETYPE', { code: data.AlarmCode, path: currentPath })
            } catch {
                console.log("error")
            }
        },
        handleClearNotification() {
            this.$store.dispatch('clearNotificaton');
        }
    }
};
</script>
<style lang="sass" scoped>
.notes_list
    max-height: 360px
    overflow-y: auto
// .v-list-item
//     height: 24px !important
</style>

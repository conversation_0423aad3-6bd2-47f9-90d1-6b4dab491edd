// 告警升级规则
<template>
    <div class="line-side-view">
        <div class="line-side-main overflow-auto">
            <SearchForm ref="contactTorm" class="mt-2" @selectChange="selectChange" :searchinput="searchinput" :show-from="showFrom" @searchForm="searchForm" />
            <v-card outlined>
                <div class="form-btn-list">
                    <!-- 搜索栏 -->
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary">
                        <v-icon @click="getDataList">mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'BJSJGZ_ADD'" @click="operaClick({})">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="error" v-has="'BJSJGZ_ALLREMOVE'" @click="sureItems()" :disabled="selectedList.length == 0">{{ $t('GLOBAL._PLSC') }}</v-btn>
                </div>
                <Tables
                    :headers="headers"
                    :desserts="desserts"
                    :loading="loading"
                    :page-options="pageOptions"
                    :btn-list="btnList"
                    :dictionaryList="dictionaryList"
                    @selectePages="selectePages"
                    table-name="ANDON_BJSJGZ"
                    @itemSelected="selectedItems"
                    @toggleSelectAll="selectedItems"
                    @tableClick="tableClick"
                >
                    <template #NoticeType="{ item }">
                        <span>{{ getNoticeTypeText(item.NoticeType) }}</span>
                    </template>
                </Tables>
            </v-card>
            <update-dialog
                ref="updateDialog"
                :dutyList="dutyList"
                :opera-obj="operaObj"
                :noticeTypeList="noticeTypeList"
                :ResponseLevelSendList="ResponseLevelSendList"
                :dealModeList="dealModeList"
                :NoterTypeList="NoterTypeList"
                :IsDownAllSendList="IsDownAllSendList"
                :NoterDepartList="NoterDepartList"
                @handlePopup="handlePopup"
            ></update-dialog>
            <dutyDialog ref="dutyDialog" :needOnTypes="needOnTypes" :opera-obj="operaObj" />
        </div>
    </div>
</template>
<script>
import physicalModel from '@/mixins/physicalModel';
import { GetListByLevel } from '@/api/common.js';
import { getAlarmTypeRootList, GetListByAlarmId, getAlarmTypeTreetList } from '@/api/andonManagement/alarmType.js';
import { getUpgradeRuleList, DeleteUpgradeRule } from '@/api/andonManagement/upgradeRule.js';
import { getAlarmgroups } from '@/api/andonManagement/alarmGroup.js';
import { upgradeRuleColum } from '@/columns/andonManagement/upgradeRule.js';
import { getRoleList } from '../../systemManagement/roleManagement/service.js';
export default {
    name: 'UpgradeRule',
    components: {
        UpdateDialog: () => import('./components/updateDialog.vue'),
        dutyDialog: () => import('./components/dutyDialog.vue')
    },
    mixins: [physicalModel],
    data() {
        return {
            operaObj: {},
            showFrom: false,
            headers: upgradeRuleColum,
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            deleteId: [],
            selectedList: [],
            desserts: [],
            searchParams: {},
            dutyList: [],
            alarmTypeList: [],
            alarmTypeRootList: [],
            subAlarmTypeList: [],
            noticeTypeList: [],
            dealModeList: [],
            NoterTypeList: [],
            NoterDepartList: [],
            dealTypeList: [],
            MainAlarmType: '',
            SubAlarmType: '',
            needOnTypes: [],
            ResponseLevelSendList: [
                { ItemName: '线体', ItemValue: 'AREA' },
                { ItemName: '工段', ItemValue: 'PRODUCTLINE' }
            ],
            IsDownAllSendList: [
                { ItemName: '是', ItemValue: 'yes' },
                { ItemName: '否', ItemValue: 'no' }
            ]
        };
    },
    computed: {
        //查询条件
        searchinput() {
            return [
                // 一级分类
                {
                    key: 'MainAlarmType',
                    icon: '',
                    type: 'combobox',
                    search: 'MainAlarmType',
                    linkageKey: 'fivthLevel',
                    childrenLinkageArray: this.alarmTypeList,
                    selectData: this.$changeSelectItems(this.alarmTypeRootList, 'AlarmCode', 'AlarmName'),
                    value: this.$store.state.searchForm.MainAlarmType,
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.MainAlarmType')
                },
                // 二级分类
                {
                    key: 'SubAlarmType',
                    icon: '',
                    type: 'combobox',
                    search: 'SubAlarmType',
                    linkageKey: 'sixthLevel',
                    childrenLinkageArray: [],
                    selectData: this.$changeSelectItems(this.sixthLevel, 'AlarmCode', 'AlarmName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.SubAlarmType')
                }
                // // 一级分类
                // {
                //     key: 'MainAlarmType',
                //     icon: '',
                //     type: 'combobox',
                //     selectData: this.$changeSelectItems(this.alarmTypeRootList, 'AlarmCode', 'AlarmName'),
                //     value: this.MainAlarmType,
                //     label: this.$t('$vuetify.dataTable.ANDON_BJSJGZ.MainAlarmType')
                // },
                // // 二级分类
                // {
                //     key: 'SubAlarmType',
                //     icon: '',
                //     type: 'combobox',
                //     selectData: this.$changeSelectItems(this.subAlarmTypeList, 'AlarmCode', 'AlarmName'),
                //     value: this.SubAlarmType,
                //     label: this.$t('$vuetify.dataTable.ANDON_BJSJGZ.SubAlarmType')
                // }
            ];
        },
        btnList() {
            return [
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary', authCode: 'BJSJGZ_EDIT' },
                // { text: "接警组详情", icon: '', code: 'view', type: 'primary' },
                { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'error', authCode: 'BJSJGZ_DELETE' }
            ];
        },
        dictionaryList() {
            return [
                { arr: this.alarmTypeList, key: 'MainAlarmType', val: 'AlarmCode', text: 'AlarmName' },
                // {arr: this.dutyList, key: 'Duty', val: 'GroupCode', text: 'GroupName'},
                { arr: this.alarmTypeList, key: 'SubAlarmType', val: 'AlarmCode', text: 'AlarmName' },
                // { arr: this.noticeTypeList, key: 'NoticeType', val: 'ItemValue', text: 'ItemName' },
                { arr: this.IsDownAllSendList, key: 'IsDownAllSend', val: 'ItemValue', text: 'ItemName' },
                { arr: this.ResponseLevelSendList, key: 'ResponseLevel', val: 'ItemValue', text: 'ItemName' },
                { arr: this.dealModeList, key: 'DealMode', val: 'ItemValue', text: 'ItemName' },
                { arr: this.NoterTypeList, key: 'NoterType', val: 'ItemValue', text: 'ItemName' },
                { arr: this.NoterDepartList, key: 'NoterDepart', val: 'ItemValue', text: 'ItemName' }
            ];
        }
    },
    async created() {
        this.getalarmTypeList();
        await this.init();
        await this.getDutyList();
        await this.getRoleDic();
        this.getAlarmTypeList();
        this.GetListByLevel();
        this.getDataList();
    },
    methods: {
        getNoticeTypeText(valueStr) {
            let text = '';
            if (valueStr != null) {
                valueStr.split(',').forEach(item => {
                    let target = this.noticeTypeList.find(itm => itm.ItemValue == item);
                    text += `,${target.ItemName}`;
                });
            }
            return text.slice(1);
        },
        // 获取接警组列表
        async getDutyList() {
            const res = await getAlarmgroups({});
            const { success, response } = res || {};
            if (success) {
                this.dutyList = response;
            } else {
                this.dutyList = [];
            }
        },
        async getRoleDic() {
            const res = await getRoleList({});
            const { success, response } = res || {};
            if (success) {
                this.NoterDepartList = response;
            } else {
                this.NoterDepartList = [];
            }
        },
        // // 获取接警组列表
        // async getDutyList() {
        //     this.dutyList = await Util.GetDepartmentByLevel('post');
        // },
        async init() {
            this.noticeTypeList = await this.$getDataDictionary('AndonPushType');
            this.dealModeList = await this.$getDataDictionary('DealMode');
            this.NoterTypeList = await this.$getDataDictionary('NoterType');
            this.needOnTypes = await this.$getDataDictionary('andon_IsNeedOn');
        },
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        // 操作栏按钮
        tableClick(item, type) {
            const MainAlarmName = this.$getDictionaryVal(item.MainAlarmType, this.alarmTypeList, 'AlarmCode', 'AlarmName');
            const SubAlarmName = this.$getDictionaryVal(item.SubAlarmType, this.alarmTypeList, 'AlarmCode', 'AlarmName');
            switch (type) {
                // 编辑
                case 'edit':
                    this.operaClick(item);
                    break;
                // 查看岗位
                case 'view':
                    this.operaObj = { MainAlarmName, SubAlarmName, Duty: item.Duty || '', EventLevel: item.EventLevel };
                    this.$refs.dutyDialog.dialog = true;
                    break;
                // 删除
                case 'delete':
                    this.deleteId = item?.ID;
                    this.sureDelete();
                    break;
                default:
                    break;
            }
        },
        // 获取工段
        async GetListByLevel() {
            let params = {
                key: 'Line' // 工段
            };
            this.productLine = [];
            const res = await GetListByLevel(params);
            let { success, response } = res;
            if (success) {
                this.productLine = response;
            }
        },
        // 获取大类列表
        async getAlarmTypeList() {
            const res = await getAlarmTypeRootList({});
            const { success, response } = res || {};
            if (success) {
                this.alarmTypeRootList = response;
            } else {
                this.alarmTypeRootList = [];
            }
        },
        // 获取子级
        async getTypeChildList(alarmId) {
            this.subAlarmTypeList = [];
            const res = await GetListByAlarmId({ alarmId });
            const { success, response } = res || {};
            if (success) {
                this.subAlarmTypeList = response;
                console.log(this.subAlarmTypeList);
            }
        },
        // 获取告警类型列表
        async getalarmTypeList() {
            const res = await getAlarmTypeTreetList({});
            this.alarmTypeList = [];
            const { success, response } = res || {};
            if (response && success) {
                response.forEach(e => {
                    this.alarmTypeList.push(e);
                    const { children } = e;
                    if (children && children.length) {
                        children.forEach(i => {
                            this.alarmTypeList.push(i);
                        });
                    }
                });
            }
        },
        // // 下拉框操作
        // selectChange(v, p) {
        //     const { key, value } = v || {};
        //     if (key == 'MainAlarmType') {
        //         const { MainAlarmType } = p;
        //         this.MainAlarmType = MainAlarmType;
        //         this.SubAlarmType = '';
        //         const obj = this.alarmTypeRootList.find(i => i.AlarmCode == value);
        //         const result = this.alarmTypeList.filter(i => i.ParentId == obj?.ID);
        //         this.subAlarmTypeList = result;
        //     }
        // },
        // 获取全部表格数据
        async getDataList() {
            this.loading = true;
            let params = {
                ...this.searchParams,
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await getUpgradeRuleList(params);
            const { success, response } = res || {};
            const { data, dataCount, page } = response || {};
            this.desserts = [];
            if (success && data) {
                const arr = data || [];
                arr.forEach(e => {
                    this.desserts.push({ ...e, actionsBtnShow: 'Status' });
                });
                this.pageOptions.total = dataCount;
                this.pageOptions.page = page;
            } else {
                this.desserts = [];
            }
            this.loading = false;
        },
        // 查询数据
        searchForm(v) {
            this.searchParams = v;
            this.getDataList();
        },
        // 新增
        operaClick(o) {
            this.operaObj = o || {};
            this.$refs.updateDialog.dialog = true;
        },
        // 批量删除
        sureItems() {
            this.deleteId = '';
            this.sureDelete();
        },
        // 删除二次确认
        sureDelete() {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    const params = [];
                    if (this.deleteId) {
                        params.push(this.deleteId);
                    } else {
                        this.selectedList.forEach(e => {
                            params.push(e.ID);
                        });
                    }
                    const res = await DeleteUpgradeRule(params);
                    this.selectedList = [];
                    this.deleteId = '';
                    const { success, msg } = res;
                    if (success) {
                        this.pageOptions.pageCount = 1;
                        this.getDataList();
                        this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 根据子组件返回来值
        handlePopup(type, data) {
            this.getDataList();
            // switch (type) {
            //     case 'refresh':
            //         this.getDataList();
            //         break;
            //     case 'detail':
            //         this.receivedorderid = data?.ID
            //         this.$refs.materailDetailDialog.dialog = true;
            //         break;
            //     default:
            //         break;
            // }
        }
    }
};
</script>
<style lang="scss" scoped>
.line-side-view {
    display: flex;

    .line-side-main {
        flex: 1;
        width: 100%;

        .v-data-table {
            width: 100%;
        }
    }
}
</style>
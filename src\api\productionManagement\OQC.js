import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TRACE; // 配置服务url
//获取测试列表
export function BatchGetPageList(data) {
    return request({
        url: baseURL + '/trace/Batch/GetOqcBatchPageList',
        method: 'post',
        data
    });
}

// 查询首页列表 
export function oqcBatchData(data) {
    return request({
        url: baseURL + '/trace/wo/oqcBatchData',
        method: 'post',
        data
    });
}
//扫码单个批次号
export function oqcSingleBatchScanData(data) {
    return request({
        url: baseURL + '/trace/wo/oqcSingleBatchScanData',
        method: 'post',
        data
    });
}

//扫码弹窗列表
// export function oqcBatchScanData(data) {
//     return request({
//         url: baseURL + '/trace/wo/oqcBatchScanData',
//         method: 'post',
//         data
//     });
// }
//
// export function cyintoBatchBadQuantity(data) {
//     return request({
//         url: baseURL + '/trace/wo/cyintoBatchBadQuantity',
//         method: 'post',
//         data
//     });
// }
//隔离 通过
export function oqcBatchCommit(data) {
    return request({
        url: baseURL + '/trace/wo/oqcBatchCommit',
        method: 'post',
        data
    });
}
//隔离 通过新接口
export function OqcoqcCommit(data) {
    return request({
        url: baseURL + '/trace/Oqc/oqcCommit',
        method: 'post',
        data
    });
}
//查询新接口
export function OqcGetPageList(data) {
    return request({
        url: baseURL + '/trace/Oqc/GetPageList',
        method: 'post',
        data
    });
}
//查询接口
export function OqcGetOqcData(data) {
    return request({
        url: baseURL + '/trace/Oqc/GetOqcData',
        method: 'post',
        data
    });
}




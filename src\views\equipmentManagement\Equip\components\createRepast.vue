<template>
    <v-dialog v-model="showDialog" max-width="1080px">
        <v-card v-if="dialogType == 'add' || dialogType == 'addedit'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                <!-- 设备录入 -->
                {{ dialogType == 'add' ? $t('GLOBAL._XZ') : $t('GLOBAL._BJ') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text mytree" style="display: flex">
                <TreeView :items="treeData" :activeKey="activeKey" :title="$t('TPM_SBGL_SBTZGL._SBFL')" @clickClassTree="clickClassTree"></TreeView>
                <v-form ref="form" v-model="valid" v-if="DeviceCategoryId != ''">
                    <v-card class="ma-1" outlined>
                        <v-card-title class="">{{ $t('TPM_SBGL_SBTZGL._SBXX') }}</v-card-title>
                        <v-card-text>
                            <v-row class="pt-8">
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3" v-for="(item, index) in SbxxList" :key="index">
                                    <v-text-field v-if="item.type == 'input'" :id="item.id + 'SbxxList'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                                    <v-autocomplete
                                        v-if="item.type == 'select'"
                                        :id="item.id + 'SbxxList'"
                                        clearable
                                        v-model="item.value"
                                        :items="item.option"
                                        item-text="ItemName"
                                        item-value="ItemValue"
                                        :label="item.label"
                                        clear
                                        dense
                                        outlined
                                    ></v-autocomplete>
                                    <el-date-picker v-if="item.type == 'date'" :placeholder="item.label" v-model="item.value" :id="item.id + 'SbxxList'" :type="item.datetype"></el-date-picker>
                                </v-col>
                            </v-row>
                        </v-card-text>
                    </v-card>
                    <v-card class="ma-1" outlined>
                        <v-card-title class="">{{ $t('TPM_SBGL_SBTZGL._SBSM') }}</v-card-title>
                        <v-card-text>
                            <v-row class="pt-8">
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3" v-for="(item, index) in SbsmList" :key="index">
                                    <v-text-field v-if="item.type == 'input'" :id="item.id + 'SbsmList'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                                    <v-autocomplete
                                        :multiple="item.multiple"
                                        v-if="item.type == 'select'"
                                        :id="item.id + 'SbsmList'"
                                        clearable
                                        v-model="item.value"
                                        :items="item.option"
                                        item-text="ItemName"
                                        item-value="ItemValue"
                                        :label="item.label"
                                        clear
                                        dense
                                        outlined
                                    ></v-autocomplete>
                                    <v-menu
                                        v-if="item.type == 'date' || item.type == 'datetime'"
                                        :ref="'menu' + index"
                                        v-model="menu[index]"
                                        :close-on-content-click="false"
                                        :nudge-right="40"
                                        transition="scale-transition"
                                        offset-y
                                        max-width="290px"
                                        min-width="290px"
                                    >
                                        <template #activator="{ on, attrs }">
                                            <v-text-field
                                                v-model="item.value"
                                                :clearable="item.isClearable ? item.isClearable : true"
                                                outlined
                                                dense
                                                :label="item.label"
                                                readonly
                                                v-bind="attrs"
                                                v-on="on"
                                            ></v-text-field>
                                        </template>
                                        <v-date-picker :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></v-date-picker>
                                    </v-menu>
                                </v-col>
                            </v-row>
                        </v-card-text>
                    </v-card>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" :disabled="DeviceCategoryId == ''" @click="addSave(dialogType)">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card v-if="dialogType == 'edit'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                <!-- 修改录入 -->
                {{ $t('GLOBAL._BJ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form" v-model="valid">
                    <v-card class="ma-1" outlined>
                        <v-card-title class="">{{ $t('TPM_SBGL_SBTZGL._SBXX') }}</v-card-title>
                        <v-card-text>
                            <v-row class="pt-8">
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field v-model="editedItem.Code" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Code')"></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field v-model="editedItem.Name" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Name')"></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-autocomplete
                                        clearable
                                        v-model="editedItem.Typecode"
                                        :items="equipmentGroup"
                                        item-text="ItemName"
                                        item-value="ItemValue"
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Type')"
                                        clear
                                        return-object
                                        dense
                                        outlined
                                    ></v-autocomplete>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-select
                                        v-model="editedItem.Status"
                                        :items="equipmentStatus"
                                        item-text="ItemName"
                                        item-value="ItemValue"
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Status')"
                                        clearable
                                        dense
                                        outlined
                                    ></v-select>
                                </v-col>

                                <!-- <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field v-model="editedItem.EamCode" outlined dense
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EamCode')"></v-text-field>
                                </v-col> -->
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field v-model="editedItem.EipCode" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EipCode')"></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field v-model="editedItem.LeaveCode" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.LeaveCode')"></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field v-model="editedItem.Eqmodel" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Eqmodel')"></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field v-model="editedItem.BarCode" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.BarCode')"></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field v-model="editedItem.Purchasedate" type="datetime-local" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Purchasedate')"></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field v-model="editedItem.Manufacturer" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Manufacturer')"></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field v-model="editedItem.Contacts" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Contacts')"></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field v-model="editedItem.Tel" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Tel')"></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-autocomplete
                                        v-model="editedItem.PersonCode"
                                        :items="peopleitems"
                                        item-value="Code"
                                        item-text="Name"
                                        flat
                                        outlined
                                        dense
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.PersonName')"
                                    >
                                        <template #item="data">
                                            <template v-if="typeof data.item !== 'object'">
                                                <v-list-item-content v-text="data.item"></v-list-item-content>
                                            </template>
                                            <template v-else>
                                                <v-list-item-content>
                                                    <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                                    <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                                </v-list-item-content>
                                            </template>
                                        </template>
                                    </v-autocomplete>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field
                                        type="datetime-local"
                                        v-model="editedItem.FirstMaintenanceTime"
                                        dense
                                        outlined
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.FirstMaintenanceTime')"
                                    />
                                </v-col>
                            </v-row>
                        </v-card-text>
                    </v-card>
                    <v-card class="ma-1" outlined>
                        <v-card-title class="">{{ $t('TPM_SBGL_SBTZGL._SBSM') }}</v-card-title>
                        <v-card-text>
                            <v-row class="pt-8">
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field v-model="editedItem.Age" type="Number" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Age')"></v-text-field>
                                </v-col>
                                <!-- <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field v-model="editedItem.Owner" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Owner')"></v-text-field>
                                </v-col> -->
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field v-model="editedItem.Cost" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Cost')"></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-text-field
                                        v-model="editedItem.Productiondate"
                                        type="datetime-local"
                                        outlined
                                        dense
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Productiondate')"
                                    ></v-text-field>
                                </v-col>
                                <!-- <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-file-input accept=".xls,.xlsx" label="操作说明书" truncate-length="14" @change="czbooks" outlined dense></v-file-input>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-file-input accept=".xls,.xlsx" label="保养说明书" truncate-length="14" @change="bybooks" outlined dense></v-file-input>
                                </v-col> -->
                                <v-col class="py-0 px-3" cols="12" sm="9" md="9">
                                    <v-text-field v-model="editedItem.Remark" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Remark')"></v-text-field>
                                </v-col>
                            </v-row>
                        </v-card-text>
                    </v-card>
                    <v-card class="ma-1" outlined>
                        <v-card-title class="">{{ $t('TPM_SBGL_SBTZGL._SBCX') }}</v-card-title>
                        <v-card-text>
                            <v-row class="pt-8">
                                <!-- <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <Treeselect noChildrenText="暂无数据"  :placeholder="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Depareaid')" noOptionsText="暂无数据" v-model="editedItem.Depareaid" :normalizer="normalizer" :option="floorList" />
                                </v-col> -->
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-autocomplete
                                        v-model="editedItem.ProductlineCode"
                                        :items="productionlineList"
                                        item-text="EquipmentName"
                                        item-value="EquipmentCode"
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.ProductlineName')"
                                        @change="changeV"
                                        clearable
                                        dense
                                        outlined
                                    ></v-autocomplete>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-autocomplete
                                        v-model="editedItem.LineCode"
                                        :items="lineCodeList"
                                        item-text="EquipmentName"
                                        item-value="EquipmentCode"
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.LineName')"
                                        @change="changeL"
                                        clearable
                                        dense
                                        outlined
                                    ></v-autocomplete>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-autocomplete
                                        v-model="editedItem.SegmentCode"
                                        :items="lineCodeGZList"
                                        item-text="EquipmentName"
                                        item-value="EquipmentCode"
                                        label="工站"
                                        clearable
                                        dense
                                        outlined
                                    ></v-autocomplete>
                                </v-col>
                            </v-row>
                        </v-card-text>
                    </v-card>
                    <v-card class="ma-1" outlined>
                        <v-card-title class="">{{ $t('TPM_SBGL_SBTZGL._SBPJ') }}</v-card-title>
                        <v-card-text>
                            <v-row class="pt-8">
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-checkbox v-model="editedItem.Smq" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Smq')"></v-checkbox>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-checkbox v-model="editedItem.Dyj" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Dyj')"></v-checkbox>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                                    <v-checkbox v-model="editedItem.Pmj" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Pmj')"></v-checkbox>
                                </v-col>
                            </v-row>
                        </v-card-text>
                    </v-card>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card v-if="dialogType == 'addList'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                <!-- 新增备品备件 -->
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <el-select
                            v-model="Listform.Part"
                            filterable
                            remote
                            reserve-keyword
                            :placeholder="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Name') + '*'"
                            :remote-method="remoteMethod"
                            :loading="partsloading"
                        >
                            <el-option v-for="item in PartList" :key="item.ID" :label="item.ItemName" :value="item.ItemValue"></el-option>
                        </el-select>
                    </v-col>
                    <!-- <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="Listform.Name" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Name')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="Listform.Code" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Code')"></v-text-field>
                    </v-col> -->
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select
                            v-model="Listform.Type"
                            :items="equipmentSpareType"
                            item-text="ItemName"
                            item-value="ItemName"
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Jigtype') + '*'"
                            clearable
                            dense
                            outlined
                        ></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="Listform.Qty" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Qty') + '*'"></v-text-field>
                    </v-col>
                    <!-- <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="Listform.Model" outlined dense :label="$t('TPM_SBGL_BJYJCX._GGXH')"></v-text-field>
                    </v-col> -->
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addListSave('addList')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card class="" v-if="dialogType == 'editList'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                <!-- 修改备品备件 -->
                {{ $t('GLOBAL._BJ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <el-select
                            v-model="editedItem.Part"
                            filterable
                            remote
                            reserve-keyword
                            :placeholder="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Name') + '*'"
                            :remote-method="remoteMethod"
                            :loading="partsloading"
                        >
                            <el-option v-for="item in PartList" :key="item.ID" :label="item.ItemName" :value="item.ItemValue"></el-option>
                        </el-select>
                    </v-col>
                    <!-- <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Name" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Name')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Code" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Code')"></v-text-field>
                    </v-col> -->
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select
                            v-model="editedItem.Type"
                            :items="equipmentSpareType"
                            item-text="ItemName"
                            item-value="ItemName"
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Jigtype') + '*'"
                            clearable
                            dense
                            outlined
                        ></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field type="number" v-model="editedItem.Qty" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Qty') + '*'"></v-text-field>
                    </v-col>
                    <!-- <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Model" outlined dense :label="$t('TPM_SBGL_BJYJCX._GGXH')"></v-text-field>
                    </v-col> -->
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addListSave('editList')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card class="" v-if="dialogType == 'addTarget'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                <!-- 新增指标 -->
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="TGRform.RunningBeat" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBZB.RunningBeat')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="TGRform.BeatSpeeding" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBZB.BeatSpeeding')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="TGRform.MachineHour" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBZB.MachineHour')"></v-text-field>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addTARSave('addTAR')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card class="" v-if="dialogType == 'editTarget'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                <!-- 修改指标 -->
                {{ $t('GLOBAL._BJ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.RunningBeat" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBZB.RunningBeat')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.BeatSpeeding" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBZB.BeatSpeeding')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.MachineHour" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBZB.MachineHour')"></v-text-field>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addTARSave('editTAR')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card class="" v-if="dialogType == 'addFile'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                <!-- 新增指标 -->
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="Fileform.Name" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.FileName') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="Fileform.Version" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.FileVersion')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-select
                            v-model="Fileform.Type"
                            :items="DocType"
                            item-text="ItemName"
                            item-value="ItemName"
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.Type') + '*'"
                            clearable
                            dense
                            outlined
                        ></v-select>
                        <!-- <v-text-field v-model="Fileform.Type" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.Type')"></v-text-field> -->
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <el-upload class="upload-demo" ref="upload" :auto-upload="false" action="" :on-change="FileChange" :on-remove="FileRemove" multiple :limit="1" :file-list="Fileform.FileList">
                            <v-btn color="primary">{{ $t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.FileUpload') }}</v-btn>
                            <div slot="tip" class="el-upload__tip">{{ $t('GLOBAL.PDFOnly') }}</div>
                        </el-upload>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="FileSave('addFile')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card class="" v-if="dialogType == 'editFile'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                <!-- 修改指标 -->
                {{ $t('GLOBAL._BJ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="Fileform.Name" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.FileName') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="Fileform.Version" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.FileVersion')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-select
                            v-model="Fileform.Type"
                            :items="DocType"
                            item-text="ItemName"
                            item-value="ItemName"
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.Type') + '*'"
                            clearable
                            dense
                            outlined
                        ></v-select>
                        <!-- <v-text-field v-model="Fileform.Type" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.Type')"></v-text-field> -->
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <el-upload class="upload-demo" ref="upload2" :auto-upload="false" action="" :on-change="FileChange" :on-remove="FileRemove" multiple :limit="1" :file-list="Fileform.FileList">
                            <v-btn color="primary">{{ $t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.FileUpload') }}</v-btn>
                            <div slot="tip" class="el-upload__tip">{{ $t('GLOBAL.PDFOnly') }}</div>
                        </el-upload>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="FileSave('editFile')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card class="" v-if="dialogType == 'addBOM'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                <!-- 新增 -->
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-row class="pt-8">
                    <!-- <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <Treeselect
                            :noChildrenText="$t('GLOBAL.noData')"
                            :noOptionsText="$t('GLOBAL.noData')"
                            :normalizer="normalizer"
                            :option="bomTreeList"
                            :placeholder="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBBOM.ParentId')"
                            v-model="BOMform.ParentId"
                        />
                    </v-col> -->
                    <!-- <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <el-select v-model="BOMform.Bom" multiple filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod" :loading="loading">
                            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </v-col> -->
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="BOMform.Name" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBBOM.AccessoriesName') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="BOMform.Code" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBBOM.AccessoriesCode') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-select
                            v-model="BOMform.Type"
                            :items="BOMType"
                            item-text="ItemName"
                            item-value="ItemName"
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.Type') + '*'"
                            clearable
                            dense
                            outlined
                        ></v-select>
                        <!-- <v-text-field v-model="Fileform.Type" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.Type')"></v-text-field> -->
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="BOMform.Model" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBBOM.Specifications')"></v-text-field>
                    </v-col>
                    <!-- <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="BOMform.Qty" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBBOM.Quantity') + '*'"></v-text-field>
                    </v-col> -->
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="BOMform.Remark" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBBOM.Remark')"></v-text-field>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addBOMSave('addBOM')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card class="" v-if="dialogType == 'editBOM'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                <!-- 修改 -->
                {{ $t('GLOBAL._BJ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-row class="pt-8">
                    <!-- <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <Treeselect
                            :noChildrenText="$t('GLOBAL.noData')"
                            :noOptionsText="$t('GLOBAL.noData')"
                            :normalizer="normalizer"
                            disabled
                            :option="bomTreeList"
                            :placeholder="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBBOM.ParentId')"
                            v-model="editedItem.ParentId"
                        />
                    </v-col> -->
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.Name" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBBOM.AccessoriesName') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.Code" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBBOM.AccessoriesCode') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-select
                            v-model="editedItem.Type"
                            :items="BOMType"
                            item-text="ItemName"
                            item-value="ItemName"
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.Type') + '*'"
                            clearable
                            dense
                            outlined
                        ></v-select>
                        <!-- <v-text-field v-model="Fileform.Type" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.Type')"></v-text-field> -->
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.Model" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBBOM.Specifications')"></v-text-field>
                    </v-col>
                    <!-- <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.Qty" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBBOM.Quantity') + '*'"></v-text-field>
                    </v-col> -->
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.Remark" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBBOM.Remark')"></v-text-field>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addBOMSave('editBOM')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { GetPartsListByKeyword } from '@/api/equipmentManagement/Parts.js';
import { GetDeviceTree } from '@/api/equipmentManagement/classification.js';
import { fileSaveForm, fileUpload } from '@/api/factoryPlant/processDocumentation.js';
import { SparepartSaveForm, DevicePartsSaveForm, BomtSaveForm, DeviceBomSaveForm, DocSaveForm, DeviceDocSaveForm, DeviceuploadFile } from '@/api/equipmentManagement/EquipParts.js';
import { EquipSaveForm, DeviceSaveForm, EquipTargetSaveForm, DeviceAccessoriesSaveForm } from '@/api/equipmentManagement/Equip.js';
import { EquipmentGetPageList } from '@/api/common.js';
import { Message, MessageBox } from 'element-ui';

export default {
    props: {
        equipmentStatus: {
            type: Array,
            default: () => []
        },
        equipmentGroup: {
            type: Array,
            default: () => []
        },
        statuslist: {
            type: Array,
            default: () => []
        },
        typecodelist: {
            type: Array,
            default: () => []
        },
        floorList: {
            type: Array,
            default: () => []
        },
        productionlineList: {
            type: Array,
            default: () => []
        },
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        },
        rowtableItem: {
            type: Object,
            default: () => {}
        },
        bomTreeList: {
            type: Array,
            default: () => []
        },
        cycleList: {
            type: Array,
            default: () => []
        },
        peopleitems: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            DeviceCategoryName: '',
            DeviceCategoryId: '',
            treeData: [],
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            menu: [],
            activeKey: '',
            SbxxList: [
                {
                    required: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipCode'),
                    value: '',
                    id: 'Code',
                    type: 'input'
                },
                {
                    required: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Name'),
                    value: '',
                    id: 'Name',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.SubName'),
                    value: '',
                    id: 'Alias',
                    type: 'input'
                },
                {
                    required: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Equipnature'),
                    value: '',
                    option: [],
                    id: 'DeviceNature',
                    type: 'select'
                },
                {
                    required: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipType'),
                    value: '',
                    option: [],
                    id: 'DeviceCategory',
                    type: 'select'
                },
                {
                    required: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipGroup'),
                    value: '',
                    option: [],
                    id: 'DeviceClass',
                    type: 'select'
                },
                // {
                //     label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.SuperiorEquit'),
                //     value: '',
                //     option: [],
                //     id: 'ParentId',
                //     type: 'select'
                // },
                // {
                //     label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Factory'),
                //     value: '',
                //     id: 'Factory',
                //     type: 'input'
                // },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Specmodel'),
                    value: '',
                    id: 'Model',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Department'),
                    value: '',
                    id: 'Deparment',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Area'),
                    value: '',
                    id: 'Area',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Group'),
                    value: '',
                    id: 'Groups',
                    type: 'input'
                }
            ],
            SbsmList: [
                {
                    required: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Accountingmethod'),
                    value: '',
                    option: this.EntryType,
                    id: 'EntryType',
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.WorkCenter'),
                    value: '',
                    id: 'WorkCenter',
                    type: 'input'
                },
                {
                    required: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.CostCenter'),
                    value: '',
                    option: [],
                    id: 'CostCenter',
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Assetnumber'),
                    value: '',
                    id: 'AssetsNo',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.AssetName'),
                    value: '',
                    id: 'AssetsName',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.OldCode'),
                    value: '',
                    id: 'OldCode',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.serialnumber'),
                    value: '',
                    id: 'Sn',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Licenseplatenumber'),
                    value: '',
                    id: 'PlateNumber',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Factorycode'),
                    value: '',
                    id: 'FactoryNo',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipItem'),
                    value: '',
                    id: 'DeviceBin',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.brand'),
                    value: '',
                    id: 'Brand',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.isImported'),
                    value: '',
                    option: [],
                    id: 'IsImport',
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.manufacturer'),
                    value: '',
                    id: 'Manufacture',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.supplier'),
                    value: '',
                    id: 'Supplier',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.producer'),
                    value: '',
                    id: 'Madein',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipSize'),
                    value: '',
                    id: 'DeviceSize',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipWeight'),
                    value: '',
                    id: 'DeviceWeight',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.FixLimit'),
                    value: '',
                    id: 'WarrantyPeriod',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Manufacturingdate'),
                    value: '',
                    id: 'ManufactureDate',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.dateproduction'),
                    value: '',
                    id: 'MakeDate',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.useYear'),
                    value: '',
                    id: 'ServiceLife',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Expectedscrapdate'),
                    value: '',
                    id: 'EstimatedScrapDate',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Scrapdate'),
                    value: '',
                    id: 'ScrapDate',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipValue'),
                    value: '',
                    id: 'DeviceValue',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Number'),
                    value: '',
                    id: 'Qty',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Unit'),
                    value: '',
                    option: this.DeviceUnit,
                    id: 'Unit',
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Keyequipmentstatistics'),
                    value: '',
                    id: 'KeyDeviceState',
                    type: 'input'
                },
                {
                    required: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Status'),
                    value: '',
                    option: this.DeviceStatus,
                    id: 'Status',
                    type: 'select'
                },
                {
                    required: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.UseStatus'),
                    value: '',
                    option: this.DeviceUsingStatus,
                    id: 'UsingStatus',
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.UseStatusTag'),
                    value: '',
                    id: 'StatusTag',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.factoryDate'),
                    value: '',
                    id: 'EntryFactoryDate',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.TestDate'),
                    value: '',
                    id: 'DebugDate',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.IncomeDate'),
                    value: '',
                    id: 'UseDate',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Storagelocation'),
                    value: '',
                    id: 'StorageLocation',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.position'),
                    value: '',
                    id: 'StoragePosition',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Nowposition'),
                    value: '',
                    id: 'CurrentStorageLocation',
                    type: 'input'
                },
                // {
                //     label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Insidelocation'),
                //     value: '',
                //     id: 'Insidelocation',
                //     type: 'input'
                // },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Inventorysituation'),
                    value: '',
                    id: 'CheckState',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Firstmaintenancetime'),
                    value: '',
                    id: 'FirstMaintenanceDate',
                    type: 'datetime'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.personresponsible'),
                    value: '',
                    multiple: true,
                    required: true,
                    id: 'Header',
                    type: 'select',
                    option: []
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.equipmentSupervisor'),
                    value: '',
                    id: 'Manager',
                    required: true,
                    multiple: true,
                    type: 'select',
                    option: []
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.LastFirstmaintenancetime'),
                    value: '',
                    id: 'LastMaintenanceDate',
                    type: 'datetime'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Inspectiontime'),
                    value: '',
                    id: 'SpotCheckTime',
                    type: 'datetime'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Comment'),
                    value: '',
                    id: 'Remark',
                    type: 'input'
                }
            ],
            LineId: '',
            LineCode: '',
            form: {
                FirstMaintenanceTime: '',
                PersonCode: '',
                MyCycle: '',
                MyProject: '',
                // 设备信息
                Name: '',
                Code: '',
                Typecode: '',
                Status: '',
                Eqmodel: '',
                // EamCode: '',
                EipCode: '',
                LeaveCode: '',
                BarCode: '',
                Purchasedate: '',
                Manufacturer: '',
                Contacts: '',
                Tel: '',
                // 设备说明
                Age: null,
                Owner: '',
                Cost: '',
                Productiondate: '',
                UseInstructions: '',
                MaintainInstructions: '',
                Remark: '',
                //设备维护
                MaintainPosition: '',
                MaintainMethod: '',
                RunningBeat: '',
                BeatSpeeding: '',
                MachineHour: 0,
                MaintainData: '',
                MaintainPlandata: '',
                // 设备产线
                Depareaid: null,
                ProductlineCode: '',
                LineCode: '',
                SegmentCode: '',
                // 设备配件
                Smq: false,
                Dyj: false,
                Pmj: false
            },
            Fileform: {
                Name: '',
                Type: '',
                Version: '',
                Size: '',
                Uploaddate: '',
                FilePath: '',
                FileList: []
            },
            Listform: {
                Part: '',
                Type: '',
                Qty: '',
                Maxqty: '',
                Safeqty: '',
                Iskey: ''
            },
            TGRform: {
                Eqcode: '',
                RunningBeat: '',
                BeatSpeeding: '',
                MachineHour: ''
            },
            BOMform: {
                ParentId: null,
                Name: '',
                Code: '',
                Type: '',
                Model: '',
                DeviceValue: '',
                InstallDate: '',
                Remark: ''
            },
            normalizer(node) {
                const { id, name, children } = node;
                return {
                    id,
                    label: name,
                    children
                };
            },
            partsloading: false,
            PartList: [],
            lineCodeList: [], //工段
            lineCodeGZList: [], //工站
            equipmentSpareType: [], //备件类型
            DocType: [], //文件类型
            BOMType: [], //bom类型
            DeviceStatus: [],
            DeviceNature: [], //设备性质
            EntryType: [], //入账方式
            DeviceClass: [], //设备类别
            DeviceUnit: [], //单位
            DeviceUsingStatus: [], //使用状态
            IsImport: []
        };
    },
    async mounted() {
        this.SbxxList.forEach(item => {
            if (item.required) {
                item.label = item.label + '*';
            }
        });
        this.SbsmList.forEach(item => {
            if (item.required) {
                item.label = item.label + '*';
            }
        });
        this.getPartList();
        this.GetFactorylineTree();
        this.BOMType = await this.$getNewDataDictionary('BomType');
        this.DocType = await this.$getNewDataDictionary('DocType');
        this.equipmentSpareType = await this.$getNewDataDictionary('PartType');
    },
    computed: {
        locale() {
            return this.$store.state.app.locale || 'zh';
        },
        editedItem() {
            if (this.dialogType == 'editBOM') {
                const { PartsId, Name, Code, Model, Remark, Qty, Type, parentId } = this.tableItem;
                let obj = {
                    Part: PartsId + '|' + Name + '|' + Code + '|' + Model,
                    Name: Name,
                    Code: Code,
                    Model: Model,
                    Type: Type,
                    Qty: Qty,
                    Remark: Remark,
                    ParentId: parentId
                };
                return obj;
            }
            const {
                FirstMaintenanceTime,
                PersonCode,
                MyCycle,
                Name,
                Code,
                Typecode,
                Status,
                Eqmodel,
                BarCode,
                // EamCode,
                EipCode,
                LeaveCode,
                Purchasedate,
                Manufacturer,
                Contacts,
                Tel,
                Age,
                Owner,
                Cost,
                Productiondate,
                UseInstructions,
                MaintainInstructions,
                MaintainPosition,
                MaintainMethod,
                RunningBeat,
                BeatSpeeding,
                MachineHour,
                MaintainData,
                MaintainPlandata,
                Depareaid = null,
                ProductlineCode,
                LineCode,
                Model,
                SegmentCode,
                Smq,
                Dyj,
                Pmj,
                Type,
                Qty,
                Maxqty,
                Safeqty,
                Iskey,
                Eqcode,
                MyProject,
                DeviceValue,
                InstallDate,
                Remark
            } = this.tableItem;
            return {
                FirstMaintenanceTime,
                PersonCode,
                MyCycle,
                MyProject,
                Name,
                Code,
                Typecode,
                Status,
                Eqmodel,
                // EamCode,
                EipCode,
                LeaveCode,
                BarCode,
                Purchasedate,
                Manufacturer,
                Contacts,
                Tel,
                Age,
                Owner,
                Cost,
                Productiondate,
                UseInstructions,
                MaintainInstructions,
                MaintainPosition,
                Model,
                MaintainMethod,
                RunningBeat,
                BeatSpeeding,
                MachineHour,
                MaintainData,
                MaintainPlandata,
                Depareaid,
                ProductlineCode,
                LineCode,
                SegmentCode,
                Smq,
                Dyj,
                Pmj,

                // Name,
                // Code,
                Type,
                Qty,
                Maxqty,
                Safeqty,
                Iskey,

                Eqcode,
                // RunningBeat,
                // BeatSpeeding,
                // MachineHour,

                DeviceValue,
                InstallDate,
                Remark
            };
        }
    },
    watch: {
        'editedItem.LineCode': 'Getlinecodelist',
        'editedItem.SegmentCode': 'GetSegmentCodelist'
    },
    methods: {
        // 树状点击获取
        clickClassTree(v) {
            if (v.id == this.DeviceCategoryId) {
                this.DeviceCategoryId = '';
            } else {
                this.DeviceCategoryId = v.id;
            }
        },
        // 获取树形数据
        async GetFactorylineTree() {
            let params = {};
            params.factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            const res = await GetDeviceTree(params);
            let { success, response } = res;
            if (success) {
                this.treeData = response || [];
                this.treeData.forEach(item => {
                    item.ItemValue = item.id;
                    item.ItemName = item.name;
                });
                this.DeviceCategoryId = '';
                this.activeKey = ' ';
            }
        },
        clearFiles() {
            this.Fileform.FileList = [];
            if (this.$refs.upload) {
                this.$refs.upload.clearFiles();
            }
            if (this.$refs.upload2) {
                this.$refs.upload2.clearFiles();
            }
        },
        async getSelect() {},
        async FileChange(File, fileList) {
            const isPDf = File.raw.type === 'application/pdf';
            if (isPDf == false) {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL.PDFOnly'), color: 'error' });
                this.clearFiles();
                return false;
            }
            const formData = new FormData();
            formData.append('file', File.raw);
            let res = await DeviceuploadFile(formData);
            // let res = await fileUpload(formData);
            this.Fileform.Size = File.size;
            this.Fileform.Uploaddate = new Date();
            this.Fileform.FilePath = res.response.FilePath;
        },
        FileRemove(File, fileList) {
            this.Fileform.Size = '';
            this.Fileform.Uploaddate = '';
            this.Fileform.FilePath = '';
        },
        Getlinecodelist(n, o) {
            this.$nextTick(() => {
                this.lineCodeList = []; //工段
                if (n) {
                    this.lineCodeList = [{ EquipmentName: this.tableItem.LineName, EquipmentCode: this.tableItem.LineCode }];
                }
            });
        },
        GetSegmentCodelist(n, o) {
            this.$nextTick(() => {
                this.lineCodeGZList = []; //工站
                if (n) {
                    this.lineCodeGZList = [{ EquipmentName: this.tableItem.SegmentName, EquipmentCode: this.tableItem.SegmentCode }];
                }
            });
        },
        closeDatePicker(index) {
            this.$set(this.menu, index, false);
        },
        // 上传操作说明书
        czbooks(file) {
            console.log(file);
        },
        // 上传保养说明书
        bybooks(file) {
            console.log(file);
        },
        async changeV(LineCode) {
            if (!LineCode) return false;
            let lineObj = this.productionlineList.find(item => item.EquipmentCode == LineCode);
            const { ID } = lineObj;
            this.lineCodeList = [];
            const res = await EquipmentGetPageList({ DataItemCode: 'EquipmentLevel', ParentId: ID, pageIndex: 1, pageSize: 9999 });
            const { success, response } = res;
            if (success) {
                this.lineCodeList = response.data;
            }
        },
        async changeL(LineCode) {
            if (!LineCode) return false;
            let segmentObj = this.lineCodeList.find(item => item.EquipmentCode == LineCode);
            const { ID } = segmentObj;
            this.lineCodeGZList = [];
            const res = await EquipmentGetPageList({ DataItemCode: 'EquipmentLevel', ParentId: ID, pageIndex: 1, pageSize: 9999 });
            const { success, response } = res;
            if (success) {
                this.lineCodeGZList = response.data;
            }
        },
        closeEquip() {
            this.showDialog = false;
            this.$refs.form.reset();
            this.form.Depareaid = null;
        },
        async remoteMethod(query) {
            this.partsloading = true;
            let params = {
                keyword: query
            };
            let res = await GetPartsListByKeyword(params);
            this.PartList = res.response;
            this.PartList.forEach(item => {
                item.ItemName = item.Name + '|' + item.Code;
                item.ItemValue = item.ID + '|' + item.Name + '|' + item.Code + '|' + item.Model;
            });
            this.partsloading = false;
        },
        async getPartList() {
            let params = {
                keyword: ''
            };
            let res = await GetPartsListByKeyword(params);
            this.PartList = res.response;
            this.PartList.forEach(item => {
                item.ItemName = item.Name + '|' + item.Code;
                item.ItemValue = item.ID + '|' + item.Name + '|' + item.Code + '|' + item.Model;
            });
        },
        // 新增设备
        async addSave(type) {
            let flag = this.SbxxList.some(item => {
                if (item.required) {
                    return item.value == '';
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            let flag2 = this.SbsmList.some(item => {
                if (item.multiple) {
                    item.value = item.value.join('|');
                }
                if (item.required) {
                    return item.value == '';
                }
            });
            if (flag2) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            // const paramsKey = Object.keys(this.form);
            let params = {};
            this.SbxxList.forEach(item => {
                params[item.id] = item.value;
            });
            this.SbsmList.forEach(item => {
                params[item.id] = item.value;
            });
            // if (this.tableItem.LineId) {
            //     params.LineId = this.tableItem.LineId;
            //     params.LineCode = this.tableItem.LineCode;
            // } else {
            //     params.LineId = this.LineId;
            //     params.LineCode = this.LineCode;
            // }
            if (type == 'addedit') {
                params.ID = this.tableItem.ID;
                params.LineId = this.tableItem.LineId;
                params.LineCode = this.tableItem.LineCode;
            } else {
                params.LineId = this.LineId;
                params.LineCode = this.LineCode;
            }
            params.DeviceCategoryId = this.DeviceCategoryId;
            const res = await DeviceSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.$parent.RepastInfoGetPage();
                this.showDialog = this.classcheckbox ? false : true;
            }
        },
        // 备品备件
        async addListSave(type) {
            const paramsKey = Object.keys(this.Listform);
            let params = { DeviceCategoryId: '' };
            const paramsObj = type == 'addList' ? this.Listform : this.editedItem;
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            if (type == 'editList') {
                params.ID = this.tableItem.ID;
                if (this.editedItem.Part == '' || this.editedItem.Type == '' || this.editedItem.Qty == '') {
                    Message({
                        message: `${this.$t('Inventory.ToOver')}`,
                        type: 'error'
                    });
                    return;
                }
            } else {
                if (this.Listform.Part == '' || this.Listform.Type == '' || this.Listform.Qty == '') {
                    Message({
                        message: `${this.$t('Inventory.ToOver')}`,
                        type: 'error'
                    });
                    return;
                }
            }
            params.PartsId = this.Listform.Part.split('|')[0];
            params.Name = this.Listform.Part.split('|')[1];
            params.Code = this.Listform.Part.split('|')[2];
            params.Model = this.Listform.Part.split('|')[3];
            let res;
            let flag;
            if (this.rowtableItem.LineId) {
                flag = true;
                params.DeviceId = this.rowtableItem.ID;
                res = await DevicePartsSaveForm(params);
            } else {
                flag = false;
                params.DeviceCategoryId = this.rowtableItem.ID;
                res = await SparepartSaveForm(params);
            }
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.GetSparepartGetPageList(this.rowtableItem, flag);
                // this.$parent.$parent.$refs.Tables.selected = [];
                this.showDialog = this.classcheckbox ? false : true;
            }
        },
        async FileSave(type) {
            if (this.Fileform.Name == '' || this.Fileform.Type == '') {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            const paramsKey = Object.keys(this.Fileform);
            let params = { DeviceCategoryId: '' };
            const paramsObj = this.Fileform;
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            if (type == 'editFile') {
                params.ID = this.tableItem.ID;
            }
            let res;
            let flag;
            if (this.rowtableItem.LineId) {
                flag = true;
                params.DeviceId = this.rowtableItem.ID;
                res = await DeviceDocSaveForm(params);
            } else {
                flag = false;
                params.DeviceCategoryId = this.rowtableItem.ID;
                res = await DocSaveForm(params);
            }
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.RepastInfoTARGetPage(this.rowtableItem, flag);
                // this.$parent.$parent.$refs.Tables.selected = [];
                this.showDialog = this.classcheckbox ? false : true;
            }
        },
        // 指标
        async addTARSave(type) {
            const paramsKey = Object.keys(this.TGRform);
            let params = { DeviceCategoryId: '' };
            console.log(paramsKey);
            const paramsObj = type == 'addTAR' ? this.TGRform : this.editedItem;
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            params.Eqid = this.rowtableItem.ID;
            if (type == 'editTAR') {
                params.ID = this.tableItem.ID;
            }
            const res = await EquipTargetSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.RepastInfoTARGetPage(this.rowtableItem);
                // this.$parent.$refs.Tables.selected = [];
                this.showDialog = this.classcheckbox ? false : true;
            }
        },
        // BOM
        async addBOMSave(type) {
            const paramsKey = Object.keys(this.BOMform);
            let params = {
                DeviceCategoryId: ''
            };
            const paramsObj = type == 'addBOM' ? this.BOMform : this.editedItem;
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            if (type == 'editBOM') {
                if (this.editedItem.Name == '' || this.editedItem.Code == '' || this.editedItem.Type == '') {
                    Message({
                        message: `${this.$t('Inventory.ToOver')}`,
                        type: 'error'
                    });
                    return;
                }
                params.ID = this.tableItem.ID;
            } else {
                if (this.BOMform.Name == '' || this.BOMform.Code == '' || this.BOMform.Type == '') {
                    Message({
                        message: `${this.$t('Inventory.ToOver')}`,
                        type: 'error'
                    });
                    return;
                }
            }
            let res;
            let flag;
            if (this.rowtableItem.LineId) {
                flag = true;
                params.DeviceId = this.rowtableItem.ID;
                res = await DeviceBomSaveForm(params);
            } else {
                flag = false;
                params.DeviceCategoryId = this.rowtableItem.ID;
                res = await BomtSaveForm(params);
            }
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.RepastBOMlistTARGetPage(this.rowtableItem, flag);
                // this.$parent.$parent.$refs.Tables.selected = [];
                this.showDialog = this.classcheckbox ? false : true;
            }
        }
    }
};
</script>

<style lang="scss">
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
}
.mytree {
    .tree-card {
        margin: 5px;
        min-height: 103.5vh !important;
        .v-treeview {
            height: calc(100vh - 20px) !important;
        }
        .tree-title {
            align-items: center !important;
            display: flex;
            flex-wrap: wrap;
            font-size: 1.25rem !important;
            letter-spacing: 0.0125em;
            line-height: 2rem;
            word-break: break-all;
            width: 100%;
            font-weight: 500;
            padding: 8px;
            border-bottom: 1px solid #aaa;
        }
    }
}
.ant-calendar-picker {
    ::v-deep input {
        border-color: #9e9e9e;
    }

    ::v-deep i {
        color: #757575;
    }
}
</style>

import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM'

// 获取部门树形数据
export function getDepartment(data) {
    const api = '/api/Department/GetTree'
    return getRequestResources(baseURL, api, 'post', data)
}

// 添加班次信息
export function saveClassesForm(data) {
    const api = '/api/Shift/SaveForm'
    return getRequestResources(baseURL, api, 'post', data)
}

// 删除班次信息
export function deleteClasses(data) {
    const api = '/api/Shift/Delete'
    return getRequestResources(baseURL, api, 'post', data)
}

// 删除班组信息
export function deleteTeam(data) {
    const api = '/api/Team/Delete'
    return getRequestResources(baseURL, api, 'post', data)
}

// 添加班组信息
export function saveTeamForm(data) {
    const api = '/api/Team/SaveForm'
    return getRequestResources(baseURL, api, 'post', data)
}

// 获取班次列表数据
export function getClassesList(data) {
    const api = '/api/Shift/GetList'
    return getRequestResources(baseURL, api, 'post', data)
}

// 获取班组列表数据
export function getTeamList(data) {
    const api = '/api/Team/GetList'
    return getRequestResources(baseURL, api, 'post', data)
}

// 添加运转模式
export function saveOperatingMode(data) {
    const api = '/api/Turnmode/SaveForm'
    return getRequestResources(baseURL, api, 'post', data)
}

// 添加运转模式
export function getModeList(data) {
    const api = '/api/Turnmode/GetList'
    return getRequestResources(baseURL, api, 'post', data)
}

// 删除运转模式信息
export function deleteMode(data) {
    const api = '/api/Turnmode/Delete'
    return getRequestResources(baseURL, api, 'post', data)
}

// 获取日历信息
export function getCalendarData(data) {
    const api = `/api/Calendar/GetList`
    return getRequestResources(baseURL, api, 'post', data)
}

// 获取日历信息
export function saveCalendar(data) {
    const api = `/api/Calendar/SaveForm`
    return getRequestResources(baseURL, api, 'post', data)
}

// 删除日历
export function delCalendar(data) {
    const api = `/api/Calendar/Delete`
    return getRequestResources(baseURL, api, 'post', data)
}

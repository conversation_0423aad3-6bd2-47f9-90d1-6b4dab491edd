export const sparePartColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
        sortable: true
    },
    // {
    //     text: '货架编号',
    //     value: 'Remark',
    //     width: 100,
    //     sortable: true
    // },
    {
        text: '备件编号',
        Namevalue: "SparePartsCode",
        value: 'Code',
        width: 130,
        sortable: true
    },
    {
        text: '备件名称',
        width: 160,
        Namevalue: "SparePartsName",
        value: 'Name',
        sortable: true
    },
    {
        text: '备件类型',
        width: 100,
        Namevalue: "SType",
        value: 'Type',
        sortable: true
    },
    {
        text: '规格型号',
        width: 100,
        Namevalue: "SparePartsSpec",
        value: 'Model',
        sortable: true
    },
    {
        text: '备件科目',
        width: 100,
        Namevalue: "Subject",
        value: 'Subject',
        sortable: true
    },
    {
        text: '采购周期',
        width: 150,
        Namevalue: "PurchaseCycle",
        value: 'PurchaseCycle',
        sortable: true
    },
    {
        text: '最低采购数',
        width: 150,
        Namevalue: "MinPurchaseQty",
        value: 'MinPurchaseQty',
        sortable: true
    },
    {
        text: '安全库存下限',
        width: 120,
        Namevalue: "Min",
        value: 'LowerBound',
        sortable: true
    }, {
        text: '安全库存上限',
        width: 160,
        Namevalue: "Max",
        value: 'UpperBound',
        sortable: true
    }, {
        text: '使用寿命',
        width: 160,
        Namevalue: "Useyears",
        value: 'ServiceLife',
        sortable: true
    }, {
        text: '单位',
        width: 160,
        Namevalue: "Unit",
        value: 'Unit',
        sortable: true
    },
    {
        text: '备注',
        width: 200,
        value: 'Remark',
        sortable: true
    },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: 220,
        sortable: true
    }
];
export const sparePartInventoryColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
        sortable: true
    },
    // {
    //     text: '货架编号',
    //     value: 'Remark',
    //     width: 100,
    //     sortable: true
    // },
    {
        text: '备件编号',
        Namevalue: "SparePartsCode",
        value: 'Code',
        width: 130,
        sortable: true
    },
    {
        text: '备件名称',
        width: 160,
        Namevalue: "SparePartsName",
        value: 'Name',
        sortable: true
    },
    {
        text: '备件类型',
        width: 100,
        Namevalue: "SType",
        value: 'Type',
        sortable: true
    },
    {
        text: '规格型号',
        width: 100,
        Namevalue: "SparePartsSpec",
        value: 'Model',
        sortable: true
    },
    {
        text: '批次号',
        width: 120,
        Namevalue: "BatchCode",
        value: 'BatchCode',
        sortable: true
    }, {
        text: '入库时间',
        width: 160,
        Namevalue: "InstoreDate",
        value: 'InstoreDate',
        sortable: true
    }, {
        text: '仓库',
        width: 160,
        Namevalue: "Warehouse",
        value: 'Warehouse',
        sortable: true
    }, {
        text: '库位',
        width: 160,
        Namevalue: "StorageBin",
        value: 'StorageBin',
        sortable: true
    }, {
        text: '库存',
        width: 160,
        Namevalue: "Stock",
        value: 'Stock',
        sortable: true
    }, {
        text: '单位',
        width: 100,
        Namevalue: "Unit",
        value: 'Unit',
        sortable: true
    }, {
        text: '品牌',
        width: 160,
        Namevalue: "Brand",
        value: 'Brand',
        sortable: true
    },
    {
        text: '供应商',
        align: 'Supplier',
        value: 'Supplier',
        width: 220,
        sortable: true
    },
    {
        text: '操作',
        value: 'actions',
        width: 150,
        sortable: true
    }
];
export const sparePartColumQR = [{
        text: '序号',
        value: 'Index',
        width: 60,
        sortable: true
    },
    {
        text: '货架编号',
        value: 'Remark',
        width: 100,
        sortable: true
    },
    {
        text: '物料编号',
        value: 'SparepartCode',
        width: 130,
        sortable: true,
        align: 'right'
    },
    {
        text: '物料名称',
        width: 160,
        value: 'SparepartName',
        sortable: true
    },
    {
        text: '入库数量',
        width: 160,
        value: 'Buynum',
        sortable: true,
        align: 'right'
    },

];

export const sparePartlogColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
        sortable: true
    },
    {
        text: '状态',
        width: 100,
        value: 'Buynum',
        sortable: true
    },
    {
        text: '数量',
        width: 100,
        value: 'Usednum',
        sortable: true,
        align: 'right'
    },
    {
        text: '使用人',
        value: 'Owner',
        width: 110,
        sortable: true
    },
    // { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    // { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    // { text: '创建时间', value: 'CreateDate', width: '160px' },
    // { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: 160,
        sortable: true
    }
];
// 备件仓库管理
export const sparePartManagementColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
        sortable: true
    },
    {
        text: '编码',
        width: 100,
        value: 'Code',
        sortable: true
    },

    {
        text: '名称',
        value: 'Name',
        width: 150,
        sortable: true
    },
    {
        text: '备件类型',
        Namevalue: "Type",
        value: 'Type',
        width: 100,
        sortable: true
    },
    {
        text: '规格型号',
        Namevalue: "Spec",
        value: 'Spec',
        width: 120,
        sortable: true
    }, {
        text: '安全库存下限',
        Namevalue: "Min",
        value: 'Min',
        width: 120,
        sortable: true
    },
    {
        text: '安全库存上限',
        Namevalue: "Max",
        value: 'Max',
        width: 120,
        sortable: true
    },
    {
        text: '使用寿命',
        Namevalue: "Useyears",
        value: 'Useyears',
        width: 100,
        sortable: true
    },
    {
        text: '单位',
        Namevalue: "Unit",
        value: 'Unit',
        width: 80,
        sortable: true
    },
    {
        text: '备注',
        width: 100,
        value: 'Remark',
        sortable: true
    },
    // { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    // { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    // { text: '创建时间', value: 'CreateDate', width: '160px' },
    // { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: 160,
        sortable: true
    }
];
// 备件出入库
export const historyspareParttColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
        sortable: true
    },
    {
        text: '类型',
        width: 90,
        value: 'Buynum',
        sortable: true
    },
    {
        text: '货架编号',
        width: 100,
        value: 'Remark',
        sortable: true
    },
    {
        text: '备件编号',
        value: 'SparepartCode',
        width: 100,
        sortable: true
    },
    {
        text: '备件名称',
        value: 'SparepartName',
        width: 160,
        sortable: true
    },
    {
        text: '数量',
        width: 100,
        value: 'Usednum',
        sortable: true,
        align: 'right'
    },
    {
        text: '时间',
        value: 'CreateDate',
        width: 100,
        sortable: true
    },
];
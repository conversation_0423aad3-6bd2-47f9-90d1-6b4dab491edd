{"version": 3, "sources": ["../../src/util/console.ts"], "names": [], "mappings": ";;;;;;;;;;;;AACA;;;;AADA;AAGA,SAAS,aAAT,CAAwB,OAAxB,EAAyC,EAAzC,EAAmD,MAAnD,EAA+D;AAC7D,MAAI,mBAAQ,MAAR,CAAe,MAAnB,EAA2B;;AAE3B,MAAI,MAAJ,EAAY;AACV,IAAA,EAAE,GAAG;AACH,MAAA,MAAM,EAAE,IADL;AAEH,MAAA,OAAO,EAAE,MAFN;AAGH,MAAA,QAAQ,EAAE;AAHP,KAAL;AAKD;;AAED,MAAI,EAAJ,EAAQ;AACN;AACA,IAAA,EAAE,CAAC,eAAH,GAAqB,EAAE,CAAC,eAAH,IAAsB,EAA3C;AACA,QAAI,EAAE,CAAC,eAAH,CAAmB,QAAnB,CAA4B,OAA5B,CAAJ,EAA0C;AAC1C,IAAA,EAAE,CAAC,eAAH,CAAmB,IAAnB,CAAwB,OAAxB;AACD;;AAED,SAAO,oBAAa,OAAb,KACL,EAAE,GAAG,sBAAsB,CAAC,EAAD,CAAzB,GAAgC,EAD7B,CAAP;AAGD;;AAEK,SAAU,WAAV,CAAuB,OAAvB,EAAwC,EAAxC,EAAkD,MAAlD,EAA8D;AAClE,MAAM,UAAU,GAAG,aAAa,CAAC,OAAD,EAAU,EAAV,EAAc,MAAd,CAAhC;AACA,EAAA,UAAU,IAAI,IAAd,IAAsB,OAAO,CAAC,IAAR,CAAa,UAAb,CAAtB;AACD;;AAEK,SAAU,WAAV,CAAuB,OAAvB,EAAwC,EAAxC,EAAkD,MAAlD,EAA8D;AAClE,MAAM,UAAU,GAAG,aAAa,CAAC,OAAD,EAAU,EAAV,EAAc,MAAd,CAAhC;AACA,EAAA,UAAU,IAAI,IAAd,IAAsB,OAAO,CAAC,IAAR,CAAa,UAAb,CAAtB;AACD;;AAEK,SAAU,YAAV,CAAwB,OAAxB,EAAyC,EAAzC,EAAmD,MAAnD,EAA+D;AACnE,MAAM,UAAU,GAAG,aAAa,CAAC,OAAD,EAAU,EAAV,EAAc,MAAd,CAAhC;AACA,EAAA,UAAU,IAAI,IAAd,IAAsB,OAAO,CAAC,KAAR,CAAc,UAAd,CAAtB;AACD;;AAEK,SAAU,SAAV,CAAqB,QAArB,EAAuC,WAAvC,EAA4D,EAA5D,EAAsE,MAAtE,EAAkF;AACtF,EAAA,WAAW,sBAAe,QAAf,mCAAgD,WAAhD,iBAAyE,EAAzE,EAA6E,MAA7E,CAAX;AACD;;AACK,SAAU,QAAV,CAAoB,QAApB,EAAsC,WAAtC,EAA2D,EAA3D,EAAqE,MAArE,EAAiF;AACrF,EAAA,YAAY,uBAAgB,QAAhB,sCAAoD,WAApD,iJAA6M,EAA7M,EAAiN,MAAjN,CAAZ;AACD;;AACK,SAAU,OAAV,CAAmB,QAAnB,EAAqC,EAArC,EAA+C,MAA/C,EAA2D;AAC/D,EAAA,WAAW,sBAAe,QAAf,kDAAsE,EAAtE,EAA0E,MAA1E,CAAX;AACD;AAED;;AAEG;;;AAEH,IAAM,UAAU,GAAG,iBAAnB;;AACA,IAAM,QAAQ,GAAG,SAAX,QAAW,CAAC,GAAD;AAAA,SAAiB,GAAG,CAClC,OAD+B,CACvB,UADuB,EACX,UAAA,CAAC;AAAA,WAAI,CAAC,CAAC,WAAF,EAAJ;AAAA,GADU,EAE/B,OAF+B,CAEvB,OAFuB,EAEd,EAFc,CAAjB;AAAA,CAAjB;;AAIA,SAAS,mBAAT,CAA8B,EAA9B,EAAuC,WAAvC,EAA4D;AAC1D,MAAI,EAAE,CAAC,KAAH,KAAa,EAAjB,EAAqB;AACnB,WAAO,QAAP;AACD;;AACD,MAAM,OAAO,GAAG,OAAO,EAAP,KAAc,UAAd,IAA4B,EAAE,CAAC,GAAH,IAAU,IAAtC,GACZ,EAAE,CAAC,OADS,GAEZ,EAAE,CAAC,MAAH,GACE,EAAE,CAAC,QAAH,IAAe,EAAE,CAAC,WAAH,CAAe,OADhC,GAEE,EAAE,IAAI,EAJZ;AAKA,MAAI,IAAI,GAAG,OAAO,CAAC,IAAR,IAAgB,OAAO,CAAC,aAAnC;AACA,MAAM,IAAI,GAAG,OAAO,CAAC,MAArB;;AACA,MAAI,CAAC,IAAD,IAAS,IAAb,EAAmB;AACjB,QAAM,KAAK,GAAG,IAAI,CAAC,KAAL,CAAW,iBAAX,CAAd;AACA,IAAA,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC,CAAD,CAArB;AACD;;AAED,SACE,CAAC,IAAI,cAAO,QAAQ,CAAC,IAAD,CAAf,sBAAL,KACC,IAAI,IAAI,WAAW,KAAK,KAAxB,iBAAuC,IAAvC,IAAgD,EADjD,CADF;AAID;;AAED,SAAS,sBAAT,CAAiC,EAAjC,EAAwC;AACtC,MAAI,EAAE,CAAC,MAAH,IAAa,EAAE,CAAC,OAApB,EAA6B;AAC3B,QAAM,IAAI,GAAU,EAApB;AACA,QAAI,wBAAwB,GAAG,CAA/B;;AACA,WAAO,EAAP,EAAW;AACT,UAAI,IAAI,CAAC,MAAL,GAAc,CAAlB,EAAqB;AACnB,YAAM,IAAI,GAAQ,IAAI,CAAC,IAAI,CAAC,MAAL,GAAc,CAAf,CAAtB;;AACA,YAAI,IAAI,CAAC,WAAL,KAAqB,EAAE,CAAC,WAA5B,EAAyC;AACvC,UAAA,wBAAwB;AACxB,UAAA,EAAE,GAAG,EAAE,CAAC,OAAR;AACA;AACD,SAJD,MAIO,IAAI,wBAAwB,GAAG,CAA/B,EAAkC;AACvC,UAAA,IAAI,CAAC,IAAI,CAAC,MAAL,GAAc,CAAf,CAAJ,GAAwB,CAAC,IAAD,EAAO,wBAAP,CAAxB;AACA,UAAA,wBAAwB,GAAG,CAA3B;AACD;AACF;;AACD,MAAA,IAAI,CAAC,IAAL,CAAU,EAAV;AACA,MAAA,EAAE,GAAG,EAAE,CAAC,OAAR;AACD;;AACD,WAAO,qBAAqB,IAAI,CAC7B,GADyB,CACrB,UAAC,EAAD,EAAK,CAAL;AAAA,uBACH,CAAC,KAAK,CAAN,GAAU,OAAV,GAAoB,IAAI,MAAJ,CAAW,IAAI,CAAC,GAAG,CAAnB,CADjB,SAGH,KAAK,CAAC,OAAN,CAAc,EAAd,cACO,mBAAmB,CAAC,EAAE,CAAC,CAAD,CAAH,CAD1B,kBACyC,EAAE,CAAC,CAAD,CAD3C,yBAEI,mBAAmB,CAAC,EAAD,CALpB;AAAA,KADqB,EAQzB,IARyB,CAQpB,IARoB,CAA5B;AASD,GA3BD,MA2BO;AACL,mCAAwB,mBAAmB,CAAC,EAAD,CAA3C;AACD;AACF", "sourcesContent": ["/* eslint-disable no-console */\nimport Vuetify from '../framework'\n\nfunction createMessage (message: string, vm?: any, parent?: any): string | void {\n  if (Vuetify.config.silent) return\n\n  if (parent) {\n    vm = {\n      _isVue: true,\n      $parent: parent,\n      $options: vm,\n    }\n  }\n\n  if (vm) {\n    // Only show each message once per instance\n    vm.$_alreadyWarned = vm.$_alreadyWarned || []\n    if (vm.$_alreadyWarned.includes(message)) return\n    vm.$_alreadyWarned.push(message)\n  }\n\n  return `[Vuetify] ${message}` + (\n    vm ? generateComponentTrace(vm) : ''\n  )\n}\n\nexport function consoleInfo (message: string, vm?: any, parent?: any): void {\n  const newMessage = createMessage(message, vm, parent)\n  newMessage != null && console.info(newMessage)\n}\n\nexport function consoleWarn (message: string, vm?: any, parent?: any): void {\n  const newMessage = createMessage(message, vm, parent)\n  newMessage != null && console.warn(newMessage)\n}\n\nexport function consoleError (message: string, vm?: any, parent?: any): void {\n  const newMessage = createMessage(message, vm, parent)\n  newMessage != null && console.error(newMessage)\n}\n\nexport function deprecate (original: string, replacement: string, vm?: any, parent?: any) {\n  consoleWarn(`[UPGRADE] '${original}' is deprecated, use '${replacement}' instead.`, vm, parent)\n}\nexport function breaking (original: string, replacement: string, vm?: any, parent?: any) {\n  consoleError(`[BREAKING] '${original}' has been removed, use '${replacement}' instead. For more information, see the upgrade guide https://github.com/vuetifyjs/vuetify/releases/tag/v2.0.0#user-content-upgrade-guide`, vm, parent)\n}\nexport function removed (original: string, vm?: any, parent?: any) {\n  consoleWarn(`[REMOVED] '${original}' has been removed. You can safely omit it.`, vm, parent)\n}\n\n/**\n * Shamelessly stolen from vuejs/vue/blob/dev/src/core/util/debug.js\n */\n\nconst classifyRE = /(?:^|[-_])(\\w)/g\nconst classify = (str: string) => str\n  .replace(classifyRE, c => c.toUpperCase())\n  .replace(/[-_]/g, '')\n\nfunction formatComponentName (vm: any, includeFile?: boolean): string {\n  if (vm.$root === vm) {\n    return '<Root>'\n  }\n  const options = typeof vm === 'function' && vm.cid != null\n    ? vm.options\n    : vm._isVue\n      ? vm.$options || vm.constructor.options\n      : vm || {}\n  let name = options.name || options._componentTag\n  const file = options.__file\n  if (!name && file) {\n    const match = file.match(/([^/\\\\]+)\\.vue$/)\n    name = match && match[1]\n  }\n\n  return (\n    (name ? `<${classify(name)}>` : `<Anonymous>`) +\n    (file && includeFile !== false ? ` at ${file}` : '')\n  )\n}\n\nfunction generateComponentTrace (vm: any): string {\n  if (vm._isVue && vm.$parent) {\n    const tree: any[] = []\n    let currentRecursiveSequence = 0\n    while (vm) {\n      if (tree.length > 0) {\n        const last: any = tree[tree.length - 1]\n        if (last.constructor === vm.constructor) {\n          currentRecursiveSequence++\n          vm = vm.$parent\n          continue\n        } else if (currentRecursiveSequence > 0) {\n          tree[tree.length - 1] = [last, currentRecursiveSequence]\n          currentRecursiveSequence = 0\n        }\n      }\n      tree.push(vm)\n      vm = vm.$parent\n    }\n    return '\\n\\nfound in\\n\\n' + tree\n      .map((vm, i) => `${\n        i === 0 ? '---> ' : ' '.repeat(5 + i * 2)\n      }${\n        Array.isArray(vm)\n          ? `${formatComponentName(vm[0])}... (${vm[1]} recursive calls)`\n          : formatComponentName(vm)\n      }`)\n      .join('\\n')\n  } else {\n    return `\\n\\n(found in ${formatComponentName(vm)})`\n  }\n}\n"], "sourceRoot": "", "file": "console.js"}
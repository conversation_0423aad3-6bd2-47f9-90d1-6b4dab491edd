import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Inventory2'

export function GetTippingPageList(data) {
    const api = '/api/MtippingPrecheck/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetScanContainerCode(data) {
    const api = '/api/MtippingPrecheck/ScanContainerCode'
    return getRequestResources(baseURL, api, 'post', data);
}

 export function GetTippingPrecheckViewList(data) {
     const api = '/api/MtippingPrecheck/GetListView'
     return getRequestResources(baseURL, api, 'post', data);
 }
// export function GetTippingPrecheckViewList(data) {
//     const api = '/api/MtippingPrecheck/GetListDetial'
//     return getRequestResources(baseURL, api, 'post', data);
// }
export function GetTippingCount(data) {
    const api = '/api/MtippingPrecheck/GetCount'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetScanTraceCode(data) {
    const api = '/api/MtippingPrecheck/ScanTraceCode'
    return getRequestResources(baseURL, api, 'post', data);
}
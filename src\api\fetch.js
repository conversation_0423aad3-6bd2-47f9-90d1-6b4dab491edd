import { configUrl } from '@/config';
import request from '@/util/request';

// method请求方法, api接口路径, baseURL接口路径项目, data请求数据,没有时可不传
// isJoin, 除get外的请求方式需要拼接参数到url上时使用，为true即可
//noloading，为true则不会有遮罩。
export function getRequestResources(baseURL, api, method, data, isJoin, noloading,isTime,headers) {
    const obj = {
        url: configUrl[process.env.VUE_APP_SERVE][baseURL] + api,
        method
    }
    if (noloading) {
        obj.noloading = true
    } else {
        obj.noloading = false
    }
    if (method == 'get' || (data && isJoin)) {
        obj.params = data;
    } else {
        obj.data = data || {};
    }
    if (data?.responseType == 'blob') {
        obj.responseType = 'blob';
    }
    if(isTime){
        obj.timeout = 60*1000*10;
    }
    if(headers){
        obj.headers = headers;
    }
    return request(obj);
}

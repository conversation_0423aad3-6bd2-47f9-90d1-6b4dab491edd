import { hiprint, defaultElementTypeProvider } from '@/index';
let hiprintTemplate;
export const mixins = {
    data() {
        return {
            dataTemplate: null
        };
    },
    async mounted() {
        await this.initData();
    },
    watch: {
        dataTemplate(newValue, oldValue) {
            if (newValue) {
                this.initData();
            }
        }
    },
    methods: {
        initData() {
            hiprint.init({
                providers: [new defaultElementTypeProvider()]
            });
            // eslint-disable-next-line no-undef
            hiprint.PrintElementTypeManager.buildByHtml($('.ep-draggable-item'));
            // eslint-disable-next-line no-undef
            $('#hiprint-printTemplate').empty();
            hiprintTemplate = new hiprint.PrintTemplate({
                template: this.dataTemplate,
                // 图片选择功能
                fontList: [
                    { title: '微软雅黑', value: 'Microsoft YaHei' },
                    { title: '黑体', value: 'STHeitiSC-Light' },
                    { title: '思源黑体', value: 'SourceHanSansCN-Normal' },
                    { title: '王羲之书法体', value: '王羲之书法体' },
                    { title: '宋体', value: 'SimSun' },
                    { title: '华为楷体', value: 'STKaiti' },
                    { title: 'cursive', value: 'cursive' }
                ],
                dataMode: 1, // 1:getJson 其他：getJsonTid 默认1
                history: true, // 是否需要 撤销重做功能
                onDataChanged: (type, json) => {
                    console.log(type); // 新增、移动、删除、修改(参数调整)、大小、旋转
                    console.log(json); // 返回 template
                },
                onUpdateError: e => {
                    console.log(e);
                },
                settingContainer: '#PrintElementOptionSetting',
                paginationContainer: '.hiprint-printPagination'
            });
            hiprintTemplate.design('#hiprint-printTemplate');
            // 获取当前放大比例, 当zoom时传true 才会有
            this.scaleValue = hiprintTemplate.editingPanel.scale || 1;
        },
        PrintTemplateFn(item) {
            hiprintTemplate.print(item, {});
        }
    }
};

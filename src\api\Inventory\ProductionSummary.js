import { getRequestResources } from '@/api/fetch';
import store from '@/store';
const baseURL = 'baseURL_Inventory'
const baseURL2 = 'baseURL_MATERIAL'

export function GetPageList(data) {
    const api = '/api/ProductionSummaryView/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetList(data) {
    const api = '/api/ProductionSummaryDetailsView/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function Reverse(data) {
    const api = '/api/ProductionSummaryDetailsView/Reverse'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetReasonCode(data) {
    // 检查缓存是否存在且未过期（1小时）
    const cachedData = store.getters['dictionary/getDictionaryByCode']('ReasonCode');
    const lastUpdate = store.getters['dictionary/getLastUpdateTime'];
    const oneHour = 24 * 60 * 60 * 1000;
    
    if (cachedData && lastUpdate && (Date.now() - lastUpdate < oneHour)) {
        return Promise.resolve({ response: cachedData });
    }
    
    // 缓存不存在或已过期，从API获取
    const api = '/api/Dataitemdetail/GetReasonCode';
    return getRequestResources(baseURL, api, 'post', data).then(resp => {
        // 更新缓存
        store.dispatch('dictionary/saveDictionary', {
            code: 'ReasonCode',
            data: resp.response
        });
        return resp;
    });
}
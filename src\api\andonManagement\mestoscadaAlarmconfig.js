import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_ANDON'
// 接警组

//获取告警规则列表
export function getMestoscadaAlarmconfig(data) {
    const api =  '/andon/MestoscadaAlarmconfig/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//分页获取告警规则列表
export function getMestoscadaAlarmconfigList(data) {
    const api =  '/andon/MestoscadaAlarmconfig/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑告警规则
export function MestoscadaAlarmconfigSaveForm(data) {
    const api =  '/andon/MestoscadaAlarmconfig/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除告警规则
export function DeleteMestoscadaAlarmconfig(data) {
    const api =  '/andon/MestoscadaAlarmconfig/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

// //获取接警组详情列表
// export function getAlarmgroupPostList(data) {
//     const api =  '/andon/AlarmgroupPost/GetList'
//     return getRequestResources(baseURL, api, 'post', data, true);
// }
// //新增、编辑接警组详情
// export function AlarmgroupPostSaveForm(data) {
//     const api =  '/andon/AlarmgroupPost/SaveForm'
//     return getRequestResources(baseURL, api, 'post', data);
// }
// //删除接警组详情
// export function DeleteAlarmgroupPost(data) {
//     const api =  '/andon/AlarmgroupPost/Delete'
//     return getRequestResources(baseURL, api, 'post', data);
// }

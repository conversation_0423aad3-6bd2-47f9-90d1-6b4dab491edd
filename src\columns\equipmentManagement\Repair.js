export const RepairColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '维修工单号',
    Namevalue: "RepairCode",
    value: 'RepairWo',
    width: 130,
    sortable: true
},
{
    text: '生产线',
    Namevalue: "_CX",
    value: 'LineCode',
    width: 200,
    sortable: true
},
{
    text: '设备名称',
    value: 'DeviceName',
    width: 220,
    sortable: true
},
{
    text: '设备编号',
    value: 'DeviceCode',
    width: 130,
    sortable: true
},
{
    text: '工单来源',
    Namevalue: "_GDLY",
    value: 'Source',
    width: 160,
    sortable: true
},
{
    text: '工单类型',
    Namevalue: "_GDLX",
    value: 'Type',
    width: 160,
    sortable: true
},
{
    text: '保修内容',
    Namevalue: "_BXLR",
    value: 'Description',
    width: 200,
    sortable: true
},

{
    text: '状态',
    Namevalue: "_ZT",
    value: 'Status',
    width: 100,
    sortable: true
},
{
    text: '紧急度',
    Namevalue: "_JJD",
    value: 'Urgency',
    width: 150,
    sortable: true
},
{
    text: '是否停机',
    value: 'IsStop',
    Namevalue: "isStop",
    width: 160,
    sortable: true
},
{
    text: '关联单号',
    Namevalue: "_GLDH",
    value: 'ReferOrderNo',
    width: 150,
    sortable: true
}, {
    text: '成本中心',
    Namevalue: "_CBZX",
    value: 'CostCenter',
    width: 150,
    sortable: true
},
//  {
//     text: '指定完成日期',
//     Namevalue: "_ZDWCRQ",
//     value: 'FinishDate',
//     width: 150,
//     sortable: true
// }, 

{
    text: '当值主管',
    Namevalue: "_DZZG",
    value: 'DutyManagerName',
    width: 150,
    sortable: true
}, {
    text: '维修主管',
    Namevalue: "_ZPR",
    value: 'RepairManager',
    width: 150,
    sortable: true
}, {
    text: '维修开始时间',
    Namevalue: "Comstardate",
    value: 'StartDate',
    width: 150,
    sortable: true
}, {
    text: '维修结束时间',
    Namevalue: "Completedate",
    value: 'FinishDate',
    width: 150,
    sortable: true
}, {
    text: '接单人',
    Namevalue: "_JDR",
    value: 'ReceiveByName',
    width: 150,
    sortable: true
}, {
    text: '报修人',
    Namevalue: "_BXR",
    value: 'ReportByName',
    width: 150,
    sortable: true
}, {
    text: '报修时间',
    Namevalue: "_BXSJ",
    value: 'ReportDate',
    width: 150,
    sortable: true
}, {
    text: '确认人',
    Namevalue: "_QRR",
    value: 'ConfirmByName',
    width: 150,
    sortable: true
}, {
    text: '确认时间',
    Namevalue: "_QRSJ",
    value: 'ConfirmDate',
    width: 150,
    sortable: true
}, {
    text: '评价',
    Namevalue: "_PJ",
    value: 'Remark',
    width: 150,
    sortable: true
},
{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 160,
    sortable: true
}
];
export const RepairPlanColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '异常描述',
    value: 'ExceptionDesc',
    width: 160,
    sortable: true
},
{
    text: '维修过程描述',
    value: 'RepairProcess',
    width: 150,
    sortable: true
},
{
    text: '原因分析—现状',
    value: 'CurrentSituation',
    width: 160,
    sortable: true
},
{
    text: '原因分析',
    value: 'Reasons1',
    width: 160,
    sortable: true
},
{
    text: '消耗备件',
    value: 'Parts1',
    width: 160,
    sortable: true
},
{
    text: '消耗备件数量',
    value: 'Parts1Num',
    width: 160,
    sortable: true,
    align: 'right'
},
// {
//     text: '消耗备件单位',
//     value: 'Parts1Unit',
//     width: 160,
//     sortable: true
// },
// {
//     text: '维修状态',
//     dictionary: true,
//     value: 'RepairStatus',
//     width: 160,
//     sortable: true
// },
// {
//     text: '维修性质',
//     value: 'RepairNature',
//     width: 160,
//     sortable: true
// },
{
    text: '承修人',
    value: 'RepairUser',
    width: 160,
    sortable: true
},
{
    text: '开始时间',
    value: 'StartTime',
    width: 160,
    sortable: true
},
{
    text: '结束时间',
    value: 'EndTime',
    width: 160,
    sortable: true
},
{
    text: '维修时长',
    value: 'RepairHours',
    width: 160,
    sortable: true,
    align: 'right'
},
// {
//     text: '维修后评价',
//     value: 'RepairAppraise',
//     width: 160,
//     sortable: true
// },
// {
//     text: '评价人',
//     value: 'AppraiseUser',
//     width: 160,
//     sortable: true
// },
// {
//     text: '不满意原因',
//     value: 'DissatisfiedReason',
//     width: 160,
//     sortable: true
// },
{
    text: '故障现象',
    value: 'FaultPhenomenon',
    width: 160,
    sortable: true
},
// {
//     text: '故障性质分类',
//     value: 'FaultNature',
//     width: 160,
//     sortable: true
// },
// {
//     text: '照片地址',
//     value: 'PicturePath',
//     width: 160,
//     sortable: true
// },
{
    text: '是否作为案例',
    value: 'IsCase',
    dictionary: true,
    width: 160,
    sortable: true
},
{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 160,
    sortable: true
}
];
export const RepairPlanColumQRcode = [{
    text: '序号',
    value: 'Index',
    width: 60,
    sortable: true
},

{
    text: '设备编号',
    value: 'DeviceCode',
    width: 120,
    sortable: true
},
{
    text: '设备名称',
    value: 'DeviceName',
    width: 120,
    sortable: true
},
{
    text: '物料编号',
    value: 'PartsCode1',
    width: 130,
    sortable: true
},
{
    text: '物料名称',
    width: 160,
    value: 'Parts1',
    sortable: true
},
{
    text: '出库数量',
    width: 160,
    value: 'Parts1Num',
    sortable: true,
    align: 'right'
},
{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 120,
    sortable: true
}
];
export const CommissioningRecordsColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '调试开始时间',
    value: 'Dealtime',
    width: 160,
    sortable: true
},
{
    text: '调试结束时间',
    value: 'Fixedtime',
    width: 160,
    sortable: true
},
{
    text: '处理人姓名',
    value: 'Maintenanceperson',
    width: 130,
    sortable: true,
    dictionary: true
},
// {
//     text: '确认时间',
//     value: 'Confirmtime',
//     width: 130,
//     sortable: true
// },
// {
//     text: '确认人姓名',
//     value: 'Confirmperson',
//     width: 130,
//     sortable: true
// },
// {
//     text: '调试类型',
//     value: 'Debugtype',
//     width: 130,
//     sortable: true
// },
// {
//     text: '调试代码',
//     value: 'Debugcode',
//     width: 130,
//     sortable: true
// },
{
    text: '调试描述',
    value: 'Failuredescription',
    width: 130,
    sortable: true
},
{
    text: '备注',
    value: 'Remark',
    width: 130,
    sortable: true
},
// {
//     text: '状态',
//     value: 'Status',
//     width: 130,
//     sortable: true
// },
// {
//     text: '使用工具',
//     value: 'Tools',
//     width: 130,
//     sortable: true
// },
{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 160,
    sortable: true
}
];

export const servicecostColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '备件成本',
    value: 'PartsRepairPrice',
    width: 130,
    sortable: true,
    align: 'right'
},
{
    text: '人工成本',
    value: 'RepairPrice',
    width: 130,
    sortable: true,
    align: 'right'
}
];
export const wxjlColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '指派人',
    Namevalue: "_ZPR",
    value: 'AssignBy',
    width: 130,
    sortable: true,
},
{
    text: '计划开始时间',
    Namevalue: "_JHKS",
    value: 'PlanStartDate',
    width: 130,
    sortable: true,
},
{
    text: '计划结束时间',
    Namevalue: "_JHJS",
    value: 'PlanFinishDate',
    width: 130,
    sortable: true,
}, {
    text: '接单人',
    Namevalue: "_JDR",
    value: 'ReceiveBy',
    width: 130,
    sortable: true,
}, {
    text: '开始处理时间',
    Namevalue: "_CLKS",
    value: 'StartDate',
    width: 130,
    sortable: true,
}, {
    text: '结束处理时间',
    Namevalue: "_CLJS",
    value: 'FinishDate',
    width: 130,
    sortable: true,
}, {
    text: '维修时长',
    Namevalue: "_WXSC",
    value: 'RepairDuration',
    width: 130,
    sortable: true,
}, {
    text: '维修状态',
    Namevalue: "_WXZT",
    value: 'Status',
    width: 130,
    sortable: true,
}, {
    text: '维修过程描述',
    Namevalue: "_WXGC",
    value: 'RepairRecordDesc',
    width: 130,
    sortable: true,
}, {
    text: '原因分析',
    Namevalue: "_YYFX",
    value: 'ReasonResult',
    width: 130,
    sortable: true,
}, {
    text: '原因分析结论',
    Namevalue: "_YYFXJL",
    value: 'RepairNature',
    width: 130,
    sortable: true,
}, {
    text: '系统分类',
    Namevalue: "_XTFL",
    value: 'FaultCategory',
    width: 130,
    sortable: true,
}, {
    text: '原因分类',
    Namevalue: "_YYFL",
    value: 'ReasonCategory',
    width: 130,
    sortable: true,
}, {
    text: '维修评价',
    Namevalue: "_WXPJ",
    value: 'FaultNature',
    width: 130,
    sortable: true,
}, {
    text: '备注',
    Namevalue: "Remark",
    value: 'Remark',
    width: 130,
    sortable: true,
},
];
export const bjColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '备件编号',
    Namevalue: "_BJBH",
    value: 'PartsCode',
    width: 130,
    sortable: true,
}, {
    text: '备件名称',
    Namevalue: "_BJMC",
    value: 'PartsName',
    width: 130,
    sortable: true,
}, {
    text: '备件类型',
    Namevalue: "_BJLX",
    value: 'PartsType',
    width: 130,
    sortable: true,
}, {
    text: '规格型号',
    Namevalue: "_GGXH",
    value: 'PartsModel',
    width: 130,
    sortable: true,
}, {
    text: '批次号',
    Namevalue: "_PCH",
    value: 'BatchCode',
    width: 130,
    sortable: true,
}, {
    text: '供应商',
    Namevalue: "_GYS",
    value: 'Supplier',
    width: 130,
    sortable: true,
}, {
    text: '数量',
    Namevalue: "_SL",
    value: 'Qty',
    width: 130,
    sortable: true,
}, {
    text: '单位',
    Namevalue: "_DW",
    value: 'Unit',
    width: 130,
    sortable: true,
},
]
export const fwcgtopColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '服务单号',
    Namevalue: "_FWDH",
    value: 'ServiceNo',
    width: 100,
    sortable: true,
}, {
    text: '申请人',
    Namevalue: "_SQR",
    value: 'RequestByName',
    width: 100,
    sortable: true,
}, {
    text: '需求原因',
    Namevalue: "_XQYY",
    value: 'RequestReason',
    width: 100,
    sortable: true,
}, {
    text: '服务内容',
    Namevalue: "_FWNR",
    value: 'RequestContent',
    width: 100,
    sortable: true,
}, {
    text: '服务状态',
    Namevalue: "_FWZT",
    value: 'Status',
    width: 100,
    sortable: true,
}, {
    text: '是否需要看现场',
    Namevalue: "_KXC",
    value: 'IsLookSite',
    width: 100,
    sortable: true,
}, {
    text: 'REQ号',
    Namevalue: "_REQ",
    value: 'ApplyReqNo',
    width: 100,
    sortable: true,
}, {
    text: '审批意见',
    Namevalue: "_SPYJ",
    value: 'ApplyApproveAdvice',
    width: 100,
    sortable: true,
}, {
    text: 'PR号',
    Namevalue: "_PR",
    value: 'PurchasePrNo',
    width: 100,
    sortable: true,
}, {
    text: 'PO号',
    Namevalue: "_PO",
    value: 'PurchasePoNo',
    width: 100,
    sortable: true,
}, {
    text: '供应商',
    Namevalue: "_GYS",
    value: 'PurchaseSupplier',
    width: 100,
    sortable: true,
}, {
    text: '服务日期',
    Namevalue: "_FWRQ",
    value: 'ServiceDate',
    width: 100,
    sortable: true,
}, {
    text: '工程是否接受',
    Namevalue: "_SFJS",
    value: 'OtherAccept',
    width: 100,
    sortable: true,
}, {
    text: '验收意见',
    Namevalue: "_YSYJ",
    value: 'AcceptanceAdvice',
    width: 100,
    sortable: true,
}, {
    text: '用家确认',
    Namevalue: "_YJQR",
    value: 'UserConfirmBy',
    width: 100,
    sortable: true,
}, {
    text: '部门领导签批',
    Namevalue: "_BMLDQP",
    value: 'DepartmentMgrBy',
    width: 100,
    sortable: true,
}, {
    text: '验收编号',
    Namevalue: "_YSBH",
    value: 'AcceptanceNo',
    width: 100,
    sortable: true,
}, {
    text: '验收日期',
    Namevalue: "_YSRQ",
    value: 'AcceptanceAdviceDate',
    width: 100,
    sortable: true,
}, {
    text: '备注',
    Namevalue: "Remark",
    value: 'Remark',
    width: 100,
    sortable: true,
}, {
    text: '操作',
    value: 'actions',
    Namevalue: "action",
    width: 150,
    sortable: true
},
]
export const fwcgdownColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '检验项目',
    Namevalue: "_JYXM",
    value: 'AcceptanceItem',
    sortable: true,
}, {
    text: '是否合格',
    Namevalue: "_SFHG",
    value: 'Result',
    width: 100,
    sortable: true,
}, {
    text: '详述不合格理由',
    Namevalue: "_BHGLY",
    value: 'FailDesc',
    sortable: true,
},
]
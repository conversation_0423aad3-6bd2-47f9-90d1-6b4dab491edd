<template>
    <!-- 通知记录 -->
    <v-dialog v-model="dialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ operaObj.ID ? '编辑通知记录' : '新增通知记录' }}
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" class="mt-8 mb-2">
                    <v-row>
                        <!-- 通知方式 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.PushType"
                                :rules="rules.PushType"
                                :items="pushTypeList"
                                item-text="ItemName"
                                item-value="ItemValue"
                                label="通知方式"
                                dense
                                outlined
                            ></v-select>
                        </v-col>
                        <!-- 接收者 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.Reciver" :rules="rules.Reciver" label="接收者" required dense outlined />
                        </v-col>
                        <!-- 内容 -->
                        <v-col :cols="12" :lg="12">
                            <v-textarea v-model="form.NoticeContent" :rules="rules.NoticeContent" rows="2" label="内容" required dense outlined />
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn color="normal" @click="closeForm">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { NoticeRecordSaveForm } from '@/api/andonManagement/noticeRecord.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        pushTypeList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            checkbox: true,
            valid: true,
            dialog: false,
            form: {
                ID: '',
                PushType: null,
                NoticeContent	: '',
                Reciver: ''
            },
            rules: {
                PushType: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                NoticeContent	: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                Reciver: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            }
        };
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal) {
                    this.desserts = [];
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        //
        closeForm() {
            this.$emit('handlePopup', 'refresh');
            this.dialog = false;
        },
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                const res = await NoticeRecordSaveForm({ ...this.form });
                const { success, msg } = res;
                if(success){
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    this.$refs.form.reset();
                    this.$emit('handlePopup', 'refresh');
                    if (this.operaObj.ID || this.checkbox) {
                        this.closeForm()
                    }
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>
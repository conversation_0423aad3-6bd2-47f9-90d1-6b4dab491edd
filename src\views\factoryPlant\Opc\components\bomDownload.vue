<template>
    <div class="bom-download">
        <div class="title">
            <span>{{ currentObj.Name }}</span>
            <span>
                <!-- <v-icon>mdi-pencil</v-icon> -->
                <v-icon style="margin-left:10px" @click="close()">mdi-close</v-icon>
            </span>
        </div>
        <!-- <div class="tips">Standard interface for downloading BOM items. | 30000</div> -->
        <div class="tips">{{ currentObj.Description }} | {{ currentObj.Timeout }}</div>
        <v-divider style="margin-top:10px"></v-divider>
        <div class="content-box">
            <div class="top">
                <div class="log">
                    <span class="small-tit">Log Transsctions</span>
                    <span class="enabled" v-if="currentObj.LogTransactions == '1'"> <v-icon size="16">mdi-check</v-icon>
                        &nbsp;Enabled</span>
                    <span class="disabled" v-if="currentObj.LogTransactions == '0'"><v-icon size="15">mdi-cancel</v-icon>
                        &nbsp;Disabled</span>
                </div>
                <div class="block-read" style="margin-left:10px">
                    <span class="small-tit">Block Read</span>
                    <span class="enabled" v-if="currentObj.BlockRead == '1'"> <v-icon size="16">mdi-check</v-icon>
                        &nbsp;Enabled</span>
                    <span class="disabled" v-if="currentObj.BlockRead == '0'"><v-icon size="15">mdi-cancel</v-icon>
                        &nbsp;Disabled</span>
                </div>
                <div class="allow" style="margin-left:10px">
                    <span class="small-tit">Allow Multiple Instances</span>
                    <span class="enabled" v-if="currentObj.AllowMultipleInstances == '1'"> <v-icon
                            size="16">mdi-check</v-icon>
                        &nbsp;Enabled</span>
                    <span class="disabled" v-if="currentObj.AllowMultipleInstances == '0'"><v-icon
                            size="15">mdi-cancel</v-icon>
                        &nbsp;Disabled</span>
                </div>
            </div>
            <div class="middle">
                <div class="tab-row">
                    <a-tabs type="card" size="small" v-model="activeKey">
                        <a-tab-pane v-for="tab in tabList" :key="tab" :tab="tab">
                            <instances :currentObj="currentObj" v-if="tab == 'Instances'" />
                            <triggers :currentObj="currentObj" v-if="tab == 'Triggers'" />
                            <properties :currentObj="currentObj" v-if="tab == 'Properties'" />
                        </a-tab-pane>
                    </a-tabs>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import instances from './instances.vue'
import properties from './properties.vue'
import triggers from './triggers.vue'
export default {
    components: {
        instances,
        triggers,
        properties
    },
    props: {
        currentObj: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            tabList: ['Instances', 'Properties', 'Triggers'],
            activeKey: 'Instances'
        }
    },
    created() {

    },
    methods: {
        close() {
            this.$emit('closeDrawer')
        }
    }
}
</script>

<style lang="scss" scoped>
.bom-download {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.title {
    display: flex;
    justify-content: space-between;
    font-size: 18px;
    font-weight: 600;
}

.content-box {
    flex: 1;
    margin-top: 5px;
    border: 1px solid #eee;
    border-radius: 4px;

    .top {
        width: 100%;
        display: flex;
        padding: 5px;
        background: #f5f5f5;
        border-bottom: 1px solid #eee;

        .small-tit {
            font-weight: 600;
        }
    }

    .middle {
        padding: 7px 0 5px;

        .tab-row {
            padding: 0 3px;
        }
    }
}

.enabled,
.disabled {
    color: #fff;
    display: flex;
    width: 95px;
    align-items: center;
    border-radius: 4px;
    padding: 0 8px;
    margin-top: 5px;

    .v-icon {
        color: #fff;
    }
}

.enabled {
    background: #008000;
}

.disabled {
    background: #ff0000;
}
</style>
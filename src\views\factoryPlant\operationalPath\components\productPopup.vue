<template>
    <v-card>
        <v-card-title class="headline primary lighten-2" primary-title>{{ $t('DFM_GYLX._TJGYLXCP') }}</v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid">
                <v-row class="mt-5">
                    <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <v-combobox
                            :items="MaterialList"
                            @change="changeMaterial"
                            item-text="ManufacturePn"
                            item-value="Code"
                            @update:search-input="getKeywords"
                            no-data-text="暂无数据"
                            clearable
                            dense
                            :rules="[v => !!v || $t('GLOBAL._MANDATORY')]"
                            required
                            v-model="Materialselected"
                            outlined
                            :label="$t('DFM_GYLX.MaterialCode')"
                        >
                            <template slot="append-outer">
                                <v-btn @click="searchData">
                                    <v-icon>mdi-magnify</v-icon>
                                </v-btn>
                            </template>
                        </v-combobox>
                    </v-col>
                    <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX.MaterialName')" disabled dense outlined v-model="form.MaterialName"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX.MaterialVersion')" disabled dense outlined v-model="form.MaterialVersion"></v-text-field>
                    </v-col>

                    <v-col class="pt-0 pb-0" :cols="12" :lg="12">
                        <v-textarea :label="$t('DFM_GYLX.Remark')" v-model="form.Remark" :value="form.Remark" outlined height="70"></v-textarea>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>

        <v-divider></v-divider>

        <v-card-actions>
            <!-- <v-spacer></v-spacer> -->
            <v-checkbox class="mr-auto" v-model="isChecked" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { getMaterialList, savePathProduct } from '../service';
export default {
    props: {
        currentSelectId: {
            type: String,
            default: ''
        },
        selectProductObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            valid: false,
            isChecked: true,
            keywords: '',
            MaterialList: [],
            Materialselected: '',
            form: {
                MaterialVersion: '',
                MaterialCode: '',
                MaterialName: '',
                Remark: ''
            }
        };
    },
    async created() {
        await this.getMaterialSelectList();
        if (this.selectProductObj && this.selectProductObj.ID) {
            this.Materialselected = this.MaterialList.find(item => item.Code === this.selectProductObj.MaterialCode);
            for (const key in this.form) {
                this.form[key] = this.selectProductObj[key];
            }
            this.form.ID = this.selectProductObj.ID;
        }
    },
    methods: {
        changeMaterial(item) {
            if (!item) {
                this.form.MaterialVersion = '';
                this.form.MaterialName = '';
                this.form.MaterialCode = '';
                return false;
            }
            this.form.MaterialVersion = item.Version;
            this.form.MaterialName = item.Description;
            this.form.MaterialCode = item.Code;
        },
        async getMaterialSelectList() {
            let resp = await getMaterialList({ page: 1, intPageSize: 100, key: this.keywords });
            this.MaterialList = resp.response.data;
        },
        getKeywords(val) {
            this.keywords = val;
        },
        resetForm() {
            this.form = {
                MaterialVersion: '',
                MaterialCode: '',
                MaterialName: '',
                Remark: ''
            };
            this.Materialselected = '';
        },
        // 表单提交
        async submitForm() {
            if (!this.$refs.form.validate()) return false;

            let resp = await savePathProduct({ RoutingId: this.currentSelectId, ...this.form });
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
            this.resetForm();
            this.$emit('getProductTableList');
            if (this.isChecked) {
                this.isChecked = !this.isChecked;
                this.$emit('handlePopup', false, 'product');
            }
        },
        closePopup() {
            this.$emit('handlePopup', false, 'product');
        },
        searchData() {
            console.log(this.keywords);
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep(.v-input__append-outer) {
    margin-top: 0 !important;
}
</style>

<template>
    <div class="box">
        <!-- <addrows></addrows> -->
    </div>
</template>
<script>
export default {
    // components: { addrows: () => import('../views/equipmentManagement/RepairPlan/components/repairAdd.vue') },
    data() {
        return {};
    },
    computed: {},
    created() { },
    methods: {}
};
</script>
<style lang="scss" scoped>
.box {
    width: 600px;
    margin: 60px auto;
}
</style>
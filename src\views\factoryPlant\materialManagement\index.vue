// 物料管理
<template>
    <div class="material-view">
        <TreeView :items="treeData" :title="$t('DFM_WLGL._WLFL')" @clickClassTree="clickTree"></TreeView>
        <div class="material-view-main">
            <SearchForm ref="contactTorm" :searchinput="searchinput" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary">
                        <v-icon @click="getDataList">mdi-cached</v-icon>
                    </v-btn>
                    <!-- <v-btn color="primary" @click="viewMaterial">查 看</v-btn> -->
                    <!-- 新增 -->
                    <v-btn color="primary" v-has="'WLGL_ADD'" @click="operaClick({})">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <!-- <v-btn color="primary" @click="copyClick">复制新增</v-btn> -->
                    <!-- 批量删除 -->
                    <!-- <v-btn color="primary" v-has="'WLGL_ALLREMOVE'" @click="deleteItems()">{{ $t('GLOBAL._PLSC') }}</v-btn> -->
                    <v-btn color="primary" v-has="'WLGL_WLFL'" @click="selectMenu('primaryClassification')">{{
                        $t('DFM_WLGL._WLFL') }}</v-btn>
                    <v-btn color="primary" @click="pushToMaterialGroup">{{ $t('DFM_WLGL._WLFZ') }}</v-btn>
                    <!-- <v-menu>
                        <template #activator="{ on, attrs }">
                            <v-btn v-bind="attrs" color="primary" v-on="on">更 多</v-btn>
                        </template>
<v-list>
    <v-list-item v-for="(item, index) in moreMenu" :key="index" :disabled="!item.dialog"
        @click="selectMenu(item.dialog)">
        <v-list-item-title>{{ item.title }}</v-list-item-title>
    </v-list-item>
</v-list>
</v-menu> -->
                </div>
                <Tables :dictionaryList="dictionaryList" :headers="headers" :desserts="desserts"
                    :tableHeight="showFrom ? 'calc(100vh - 250px)' : 'calc(100vh - 190px)'" :loading="loading"
                    :page-options="pageOptions" :btn-list="btnList" table-name="DFM_WLGL" @selectePages="selectePages"
                    @itemSelected="selectedItems" @toggleSelectAll="selectedItems" @tableClick="tableClick">
                    <template #Deleted="{ item }">
                        <v-switch v-model="item.Deleted" @change="(val) => handleChangeDeleted(val, item)"
                            style="height: 30px; transform: scale(0.8); transform-origin: left"
                            class="ma-0 ba-0"></v-switch>
                    </template>
                </Tables>
                <copyOne-dialog ref="copyOne" :material-obj="materialObj" @handlePopup="handlePopup"></copyOne-dialog>
                <classification-dialog ref="classification" :headers="classificationHeaders"
                    :classification-dialog="classDialog" @handlePopup="handlePopup"></classification-dialog>
                <viewTheMaterial-dialog ref="viewTheMaterial"></viewTheMaterial-dialog>
                <attributeTypes-dialog ref="attributeTypes"></attributeTypes-dialog>
                <materialImport-dialog ref="materialImport"></materialImport-dialog>
                <operaTheMaterial-dialog ref="operaTheMaterial" :materialTeamList="materialTeamList"
                    :classifyList="classifyList" :material-obj="materialObj" :IsSpecial="IsSpecial"
                    @handlePopup="handlePopup"></operaTheMaterial-dialog>
                <!-- 属性扩展 -->
                <AttributeDialog ref="attribute"></AttributeDialog>
            </v-card>
        </div>
    </div>
</template>
<script>
import { getMaterialTree, GetPageList, DeleteMaterials, saveForm } from '@/api/factoryPlant/material.js';
import { getSelectClassifyList } from './service';
import { materialsColum, parimaryClassification, sencondClassification } from '@/columns/factoryPlant/materialManagement.js';
export default {
    name: 'MaterialManagement',
    components: {
        ViewTheMaterialDialog: () => import('./components/viewTheMaterialDialog.vue'),
        OperaTheMaterialDialog: () => import('./components/operaTheMaterialDialog.vue'),
        CopyOneDialog: () => import('./components/copyOneDialog.vue'),
        ClassificationDialog: () => import('./components/classificationDialog.vue'),
        attributeTypesDialog: () => import('./components/attributeTypesDialog.vue'),
        materialImportDialog: () => import('./components/materialImportDialog.vue'),
        AttributeDialog: () => import('./components/attributeDialog.vue')
    },
    data() {
        return {
            Code: '',
            treeID: '',
            showFrom: false,
            treeClick: true,
            moreMenu: [
                { title: '一级分类管理', dialog: 'primaryClassification' },
                { title: '二级分类管理', dialog: 'secondaryClassification' },
                { title: '附加类型类型', dialog: 'attributeTypes' },
                { title: '附加类型管理', dialog: '' },
                { title: '物料导入', dialog: 'import' },
                { title: '物料模板导出', dialog: '' },
                { title: '物料变更履历', dialog: '' }
            ],
            classDialog: '',
            deleteId: '',
            // tree 字典数据
            treeData: [],
            materialObj: {},
            searchParams: {},
            headers: materialsColum,
            classificationHeaders: parimaryClassification,
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            selectedList: [],
            desserts: [],
            classifyList: [],
            materialTeamList: [],
            IsSpecial: [
                { k: '1', v: '是' },
                { k: '0', v: '否' }
            ]
        };
    },
    computed: {
        //查询条件
        searchinput() {
            return [
                // {
                //     key: 'Code',
                //     value: '',
                //     icon: '',
                //     label: '物料料号',
                //     placeholder: '请输入物料料号'
                // },
                // {
                //     key: 'Plant',
                //     value: '',
                //     icon: '',
                //     // label: '厂别',
                //     label: this.$t('$vuetify.dataTable.DFM_WLGL.Plant'),
                //     placeholder: ''
                // },
                {
                    key: 'Description',
                    value: '',
                    icon: '',
                    // label: '物料描述',
                    label: this.$t('DFM_WLGL.Description'),
                    placeholder: ''
                }
            ];
        },
        btnList() {
            return [
                { text: this.$t('DFM_WLGL.copy'), icon: '', code: 'copy', type: 'primary', authCode: 'WLGL_COPY' },
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary', authCode: 'WLGL_EDIT' },
                // { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'red', authCode: 'WLGL_DELETE' }
            ];
        },
        dictionaryList() {
            return [
                { arr: this.IsSpecial, key: 'IsSpecial', val: 'k', text: 'v' },
                { arr: this.classifyList, key: 'Type', val: 'Code', text: 'Name' },
                { arr: this.materialTeamList, key: 'Categorycode', val: 'Code', text: 'Name' }
            ];
        }
    },
    async mounted() {
        await this.getClassifyList();
        await this.getMaterialTeamList();
        await this.getTreeList();
        this.getDataList();
    },
    methods: {
        // 跳转物料分组
        pushToMaterialGroup() {
            this.$router.push('/materialManagement/materialGroup')
        },
        async handleChangeDeleted(val, item) {
            let params = JSON.parse(JSON.stringify(item))
            params.Deleted = val ? 0 : 1
            try {
                await saveForm({ ...params })
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
            } catch {
                this.getDataList()
            }
        },
        async getClassifyList() {
            let resp = await getSelectClassifyList({ Identities: 'MaterialType' });
            this.classifyList = resp.response;
        },
        async getMaterialTeamList() {
            let resp = await getSelectClassifyList({ Identities: 'MaterialGroup' });
            this.materialTeamList = resp.response;
        },
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        // 操作栏按钮
        tableClick(item, type) {
            switch (type) {
                // 复制
                case 'copy':
                    this.copyClick(item);
                    break;
                // 编辑
                case 'edit':
                    this.operaClick(item);
                    break;
                // 删除
                case 'delete':
                    this.deleteId = item.ID;
                    this.sureDelete();
                    break;
                case 'attribute':
                    this.$refs.attribute.dialog = true;
                    this.$refs.attribute.initData(item);
                    return;
                default:
                    break;
            }
        },
        // 获取全部表格数据
        async getDataList() {
            this.loading = true;
            const o = this.treeClick ? {
                Description: ''
            } : {
                type: ''
            }
            let params = {
                ...this.searchParams,
                type: this.Code,
                ...o,
                SearchType: "ALL",
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await GetPageList(params);
            const { success, response } = res || {};
            const { data, dataCount, page, pageCount, pageSize } = response || {};
            if (success) {
                data.map(item => {
                    item.Deleted = item.Deleted === 0 ? true : false
                    return item
                })
                this.desserts = data;
                this.pageOptions.total = dataCount;
                this.pageOptions.page = page;
            } else {
                this.desserts = [];
            }
            this.loading = false;
        },
        // 获取树型
        async getTreeList() {
            let params = {
                Identities: 'MaterialType'
            };
            const res = await getSelectClassifyList(params);
            const { success, response } = res || {};
            if (success) {
                response.forEach(element => {
                    element.name = element.Name;
                    element.id = element.ID;
                });
                this.treeData = response || [];
            }
        },
        // 点击树状传回数值
        clickTree(v) {
            this.treeClick = true;
            // 切换查询清空
            this.Code = v.Code;
            // this.treeID = v.ID;
            this.searchinput.forEach(element => {
                element.value = '';
            });
            this.pageOptions.pageCount = 1;
            this.getDataList();
        },
        // 查询数据
        searchForm(v) {
            this.treeClick = false;
            this.searchParams = v;
            this.getDataList();
        },
        // 查看物料
        viewMaterial() {
            this.$refs.viewTheMaterial.viewDialog = true;
        },
        // 新增/编辑物料
        operaClick(v) {
            this.materialObj = v;
            this.$refs.operaTheMaterial.opraeDialog = true;
        },
        // 复制新增
        copyClick(v) {
            this.materialObj = v;
            this.$refs.copyOne.copyDialog = true;
        },
        // 批量删除
        async deleteItems() {
            if (this.selectedList.length > 0) {
                this.deleteId = '';
                this.sureDelete();
            } else {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'error' });
            }
        },
        // 确认删除
        sureDelete() {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            }).then(async () => {
                const params = [];
                if (this.deleteId) {
                    params.push(this.deleteId);
                } else {
                    this.selectedList.forEach(e => {
                        params.push(e.ID);
                    });
                }
                const res = await DeleteMaterials(params);
                this.selectedList = [];
                this.deleteId = '';
                const { success, msg } = res;
                if (success) {
                    this.pageOptions.pageCount = 1;
                    this.getDataList();
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                }
            });
        },
        //选择更多
        selectMenu(dialog) {
            switch (dialog) {
                case 'primaryClassification':
                case 'secondaryClassification':
                    this.$refs.classification.dialog = true;
                    this.classDialog = dialog;
                    this.classificationHeaders = dialog === 'primaryClassification' ? parimaryClassification : sencondClassification;
                    break;

                case 'attributeTypes':
                    this.$refs.attributeTypes.sttributeTypesDialog = true;
                    break;
                case 'import':
                    this.$refs.materialImport.materialImportDialog = true;
                    break;

                default:
                    break;
            }
        },
        // 根据子组件返回来的类型来进行操作
        handlePopup(type, data) {
            switch (type) {
                case 'opear':
                    this.getDataList();
                    break;
                case 'upp':
                    this.isUpdate = false;
                    break;
                case 'detail':
                    this.selectDetailObj = {};
                    if (data) this.selectDetailObj = data;
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.material-view {
    display: flex;

    .material-view-main {
        width: 100%;
        overflow: hidden;
    }
}
</style>

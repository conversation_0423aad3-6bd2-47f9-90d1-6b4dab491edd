<template>
    <v-card>
        <v-card-title class="text-h6 justify-space-between primary lighten-2">
            <!-- 设备录入 -->
            {{ editItemObj.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ') }}
        </v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid">
                <v-row class="mt-4">
                    <v-col class="pt-0 pb-0" :cols="12" :lg="6">
                        <!-- <v-text-field v-model="form.StopReason" outlined dense></v-text-field> -->
                        <v-autocomplete v-model="form.StopReason" :items="reasonList" required item-value="name" item-text="name" outlined clearable dense label="停机原因"></v-autocomplete>
                    </v-col>
                    <v-col class="pt-0 pb-0" :cols="12" :lg="6">
                        <v-text-field type="datetime-local" v-model="form.StartTime" outlined dense label="开始时间"></v-text-field>
                    </v-col>
                    <v-col class="pt-0 pb-0" :cols="12" :lg="6">
                        <v-text-field type="datetime-local" v-model="form.EndTime" outlined dense label="结束时间"></v-text-field>
                    </v-col>
                    <v-col class="pt-0 pb-0" :cols="12" :lg="6">
                        <v-text-field v-model="form.Duration" outlined dense label="时长(分钟)"></v-text-field>
                    </v-col>
                    <v-col class="pt-0 pb-0" :cols="12" :lg="6">
                        <v-text-field v-model="form.LossNum" outlined dense label="损失量"></v-text-field>
                    </v-col>
                    <v-col class="pt-0 pb-0" :cols="12" :lg="6">
                        <v-text-field v-model="form.ReworkNum" outlined dense label="返工量"></v-text-field>
                    </v-col>
                    <v-col class="pt-0 pb-0" :cols="12" :lg="12">
                        <v-textarea height="60px" v-model="form.Remark" outlined dense label="备注"></v-textarea>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions class="pa-5 lighten-3">
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="submitForm()">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn @click="closePopup()">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { saveDetailForm, updateDetailForm } from '../service';
import { GetReasontree } from '@/api/factoryPlant/reasonDetail.js';
export default {
    props: {
        type: {
            type: String,
            default: 'add'
        },
        editItemObj: {
            type: Object,
            default: () => {}
        },
        selectList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            reasonList: [],
            valid: false,
            form: {
                StopReason: '',
                StartTime: '',
                EndTime: '',
                Duration: '',
                LossNum: '',
                ReworkNum: '',
                Remark: ''
            }
        };
    },
    created() {
        this.getTreeList();
        if (this.editItemObj && this.editItemObj.ID) {
            for (const key in this.form) {
                this.form[key] = this.editItemObj[key];
            }
            this.form.ID = this.editItemObj.ID;
        }
    },
    methods: {
        //查询原因列表
        async getTreeList() {
            const res = await GetReasontree();
            const { success, response } = res;
            if (success) {
                let arr = [];
                let list = response.find(item => item.name == 'OEE损失类型')?.children;
                if (list && list.length) {
                    list.forEach(item => {
                        if (item.children && item.children.length) {
                            item.children.forEach(itm => {
                                arr.push(itm);
                            });
                        }
                    });
                }
                this.reasonList = arr;
            } else {
                this.reasonList = [];
            }
        },

        async submitForm() {
            if (!this.$refs.form.validate()) return false;
            if (this.type == 'add') {
                await saveDetailForm({ Records: this.selectList, ...this.form });
            } else if (this.type == 'edit') {
                await updateDetailForm({ ...this.editItemObj, ...this.form });
            }
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
            this.$refs.form.reset();
            if (this.type == 'edit') {
                this.$emit('getDetailData');
            }
            this.$emit('closePopup');
        },

        closePopup() {
            this.$emit('closePopup');
        }
    }
};
</script>

<style>
</style>
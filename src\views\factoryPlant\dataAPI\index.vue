// 数据API
<template>
    <div class="basic-process-view">
        <div class="basic-process-main">
            <SearchForm class="" ref="contactTorm" :searchinput="searchinput" :show-from="showFrom"
                @searchForm="searchForm" />
            <v-card class="ma-1" outlined>
                <div class="form-btn-list">
                    <!-- 搜索栏 -->
                    <!-- <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn> -->
                    <v-btn icon color="primary">
                        <v-icon @click="getDataList">mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'SJAPI_ADD'" @click="operaClick({})">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="error" v-has="'SJAPI_ALLREMOVE'" @click="deleteItems()"
                        :disabled="selectedList.length == 0">{{
                            $t('GLOBAL._PLSC')
                        }}</v-btn>
                </div>
                <Tables :desserts="desserts" :loading="loading" :page-options="pageOptions" :btn-list="btnList"
                    :headers="headers" :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="DFM_DATAAPI" @selectePages="selectePages" @itemSelected="selectedItems"
                    @toggleSelectAll="selectedItems" @tableClick="tableClick"></Tables>
            </v-card>
            <update-dialog ref="updateDialog" :opera-obj="operaObj" @handlePopup="handlePopup"></update-dialog>
        </div>
    </div>
</template>
<script>
import { DeleteDataApi, DataApiGetList } from '@/api/factoryPlant/dataAPI.js';
import { dataAPIColumns } from '@/columns/factoryPlant/dataAPI.js';
export default {
    name: 'MaterialRelationshipMapping',
    components: {
        UpdateDialog: () => import('./components/updateDialog.vue')
    },
    data() {
        return {
            operaObj: {},
            showFrom: false,
            deleteId: '',
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            headers: dataAPIColumns,
            selectedList: [],
            desserts: [],
            searchParams: {}
        };
    },
    computed: {
        //查询条件
        searchinput() {
            return [
                // 关键字
                {
                    key: 'key',
                    value: '',
                    icon: '',
                    label: '关键字'
                }
            ];
        },
        btnList() {
            return [
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary', authCode: 'SJAPI_EDIT' },
                { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'red', authCode: 'SJAPI_DELETE' }
            ];
        }
    },
    async created() {
        await this.getDataList();
    },
    methods: {
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        // 操作栏按钮
        tableClick(item, type) {
            switch (type) {
                // 编辑
                case 'edit':
                    this.operaClick(item);
                    break;
                // 删除
                case 'delete':
                    this.deleteId = item.ID;
                    this.sureDelete();
                    break;
                default:
                    break;
            }
        },
        // 获取全部表格数据
        async getDataList() {
            this.loading = true;
            let params = {
                ...this.searchParams,
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await DataApiGetList(params);
            const { success, response } = res || {};
            const { data, dataCount, page } = response
            if (success) {
                data.forEach(e => {
                    if (e.Sql.length > 183) e.HandleSql = e.Sql.substr(0, 90) + '……' + e.Sql.substr(e.Sql.length - 90, 90)
                    else e.HandleSql = e.Sql
                });
                this.desserts = data;
                this.pageOptions.total = dataCount
                this.pageOptions.page = page
            } else {
                this.desserts = [];
            }
            this.loading = false;
        },
        // 查询数据
        searchForm(v) {
            this.searchParams = v;
            this.getDataList();
        },
        // 新增/编辑基础工序
        operaClick(o) {
            this.operaObj = o;
            this.$refs.updateDialog.updateDialog = true;
        },
        // 批量删除
        async deleteItems() {
            this.deleteId = '';
            this.sureDelete();
        },
        // 确认删除
        sureDelete() {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            }).then(async () => {
                const params = [];
                if (this.deleteId) {
                    params.push(this.deleteId);
                } else {
                    this.selectedList.forEach(e => {
                        params.push(e.ID);
                    });
                }
                const res = await DeleteDataApi(params);
                this.selectedList = [];
                this.deleteId = '';
                const { success, msg } = res;
                if (success) {
                    this.pageOptions.pageCount = 1;
                    this.getDataList();
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                }
            });
        },
        // 根据子组件返回来值
        handlePopup() {
            this.getDataList();
        }
    }
};
</script>
<style lang="scss" scoped>
.basic-process-view {
    display: flex;

    .basic-process-main {
        width: 100%;
        overflow: hidden;
    }
}
</style>

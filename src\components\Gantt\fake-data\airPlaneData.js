const airPlaneData = [
    {
        id: '1',
        code: '11',
        type: '张三',
        layout: '',
        height: '40',
        timeBlock: [
            // {
            //     "id": "1",
            //     "startTime": new Date('Fri Jul 29 2021 4:50:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 11:50:24 GMT+0800 (中国标准时间)')
            // },
            {
                id: '2',
                startTime: new Date('Fri Jul 29 2021 7:20:24 GMT+0800 (中国标准时间)'),
                endTime: new Date('Fri Jul 29 2021 9:00:24 GMT+0800 (中国标准时间)')
            }
            // {
            //     "id": "3",
            //     "startTime": new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 5:50:24 GMT+0800 (中国标准时间)')
            // },
            // {
            //     "id": "1",
            //     "startTime": new Date('Fri Jul 29 2021 14:50:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 16:50:24 GMT+0800 (中国标准时间)')
            // },
            // {
            //     "id": "4",
            //     "startTime": new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 5:50:24 GMT+0800 (中国标准时间)')
            // },
            // {
            //     "id": "5",
            //     "startTime": new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 5:50:24 GMT+0800 (中国标准时间)')
            // },
            // {
            //     "id": "6",
            //     "startTime": new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 8:30:24 GMT+0800 (中国标准时间)')
            // }
        ]
    },
    {
        id: '2',
        code: '12',
        type: '李四',
        height: '40',
        layout: '',
        timeBlock: [
            {
                id: '4',
                startTime: new Date('Fri Jul 29 2021 19:00:24 GMT+0800 (中国标准时间)'),
                endTime: new Date('Fri Jul 29 2021 23:59:24 GMT+0800 (中国标准时间)')
            }
        ]
    },
    {
        id: '3',
        code: '13',
        type: '王五',
        height: '40',
        layout: '',
        timeBlock: [
            {
                id: '1',
                startTime: new Date('Fri Jul 29 2021 4:50:24 GMT+0800 (中国标准时间)'),
                endTime: new Date('Fri Jul 29 2021 11:50:24 GMT+0800 (中国标准时间)')
            }
            // {
            //     "id": "2",
            //     "startTime": new Date('Fri Jul 29 2021 7:20:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 9:00:24 GMT+0800 (中国标准时间)')
            // },
            // {
            //     "id": "3",
            //     "startTime": new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 5:50:24 GMT+0800 (中国标准时间)')
            // },
            // {
            //     "id": "4",
            //     "startTime": new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 5:50:24 GMT+0800 (中国标准时间)')
            // },
            // {
            //     "id": "5",
            //     "startTime": new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 5:50:24 GMT+0800 (中国标准时间)')
            // },
            // {
            //     "id": "6",
            //     "startTime": new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 8:30:24 GMT+0800 (中国标准时间)')
            // }
        ]
    },
    {
        id: '4',
        code: '14',
        type: '赵六',
        height: '40',
        layout: '',
        timeBlock: []
    },
    {
        id: '5',
        code: '15',
        type: '李七',
        height: '40',
        layout: '',
        timeBlock: [
            {
                id: '1',
                startTime: new Date('Fri Jul 29 2021 4:50:24 GMT+0800 (中国标准时间)'),
                endTime: new Date('Fri Jul 29 2021 11:50:24 GMT+0800 (中国标准时间)')
            }
            // {
            //     "id": "2",
            //     "startTime": new Date('Fri Jul 29 2021 7:20:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 9:00:24 GMT+0800 (中国标准时间)')
            // },
            // {
            //     "id": "3",
            //     "startTime": new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 5:50:24 GMT+0800 (中国标准时间)')
            // },
            // {
            //     "id": "4",
            //     "startTime": new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 5:50:24 GMT+0800 (中国标准时间)')
            // },
            // {
            //     "id": "5",
            //     "startTime": new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 5:50:24 GMT+0800 (中国标准时间)')
            // },
            // {
            //     "id": "6",
            //     "startTime": new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
            //     "endTime": new Date('Fri Jul 29 2021 8:30:24 GMT+0800 (中国标准时间)')
            // }
        ]
    },
    {
        id: '6',
        code: '16',
        type: 'A320',
        layout: '',
        timeBlock: []
    },
    {
        id: '7',
        code: '17',
        type: 'A320',
        layout: '',
        timeBlock: []
    },
    {
        id: '8',
        code: '18',
        type: 'A320',
        layout: '',
        timeBlock: [
            {
                id: '1',
                startTime: new Date('Fri Jul 29 2021 4:50:24 GMT+0800 (中国标准时间)'),
                endTime: new Date('Fri Jul 29 2021 11:50:24 GMT+0800 (中国标准时间)')
            },
            {
                id: '2',
                startTime: new Date('Fri Jul 29 2021 7:20:24 GMT+0800 (中国标准时间)'),
                endTime: new Date('Fri Jul 29 2021 9:00:24 GMT+0800 (中国标准时间)')
            },
            {
                id: '3',
                startTime: new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
                endTime: new Date('Fri Jul 29 2021 5:50:24 GMT+0800 (中国标准时间)')
            },
            {
                id: '4',
                startTime: new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
                endTime: new Date('Fri Jul 29 2021 5:50:24 GMT+0800 (中国标准时间)')
            }
        ]
    },
    {
        id: '9',
        code: '19',
        type: 'A320',
        layout: '',
        timeBlock: []
    },
    {
        id: '10',
        code: '20',
        type: 'A320',
        layout: '',
        timeBlock: []
    },
    {
        id: '11',
        code: '21',
        type: 'A320',
        layout: '',
        timeBlock: []
    },
    {
        id: '12',
        code: '21',
        type: 'A320',
        layout: '',
        timeBlock: [
            {
                id: '4',
                startTime: new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
                endTime: new Date('Fri Jul 29 2021 5:50:24 GMT+0800 (中国标准时间)')
            },
            {
                id: '5',
                startTime: new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
                endTime: new Date('Fri Jul 29 2021 5:50:24 GMT+0800 (中国标准时间)')
            },
            {
                id: '6',
                startTime: new Date('Fri Jul 29 2021 3:50:24 GMT+0800 (中国标准时间)'),
                endTime: new Date('Fri Jul 29 2021 8:30:24 GMT+0800 (中国标准时间)')
            }
        ]
    },
    {
        id: '13',
        code: '21',
        type: 'A320',
        layout: '',
        timeBlock: []
    },
    {
        id: '14',
        code: '21',
        type: 'A320',
        layout: '',
        timeBlock: []
    },
    {
        id: '15',
        code: '21',
        type: 'A320',
        layout: '',
        timeBlock: []
    },
    {
        id: '16',
        code: '21',
        type: 'A320',
        layout: '',
        timeBlock: []
    }
];
export default airPlaneData;

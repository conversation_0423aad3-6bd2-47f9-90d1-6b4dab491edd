<template>
    <v-dialog v-model="showDialog" max-width="1080px">
        <v-card v-if="dialogType == 'cg'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('TPM_SBGL_FWDGL._CG') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" :sm="item.sm ? item.sm : 3" :md="item.sm ? item.sm : 3" v-for="(item, index) in cgList" :key="index">
                            <v-text-field v-if="item.type == 'input'" :disabled="item.disabled" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                            <v-autocomplete
                                v-if="item.type == 'select'"
                                clearable
                                v-model="item.value"
                                :items="item.options"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="item.label"
                                clear
                                dense
                                outlined
                            ></v-autocomplete>
                            <v-menu
                                v-if="item.type == 'date' || item.type == 'datetime'"
                                :ref="'menu' + index"
                                v-model="menu[index]"
                                :close-on-content-click="false"
                                :nudge-right="40"
                                transition="scale-transition"
                                offset-y
                                max-width="290px"
                                min-width="290px"
                            >
                                <template #activator="{ on, attrs }">
                                    <v-text-field
                                        v-model="item.value"
                                        :clearable="item.isClearable ? item.isClearable : true"
                                        outlined
                                        dense
                                        :label="item.label"
                                        readonly
                                        v-bind="attrs"
                                        v-on="on"
                                    ></v-text-field>
                                </template>
                                <v-date-picker :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></v-date-picker>
                            </v-menu>
                            <div class="textfieldbox">
                                <v-text-field
                                    v-model="item.value"
                                    :clearable="item.isClearable ? item.isClearable : true"
                                    outlined
                                    dense
                                    v-if="item.type == 'time'"
                                    :label="item.label"
                                    readonly
                                ></v-text-field>
                                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-if="item.type == 'time'" v-model="item.value" type="datetime" :placeholder="item.label"></el-date-picker>
                            </div>
                            <el-radio-group v-model="item.value" v-if="item.type == 'radio'">
                                <div class="textlabel">{{ item.label }}:</div>
                                <el-radio :label="it.value" :key="ind" v-for="(it, ind) in item.radiolist">{{ it.label }}</el-radio>
                            </el-radio-group>
                        </v-col>
                        <!-- <v-col class="py-0 px-3" cols="12" :sm="6" :md="6">
                            <div class="textlabel">{{ $t('$vuetify.dataTable.TPM_SBGL_FWDGL.spjg') }}:</div>
                            <el-radio v-model="spjg" label="1">{{ $t('GLOBAL._TG') }}</el-radio>
                            <el-radio v-model="spjg" label="0">{{ $t('GLOBAL._BH') }}</el-radio>
                        </v-col> -->
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('cg')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card v-if="dialogType == 'cd'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('TPM_SBGL_FWDGL._CD') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" :sm="item.sm ? item.sm : 3" :md="item.sm ? item.sm : 3" v-for="(item, index) in cdList" :key="index">
                            <v-text-field v-if="item.type == 'input'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                            <v-autocomplete
                                v-if="item.type == 'select'"
                                clearable
                                v-model="item.value"
                                :items="item.options"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="item.label"
                                clear
                                dense
                                outlined
                            ></v-autocomplete>
                            <v-menu
                                v-if="item.type == 'date' || item.type == 'datetime'"
                                :ref="'menu' + index"
                                v-model="menu[index]"
                                :close-on-content-click="false"
                                :nudge-right="40"
                                transition="scale-transition"
                                offset-y
                                max-width="290px"
                                min-width="290px"
                            >
                                <template #activator="{ on, attrs }">
                                    <v-text-field
                                        v-model="item.value"
                                        :clearable="item.isClearable ? item.isClearable : true"
                                        outlined
                                        dense
                                        :label="item.label"
                                        readonly
                                        v-bind="attrs"
                                        v-on="on"
                                    ></v-text-field>
                                </template>
                                <v-date-picker :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></v-date-picker>
                            </v-menu>
                            <div class="textfieldbox">
                                <v-text-field
                                    v-model="item.value"
                                    :clearable="item.isClearable ? item.isClearable : true"
                                    outlined
                                    dense
                                    v-if="item.type == 'time'"
                                    :label="item.label"
                                    readonly
                                ></v-text-field>
                                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-if="item.type == 'time'" v-model="item.value" type="datetime" :placeholder="item.label"></el-date-picker>
                            </div>
                            <el-radio-group v-model="item.value" v-if="item.type == 'radio'">
                                <div class="textlabel">{{ item.label }}:</div>
                                <el-radio :label="it.value" :key="ind" v-for="(it, ind) in item.radiolist">{{ it.label }}</el-radio>
                            </el-radio-group>
                        </v-col>
                        <!-- <v-col class="py-0 px-3" cols="12" :sm="6" :md="6">
                            <div class="textlabel">{{ $t('$vuetify.dataTable.TPM_SBGL_FWDGL.spjg') }}:</div>
                            <el-radio v-model="spjg" label="1">{{ $t('GLOBAL._TG') }}</el-radio>
                            <el-radio v-model="spjg" label="0">{{ $t('GLOBAL._BH') }}</el-radio>
                        </v-col> -->
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('cd')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card v-if="dialogType == 'ys'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('TPM_SBGL_FWDGL._YS') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" :sm="item.sm ? item.sm : 3" :md="item.sm ? item.sm : 3" v-for="(item, index) in ysList" :key="index">
                            <v-text-field v-if="item.type == 'input'" :disabled="item.disabled" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                            <v-autocomplete
                                v-if="item.type == 'select'"
                                clearable
                                v-model="item.value"
                                :items="item.options"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="item.label"
                                clear
                                dense
                                outlined
                            ></v-autocomplete>
                            <v-menu
                                v-if="item.type == 'date' || item.type == 'datetime'"
                                :ref="'menu' + index"
                                v-model="menu[index]"
                                :close-on-content-click="false"
                                :nudge-right="40"
                                transition="scale-transition"
                                offset-y
                                max-width="290px"
                                min-width="290px"
                            >
                                <template #activator="{ on, attrs }">
                                    <v-text-field
                                        v-model="item.value"
                                        :clearable="item.isClearable ? item.isClearable : true"
                                        outlined
                                        dense
                                        :label="item.label"
                                        readonly
                                        v-bind="attrs"
                                        v-on="on"
                                    ></v-text-field>
                                </template>
                                <v-date-picker :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></v-date-picker>
                            </v-menu>
                            <div class="textfieldbox">
                                <v-text-field
                                    v-model="item.value"
                                    :clearable="item.isClearable ? item.isClearable : true"
                                    outlined
                                    dense
                                    v-if="item.type == 'time'"
                                    :label="item.label"
                                    readonly
                                ></v-text-field>
                                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-if="item.type == 'time'" v-model="item.value" type="datetime" :placeholder="item.label"></el-date-picker>
                            </div>
                            <el-radio-group v-model="item.value" v-if="item.type == 'radio'">
                                <div class="textlabel">{{ item.label }}:</div>
                                <el-radio :label="it.value" :key="ind" v-for="(it, ind) in item.radiolist">{{ it.label }}</el-radio>
                            </el-radio-group>
                        </v-col>
                    </v-row>
                </v-form>
                <jytable ref="jytable" :SupplierServiceId="tableItem.ID" :tableHeight="'calc(40vh) !important'"></jytable>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('ys')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card v-if="dialogType == 'yjqr'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('TPM_SBGL_FWDGL._yjqr') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" :sm="item.sm ? item.sm : 3" :md="item.sm ? item.sm : 3" v-for="(item, index) in yjqrList" :key="index">
                            <v-text-field v-if="item.type == 'input'" :disabled="true" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                            <v-autocomplete
                                :disabled="true"
                                v-if="item.type == 'select'"
                                clearable
                                v-model="item.value"
                                :items="item.options"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="item.label"
                                clear
                                dense
                                outlined
                            ></v-autocomplete>
                            <v-menu
                                v-if="item.type == 'date' || item.type == 'datetime'"
                                :ref="'menu' + index"
                                v-model="menu[index]"
                                :disabled="true"
                                :close-on-content-click="false"
                                :nudge-right="40"
                                transition="scale-transition"
                                offset-y
                                max-width="290px"
                                min-width="290px"
                            >
                                <template #activator="{ on, attrs }">
                                    <v-text-field
                                        v-model="item.value"
                                        :clearable="item.isClearable ? item.isClearable : true"
                                        outlined
                                        dense
                                        :label="item.label"
                                        readonly
                                        v-bind="attrs"
                                        v-on="on"
                                    ></v-text-field>
                                </template>
                                <v-date-picker :disabled="true" :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></v-date-picker>
                            </v-menu>
                            <div class="textfieldbox">
                                <v-text-field
                                    :disabled="true"
                                    v-model="item.value"
                                    :clearable="item.isClearable ? item.isClearable : true"
                                    outlined
                                    dense
                                    v-if="item.type == 'time'"
                                    :label="item.label"
                                    readonly
                                ></v-text-field>
                                <el-date-picker
                                    :disabled="true"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    v-if="item.type == 'time'"
                                    v-model="item.value"
                                    type="datetime"
                                    :placeholder="item.label"
                                ></el-date-picker>
                            </div>
                            <el-radio-group :disabled="item.disabled" v-model="item.value" v-if="item.type == 'radio'">
                                <div class="textlabel">{{ item.label }}:</div>
                                <el-radio :label="it.value" :key="ind" v-for="(it, ind) in item.radiolist">{{ it.label }}</el-radio>
                            </el-radio-group>
                        </v-col>
                    </v-row>
                </v-form>
                <jytable ref="jytable" :SupplierServiceId="tableItem.ID" :tableHeight="'calc(40vh) !important'"></jytable>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('yjqr')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card v-if="dialogType == 'ldys'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('TPM_SBGL_FWDGL._LDYS') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" :sm="item.sm ? item.sm : 3" :md="item.sm ? item.sm : 3" v-for="(item, index) in ldyslist" :key="index">
                            <v-text-field v-if="item.type == 'input'" :disabled="true" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                            <v-autocomplete
                                :disabled="true"
                                v-if="item.type == 'select'"
                                clearable
                                v-model="item.value"
                                :items="item.options"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="item.label"
                                clear
                                dense
                                outlined
                            ></v-autocomplete>
                            <v-menu
                                v-if="item.type == 'date' || item.type == 'datetime'"
                                :ref="'menu' + index"
                                v-model="menu[index]"
                                :disabled="true"
                                :close-on-content-click="false"
                                :nudge-right="40"
                                transition="scale-transition"
                                offset-y
                                max-width="290px"
                                min-width="290px"
                            >
                                <template #activator="{ on, attrs }">
                                    <v-text-field
                                        v-model="item.value"
                                        :clearable="item.isClearable ? item.isClearable : true"
                                        outlined
                                        dense
                                        :label="item.label"
                                        readonly
                                        v-bind="attrs"
                                        v-on="on"
                                    ></v-text-field>
                                </template>
                                <v-date-picker :disabled="true" :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></v-date-picker>
                            </v-menu>
                            <div class="textfieldbox">
                                <v-text-field
                                    :disabled="true"
                                    v-model="item.value"
                                    :clearable="item.isClearable ? item.isClearable : true"
                                    outlined
                                    dense
                                    v-if="item.type == 'time'"
                                    :label="item.label"
                                    readonly
                                ></v-text-field>
                                <el-date-picker
                                    :disabled="true"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    v-if="item.type == 'time'"
                                    v-model="item.value"
                                    type="datetime"
                                    :placeholder="item.label"
                                ></el-date-picker>
                            </div>
                            <el-radio-group :disabled="item.disabled" v-model="item.value" v-if="item.type == 'radio'">
                                <div class="textlabel">{{ item.label }}:</div>
                                <el-radio :label="it.value" :key="ind" v-for="(it, ind) in item.radiolist">{{ it.label }}</el-radio>
                            </el-radio-group>
                        </v-col>
                    </v-row>
                </v-form>
                <jytable ref="jytable" :SupplierServiceId="tableItem.ID" :tableHeight="'calc(40vh) !important'"></jytable>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('ldys')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card v-if="dialogType == 'bh'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._BH') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" :sm="item.sm ? item.sm : 3" :md="item.sm ? item.sm : 3" v-for="(item, index) in bhList" :key="index">
                            <v-text-field v-if="item.type == 'input'" :disabled="item.disabled" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                            <v-autocomplete
                                v-if="item.type == 'select'"
                                clearable
                                v-model="item.value"
                                :items="item.options"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="item.label"
                                clear
                                dense
                                outlined
                            ></v-autocomplete>
                            <v-menu
                                v-if="item.type == 'date' || item.type == 'datetime'"
                                :ref="'menu' + index"
                                v-model="menu[index]"
                                :close-on-content-click="false"
                                :nudge-right="40"
                                transition="scale-transition"
                                offset-y
                                max-width="290px"
                                min-width="290px"
                            >
                                <template #activator="{ on, attrs }">
                                    <v-text-field
                                        v-model="item.value"
                                        :clearable="item.isClearable ? item.isClearable : true"
                                        outlined
                                        dense
                                        :label="item.label"
                                        readonly
                                        v-bind="attrs"
                                        v-on="on"
                                    ></v-text-field>
                                </template>
                                <v-date-picker :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></v-date-picker>
                            </v-menu>
                            <div class="textfieldbox">
                                <v-text-field
                                    v-model="item.value"
                                    :clearable="item.isClearable ? item.isClearable : true"
                                    outlined
                                    dense
                                    v-if="item.type == 'time'"
                                    :label="item.label"
                                    readonly
                                ></v-text-field>
                                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-if="item.type == 'time'" v-model="item.value" type="datetime" :placeholder="item.label"></el-date-picker>
                            </div>
                            <el-radio-group v-model="item.value" v-if="item.type == 'radio'">
                                <div class="textlabel">{{ item.label }}:</div>
                                <el-radio :label="it.value" :key="ind" v-for="(it, ind) in item.radiolist">{{ it.label }}</el-radio>
                            </el-radio-group>
                        </v-col>
                        <!-- <v-col class="py-0 px-3" cols="12" :sm="6" :md="6">
                            <div class="textlabel">{{ $t('$vuetify.dataTable.TPM_SBGL_FWDGL.spjg') }}:</div>
                            <el-radio v-model="spjg" label="1">{{ $t('GLOBAL._TG') }}</el-radio>
                            <el-radio v-model="spjg" label="0">{{ $t('GLOBAL._BH') }}</el-radio>
                        </v-col> -->
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('bh')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { GetRepairServiceReject, GetRepairServiceRequest, GetRepairServicePurchase, GetRepairServiceAccept, GetUserConfirm, GetManagerConfirm } from '@/api/equipmentManagement/ServicOrder.js';
import { fwcgdownColum } from '@/columns/equipmentManagement/Repair.js';
import jytable from '@/views/equipmentManagement/ServicOrder/components/jytable.vue';
import { Message, MessageBox } from 'element-ui';
import moment from 'moment';
import { GetPersonList } from '@/api/equipmentManagement/Equip.js';

export default {
    components: {
        jytable
    },
    props: {
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            fwcgdownColum,
            loading2: false,
            desserts2: [],
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            spjg: '',
            menu: [],
            FileList: [],
            UserAcceptanceData: [],
            LeaderAcceptanceData: [],
            bhList: [
                {
                    disabled: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.Requestcontent'),
                    value: '',
                    sm: 12,
                    id: 'RequestContent',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.reqh') + ' *',
                    value: '',
                    sm: 6,
                    id: 'ApplyReqNo',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.spyj'),
                    value: '',
                    sm: 6,
                    id: 'ApplyApproveAdvice',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.spr'),
                    value: '',
                    sm: 6,
                    id: 'ApplyApproveBy',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.spsj'),
                    value: '',
                    sm: 6,
                    id: 'ApplyApproveDate',
                    type: 'time'
                },
                {
                    disabled: true,
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.spjg') + ' *',
                    value: 'NG',
                    sm: 6,
                    id: 'ApplyApproveResult',
                    type: 'radio',
                    radiolist: [
                        {
                            label: this.$t('GLOBAL._BH'),
                            value: 'NG'
                        }
                    ]
                }
            ],
            cgList: [
                {
                    disabled: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.Requestcontent'),
                    value: '',
                    sm: 12,
                    id: 'RequestContent',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.reqh') + ' *',
                    value: '',
                    sm: 6,
                    id: 'ApplyReqNo',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.spyj'),
                    value: '',
                    sm: 6,
                    id: 'ApplyApproveAdvice',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.spr'),
                    value: '',
                    sm: 6,
                    id: 'ApplyApproveBy',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.spsj'),
                    value: '',
                    sm: 6,
                    id: 'ApplyApproveDate',
                    type: 'time'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.spjg') + ' *',
                    value: '',
                    sm: 6,
                    id: 'ApplyApproveResult',
                    type: 'radio',
                    radiolist: [
                        {
                            label: this.$t('GLOBAL._TG'),
                            value: 'OK'
                        },
                        {
                            label: this.$t('GLOBAL._BH'),
                            value: 'NG'
                        }
                    ]
                }
            ],
            cdList: [
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.Requestcontent') + ' *',
                    value: '',
                    sm: 12,
                    id: 'RequestContent',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.prh') + ' *',
                    value: '',
                    sm: 6,
                    id: 'PurchasePrNo',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.poh') + ' *',
                    value: '',
                    sm: 6,
                    id: 'PurchasePoNo',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.PurchasePoItem') + ' *',
                    value: '',
                    sm: 6,
                    id: 'PurchasePoItem',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.PurchasePoLine') + ' *',
                    value: '',
                    sm: 6,
                    id: 'PurchasePoLine',
                    type: 'input'
                },

                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.gys'),
                    value: '',
                    sm: 6,
                    id: 'PurchaseSupplier',
                    type: 'input'
                },
                {
                    // label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.spr'),
                    // value: '',
                    // sm: 6,
                    // id: 'PurchaseApproveBy',
                    // type: 'input'
                },
                {
                    // label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.spsj'),
                    // value: '',
                    // sm: 6,
                    // id: 'PurchaseApproveDate',
                    // type: 'time'
                },
                {
                    // require: true,
                    // label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.spjg') + ' *',
                    // value: '',
                    // sm: 6,
                    // id: 'PurchaseApproveResult',
                    // type: 'radio',
                    // radiolist: [
                    //     {
                    //         label: this.$t('GLOBAL._TG'),
                    //         value: 'OK'
                    //     },
                    //     {
                    //         label: this.$t('GLOBAL._BH'),
                    //         value: 'NG'
                    //     }
                    // ]
                }
            ],
            ysList: [
                {
                    disabled: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.Requestcontent'),
                    value: '',
                    sm: 12,
                    id: 'RequestContent',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.ysyj') + ' *',
                    value: '',
                    sm: 6,
                    id: 'AcceptanceAdvice',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.ysjg') + ' *',
                    value: '',
                    sm: 6,
                    id: 'AcceptanceResult',
                    type: 'radio',
                    radiolist: [
                        {
                            label: this.$t('GLOBAL._TG'),
                            value: 'OK'
                        },
                        {
                            label: this.$t('GLOBAL._BTG'),
                            value: 'NG'
                        }
                    ]
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.yssj') + ' *',
                    value: '',
                    sm: 6,
                    id: 'AcceptanceAdviceDate',
                    type: 'time'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.shsj'),
                    value: '',
                    sm: 6,
                    id: 'ServiceDate',
                    type: 'time'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.sfjs') + ' *',
                    value: '',
                    sm: 12,
                    id: 'OtherAccept',
                    type: 'radio',
                    radiolist: [
                        {
                            label: this.$t('GLOBAL._TG'),
                            value: 'OK'
                        },
                        {
                            label: this.$t('GLOBAL._BH'),
                            value: 'NG'
                        }
                    ]
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.yjqs'),
                    value: '',
                    sm: 6,
                    id: 'UserConfirmBy',
                    type: 'select',
                    options: []
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.bmsp'),
                    value: '',
                    sm: 6,
                    id: 'FactoryMgrBy',
                    type: 'select',
                    options: []
                }
            ],
            yjqrList: [
                {
                    disabled: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.Requestcontent'),
                    value: '',
                    sm: 12,
                    id: 'RequestContent',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.ysyj') + ' *',
                    value: '',
                    sm: 6,
                    id: 'AcceptanceAdvice',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.ysjg') + ' *',
                    disabled: true,
                    value: '',
                    sm: 6,
                    id: 'AcceptanceResult',
                    type: 'radio',
                    radiolist: [
                        {
                            label: this.$t('GLOBAL._TG'),
                            value: 'OK'
                        },
                        {
                            label: this.$t('GLOBAL._BTG'),
                            value: 'NG'
                        }
                    ]
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.yssj') + ' *',
                    value: '',
                    sm: 6,
                    id: 'AcceptanceAdviceDate',
                    type: 'time'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.shsj'),
                    value: '',
                    sm: 6,
                    id: 'ServiceDate',
                    type: 'time'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.sfjs') + ' *',
                    value: '',
                    sm: 12,
                    id: 'OtherAccept',
                    disabled: true,
                    type: 'radio',
                    radiolist: [
                        {
                            label: this.$t('GLOBAL._TG'),
                            value: 'OK'
                        },
                        {
                            label: this.$t('GLOBAL._BH'),
                            value: 'NG'
                        }
                    ]
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.yjqs') + ' *',
                    value: '',
                    sm: 6,
                    id: 'DepartmentMgrResult',
                    type: 'radio',
                    radiolist: [
                        {
                            label: this.$t('GLOBAL._TG'),
                            value: 'OK'
                        },
                        {
                            label: this.$t('GLOBAL._BH'),
                            value: 'NG'
                        }
                    ]
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.bmsp'),
                    value: '',
                    sm: 6,
                    id: 'FactoryMgrBy',
                    type: 'select',
                    options: []
                }
            ],
            ldyslist: [
                {
                    disabled: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.Requestcontent'),
                    value: '',
                    sm: 12,
                    id: 'RequestContent',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.ysyj') + ' *',
                    value: '',
                    sm: 6,
                    id: 'AcceptanceAdvice',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.ysjg') + ' *',
                    disabled: true,
                    value: '',
                    sm: 6,
                    id: 'AcceptanceResult',
                    type: 'radio',
                    radiolist: [
                        {
                            label: this.$t('GLOBAL._TG'),
                            value: 'OK'
                        },
                        {
                            label: this.$t('GLOBAL._BTG'),
                            value: 'NG'
                        }
                    ]
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.yssj') + ' *',
                    value: '',
                    sm: 6,
                    id: 'AcceptanceAdviceDate',
                    type: 'time'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.shsj'),
                    value: '',
                    sm: 6,
                    id: 'ServiceDate',
                    type: 'time'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.sfjs') + ' *',
                    value: '',
                    sm: 12,
                    id: 'OtherAccept',
                    disabled: true,
                    type: 'radio',
                    radiolist: [
                        {
                            label: this.$t('GLOBAL._TG'),
                            value: 'OK'
                        },
                        {
                            label: this.$t('GLOBAL._BH'),
                            value: 'NG'
                        }
                    ]
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.yjqs'),
                    value: '',
                    sm: 6,
                    id: 'UserConfirmBy',
                    type: 'select',
                    options: []
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_FWDGL.bmsp') + ' *',
                    value: '',
                    sm: 6,
                    id: 'FactoryMgrResult',
                    type: 'radio',
                    radiolist: [
                        {
                            label: this.$t('GLOBAL._TG'),
                            value: 'OK'
                        },
                        {
                            label: this.$t('GLOBAL._BH'),
                            value: 'NG'
                        }
                    ]
                }
            ]
        };
    },
    computed: {
        locale() {
            return this.$store.state.app.locale || 'zh';
        }
    },
    watch: {
        // dialogType(val) {
        //     console.log(val,123)
        //     if (val == 'ys') {
        //         setTimeout(() => {
        //             this.$refs.jytable.getData(this.tableItem);
        //         }, 100);
        //     }
        // }
    },
    async mounted() {
        let UserAcceptance = await GetPersonList('UserAcceptance');
        this.UserAcceptanceData = UserAcceptance.response[0].ChildNodes;
        this.UserAcceptanceData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.ysList[6].options = this.UserAcceptanceData;
        this.ldyslist[6].options = this.UserAcceptanceData;
        let LeaderAcceptance = await GetPersonList('LeaderAcceptance');
        this.LeaderAcceptanceData = LeaderAcceptance.response[0].ChildNodes;
        this.LeaderAcceptanceData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.ysList[7].options = this.LeaderAcceptanceData;
        this.yjqrList[7].options = this.LeaderAcceptanceData;
    },
    methods: {
        load(item, type) {
            if (type == 'ys' || type == 'yjqr' || type == 'ldys') {
                setTimeout(() => {
                    this.$refs.jytable.getData(this.tableItem);
                }, 100);
            }
            if (type == 'cg') {
                this.cgList.forEach(it => {
                    for (let k in item) {
                        if (it.id == k) {
                            if (it.id == 'ApplyApproveResult') {
                                it.value = Number(item[k]);
                            } else {
                                it.value = item[k];
                            }
                        }
                    }
                });
            }
            if (type == 'bh') {
                this.bhList.forEach(it => {
                    for (let k in item) {
                        if (it.id == k) {
                            if (it.id == 'ApplyApproveResult') {
                                it.value = 'NG';
                            } else {
                                it.value = item[k];
                            }
                        }
                    }
                });
            }
            if (type == 'cd') {
                this.cdList.forEach(it => {
                    for (let k in item) {
                        if (it.id == k) {
                            if (it.id == 'PurchaseApproveResult') {
                                it.value = Number(item[k]);
                            } else {
                                it.value = item[k];
                            }
                        }
                    }
                });
            }
            if (type == 'ys') {
                this.ysList.forEach(it => {
                    for (let k in item) {
                        if (it.id == k) {
                            if (it.id == 'AcceptanceResult') {
                                it.value = Number(item[k]);
                            } else if (it.id == 'OtherAccept') {
                                it.value = Number(item[k]);
                            } else {
                                it.value = item[k];
                            }
                        }
                    }
                });
            }
            if (type == 'yjqr') {
                this.yjqrList.forEach(it => {
                    for (let k in item) {
                        if (it.id == k) {
                            if (it.id == 'AcceptanceResult') {
                                it.value = Number(item[k]);
                            } else if (it.id == 'OtherAccept') {
                                it.value = Number(item[k]);
                            } else {
                                it.value = item[k];
                            }
                        }
                    }
                });
            }
            if (type == 'ldys') {
                this.ldyslist.forEach(it => {
                    for (let k in item) {
                        if (it.id == k) {
                            if (it.id == 'AcceptanceResult') {
                                it.value = Number(item[k]);
                            } else if (it.id == 'OtherAccept') {
                                it.value = Number(item[k]);
                            } else {
                                it.value = item[k];
                            }
                        }
                    }
                });
            }
            this.showDialog = true;
        },
        clear() {
            // this.cgList.forEach(item => {
            //     item.value = '';
            // });
            this.cdList.forEach(item => {
                item.value = '';
            });
            this.ysList.forEach(item => {
                item.value = '';
            });
        },
        async addSave(key) {
            let res;
            let obj = {};
            let flag = false;
            obj.ID = this.tableItem.ID;
            switch (key) {
                case 'bh':
                    flag = this.bhList.some(item => {
                        if (item.require) {
                            return item.value == '' || item.value == null;
                        }
                    });
                    if (flag) {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    this.bhList.forEach(item => {
                        obj[item.id] = item.value;
                    });
                    res = await GetRepairServiceRequest(obj);
                    break;
                case 'cg':
                    flag = this.cgList.some(item => {
                        if (item.require) {
                            return item.value == '' || item.value == null;
                        }
                    });
                    if (flag) {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    this.cgList.forEach(item => {
                        obj[item.id] = item.value;
                    });
                    res = await GetRepairServiceRequest(obj);
                    break;
                case 'cd':
                    flag = this.cdList.some(item => {
                        if (item.require) {
                            return item.value == '' || item.value == null;
                        }
                    });
                    if (flag) {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    this.cdList.forEach(item => {
                        obj[item.id] = item.value;
                    });

                    res = await GetRepairServicePurchase(obj);
                    break;
                case 'ys':
                    flag = this.ysList.some(item => {
                        if (item.require) {
                            return item.value == '' || item.value == null;
                        }
                    });
                    if (flag) {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    this.ysList.forEach(item => {
                        obj[item.id] = item.value;
                    });
                    res = await GetRepairServiceAccept(obj);
                    break;
                case 'yjqr':
                    if (this.yjqrList[5].value == null) {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    obj = {
                        ID: this.tableItem.ID,
                        UserConfirmDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
                        UserConfirmResult: this.yjqrList[6].value,
                        RequestContent: this.tableItem.RequestContent
                    };
                    res = await GetUserConfirm(obj);
                    break;
                case 'ldys':
                    if (this.ldyslist[6].value == null) {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    obj = {
                        ID: this.tableItem.ID,
                        FactoryMgrDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
                        FactoryMgrResult: this.ldyslist[7].value,
                        RequestContent: this.tableItem.RequestContent
                    };
                    res = await GetManagerConfirm(obj);
                    break;
            }
            let { success, msg } = res;
            if (key == 'bh') {
                msg = '驳回成功';
            }
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.$parent.MyGetRepairServicePageList();
                this.showDialog = this.classcheckbox ? false : true;
            }
        },
        closeEquip() {
            this.showDialog = false;
            // this.$refs.form.reset();
        },
        closeDatePicker(index) {
            this.$set(this.menu, index, false);
        }
    }
};
</script>
<style lang="scss">
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
    .textlabel {
        display: inline-flex;
        font-size: 16px;
        margin-right: 25px;
    }
    .el-radio-group {
        height: 40px;
        margin-top: 10px;
    }
    .el-radio__input.is-checked + .el-radio__label {
        color: #3dcd58;
    }
    .el-radio__input.is-checked .el-radio__inner {
        border-color: #3dcd58;
        background: #3dcd58;
    }
    .el-radio__label {
        font-size: 16px;
    }
    .el-date-editor {
        width: 100%;
        position: absolute;
        opacity: 0;
        top: 0;
        .el-input__inner {
            font-size: 16px;
            height: 32.5px;
            color: rgba(0, 0, 0, 0.87);
            border: 1px solid #9e9e9e;
        }
        .el-input__icon {
            line-height: 32.5px;
        }
    }
}
</style>

<style lang="scss" scoped>
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
    .textfieldbox {
        position: relative;
    }
}

.ant-calendar-picker {
    ::v-deep input {
        border-color: #9e9e9e;
    }

    ::v-deep i {
        color: #757575;
    }
}
</style>

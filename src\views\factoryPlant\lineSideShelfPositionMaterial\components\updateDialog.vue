<template>
    <v-dialog v-model="updateDialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ operaObj.ID ? $t('DFM_XBHJWL.editBind') : $t('DFM_XBHJWL.addBind') }}
                <v-icon @click="updateDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" class="mt-8">
                    <v-row>
                        <!-- 物料代码 -->
                        <v-col class="px-3 py-0" :cols="12" :lg="12">
                            <v-text-field v-model="form.MaterialCode" :rules="rules.MaterialCode" :label="$t('$vuetify.dataTable.DFM_XBHJWL.MaterialCode')" required dense outlined></v-text-field>
                        </v-col>
                        <!-- label="有效标志" -->
                        <v-col class="px-3 py-0" :cols="12" :lg="12">
                            <v-select v-model="form.Status" :rules="rules.Status" :label="$t('$vuetify.dataTable.DFM_XBHJWL.Status')" :items="statusItems" required dense outlined></v-select>
                        </v-col>
                        <!-- label="备注"  -->
                        <v-col class="px-3 py-0" :cols="12" :lg="12">
                            <v-textarea v-model="form.Remark" :label="$t('$vuetify.dataTable.DFM_XBHJWL.Remark')" :rows="2" dense outlined></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions class="py-0">
                <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn color="normal" @click="updateDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { saveRackingBinMaterialForm } from '@/api/factoryPlant/sideLine.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        currentSelecBinId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            checkbox: true,
            valid: true,
            updateDialog: false,
            form: {
                BinId: '',
                ID: '',
                MaterialCode: '',
                Remark: '',
                Status: ''
            },
            rules: {
                MaterialCode: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                Status: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            },
            statusItems: ['Y', 'N']
        };
    },
    watch: {
        operaObj: {
            handler(curVal) {
                for (const key in this.form) {
                    if (Object.hasOwnProperty.call(this.form, key)) {
                        this.form[key] = curVal[key];
                    }
                }
            },
            deep: true
        }
    },
    methods: {
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                const res = await saveRackingBinMaterialForm({ ...this.form, BinId: this.currentSelecBinId });
                const { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    if (this.operaObj.ID || this.checkbox) {
                        this.$emit('handlePopup', 'refresh');
                        this.updateDialog = false;
                    }else {
                        this.$refs.form.reset();
                    }
                }
                return false;
            }
        }
    }
};
</script>

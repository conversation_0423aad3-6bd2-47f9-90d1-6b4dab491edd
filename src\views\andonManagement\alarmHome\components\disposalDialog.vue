<template>
    <!-- 处理告警 -->
    <v-dialog v-model="dialog" persistent="persistent" max-width="1300px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title="primary-title">
                {{ $t('ANDON_BJZY.jjcz') }}--{{ form.MainAlarmName }}
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="mt-6 alarm-home-dds text-show-ellipsis">
                <v-row>
                    <!-- 报警列表 -->
                    <v-col :cols="8" class="activ-style list-height alarm-bdmr activ-background" :lg="8">
                        <div class="d-flex justify-space-between mb-3 alarm-home-search">
                            <div style="width: 14%">
                                <v-menu v-model="startShow" :close-on-content-click="false" transition="scale-transition" offset-y="offset-y" max-width="290px" min-width="290px">
                                    <template #activator="{ on, attrs }">
                                        <v-text-field
                                            v-model="searchForm.startTime"
                                            dense="dense"
                                            label="开始时间"
                                            readonly="readonly"
                                            v-bind="attrs"
                                            v-on="on"
                                            single-line="single-line"
                                            class="white-bk"
                                        ></v-text-field>
                                    </template>
                                    <v-date-picker v-model="searchForm.startTime" no-title="no-title" @input="startShow = false"></v-date-picker>
                                </v-menu>
                            </div>
                            <div style="width: 14%">
                                <v-menu v-model="endShow" :close-on-content-click="false" transition="scale-transition" offset-y="offset-y" max-width="290px" min-width="290px">
                                    <template #activator="{ on, attrs }">
                                        <v-text-field
                                            v-model="searchForm.endTime"
                                            dense="dense"
                                            label="结束时间"
                                            readonly="readonly"
                                            v-bind="attrs"
                                            v-on="on"
                                            single-line="single-line"
                                            class="white-bk"
                                        ></v-text-field>
                                    </template>
                                    <v-date-picker v-model="searchForm.endTime" no-title="no-title" @input="endShow = false"></v-date-picker>
                                </v-menu>
                            </div>
                            <!-- 产线 -->
                            <div style="width: 17%">
                                <!-- <v-text-field class="white-bk" label="产线" dense single-line/> -->
                                <v-combobox
                                    v-model="searchForm.areaid"
                                    :items="areaList"
                                    class="white-bk"
                                    :search-input.sync="searchA"
                                    item-text="EquipmentName"
                                    :label="$t('ANDON_BJZY.areaid')"
                                    item-value="ID"
                                    persistent-hint="persistent-hint"
                                    dense="dense"
                                    single-line="single-line"
                                    @change="i => selectItem(i.ID, 1)"
                                >
                                    <template #no-data>
                                        <v-list-item>
                                            <v-list-item-content>no data</v-list-item-content>
                                        </v-list-item>
                                    </template>
                                </v-combobox>
                            </div>
                            <div style="width: 17%">
                                <v-combobox
                                    v-model="searchForm.productLineId"
                                    :items="productLineItems"
                                    class="white-bk"
                                    :search-input.sync="searchP"
                                    item-text="EquipmentName"
                                    :label="$t('ANDON_BJZY.ProductLine')"
                                    item-value="ID"
                                    persistent-hint="persistent-hint"
                                    dense="dense"
                                    single-line="single-line"
                                    @change="i => selectItem(i.ID, 2)"
                                >
                                    <template #no-data>
                                        <v-list-item>
                                            <v-list-item-content>no data</v-list-item-content>
                                        </v-list-item>
                                    </template>
                                </v-combobox>
                            </div>
                            <div style="width: 17%">
                                <!-- <v-text-field class="white-bk" label="产线" dense single-line/> -->
                                <v-combobox
                                    v-model="searchForm.unitId"
                                    :items="segmentItems"
                                    class="white-bk"
                                    :search-input.sync="searchS"
                                    item-text="EquipmentName"
                                    label="工站"
                                    item-value="ID"
                                    persistent-hint="persistent-hint"
                                    dense="dense"
                                    single-line="single-line"
                                    @change="i => selectItem(i.ID, 3)"
                                >
                                    <template #no-data>
                                        <v-list-item>
                                            <v-list-item-content>no data</v-list-item-content>
                                        </v-list-item>
                                    </template>
                                </v-combobox>
                            </div>
                            <!-- 设备 -->
                            <div style="width: 17%">
                                <v-combobox
                                    v-model="searchForm.equipmentCode"
                                    :items="equipmentItems"
                                    class="white-bk"
                                    :search-input.sync="searchE"
                                    item-text="EquipmentName"
                                    :label="$t('ANDON_BJZY.EquipmentName')"
                                    item-value="ID"
                                    persistent-hint="persistent-hint"
                                    dense="dense"
                                    single-line="single-line"
                                >
                                    <template #no-data>
                                        <v-list-item>
                                            <v-list-item-content>no data</v-list-item-content>
                                        </v-list-item>
                                    </template>
                                </v-combobox>
                            </div>
                        </div>
                        <div class="d-flex justify-space-between mb-2">
                            <div style="width: 40%">
                                <v-text-field
                                    v-model="customizeSearch"
                                    append-icon="mdi-magnify"
                                    label="筛选"
                                    class="py-0 my-0 white-bk"
                                    single-line="single-line"
                                    hide-details="hide-details"
                                ></v-text-field>
                            </div>
                            <div style="width: 20%; text-align: right">
                                <v-btn color="primary" @click="initData()" style="width: 5%" class="mr-5">{{ $t('GLOBAL._CX') }}</v-btn>
                                <v-btn color="primary" @click="resetSearch()" style="width: 5%">{{ $t('GLOBAL._CZ') }}</v-btn>
                            </div>
                        </div>
                        <Tables
                            ref="tablePath"
                            :customizeSearch="customizeSearch"
                            :click-fun="selectedPath"
                            :current-select-id="currentSelectId"
                            itemKey="ID"
                            :showSelect="false"
                            table-height="520px"
                            :isSearch="false"
                            :loading="loading"
                            :headers="alarmListColumns"
                            :desserts="desserts"
                            :footer="false"
                            :page-options="pageData"
                        ></Tables>
                    </v-col>

                    <!-- 报警信息 -->
                    <v-col :cols="4" :lg="4" class="activ-style list-height">
                        <v-row>
                            <!-- 二级分类 -->
                            <v-col :cols="12" class="activ-txt" :lg="12">
                                <v-row>
                                    <!-- 二级分类 -->
                                    <v-col :cols="6" class="activ-txt alarm-bdr" :lg="6">
                                        {{ $t('ANDON_BJZY.SubAlarmName') }}
                                    </v-col>
                                    <v-col :cols="6" class="activ-txt" :lg="6">
                                        {{ $t('ANDON_BJZY.problemLevelName') }}
                                    </v-col>
                                    <!-- 二级分类 -->
                                    <v-col :cols="6" :lg="6" class="activ-height activ-background tb-border">
                                        <v-text-field class="white-bk" v-model="form.SubAlarmName" disabled="disabled" dense="dense" />
                                    </v-col>
                                    <v-col :cols="6" :lg="6" class="activ-height activ-style activ-background tb-border alarm-bdmr bt-l">
                                        <v-text-field class="white-bk" v-model="form.problemLevelName" dense="dense" disabled="disabled" />
                                    </v-col>
                                </v-row>
                            </v-col>
                            <!-- 二级分类 -->
                            <v-col :cols="12" class="activ-txt" :lg="12">
                                <v-row>
                                    <!-- 工段 -->
                                    <v-col :cols="6" class="activ-txt" :lg="6">
                                        {{ $t('ANDON_BJZY.ProductLineName') }}
                                    </v-col>
                                    <!-- 设备 -->
                                    <v-col :cols="6" class="activ-txt" :lg="6">
                                        {{ $t('ANDON_BJZY.EquipmentName') }}
                                    </v-col>
                                    <!-- 工段 -->
                                    <v-col :cols="6" class="activ-height activ-background tb-border" :lg="6">
                                        <v-text-field class="white-bk" v-model="form.ProductLineName" disabled="disabled" dense="dense" />
                                    </v-col>
                                    <!-- 设备 -->
                                    <v-col :cols="6" :lg="6" class="activ-height activ-background bt-l">
                                        <v-text-field class="white-bk" v-model="form.EquipmentName" dense="dense" disabled="disabled" />
                                    </v-col>
                                </v-row>
                            </v-col>
                            <!-- 告警内容 -->
                            <v-col :cols="12" class="activ-txt activ-background bt-l" :lg="12">
                                <v-textarea class="white-bk" v-model="form.AlarmContent" :label="$t('ANDON_BJZY.AlarmContent')" rows="2" dense="dense" disabled="disabled" />
                            </v-col>
                            <!-- 关警内容-->
                            <v-col :cols="12" class="activ-txt activ-background bt-l" :lg="12">
                                <v-textarea class="white-bk" v-model="form.Comment" :label="$t('ANDON_BJZY.Comment')" rows="2" dense="dense" outlined="outlined" />
                            </v-col>
                            <v-col :cols="6" class="activ-txt activ-background bt-l" :lg="6" style="border: none">
                                <v-btn color="primary" :disabled="AlarmTypeCode == ''"  @click="showExperienceDialog">{{ $t('ANDON_BJZY.JYK') }}</v-btn>
                            </v-col>
                            <!--  -->
                            <v-col :cols="6" :lg="6" class="activ-background activ-style alarm-bdmr" style="border: none">
                                <v-btn color="primary" @click="showPreviewDialog">{{ $t('ANDON_BJZY.AlarmPic') }}</v-btn>
                            </v-col>
                            <v-col :cols="12" :lg="12" class="activ-style opear-message opear-btns activ-background">
                                <!-- <div class="white-bk" @click="closeForm"> <span class="iconfont
                icon-cuowu"></span> </div> -->
                                <template v-if="operaObj.eventStatus == 'RESPOND'">
                                    <div class="upgrade-btn" @click="submitForm('upgrade')">
                                        <!-- <span style="font-size: 20px">升级</span> -->
                                        <span class="iconfont icon-shengji"></span>
                                    </div>
                                    <div class="close-btn" @click="submitForm('close')">
                                        <!-- <span style="font-size: 20px">结束</span> -->
                                        <span class="iconfont icon-guanbi"></span>
                                    </div>
                                </template>
                                <div v-else class="white-bk" style="border: 1px solid #bdbdbd" @click="closeForm">
                                    <span class="iconfont icon-cuowu"></span>
                                </div>
                            </v-col>
                        </v-row>
                    </v-col>
                </v-row>
            </v-card-text>
        </v-card>
        <v-dialog v-model="dialogExperienceVisible" max-width="40%">
            <v-card>
                <v-toolbar color="primary" dark="dark">
                    <v-toolbar-title>{{ $t('ANDON_BJZY.JYK') }}</v-toolbar-title>
                    <v-spacer></v-spacer>
                    <v-btn icon="icon" @click="dialogExperienceVisible = false">
                        <v-icon>mdi-close</v-icon>
                    </v-btn>
                </v-toolbar>
                <div class="EquipmentSearch">
                    <div class="addForm">
                        <v-text-field v-model="SolutionDesc" outlined dense :label="$t('$vuetify.dataTable.ExperienceLibrary.czfa')"></v-text-field>
                    </div>
                    <div class="addForm">
                        <v-btn color="primary" @click="getExperienceTableData()">{{ $t('GLOBAL._CX') }}</v-btn>
                    </div>
                    <div class="addForm">
                        <v-btn color="primary" @click="AddExperienceNew()">{{ $t('GLOBAL._XZ') }}</v-btn>
                    </div>
                </div>
                <v-card-text>
                    <Tables
                        :showSelect="false"
                        ref="BjTables"
                        :btn-list="btnList"
                        @tableClick="tableClick"
                        tableHeight="300px"
                        table-name="ExperienceLibrary"
                        :headers="ExperienceColumns"
                        :desserts="Experiencedesserts"
                        :footer="false"
                    ></Tables>
                </v-card-text>
            </v-card>
        </v-dialog>
        <v-dialog v-model="dialogupgradeVisible" max-width="40%">
            <v-card>
                <v-toolbar color="primary" dark="dark">
                    <v-toolbar-title>{{ $t('GLOBAL.upgrade') }}</v-toolbar-title>
                    <v-spacer></v-spacer>
                    <v-btn icon="icon" @click="dialogupgradeVisible = false">
                        <v-icon>mdi-close</v-icon>
                    </v-btn>
                </v-toolbar>
                <v-card-text style="margin-top: 20px">
                    <div style="height: 200px">
                        <v-combobox
                            v-model="UpgradeType"
                            class="white-bk"
                            :label="$t('$vuetify.dataTable.ANDON_BJSJGZ.UpgradeType')"
                            :items="UpgradeTypeList"
                            item-text="ItemName"
                            item-value="ItemValue"
                            dense="dense"
                            outlined="outlined"
                            clearable="clearable"
                        ></v-combobox>
                        <v-combobox
                            v-model="UpgraderList"
                            class="white-bk"
                            :label="UpgradeType.ItemValue == 'Upgrade' ? $t('$vuetify.dataTable.ANDON_BJSJGZ.Noter') : $t('$vuetify.dataTable.ANDON_BJSJGZ.Noter') + ' *'"
                            :search-input.sync="search2"
                            :items="UserList"
                            item-text="UserFullName"
                            item-value="LoginName"
                            @change="changeUser"
                            dense="dense"
                            outlined="outlined"
                            clearable="clearable"
                            multiple="multiple"
                        ></v-combobox>
                        <v-textarea class="white-bk" v-model="MyAlarmContent" :label="$t('DFM_GYLX.Remark')" rows="2" dense="dense" outlined="outlined" />
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <el-button @click="dialogupgradeVisible = false">取 消</el-button>
                    <el-button type="primary" @click="Upgrade()">确 定</el-button>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <v-dialog v-model="dialogVisible" max-width="60%">
            <v-card>
                <v-toolbar color="primary" dark="dark">
                    <v-toolbar-title>文件预览</v-toolbar-title>
                    <v-spacer></v-spacer>
                    <v-btn icon="icon" @click="dialogVisible = false">
                        <v-icon>mdi-close</v-icon>
                    </v-btn>
                </v-toolbar>
                <v-card-text>
                    <v-row :gutter="10">
                        <v-col v-for="(file, index) in previewFiles" :key="index" cols="12" sm="6" md="4">
                            <v-card @click="showFullImagePreview(file)">
                                <v-img :src="file" aspect-ratio="1.75"></v-img>
                            </v-card>
                        </v-col>
                    </v-row>
                </v-card-text>
            </v-card>
        </v-dialog>
        <!-- 全屏图片预览对话框 -->
        <v-dialog v-model="fullImageDialogVisible" fullscreen="fullscreen" hide-overlay="hide-overlay" transition="dialog-bottom-transition">
            <v-card>
                <v-toolbar color="primary" dark="dark">
                    <v-toolbar-title>全屏预览</v-toolbar-title>
                    <v-spacer></v-spacer>
                    <v-btn icon="icon" @click="fullImageDialogVisible = false">
                        <v-icon>mdi-close</v-icon>
                    </v-btn>
                </v-toolbar>
                <v-card-text>
                    <v-img :src="fullImageUrl" contain="contain" height="100%"></v-img>
                </v-card-text>
            </v-card>
        </v-dialog>
        <v-dialog v-model="addExperienceModel" persistent width="30%">
            <v-card>
                <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                    {{ $t('GLOBAL._XZ') }}
                    <v-icon @click="addExperienceModel = false">mdi-close</v-icon>
                </v-card-title>
                <div class="addForm" style="margin: 30px 30px 0 30px">
                    <v-text-field v-model="addForm.SolutionDesc" outlined dense :label="$t('$vuetify.dataTable.ExperienceLibrary.czfa')"></v-text-field>
                </div>
                <div class="addForm" style="margin: 0 30px">
                    <v-text-field type="number" disabled v-model="addForm.AdoptionCount" outlined dense :label="$t('$vuetify.dataTable.ExperienceLibrary.cycs')"></v-text-field>
                </div>
                <v-divider></v-divider>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <el-button @click="addExperienceModel = false">取 消</el-button>
                    <el-button type="primary" @click="SaveExperience()">确 定</el-button>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </v-dialog>
</template>

<script>
import { AlarmRecordGetList, AlarmRecordUpgrade, AlarmRecordClose, AlarmRecordDefectSelect, FindUpgradeUserInfo, GetAlarmExperienceList } from '@/api/andonManagement/alarmHome.js';
import { GetListByAlarmId, AlarmSolutionSaveForm, AlarmSolutionInsert } from '@/api/andonManagement/alarmType.js';
import { getReasonInfoList } from '@/api/factoryPlant/reasonInfo.js';
import { alarmListColumns, ExperienceColumns } from '@/columns/andonManagement/alarmHome.js';
import { GetFilteredUsersList } from '../../../systemManagement/userManagement/service.js';
import Util from '@/util';
import dayjs from 'dayjs';

export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        productLineList: {
            type: Array,
            default: () => []
        },
        problemLevelList: {
            type: Array,
            default: () => []
        },
        areaList: {
            type: Array,
            default: () => []
        },
        equipmentList: {
            type: Array,
            default: () => []
        },
        segmentList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            SolutionDesc: '',
            MyAlarmContent: '',
            UpgradeType: {
                ItemValue: '',
                ItemName: ''
            },
            UpgradeTypeList: [],
            UpgraderList: [],
            EmpLark: '',
            Currentman: '',
            UserList: [],
            dialogupgradeVisible: false,
            ExperienceColumns: ExperienceColumns,
            Experiencedesserts: [],
            searchForm: {
                areaid: '',
                productLineId: '',
                unitId: '',
                equipmentCode: '',
                startTime: '',
                endTime: ''
            },
            search2: null,
            searchS: null,
            searchE: null,
            productLineItems: [],
            segmentItems: [],
            equipmentItems: [],
            customizeSearch: '',
            startShow: false,
            endShow: false,
            searchP: null,
            searchA: null,
            dialog: '',
            loading: false,
            alarmListColumns,
            currentSelectId: '',
            showImagePreview: false,
            srcList: [],
            desserts: [],
            pageData: {
                pageSize: 99999
            },
            pageIndex: 1,
            dataCount: 1,
            pageSize: 40,
            typeChildList: [],
            unitList: [],
            valid: true,
            form: {
                ProductLineName: '',
                SubAlarmName: '',
                MainAlarmName: '',
                EquipmentName: '',
                problemLevelName: '',
                AlarmContent: '',
                Comment: '',
                Unit: '',
                ManQuantity: '',
                DEVICE_Quantity: '',
                AlarmPic: ''
            },
            rules: {
                ProductLine: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            },
            fns: {
                close: AlarmRecordClose,
                upgrade: AlarmRecordUpgrade
            },
            defectSelect: {},
            dialogVisible: false,
            dialogExperienceVisible: false,
            fullImageDialogVisible: false,
            previewFiles: [],
            fullImageUrl: '',
            AlarmTypeCode: '',
            addForm: {
                SolutionDesc: '',
                AdoptionCount: 0
            },
            addExperienceModel: false
        };
    },
    watch: {
        dialog: {
            async handler(curVal) {
                this.desserts = [];
                if (curVal) {
                    const { EmpLark, Currentman } = this.operaObj;
                    //监听处理
                    this.$nextTick(() => {
                        if (document.querySelector('.v-data-table__wrapper')) {
                            const selectWrap = document.querySelector('.v-data-table__wrapper');
                            selectWrap.addEventListener('scroll', this.scrollLoadMore);
                        }
                    });
                    await this.getTypeChildList();
                    this.resetSearch();
                    this.initDate();
                    this.initData();
                }
            },
            deep: true,
            immediate: true
        }
    },
    computed: {
        btnList() {
            return [{ text: this.$t('DFM_MXGL._XZ'), icon: '', code: 'check', type: 'primary' }];
        }
    },
    async created() {
        this.unitList = await Util.getUnitList();
        this.getUserList();
        this.UpgradeTypeList = await this.$getNewDataDictionary('UpgradeType');
    },
    mounted() {
        this.$emit('initOpenUnreadDialog');
    },
    methods: {
        async SaveExperience() {
            let params = {
                ...this.addForm
            };
            params.AlarmCode = this.AlarmTypeCode;
            params.ID = null;
            let res = await AlarmSolutionInsert(params);
            this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
            this.getExperienceTableData();
            this.addExperienceModel = false;
        },
        AddExperienceNew() {
            this.addForm.SolutionDesc = '';
            this.addForm.AdoptionCount = 0;
            this.addExperienceModel = true;
        },
        async Upgrade() {
            if (this.UpgradeType.ItemValue != 'Upgrade') {
                if (this.EmpLark == '') {
                    return this.$store.commit('SHOW_SNACKBAR', {
                        text: this.$t('ANDON_BJZY.CheckPostUser'),
                        color: 'error'
                    });
                }
            }
            if (!this.currentSelectId) {
                return this.$store.commit('SHOW_SNACKBAR', {
                    text: this.$t('ANDON_BJZY.tishi'),
                    color: 'error'
                });
            }
            const fn = AlarmRecordUpgrade;
            const res = await fn({
                id: this.currentSelectId,
                EmpLark: this.EmpLark,
                AlarmContent: this.MyAlarmContent,
                Currentman: this.Currentman,
                UpgradeType: this.UpgradeType.ItemValue
            });
            const { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', {
                    text: msg,
                    color: 'success'
                });
                this.$emit('handlePopup', 'refresh');
                this.dialogupgradeVisible = false;
            } else {
                this.$store.commit('SHOW_SNACKBAR', {
                    text: msg,
                    color: 'error'
                });
            }
        },
        tableClick(item, type) {
            switch (type) {
                case 'check':
                    this.ExperienceCheck(item);
                    return;
            }
        },
        async ExperienceCheck(item) {
            item.AdoptionCount = Number(item.AdoptionCount) + 1;
            let res = await AlarmSolutionSaveForm(item);
            this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
            this.form.Comment = item.SolutionDesc;
            this.dialogExperienceVisible = false;
        },
        async initData() {
            this.currentSelectId = '';
            this.form.ProductLineName = '';
            this.form.SubAlarmName = '';
            this.form.AlarmContent = '';
            this.form.EquipmentName = '';
            this.form.problemLevelName = '';
            this.form.AlarmPic = '';
            this.form.MainAlarmName = this.operaObj.AlarmName;
            this.pageIndex = 1;
            this.desserts = [];
            this.getdata();
        },
        showPreviewDialog() {
            this.dialogVisible = true;
        },
        async showExperienceDialog() {
            let params = {
                AlarmTypeCode: this.AlarmTypeCode
            };
            let res = await GetAlarmExperienceList(params);
            this.SolutionDesc = '';
            this.Experiencedesserts = res.response;
            this.dialogExperienceVisible = true;
        },
        async getExperienceTableData() {
            let params = {};
            params.SolutionDesc = this.SolutionDesc;
            params.AlarmTypeCode = this.AlarmTypeCode;
            let res = await GetAlarmExperienceList(params);
            let { success, response } = res;
            if (success) {
                this.Experiencedesserts = response || {} || [];
            }
        },
        showFullImagePreview(file) {
            this.fullImageUrl = file;
            this.fullImageDialogVisible = true;
        },
        // 日期格式化sss
        initDate() {
            const nowDate = dayjs(new Date()).format('YYYY-MM-DD');
            this.searchForm.endTime = nowDate;
            this.searchForm.startTime = dayjs(new Date()).subtract(30, 'day').format('YYYY-MM-DD');
        },
        showImage(alarmPic) {
            this.showImagePreview = true;
            this.srcList = alarmPic.split(',');
        },
        selectItem(id, key) {
            switch (key) {
                case 1:
                    this.searchForm.productLineId = null;
                    this.productLineItems = this.productLineList.filter(i => i.ParentId == id);
                    this.segmentItems = [];
                    this.searchForm.unitId = null;
                    this.equipmentItems = [];
                    this.searchForm.equipmentCode = null;
                    break;

                case 2:
                    this.segmentItems = this.segmentList.filter(i => i.ParentId == id);
                    this.searchForm.unitId = null;
                    this.equipmentItems = [];
                    this.searchForm.equipmentCode = null;
                    break;

                case 3:
                    this.equipmentItems = this.equipmentList.filter(i => i.ParentId == id);
                    this.searchForm.equipmentCode = null;
                    break;

                default:
                    break;
            }
        },
        // 重置搜索栏
        resetSearch() {
            for (const key in this.searchForm) {
                if (Object.hasOwnProperty.call(this.searchForm, key)) {
                    this.searchForm[key] = '';
                }
            }
            this.initDate();
        },
        /**
         * 监听滚动条
         * */
        scrollLoadMore() {
            let scrollWrap = document.querySelector('.v-data-table__wrapper');
            var currentScrollTop = scrollWrap.scrollTop;
            var currentOffsetHeight = scrollWrap.scrollHeight;
            var currentClientHeight = scrollWrap.clientHeight;
            const h = currentOffsetHeight - currentScrollTop - currentClientHeight;
            if (h < 1 && this.desserts.length < this.dataCount && this.desserts.length > 0) {
                //到底部了 重新请求数据
                this.pageIndex++; //页码++
                //TODO 执行加载数据方法
                this.getdata();
            }
        },
        // 获取子级
        async getTypeChildList() {
            this.typeChildList = [];
            const res = await GetListByAlarmId({ alarmId: this.operaObj.ID });
            const { success, response } = res || {};
            if (success) {
                this.typeChildList = response;
            }
        },
        async getdata() {
            if (!this.dialog) return;
            const mainAlarmType = this.form.AlarmCode || this.operaObj.AlarmCode;
            const { areaid, productLineId, endTime, startTime } = this.searchForm;
            const o = {
                areacode: areaid?.EquipmentCode,
                productLine: productLineId?.EquipmentCode,
                endTime: endTime + ' 23:59:59',
                startTime: startTime + ' 00:00:00'
            };
            const res = await AlarmRecordGetList({
                pageIndex: this.pageIndex,
                pageSize: this.pageSize,
                eventStatus: this.operaObj.eventStatus,
                mainAlarmType,
                ...o
            });
            const { success, response } = res;
            if (success) {
                const { data, dataCount } = response;
                this.dataCount = dataCount || 0;
                const arr = JSON.parse(JSON.stringify(this.desserts));
                this.desserts = [];
                data.forEach(e => {
                    const { SubAlarmType, ProductLine } = e;
                    e.SubAlarmTypeCode = SubAlarmType;
                    e.SubAlarmType = this.$getDictionaryVal(SubAlarmType, this.typeChildList, 'AlarmCode', 'AlarmName');
                    e.ProductLineName = this.$getDictionaryVal(ProductLine, this.productLineList, 'EquipmentCode', 'EquipmentName');
                    arr.push(e);
                });
                this.desserts = arr;
            }
        },
        //点击表格行
        async selectedPath(o) {
            const { SubAlarmType, SubAlarmTypeCode, ProductLineName, AlarmContent, EquipmentCode, ProblemLevel, EventNo, AlarmPic } = o;
            this.AlarmTypeCode = SubAlarmTypeCode;
            this.form.AlarmContent = AlarmContent;
            this.form.SubAlarmName = SubAlarmType;
            this.form.ProductLineName = ProductLineName;
            this.form.AlarmPic = AlarmPic;
            if (this.form.AlarmPic) {
                this.previewFiles = this.form.AlarmPic.split(',');

            }
            const problemLevelName = this.$getDictionaryVal(ProblemLevel, this.problemLevelList, 'ItemValue', 'ItemName');
            this.form.problemLevelName = problemLevelName;
            this.returnedItem = [];
            this.form.EquipmentName = this.$getDictionaryVal(EquipmentCode, this.equipmentList, 'EquipmentCode', 'EquipmentName');
            this.currentSelectId = o.ID;
            if (EventNo) {
                this.defectSelect = await this.AlarmRecordDefectSelect(o.ID);
                if (this.defectSelect) {
                    const { ManQuantity, DEVICE_Quantity, Unit } = this.defectSelect;
                    this.form.ManQuantity = ManQuantity;
                    this.form.DEVICE_Quantity = DEVICE_Quantity;
                    this.form.Unit = Unit;
                }
            } else {
                this.form.ManQuantity = '';
                this.form.DEVICE_Quantity = '';
                this.form.Unit = '';
            }
        },
        async FindUpgradeUserInfo(id) {
            let result = null;
            try {
                const res = await FindUpgradeUserInfo({ id });
                const { success, response } = res;
                if (success) {
                    result = response;
                }
            } catch (error) {
                console.log(error);
            }
            return result;
        },
        // 当前是否编辑
        async AlarmRecordDefectSelect(id) {
            let result = null;
            try {
                const res = await AlarmRecordDefectSelect({ id });
                const { success, response } = res;
                if (success) {
                    result = response;
                }
            } catch (error) {
                console.log(error);
            }
            return result;
        },
        closeForm() {
            this.dialog = false;
        },
        changeUser(v) {
            this.EmpLark = v.map(item => item.LoginName).join(',');
            this.Currentman = v.map(item => item.UserName).join(',');
        },
        async getUserList() {
            const res = await GetFilteredUsersList({ Postid: '操作工' });
            const { success, response } = res || {};
            if (response && success) {
                this.UserList = response;
                this.UserList = this.UserList.map(user => {
                    // 创建一个新的对象，与原来的对象几乎相同，但userName被修改了
                    return {
                        ...user, // 展开运算符用于复制原对象的所有属性
                        UserFullName: user.PostName + ' || ' + user.UserNo + ' || ' + user.UserName
                    };
                });
            } else {
                this.UserList = [];
            }
            console.log(this.UserList);
        },
        // 提交
        async submitForm(type) {
            if (type == 'upgrade') {
                this.dialogupgradeVisible = true;
            } else {
                if (!this.currentSelectId) {
                    return this.$store.commit('SHOW_SNACKBAR', {
                        text: this.$t('ANDON_BJZY.tishi'),
                        color: 'error'
                    });
                }
                const fn = type == 'close' ? AlarmRecordClose : AlarmRecordUpgrade;
                const res = await fn({
                    id: this.currentSelectId,
                    Comment: this.form.Comment
                });
                const { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', {
                        text: msg,
                        color: 'success'
                    });
                    this.$emit('handlePopup', 'refresh');
                    this.closeForm();
                } else {
                    this.$store.commit('SHOW_SNACKBAR', {
                        text: msg,
                        color: 'error'
                    });
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped="scoped">
.EquipmentSearch {
    display: flex;
    margin: 10px 0 0 10px;
    .addForm {
        margin-right: 5px;
    }
}
.activ-txt {
    line-height: 6px;
}

.bt-l {
    border-top: 1px solid #bdbdbd;
}

.bt-a {
    border-right: 1px solid #bdbdbd;
}

.activ-style {
    border: 1px solid #bdbdbd;
    border-bottom: none;
}

.tb-border {
    border-top: 1px solid #bdbdbd;
    border-bottom: 1px solid #bdbdbd;
}

.activ-height {
    height: 56px;
}

.alarm-message {
    height: 80px;
}

.opear-message {
    height: 146px;
    border-left: 0;
    border-right: 0;
    border-bottom: 1px solid #bdbdbd;
    // margin-bottom: 10px;
}

.opear-btns {
    display: flex;
    justify-content: space-around;
    align-items: center;

    div {
        cursor: pointer;
        // border: 1px solid gainsboro;
        height: 100%;
        width: 45%;
        display: flex;
        justify-content: space-around;
        align-items: center;
    }

    .upgrade-btn {
        background: #f2c85d;

        .iconfont {
            font-size: 60px !important;
        }
    }

    .close-btn {
        background: #ed1616;

        .iconfont {
            font-size: 60px !important;
        }
    }
}

.white-bk {
    background: #fff !important;
}

.alarm-bdmr {
    border-right: none;
}

.alarm-bdr {
    border-right: 1px solid #bdbdbd;
}

.col-12 .col-lg-6,
.col-6,
.col-lg-12,
.col-lg-6.col-12,
.col-lg-6.col-6 {
    padding: 6x;
}

.list-height {
    // height: 416px;
    height: 621px;
}
</style>
<style lang="scss">
.alarm-home-dds {
    .v-text-field.v-text-field--enclosed .v-text-field__details {
        margin-bottom: 0;
    }

    .v-text-field__details {
        display: none;
    }

    .activ-background {
        background: #f5f5f5;
    }

    .iconfont {
        font-size: 80px;
    }

    // thead{
    //     display: none;
    // }
    .v-input--dense > .v-input__control > .v-input__slot {
        margin-bottom: 1px;
    }
}
</style>
//, dictionary: true
export const reportBaseModelColumns = [
    { text: '序号', value: 'Index', width: '60px' },
    { text: '产线名称', value: 'Projectname', width: '160px' },
    { text: '产线编码', value: 'Productline', width: '100px' },
    { text: '工段编码', value: 'Segmentcode', width: '160px' },
    { text: '工段名称', value: 'Segmentname', width: '160px' },
    { text: '工序名称', value: 'Processname', width: '160px' },
    { text: '设备编码', value: 'Devicecode', width: '160px' },
    { text: '投入物料编码', value: 'Inmaterialcode', width: '160px' },
    { text: '投入物料名称', value: 'Inmaterialname', width: '160px' },
    { text: '产出物料编码', value: 'Outmaterialcode', width: '100px' },
    { text: '产出物料名称', value: 'Outmaterialname', width: '160px' },
    { text: '投入采集点', value: 'Intag', width: '160px' },
    { text: '故障采集点', value: 'Faulttag', width: '160px' },
    { text: '合格产出采集点', value: 'Oktag', width: '160px' },
    { text: 'NG品采集点', value: 'Ngtag', width: '160px' },
    { text: '是否上料工站', value: 'Isfeed', width: '100px', dictionary: true },
    { text: 'CT', value: 'Ct', width: '160px' },
    { text: '是否瓶颈工站', value: 'Ischokepoint', width: '160px', dictionary: true },
    { text: '是否工段收盘站', value: 'Issegmenttag', width: '160px', dictionary: true },
    { text: '是否整线收盘站', value: 'Isproductlinetag', width: '160px', dictionary: true },
    { text: '工站排序', value: 'OrderNum', width: '160px' },
    { text: '工段排序', value: 'SegmentSeq', width: '100px' },
    { text: 'CT采集点', value: 'CtTag', width: '100px' },
    { text: '设备状态采集点', value: 'Stopstatustag', width: '160px' },
    { text: 'SFC测试机台ID', value: 'Sfcmachineid', width: '160px' },
    { text: 'SFC数据库名称', value: 'Sfcdbname', width: '160px' },
    { text: '是否为测试工段', value: 'Istestsegment', width: '160px', dictionary: true },
    { text: '工段良率计算物料上料点', value: 'Issegmentmaterial', width: '160px', dictionary: true },
    { text: '原始CT', value: 'Orgct', width: '160px' },
    { text: '工段是否隐藏', value: 'Isenabled', width: '160px', dictionary: true },
    { text: '是否启用', value: 'Enable', width: '160px' },
    { text: '产品型号', value: 'ProductCode', width: '160px' },
    { text: '产品型号名称', value: 'ProductName', width: '100px' },
    { text: '产品类型', value: 'ProductType', width: '160px' },
    {
        text: '操作',
        value: 'actions',
        align: 'center',
        width: '140px',
    }
];

import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url

//  模板下载
export function DownLoadTemplate(data) {
    return request({
        url: baseURL + '/api/StaffDutyLine/DownLoadTemplate',
        method: 'post',
        data
    });
}
// 数据导入
export function ImportData(data) {
    return request({
        url: baseURL + '/api/StaffDutyLine/ImportData',
        method: 'post',
        data
    });
}
// 列表
export function StaffDutyLineGetPageList(data) {
    return request({
        url: baseURL + '/api/StaffDutyLine/GetPageList',
        method: 'post',
        data
    });
}

// 列表
export function StaffDutyLineGetList(data) {
    return request({
        url: baseURL + '/api/StaffDutyLine/GetList',
        method: 'post',
        data
    });
}
// 新增&编辑
export function StaffDutyLineSaveForm(data) {
    return request({
        url: baseURL + '/api/StaffDutyLine/SaveForm',
        method: 'post',
        data
    });
}

// 删除
export function StaffDutyLineDelete(data) {
    return request({
        url: baseURL + '/api/StaffDutyLine/Delete',
        method: 'post',
        data
    });
}

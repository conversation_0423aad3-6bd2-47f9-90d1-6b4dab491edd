<!-- eslint-disable vue/valid-v-slot -->
<template>
    <div class="kanban-table" style="height: 100%">
        <a-table rowKey="ID" :columns="columns" :locale="{ emptyText: ' ' }" :style="`height: ${tableHeight};overflow-y: auto`" :pagination="false" size="small" :data-source="data">
            <template slot="operation" slot-scope="text, record">
                <a @click="() => tableClick(record.ID)">设备调机</a>
            </template>
        </a-table>
        <a-pagination v-if="isShowPagination" class="mt-3" @change="changePage" :defaultPageSize="pageOptions.pageSize" v-model="current" :total="pageOptions.total" />
    </div>
</template>

<script>
export default {
    props: {
        isShowPagination: {
            type: Boolean,
            default: true
        },
        tableHeight: {
            type: String,
            default: ''
        },
        pageOptions: {
            type: Object,
            default: () => {}
        },
        total: {
            type: Number,
            default: 500
        },
        columns: {
            type: Array,
            default: () => []
        },
        data: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            current: 1
        };
    },
    watch: {
        pageOptions: {
            handler(nv, ov) {
                this.current = nv.page;
            },
            deep: true
        }
    },
    // computed: {
    //     current() {
    //         return this.pageOptions.page;
    //     }
    // },
    methods: {
        tableClick(id) {
            this.$emit('tableClick', id, 'machine');
        },
        changePage(page) {
            this.$emit('handleChangePage', page);
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .ant-table-small.ant-table {
    border: none;
}
::v-deep .ant-table-thead {
    th {
        color: #fff;
        background: none;
    }
}
::v-deep .ant-table-tbody {
    tr:hover td {
        background: none !important;
    }
    td {
        color: #00fefe;
    }
}

::v-deep .ant-table-placeholder {
    background: none;
    .ant-empty-description {
        color: #fff;
    }
}
::v-deep .ant-pagination {
    text-align: right;
    li {
        a {
            color: #000;
        }
    }
    .ant-pagination-item-active {
        a {
            color: #5dd00f;
        }
    }
    .ant-pagination-jump-prev.ant-pagination-jump-prev-custom-icon,
    .ant-pagination-jump-next.ant-pagination-jump-next-custom-icon {
        .ant-pagination-item-ellipsis {
            color: #00ffff;
        }
    }
    .ant-pagination-prev,
    .ant-pagination-item,
    .ant-pagination-jump-prev.ant-pagination-jump-prev-custom-icon,
    .ant-pagination-next {
        transform: scale(0.8);
    }
}
::-webkit-scrollbar {
    // background: none;
    display: none !important;
}
</style>
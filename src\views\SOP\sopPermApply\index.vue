<!--
 * @Descripttion: (权限申请审核表/DFM_B_SOP_PERM_APPLY)
 * @version: (1.0)
 * @Author: (admin)
 * @Date: (2025-05-09)
 * @LastEditors: (admin)
 * @LastEditTime: (2025-05-09)
-->

<template>
  <div class="root">
    <div class="root-head">
      <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
            				    
      <el-form-item label="申请人ID" prop="applicantId">
        <el-select v-model="searchForm.applicantId" placeholder="请选择申请人ID">
          <el-option v-for="item in  applicantIdOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
        </el-select>
      </el-form-item>
    				    
      <el-form-item label="申请对象ID" prop="targetId">
        <el-select v-model="searchForm.targetId" placeholder="请选择申请对象ID">
          <el-option v-for="item in  targetIdOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
        </el-select>
      </el-form-item>
    				    
      <el-form-item label="对象类型(1-目录 2-文档)" prop="targetType">
        <el-select v-model="searchForm.targetType" placeholder="请选择对象类型(1-目录 2-文档)">
          <el-option v-for="item in  targetTypeOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
        </el-select>
      </el-form-item>
    				    
      <el-form-item label="申请权限级别" prop="requestPerm">
        <el-select v-model="searchForm.requestPerm" placeholder="请选择申请权限级别">
          <el-option v-for="item in  requestPermOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
        </el-select>
      </el-form-item>
    				    
      <el-form-item label="审批状态(0-待处理 1-通过 2-拒绝)" prop="approveStatus">
        <el-select v-model="searchForm.approveStatus" placeholder="请选择审批状态(0-待处理 1-通过 2-拒绝)">
          <el-option v-for="item in  approveStatusOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
        </el-select>
      </el-form-item>
    				    
      <el-form-item label="审批人ID" prop="approverId">
        <el-select v-model="searchForm.approverId" placeholder="请选择审批人ID">
          <el-option v-for="item in  approverIdOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
        </el-select>
      </el-form-item>
    				    
      <el-form-item label="审批时间">
        <el-date-picker v-model="dateRangeApproveTime" style="width: 240px" value-format="yyyy-MM-dd" type="daterange" range-separator="-"
          start-placeholder="开始日期" end-placeholder="结束日期" placeholder="请选择审批时间" :picker-options="{ firstDayOfWeek: 1}"></el-date-picker>
      </el-form-item>
    				    
      <el-form-item label="创建时间">
        <el-date-picker v-model="dateRangeCreatedate" style="width: 240px" value-format="yyyy-MM-dd" type="daterange" range-separator="-"
          start-placeholder="开始日期" end-placeholder="结束日期" placeholder="请选择创建时间" :picker-options="{ firstDayOfWeek: 1}"></el-date-picker>
      </el-form-item>

        <el-form-item class="mb-2">
          <el-button icon="el-icon-search" @click="getSearchBtn()">{{ $t('GLOBAL._CX') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="success" icon="el-icon-circle-plus-outline" @click="showDialog({})">
            {{ $t('GLOBAL._XZ') }}
          </el-button>
        </el-form-item>
              </el-form>
    </div>
    <div class="root-main">
      <el-table class="mt-3"
                :height="mainH"
                border
                :data="tableData"
                style="width: 100%">
        <el-table-column v-for="(item) in tableName"
                         :default-sort="{prop: 'date', order: 'descending'}"
                         :key="item.ID"
                         :prop="item.field"
                         :label="item.label"
                         :width="item.width"
                         :align="item.alignType"
                         sortable
                         show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row[item.field] }}
          </template>
        </el-table-column>
        <el-table-column prop="operation" width="160" :label="$t('GLOBAL._ACTIONS')" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="showDialog(scope.row)">{{ $t('GLOBAL._BJ') }}</el-button>
                        <el-button size="mini" type="text" @click="delRow(scope.row)">{{ $t('GLOBAL._SC') }}</el-button>
          
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="root-footer">
      <el-pagination
          class="mt-3"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchForm.pageIndex"
          :page-sizes="[10,20, 50, 100,500]"
          :page-size="searchForm.pageSize"
          layout="->,total, sizes, prev, pager, next, jumper"
          :total="total"
          background
      ></el-pagination>
    </div>
    <form-dialog @saveForm="getSearchBtn" ref="formDialog"></form-dialog>
  </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';

import FormDialog from './form-dialog'
import {
    delSopPermApply, getSopPermApplyList
} from "@/api/SOP/sopPermApply";
import {getTableHead} from "@/util/dataDictionary.js";

import { sopPermApplyColumn } from '@/api/SOP/sopPermApply.js';


export default {
  name: 'index.vue',
  components: {
    FormDialog,
  },
  data() {
    return {
      searchForm: {
        pageIndex: 1,
        pageSize: 20,
      },
      total: 0,
      tableData: [{}],
      hansObj: this.$t('权限申请审核表.table'),
      tableName: [],
      loading: false,
      tableOption: [],
      mainH: 0,
      buttonOption:{
        name:'权限申请审核表',
        serveIp:'baseURL_SOP',
        uploadUrl:'/api/SopPermApply/ImportData', //导入
        exportUrl:'/api/SopPermApply/ExportData', //导出
        DownLoadUrl:'/api/SopPermApply/DownLoadTemplate', //下载模板
      }
    }
  },
  mounted() {
    this.getZHHans()
    this.getTableData()
    this.$nextTick(() => {
      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)
    })
    window.onresize = () => {
      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)
    }
  },
  methods: {
    getZHHans() {
      for (let key in this.hansObj) {
        this.tableName = getTableHead(this.hansObj, this.tableOption)
      }
    },
    showDialog(row) {
      this.$refs.formDialog.show(row)
    },
    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },
    handleSizeChange(size) {
      this.searchForm.pageSize = size
      this.getTableData()
    },
    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },

    delRow(row) {
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        delSopPermApply([row.ID]).then(res => {
          this.$message.success(res.msg)
          this.getTableData()
        })
      }).catch(err => {
        console.log(err);
      });
    },

    getTableData(data) {
      getSopPermApplyList(this.searchForm).then(res => {
        this.tableData = res.response.data
        this.total = res.response.dataCount
      })
    }
  }
}

//<!-- 移到到src/local/en.json和zh-Hans.json -->
//"SopPermApply": {
//    "table": {
//        "applicantId": "applicantId",
//        "targetId": "targetId",
//        "targetType": "targetType",
//        "requestPerm": "requestPerm",
//        "approveStatus": "approveStatus",
//        "approverId": "approverId",
//        "approveTime": "approveTime",
//        "createdate": "createdate",
//    }
//},
</script>

<style lang="scss" scoped>
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}

.mt-8p {
  margin-top: 8px;
}

.pd-left {
  padding-left: 5px
}
</style>
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_MATERIAL'

//删除
export function IssueMaterialDelete(data) {
    const api = '/materail/IssueMaterial/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//分页获取库存列表
export function getLineLibraryList(data) {
    const api = '/materail/IssueMaterial/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

//物料移库
export function moveWarehouse(data) {
    const api = '/materail/IssueMaterial/StockTransfer'
    return getRequestResources(baseURL, api, 'post', data);
}

//物料拆分
export function splitMaterial(data) {
    const api = '/materail/IssueMaterial/MaterialSplit'
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取AGV仓库list
export function getAGVList(data) {
    const api = '/materail/AgvwarehouseManage/GetWarehouseList'
    return getRequestResources(baseURL, api, 'post', data);
}

//分页获取物料结存列表
export function WarehouseStorageGetPageList(data) {
    const api = '/materail/WarehouseStorage/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

//分页获取库存列表
export function WarehouseStorageSaveForm(data) {
    const api = '/materail/WarehouseStorage/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}

//分页获取入库库存列表
export function PickingDumpOrderGetListByDateAndHcode(data) {
    const api = '/materail/PickingDumpOrder/GetListByDateAndHcode'
    return getRequestResources(baseURL, api, 'post', data);
}

//分页获取结存记录列表
export function WarehousestorageRecordGetPageList(data) {
    const api = '/materail/WarehousestorageRecord/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

//修改物料结存
export function WarehousestorageRecordSaveForm(data) {
    const api = '/materail/WarehousestorageRecord/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}

//产线库存查询
export function CompanyMaterialGetPageList(data) {
    const api = '/trace/CompanyMaterial/GetPageList'
    return getRequestResources('baseURL_TRACE', api, 'post', data);
}

//产线发料汇总
export function GetIssueList(data) {
    const api = '/materail/IssueMaterial/GetIssueList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增产线退料
export function MaterialStockBackSaveForm(data) {
    const api = '/materail/WarehouseStockReturn/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//产线退料列表
export function MaterialStockBackGetPageList(data) {
    const api = '/materail/WarehouseStockReturn/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
<template>
  <el-dialog
    :style="backgroundVar"
    :title="titletbale"
    :append-to-body="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="value"
    :before-close="handleClose1"
    lock-scroll
    :fullscreen="true"
  >
    <div class="styleTable">
      <el-table
        :data="tableData"
        border
        :show-overflow-tooltip="true"
        :height="tableHeight"
        style="width: auto;color:#fff; font-size: 22px;font-weight: bold;overflow-y: auto;"
        :header-cell-style="{background:'#fafafa',textAlign: 'center',fontSize:'22px',color:'#409eff'}"
      >
        <el-table-column
          v-for="(header, index) in Object.keys(tableData[0])"
          :key="index"
          :prop="header"
          :label="header"
          header-align="center"
          align="center"
          show-overflow-tooltip
        >
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>
<script>
import { getChartStructure, getTableList } from '@/api/simConfig/simconfignew.js';
import { title } from 'echarts/lib/theme/dark';

export default {
  props: {
    // 是否显示弹出框
    value: {
      type: Boolean,
      default: false
    },
    position: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    id1: {
      type: String,
      default: ''
    },
    titletbale: {
      type: String,
      default: ''
    },
    backgroundImg: {
      type: String,
      default: ''
    }
  },
  computed: {
    backgroundVar() {
      return {
        '--background': this.backgroundImg
      }
    },
  },
  data() {
    return {
      tableData: [{}],
      columns: [],
      tableHeight: 0,
    }
  },

  created() {
    this.getBarList()
  },
  mounted() {
    this.$nextTick(function () {
      this.tableHeight = window.innerHeight - 120;
      let self = this;
      window.onresize = function () {
        self.tableHeight = window.innerHeight - 120;
      }
    })
  },
  methods: {
    async getBarList() {
      console.log(this.position, 'this.Orderthis.Orderthis.Order');
      let params =
      {
        "simLevel": this.$route.path == '/simManagement/simSpot' ? 'SIM2' : this.position.split('-')[0],
        "position": [
          this.position
        ],
        "paramList": [
          this.simlevel,
          this.BaseTime
        ]
      }
      let res = await getTableList(params)

      if (res.success && res.response != null) {
        this.tableData = res.response[0].positionResult
      }
    },
    handleClose1() {
      this.$emit('showCheck7')
    }
  }
}
</script>
<style scoped>
/deep/ .el-dialog {
    background: var(--background) no-repeat 0 0;
    background-size: 100% 100% !important;
    overflow: hidden;
}
.styleTable {
    height: 100%;
}
.styleTable /deep/.el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}
.styleTable /deep/ .el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}

.styleTable /deep/ .el-table tr {
    background-color: transparent !important;
    border: none;
}
.styleTable /deep/ .el-table--enable-row-transition .el-table__body td,
.el-table .cell {
    background-color: transparent !important;
}
.styleTable /deep/ .el-table th.el-table__cell {
    background-color: transparent !important;
    color: #fff;
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
    width: 15px;
    height: 0px;
}
/deep/ .el-dialog__title {
    color: #fff !important;
}
</style>
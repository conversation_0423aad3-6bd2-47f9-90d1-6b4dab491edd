import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_MATERIAL'
// 物料转储单

//分页获取转储单列表
export function getPickingDumpOrderList(data) {
    const api =  '/materail/PickingDumpOrder/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑转储单
export function PickingDumpOrderSaveForm(data) {
    const api =  '/materail/PickingDumpOrder/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除转储单
export function DeletePickingDumpOrder(data) {
    const api =  '/materail/PickingDumpOrder/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//入库确认
export function PickingDumpOrderConfirmForm(data) {
    const api =  '/materail/PickingDumpOrder/ConfirmForm'
    return getRequestResources(baseURL, api, 'post', data);
}


//获取转储单详情列表
export function getPickingDumpMaterialList(data) {
    const api =  '/materail/PickingDumpMaterial/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑转储单详情
export function PickingDumpMaterialSaveForm(data) {
    const api =  '/materail/PickingDumpMaterial/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除转储单详情
export function DeletePickingDumpMaterial(data) {
    const api =  '/materail/PickingDumpMaterial/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

//获取入库明细列表
export function ReceivedMaterialGetList(data) {
    const api =  '/materail/ReceivedMaterial/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//编辑库明细列表
export function ReceivedMaterialSaveForm(data) {
    const api =  '/materail/ReceivedMaterial/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除入库明细列表
export function ReceivedMaterialDelete(data) {
    const api =  '/materail/ReceivedMaterial/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//根据扫描条码查询相应物料信息
export function ReceivedMaterialGetMaterialList(data) {
    const api = '/materail/ReceivedMaterial/GetMaterialList'
    return getRequestResources(baseURL, api, 'post', data);
}
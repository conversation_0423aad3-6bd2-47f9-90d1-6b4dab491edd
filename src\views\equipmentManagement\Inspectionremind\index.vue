<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <!-- @click="RepastInfoGetPage" -->
                    <v-btn icon color="primary" @click="getTableData">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="[]"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_JZTXCX"
                    :headers="InspectionremindColum"
                    :desserts="desserts"
                    @selectePages="selectePages"
                ></Tables>
            </v-card>
        </div>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';
import moment from 'moment'; //引入 moment.js  vue 项目
import { GetMeasureAccountGetPageList } from '@/api/equipmentManagement/Inspectionremind.js';
import { InspectionremindColum } from '@/columns/equipmentManagement/Inspectionremind.js';
export default {
    name: 'RepastModel',
    components: {},
    data() {
        return {
            loading: false,
            showFrom: false,
            papamstree: {
                MeasureNo:'',
                AccountAssetNo:'',
                VerifyMethod: '',
                MeasureType: '',
                ExpirationDateFrom: moment()
                    .month(moment().month() + 2)
                    .startOf('month')
                    .format('YYYY-MM-DD'),
                ExpirationDateTo: moment()
                    .month(moment().month() + 2)
                    .endOf('month')
                    .format('YYYY-MM-DD'),
                pageIndex: 1,
                pageSize: 20
            },
            InspectionremindColum,
            VerifyMethod: [],
            MeasureType: [],
            //查询条件
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            }
        };
    },
    computed: {
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'MeasureNo',
                    label: this.$t('TPM_SBGL_JLRWGL.MeasureNo'),
                    icon: 'mdi-account-check',
                    type: 'input',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'AccountAssetNo',
                    label: this.$t('TPM_SBGL_JLRWGL.AccountAssetNo'),
                    icon: 'mdi-account-check',
                    type: 'input',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'VerifyMethod',
                    label: this.$t('TPM_SBGL_JLRWGL.jdff'),
                    icon: 'mdi-account-check',
                    selectData: this.VerifyMethod,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'MeasureType',
                    label: this.$t('TPM_SBGL_JLRWGL.abcfl'),
                    icon: 'mdi-account-check',
                    selectData: this.MeasureType,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: moment()
                        .month(moment().month() + 2)
                        .startOf('month')
                        .format('YYYY-MM-DD'), // 后2个月的最后一天,
                    key: 'ExpirationDateFrom',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_JLQJGL.jyrqks'),
                    placeholder: this.$t('TPM_SBGL_JLQJGL.jyrqks')
                },
                {
                    value: moment()
                        .month(moment().month() + 2)
                        .endOf('month')
                        .format('YYYY-MM-DD'), // 后2个月的最后一天,
                    key: 'ExpirationDateTo',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_JLQJGL.jyrqjs'),
                    placeholder: this.$t('TPM_SBGL_JLQJGL.jyrqjs')
                }
            ];
        }
    },
    async mounted() {
        this.MeasureType = await this.$getNewDataDictionary('MeasureAccoundType');
        this.VerifyMethod = await this.$getNewDataDictionary('MeasureAccountVerifyMethod');
        this.getTableData();
    },
    methods: {
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.getTableData();
        },
        async getTableData() {
            let params = {
                ...this.papamstree
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading = true;
            const res = await GetMeasureAccountGetPageList(params);
            let { success, response } = res;
            response.data.forEach(item => {
                this.MeasureType.forEach(it => {
                    if (item.Type == it.ItemValue) {
                        item.Type = it.ItemName;
                        item.TypeValue = it.ItemValue;
                    }
                });
            });
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.getTableData();
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}

.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

// 报警记录列表
<template>
    <div class="line-side-view">
        <div class="line-side-main">
            <SearchForm ref="contactTorm" class="mt-2" @selectChange="selectChange" :searchinput="searchinput" :show-from="showFrom" @searchForm="searchForm" />
            <v-card outlined>
                <div class="form-btn-list">
                    <!-- 搜索栏 -->
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary">
                        <v-icon @click="getDataList">mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'RYCXPZ_SJDC'" @click="handleExport()">{{ $t('SHIFT_RYSJ_RYCXPZ._DC') }}</v-btn>
                    <!-- <v-btn color="primary" @click="operaClick({})">新增</v-btn>
                    <v-btn color="primary" @click="sureItems()">{{ $t('GLOBAL._PLSC') }}</v-btn> -->
                </div>
                <Tables
                    :showSelect="false"
                    :headers="headers"
                    :desserts="desserts"
                    :loading="loading"
                    :tableHeight="showFrom ? 'calc(100vh - 370px)' : 'calc(100vh - 200px)'"
                    :page-options="pageOptions"
                    :btn-list="btnList"
                    :dictionaryList="dictionaryList"
                    table-name="ANDON_BJJL"
                    @selectePages="selectePages"
                    @itemSelected="selectedItems"
                    @toggleSelectAll="selectedItems"
                    @tableClick="tableClick"
                ></Tables>
            </v-card>
            <v-dialog v-model="dialogVisible" max-width="60%">
                <v-card>
                    <v-toolbar color="primary" dark="dark">
                        <v-toolbar-title>文件预览</v-toolbar-title>
                        <v-spacer></v-spacer>
                        <v-btn icon="icon" @click="dialogVisible = false">
                            <v-icon>mdi-close</v-icon>
                        </v-btn>
                    </v-toolbar>
                    <v-card-text>
                        <v-row :gutter="10">
                            <v-col v-for="(file, index) in previewFiles" :key="index" cols="12" sm="6" md="4">
                                <v-card @click="showFullImagePreview(file)">
                                    <v-img :src="file" aspect-ratio="1.75"></v-img>
                                </v-card>
                            </v-col>
                        </v-row>
                    </v-card-text>
                </v-card>
            </v-dialog>
            <!-- 全屏图片预览对话框 -->
            <v-dialog v-model="fullImageDialogVisible" fullscreen="fullscreen" hide-overlay="hide-overlay" transition="dialog-bottom-transition">
                <v-card>
                    <v-toolbar color="primary" dark="dark">
                        <v-toolbar-title>全屏预览</v-toolbar-title>
                        <v-spacer></v-spacer>
                        <v-btn icon="icon" @click="fullImageDialogVisible = false">
                            <v-icon>mdi-close</v-icon>
                        </v-btn>
                    </v-toolbar>
                    <v-card-text>
                        <v-img :src="fullImageUrl" contain="contain" height="100%"></v-img>
                    </v-card-text>
                </v-card>
            </v-dialog>
            <detailsDialog ref="detailsDialog" :opera-obj="operaObj" @handlePopup="handlePopup" />
            <upgradeDialog ref="upgradeDialog" :opera-obj="operaObj" @handlePopup="handlePopup" />
            <stepDialog ref="stepDialog" :opera-obj="operaObj" @handlePopup="handlePopup" />
            <lossDialog ref="lossDialog" :opera-obj="operaObj" @handlePopup="handlePopup" />
        </div>
    </div>
</template>
<script>
import { getAlarmTypeRootList, getAlarmTypeTreetList } from '@/api/andonManagement/alarmType.js';
import { DeleteUpgradeRule } from '@/api/andonManagement/upgradeRule.js';
import { AlarmRecordPageList, ExportAlarmRecordToExcel } from '@/api/andonManagement/alarmRecord.js';
import { alarmRecordColumns } from '@/columns/andonManagement/alarmRecord.js';
import Util from '@/util';
import dayjs from 'dayjs';
import physicalModel from '@/mixins/physicalModel';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_ANDON;
export default {
    name: 'UpgradeRule',
    components: {
        detailsDialog: () => import('./components/detailsDialog.vue'),
        upgradeDialog: () => import('./components/upgradeDialog.vue'),
        stepDialog: () => import('./components/stepDialog.vue'),
        lossDialog: () => import('./components/lossDialog.vue')
    },
    mixins: [physicalModel],
    data() {
        return {
            operaObj: {},
            showFrom: false,
            headers: alarmRecordColumns,
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            deleteId: [],
            selectedList: [],
            desserts: [],
            productLine: [],
            alarmTypeRootList: [],
            alarmTypeList: [],
            startTime: '',
            endTime: '',
            dialogVisible: false,
            fullImageDialogVisible: false,
            fullImageUrl: '',
            previewFiles: [],
            searchParams: {},
            AreaList: [],
            LineCodeList: [],
            equipmentList: [],
            unitList: [],
            eventStatusList: [],
            problemLevelList: [],
            isMainList: [
                { k: '0', v: '是' },
                { k: '1', v: '否' }
            ],
            IsMainList: [
                { k: '0', v: '否' },
                { k: '1', v: '是' }
            ]
        };
    },
    computed: {
        //查询条件
        searchinput() {
            return [
                // 开始时间
                {
                    key: 'startTime',
                    type: 'time',
                    icon: '',
                    value: this.startTime,
                    label: this.$t('GLOBAL.StartTime')
                },
                // 结束时间
                {
                    key: 'endTime',
                    type: 'time',
                    icon: '',
                    value: this.endTime,
                    label: this.$t('GLOBAL.EndTime')
                },
                // 产品线
                {
                    key: 'areacode',
                    icon: '',
                    type: 'combobox',
                    search: 'areacode',
                    linkageKey: 'firstLevel',
                    childrenLinkageArray: this.LineCodeList,
                    selectData: this.$changeSelectItems(this.AreaList, 'EquipmentCode', 'EquipmentName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.AreaCode')
                },
                // 工段
                {
                    key: 'Line',
                    icon: '',
                    type: 'combobox',
                    search: 'Line',
                    linkageKey: 'secondLevel',
                    childrenLinkageArray: this.unitList,
                    selectData: this.$changeSelectItems(this.secondLevel, 'EquipmentCode', 'EquipmentName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.ProductLineName')
                },
                // 工站
                {
                    key: 'unitcode',
                    icon: '',
                    type: 'combobox',
                    search: 'unitcode',
                    linkageKey: 'thirthLevel',
                    childrenLinkageArray: this.equipmentList,
                    selectData: this.$changeSelectItems(this.thirthLevel, 'EquipmentCode', 'EquipmentName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.UnitName')
                },
                // 设备
                {
                    key: 'equipmentCode',
                    icon: '',
                    type: 'combobox',
                    search: 'equipmentCode',
                    linkageKey: 'fourthLevel',
                    childrenLinkageArray: [],
                    selectData: this.$changeSelectItems(this.fourthLevel, 'EquipmentCode', 'EquipmentName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.EquipmentName')
                },
                // 状态
                {
                    key: 'eventStatus',
                    icon: '',
                    type: 'select',
                    selectData: this.$changeSelectItems(this.eventStatusList, 'ItemValue', 'ItemName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.state')
                },
                // 一级分类
                {
                    key: 'MainAlarmType',
                    icon: '',
                    type: 'combobox',
                    search: 'MainAlarmType',
                    linkageKey: 'fivthLevel',
                    childrenLinkageArray: this.alarmTypeList,
                    selectData: this.$changeSelectItems(this.alarmTypeRootList, 'AlarmCode', 'AlarmName'),
                    value: this.$store.state.searchForm.MainAlarmType,
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.MainAlarmType')
                },
                // 二级分类
                {
                    key: 'SubAlarmType',
                    icon: '',
                    type: 'combobox',
                    search: 'SubAlarmType',
                    linkageKey: 'sixthLevel',
                    childrenLinkageArray: [],
                    selectData: this.$changeSelectItems(this.sixthLevel, 'AlarmCode', 'AlarmName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.SubAlarmType')
                },
                // 事件号
                {
                    key: 'EventNo',
                    icon: '',
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.EventNo')
                },
                // 是否查询全部告警
                {
                    key: 'isMain',
                    icon: '',
                    type: 'combobox',
                    search: 'isMain',
                    selectData: this.$changeSelectItems(this.isMainList, 'k', 'v'),
                    value: '',
                    label: this.$t('ANDON_BJJL.isMain')
                }
            ];
        },
        btnList() {
            return [
                { text: this.$t('ANDON_BJJL.details'), icon: '', code: 'details', type: 'primary', authCode: 'BJJLCX_BJXQ' },
                { text: this.$t('ANDON_BJJL.upgrade'), icon: '', code: 'upgrade', type: 'primary', authCode: 'BJJLCX_SJLX' },
                { text: this.$t('ANDON_BJJL.AlarmPic'), icon: '', code: 'AlarmPic', type: 'primary',noShowKey: 'AlarmPic' }
                // { text: '查看损耗', icon: '', code: 'loss', type: 'primary'  }
            ];
        },
        dictionaryList() {
            return [
                // {arr: this.LineCodeList, key: 'areacode', val: 'equipmentCode', text: 'EquipmentName'},
                { arr: this.IsMainList, key: 'IsMain', val: 'k', text: 'v' },
                { arr: this.alarmTypeList, key: 'MainAlarmType', val: 'AlarmCode', text: 'AlarmName' },
                { arr: this.alarmTypeList, key: 'SubAlarmType', val: 'AlarmCode', text: 'AlarmName' },
                { arr: this.eventStatusList, key: 'RecordStatus', val: 'ItemValue', text: 'ItemName' },
                { arr: this.problemLevelList, key: 'ProblemLevel', val: 'ItemValue', text: 'ItemName' }
            ];
        }
    },
    async created() {
        console.log('create', this.$store.state.searchForm.MainAlarmType);
        await this.init();
        await this.getAlarmTypeList();
        await this.getalarmTypeList();
        this.eventStatusList = await this.$getDataDictionary('alarmEventStatus');
        this.problemLevelList = await this.$getDataDictionary('problemLevel');
        this.getDataList();
    },
    mounted() {
        console.log('mounted');
    },
    methods: {
        async init() {
            const nowTime = new Date();
            const startTime = dayjs(nowTime).format('YYYY-MM-DD') + ' 00:00:00';
            const endTime = dayjs(nowTime).format('YYYY-MM-DD HH:mm:ss');
            this.startTime = startTime;
            this.endTime = endTime;
            this.searchParams = { startTime, endTime };
            // 获取工段
            this.LineCodeList = await Util.GetEquipmenByLevel('Line');
            // 产品线
            this.AreaList = await Util.GetEquipmenByLevel('Area');
            // 获取工站
            this.unitList = await Util.GetEquipmenByLevel('Segment');
            // 设备
            this.equipmentList = await Util.GetEquipmenByLevel('Unit');
        },
        // 获取告警类型列表
        async getalarmTypeList() {
            const res = await getAlarmTypeTreetList({});
            this.alarmTypeList = [];
            const { success, response } = res || {};
            if (response && success) {
                response.forEach(e => {
                    this.alarmTypeList.push(e);
                    const { children } = e;
                    if (children && children.length) {
                        children.forEach(i => {
                            this.alarmTypeList.push(i);
                        });
                    }
                });
            }
        },
        // 获取大类列表
        async getAlarmTypeList() {
            const res = await getAlarmTypeRootList({});
            const { success, response } = res || {};
            if (success) {
                this.alarmTypeRootList = response || [];
            } else {
                this.alarmTypeRootList = [];
            }
        },
        async handleExport() {
            const { startTime, endTime } = this.searchParams;
            let params = {
                ...this.searchParams,
                startTime: startTime || this.startTime,
                endTime: endTime || this.endTime
            };
            let resp = await ExportAlarmRecordToExcel({ ...params });
            let blob = new Blob([resp], {
                type: 'application/octet-stream'
            });
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            var link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.download = `安灯报警记录${formattedDateTime}.xls`; // 修正模板字符串语法

            // 触发下载
            link.click();

            // 释放URL对象
            window.URL.revokeObjectURL(link.href);
        },
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        showFullImagePreview(file) {
            this.fullImageUrl = file;
            this.fullImageDialogVisible = true;
        },
        // 操作栏按钮
        tableClick(item, type) {
            this.operaObj = item;
            var alarmPic = this.operaObj.AlarmPic;
            if (alarmPic) {
                this.previewFiles = alarmPic.split(',');
            }
            switch (type) {
                // 详情
                case 'details':
                    this.$refs.detailsDialog.dialog = true;
                    break;
                // 升级
                case 'upgrade':
                    this.$refs.stepDialog.dialog = true;
                    break;
                // 损耗
                case 'AlarmPic':
                    this.dialogVisible = true;
                    break;
                default:
                    break;
            }
        },
        // 获取全部表格数据
        async getDataList() {
            this.loading = true;
            const { startTime, endTime } = this.searchParams;
            let params = {
                ...this.searchParams,
                startTime: startTime || this.startTime,
                endTime: endTime || this.endTime,
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await AlarmRecordPageList(params);
            const { success, response } = res || {};
            const { data, dataCount, page } = response || {};
            this.desserts = [];
            if (success && data) {
                const arr = data || [];
                arr.forEach(e => {
                    const { Predicttime } = e;
                    this.desserts.push({ ...e, Predicttime: Predicttime > 0 ? Predicttime : '' });
                });
                this.pageOptions.total = dataCount;
                this.pageOptions.page = page;
            } else {
                this.desserts = [];
            }
            this.loading = false;
        },
        // 查询数据
        searchForm(v) {
            this.searchParams = v;
            this.getDataList();
        },
        getSubAlarmType() {
            return null;
        },
        // 新增
        operaClick(o) {
            this.operaObj = o || {};
            this.$refs.updateDialog.dialog = true;
        },
        // 批量删除
        sureItems() {
            if (this.selectedList.length > 0) {
                this.deleteId = '';
                this.sureDelete();
            } else {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'error' });
            }
        },
        // 删除二次确认
        sureDelete() {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    const params = [];
                    if (this.deleteId) {
                        params.push(this.deleteId);
                    } else {
                        this.selectedList.forEach(e => {
                            params.push(e.ID);
                        });
                    }
                    const res = await DeleteUpgradeRule(params);
                    this.selectedList = [];
                    this.deleteId = '';
                    const { success, msg } = res;
                    if (success) {
                        this.pageOptions.pageCount = 1;
                        this.getDataList();
                        this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 根据子组件返回来值
        handlePopup(type, data) {
            this.getDataList();
            // switch (type) {
            //     case 'refresh':
            //         this.getDataList();
            //         break;
            //     case 'detail':
            //         this.receivedorderid = data?.ID
            //         this.$refs.materailDetailDialog.dialog = true;
            //         break;
            //     default:
            //         break;
            // }
        }
    }
};
</script>
<style lang="scss" scoped>
.line-side-view {
    display: flex;

    .line-side-main {
        flex: 1;
        width: 100%;

        .v-data-table {
            width: 100%;
        }
    }
}
</style>
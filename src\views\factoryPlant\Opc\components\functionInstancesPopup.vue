<template>
    <div class="function-instances-popup">
        <a-form :model="form" :label-col="{ span: 4, }" :wrapper-col="{ span: 14, }">
            <a-form-item v-if="!funcId" label="Function">
              <el-select filterable clearable style="width: 100%" v-model="form.OpcFunctionId" placeholder="请选择" >
                <el-option v-for="item in funcList" :key="item.ID" :label="item.Name" :value="item.ID">
                </el-option>
              </el-select>
<!--                <a-select show-search v-model="form.OpcFunctionId" placeholder="please select your function">-->
<!--                    <a-select-option v-for="item in funcList" :key="item.ID" :value="item.ID">{{ item.Name-->
<!--                    }}</a-select-option>-->
<!--                </a-select>-->
            </a-form-item>
            <a-form-item label="Machine">
                      <el-select filterable clearable style="width: 100%" v-model="form.EquipmentId" placeholder="请选择" >
                        <el-option v-for="item in machineList" :key="item.ID" :label="item.EquipmentName" :value="item.ID">
                        </el-option>
                      </el-select>
<!--                <a-select show-search v-model="form.EquipmentId" placeholder="please select your machine">-->
<!--                    <a-select-option v-for="item in machineList" :key="item.ID" :value="item.ID">{{ item.EquipmentName-->
<!--                    }}</a-select-option>-->
<!--                </a-select>-->
            </a-form-item>
            <a-form-item label="Instance" name="desc">
                <a-textarea v-model="form.InstanceId" />
            </a-form-item>
            <a-form-item label="Note" name="desc">
                <a-textarea v-model="form.Note" />
            </a-form-item>
            <a-form-item label="Enabled">
                <a-switch v-model="form.IsEnabled" />
            </a-form-item>
            <a-form-item label="Notification">
                <a-switch v-model="form.GenerateNotification" />
            </a-form-item>
        </a-form>
    </div>
</template>

<script>
import { getFunctionNoPage } from '../service'
import Util from '@/util';
export default {
    props: {
        funcId: {
            type: String,
            default: ''
        },
        machineList: {
            type: Array,
            default: () => []
        },
        funcList: {
            type: Array,
            default: () => []
        },
        editItemObj: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            form: {
                EquipmentId: undefined,
                OpcFunctionId: undefined,
                InstanceId: '',
                Note: '',
                GenerateNotification: false,
                IsEnabled: false
            }
        }
    },
    async created() {
        if (this.funcId) this.form.OpcFunctionId = this.funcId
        if (this.editItemObj && this.editItemObj.ID) {
            for (const key in this.form) {
                this.form[key] = this.editItemObj[key]
            }
            this.form.ID = this.editItemObj.ID
        }
    },
    methods: {

    }
}
</script>

<style lang="scss" scoped>
.ant-row.ant-form-item {
    margin-bottom: 10px;
}
</style>

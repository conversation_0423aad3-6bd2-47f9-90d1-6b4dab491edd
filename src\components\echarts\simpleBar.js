export function getsimpleBar(data, key, color) {
    let unit = "%"
    if (key) {
        unit = key
    }
    let option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function (params) {
                var relVal = params[0].name
                for (var i = 0, l = params.length; i < l; i++) {
                    if (params[i].value != 0) {
                        relVal += " : " + params[i].value + unit
                    }
                }
                return relVal
            }
        },
        legend: {
            orient: 'vertical',
            left: 'left'
        },
        xAxis: {
            type: 'category',
            axisTick: {
                show: false,
            },
            splitLine: {
                show: false,
            },
            axisLine: {
                show: false,
            },
            data: data.xdata
        },
        textStyle: {
            color: '#fff',
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        yAxis: {
            type: 'value',
            name: unit,
            axisLabel: {
                color: 'rgb(196, 194, 194)',
                fontSize: 13
            },
            axisTick: {
                show: false,
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: 'rgb(196, 194, 194)',
                    type: 'dashed' //背景色为虚线
                }
            },
            axisLine: {
                show: false,
            },
        },
        series: [
            {
                data: data.data,
                type: 'bar',
                barMaxWidth: "100px",
                itemStyle: {
                    color: color
                },
                label: {
                    show: true,
                    color: "black",
                    formatter: function (params) {
                        if (params.value != 0) {
                            return params.value + unit
                        } else {
                            return ""
                        }
                    }
                }
            }
        ]
    };
    return option
}

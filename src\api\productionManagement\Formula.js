import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Formula'
const baseURL1 = 'baseURL_DFM'
const baseURL2 = 'baseURL_DFM'
const baseURL3 = 'baseURL_30015'
//CIP清洗时间
export function addCipTime(data) {
    const api = '/ppm/CipTime/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}

//CIP清洗时间列表
export function getCipTimeList(data) {
    const api = '/ppm/CipTime/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}
//CIP清洗时间删除
export function delCipTimeList(data) {
    const api = '/ppm/CipTime/Delete'
    return getRequestResources(baseURL3, api, 'post', data);
}
//CIP切换方式下拉选项
export function GetCipSwitchList(data) {
    const api = '/api/DataItemDetail/GetPageList'
    return getRequestResources(baseURL1, api, 'post', data);
}
//CIP方式新增
export function addCipSwitchtype(data) {
    const api = '/ppm/CipSwitchtype/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}
//CIP方式列表
export function getCipSwitchtype(data) {
    const api = '/ppm/CipSwitchtype/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}
//CIP方式DEL
export function delCipSwitchtype(data) {
    const api = '/ppm/CipSwitchtype/Delete'
    return getRequestResources(baseURL3, api, 'post', data);
}
// 产线
export function getListByLevel(data) {
    const api = '/api/Equipment/GetListByLevel?key=' + data.key
    return getRequestResources(baseURL1, api, 'post', data);
}
// 产线
export function getLineList(data) {
    const api = `/ppm/Formulaschedule/GetLineList?areaCode=${data.areaCode}`
    return getRequestResources(baseURL3, api, 'post', null);
}
//获取子节点
export function getLineSegmentList(data) {
    const api = `/ppm/Formulaschedule/GetLineSegmentList`
    return getRequestResources(baseURL3, api, 'post', data);
}
// 瓶型
export function getSalescontainer(data) {
    const api = '/ppm/Salescontainer/GetList'
    return getRequestResources(baseURL3, api, 'post', data);
}
//产线配方及缸容量推荐新增
export function addRecommandcapacity(data) {
    const api = '/ppm/Recommandcapacity/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}
//产线配方及缸容量推荐列表
export function getRecommandcapacity(data) {
    const api = '/ppm/Recommandcapacity/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}
//产线配方及缸容量推荐删除
export function delRecommandcapacity(data) {
    const api = '/ppm/Recommandcapacity/Delete'
    return getRequestResources(baseURL3, api, 'post', data);
}
//新增喉头添加基础表
export function addThroataddition(data) {
    const api = '/ppm/Throataddition/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}
//列表喉头添加基础表
export function getThroataddition(data) {
    const api = '/ppm/Throataddition/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}
//删除列表喉头添加基础表
export function delRThroataddition(data) {
    const api = '/ppm/Throataddition/Delete'
    return getRequestResources(baseURL3, api, 'post', data);
}
//新增喉头添加基础表
export function addCookingloss(data) {
    const api = '/ppm/Cookingloss/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}
//列表喉头添加基础表
export function getCookingloss(data) {
    const api = '/ppm/Cookingloss/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}
//删除列表喉头添加基础表
export function delCookingloss(data) {
    const api = '/ppm/Cookingloss/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增喉头添加基础表
export function addFormulaloss(data) {
    const api = '/ppm/Formulaloss/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//列表喉头添加基础表
export function getFormulaloss(data) {
    const api = '/ppm/Formulaloss/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除列表喉头添加基础表
export function delFormulaloss(data) {
    const api = '/ppm/Formulaloss/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//MaterialGroupId下拉框
export function getMaterialGroup(data) {
    const api = '/api/MaterialGroup/GetList'
    return getRequestResources(baseURL1, api, 'post', data);
}
//获取设备信息
export function getEnergyInstrumentPageList(data) {
    const api = '/ppm/PoProducedExecution/GetEnergyInstrumentPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}
//获取物料信息
export function getMaterialPageList(data) {
    const api = '/api/Material/GetPageList'
    return getRequestResources(baseURL1, api, 'post', data);
}
//获取物料版本信息
export function getMaterialVersionList(data) {
    const api = '/api/MaterialVersion/GetPageList'
    return getRequestResources(baseURL1, api, 'post', data);
}
//物料下拉框
export function getMaterial(data) {
    const api = '/api/Material/GetList'
    return getRequestResources(baseURL1, api, 'post', data);
}
//物料版本下拉框
export function getMaterialVersion(data) {
    const api = '/api/MaterialVersion/GetList'
    return getRequestResources(baseURL1, api, 'post', data);
}
//物料版本下拉框
export function getSapSegment(data) {
    const api = '/api/SapSegment/GetSegmentList'
    return getRequestResources(baseURL1, api, 'post', data);
}
//获取计量单位
export function getUnitmanage(data) {
    const api = '/api/Unitmanage/GetList'
    return getRequestResources(baseURL1, api, 'post', data);
}
//新增喉头添加基础表
export function addSapEquipment(data) {
    const api = '/api/SapEquipment/SaveForm'
    return getRequestResources(baseURL2, api, 'post', data);
}
//列表喉头添加基础表
export function getaddSapEquipment(data) {
    const api = '/api/SapEquipment/GetPageList'
    return getRequestResources(baseURL2, api, 'post', data);
}
//删除列表喉头添加基础表
export function delSapEquipment(data) {
    const api = '/api/SapEquipment/Delete'
    return getRequestResources(baseURL2, api, 'post', data);
}
//新增SapBomPhaseInjection
export function addSapBomPhaseInjection(data) {
    const api = '/api/SapBomPhaseInjection/SaveForm'
    return getRequestResources(baseURL1, api, 'post', data);
}
//列表SapBomPhaseInjection
export function getSapBomPhaseInjection(data) {
    const api = '/api/SapBomPhaseInjection/GetPageList'
    return getRequestResources(baseURL1, api, 'post', data);
}
//删除SapBomPhaseInjection
export function delSapBomPhaseInjection(data) {
    const api = '/api/SapBomPhaseInjection/Delete'
    return getRequestResources(baseURL1, api, 'post', data);
}

// 查询工序与设备的关联关系
export function getSapSegmentEquipmentList(data) {
    const api = '/api/SapSegmentEquipment/GetList'
    return getRequestResources(baseURL1, api, 'post', data);
}
// 获取工段
export function getSapSegmentList(data) {
    const api =  '/api/SapSegment/GetList'
    return getRequestResources(baseURL1, api, 'post', data);
}
// 获取工段
export function getSapSegmentList2(data) {
    const api =  '/api/SapSegment/GetSegmentList'
    return getRequestResources(baseURL1, api, 'post', data);
}
// 修改 新增工段
export function saveOperation(data) {
    const api = '/api/SapSegment/SaveOperation'
    return getRequestResources(baseURL1, api, 'post', data);
}

// 删除工段、工序
export function deleteOperation(data) {
    const api = '/api/SapSegment/Delete'
    return getRequestResources(baseURL1, api, 'post', data);
}

// 修改 新增工序
export function savePhase(data) {
    const api = '/api/SapSegment/SavePhase'
    return getRequestResources(baseURL1, api, 'post', data);
}

// 查询设备列表
export function getDeviceList(data) {
    const api = '/api/Equipment/GetList'
    return getRequestResources(baseURL1, api, 'post', data)
}

// 新增工序与设备关联
export function saveSapSegmentEquipment(data) {
    const api = '/api/SapSegmentEquipment/SaveForm'
    return getRequestResources(baseURL1, api, 'post', data);
}

// 删除工序与设备关联
export function deleteSapSegmentEquipment(data) {
    const api = '/api/SapSegmentEquipment/Delete'
    return getRequestResources(baseURL1, api, 'post', data);
}

// 产品bom定义列表
export function getSapSegmentMaterialList(data) {
    const api = '/api/SapSegmentMaterial/GetPageList'
    return getRequestResources(baseURL1, api, 'post', data);
}
//获取物料版本列表
export function getSelectMaterialVersionList(id) {
    const api = '/api/SapSegmentMaterial/GetSelectMaterialVersionList/mvId?mvId=' + id
    return getRequestResources(baseURL1, api, 'get');
}
//保存COPY物料版本
export function setSapSegmentMaterial(data) {
    const api = '/api/SapSegmentMaterial/CopyData'
    return getRequestResources(baseURL1, api, 'post', data);
}
// 产品bom定义列表
export function getSapSegmentMaterialStep(data) {
    const api = '/api/SapSegmentMaterialStep/ImportData'
    return getRequestResources(baseURL1, api, 'post', data);
}

// 新增保存
export function saveSapSegmentMaterial(data) {
    const api = '/api/SapSegmentMaterial/SaveForm'
    return getRequestResources(baseURL1, api, 'post', data);
}
// 新增copyMaterialBon
export function saveCopyMaterialBom(data) {
    const api = '/api/SapSegmentMaterial/CopyData'
    return getRequestResources(baseURL1, api, 'post', data);
}

// 删除
export function deleteSapSegmentMaterial(data) {
    const api = '/api/SapSegmentMaterial/Delete'
    return getRequestResources(baseURL1, api, 'post', data);
}

// 产品物料明细
export function getSapSegmentMaterialStepList(data) {
    const api = '/api/SapSegmentMaterialStep/GetList'
    return getRequestResources(baseURL1, api, 'post', data);
}

// 新增-编辑
export function saveSapSegmentMaterialStep(data) {
    const api = '/api/SapSegmentMaterialStep/SaveForm'
    return getRequestResources(baseURL1, api, 'post', data);
}

// 删除
export function deleteSapSegmentMaterialStep(data) {
    const api = '/api/SapSegmentMaterialStep/Delete'
    return getRequestResources(baseURL1, api, 'post', data);
}

// 工序和SalesContainer对应关系
export function getSalescontainerList(data) {
    const api = '/ppm/Salescontainer/GetCheckList'
    return getRequestResources(baseURL3, api, 'post', data);
}


// 保存工序和SalesContainer对应关系
export function salescontainerSave(data) {
    const api = '/ppm/PhaseSalescontainerView/Save'
    return getRequestResources(baseURL3, api, 'post', data);
}

// 特殊配方对应缸重维护
// 列表
export function getSpecialformulacapacityList(data) {
    const api = '/ppm/Specialformulacapacity/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}

// 保存
export function specialformulacapacitySaveForm(data) {
    const api = '/ppm/Specialformulacapacity/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}

// 删除
export function specialformulacapacityDelete(data) {
    const api = '/ppm/Specialformulacapacity/Delete'
    return getRequestResources(baseURL3, api, 'post', data);
}

// 查寻树
export function getTreeList(data) {
    const api = '/api/RecipeCommon/GetRecipeTreeList'
    return getRequestResources(baseURL2, api, 'post', data);
}

// 预处理文本

// 获取预处理文本列表
export function getProcessDataView(data) {
    const api = '/api/ProcessDataView/GetList'
    return getRequestResources(baseURL3, api, 'post', data);
}

// 获取最新预处理文本
export function getLastProcessData(data) {
    const api = `/api/ProcessDataView/GetLastProcessData/${data.materialVersionId}/${data.status}`
    return getRequestResources(baseURL3, api, 'get', null);
}

// 获取最新预处理文本
export function getetLastNewProcessData(data) {
    const api = `/api/ProcessDataView/GetLastNewProcessData/${data.materialVersionId}/${data.status}`
    return getRequestResources(baseURL3, api, 'get', null);
}

// 保存最新预处理文本
export function savePreprocessingData(data) {
    const api = '/api/ProcessDataView/SavePreprocessingData'
    return getRequestResources(baseURL3, api, 'post', data);
}

// 更新预处理文本
export function editPreprocessingData(data) {
    const api = '/api/ProcessDataView/EditPreprocessingData'
    return getRequestResources(baseURL3, api, 'post', data);
}


// 辅助工时
// 列表
export function getUnproductiveSearchView(data) {
    const api = '/api/UnproductiveSearchView/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}

// 获取group reason
export function getGroupAndReasonModelList(data) {
    const api = '/api/UnproductiveTime/GetGroupAndReasonModelList'
    return getRequestResources(baseURL3, api, 'post', data);
}

// 新增
export function unproductiveTimeSave(data) {
    const api = '/api/UnproductiveTime/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}
// 删除
export function unproductiveSearchViewDelete(data) {
    const api = '/api/UnproductiveSearchView/DeleteByIdList'
    return getRequestResources(baseURL3, api, 'post', data);
}


//获取产线关系表
export function getLineRelationList(data) {
    const api = '/ppm/Linerelation/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}
//保存产线关系
export function lineRelationSaveForm(data) {
    const api = '/ppm/Linerelation/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}
//删除产线关系
export function delLineRelation(data){
    const api = '/ppm/Linerelation/Delete'
    return getRequestResources(baseURL3, api, 'post', data);
}
//获取换型事件
export function getChangeModel(data) {
    const api = '/ppm/Changemodel/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}
//删除换型时间
export function delChangeModel(data) {
    const api = '/ppm/Changemodel/Delete'
    return getRequestResources(baseURL3, api, 'post', data);
}

//添加修改换型时间
 export function addChangeModel(data) {
    const api = '/ppm/Changemodel/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}

//获取生产速度
export function getProductSpeed(data) {
    const api = '/ppm/ProductSpeed/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}
//删除生产速度
export function delProductSpeed(data) {
    const api = '/ppm/ProductSpeed/Delete'
    return getRequestResources(baseURL3, api, 'post', data);
}

//添加修改生产速度
 export function addProductSpeed(data) {
    const api = '/ppm/ProductSpeed/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}
//添加配方煮缸派产量
export function addFormulaTankCapacity(data) {
    const api = '/ppm/FormulaTankCapacity/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}
////配方煮缸派产量列表
export function getFormulaTankCapacityList(data) {
    const api = '/ppm/FormulaTankCapacity/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}
////配方煮缸派产量详情
export function getFormulaTankCapacityInfo(id) {
    const api = '/ppm/FormulaTankCapacity/GetEntity/' + id
    return getRequestResources(baseURL3, api, 'get');
}
////配方煮缸派产量删除
export function delFormulaTankCapacity(data) {
    const api = '/ppm/FormulaTankCapacity/Delete'
    return getRequestResources(baseURL3, api, 'post',data);
}
//获取工单排序树信息
export function getSortFormulaTree(data) {
    const api = '/ppm/Formulaschedule/GetSortFormulaTree'
    return getRequestResources(baseURL3, api, 'post',data);
}
//保存工单排序树信息
export function setSortFormulaTree(data) {
    const api = '/ppm/Formulaschedule/SaveSortFormulaTree'
    return getRequestResources(baseURL3, api, 'post',data);
}
//获取配方工单信息
export function getFormulaInfo(id) {
    const api = '/ppm/Formulaschedule/GetFormulaInfo/' + id
    return getRequestResources(baseURL3, api, 'get');
}
//获取罐包工单信息
export function getPackOrderInfo(id) {
    const api = '/ppm/Formulaschedule/GetPackOrderInfo/' + id
    return getRequestResources(baseURL3, api, 'get');
}
//添加包装生产速度
export function addPackSpeed(data) {
    const api = '/ppm/Packspeed/SaveForm'
    return getRequestResources(baseURL3, api, 'post',data);
}
//获取包装生产速度列表
export function getPackSpeedList(data) {
    const api = '/ppm/Packspeed/GetPageList'
    return getRequestResources(baseURL3, api, 'post',data);
}
//删除
export function delPackSpeed(data) {
    const api = '/ppm/Packspeed/Delete'
    return getRequestResources(baseURL3, api, 'post',data);
}
//获取包装生产速度详情
export function getPackOrderDetail(id) {
    const api = '/ppm/Packspeed/GetEntity/' + id
    return getRequestResources(baseURL3, api, 'get');
}
//返工说明保存
export function addChangePackOrderReworkInfo(data) {
    const api = '/ppm/Formulaschedule/ChangePackOrderReworkInfo'
    return getRequestResources(baseURL3, api, 'post',data);
}
//添加配方产线标准缸重
export function addFormulaTankCapacityLine(data) {
    const api = '/ppm/FormulaTankCapacity/SaveForm'
    return getRequestResources(baseURL3, api, 'post',data);
}
//获取配方产线标准缸重列表
export function getFormulaTankCapacityLineList(data) {
    const api = '/ppm/FormulaTankCapacity/GetPageList'
    return getRequestResources(baseURL3, api, 'post',data);
}
//删除配方产线标准缸重
export function delFormulaTankCapacityLine(data) {
    const api = '/ppm/FormulaTankCapacity/Delete'
    return getRequestResources(baseURL3, api, 'post',data);
}
//获取配方产线标准缸重详情
export function getFormulaTankCapacityLineDetail(id) {
    const api = '/ppm/FormulaTankCapacity/GetEntity/' + id
    return getRequestResources(baseURL3, api, 'get');
}
//获取排除工单列表
export function getPackOrderByFormula(data) {
    const api = '/ppm/Formulaschedule/GetPackOrderByFormula'
    return getRequestResources(baseURL3, api, 'post',data);
}
//更新工单速度
export function UpdateSapPackOrderSpeed(data) {
    const api = '/ppm/Sappackorder/UpdateSapPackOrderSpeed'
    return getRequestResources(baseURL3, api, 'post',data);
}
//获取工单排序树信息
export function getSortCookTree(data) {
    const api = '/ppm/Formulaschedule/GetSortCookTree  '
    return getRequestResources(baseURL3, api, 'post',data);
}
//获取罐包工单信息
export function getProductionOrderInfo(id) {
    const api = '/ppm/Formulaschedule/GetProductionOrderInfo/' + id
    return getRequestResources(baseURL3, api, 'get');
}
//获取罐包工单信息
export function getOrderUsableThroarInventory(data) {
    const api = '/ppm/Formulaschedule/GetOrderUsableThroarInventory'
    return getRequestResources(baseURL3, api, 'post',data);
}
//新增喉头
export function addWorkOrderThroat(data) {
    const api = '/ppm/Workorderthroat/SaveForm'
    return getRequestResources(baseURL3, api, 'post',data);
}
//修改喉头
export function editWorkOrderThroat(data) {
    const api = '/ppm/Workorderthroat/ChangeWorkThroat'
    return getRequestResources(baseURL3, api, 'post',data);
}
//删除喉头
export function delWorkOrderThroat(data) {
    const api = '/ppm/Workorderthroat/Delete'
    return getRequestResources(baseURL3, api, 'post',data);
}
//保存煮制时间维护
export function addCookTimeModel(data) {
    const api = '/api/Cooktime/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}
//获取煮制时间维护列表
export function getCookTimeModelList(data) {
    const api = '/api/Cooktime/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}
//删除煮制时间维护
export function delCookTimeMode(data) {
    const api = '/api/Cooktime/Delete'
    return getRequestResources(baseURL3, api, 'post', data);
}
//新增CIP记录
export function addCipInfo(data) {
    const api = '/api/Cipinfo/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}
//删除CIP记录
export function delCipInfo(data) {
    const api = '/api/Cipinfo/Delete'
    return getRequestResources(baseURL3, api, 'post', data);
}
//获取CIP记录
export function getCipInfo(data) {
    const api = '/api/Cipinfo/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}
//获取CIP列表详情
export function getCipInfoDetail(data) {
    const api = '/api/CipinfoDetail/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}
//保存CIP列表详情
export function saveCipinfoDetail(data) {
    const api = '/api/CipinfoDetail/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}
//获取CIP列表详情
export function getCipInfoDetails(id) {
    const api = '/api/Cipinfo/GetEntity/' +id
    return getRequestResources(baseURL3, api, 'get');
}
//新增入轻重量
export function addProdWeight(data) {
    const api = '/api/ProdWeight/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}
//删除入轻重量
export function delProdWeight(data) {
    const api = '/api/ProdWeight/Delete'
    return getRequestResources(baseURL3, api, 'post', data);
}
//获取入轻重量
export function getProdWeight(data) {
    const api = '/api/ProdWeight/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}
//物料排产属性
export function getMaterialPlanPropertyPageList(data) {
    const api = '/api/MaterialPropertyValue/GetMaterialPlanPropertyPageList'
    return getRequestResources(baseURL1, api, 'post', data);
}
//物料需求查询
export function getMaterialRequirementDto(data) {
    const api = '/ppm/Sappackorder/GetMaterialRequirementDto'
    return getRequestResources(baseURL3, api, 'post', data);
}
//获取入轻重量
export function getProdWeightDetail(id) {
    const api = '/api/ProdWeight/GetEntity/' + id
    return getRequestResources(baseURL3, api, 'get');
}

//
export function getHandPackTree(data) {
    const api = '/ppm/Formulaschedule/GetHandPackTree'
    return getRequestResources(baseURL3, api, 'post',data);
}
//获取加热油工单规则
export function getOilFormula(data){
    const api = '/ppm/OilFormula/GetPageList'
    return getRequestResources(baseURL3, api, 'post',data);
}
//删除加热油工单规则
export function delOilFormula(data){
    const api = '/ppm/OilFormula/Delete'
    return getRequestResources(baseURL3, api, 'post', data);
}
//添加或者修改加热油工单规则
export function addOilFormula(data) {
    const api = '/ppm/OilFormula/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data);
}
export function getMMILogList(data) {
    const api = '/api/InterfaceLog/GetPageList'
    return getRequestResources(baseURL3, api, 'post', data);
}

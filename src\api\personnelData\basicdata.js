import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url
//获取人员基础数据
export function StaffGetPageList(data) {
    return request({
        url: baseURL + '/api/Staff/GetPageList',
        method: 'post',
        data
    });
}
export function StaffSiteGetList(data) {
    return request({
        url: baseURL + '/api/Staff/GetList',
        method: 'post',
        data
    });
}
//新增人员
export function StaffSaveForm(data) {
    return request({
        url: baseURL + '/api/Staff/SaveForm',
        method: 'post',
        data
    });
}
//删除
export function StaffDelete(data) {
    return request({
        url: baseURL + '/api/Staff/Delete',
        method: 'post',
        data
    });
}

// 模板导入
export function StaffImprot(data) {
    return request({
        url: baseURL + '/api/Staff/Import',
        method: 'post',
        data
    });
}





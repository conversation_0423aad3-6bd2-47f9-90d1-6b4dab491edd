import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';

//备件列表
export function SparepartGetPageList(data) {
    const api = '/api/DeviceCategoryParts/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
//备件列表设备
export function DevicePartsGetList(data) {
    const api = '/api/DeviceParts/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
//备件删除
export function SparepartDelete(data) {
    const api = '/api/DeviceCategoryParts/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
//备件删除设备
export function DevicePartsDelete(data) {
    const api = '/api/DeviceParts/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
//备件新增
export function SparepartSaveForm(data) {
    const api = '/api/DeviceCategoryParts/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
//备件新增设备
export function DevicePartsSaveForm(data) {
    const api = '/api/DeviceParts/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}

//Bom列表
export function BomGetPageList(data) {
    const api = '/api/DeviceCategoryBom/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
//Bom列表设备
export function DeviceBomGetList(data) {
    const api = '/api/DeviceBom/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}

//Bom删除
export function BomDelete(data) {
    const api = '/api/DeviceCategoryBom/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
//Bom删除设备
export function DeviceBomDelete(data) {
    const api = '/api/DeviceBom/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
//Bom新增
export function BomtSaveForm(data) {
    const api = '/api/DeviceCategoryBom/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
//Bom新增设备
export function DeviceBomSaveForm(data) {
    const api = '/api/DeviceBom/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
//File列表
export function DocGetPageList(data) {
    const api = '/api/DeviceCategoryDoc/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
//File列表设备
export function DeviceDocGetList(data) {
    const api = '/api/DeviceDoc/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
//File删除
export function DocDelete(data) {
    const api = '/api/DeviceCategoryDoc/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
//File删除设备
export function DeviceDocDelete(data) {
    const api = '/api/DeviceDoc/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
//上传文件
export function DeviceuploadFile(data) {
    const api = '/api/DeviceCategoryDoc/uploadFile';
    return getRequestResources(baseURL, api, 'post', data);
}
//获取文件
export function DeviceGetFileUrl(data) {
    const api = '/api/DeviceCategoryDoc/GetFileUrl';
    return getRequestResources(baseURL, api, 'get', data);
}

//File新增
export function DocSaveForm(data) {
    const api = '/api/DeviceCategoryDoc/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
//File新增设备
export function DeviceDocSaveForm(data) {
    const api = '/api/DeviceDoc/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}

//履历列表
export function HistoryGetPageList(data) {
    const api = '/api/DeviceHistory/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}

//履历删除
export function HistoryDelete(data) {
    const api = '/api/DeviceHistory/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}

//履历新增
export function HistorySaveForm(data) {
    const api = '/api/DeviceHistory/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
function anti_shake(config, axios) {
    // if (config.url.indexOf('SaveForm') === -1) {
    //     return false
    // }
    const now_date = new Date().getTime();

    const request_info = JSON.parse(sessionStorage.getItem("request_url"));

    let checkUrl = config.url + (config.data ? JSON.stringify(config.data) : '') + (config.params ? JSON.stringify(config.params) : '')
    sessionStorage.setItem(
        "request_url",
        JSON.stringify({ url: checkUrl, time: new Date().getTime() })
    );
    if (request_info === null) return true;
    if (now_date - request_info.time < 800 && request_info.url === checkUrl) {

        let cancel;
        config.cancelToken = new axios.CancelToken((c) => {
            cancel = c;
        });
        cancel(`${config.url}请求被中断`);

        return false;
    }
    return true;
}

export default anti_shake;

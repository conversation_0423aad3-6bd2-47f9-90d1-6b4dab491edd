
export const oqclistColum = [
    // {
    //     text: '序号',
    //     value: 'Index',
    //     sortable: true
    // },
    {
        text: '产品线',
        value: 'FullLineName',
        width: 140
    },
    {
        text: 'OQC批号',
        value: 'OqcBatchNo',
        width: 120
    },
    {
        text: '物料描述',
        value: 'MaterialDescription',
        width: 140
    },
    {
        text: '物料编码',
        value: 'MaterialCode',
        width: 120
    },
    {
        text: '结果',
        value: 'Result',
        dictionary: true,
        width: 100
    },
    {
        text: '备注',
        width: 100,
        value: 'Remark'
    },
    {
        text: '创建时间',
        width: 160,
        value: 'CreateDate'
    },
    {
        text: '创建人',
        width: 100,
        value: 'CreateUserId'
    },
    { text: '操作', align: 'center',width: 140, value: 'actions' }
];
export const batchColum = [
    {
        text: '工单号',
        value: 'WoCode',
        width: 120
    },
    {
        text: '料号',
        width: 120,
        value: 'MaterialCode'
    },
    {
        text: '批次号',
        value: 'BatchNo',
        width: 100
    },
    {
        text: '数量',
        width: 100,
        value: 'RemainQuantity',
        semicolonFormat: true
    },
    { text: '操作', align: 'center',width: 100, value: 'actions' }
];
export const reasonColum = [
    {
        text: '检测项目',
        value: 'name',
        width: 120
    },
    {
        text: '抽检数',
        value: 'cjs',
        isEditCell: true,
        digitalConver: true,
        width: 90
    },
    {
        text: '不良数',
        width: 90,
        isEditCell: true,
        digitalConver: true,
        value: 'bls'
    },
    {
        text: '不良原因',
        width: 240,
        value: 'blyy'
    },
    { text: '操作', align: 'center',width: 90, value: 'actions' }
];

export const reasonUpdateColum = [
    {
        text: '检测项目',
        value: 'CheckItemName',
        width: 120
    },
    {
        text: '数量',
        width: 100,
        isEditCell: true,
        digitalConver: true,
        value: 'Quantity'
    },
    {
        text: '不良原因',
        width: 160,
        value: 'Reason'
    },
    { text: '操作', align: 'center',width: 100, value: 'actions' }
];
export const batchDetailsColum = [
    {
        text: '工单号',
        value: 'WoCode',
        width: 120
    },
    {
        text: '物料描述',
        width: 140,
        value: 'MaterialDescription'
    },
    {
        text: '料号',
        width: 120,
        value: 'MaterialCode'
    },
    {
        text: '批次号',
        value: 'BatchNo',
        width: 100
    },
    {
        text: '数量',
        width: 100,
        value: 'BatchQuantity',
        semicolonFormat: true
    }
];
export const reasonDetailsColum = [
    {
        text: '检测项目',
        value: 'CheckItemName',
        width: 120
    },
    {
        text: '抽检数',
        value: 'CheckQty',
        semicolonFormat: true,
        width: 90
    },
    {
        text: '不良数',
        width: 90,
        semicolonFormat: true,
        value: 'BadQty'
    },
    {
        text: '不良原因',
        width: 240,
        value: 'blyy'
    }
];
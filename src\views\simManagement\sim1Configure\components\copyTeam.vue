<template>
  <v-dialog
    v-model="copyDialog"
    max-width="1080px"
  >
    <v-card>
      <v-card-title
        class="headline primary lighten-2"
        primary-title
      >
        复制
      </v-card-title>
      <v-card-text style="padding: 0;">
        <div>
          <v-form ref="form">
            <v-row class="ma-4">
              <span style="color: red;">*</span>
              <v-col
                class="py-0 px-3"
                cols="12"
                sm="6"
                md="4"
              >
                <v-select
                  v-model="form.Moudelname"
                  :items="dutyList"
                  item-text="Fullname"
                  item-value="Encode"
                  :label="$t('$vuetify.dataTable.SIM_CONFIG.Moudelname')"
                  return-object
                  dense
                  outlined
                  :rules="[v => !!v || $t('GLOBAL._MANDATORY')]"
                >
                </v-select>
              </v-col>
              <span style="color: red;">*</span>
              <v-col
                class="py-0 px-3"
                cols="12"
                sm="6"
                md="4"
              >
                <v-text-field
                  v-model="form.Olinetit"
                  outlined
                  dense
                  :label="$t('$vuetify.dataTable.SIM_CONFIG.Olinetit')"
                  :rules="[v => !!v || $t('GLOBAL._MANDATORY')]"
                >
                </v-text-field>
              </v-col>
            </v-row>
            <v-row class="ma-4">
              <span style="color: red;">*</span>
              <v-col v-if="form.Moudelname.Fullname == 'SIM1' || form.Moudelname.Fullname == 'SIM1.5'">
                <el-input
                  v-model="form.selectName"
                  disabled
                  placeholder="请选择成本中心"
                  style="margin-bottom: 10px;"
                ></el-input>
                <el-input
                  v-model="filterText"
                  placeholder="输入过滤关键词"
                ></el-input>
                <el-tree
                  class="filter-tree"
                  :data="formattedTreeData"
                  :props="defaultProps"
                  :filter-node-method="filterNode"
                  ref="tree"
                  @node-click="handleCheck"
                ></el-tree>
              </v-col>
              <v-col v-if="form.Moudelname.Fullname == 'SIM2'">
                <el-input
                  v-model="form.selectName"
                  disabled
                  placeholder="请选择部门"
                  style="margin-bottom: 10px;"
                ></el-input>
                <el-input
                  v-model="filterText"
                  placeholder="输入过滤关键词"
                ></el-input>
                <el-tree
                  class="filter-tree"
                  :data="EquipmentProductLineTree"
                  :props="defaultProps"
                  :filter-node-method="filterNode"
                  ref="tree"
                  @node-click="handleCheck"
                ></el-tree>
              </v-col>

            </v-row>
            <div style="font-size: 16px;margin-bottom: 10px;margin-left: 30px;"> <span style="color: red;">*</span>上传背景图片：</div>
            <v-row class="ma-4">
              <v-col
                class="py-0 px-3"
                cols="12"
                sm="6"
                md="4"
              >
                <el-upload
                  ref="xiangqtu"
                  class="upload-demo"
                  style="width:800px"
                  accept="image/jpeg,image/gif,image/png"
                  :action="'https://sim.fhtdchem.com'+'/simapi/PageConfig/Upload'"
                  :before-upload="beforeAvatarUpload1"
                  multiple
                  :on-preview="handlePreview"
                  :on-remove="handleRemove4"
                  :on-success="handleAvatarSuccess4"
                  :file-list="deailFileList"
                  list-type="picture-card"
                  :rules="[v => !!v || $t('GLOBAL._MANDATORY')]"
                >
                  <i
                    slot="default"
                    class="el-icon-plus"
                  />
                  <div
                    slot="file"
                    slot-scope="{file}"
                  >
                    <img
                      :src="file.url"
                      alt=""
                      class="el-upload-list__item-thumbnail"
                    >
                    <span class="el-upload-list__item-actions">
                      <!-- <span
                  class="el-upload-list__item-delete"
                  @click="handlePreview(file)"
                >
                  <i class="el-icon-zoom-in" />
                </span> -->
                      <span
                        class="el-upload-list__item-delete"
                        @click="handleRemove4(file,deailFileList)"
                      >
                        <i class="el-icon-delete" />
                      </span>
                    </span>
                  </div>
                  <!-- <el-button size="small" type="primary">点击上传</el-button> -->
                </el-upload>
              </v-col>
            </v-row>
          </v-form>
          <v-card-actions style="justify-content: flex-end;">
            <v-btn
              style="float: right;"
              color="primary"
              @click="submitForm"
            >{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn
              color="normal"
              @click="closeChange"
            >{{ $t('GLOBAL._GB') }}</v-btn>
          </v-card-actions>
        </div>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
import { getTree, saveCopyConfig } from '@/api/simConfig/simconfignew.js';


export default {
  props: {
    TwoId: {
      type: String,
      default: ''
    },
    dialogType: {
      type: String,
      default: ''
    },
    OneId: {
      type: String,
      default: ''
    },
    Moudelname: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      copyDialog: false,
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      treeData: [],
      form: {
        Moudelname: '',
        Olinetit: '',
        selectName: '',
        selectCode: '',
        img: '',
      },
      deailFileList: [],
      dutyList: [
        {
          Fullname: 'SIM1',
          Encode: 'SIM1'
        },
        {
          Fullname: 'SIM1.5',
          Encode: 'SIM1.5'
        },
        {
          Fullname: 'SIM2',
          Encode: 'SIM2'
        },
        {
          Fullname: 'SIM3',
          Encode: 'SIM3'
        },
        {
          Fullname: 'SIM4',
          Encode: 'SIM4'
        },
      ],
      // selectName: '',
      // selectCode: '',
      filterText: '',
      rules: {
        Olinetit: [v => !!v || this.$t('GLOBAL.Olinetit')],
      },
    };
  },
  computed: {
    //产线树
    EquipmentProductLineTree() {
      return this.$store.getters.EquipmentProductLineTree
    },
    formattedTreeData() {
      function formatNode(node) {
        const formattedNode = {
          ...node,
          label: node.name
        };
        if (node.children && node.children.length > 0) {
          formattedNode.children = node.children.map(child => formatNode(child));
        }
        return formattedNode;
      }
      return this.treeData.map(node => formatNode(node));
    }
  },
  created() {
    // console.log(this.OneId, 'this.OneIdthis.OneId');

  },
  mounted() {

  },
  methods: {
    beforeAvatarUpload1(file) {
      const isLtSize = file.size / 1024 < 3072
      if (!isLtSize) {
        this.$message.error('上传图片大小不能超过3M')
        return false
      }
      return true
    },
    handlePreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleRemove4(file, deailFileList) {
      for (let i = 0; i < deailFileList.length; i++) {
        if (file.name === deailFileList[i].name) {
          deailFileList.splice(i, 1)
          this.deailFileList = deailFileList
        }
      }
    },
    handleAvatarSuccess4(file) {
      this.form.img = file.response.fileName
      // 图片上传成功
      this.deailFileList.push({
        name: file.response.fileName,
        url: file.response.fileUrl,
      })
    },
    save() {
      this.getTreeList()
    },
    async getTreeList() {
      const res = await getTree();
      if (res.success) {
        this.treeData = res.response
      }
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleCheck(data) {
      this.form.selectName = data.name
      this.form.selectCode = data.id
    },
    async submitForm() {
      const params = {
        "CopyId": this.OneId,
        "SimLevel": this.form.selectCode,
        "Olinetit": this.form.Olinetit,
        "Baroundimg": this.form.img
      }
      const res = await saveCopyConfig(params);
      if (res.success) {
        this.$store.commit('SHOW_SNACKBAR', { text: '复制成功', color: 'success' });
        this.$emit('checkCopy')
      }
    },
    closeChange() {
      this.$emit('checkCopy')
    }
  },
}
</script>
<style lang="scss" scoped>
</style>
export function DetectZooms (data,titName) {
    document.addEventListener('mousewheel', function (e) {
        e = e || window.event;
        if ((e.wheelDelta && event.ctrlKey) || e.detail) {
            event.preventDefault();
        }
    }, {
        capture: false,
        passive: false
    });
}
export function setRemFontSize (data,titName) {
    const baseSize = data; // 基准字体大小
    const designWidth = 1920; // 设计稿宽度
    const viewportWidth = document.documentElement.clientWidth || window.innerWidth;
    const fontSize = viewportWidth / designWidth * baseSize;
    document.documentElement.setAttribute('style', 'font-size: '+ fontSize +'px !important')
}
export function setWebFontSize (data,titName) {
    const baseSize = window.devicePixelRatio
    if(baseSize > 1){
        console.log('baseSize',baseSize)
    }
}

<template>
    <div>
        <div class="form-btn-list">
            <v-btn color="primary" v-has="'SBTZGL_SBWJ_ADD'" :disabled="!rowtableItem.ID" @click="btnClickEvet('addFile')">{{ $t('GLOBAL._XZ') }}</v-btn>
            <v-btn color="primary" v-has="'SBTZGL_SBWJ_ALLREMOVE'" :disabled="!deleteList.length" @click="btnClickEvet('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn>
        </div>
        <Tables
            :footer="false"
            :page-options="pageOptions"
            :loading="loading"
            :btn-list="btnList"
            tableHeight="calc(100vh - 220px)"
            table-name="TPM_SBGL_SBTZGL_SBWJ"
            :headers="EquipPropColum"
            :desserts="desserts"
            @selectePages="selectePages"
            @tableClick="tableClick"
            @itemSelected="SelectedItems"
            @toggleSelectAll="SelectedItems"
        ></Tables>
        <createRepast ref="createRepast" @load="load" :dialogType="dialogType" :tableItem="tableItem" :DeviceCategoryId="DeviceCategoryId"></createRepast>
    </div>
</template>
<script>
import { DeviceCategoryPropGetList, DeviceCategoryPropDelete } from '@/api/equipmentManagement/DeviceCategoryProp.js';
import { EquipPropColum } from '@/columns/equipmentManagement/Equip.js';
import { Message } from 'element-ui';

export default {
    components: {
        createRepast: () => import('./components/createRepast2.vue')
    },
    props: {
        rowtableItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            loading: false,
            showFrom: false,
            DeviceCategoryId: '',
            papamstree: {
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            EquipPropColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            MyFlag: false,
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            hasChildren: {} // 新增字典详情判断-子节点才能新增
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'editFile',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBTZGL_SBWJ_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBTZGL_SBWJ_DELETE'
                }
            ];
        }
    },
    async created() {
        // await this.RepastInfoTARGetPage();
    },
    methods: {
        load(item) {
            this.RepastInfoPropGetPage(item);
        },
        // 设备target列表查询
        async RepastInfoPropGetPage(itemrow, flag) {
            let params = {
                DeviceCategoryId: ''
            };
            this.loading = true;
            params.DeviceCategoryId = this.rowtableItem.ID;
            this.DeviceCategoryId = this.rowtableItem.ID;
            let res = await DeviceCategoryPropGetList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = response || {} || [];
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'addFile':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'editFile':
                    for (let k in this.tableItem) {
                        this.$refs.createRepast.SbxxList.forEach(item => {
                            if (item.id == k) {
                                item.value = this.tableItem[k];
                            }
                        });
                    }
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await DeviceCategoryPropDelete(params);
                    if (res.success) {
                        this.tableItem = {};
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.RepastInfoPropGetPage(this.rowtableItem);
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + item);
            this.deleteList = item;
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoPropGetPage(this.rowtableItem);
        }
    }
};
</script>
<style lang="scss" scoped></style>

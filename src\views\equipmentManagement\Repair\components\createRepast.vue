<template>
    <v-dialog v-model="showDialog" max-width="980px">
        <v-card class="" v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete v-model="form.ProductlineCode" :items="productionlineList"
                                item-text="EquipmentName" item-value="EquipmentCode"
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.ProductlineCode')" @change="changeV"
                                return-object required :rules="[v => !!v || '产线必填']" clearable dense
                                outlined></v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete v-model="form.LineCode" :items="lineCodeList" item-text="EquipmentName"
                                item-value="EquipmentCode" :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.LineCode')"
                                @change="changeL" required :rules="[v => !!v || '工段必填']" return-object clearable dense
                                outlined></v-autocomplete>
                        </v-col>

                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete v-model="form.ShiftCode" :items="ShiftList" required
                                :rules="[v => !!v || '班次必填']" item-value="ID" item-text="Name" return-object outlined
                                clearable dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.ShiftName')"></v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete v-model="form.DeviceId" :loading="loading" :items="items" required
                                :rules="[v => !!v || '设备编号必填']" item-value="ID" item-text="Code" flat @change="getBomList"
                                outlined return-object clearable dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.DeviceCode')">
                                <template #item="data">
                                    <template v-if="typeof data.item !== 'object'">
                                        <v-list-item-content v-text="data.item"></v-list-item-content>
                                    </template>
                                    <template v-else>
                                        <v-list-item-content>
                                            <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                            <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                        </v-list-item-content>
                                    </template>
                                </template>
                            </v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="form.InputType" :items="inputTypeList" required
                                :rules="[v => !!v || '录入类型必填']" item-value="ItemValue" item-text="ItemName" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.InputType')"></v-select>
                        </v-col>

                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="form.Allday" :items="alldays" item-value="k" item-text="v" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.Allday')"></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="form.BOM" :items="deviceBomList" item-value="ID" item-text="AccessoriesName"
                                return-object outlined dense label="BOM"></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="form.FaultCode" :items="faultType" item-value="value" item-text="name"
                                outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.FaultCode')"></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.AbnormalDesc')"
                                v-model="form.AbnormalDesc" required :rules="[v => !!v || '异常描述必填']" auto-grow outlined
                                rows="4" row-height="30"></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card class="" v-if="dialogType == 'look'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XQ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field :title="editedItem.EventNum" disabled outlined dense
                            v-model="editedItem.EventNum"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-autocomplete v-model="editedItem.ProductlineCode" :items="productionlineList"
                            item-text="EquipmentName" item-value="EquipmentCode"
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.ProductlineCode')" @change="changeV"
                            return-object disabled required :rules="[v => !!v || '产线必填']" clearable dense
                            outlined></v-autocomplete>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-autocomplete v-model="editedItem.LineCode" :items="lineCodeList" item-text="EquipmentName"
                            item-value="EquipmentCode" :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.LineCode')"
                            @change="changeL" required :rules="[v => !!v || '工段必填']" return-object clearable disabled dense
                            outlined></v-autocomplete>
                    </v-col>

                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-autocomplete v-model="editedItem.ShiftCode" :items="ShiftList" required
                            :rules="[v => !!v || '班次必填']" item-value="ID" item-text="Name" return-object outlined disabled
                            clearable dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.ShiftName')"></v-autocomplete>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-autocomplete v-model="editedItem.DeviceId" :loading="loading" :items="items" required
                            :rules="[v => !!v || '设备编号必填']" item-value="ID" item-text="Code" flat outlined return-object
                            clearable disabled dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.DeviceCode')">
                            <template #item="data">
                                <template v-if="typeof data.item !== 'object'">
                                    <v-list-item-content v-text="data.item"></v-list-item-content>
                                </template>
                                <template v-else>
                                    <v-list-item-content>
                                        <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                        <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                    </v-list-item-content>
                                </template>
                            </template>
                        </v-autocomplete>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select v-model="editedItem.InputType" :items="inputTypeList" required
                            :rules="[v => !!v || '录入类型必填']" item-value="ItemValue" item-text="ItemName" outlined disabled
                            dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.InputType')"></v-select>
                    </v-col>

                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select v-model="editedItem.Allday" :items="alldays" item-value="k" item-text="v" disabled
                            outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.Allday')"></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select v-model="editedItem.BomId" :items="deviceBomList" disabled item-value="ID"
                            item-text="AccessoriesName" outlined dense label="BOM"></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select v-model="editedItem.FaultCode" disabled :items="faultType" item-value="value"
                            item-text="name" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.FaultCode')"></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.AbnormalDesc')"
                            v-model="editedItem.AbnormalDesc" required :rules="[v => !!v || '异常描述必填']" auto-grow outlined
                            disabled rows="4" row-height="30"></v-textarea>
                    </v-col>
                </v-row>
            </v-card-text>
        </v-card>
        <v-card class="" v-if="dialogType == 'edit'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XG') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field :title="editedItem.EventNum" disabled outlined dense
                                v-model="editedItem.EventNum"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete v-model="editedItem.ProductlineCode" :items="productionlineList"
                                item-text="EquipmentName" item-value="EquipmentCode"
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.ProductlineCode')" @change="changeV"
                                return-object required :rules="[v => !!v || '产线必填']" clearable dense
                                outlined></v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete v-model="editedItem.LineCode" :items="lineCodeList" item-text="EquipmentName"
                                item-value="EquipmentCode" :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.LineCode')"
                                @change="changeL" required :rules="[v => !!v || '工段必填']" return-object clearable dense
                                outlined></v-autocomplete>
                        </v-col>

                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete v-model="editedItem.ShiftCode" :items="ShiftList" required
                                :rules="[v => !!v || '班次必填']" item-value="ID" item-text="Name" return-object outlined
                                clearable dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.ShiftName')"></v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete v-model="editedItem.DeviceId" :loading="loading" :items="items" required
                                :rules="[v => !!v || '设备编号必填']" item-value="ID" item-text="Code" flat outlined return-object
                                clearable dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.DeviceCode')">
                                <template #item="data">
                                    <template v-if="typeof data.item !== 'object'">
                                        <v-list-item-content v-text="data.item"></v-list-item-content>
                                    </template>
                                    <template v-else>
                                        <v-list-item-content>
                                            <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                            <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                        </v-list-item-content>
                                    </template>
                                </template>
                            </v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="editedItem.InputType" :items="inputTypeList" required
                                :rules="[v => !!v || '录入类型必填']" item-value="ItemValue" item-text="ItemName" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.InputType')"></v-select>
                        </v-col>

                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="editedItem.Allday" :items="alldays" item-value="k" item-text="v" outlined
                                dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.Allday')"></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="editedItem.BomId" :items="deviceBomList" item-value="ID"
                                item-text="AccessoriesName" return-object outlined dense label="BOM"></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="editedItem.FaultCode" :items="faultType" item-value="value" item-text="name"
                                outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.FaultCode')"></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXGD.AbnormalDesc')"
                                v-model="editedItem.AbnormalDesc" required :rules="[v => !!v || '异常描述必填']" auto-grow
                                outlined rows="4" row-height="30"></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions pa-4 class="lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'debug'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                调试
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                        <v-text-field v-model="debugForm.Debugtype" outlined dense label="调试类型"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                        <v-text-field v-model="debugForm.Failuredescription" outlined dense label="调试记录"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                        <v-text-field v-model="debugForm.Maintenanceperson" outlined dense label="操作员"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                        <v-text-field v-model="debugForm.Confirmtime" type="datetime-local" outlined dense
                            label="调试时间"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                        <v-text-field v-model="debugForm.Status" outlined dense label="设备状态"></v-text-field>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'debuglog'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                调试记录
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                        <v-text-field v-model="debugForm.Debugtype" outlined dense label="调试类型"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                        <v-text-field v-model="debugForm.Failuredescription" outlined dense label="调试记录"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                        <v-text-field v-model="debugForm.Maintenanceperson" outlined dense label="操作员"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                        <v-text-field v-model="debugForm.Confirmtime" type="datetime-local" outlined dense
                            label="调试时间"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                        <v-text-field v-model="debugForm.Status" outlined dense label="设备状态"></v-text-field>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { DeviceRepairWoSaveForm, DeviceAccessoriesGetList } from '@/api/equipmentManagement/Repair.js';
import { EquipGetList } from '@/api/equipmentManagement/Equip.js';
import { StaffSiteGetList } from '@/api/peopleManagement/basicdata.js';
import { EquipmentGetPageList, ShiftGetList } from '@/api/common.js';
export default {
    props: {
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => { }
        },
        equipStatuslist: {
            type: Array,
            default: () => []
        },
        maintenanceType: {
            type: Array,
            default: () => []
        },
        inputTypeList: {
            type: Array,
            default: () => []
        },
        productionlineList: {
            type: Array,
            default: () => []
        },
        alldays: {
            type: Array,
            default: () => []
        },
        faultType: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            deviceBomList: [],
            // 设备账号查询
            loading: false,
            items: [],
            peopleitems: [],
            lineCodeList: [], // 工段
            ShiftList: [], // 班次
            //
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            form: {
                FaultCode: '',
                BomId: '',
                ProductlineCode: '',
                ProductlineName: '',
                LineCode: '',
                LineName: '',
                ShiftName: '',
                ShiftCode: '',
                DeviceCode: '',
                DeviceId: '',
                AbnormalDesc: '',
                InputType: '',
                RepairType: '应急维修',
                Allday: '',
            },
            debugForm: {
                Debugtype: '',
                Failuredescription: '',
                Maintenanceperson: '',
                Confirmtime: '',
                Status: ''
            }
        };
    },
    computed: {
        editedItem() {
            const { FaultCode, EventNum, BomId, ProductlineCode, ProductlineName, LineCode, LineName, ShiftName, ShiftCode, DeviceCode, DeviceId, AbnormalDesc, InputType, RepairType, Allday } = this.tableItem;
            return {
                FaultCode,
                EventNum,
                BomId,
                ProductlineCode,
                ProductlineName,
                LineCode,
                LineName,
                ShiftName,
                ShiftCode,
                DeviceId,
                DeviceCode,
                AbnormalDesc,
                InputType,
                RepairType,
                Allday
            };
        }
    },
    watch: {
        showDialog(val) {
            if (this.dialogType == 'add') {
                this.$nextTick(() => {
                    this.$refs.form.reset();
                });
            }
            if (val && (this.dialogType == 'edit' || this.dialogType == 'look')) {
                const { DeviceId } = this.tableItem;
                if (DeviceId) {
                    this.getBomList({ ID: DeviceId });
                }
            }
        },
        // search(val) {
        //     debugger;
        //     val && val !== this.form.DeviceCode && this.querySelections({ key: val });
        // },
        'editedItem.LineCode': 'Getlinecodelist',
        'editedItem.DeviceId': 'GetDeviceCodelist'
    },
    created() {
        this.queryPeoplelist();
        this.GetShiftGetList();
    },
    methods: {
        async getBomList(data) {
            if (!data) return false;
            const { ID } = data;
            let resp = await DeviceAccessoriesGetList({ eqid: ID });
            this.deviceBomList = resp.response;
        },
        closeEquip() {
            this.showDialog = false;
            this.$refs.form.reset();
        },
        // 回显工段
        Getlinecodelist(n, o) {
            this.$nextTick(() => {
                this.lineCodeList = []; //工段
                if (n) {
                    this.lineCodeList = [{ EquipmentName: this.tableItem.LineName, EquipmentCode: this.tableItem.LineCode }];
                }
            });
        },
        GetDeviceCodelist(n, o) {
            this.$nextTick(() => {
                if (n) {
                    this.querySelections({});
                }
            });
        },
        // 工段
        async changeV(LineCode) {
            const { ID, EquipmentCode } = LineCode;
            this.lineCodeList = [];
            const res = await EquipmentGetPageList({ DataItemCode: 'EquipmentLevel', ParentId: ID, pageIndex: 1, pageSize: 9999 });
            const { success, response } = res;
            if (success) {
                this.lineCodeList = response.data;
            }
            await this.querySelections({ ProductlineCode: EquipmentCode });
        },
        //获取工段下设备
        async changeL(LineCode) {
            const { ID, EquipmentCode } = LineCode;
            this.lineCodeGZList = [];
            const res = await EquipmentGetPageList({ DataItemCode: 'EquipmentLevel', ParentId: ID, pageIndex: 1, pageSize: 9999 });
            const { success, response } = res;
            if (success) {
                this.lineCodeGZList = response.data;
            }
            await this.querySelections({ linecode: EquipmentCode });
        },
        // 获取班次
        async GetShiftGetList() {
            const res = await ShiftGetList();
            const { success, response } = res;
            if (success) {
                this.ShiftList = response || [];
            }
        },
        // 获取设备
        async querySelections(v) {
            this.loading = true;
            const res = await EquipGetList(v);
            let { success, response } = res;
            if (success) {
                this.items = response;
                this.loading = false;
            }
        },
        // 获取人员
        async queryPeoplelist() {
            this.loading = true;
            const res = await StaffSiteGetList({ key: '' });
            let { success, response } = res;
            if (success) {
                this.peopleitems = response;
                this.loading = false;
            }
        },
        async addSave(type) {
            let formSate = await this.$refs.form.validate();
            if (formSate) {
                const paramsKey = Object.keys(this.form);
                let params = {};
                const paramsObj = type == 'add' ? this.form : this.editedItem;
                const { LineCode, ProductlineCode, DeviceId, ShiftCode, BomId } = paramsObj;
                let obj = {};
                if (ProductlineCode.EquipmentCode) {
                    const { EquipmentCode, EquipmentName } = ProductlineCode;
                    obj = { ProductlineCode: EquipmentCode, ProductlineName: EquipmentName };
                }
                if (LineCode.EquipmentCode) {
                    const { EquipmentCode, EquipmentName } = LineCode;
                    obj = { ...obj, LineCode: EquipmentCode, LineName: EquipmentName };
                }
                if (DeviceId.Code) {
                    const { Code, ID } = DeviceId;
                    obj = { ...obj, DeviceCode: Code, DeviceId: ID };
                }
                if (ShiftCode.Name) {
                    const { Name, ID } = ShiftCode;
                    obj = { ...obj, ShiftName: Name, ShiftCode: ID };
                }
                if (BomId.ID) {
                    const { AccessoriesName, ID } = BomId;
                    obj = { ...obj, BomName: AccessoriesName, BomId: ID };
                }
                paramsKey.forEach(item => {
                    params[item] = paramsObj[item];
                });
                Object.assign(params, obj);
                if (type == 'edit') {
                    params.ID = this.tableItem.ID;
                }
                const res = await DeviceRepairWoSaveForm(params);
                let { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                    this.$parent.$parent.RepastInfoGetPage();
                    this.showDialog = this.classcheckbox ? false : true;
                }
            }
        }
    }
};
</script>

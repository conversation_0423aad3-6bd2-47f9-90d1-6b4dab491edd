<template>
    <div class="admin">
        <app-drawer ref="drawer" class="admin_drawer" @drawer:collapsed="mini = !mini" />
        <app-toolbar class="admin_toolbar" extended @side-icon-click="handleDrawerVisiable" />
        <v-main class="lighten-3 pt-15">
            <!-- Page Wrapper -->
            <div class="page_wrapper">
                <router-view />
            </div>
            <!-- App Footer -->
            <!-- <v-footer height="auto" class="pa-3 app--footer"> -->
            <!-- <span> &copy; {{ new Date().getFullYear() }}</span> -->
            <!-- <v-spacer /> -->
            <!-- <span class="caption mr-1">Make With Love</span> -->
            <!-- <v-icon color="pink" small>mdi-heart</v-icon> -->
            <!-- </v-footer> -->
        </v-main>
        <!-- Go to top -->
        <app-fab />
    </div>
</template>

<script>
import AppDrawer from '@/components/AppDrawer';
import AppToolbar from '@/components/AppToolbar';
import AppFab from '@/components/AppFab';

export default {
    name: 'LayoutDefault',
    components: {
        AppDrawer,
        AppToolbar,
        AppFab
    },

    data() {
        return {
            mini: false,
            showDrawer: true
        };
    },
    methods: {
        handleDrawerVisiable() {
            this.$refs.drawer.toggleDrawer();
        }
    }
};
</script>

<style lang="scss" scoped>
.page_wrapper {
    // display: block
    height: calc(100vh - 40px);
    min-height: calc(100vh - 112px - 48px);
    padding: 12px;
    overflow: auto;

    .container {
        max-width: 1200px;
    }
}
</style>

<template>
  <div class="mattersNeedingBox">
    <div
      class="titName"
      v-for="(item,index) in list"
      :key="index"
    >
      {{ item.SortCode + '）' + item.ItemName }}
    </div>
  </div>
</template>
<script>
import { GetPageList, getClassifyTree } from '@/api/systemManagement/dataDictionary.js';
export default {
  props: {
    itemCode: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    list: [],
    simid: '',
  }),
  created() {
    console.log('进来了');
  },
  mounted() {
    this.getClassifyTree()
  },
  methods: {
    // 获取字典数据
    async getClassifyTree() {
      let papams = {
        lang: this.$store.getters.getLocale
      };
      const res = await getClassifyTree(papams);
      console.log(res, 'res99999999999999999999');

      if (res && res.success) {
        this.treeData = res.response;
        this.treeData.map((el, index) => {
          if (el.value == 'SIM') {
            this.simid = el.id
          }
        })

      } else {
        this.treeData = [];
      }
      this.GetPageList()

    },
    // 字典数据点击
    async GetPageList(e) {
      let params =
      {
        "lang": "cn",
        "key": "",
        "RootId": this.simid,
        "itemCode": this.itemCode,
        "pageIndex": 1,
        "pageSize": 50,
        enable: 1,
      };
      console.log(params, 'paramsparamsparamsparamsparamsparamsparamsparamsparamsparamsparamsparams');

      const res = await GetPageList(params);
      console.log(res, 'res88888888888888888888');

      let { success, response } = res || {};
      if (success) {
        // console.log(response, 'kkkk');
        this.list = response.data.sort((a, b) => a.SortCode - b.SortCode);
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.mattersNeedingBox {
    width: 100%;
    height: 90%;
    overflow-y: auto;
}
.titName {
    width: 100%;
    height: auto;
    font-size: 16px;
    color: #fff;
    margin-top: 10px;
    letter-spacing: 1px;
    font-weight: bold;
}
</style>
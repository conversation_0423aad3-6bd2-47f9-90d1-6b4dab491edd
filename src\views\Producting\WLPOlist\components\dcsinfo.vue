<template>
    <div class="usemystyle parameter">
        <div class="InventorySearchBox">
            <div class="searchbox">
                <el-input class="quickSearchinput" :placeholder="$t('BatchPallets.QuickSearch')" v-model="QuickSearch">
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>
                <el-button style="margin-left: 5px" size="small" icon="el-icon-refresh" @click="getsearch()">{{
                    this.$t('Inventory.refresh') }}</el-button>
                <el-button size="small" style="margin-left: 5px" icon="el-icon-s-help" @click="getempty()">{{
                    this.$t('GLOBAL._CZ') }}</el-button>

            </div>
        </div>
        <div class="tablebox">
            <el-table :data="tableList" style="width: 100%" height="700">
                <el-table-column v-for="(item, index) in header" :fixed="item.fixed ? item.fixed : false" :key="index"
                    :align="item.align" :prop="item.prop ? item.prop : item.value"
                    :label="$t(`$vuetify.dataTable.${tableId}.${item.value}`)" :width="item.width">
                 
                    <template slot-scope="scope">
                        {{ scope.row[item.prop] }}
                    </template>
                </el-table-column>
            </el-table>
            <div class="paginationbox">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="pageOptions.page" :page-sizes="pageOptions.pageSizeitems"
                    :page-size="pageOptions.pageSize" layout="total, sizes, prev, pager, next, jumper"
                    :total="pageOptions.total" background></el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';
import { PODcsInfoColumn } from '@/columns/factoryPlant/tableHeaders';
import { GetPoDcsInfo } from '@/api/Producting/POlist.js';

export default {
    name: 'dscinfo',
    data() {
        return {
            QuickSearch: '',
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            header: PODcsInfoColumn,
            tableList: [],
            tableId: 'POLIST_dcsinfo',
            Recipe: '',
            ProductionOrderNo: '',
        };
    },
    mounted() {
        this.changePagination();
        console.log(this.ProductionOrderNo);

    
    },
    methods: {
        getProductionOrderNo(ProductionOrderNo) {
            this.tableList = [];
            this.ProductionOrderNo = ProductionOrderNo;
            this.gettabeldata();
        },
     
        async gettabeldata() {
            let params = {
                Key: this.QuickSearch,
                BatchId: this.ProductionOrderNo,
                pageIndex: this.pageOptions.page,
                pageSize: this.pageOptions.pageSize
            };
            let res = await GetPoDcsInfo(params);
            this.tableList = res.response ?? [];
          
        },
        getempty() {
            this.QuickSearch = '';
            this.pageOptions.page = 1;
            this.pageOptions.pageSize = 20;
            this.gettabeldata();
        },
        getsearch() {
            this.pageOptions.page = 1;
            this.pageOptions.pageSize = 20;
            this.gettabeldata();
        },
        changePagination() {
            let el2 = document.getElementsByClassName(`el-select-dropdown__item`);
            for (let i = 0; i < el2.length; i++) {
                el2[i].innerHTML = el2[i].innerHTML.replace('条/页', this.$t('PAGINATION.MYPAGE'));
            }
        },
        handleSizeChange(val) {
            this.pageOptions.pageSize = val;
            this.gettabeldata();
        },
        handleCurrentChange(val) {
            this.pageOptions.page = val;
            this.gettabeldata();
        },
    }
};
</script>
<style lang="scss" scoped>
.parameter {
    .searchboxtitle {
        font-size: 1.7vh;
        color: #767777;
        padding-bottom: 5px;
        margin-left: 10px;
    }

    .el-tabs {
        height: 94%;
    }

    .subsubtabs {
        .el-tabs--border-card {
            border: 0 !important;
            box-shadow: none !important;
        }
    }

    .paginationbox {
        height: 10vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .dialogdetailbox {
        display: flex;
        align-items: center;
        width: 100%;
        margin-top: 10px;

        .dialogdetailsinglelabel {
            font-weight: 600;
            width: 47%;
            text-align: right;
        }

        .dialogdetailsinglevalue {
            width: 78%;
            margin-left: 20px;
        }
    }

    .splitdetailbox {
        padding-bottom: 10px;
        border: 1px solid #e8e8e8;
        margin-bottom: 5px;

        .splitdetailboxtitle {
            background: #f5f5f5;
            height: 3.5vh;
            display: flex;
            align-items: center;
            padding-left: 5px;
            font-size: 1.1rem;
            color: #303133;
        }

        .detailsnote {
            background-color: #fdf6ec;
            border-color: #faecd8;
            color: #e6a23c;
            padding: 8px;
            font-size: 0.9rem;
            margin: 5px 10px 0px 10px;
        }

        .splitdetailboxtitleTag {
            margin-left: 5px;
            background: #5cb85c;
            color: #fff;
            border-color: #5cb85c;
        }
    }
}

#Producedialog {
    .el-input {
        width: 250px !important;
    }

    .el-select {
        width: 250px !important;
    }
}
</style>

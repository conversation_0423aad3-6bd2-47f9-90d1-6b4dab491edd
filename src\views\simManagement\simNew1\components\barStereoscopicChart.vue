<template>
  <div
    :id="id1"
    :style="styles"
  ></div>
</template>
<script>
import { title } from 'echarts/lib/theme/dark';

export default {
  props: {
    id1: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    list: {
      type: Array,
      default: () => []
    },
    styles: {
      type: Object,
      default: () => { }
    },
  },
  data: () => ({
    barSterChart: null,
    // styless: {
    //   width: '100%',
    //   height: '100%',
    //   backgroundImage: 'url("https://img1.baidu.com/it/u=2756664614,3290369440&fm=253&fmt=auto&app=138&f=JPEG?w=753&h=500")',
    //   backgroundSize: '100%',
    // }
  }),
  created() {
    console.log(this.styles, 'style');
    // this.styless.backgroundImage = this.styles.backgroundImage
  },
  mounted() {
    this.query()
  },
  methods: {
    query() {
      this.barSterChart = document.getElementById(this.id1);
      var myChart = this.$echarts.init(this.barSterChart);
      var option;

      option = {
        title: {
          text: this.title,
          textStyle: { // 标题样式
            color: '#fff'
          }
        },
        tooltip: {
          trigger: "item"
        },
        legend: {
          top: 0,
          data: [
            {
              name: "报修数量",
              textStyle: {
                color: "white"
              }
            },
            {
              name: "完成数量",
              textStyle: {
                color: "white"
              }
            },
            {
              name: "完成率",
              textStyle: {
                color: "white"
              }
            }
          ]
        },
        grid: {
          top: '25%',
          bottom: '20%',
          right: '5%',
          left: '5%',
          containLabel: true
        },
        toolbox: {
          show: true,
        },
        calculable: true,
        xAxis: [
          {
            type: 'category',
            splitLine: {
              show: false
            },
            data: ['10.10', '10.11', '10.12', '10.13', '10.14'],
            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff" //X轴文字颜色
              },
            },
          }
        ],
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff" //X轴文字颜色
              },
            },
          },
          {
            //右边百分比部分
            name: '百分比',
            type: "value",
            position: "right",
            axisLine: {
              lineStyle: {
                color: "#fff"
              }
            },
            axisTick: {
              show: false,
            },
            min: 0,
            max: 100,
            axisLabel: {
              textStyle: {
                color: "#fff",
              },
              show: true,
              interval: "auto",
              formatter: "{value}%",
            },
            show: true,
            splitLine: {  //网格线
              show: false
            }
          }
        ],
        series: [
          {
            name: '报修数量',
            tooltip: {
              show: false
            },
            type: 'bar',
            barWidth: 10,
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0,
                  color: "#e69138" // 0% 处的颜色
                }, {
                  offset: 0.6,
                  color: "#e69138" // 60% 处的颜色
                }, {
                  offset: 1,
                  color: "#e69138" // 100% 处的颜色
                }], false)
              }
            },
            data: [10, 20, 30, 40, 20],
            barGap: 0,
          }, {
            name: '报修数量',
            type: 'bar',
            barWidth: 10,
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0,
                  color: "#b45f06" // 0% 处的颜色
                }, {
                  offset: 0.6,
                  color: "#b45f06" // 60% 处的颜色
                }, {
                  offset: 1,
                  color: "#b45f06" // 100% 处的颜色
                }], false)
              }
            },
            barGap: 0,
            data: [10, 20, 30, 40, 20],
            label: {
              show: true,
              position: 'top',
              textStyle: {
                color: 'white',
                fontSize: 10
              }
            }
          }, {
            name: '报修数量',
            tooltip: {
              show: false
            },
            type: 'pictorialBar',
            itemStyle: {
              borderWidth: 1,
              borderColor: '#ff9900',
              color: '#ff9900' // 控制顶部方形的颜色
            },
            symbol: 'path://M 0,0 l 90,0 l -60,60 l -90,0 z',
            symbolSize: ['18', '7'], // 第一个值控制顶部方形大小
            symbolOffset: ['-10', '-4'], // 控制顶部放行 左右和上下
            symbolRotate: -16,
            symbolPosition: 'end',
            data: [10, 20, 30, 40, 20],
            z: 3,
          },
          {
            name: '完成数量',
            tooltip: {
              show: false
            },
            type: 'bar',
            barWidth: 10,
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0,
                  color: "#15a00f" // 0% 处的颜色
                }, {
                  offset: 0.6,
                  color: "#11ad0a" // 60% 处的颜色
                }, {
                  offset: 1,
                  color: "#19c213" // 100% 处的颜色
                }], false)
              }
            },
            data: [5, 8, 22, 30, 10],
            barGap: 0,

          }, {
            name: '完成数量',
            type: 'bar',
            barWidth: 10,
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0,
                  color: "#26ec10" // 0% 处的颜色
                }, {
                  offset: 0.6,
                  color: "#26ec10" // 60% 处的颜色
                }, {
                  offset: 1,
                  color: "#26ec10" // 100% 处的颜色
                }], false)
              }
            },
            barGap: 0,
            data: [5, 8, 22, 30, 10],
            label: {
              show: true,
              position: 'top',
              textStyle: {
                color: 'white',
                fontSize: 10
              }
            }
          }, {
            name: '完成数量',
            tooltip: {
              show: false
            },
            type: 'pictorialBar',
            itemStyle: {
              borderWidth: 1,
              borderColor: '#fff',
              color: '#26f000' // 顶部方块的颜色
            },
            symbol: 'path://M 0,0 l 90,0 l -60,60 l -90,0 z',
            symbolSize: ['17', '7'], // 第一个值控制顶部方形大小
            symbolOffset: ['10', '-4'], // 控制顶部放行 左右和上下
            symbolRotate: -16,
            symbolPosition: 'end',
            data: [5, 8, 22, 30, 10],
            z: 3,

          },
          {
            name: '完成率',
            type: 'line',
            smooth: true,
            barWidth: 10,
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0,
                  color: "#ffb017" // 0% 处的颜色
                }, {
                  offset: 0.6,
                  color: "#ffb017" // 60% 处的颜色
                }, {
                  offset: 1,
                  color: "#ffb017" // 100% 处的颜色
                }], false)
              }
            },
            barGap: 0,
            data: [80, 85, 80, 90, 50],
            label: {
              show: true,
              position: 'top',
              textStyle: {
                color: 'white',
                fontSize: 10
              },
              formatter: '{c}%'
            }
          },
        ]
      };

      myChart.setOption(option, true);

      window.addEventListener("resize", () => {
        myChart.resize()
      }, false);
    },

  },
}
</script>
<style lang="scss" scoped>
// #barSterChart {
//     width: 100%;
//     height: 100%;
// }
</style>
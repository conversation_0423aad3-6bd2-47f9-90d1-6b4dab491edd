<template>
    <div class="dictionary-view">
        <TreeView :items="treeData" :title="$t('DFM_ZZJM._ZZXX')" @clickClassTree="clickClassTree"></TreeView>
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinput" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="DepartmentGetPageList">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'ZZJM_ADD'" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="error" v-has="'ZZJM_ALLREMOVE'" :disabled="deleteList.length == 0"
                        @click="btnClickEvet('delete')">{{
                            $t('GLOBAL._PLSC') }}</v-btn>
                </div>
                <Tables :page-options="pageOptions" :loading="loading" :headers="headers"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'" table-name="DFM_ZZJM"
                    :btn-list="btnList" :desserts="desserts" @selectePages="selectePages" @tableClick="tableClick"
                    @itemSelected="SelectedItems" @toggleSelectAll="SelectedItems"></Tables>
                <department-dialog ref="department" :rootitems="rootitems" :treeDatas="treeData" :dialog-type="dialogType"
                    :table-item="tableItem" :delete-list="deleteList" :has-children="hasChildren"></department-dialog>
                <AttributeDialog ref="attribute"></AttributeDialog>
            </v-card>
        </div>
    </div>
</template>
<script>
import { companyTree } from '@/api/common.js';
import { GetPageList } from '@/api/systemManagement/dataDictionary.js';
import { DepartmentGetPageList, DepartmentDelete } from '@/api/factoryPlant/departmentManagement.js';
import { departmentManagementColum } from '@/columns/factoryPlant/departmentManagement.js';
const searchinputs = [
    {
        value: '',
        key: 'ItemName',
        icon: 'mdi-account-check',
        label: '',
        placeholder: '请输入项目名称/项目值'
    }
];
export default {
    name: 'DataDictionary',
    components: {
        DepartmentDialog: () => import('./components/departmentDialog.vue'),
        AttributeDialog: () => import('./components/attributeDialog.vue')
    },
    data() {
        return {
            // tree 字典数据
            loading: true,
            showFrom: false,
            treeData: [],
            papamstree: {
                RootId: null,
                itemCode: null,
                lang: null,
                key: null,
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            searchinput: searchinputs,
            headers: departmentManagementColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            hasChildren: {}, // 新增字典详情判断-子节点才能新增

            // 新增属性列表
            rootitems: []
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'ZZJM_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'ZZJM_DELETE'
                }
            ];
        }
    },
    mounted() {
        this.companyTree();
        this.DepartmentGetPageList();
        this.GetPageList();
    },
    methods: {
        // 查询数据
        searchForm(value) {
            this.papamstree.key = value.ItemName;
            this.DepartmentGetPageList();
        },
        // 树状点击获取
        clickClassTree(v) {
            console.log(v.name + '点击tree');
            // 判断是否还有节点
            this.hasChildren = v;
            this.papamstree.key = '';
            this.papamstree.RootId = v.id;
            this.papamstree.itemCode = v.value;
            this.DepartmentGetPageList();
        },
        // 获取树形数据
        async companyTree() {
            let papams = {
                // lang: this.lang
                // level: ''
            };
            const res = await companyTree(papams);
            console.log(res);
            if (res.success) {
                this.treeData = res.response;
            } else {
                this.treeData = [];
            }
        },
        // 树形数据点击
        async DepartmentGetPageList() {
            let params = {
                key: this.papamstree.key,
                parentid: this.papamstree.RootId,
                page: this.papamstree.pageIndex,
                intPageSize: this.papamstree.pageSize
            };
            this.loading = true;
            const res = await DepartmentGetPageList(params);
            if (res.success) {
                this.loading = false;
                let data = res.response.data || [];
                data.forEach((item, index) => {
                    item.index = (res.response.page - 1) * res.response.pageSize + index + 1;
                });
                this.desserts = res.response.data;
                this.pageOptions.total = res.response.dataCount;
                this.pageOptions.page = res.response.page;
                this.pageOptions.pageCount = res.response.pageCount;
                this.pageOptions.pageSize = res.response.pageSize;
            }
        },

        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.department.dialog = true;
                    return;
                case 'delete':
                    if (this.deleteList.length) {
                        this.deltable();
                    } else {
                        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'warning' });
                    }
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.$refs.department.dialog = true;
                    return;
                case 'attribute':
                    this.$refs.attribute.dialog = true;
                    this.$refs.attribute.initData(item);
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await DepartmentDelete(params);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.DepartmentGetPageList();
                        this.companyTree();
                        this.deleteList = [];
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            this.tableItem = {}
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.DepartmentGetPageList();
        },

        // 获取属性编码
        async GetPageList() {
            this.rootitems = [];
            let papams = {
                lang: this.lang,
                itemCode: 'DepartmentLevel'
                // pageIndex: this.papamstree.pageIndex,
                // pageSize: this.papamstree.pageSize
            };
            const res = await GetPageList(papams);
            let { success, response } = res;
            if (success) {
                this.rootitems = response.data || [];
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: hidden;
    }
}
</style>

import { getRequestResources } from '@/api/fetch';
import request from '@/util/request';
const baseURL_30015 = 'baseURL_30015'
const DFM = 'baseURL_DFM'
const baseURL_EQUIPMENT = 'baseURL_EQUIPMENT';

//kpi列表
export function GetSafeMsg(data) {
    const api = '/api/SafetytgtView/GetPageList'
    return getRequestResources(baseURL_30015, api, 'post', data);
}

//kpi列表保存
export function GetSafeSaveForm(data) {
    const api = '/api/SafetytgtView/SaveForm'
    return getRequestResources(baseURL_30015, api, 'post', data);
}


//kpi列表删除
export function GetSafeDelete(data) {
    const api = '/api/SafetytgtView/Delete'
    return getRequestResources(baseURL_30015, api, 'post', data);
}

//kpi列表数据类型
export function GetKPIMsgGetDataType(data) {
    const api = '/api/Kpitgt/GetDataType'
    return getRequestResources(baseURL_30015, api, 'post', data);
}

//kpi导入
export function GetKPIMsgImportData(data) {
    const api = '/api/Kpitgt/ImportData'
    return getRequestResources(baseURL_30015, api, 'post', data);
}

//kpi物料模型
export function GetEquipmentListByLevel(data) {
    const api = '/api/Equipment/GetEquipmentTree';
    return getRequestResources(DFM, api, 'post', data);
}
//kpi物料模型
export function getUnitList(data) {
    const api = '/api/Unitmanage/GetList'
    return getRequestResources(DFM, api, 'post', data);
}
export function ExportData(url, data) {
    return request({
        url: url,
        method: 'post',
        data,
        responseType: 'blob'
    });
}
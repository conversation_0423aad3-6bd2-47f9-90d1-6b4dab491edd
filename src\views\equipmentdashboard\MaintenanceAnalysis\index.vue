<template>
    <div :class="isFull ? 'device-kanban-full' : 'device-kanban-not'">
        <dv-full-screen-container>
            <div class="sbkb">
                <div class="all">
                    <div class="title">
                        维修分析
                        <div class="searchbox" style="top: 10px">
                            <div class="dashboardinputbox" style="width: 270px">
                                <el-date-picker value-format="yyyy-MM-dd" v-model="time" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                            </div>
                        </div>
                        <div class="searchbox">
                            <div class="dashboardinputbox" v-for="(item, index) in inputlist" :key="index">
                                <el-input v-model="item.value" v-if="item.type == 'input'" :placeholder="item.name"></el-input>
                                <el-select clearable v-if="item.type == 'select'" v-model="item.value" filterable :placeholder="item.name">
                                    <el-option v-for="(it, ind) in item.option" :key="ind" :label="it.ItemName" :value="it.ItemValue"></el-option>
                                </el-select>
                            </div>
                            <div class="dashboardinputbox"><el-button icon="el-icon-search" size="mini" @click="search">查询</el-button></div>
                            <div class="nowtimebox">
                                {{ nowTime }}
                            </div>
                        </div>
                    </div>
                    <div class="tabbox">
                        <div class="tabboxrow">
                            <div class="tabboxbox" style="width: 26%">
                                <div class="tabboxboxtitle">维修设备数量</div>
                                <div class="tabboxboxcenter" id="chart1"></div>
                            </div>
                            <div class="tabboxbox" style="width: 46%">
                                <div class="tabboxboxtitle">设备维修率</div>
                                <div class="tabboxboxcenter">
                                    <div class="tabboxboxcentertop">
                                        <div class="tabboxboxcenterleft" id="chart2"></div>
                                        <div class="tabboxboxcenterright">
                                            <div class="tabboxboxcentericon" v-for="(item, index) in iconlist" :key="index">
                                                <div class="iconbox">
                                                    <img :src="item.src" />
                                                </div>
                                                <div class="valuebox">
                                                    <div class="valueboxnum">{{ item.value }}</div>
                                                    <div class="valueboxtitle">{{ item.title }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tabboxboxcenterbottom">
                                        <div class="tabboxboxcentericon" v-for="(item, index) in iconlist2" :key="index">
                                            <div class="iconbox">
                                                <img :src="item.src" />
                                            </div>
                                            <div class="valuebox">
                                                <div class="valueboxnum">{{ item.value }}</div>
                                                <div class="valueboxtitle">{{ item.title }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tabboxbox" style="width: 26%">
                                <div class="tabboxboxtitle">维修任务</div>
                                <div class="tabboxboxcenter">
                                    <el-table :data="wxrwdata" stripe height="100%" style="width: 100%">
                                        <el-table-column
                                            v-for="(it, ind) in wxfxheader"
                                            :key="ind"
                                            align="center"
                                            :prop="it.prop ? it.prop : it.value"
                                            :label="it.text"
                                            :width="it.width"
                                        ></el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </div>
                        <div class="tabboxrow">
                            <div class="tabboxbox" style="width: 26%">
                                <div class="tabboxboxtitle">设备维修次数</div>
                                <div class="tabboxboxcenter" id="chart3"></div>
                            </div>
                            <div class="tabboxbox" style="width: 46%">
                                <div class="tabboxboxtitle">设备维修时长</div>
                                <div class="tabboxboxcenter">
                                    <el-table :data="wxscdata" stripe height="100%" style="width: 100%">
                                        <el-table-column
                                            v-for="(it, ind) in wxscheader"
                                            :key="ind"
                                            align="center"
                                            :prop="it.prop ? it.prop : it.value"
                                            :label="it.text"
                                            :width="it.width"
                                        ></el-table-column>
                                    </el-table>
                                </div>
                            </div>
                            <div class="tabboxbox" style="width: 26%">
                                <div class="tabboxboxtitle">维修人员作业次数</div>
                                <div class="tabboxboxcenter" id="chart4"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </dv-full-screen-container>
    </div>
</template>
<script>
import { DeviceChartGetRepairQty, DeviceChartGetRepairRate, DeviceChartGetDeviceRepairWo, DeviceChartGetRepairSum, DeviceChartGetDeviceRepair } from '@/api/equipmentManagement/MaintenanceAnalysis';
import { wxfxheader, wxscheader } from '@/columns/equipmentdashboard/tableheader';
import { getbarstack } from '@/components/echarts/stackBar.js';
import { getlinebydata } from '@/components/echarts/Line.js';
import { getCircleBar } from '@/components/echarts/CircleBar.js';
import { getGaugeProgress } from '@/components/echarts/GaugeProgress.js';
import { getsimpleBar } from '@/components/echarts/simpleBar.js';

import moment from 'moment';
import '@/views/equipmentdashboard/style.scss';
export default {
    data() {
        return {
            isFull: false,
            myChart1: null,
            myChart2: null,
            myChart3: null,
            myChart4: null,
            wxrwdata: [],
            wxfxheader: wxfxheader,
            wxscdata: [],
            wxscheader: wxscheader,
            inputlist: [
                {
                    id: 'DeviceCode',
                    type: 'input',
                    value: '',
                    name: '设备编码'
                },
                {
                    id: 'DeviceCategroy',
                    value: '',
                    type: 'select',
                    option: [],
                    name: '设备类型'
                },
                {
                    id: 'Department',
                    value: '',
                    type: 'input',
                    name: '工作区'
                }
            ],
            iconlist: [
                {
                    src: require('../image/jhsc.png'),
                    value: 0,
                    title: '计划时长'
                },
                {
                    src: require('../image/wxsc.png'),
                    value: 0,
                    title: '总维修时长'
                }
            ],
            iconlist2: [
                {
                    src: require('../image/wxsc.png'),
                    value: 0,
                    title: '待维修'
                },
                {
                    src: require('../image/wxs.png'),
                    value: 0,
                    title: '总维修数'
                },
                {
                    src: require('../image/jrwc.png'),
                    value: 0,
                    title: '今日完成'
                }
            ],
            time: [moment().startOf('week').format('YYYY-MM-DD'), moment().endOf('week').format('YYYY-MM-DD')],
            Timeinterval: null,
            nowTime: '',
            SearchParams: {}
        };
    },
    async mounted() {
        this.inputlist.forEach(item => {
            this.SearchParams[item.id] = item.value;
        });
        this.SearchParams.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
        this.SearchParams.queryStart = this.time[0];
        this.SearchParams.queryEnd =  this.time[1] + ' 23:59:59';
        this.inputlist[1].option = await this.$getNewDataDictionary('DeviceCategory');
        this.gettime();
        this.MyDeviceChartGetDeviceRepairWo();
        this.MyDeviceChartGetDeviceRepair();
        setTimeout(() => {
            this.getChart1();
            this.getChart2();
            this.getChart3();
            this.getChart4();
            window.addEventListener('resize', this.handleResize);
        }, 500);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.myChart1) {
            this.myChart1.dispose(); // 清理图表实例
            this.myChart2.dispose(); // 清理图表实例
            this.myChart3.dispose(); // 清理图表实例
            this.myChart4.dispose(); // 清理图表实例
        }
        this.Timeinterval = null;
    },
    methods: {
        search() {
            if (this.time == null) {
                this.time = [];
            }
            this.inputlist.forEach(item => {
                this.SearchParams[item.id] = item.value;
            });
            this.SearchParams.queryStart = this.time[0];
            this.SearchParams.queryEnd =  this.time[1] + ' 23:59:59';
            this.MyDeviceChartGetDeviceRepairWo();
            this.MyDeviceChartGetDeviceRepair();
            this.getChart1();
            this.getChart2();
            this.getChart3();
            this.getChart4();
        },
        gettime() {
            this.nowTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.Timeinterval = setInterval(() => {
                this.nowTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            }, 1000);
        },
        async MyDeviceChartGetDeviceRepairWo() {
            let res = await DeviceChartGetDeviceRepairWo(this.SearchParams);
            for (let k in res.response) {
                let item = res.response[k];
                item.ReceiveBy = await this.$getPerson(item.ReceiveBy);
            }
            this.wxrwdata = res.response;
        },
        async MyDeviceChartGetDeviceRepair() {
            let res = await DeviceChartGetDeviceRepair(this.SearchParams);
            for (let k in res.response) {
                let item = res.response[k];
                item.ReceiveBy = await this.$getPerson(item.ReceiveBy);
            }
            this.wxscdata = res.response;
        },
        async getChart1() {
            let chartDom = document.getElementById('chart1');
            this.myChart1 = this.$echarts.init(chartDom);

            let res = await DeviceChartGetRepairQty(this.SearchParams);
            let data = res.response.series;
            let option = getCircleBar(data);
            this.myChart1.setOption(option, true);
        },
        async getChart2() {
            let chartDom = document.getElementById('chart2');
            let res = await DeviceChartGetRepairRate(this.SearchParams);
            let dataArr = res.response.series[0].data;
            this.iconlist[0].value = dataArr[5];
            this.iconlist[1].value = dataArr[3];
            this.iconlist2[0].value = dataArr[0];
            this.iconlist2[1].value = dataArr[2];
            this.iconlist2[2].value = dataArr[1];
            this.myChart2 = this.$echarts.init(chartDom);
            let option = getGaugeProgress(dataArr[4]);
            this.myChart2.setOption(option, true);
        },
        async getChart3() {
            let chartDom = document.getElementById('chart3');
            this.myChart3 = this.$echarts.init(chartDom);
            this.SearchParams.datavalue = 'RepairSum';
            let res = await DeviceChartGetRepairSum(this.SearchParams);
            let data = res.response;
            let data1 = {
                xdata: data.categorydata,
                data: data.series[0].data
            };
            let option = getsimpleBar(data1, '次', '#5EF02B');
            this.myChart3.setOption(option, true);
        },
        async getChart4() {
            let chartDom = document.getElementById('chart4');
            this.myChart4 = this.$echarts.init(chartDom);
            this.SearchParams.datavalue = 'RepairCount';
            let res = await DeviceChartGetRepairSum(this.SearchParams);
            let data = res.response;
            for (let k in data.categorydata) {
                data.categorydata[k] = await this.$getPerson(data.categorydata[k]);
            }
            let data1 = {
                xdata: data.categorydata,
                data: data.series[0].data
            };
            let option = getsimpleBar(data1, '次', '#E5D53D');
            this.myChart4.setOption(option, true);
        },
        handleResize() {
            if (this.myChart1) {
                this.myChart1.resize();
                this.myChart2.resize();
                this.myChart3.resize();
                this.myChart4.resize();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.page_wrapper {
    padding: 0 !important;
}
</style>

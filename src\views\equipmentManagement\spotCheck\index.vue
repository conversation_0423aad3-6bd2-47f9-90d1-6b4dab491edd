<template>
    <div class="dictionary-view">
        <TreeView :items="treeData"   :title="$t('TPM_SBGL_SBTZGL._SBFL')" @clickClassTree="clickClassTree"></TreeView>
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'SBDJXM_DC'" @click="handleExport">{{ $t('GLOBAL._EXPORT') }}</v-btn>
                    <v-btn color="primary" v-has="'SBDJXM_DR'" @click="handleImport('spotCheckItem')">{{ $t('GLOBAL._DR') }}</v-btn>
                    <v-btn color="primary" v-has="'SBDJXM_ADD'" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="primary" v-has="'SBDJXM_ALLREMOVE'" :disabled="!deleteList.length" @click="btnClickEvet('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn>
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_SBDJXM"
                    :headers="keepPlanColum"
                    :desserts="desserts"
                    @selectePages="selectePages"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"
                ></Tables>
                <createRepast
                    ref="createRepast"
                    :SpotCheckType="SpotCheckType"
                    :DeviceCategoryId="papamstree.DeviceCategoryId"
                    :maintenanceType="maintenanceType"
                    :repastTypelist="repastTypelist"
                    :dialogType="dialogType"
                    :tableItem="tableItem"
                ></createRepast>
            </v-card>
        </div>

        <div class="loading-box" v-if="importLoading">
            <a-spin tip="导入中..." :spinning="importLoading"></a-spin>
        </div>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';

import { keepPlanColum } from '@/columns/equipmentManagement/upkeep.js';
import {
    GetSpotCheckItemDelete,
    GetSpotCheckItemGetFileUrl,
    GetDeviceCategoryTree,
    GetSpotCheckItemPageList,
    ImportSpotCheckItemData,
    GetSpotCheckItemExportData
} from '@/api/equipmentManagement/SpotCheckItem.js';
import { configUrl } from '@/config';
import { Message } from 'element-ui';
import { GetExportData } from '@/api/equipmentManagement/Equip.js';
import equipment from '@/mixins/equipment';
export default {
    name: 'RepastModel',
    components: {
        createRepast: () => import('./components/createRepast.vue')
    },
    mixins: [equipment],
    data() {
        return {
            importLoading: false,
            // tree 字典数据
            loading: true,
            showFrom: false,
            treeData: [],
            papamstree: {
                DeviceCategoryId: '',
                Code: '',
                Name: '',
                Type: '',
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            keepPlanColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            hasChildren: {}, // 新增字典详情判断-子节点才能新增

            //就餐类型
            repastTypelist: [],
            maintenanceType: [], //保养点检类型
            mcCyclelist: [],
            SpotCheckType: []
        };
    },
    computed: {
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'Code',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.ProjectCode'),
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Name',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.ProjectName'),
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Type',
                    selectData: this.SpotCheckType,
                    icon: 'mdi-account-check',
                    type: 'select',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Classify'),
                    placeholder: '分类'
                }
            ];
        },
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBDJXM_EDIT'
                },
                {
                    text: this.$t('GLOBAL._CheckFile'),
                    code: 'file',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBDJXM_CHECK'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBDJXM_DELETE'
                }
            ];
        }
    },
    async mounted() {
        this.GetFactorylineTree();
        // 获取就餐类型
        this.RepastType();
        this.getMaintainType();
        this.SpotCheckType = await this.$getNewDataDictionary('SpotCheckType');
        // this.GetMcCyclelist();
    },
    methods: {
        async handleExport() {
            let params = {
                ...this.papamstree
            };
            const baseUrl3 = configUrl[process.env.VUE_APP_SERVE]['baseURL_EQUIPMENT'] + `/api/SpotCheckItem/ExportData`;
            let res = await GetExportData(baseUrl3, params);
            let binaryData = [];
            binaryData.push(res);
            const url = window.URL.createObjectURL(new Blob(binaryData));
            console.log(url);
            const link = document.createElement('a');
            link.href = url;
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            let fileName = `设备点检项目${formattedDateTime}.xlsx`;
            document.body.appendChild(link);
            link.setAttribute('download', fileName);
            link.click();
            window.URL.revokeObjectURL(link.href);
        },
        handleImport() {
            let _this = this;
            let input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xls,.xlsx';
            input.click();
            let Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            input.onchange = async function () {
                let file = input.files[0];
                let formdata = new FormData();
                formdata.append('file', file);
                _this.importLoading = true;
                try {
                    let res = await ImportSpotCheckItemData(formdata, Factory);
                    _this.$store.commit('SHOW_SNACKBAR', { text: res.response });
                    _this.RepastInfoGetPage();
                    _this.importLoading = false;
                } catch {
                    _this.importLoading = false;
                }
            };
        },
        // 获取树形数据
        async GetFactorylineTree() {
            let params = {};
            params.factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            const res = await GetDeviceCategoryTree(params);
            let { success, response } = res;
            if (success) {
                this.treeData = response || [];
                this.papamstree.DeviceCategoryId = this.treeData[0].id;
                this.RepastInfoGetPage();
            }
        },
        clickClassTree(val) {
            this.papamstree.pageIndex = 1;
            if (val.id == this.papamstree.DeviceCategoryId) {
                this.papamstree.DeviceCategoryId = '';
            } else {
                this.papamstree.DeviceCategoryId = val.id;
            }
            this.RepastInfoGetPage();
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.RepastInfoGetPage();
        },
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                ...this.papamstree
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading = true;
            const res = await GetSpotCheckItemPageList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },

        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    this.$refs.createRepast.clearFiles();
                    return;
                case 'delete':
                    this.tableItem = {};
                    this.deltable();
                    // this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'warning' });
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.$refs.createRepast.Fileform.FilePath = this.tableItem.FilePath;
                    this.$refs.createRepast.Fileform.Name = this.tableItem.FileName;
                    if (this.tableItem.FileName != '' || this.tableItem.FileName != null) {
                        this.$refs.createRepast.Fileform.FileList = [
                            {
                                name: this.tableItem.FileName
                            }
                        ];
                    }
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'file':
                    if (item.FilePath == '' || item.FilePath == null) {
                        Message({
                            message: `${this.$t('GLOBAL.NoFile')}`,
                            type: 'warning'
                        });
                        return false;
                    }
                    this.getFile();
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        async getFile() {
            let params = {
                fileName: this.tableItem.FilePath
            };
            let res = await GetSpotCheckItemGetFileUrl(params);
            window.open(res.response);
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await GetSpotCheckItemDelete(params);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.RepastInfoGetPage();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        },
        // 获取设备维修周期
        async GetMcCyclelist() {
            // const res = await this.$getDataDictionary('Cycle');
            // this.mcCyclelist = res || [];
        },
        // 获取就餐类型
        async RepastType() {
            const res = await this.$getDataDictionary('RepastType');
            this.repastTypelist = res || [];
        },
        // 获取保养维修类型字典
        async getMaintainType() {
            const res = await this.$getDataDictionary('MaintainType');
            this.maintenanceType = res || [];
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}

.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

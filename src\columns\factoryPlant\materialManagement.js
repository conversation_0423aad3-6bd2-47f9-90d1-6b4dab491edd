// import i18n from '@/plugins/i18n';
// 物料管理
export const materialsColum = [
    { text: '序号', value: 'Index', width: '60px' },
    // { text: '厂别', value: 'Plant', width: '120px' },
    { text: '物料料号', value: 'Code', width: '140px' },
    { text: '物料名称', value: 'NAME', width: '180px' },
    { text: '物料分类', value: 'Type', width: '140px', dictionary: true },
    // { text: '物料组', value: 'Categorycode', width: '140px', dictionary: true },
    { text: '基本单位', value: 'UnitName', width: '140px' },
    { text: '版本号', value: 'Version', width: '120px' },
    // { text: '等离子作业', value: 'IsSpecial', width: '160px', dictionary: true },
    { text: '描述', value: 'Description', width: '160px' },
    { text: '属性', align: 'center', value: 'attribute', width: '150px' },
    { text: '是否启用', align: 'center', value: 'Deleted', width: '110px' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        value: 'actions',
        align: 'center',
        width: '160px',
        sortable: true
    }
];

// 一级分类
export const parimaryClassification = [
    // { text: '序号', value: 'Index', with: 60, sortable: true },
    { text: '编号', value: 'Code', width: '120px' },
    { text: '名称', value: 'Name', width: '120px' },
    { text: '特征', value: 'traitName', width: '120px' },
    { text: '描述', value: 'Description', width: '120px' },
    {
        text: '操作',
        value: 'actions',
        align: 'center',
        width: '80px',
        sortable: true
    }
];

// 二级分类
export const sencondClassification = [
    { text: '编号', value: 'lang', width: '120px' },
    { text: '名称', value: 'SortCode', width: '120px' },
    { text: '所属一级分类', value: 'SortCode', width: '120px' },
    { text: '厂别', value: 'SimpleSpelling', width: '120px' },
    { text: '描述', value: 'QuickQuery', width: '120px' },
    {
        text: '操作',
        value: 'actions',
        align: 'center',
        width: '80px',
        sortable: true
    }
];

// 物料BOM管理
export const materialBOMColum = [
    {
        text: '序号',
        value: 'Index',
        width: '60px',
        sortable: true
    },
    { text: '成品料号', value: 'MaterialCode', width: '160px' },
    { text: '成品名称', value: 'MaterialName', width: '240px' },
    { text: '版本', value: 'Version', width: '100px' },
    // { text: 'BOM使用标志', value: 'BomUsage', width: '150px' },
    // { text: '替代BOM', value: 'AltBom', width: '120px' },
    // { text: '数量', value: 'Quantity', width: '120px' },
    { text: '单位', value: 'Uom', width: '120px' },
    { text: '状态', value: 'Status', width: '120px' },
    { text: '有效期自', value: 'EffectStart', width: '180px' },
    { text: '有效期至', value: 'EffectEnd', width: '180px' },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    {
        text: '操作',
        value: 'actions',
        align: 'center',
        width: '140px',
        sortable: true
    }
];
// 物料BOM明细
export const materialBOMDetail = [
    { label: '子物料号', prop: 'CompoentName', width: '180px' },
    { label: '子物料编码', prop: 'CompoentCode', width: '110px' },
    { label: '上级物料', prop: 'ParentName', width: '110px' },
    { label: '上级物料编码', prop: 'ParentCode', width: '110px' },
    { label: '良品率', prop: 'Conversionrate', },
    { label: '父级数量', prop: 'ParentQuantity', },
    { label: '子级数量', prop: 'CompoentQuantity', },
    { label: '父级单位', prop: 'ParentUom', },
    { label: '子级单位', prop: 'CompoentUom', },
    { label: '物料组', prop: 'MaterialGroup', type: 'template', template: 'MaterialGroup', },
    {
        label: '操作',
        prop: 'actions',
        type: 'template',
        width: '80px',
        template: 'actions',
        sortable: true
    }
];

// 物料BOM明细
export const prmMaterialBOMDetail = [
    { label: '子物料号', prop: 'CompoentName', width: '350px' },
    { label: '子物料编码', prop: 'CompoentCode', width: '120px' },
    { label: '上级物料', prop: 'ParentName', width: '260px' },
    { label: '上级物料编码', prop: 'ParentCode', width: '120px' },
    { label: '父级数量', prop: 'ParentQuantity', width: '100px' },
    { label: '子级数量', prop: 'CompoentQuantity', width: '100px' },
    {
        label: '操作',
        prop: 'actions',
        type: 'template',
        width: '60px',
        template: 'actions'
    }
];

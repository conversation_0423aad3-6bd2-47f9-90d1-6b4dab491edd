<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <!-- @click="RepastInfoGetPage" -->
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'SBGLZY_SBDXJH_ADD'" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <!-- <v-btn color="primary" @click="btnClickEvet('')">{{ $t('GLOBAL._FZXZ') }}</v-btn> -->
                    <v-btn color="primary" v-has="'SBGLZY_SBDXJH_DC'" @click="handleExport">{{ $t('GLOBAL._DC') }}</v-btn>
                    <v-btn color="primary" v-has="'SBGLZY_SBDXJH_DR'" @click="handleImport()">{{ $t('GLOBAL._DR') }}</v-btn>
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    :clickFun="clickFun"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_SBDXJH"
                    :headers="OverhaulplanColum"
                    :desserts="desserts"
                    @selectePages="selectePages"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"
                ></Tables>
                <createRepast ref="createRepast" :dialogType="dialogType" :tableItem="tableItem"></createRepast>
            </v-card>
            <el-drawer size="80%" :title="rowtableItem.Year + ' | ' + rowtableItem.Month + ' | ' + rowtableItem.Line" :wrapperClosable="false" :visible.sync="detailShow" direction="rtl">
                <v-card class="ma-1">
                    <div class="form-btn-list">
                        <v-btn color="primary" v-has="'SBGLZY_SBDXJH_SCDXRW'" @click="btnClickEvet('add2')">{{ $t('TPM_SBGL_SBDXJH.scdxrw') }}</v-btn>
                    </div>
                    <Tables
                        :page-options="pageOptions"
                        :footer="false"
                        :showSelect="false"
                        :loading="loading2"
                        :btn-list="btnList2"
                        tableHeight="calc(100vh - 220px)"
                        table-name="TPM_SBGL_SBDXJH"
                        :headers="OverhaulTaskColum"
                        :desserts="desserts2"
                        @tableClick="tableClick2"
                    ></Tables>
                </v-card>
            </el-drawer>
        </div>
        <createRepast2 ref="createRepast2" :DevList="DevList" @loadData2="RepastInfoLogGetPage" :woData="tableItem" :dialogType="dialogType" :tableItem="tableItem2"></createRepast2>
        <div class="loading-box" v-if="importLoading">
            <a-spin tip="导入中..." :spinning="importLoading"></a-spin>
        </div>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';

import {
    GetOverhaulPlanPageList,
    GetEquipmentListByLevel,
    GetOverhaulWoGetList,
    GetOverhaulPlanImportData,
    GetOverhaulWoDelete,
    GetOverhaulWoSaveForm,
    GetOverhaulPlanDelete
} from '@/api/equipmentManagement/Overhaulplan.js';
import { configUrl } from '@/config';
import { EquipmentGetEquipmentTree, GetListByLevel } from '@/api/common.js';
import { GetExportData, GetPersonList } from '@/api/equipmentManagement/Equip.js';
import { GetDevicePageList } from '@/api/equipmentManagement/MyRepair.js';
import { OverhaulplanColum, OverhaulTaskColum } from '@/columns/equipmentManagement/Overhaulplan.js';
export default {
    name: 'RepastModel',
    components: {
        createRepast2: () => import('./components/createRepast2.vue'),
        createRepast: () => import('./components/createRepast.vue')
    },
    data() {
        return {
            detailShow: false,
            importLoading: false,
            tab: null,
            loading: false,
            showFrom: false,
            DevList: [],
            papamstree: {
                Line: '',
                Year: '',
                Month: '',
                Status: '',
                pageIndex: 1,
                pageSize: 20
            },
            OverhaulTaskColum,
            //查询条件
            OverhaulplanColum,
            desserts: [],
            loading2: false,
            desserts2: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            tableItem2: {},
            deleteList: [], //批量选中
            hasChildren: {}, // 新增字典详情判断-子节点才能新增
            //
            OverhaulLine: [],
            OverhaulYear: [],
            OverhaulMonth: [],
            OverhaulWoStatus: [],
            MaintenanceGroupData: [],

            rowtableItem: {},
            OverhaulBy: []
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._XG'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_SBGLZY_SBDXJH_EDIT'
                },
                {
                    text: this.$t('GLOBAL._FZXZ'),
                    code: 'copy',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_SBDXJH_COPYADD'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBGLZY_SBDXJH_DELETE'
                }
            ];
        },
        btnList2() {
            return [
                {
                    text: this.$t('GLOBAL._XG'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: ''
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: ''
                }
            ];
        },
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'Line',
                    label: this.$t('TPM_SBGL_SBDXJH._SCX'),
                    icon: 'mdi-account-check',
                    selectData: this.OverhaulLine,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Year',
                    label: this.$t('TPM_SBGL_SBDXJH._ND'),
                    icon: 'mdi-account-check',
                    selectData: this.OverhaulYear,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Month',
                    label: this.$t('TPM_SBGL_SBDXJH._YF'),
                    icon: 'mdi-account-check',
                    selectData: this.OverhaulMonth,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Status',
                    label: this.$t('TPM_SBGL_SBDXJH._ZT'),
                    icon: 'mdi-account-check',
                    selectData: this.OverhaulWoStatus,
                    type: 'select',
                    placeholder: ''
                }
            ];
        }
    },
    async mounted() {
        let MaintenanceGroup = await GetPersonList('MaintenanceGroup');
        this.MaintenanceGroupData = MaintenanceGroup.response[0].ChildNodes;
        this.MaintenanceGroupData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.$refs.createRepast2.addList[3].options = this.MaintenanceGroupData;
        let Overhaul = await GetPersonList('OverhaulBy');
        this.OverhaulBy = Overhaul.response[0].ChildNodes;
        this.OverhaulBy.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.OverhaulWoStatus = await this.$getNewDataDictionary('OverhaulPlanStatus');
        this.OverhaulYear = this.getTenYearsRange();
        this.OverhaulMonth = this.getMonth();
        console.log(this.OverhaulBy, 123);
        this.$refs.createRepast.addList[0].options = this.OverhaulYear;
        this.$refs.createRepast.addList[1].options = this.OverhaulMonth;
        this.$refs.createRepast.addList[6].options = this.OverhaulWoStatus;
        this.RepastInfoGetPage();
        this.GetFactorylineTree();
        this.getLine();
        this.MyGetDevicePageList();
    },
    methods: {
        async MyGetDevicePageList() {
            let res = await GetDevicePageList();
            this.DevList = res.response;
            console.log(this.DevList);
            this.DevList.forEach(item => {
                item.ItemName = item.LineCode + ':' + item.Name + ':' + item.AssetsNo;
                item.ItemValue = item.ID + '|' + item.Code + '|' + item.LineCode+ '|' + item.Name;
            });
            this.$refs.createRepast2.addList[0].options = this.DevList;
        },
        async getLine() {
            let params = {
                key: 'Line'
            };
            let res = await GetListByLevel(params);
            res.response.forEach(item => {
                item.ItemName = item.EquipmentName;
                item.ItemValue = item.EquipmentName;
            });
            this.OverhaulLine = res.response;
            this.$refs.createRepast.addList[2].options = this.OverhaulLine;
        },
        getMonth() {
            let arr = [];
            for (let i = 1; i <= 12; i++) {
                let obj = {
                    ItemValue: i,
                    ItemName: i
                };
                arr.push(obj);
            }
            return arr;
        },
        getTenYearsRange() {
            let year = new Date().getFullYear(); // 如果没有提供年份，则使用当前年份
            let startYear = year - 5;
            let endYear = year + 5;
            let years = [];
            for (let i = startYear; i <= endYear; i++) {
                let obj = {
                    ItemValue: i,
                    ItemName: i
                };
                years.push(obj);
            }
            return years;
        },
        async GetFactorylineTree() {},
        async handleExport() {
            let params = {
                ...this.papamstree
            };
            const baseUrl3 = configUrl[process.env.VUE_APP_SERVE]['baseURL_EQUIPMENT'] + `/api/OverhaulPlan/ExportData`;
            let res = await GetExportData(baseUrl3, params);
            let binaryData = [];
            binaryData.push(res);
            const url = window.URL.createObjectURL(new Blob(binaryData));
            console.log(url);
            const link = document.createElement('a');
            link.href = url;
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            let fileName = `设备大修计划${formattedDateTime}.xlsx`;
            document.body.appendChild(link);
            link.setAttribute('download', fileName);
            link.click();
            window.URL.revokeObjectURL(link.href);
        },
        handleImport() {
            let _this = this;
            let input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xls,.xlsx';
            input.click();
            let Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            input.onchange = async function () {
                let file = input.files[0];
                let formdata = new FormData();
                formdata.append('file', file);

                _this.importLoading = true;
                try {
                    let res = await GetOverhaulPlanImportData(formdata, Factory);
                    _this.$store.commit('SHOW_SNACKBAR', { text: res.response });
                    _this.RepastInfoGetPage();
                    _this.importLoading = false;
                } catch {
                    _this.importLoading = false;
                }
            };
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.RepastInfoGetPage();
        },

        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.createRepast.addList.forEach(item => {
                        item.value = '';
                    });
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'add2':
                    this.dialogType = val;
                    this.$refs.createRepast2.addList.forEach(item => {
                        item.value = '';
                    });
                    this.$refs.createRepast2.showDialog = true;
                    return;
                case 'delete':
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'warning' });
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.$refs.createRepast.getData(this.tableItem);
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'copy':
                    this.$refs.createRepast.getData(this.tableItem);
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        tableClick2(item, type) {
            this.dialogType = type;
            this.tableItem2 = item;
            switch (type) {
                case 'edit':
                    this.$refs.createRepast2.getData(this.tableItem2);
                    this.$refs.createRepast2.showDialog = true;
                    return;
                case 'delete':
                    this.deltable2();
                    return;
            }
        },
        async RepastInfoGetPage() {
            let params = {};
            for (let k in this.papamstree) {
                if (this.papamstree[k] != '') {
                    params[k] = this.papamstree[k];
                }
            }
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading = true;
            const res = await GetOverhaulPlanPageList(params);
            let { success, response } = res;
            response.data.forEach(item => {
                this.OverhaulWoStatus.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                        item.StatusValue = it.ItemValue;
                    }
                });
                this.MaintenanceGroupData.forEach(it => {
                    if (item.TaskBy == it.ItemValue) {
                        item.TaskBy = it.ItemName;
                        item.TaskByValue = it.ItemValue;
                    }
                });
            });
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        //  查看BOM详情
        clickFun(data) {
            this.tableItem = data;
            this.rowtableItem = data || {};
            this.detailShow = true;
            this.RepastInfoLogGetPage();
        },
        async RepastInfoLogGetPage() {
            if (!this.rowtableItem.ID) {
                return false;
            }
            let params = {
                OverhaulPlanId: this.rowtableItem.ID
            };
            this.loading2 = true;
            const res = await GetOverhaulWoGetList(params);
            let { success, response } = res;
            response.forEach(item => {
                this.OverhaulWoStatus.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                this.MaintenanceGroupData.forEach(it => {
                    if (item.TaskBy == it.ItemValue) {
                        item.TaskBy = it.ItemName;
                        item.TaskByValue = it.ItemValue;
                    }
                });
            });
            if (success) {
                this.loading2 = false;
                this.desserts2 = response || {} || [];
            }
        },
        // 删除
        deltable2() {
            let params = [this.tableItem2.ID];
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await GetOverhaulWoDelete(params);
                    if (res.success) {
                        this.tableItem2 = {};
                        this.RepastInfoLogGetPage();
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await GetOverhaulPlanDelete(params);
                    if (res.success) {
                        this.tableItem = {};
                        this.RepastInfoGetPage();
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}

.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

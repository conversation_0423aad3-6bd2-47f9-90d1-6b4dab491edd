<template>
    <div class="">
        <!-- <div class="d-flex">
            <h4 class="pa-2">备件仓位</h4>
            <v-btn class="ml-auto" color="" @click="showView = !showView">切换图表</v-btn>
        </div>
        <div v-if="showView">
            <v-row>
                <v-col cols="12" md="8" class="d-flex flex-wrap connet">
                    <v-card class="mx-auto my-2" width="224" v-for="i in 24" :key="i">
                        <v-img src="" height="120px"></v-img>
                        <div class="d-flex">
                            <v-card-title>A04-2</v-card-title>
                            <v-progress-circular model-value="20" color="blue-grey" :size="60" max="200"></v-progress-circular>
                        </div>
                        <v-card-actions>
                            <v-btn color="orange lighten-2" text>入库</v-btn>
                            <v-spacer></v-spacer>
                            <v-btn color="orange lighten-2" text>记录</v-btn>
                        </v-card-actions>
                    </v-card>
                </v-col>
                <v-col cols="12" md="4">
                    <v-form ref="fromQR" v-model="valid">
                        <v-row>
                            <v-col class="text-right">
                                <v-btn class="ml-2" color="primary" @click="getQRcodes">扫码入库</v-btn>
                            </v-col>
                            <v-col class="" cols="12">
                                <v-text-field v-model="fromQR.Remark" disabled outlined dense label="货架编号"></v-text-field>
                            </v-col>
                            <v-col class="" cols="12">
                                <v-text-field v-model="fromQR.Code" disabled outlined dense label="备件编号"></v-text-field>
                            </v-col>
                            <v-col class="" cols="12">
                                <v-text-field v-model="fromQR.BuyNum" type="Number" outlined dense label="备件数量"></v-text-field>
                            </v-col>
                            <v-col class="text-right">
                                <v-btn class="ml-2" color="primary" :disabled="!submitStatue" large fab @click="addSparePartlist">确认</v-btn>
                            </v-col>
                        </v-row>
                    </v-form>
                </v-col>
            </v-row>
        </div> -->
        <v-tabs>
            <v-tab>
                {{ $t('TPM_SBGL_SBBJGL._RKLB') }}
            </v-tab>
            <v-tab></v-tab>
            <v-tab-item>
                <v-card flat>
                    <v-row>
                        <v-col cols="12" md="8">
                            <Tables :page-options="pageOptions" :loading="loading" :btn-list="btnList"
                                tableHeight="calc(100vh - 200px)" table-name="TPM_SBGL_SBBJGL_RK"
                                :headers="sparePartColumQR" :desserts="desserts" @selectePages="selectePages"></Tables>
                        </v-col>
                        <v-col cols="12" md="4">
                            <v-form ref="fromQR" v-model="valid">
                                <v-row>
                                    <v-col class="text-right">
                                        <v-btn class="ml-2" v-has="'SBBJGL_RK_SMRK'" color="primary" @click="getQRcodes">{{
                                            $t('TPM_SBGL_SBBJGL._SMRK') }}</v-btn>
                                    </v-col>
                                    <v-col class="" cols="12">
                                        <v-text-field v-model="fromQR.Remark" disabled outlined dense
                                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_RK.Remark')"></v-text-field>
                                    </v-col>
                                    <v-col class="" cols="12">
                                        <v-text-field v-model="fromQR.Code" disabled outlined dense
                                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_RK.SparepartCode')"></v-text-field>
                                    </v-col>
                                    <v-col class="" cols="12">
                                        <v-text-field v-model="fromQR.BuyNum" type="Number" outlined dense
                                            :label="$t('TPM_SBGL_SBBJGL._BJSL')"></v-text-field>
                                    </v-col>
                                    <v-col class="text-right">
                                        <v-btn class="ml-2" color="primary" :disabled="!submitStatue" large fab
                                            @click="addSparePartlist">{{ $t('GLOBAL._QD') }}</v-btn>
                                    </v-col>
                                </v-row>
                            </v-form>
                        </v-col>
                    </v-row>
                </v-card>
                <createRepasts ref="createRepast"></createRepasts>
            </v-tab-item>
        </v-tabs>
        <QRcode ref="QRcode" @getQRcodesRes="getQRcodesRes"></QRcode>
    </div>
</template>
<script>
import { SparepartGetPageList, addSparePart, getInStorageData } from '@/api/equipmentManagement/sparePart.js';
import { sparePartColumQR } from '@/columns/equipmentManagement/sparePart.js';
export default {
    name: 'CreateRepasts',
    components: {
        createRepasts: () => import('./components/createRepasts.vue')
    },
    data() {
        return {
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            papamstree: {
                key: null,
                pageIndex: 1,
                pageSize: 20
            },
            valid: false,
            desserts: [],
            btnList: [],
            loading: false,
            showView: false,
            sparePartColumQR,
            dialogType: '',
            fromQR: {
                Remark: '', //货架编号
                Code: '', // 备件编号
                BuyNum: null //入库数量
            }
        };
    },
    computed: {
        submitStatue() {
            return (this.fromQR.Remark || this.fromQR.Remark) && +this.fromQR.BuyNum > 0;
        }
    },
    created() { },
    mounted() {
        this.RepastInfoGetPage();
    },
    methods: {
        // 扫码录入
        getQRcodes() {
            this.$refs.QRcode.getQRcode();
        },
        // 获取查询结果
        getQRcodesRes(value) {
            let val = JSON.parse(value.text);
            let { Remark, SparePartsCode } = val;
            this.fromQR.Remark = Remark;
            this.fromQR.Code = SparePartsCode;
        },
        QRCcodeckick() {
            this.$refs.createRepast.showDialog = true;
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        },
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                key: this.papamstree.key,
                pageIndex: this.papamstree.pageIndex,
                pageSize: this.papamstree.pageSize
            };
            this.loading = true;
            const res = await getInStorageData(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        // 扫码新增备件数量
        async addSparePartlist() {
            let params = {
                Type: 1,
                Remark: this.fromQR.Remark,
                Code: this.fromQR.Code,
                BuyNum: +this.fromQR.BuyNum
            };
            const res = await addSparePart(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '添加成功', color: 'success' });
                this.RepastInfoGetPage();
                this.$refs.fromQR.reset();
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.connet {
    height: calc(100vh - 160px);
    overflow: auto;

    .text-right {
        text-align: right;
    }
}
</style>
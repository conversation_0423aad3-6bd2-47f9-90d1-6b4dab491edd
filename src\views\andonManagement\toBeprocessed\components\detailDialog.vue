<template>
    <!-- 处理告警 -->
    <v-dialog v-model="dialog" persistent max-width="980px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ $t('ANDON_BJJL.details') }}--{{ operaObj.AreaName }} {{ operaObj.ProductLineName?' , ' + operaObj.ProductLineName: ''}} {{ operaObj.UnitName?' , ' + operaObj.UnitName: '' }}
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="mt-3 alarm-home-dd">
                <!-- <div class="mb-2 text-h6" style="color: black">{{ operaObj.MainAlarm }} {{ operaObj.SubAlarm?' -- ' + operaObj.SubAlarm: '' }}
                {{ operaObj.problemLevel?'(' + operaObj.problemLevel + ')': '' }}</div> -->
               <Tables
                    itemKey="ID"
                    :showSelect="false"
                    table-height="580px"
                    :loading="loading"
                    :headers="handleDeatailColumns"
                    :desserts="desserts"
                    :page-options="pageOptions"
                    table-name="ANDON_BJJL"
                    @selectePages="selectePages"
                ></Tables>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script>
import { AlarmRecordGetDetailListById } from '@/api/andonManagement/alarmRecord.js';
import { handleDeatailColumns } from '@/columns/andonManagement/alarmRecord.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            dialog: '',
            loading: false,
            handleDeatailColumns,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            }
        };
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal) {
                    this.pageIndex = 1
                    this.getdata()
                }
            },
            deep: true,
            immediate: true
        }
    },
    async created(){ },
    methods: {
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getdata();
        },
        async getdata() {
            const params = {
                Id: this.operaObj.ID,
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await AlarmRecordGetDetailListById(params);
            const { success, response } = res;
            if (success) {
                this.desserts = [];
                const { data, dataCount, page } = response;
                this.pageOptions.total = dataCount;
                this.pageOptions.page = page;
                data.map(e => {
                    const { MainEventId, ID, Total } = e;
                    const { MainAlarm, SubAlarm } = this.operaObj;
                    const times = MainEventId == ID?1: Total;
                    this.desserts.push({ ...e, MainAlarm, SubAlarm, times});
                });
            }
        },
        getDesserts(){

        },
        closeForm() {
            this.dialog = false;
        }
    }
};
</script>
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';


export function GetMeasureAccountPageList(data) {
    const api = '/api/MeasureAccount/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetMeasureAccountDelete(data) {
    const api = '/api/MeasureAccount/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetMeasureAccountImportData(data) {
    const api = '/api/MeasureAccount/ImportData';
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetMeasureAccountSaveForm(data) {
    const api = '/api/MeasureAccount/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureAccountCreateMeasureCalibrateWo(data) {
    const api = '/api/MeasureCalibrateWo/CreateMeasureCalibrateWo';
    return getRequestResources(baseURL, api, 'post', data);
}


//检验项目
export function GetMeasureCalibrateItemGetList(data) {
    const api = '/api/MeasureCalibrateItem/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetMeasureCalibrateItemDelete(data) {
    const api = '/api/MeasureCalibrateItem/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureCalibrateItemSaveForm(data) {
    const api = '/api/MeasureCalibrateItem/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureCalibrateItemUploadFile(data) {
    const api = '/api/MeasureCalibrateItem/UploadFiles';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureCalibrateItemGetFileUrl(data) {
    const api = '/api/MeasureCalibrateItem/GetFileUrl';
    return getRequestResources(baseURL, api, 'get', data);
}
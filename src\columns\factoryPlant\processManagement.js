export const basicInfoColum = [
    { text: '标准加basicInfoColum工时间', value: 'StandardTime', width: '210px', isEditCell: true },
    { text: '标准加工单位', value: 'StandardUom', width: '210px', isEditCell: true },
    { text: '准备时间', value: 'PrepareTime', width: '150px', isEditCell: true },
    { text: '运输时间', value: 'TransTime', width: '180px', isEditCell: true },
    { text: '用工人数', value: 'StandardPersonNum', width: '170px', isEditCell: true },
    { text: '设计产能', value: 'UnitCapacity', width: '150px', isEditCell: true },
    { text: '良品率', value: 'YieldRate', width: '150px', isEditCell: true },
    { text: '描述', value: 'Description', width: '150px' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '140px'
    }
];

export const deviceColum = [
    { text: '设备名称', value: 'EquipName', width: '150px', isEditCell: true },
    { text: '设备编码', value: 'EquipCode', width: '160px', isEditCell: true },
    // { text: '设备ID', value: 'EquipId', width: '140px' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '140px'
    }
];

export const fixturesColum = [
    { text: '料号', value: 'MaterialCode', width: '140px', dictionary: true },
    { text: '类型', value: 'FixturesType', width: '140px', dictionary: true },
    { text: '设备类型', value: 'DeviceType', width: '280px', dictionary: true },
    { text: '数量', value: 'Quantity', width: '140px' },
    { text: '备注', value: 'Remark', width: '140px' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '140px'
    }
];

export const materialColum = [
    { text: '工序编号', value: 'ProcCode', width: '140px' },
    { text: '投入产出', value: 'Inout', width: '160px', dictionary: true, isEditCell: true },
    { text: '物料编号', value: 'MaterialCode', width: '140px' },
    { text: '物料名称', value: 'MaterialName', width: '180px' },
    { text: '父级数量', value: 'ParentQuantity', width: '140px', isEditCell: true },
    { text: '父级单位', value: 'ParentUnit', width: '140px' },
    { text: '数量', value: 'Quantity', width: '180px', isEditCell: true },
    { text: '单位', value: 'Unit', width: '180px'},
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    { text: '备注', value: 'Remark', width: '140px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '140px'
    }
];

export const procFileDetailColum = [
    { text: '工厂', value: 'Factory', width: '140px' },
    { text: '工艺文件编号', value: 'FileNo', width: '170px' },
    { text: '工艺文件版本', value: 'FileVersion', width: '170px' },
    { text: '工艺文件名称', value: 'FileName', width: '160px', dictionary: true },
    // { text: '起始页码', value: 'StarPageno', width: '180px' },
    // { text: '截止页码', value: 'EndPageno', width: '180px' },
    { text: '当前页文件编号', value: 'Docid', width: '170px' },
    { text: '当前页具体版本', value: 'DocVersion', width: '170px' },
    { text: '当前页图号', value: 'DocCode', width: '180px' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '140px'
    }
];

export const paramColum = [
    { text: '名称', value: 'Name', width: '140px' },
    { text: '编码', value: 'Code', width: '140px' },
    { text: '标准值', value: 'Standard', width: '160px' },
    { text: '上限', value: 'UpperLimit', width: '140px' },
    { text: '下限', value: 'LowerLimit', width: '140px' },
    { text: '上上限', value: 'UpperUpperLimit', width: '140px' },
    { text: '下下限', value: 'LowerLowerLimit', width: '140px' },
    { text: '单位', value: 'Uom', width: '140px' },
    { text: '排序', value: 'SORT', width: '140px' },
    { text: '是否可用', value: 'Enabled', width: '140px', dictionary: true },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '140px'
    }
];

export const staffingColum = [
    // { text: '职责编码', value: 'DutyCode', width: '180px' },
    { text: '职责名称', value: 'DutyName', width: '140px' },
    { text: '人员技能', value: 'DutyType', width: '140px', dictionary: true },
    { text: '基准工时', value: 'TimeBase', width: '180px' },
    { text: '运行人数', value: 'RunNum', width: '140px' },
    { text: '设置人数', value: 'SetNum', width: '160px' },
    { text: '描述', value: 'Description', width: '180px' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '140px'
    }
];

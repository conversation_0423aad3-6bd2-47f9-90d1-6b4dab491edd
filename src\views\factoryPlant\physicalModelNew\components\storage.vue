<template>
  <div>
    <div class="content-box">
      <a-tabs style="margin-top:5px" type="card" size="small" @change="handleChangeTab" v-model="activeKey">
        <a-tab-pane v-for="tab in tabList" :key="tab" :tab="tab">
          <General v-if="tab === 'General'" ref="general" :EquipmentId="EquipmentId"/>
          <MaterialMapping v-if="tab === 'Material Mapping'" ref="materialMapping" :EquipmentId="EquipmentId"/>
          <Capacities v-if="tab === 'Capacities'" ref="capacities" :EquipmentId="EquipmentId"/>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script>
import General from './general.vue'
import MaterialMapping from './materialMapping.vue'
import Capacities from './capacities.vue'
export default {
  name: 'Storage',
  props:{
    EquipmentId:{
      type: String,
      default: ''
    }
  },
  components:{
    General,
    MaterialMapping,
    Capacities
  },
  data() {
    return {
      tabList:  ['General', 'Material Mapping', 'Capacities'],
      activeKey: 'General'
    }
  },
  mounted() {
    
  },
  methods: {
    handleChangeTab() { }
  }
}
</script>

<style lang="scss" scoped></style>
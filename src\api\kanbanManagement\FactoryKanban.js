import { getRequestResources } from '@/api/fetch';
const SIM = 'baseURL_KPI',  QMS = 'baseURL_QMS', ANDON = 'baseURL_ANDON'
// 获取费用看板
export function FindMeterialTotalCost(data) {
    const api = '/kpiapi/FactoryBoard/FindMeterialTotalCost'
    return getRequestResources(SIM, api, 'post', data)
}

// 获取费用看板成本统计TOP10
export function FindMaterialPcsCount(data) {
    const api = '/kpiapi/FactoryBoard/FindMaterialPcsCount'
    return getRequestResources(SIM, api, 'post', data)
}

// 获取材料房信息
export function FindStorageCountBoard(data) {
    const api = '/kpiapi/FactoryBoard/FindStorageCountBoard'
    return getRequestResources(SIM, api, 'post', data)
}

// 获取工厂看板信息
export function getFactoryBoardInfo(data) {
    const api = '/kpiapi/FactoryBoard/FindFactoryBoardByDate'
    return getRequestResources(SIM, api, 'post', data)
}
// 实际产量数据
export function FindCalCountByLineAndType(data) {
    const api = '/kpiapi/FactoryBoard/FindCalCountByLineAndType'
    return getRequestResources(SIM, api, 'post', data)
}

// 获取安灯记录信息
export function getAndonInfo(data) {
    const api = '/andon/AlarmRecord/FindHotAndonRecord'
    return getRequestResources(ANDON, api, 'post', data)
}

// 获取安灯统计信息信息
export function getAndonCount(data) {
    const api = '/andon/AlarmRecord/FindAndonCount'
    return getRequestResources(ANDON, api, 'post', data)
}

// 查询QMS客诉信息
export function getCustomerComplaintData(data) {
    const api = '/backend-api/workflow/customerComplain/getCustomerComplainStatistics'
    return getRequestResources(QMS, api, 'get', data)
}

// 获取费用成本二级页面数据
export function FindMaterialCostDetail(data) {
    const api = '/kpiapi/FactoryBoard/FindMaterialCostDetail'
    return getRequestResources(SIM, api, 'post', data)
}

// 获取材料房二级页面数据
export function FindStorageMaterialBoard(data) {
    const api = '/kpiapi/FactoryBoard/FindStorageMaterialBoard'
    return getRequestResources(SIM, api, 'post', data)
}

// 获取订单总量二级页面数据
export function FindFindWoCountBoard(data) {
    const api = '/kpiapi/FactoryBoard/FindFindWoCountBoard'
    return getRequestResources(SIM, api, 'post', data)
}

// 获取产量二级页面数据
export function FindYieldCountBoard(data) {
    const api = '/kpiapi/FactoryBoard/FindYieldCountBoard'
    return getRequestResources(SIM, api, 'post', data)
}
// 获取良品入库二级页面数据
export function FindInstockCountBoard(data) {
    const api = '/kpiapi/FactoryBoard/FindInstockCountBoard'
    return getRequestResources(SIM, api, 'post', data)
}

// 获取一次良率二级页面数据
export function FindRateCountBoard(data) {
    const api = '/kpiapi/FactoryBoard/FindRateCountBoard'
    return getRequestResources(SIM, api, 'post', data)
}
// 获取入库良率二级页面数据
export function FindInstockYieldCountBoard(data) {
    const api = '/kpiapi/FactoryBoard/FindInstockYieldCountBoard'
    return getRequestResources(SIM, api, 'post', data)
}


// 获取OEE二级页面数据
export function FindOEECountBoard(data) {
    const api = '/kpiapi/FactoryBoard/FindOEECountBoard'
    return getRequestResources(SIM, api, 'post', data)
}
// 获取OPE二级页面数据
export function FindOPECountBoard(data) {
    const api = '/kpiapi/FactoryBoard/FindOPECountBoard'
    return getRequestResources(SIM, api, 'post', data)
}
// 获取出勤二级页面数据
export function FindADACountBoard(data) {
    const api = '/kpiapi/FactoryBoard/FindADACountBoard'
    return getRequestResources(SIM, api, 'post', data)
}
// 获取工效二级页面数据
export function FindWEECountBoard(data) {
    const api = '/kpiapi/FactoryBoard/FindWEECountBoard'
    return getRequestResources(SIM, api, 'post', data)
}
// 主动获取andon信息
export function SendFactoryBoardInfo() {
    const api = '/andon/AlarmRecord/SendFactoryBoardInfo'
    return getRequestResources('baseURL_ANDON', api, 'get')
}
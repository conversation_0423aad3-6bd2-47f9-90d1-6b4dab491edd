<template>
  <el-dialog :title="dialogForm.ID ? '编辑' : '新增'" :visible.sync="dialogVisible" width="600px"
    :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
    @close="dialogVisible = false">
    <el-form :rules="rules" ref="dialogForm" :model="dialogForm" label-width="110px">
      <el-form-item label="站点编码" prop="Code">
        <el-input v-model="dialogForm.Code"  placeholder="" />
      </el-form-item>
      <el-form-item label="站点名称" prop="Name">
        <el-input v-model="dialogForm.Name"  placeholder="" />
      </el-form-item>
      <el-form-item label="采集类型" prop="Type">
        <el-select style="width: 100%" v-model="dialogForm.Type" placeholder="请选择">
          <el-option v-for="(item, index) in typeOptions" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="URL" prop="Url">
        <el-input v-model="dialogForm.Url"  placeholder="" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input type="textarea" v-model="dialogForm.Remarks" placeholder="" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogVisible = false">取 消</el-button>
      <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small"
        @click="submit()">确定
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { influxOpcServerSaveForm } from "@/api/factoryPlant/communicationStation.js";
export default {
  name: 'Dialog',
  data() {
    return {
      dialogForm: {},
      dialogVisible: false,
      formLoading: false,
      typeOptions: [{
        label: 'OPCDA',
        value: 0
      }
      // , {
      //   label: 'OPCUA',
      //   value: 1
      // }
    ],
      rules: {
        Code: [
          { required: true, message: '请输入站点编码', trigger: 'blur' }
        ],
        Name: [
          { required: true, message: '请输入站点名称', trigger: 'blur' }
        ],
        Type: [
          { required: true, message: '请选择采集类型', trigger: 'change' }
        ],
        Url: [
          { required: true, message: '请输入URL', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
  },
  methods: {
    submit() {
      this.$refs.dialogForm.validate( async (valid) => {
        if (valid) {
          const { msg } = await influxOpcServerSaveForm(this.dialogForm)
          this.$message.success(msg)
          this.$emit('SaveForm')
          this.dialogVisible = false
        }
      });
    },
    show(data) {
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.dialogForm = {
          ...data
        }
        this.$refs.dialogForm.resetFields()
      })
    }
  }
}
</script>

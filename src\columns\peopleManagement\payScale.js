// 
export const payScaleColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '引入',
        value: 'FullName',
        width: 280,
        sortable: true
    },
    {
        text: '产品线',
        value: 'Line',
        width: 120,
        sortable: true
    },
    {
        text: '声学电磁产品事业部',
        value: 'Deparment',
        width: 150,
        sortable: true
    },
    {
        text: '职称',
        value: 'JobTitle',
        width: 100,
        sortable: true
    },
    {
        text: '上班天数',
        value: 'ActualWorkDays',
        width: 100,
        sortable: true
    },
    {
        text: '班制（10/11）',
        value: 'ShiftType',
        width: 150,
        sortable: true
    },
    {
        text: '工作日天数',
        value: 'WorkDays',
        width: 120,
        sortable: true
    },
    {
        text: '休息日加班天数',
        value: 'OvertimeDays',
        width: 130,
        sortable: true
    },
    {
        text: '基本工资标准',
        value: 'WageStandard',
        width: 120,
        sortable: true
    },
    {
        text: '基本工资',
        value: 'WageBasic',
        width: 100,
        sortable: true
    },
    {
        text: '加班工资',
        value: 'WageOvertime',
        width: 100,
        sortable: true
    },
    {
        text: '工时工资',
        value: 'WageHour',
        width: 100,
        sortable: true
    },
    {
        text: '工时日工资（每日）',
        value: 'WageWorkingDay',
        width: 160,
        sortable: true
    },
    {
        text: '全勤奖',
        value: 'WageAttendance',
        width: 100,
        sortable: true
    },
    {
        text: '夜班津贴',
        value: 'AllowanceNight',
        width: 110,
        sortable: true
    },
    {
        text: '伙食补贴',
        value: 'AllowanceMeal',
        width: 110,
        sortable: true
    },
    {
        text: '平均技能工资',
        value: 'WageSkill',
        width: 120,
        sortable: true
    },
    {
        text: '津补贴日工资',
        value: 'AllowanceDay',
        width: 120,
        sortable: true
    },
    {
        text: '社保',
        value: 'SocialSecurity',
        width: 100,
        sortable: true
    },
    {
        text: '公积金',
        value: 'AccumulationFund',
        width: 100,
        sortable: true
    },
    {
        text: '社保公积金（每日）',
        value: 'AscAfAvgDay',
        width: 150,
        sortable: true
    },
    {
        text: '技能津补贴社保分成',
        value: 'JtSbDay',
        width: 150,
        sortable: true
    },
    {
        text: '日人工成本',
        value: 'LaborCost',
        width: 110,
        sortable: true
    },
    { text: '操作',  width: 150, align: 'center', value: 'actions', sortable: true }
]
export const pieceBasisColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '产品线',
        value: 'Line',
        width: 120,
        sortable: true
    },
    {
        text: '物料号',
        value: 'MaterielCode',
        width: 150,
        sortable: true
    },
    {
        text: '物料描述',
        value: 'MaterielDescription',
        width: 220,
        sortable: true
    },
    {
        text: '工段',
        value: 'SubsectionOne',
        width: 120,
        sortable: true
    },
    {
        text: '班制',
        value: 'ShiftSystem',
        width: 120,
        sortable: true
    },
    {
        text: '工序',
        value: 'SubsectionTwo',
        width: 140,
        sortable: true
    },
    {
        text: '岗位名称',
        value: 'JobContent',
        width: 120,
        sortable: true
    },
    {
        text: '部门分类',
        value: 'Department',
        width: 130,
        sortable: true
    },
    {
        text: '配置岗位职称',
        value: 'JobTitle',
        width: 120,
        sortable: true
    },
    {
        text: '阶段目标（pcs/班/机）',
        value: 'StageTarget',
        width: 180,
        sortable: true
    },
    {
        text: '人机比',
        value: 'ManMachineRatio',
        width: 100,
        sortable: true
    },
    {
        text: '计件基准产能',
        value: 'BasicOutput',
        width: 120,
        sortable: true
    },
    {
        text: '目标PPM（生产&工艺制定，质量复核）',
        value: 'TargetPpm',
        width: 250,
        sortable: true
    },
    {
        text: '基准工资',
        value: 'BasicWage',
        width: 100,
        sortable: true
    },
    {
        text: '基准单价（基准工资/目标产出）',
        value: 'BasicPrice',
        width: 210,
        sortable: true
    },
    {
        text: '日津贴标准',
        value: 'AllowanceDay',
        width: 120,
        sortable: true
    },
    { text: '操作',  width: 150, align: 'center', value: 'actions', sortable: true }
]
export const pieceRateColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '最小百分比',
        value: 'MinPercent',
        width: 120,
        sortable: true
    },
    {
        text: '最大百分比',
        value: 'MaxPercent',
        width: 150,
        sortable: true
    },
    {
        text: '津贴占比%',
        value: 'AllowancePercent',
        width: 100,
        sortable: true
    },
    { text: '操作',  width: 150, align: 'center', value: 'actions', sortable: true }
]
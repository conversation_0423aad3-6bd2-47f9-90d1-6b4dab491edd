import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_MATERIAL'

// 列表
export function getMaterialGroupList(data) {
  const api = '/api/MaterialGroup/GetPageList'
  return getRequestResources(baseURL, api, 'post', data);
}

// 新增
export function materialGroupSaveForm(data) {
  const api = '/api/MaterialGroup/SaveForm'
  return getRequestResources(baseURL, api, 'post', data);
}

// 删除
export function materialGroupDelete(data) {
  const api = '/api/MaterialGroup/Delete'
  return getRequestResources(baseURL, api, 'post', data);
}

// 明细列表
export function getMaterialGroupMappingList(data) {
  const api = '/api/MaterialGroupMapping/GetPageList'
  return getRequestResources(baseURL, api, 'post', data);
}


export function getMaterialGroupPropertyList(data) {
  const api = '/api/BasePropertyValue/GetMaterialGroupPropertyList'
  return getRequestResources(baseURL, api, 'post', data);
}
export function SaveMaterialGroupPropertyList(data) {
  const api = '/api/BasePropertyValue/SaveMaterialGroupPropertyList'
  return getRequestResources(baseURL, api, 'post', data);
}

// 明细新增
export function materialGroupMappingSaveForm(data) {
  const api = '/api/MaterialGroupMapping/SaveForm'
  return getRequestResources(baseURL, api, 'post', data);
}

// 明细删除
export function materialGroupMappingDelete(data) {
  const api = '/api/MaterialGroupMapping/Delete'
  return getRequestResources(baseURL, api, 'post', data);
}

// 查询公共数据字典
export function getDataDictionary(data) {
  const api = '/api/DataItemDetail/GetPageList'
  return getRequestResources(baseURL, api, 'post', data)
}

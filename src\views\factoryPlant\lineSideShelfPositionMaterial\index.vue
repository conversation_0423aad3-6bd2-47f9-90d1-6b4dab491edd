// 线边货架位物料
<template>
    <div class="line-side-view">
        <TreeView :items="treeData" :title="$t('DFM_XBHJWL.bindInfo')" @clickClassTree="clickTree"></TreeView>
        <div class="line-side-main overflow-auto">
            <SearchForm ref="contactTorm" :searchinput="searchinput" :show-from="showFrom" @searchForm="searchForm" />
            <v-card outlined>
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        <!-- 搜索栏 -->
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary">
                        <v-icon @click="getDataList">mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" @click="operaClick({})">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="primary" @click="deleteItems()">{{ $t('GLOBAL._PLSC') }}</v-btn>
                </div>
                <Tables :headers="headers" :desserts="desserts" :loading="loading" :page-options="pageOptions"
                    :btn-list="btnList" :click-fun="selectedPath" :current-select-id="currentSelectId"
                    table-name="DFM_XBHJWL" @selectePages="selectePages" @itemSelected="selectedItems"
                    @toggleSelectAll="selectedItems" @tableClick="tableClick"></Tables>
            </v-card>
            <update-dialog ref="updateDialog" :current-selec-bin-id="currentSelecBinId" :opera-obj="operaObj"
                @handlePopup="handlePopup"></update-dialog>
        </div>
    </div>
</template>
<script>
import { getRackingTree, GetRackingBinMaterialPageList, DeleteRackingBinMaterial } from '@/api/factoryPlant/sideLine.js';
import { lineSideShelfPositionMaterial } from '@/columns/factoryPlant/lineSideShelfPosition.js';
export default {
    name: 'ReasonDetail',
    components: {
        UpdateDialog: () => import('./components/updateDialog.vue')
    },
    data() {
        return {
            operaObj: {},
            showFrom: false,
            deleteId: '',
            // tree 字典数据
            treeData: [],
            reasonDetailId: '',
            currentSelectId: '',
            currentSelecBinId: '',
            headers: lineSideShelfPositionMaterial,
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            selectedList: [],
            desserts: [],
            searchParams: {},
            MaterialCode: {}
        };
    },
    computed: {
        //查询条件
        searchinput() {
            return [
                // 物料代码
                {
                    key: 'key',
                    value: '',
                    icon: '',
                    label: this.$t('$vuetify.dataTable.DFM_XBHJWL.MaterialCode')
                }
            ];
        },
        btnList() {
            return [
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary' },
                { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'red' }
            ];
        }
    },
    mounted() {
        this.getTreeList();
        this.getDataList();
    },
    methods: {
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        //点击表格行
        selectedPath(o) {
            const { BinId, ID } = o;
            this.currentSelecBinId = BinId;
            this.currentSelectId = ID;
            // this.getDataList();
        },
        // 操作栏按钮
        tableClick(item, type) {
            switch (type) {
                // 编辑
                case 'edit':
                    this.operaClick(item);
                    break;
                // 删除
                case 'delete':
                    this.deleteId = item.ID;
                    this.sureDelete();
                    break;
                default:
                    break;
            }
        },
        // 获取全部表格数据
        async getDataList() {
            this.loading = true;
            let params = {
                ...this.searchParams,
                BinId: this.currentSelecBinId,
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await GetRackingBinMaterialPageList(params);
            const { success, response } = res || {};
            const { data, dataCount, page } = response || {};
            if (success) {
                this.desserts = data;
                this.pageOptions.total = dataCount;
                this.pageOptions.page = page;
                // this.pageOptions.pageCount = pageCount;
                // this.pageOptions.pageSize = pageSize;
            } else {
                this.desserts = [];
            }
            this.loading = false;
        },
        // 获取树型
        async getTreeList() {
            const res = await getRackingTree({});
            const { success, response } = res;
            if (success) {
                this.treeData = response;
            } else {
                this.treeData = [];
            }
        },
        // 点击树状传回数值
        clickTree(v) {
            // 切换查询清空
            this.currentSelecBinId = v.id;
            this.currentSelectId = '';
            this.searchinput.forEach(element => {
                element.value = '';
            });
            this.pageOptions.pageCount = 1;
            this.getDataList();
        },
        // 查询数据
        searchForm(v) {
            this.searchParams = v;
            this.getDataList();
        },
        // 新增/编辑物料
        operaClick(o) {
            if (this.currentSelecBinId || o.BinId) {
                this.operaObj = o;
                this.$refs.updateDialog.updateDialog = true;
            } else {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('DFM_XBHJWL.selectePosition'), color: 'error' });
            }
        },
        // 批量删除
        async deleteItems() {
            if (this.selectedList.length > 0) {
                this.deleteId = '';
                this.sureDelete();
            } else {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'error' });
            }
        },
        // 确认删除
        sureDelete() {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            }).then(async () => {
                const params = [];
                if (this.deleteId) {
                    params.push(this.deleteId);
                } else {
                    this.selectedList.forEach(e => {
                        params.push(e.ID);
                    });
                }
                const res = await DeleteRackingBinMaterial(params);
                this.selectedList = [];
                this.deleteId = '';
                const { success, msg } = res;
                if (success) {
                    this.pageOptions.pageCount = 1;
                    this.getDataList();
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                }
            });
        },
        // 根据子组件返回来值
        handlePopup() {
            this.getDataList();
        }
    }
};
</script>
<style lang="scss" scoped>
.line-side-view {
    display: flex;

    .line-side-main {
        flex: 1;
        width: 100%;

        .v-data-table {
            width: 100%;
        }
    }
}
</style>

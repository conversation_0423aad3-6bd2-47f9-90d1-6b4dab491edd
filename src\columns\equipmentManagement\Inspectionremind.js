export const InspectionremindColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},{
    text: '资产码',
    value: 'AccountAssetNo',
    Namevalue: "AccountAssetNo",
    width: 150,
    sortable: true
}, {
    text: '部门内部编号',
    value: 'MeasureNo',
    Namevalue: "bmnbbh",
    width: 150,
    sortable: true
}, {
    text: '计量器具名称',
    value: 'Name',
    Namevalue: "jlqjmc",
    width: 150,
    sortable: true
}, {
    text: '规格',
    value: 'Model',
    Namevalue: "gg",
    width: 100,
    sortable: true
}, {
    text: '检定方法',
    value: 'VerifyMethod',
    Namevalue: "jdff",
    width: 150,
    sortable: true
}, {
    text: '调校日期',
    value: 'CalibrateDate',
    Namevalue: "tjrq",
    width: 150,
    sortable: true
}, {
    text: '有效期',
    value: 'ExpirationDate',
    Namevalue: "yxq",
    width: 150,
    sortable: true
}, {
    text: '检定部门',
    value: 'VerifyDepartment',
    Namevalue: "jdbm",
    width: 150,
    sortable: true
}, {
    text: '使用部门',
    value: 'UsingDepartment',
    Namevalue: "sybm",
    width: 150,
    sortable: true
}, {
    text: '分部门',
    value: 'SubDepartment',
    Namevalue: "fbm",
    width: 150,
    sortable: true
}, {
    text: '放置地点',
    value: 'StorageLocation',
    Namevalue: "fzdd",
    width: 150,
    sortable: true
}, {
    text: 'ABC分类',
    value: 'MeasureType',
    Namevalue: "abcfl",
    width: 150,
    sortable: true
}]
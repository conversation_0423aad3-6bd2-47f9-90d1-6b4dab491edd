<template>
    <div class="detail-module">
        <Tables table-name="DFM_GYLX" ref="table" table-height="320" :headers="operationalPathDetail"
            @selectePages="selectePages" @tableClick="tableClick" :loading="loading" :desserts="detailData"
            :pageOptions="pageData" />
    </div>
</template>

<script>
import { operationalPathDetail } from '@/columns/factoryPlant/tableHeaders';
import { getDetailList, deleteDetail } from '../service';
export default {
    props: {
        currentSelectId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            loading: false,
            operationalPathDetail,
            pageData: {
                pageSize: 20,
                page: 1,
                total: 0,
                pageCount: 0,
                pageSizeitems: ['10', '20', '30', '40']
            },
            detailData: []
        };
    },
    created() {
        this.getdata();
    },
    methods: {
        getSelected(items) {
            this.selecteds = items;
        },
        batchDelete() {
            let selecteds = this.$refs.table.selected;
            if (selecteds.length === 0) {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
                return false;
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let ids = [];
                    selecteds.forEach(item => {
                        ids.push(item.ID);
                    });
                    let resp = await deleteDetail(ids);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
                    this.getdata();
                })
                .catch(() => { });
        },
        tableClick(item, type) {
            switch (type) {
                case 'edit':
                    this.editDetail(item);
                    break;
                case 'delete':
                    this.deleteDetail(item);
                    break;
            }
        },
        deleteDetail(data) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let resp = await deleteDetail([data.ID]);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
                    this.getdata();
                })
                .catch(() => { });
        },
        editDetail(item) {
            this.$emit('handlePopup', true, 'detail', item);
        },
        selectePages(data) {
            this.pageData.page = data.pageCount;
            this.pageData.pageSize = data.pageSize;
            this.getdata();
        },
        async getdata() {
            if (!this.currentSelectId) return;
            this.loading = true;
            try {
                let resp = await getDetailList({ routingId: this.currentSelectId, page: this.pageData.page, intPageSize: this.pageData.pageSize });
                this.loading = false;
                this.$refs.table.selected = [];
                this.detailData = resp.response.data;
                this.pageData.total = resp.response.dataCount;
                this.pageData.pageCount = resp.response.pageCount;
            } catch {
                this.loading = false;
            }
        }
    }
};
</script>

<style>

</style>
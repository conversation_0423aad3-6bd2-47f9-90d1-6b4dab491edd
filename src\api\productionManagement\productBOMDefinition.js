import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_TRACE'
// 产品bom定义列表
export function getSapSegmentMaterialList(data) {
    const api =  '/api/SapSegmentMaterial/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 产品物料明细 
export function getSapSegmentMaterialStepList(data) {
  const api =  '/api/SapSegmentMaterialStep/GetList'
  return getRequestResources(baseURL, api, 'post', data);
}
// 新增保存 
export function saveSapSegmentMaterial(data) {
  const api =  '/api/SapSegmentMaterial/SaveForm'
  return getRequestResources(baseURL, api, 'post', data);
}

// 删除 
export function deleteSapSegmentMaterial(data) {
  const api =  '/api/SapSegmentMaterial/Delete'
  return getRequestResources(baseURL, api, 'post', data);
}
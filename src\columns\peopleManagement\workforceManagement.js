export const workforceColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '员工名称',
        value: 'CompanyName',
        width: 120,
        sortable: true
    },
    {
        text: '员工号',
        value: 'SectionName',
        width: 100,
        sortable: true
    },
    {
        text: '岗位',
        value: 'WoCode',
        width: 100,
        sortable: true
    },
    {
        text: '技能',
        value: 'MaterialCode',
        width: 150,
        sortable: true
    },
    {
        text: '班次',
        value: 'MaterialDescription',
        width: 140,
        sortable: true
    },
    {
        text: '开始时间',
        value: 'PlanStartTime',
        // width: 150,
        sortable: true
    },
    {
        text: '结束时间',
        value: 'PlanEndTime',
        // width: 150,
        sortable: true
    },
    { text: '操作', align: 'center', value: 'actions', sortable: true }
];

// 操作人员信息
export const personalColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '楼层号',
        value: 'Code',
        width: 100,
        sortable: true
    },
    {
        text: '楼层名称',
        value: 'Name',
        width: 100,
        sortable: true
    },
    // {
    //     text: '备注',
    //     value: 'Remark',
    //     width: 150,
    //     sortable: true
    // },
    { text: '操作',width: 100, align: 'center', value: 'actions', sortable: true }
   
];
// 展示人员信息
export const personalsColums = [
    {
        text: '序号',
        value: 'Index',
        width: 80,
        sortable: true
    },
    {
        text: '工厂',
        value: 'Factory',
        width: 120,
        sortable: true
    },
    {
        text: '部门',
        value: 'Departement',
        width: 120,
        sortable: true
    },
    {
        text: '员工号',
        value: 'Code',
        width: 100,
        sortable: true
    },
    {
        text: '姓名',
        value: 'Name',
        width: 100,
        sortable: true
    },
    {
        text: '岗位',
        value: 'PostNames',
        width: 240,
        sortable: true
    },
    {
        text: '物料号',
        value: 'MaterialCode',
        width: 120,
        sortable: true
    },
    // {
    //     text: '线体号',
    //     value: 'PostNames',
    //     width: 240,
    //     sortable: true
    // },
    {
        text: '职称',
        value: 'JobTitleId',
        width: 120,
        sortable: true
    },
    {
        text: '星级',
        value: 'StarLevelId',
        width: 100,
        sortable: true
    },
    {
        text: '班组',
        value: 'ShiftGroupName',
        width: 100,
        sortable: true
    },
    {
        text: '工段',
        value: 'WorkshopSectionName',
        width: 360,
        sortable: true
    },
    {
        text: '飞书',
        value: 'Feishu',
        width: 140,
        sortable: true
    },
    {
        text: '微信',
        value: 'Wechat',
        width: 140,
        sortable: true
    },
    {
        text: '手机',
        value: 'Phone',
        width: 140,
        sortable: true
    }
];
// 楼层信息
export const shiftColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        label: '名称',
        prop: 'name'
    },
    {
        label: '编码',
        prop: 'code'
    },
    {
        label: '排序',
        prop: 'sort'
    },
    {
        label: '操作',
        type: 'template',
        prop: 'actions',
        template: 'actions'
    }
];

<!--
 * @Descripttion: (配置工单批次管理)
 * @version: (1.0)
 * @Author: (SECI)
 * @Date: (2025-05-22)
 * @LastEditors: (SECI)
 * @LastEditTime: (2025-05-22)
-->

<template>
  <div class="root">
    <div class="root-head">
      <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
        <el-form-item label="计划编号" class="mb-2">
          <el-input clearable v-model="searchForm.OrderNo" placeholder="请输入计划编号" />
        </el-form-item>
        <el-form-item label="产品名称" class="mb-2">
          <el-input clearable v-model="searchForm.MaterialName" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="产线" class="mb-2">
          <el-input clearable v-model="searchForm.lineCode" placeholder="请输入产线" />
        </el-form-item>
        <el-form-item label="计划时间" class="mb-2">
          <el-date-picker v-model="searchForm.StartWorkday" type="datetime" placeholder="选择开始时间"></el-date-picker>
          ~
          <el-date-picker v-model="searchForm.FinishWorkday" type="datetime" placeholder="选择结束时间"></el-date-picker>
        </el-form-item>
        <el-form-item class="mb-2">
          <el-button icon="el-icon-search" @click="getSearchBtn()">{{ $t('GLOBAL._CX') }}</el-button>
        </el-form-item>
        
      </el-form>
    </div>
    <div class="root-main">
      <el-table class="mt-3"
                :height="mainH"
                border
                :data="tableData"
                style="width: 100%">
        <el-table-column prop="operation" width="160" :label="$t('GLOBAL._ACTIONS')" align="center">
          <template slot-scope="scope">
            <div class="combination">
              <el-button size="mini" type="text" @click="poConsumeListDialog(scope.row)">{{ $t('GLOBAL._CONSUME') }}</el-button>
              <el-button size="mini" type="text" @click="poProducedListDialog(scope.row)">{{ $t('GLOBAL._PRODUCE') }}</el-button>
            </div>                        
          </template>
        </el-table-column>
        <el-table-column v-for="(item) in tableName"
                         :default-sort="{prop: 'date', order: 'descending'}"
                         :key="item.ID"
                         :prop="item.field"
                         :label="item.label"
                         :width="item.width"
                         :align="item.alignType"
                         sortable
                         show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row[item.field] }}
          </template>
        </el-table-column>

      </el-table>
    </div>
    <div class="root-footer">
      <el-pagination
          class="mt-3"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchForm.pageIndex"
          :page-sizes="[10,20,50,100,500]"
          :page-size="searchForm.pageSize"
          layout="->,total, sizes, prev, pager, next, jumper"
          :total="total"
          background
      ></el-pagination>
    </div>
    <form-dialog @saveForm="getSearchBtn" ref="formDialog"></form-dialog>
    <POList ref="poList" />
  </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';
import FormDialog from './form-dialog'
import POList from './poListDetail.vue'
import {delWeekSchedule, getPOPageList,cancelProductionOrder} from "@/api/planManagement/weekSchedule";
import {getTableHead} from "@/util/dataDictionary.js";

export default {
  name: 'index',
  components: {
    FormDialog,
    POList
  },
  data() {
    return {
      searchForm: {
        pageIndex: 1,
        pageSize: 20,
      },
      total: 0,
      tableData: [{}],
      hansObj: this.$t('WeekFormulationReport.table'),
      tableName: [],
      loading: false,
      tableOption: [
        {code: 'LineCode', width: 150, align: 'left'},
        {code: 'SegmentCode', width: 150, align: 'left'},
        {code: 'ProductionOrderNo', width: 180, align: 'left'},
        {code: 'WeekScheduleOrderNo', width: 180, align: 'left'},
        {code: 'MaterialCode', width: 150, align: 'left'},
        {code: 'MaterialName', width: 180, align: 'left'},
        {code: 'PlanDate', width: 180, align: 'center'},
        {code: 'PlanStartTime', width: 180, align: 'center'},
        {code: 'PlanEndTime', width: 180, align: 'center'},
        {code: 'PlanQty', width: 150, align: 'right'},
        {code: 'StartTime', width: 180, align: 'center'},
        {code: 'EndTime', width: 180, align: 'center'},
        {code: 'PoStatus', width: 150, align: 'center'},
        {code: 'ReleaseStatus', width: 150, align: 'center'},
        {code: 'ProduceStatus', width: 150, align: 'center'},
        {code: 'Remark', width: 150, align: 'left'},
      ],
      mainH: 0,
      buttonOption:{
        name:'配置周计划',
        serveIp:'baseURL_PPM',
        uploadUrl:'/ppm/WeekSchedule/ImportData', //导入
        //exportUrl:'/ppm/WeekSchedule/ExportData', //导出
        DownLoadUrl:'/ppm/WeekSchedule/DownLoadTemplate', //下载模板
      }
    }
  },
  mounted() {
    this.getZHHans()
    this.getTableData()
    this.$nextTick(() => {
      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)
    })
    window.onresize = () => {
      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)
    }
  },
  methods: {
    getZHHans() {
      this.tableName = getTableHead(this.hansObj, this.tableOption)
    },
    poConsumeListDialog(row) {
      this.$refs.poList.show(row)
    },
    poProducedListDialog(row) {
      this.$refs.poList.show(row)
    },
    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },
    handleSizeChange(size) {
      this.searchForm.pageSize = size
      this.getTableData()
    },
    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },    
    getTableData(data) {
      this.searchForm.Category = "Formulation"
      //getWeekScheduleList
      getPOPageList(this.searchForm).then(res => {
        //console.log(res);
        this.tableData = res.response.data
        this.total = res.response.dataCount
      })
    },
  }
}

</script>

<style lang="scss" scoped>
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}

.mt-8p {
  margin-top: 8px;
}

.pd-left {
  padding-left: 5px
}
</style>
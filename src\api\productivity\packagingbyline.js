import { getRequestResources } from '@/api/fetch';
const baseURL_30015 = 'baseURL_30015'
const DFM = 'baseURL_DFM'

//物料模型
export function GetEquipmentListByLevel(data) {
    const api = '/api/Equipment/GetEquipmentTree';
    return getRequestResources(DFM, api, 'post', data);
}
//Line新增
export function GetLosstgtControllerLineSaveForm(data) {
    const api = '/api/LosstgtControllerLine/SaveForm'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Line删除
export function GetLosstgtControllerLineDelete(data) {
    const api = '/api/LosstgtControllerLine/Delete'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Line列表
export function GetLosstgtControllerLinePageList(data) {
    const api = '/api/LosstgtControllerLine/GetPageList'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Line导入
export function GetLosstgtControllerLineImportData(data) {
    const api = '/api/LosstgtControllerLine/ImportData'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
import request from '@/util/request';

export function ExportData(url, data) {
    return request({
        url: url,
        method: 'post',
        data,
        responseType: 'blob'
    });
}
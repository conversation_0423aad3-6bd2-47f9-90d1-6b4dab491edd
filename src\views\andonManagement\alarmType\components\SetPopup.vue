<template>
    <div>
        <v-dialog v-model="Setdialog" persistent max-width="980px">
            <v-card>
                <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                    {{ $t('GLOBAL._SET') }}
                    <v-icon @click="closePopup">mdi-close</v-icon>
                </v-card-title>
                <div class="EquipmentSearch">
                    <div class="addForm">
                        <v-text-field v-model="key" outlined dense :label="$t('GLOBAL._KEY')"></v-text-field>
                    </div>
                    <div class="addForm">
                        <v-btn color="primary" @click="getTableData()">{{ $t('GLOBAL._CX') }}</v-btn>
                    </div>
                    <div class="addForm">
                        <v-btn color="primary" @click="AddNew()">{{ $t('GLOBAL._XZ') }}</v-btn>
                    </div>
                </div>
                <Tables
                    :page-options="pageOptions"
                    :showSelect="false"
                    :loading="loading"
                    ref="BjTables"
                    :btn-list="btnList"
                    tableHeight="300px"
                    table-name="ANDON_SET"
                    @tableClick="tableClick"
                    :headers="alarmSetColumns"
                    :desserts="desserts"
                    @selectePages="selectePages"
                ></Tables>
                <v-divider></v-divider>
            </v-card>
        </v-dialog>
        <v-dialog v-model="addModel" persistent width="30%">
            <v-card>
                <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                    {{ addType == 'add' ? $t('GLOBAL._XZ') : $t('GLOBAL._BJ') }}
                    <v-icon @click="addModel = false">mdi-close</v-icon>
                </v-card-title>
                <div class="addForm" style="margin: 30px 30px 0 30px">
                    <v-text-field v-model="PropertyCode" :disabled="addType == 'edit'" outlined dense :label="$t('$vuetify.dataTable.ANDON_SET.PropertyCode')"></v-text-field>
                </div>
                <div class="addForm" style="margin: 0 30px">
                    <v-text-field v-model="Remark" outlined dense :label="$t('$vuetify.dataTable.ANDON_SET.Remark')"></v-text-field>
                </div>
                <div class="addForm" style="margin: 0 30px">
                    <v-text-field v-model="PropertyValue" outlined dense :label="$t('$vuetify.dataTable.ANDON_SET.PropertyValue')"></v-text-field>
                </div>
                <v-divider></v-divider>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <el-button @click="addModel = false">取 消</el-button>
                    <el-button type="primary" @click="Save()">确 定</el-button>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <v-dialog v-model="dialogExperienceVisible" persistent max-width="40%">
            <v-card>
                <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                    {{ $t('ANDON_BJZY.JYK') }}
                    <v-icon @click="dialogExperienceVisible = false">mdi-close</v-icon>
                </v-card-title>
                <div class="EquipmentSearch">
                    <div class="addForm">
                        <v-text-field v-model="SolutionDesc" outlined dense :label="$t('$vuetify.dataTable.ExperienceLibrary.czfa')"></v-text-field>
                    </div>
                    <div class="addForm">
                        <v-btn color="primary" @click="getExperienceTableData()">{{ $t('GLOBAL._CX') }}</v-btn>
                    </div>
                    <div class="addForm">
                        <v-btn color="primary" @click="AddExperienceNew()">{{ $t('GLOBAL._XZ') }}</v-btn>
                    </div>
                </div>
                <Tables
                    :showSelect="false"
                    ref="BjTables"
                    :btn-list="btnExperienceList"
                    @tableClick="tableExperienceClick"
                    tableHeight="300px"
                    table-name="ExperienceLibrary"
                    :headers="ExperienceColumns"
                    :desserts="Experiencedesserts"
                    :footer="false"
                ></Tables>
                <v-divider></v-divider>
            </v-card>
        </v-dialog>
        <v-dialog v-model="addExperienceModel" persistent width="30%">
            <v-card>
                <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                    {{ addExperienceType == 'add' ? $t('GLOBAL._XZ') : $t('GLOBAL._BJ') }}
                    <v-icon @click="addExperienceModel = false">mdi-close</v-icon>
                </v-card-title>
                <div class="addForm" style="margin: 30px 30px 0 30px">
                    <v-text-field v-model="addForm.SolutionDesc" outlined dense :label="$t('$vuetify.dataTable.ExperienceLibrary.czfa')"></v-text-field>
                </div>
                <div class="addForm" style="margin: 0 30px">
                    <v-text-field type="number" disabled v-model="addForm.AdoptionCount" outlined dense :label="$t('$vuetify.dataTable.ExperienceLibrary.cycs')"></v-text-field>
                </div>
                <v-divider></v-divider>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <el-button @click="addExperienceModel = false">取 消</el-button>
                    <el-button type="primary" @click="SaveExperience()">确 定</el-button>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>
<script>
import { Message, MessageBox } from 'element-ui';
import { alarmSetColumns } from '@/columns/andonManagement/alarmType.js';
import {
    AlarmSolutionDelete,
    AlarmSolutionSaveForm,
    AlarmSolutionInsert,
    GetAlarmTypePropertyPageList,
    AlarmTypePropertyValueSaveForm,
    AlarmTypePropertyValueDelete
} from '@/api/andonManagement/alarmType.js';
import { ExperienceColumns } from '@/columns/andonManagement/alarmHome.js';
import { GetAlarmExperienceList } from '@/api/andonManagement/alarmHome.js';

export default {
    props: {},
    data() {
        return {
            addExperienceType: 'add',
            addExperienceModel: false,
            addForm: {
                SolutionDesc: '',
                AdoptionCount: 0
            },
            SolutionDesc: '',
            dialogExperienceVisible: false,
            Experiencedesserts: [],
            ExperienceColumns: ExperienceColumns,
            addType: '',
            addModel: false,
            PropertyCode: '',
            Remark: '',
            PropertyValue: '',
            alarmSetColumns,
            loading: false,
            desserts: [],
            key: '',
            CheckRow: {},
            tableItem: {},
            Setdialog: false,
            papamstree: {
                alarmTypeId: '',
                key: '',
                pageIndex: 1,
                pageSize: 20
            },
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            }
        };
    },
    computed: {
        btnList() {
            return [
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary' },
                { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'red', emptyKey: 'ClassId' }
            ];
        },
        btnExperienceList() {
            return [
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary' },
                { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'red', emptyKey: 'ClassId' }
            ];
        }
    },
    methods: {
        async SaveExperience() {
            let params = {
                ...this.addForm
            };
            params.AlarmCode = this.CheckRow.AlarmCode;
            let res;
            if (this.addExperienceType == 'edit') {
                params.ID = this.tableItem.ID;
                res = await AlarmSolutionSaveForm(params);
            } else {
                params.ID = null;
                res = await AlarmSolutionInsert(params);
            }
            this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
            this.getExperienceTableData();
            this.addExperienceModel = false;
        },
        AddExperienceNew() {
            this.addExperienceType = 'add';
            this.addForm.SolutionDesc = '';
            this.addForm.AdoptionCount = 0;
            this.addExperienceModel = true;
        },
        tableExperienceClick(item, type) {
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.addExperienceType = 'edit';
                    this.addForm.SolutionDesc = this.tableItem.SolutionDesc;
                    this.addForm.AdoptionCount = Number(this.tableItem.AdoptionCount);
                    this.addExperienceModel = true;
                    return;
                case 'delete':
                    this.delExperiencetable();
                    return;
            }
        },
        // 删除
        delExperiencetable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await AlarmSolutionDelete(params);
                    if (res.success) {
                        this.tableItem = {};
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.getExperienceTableData();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        tableClick(item, type) {
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.addType = 'edit';
                    this.PropertyCode = this.tableItem.PropertyCode;
                    this.Remark = this.tableItem.Remark;
                    this.PropertyValue = this.tableItem.PropertyValue;
                    this.addModel = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await AlarmTypePropertyValueDelete(params);
                    if (res.success) {
                        this.tableItem = {};
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.getTableData();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        AddNew() {
            this.PropertyCode = '';
            this.Remark = '';
            this.PropertyValue = '';
            this.addType = 'add';
            this.addModel = true;
        },
        async Save() {
            let params = {
                alarmTypeId: this.CheckRow.ID,
                PropertyCode: this.PropertyCode,
                Remark: this.Remark,
                PropertyValue: this.PropertyValue,
                ClassId: this.tableItem.ClassId
            };
            if (this.addType == 'edit') {
                params.ID = this.tableItem.ID;
            } else {
                params.ID = null;
            }
            let res = await AlarmTypePropertyValueSaveForm(params);
            this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
            this.getTableData();
            this.addModel = false;
        },
        closePopup() {
            this.Setdialog = false;
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.getTableData();
        },
        async getExperienceTableData(v) {
            if (v) {
                this.CheckRow = v;
            }
            let params = {
                ...this.papamstree
            };
            params.SolutionDesc = this.SolutionDesc;
            params.AlarmTypeCode = this.CheckRow.AlarmCode;
            let res = await GetAlarmExperienceList(params);
            let { success, response } = res;
            if (success) {
                this.Experiencedesserts = response || {} || [];
            }
        },
        async getTableData(v) {
            if (v) {
                this.CheckRow = v;
            }
            let params = {
                ...this.papamstree
            };
            params.alarmTypeId = this.CheckRow.ID;
            params.key = this.key;
            let res = await GetAlarmTypePropertyPageList(params);
            let { success, response } = res;
            if (success) {
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.EquipmentSearch {
    display: flex;
    margin: 10px 0 0 10px;
    .addForm {
        margin-right: 5px;
    }
}
</style>

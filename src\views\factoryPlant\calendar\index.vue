<!-- eslint-disable vue/valid-v-slot -->
<template>
    <div class="calendar">
        <TreeView :title="$t('DFM_RL._GSXX')" :items="departmentData" @clickClassTree="getTreeNode" />
        <div class="main">
            <v-row class="search-row py-1 mt-2 px-2">
                <v-col :cols="12" :lg="4" class="pt-0 pb-0 d-flex">
                    <v-menu ref="menu1" v-model="menu1" :close-on-content-click="false" transition="scale-transition"
                        offset-y max-width="290px" min-width="290px">
                        <template #activator="{ on, attrs }">
                            <v-text-field v-model="searchDate" :label="$t('DFM_RL._RQ')" persistent-hint outlined dense
                                v-bind="attrs" v-on="on"></v-text-field>
                        </template>
                        <v-date-picker v-model="searchDate" :locale="locale" type="month" no-title
                            @input="menu1 = false">
                        </v-date-picker>
                    </v-menu>
                    <v-btn color="primary" class="ml-3" @click="search()">{{ $t('GLOBAL._CX') }}</v-btn>
                </v-col>
                <v-col :cols="12" :lg="8" class="pt-0 pb-0 text-right">
                    <v-btn icon color="primary" :disabled="isDisabled">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn class="ml-3" :disabled="isDisabled" @click="setCalendar()">{{ $t('DFM_RL._SZRL') }}</v-btn>
                    <v-btn class="ml-3" :disabled="isDisabled" @click="checkAll()">{{ $t('DFM_RL._QX') }}</v-btn>
                    <v-btn class="ml-3" :disabled="isDisabled" @click="delCalendar()">{{ $t('DFM_RL._SCRL') }}</v-btn>
                    <v-menu offset-y left>
                        <template #activator="{ on, attrs }">
                            <v-btn class="ml-3" v-bind="attrs" :disabled="isDisabled" v-on="on">{{ $t('DFM_RL._GD') }}
                            </v-btn>
                        </template>
                        <v-list>
                            <v-list-item v-for="(item, index) in items" :key="index" @click="handleMore(index)">
                                <v-list-item-title>{{ item }}</v-list-item-title>
                            </v-list-item>
                        </v-list>
                    </v-menu>
                </v-col>
            </v-row>
            <div class="calendar-content">
                <v-toolbar flat color="white">
                    <v-btn outlined class="mr-4" color="grey darken-2" @click="setToday">{{ $t('DFM_RL._DQY') }}</v-btn>
                    <v-btn fab text small color="grey darken-2" @click="prev">
                        <v-icon small>mdi-chevron-left</v-icon>
                    </v-btn>
                    <v-btn fab text small color="grey darken-2" @click="next">
                        <v-icon small>mdi-chevron-right</v-icon>
                    </v-btn>
                    <v-toolbar-title v-if="$refs.calendar">
                        {{ $refs.calendar.title }}
                    </v-toolbar-title>
                </v-toolbar>

                <v-calendar ref="calendar" v-model="focus" :locale="locale" color="primary" :events="events"
                    :type="type" @change="getEvents" @click:more="viewDay" @click:date="viewDay">
                    <template #day="{ date }">
                        <div v-if="calendarShowArr(date).length" class="date-item">
                            <div v-for="item in calendarShowArr(date)" :key="item.ID" class="date-item-inner">
                                <div class="check-and-classes">
                                    <v-checkbox v-model="isCheckeds[date + item.ID]" class="mt-0 pt-0 pl-1"
                                        @change="changeCheck(item, isCheckeds[date + item.ID])"></v-checkbox>
                                    <p class="mb-0">{{ showClassName(item.Shiftid) }}：{{ showTeamName(item.Teamid) }}
                                    </p>
                                </div>
                                <p class="pl-2 mb-0">时间：{{ item.Starttime.substr(11, 5) + '-' + item.Endtime.substr(11,
                                        5)
                                }}</p>
                            </div>
                        </div>
                    </template>
                </v-calendar>
            </div>
        </div>
        <!-- 设置日历 -->
        <v-dialog v-model="isShowSetPopup" scrollable persistent width="55%">
            <SetCalendar v-if="isShowSetPopup" :current-deparment="currentDeparment" @getdata="getdata"
                @handlePopup="handlePopup" />
        </v-dialog>

        <!-- 设置每天日历 -->
        <v-dialog v-model="isShowSetDayPopup" scrollable persistent width="55%">
            <SetDay v-if="isShowSetDayPopup" :current-deparment="currentDeparment" :edit-day="editDay"
                :edit-day-data="editDayData" @getdata="getdata" @closePopup="closePopup"></SetDay>
        </v-dialog>

        <!-- 班次管理 -->
        <v-dialog v-model="isShowClassesPopup" scrollable width="55%">
            <ClassesPopup v-if="isShowClassesPopup" @handlePopup="handlePopup" />
        </v-dialog>
        <!-- 班组管理 -->
        <v-dialog v-model="isShowTeamPopup" scrollable width="55%">
            <TeamPopup :currentDeparment="currentDeparment" v-if="isShowTeamPopup" @handlePopup="handlePopup" />
        </v-dialog>
        <!-- 运转模式管理 -->
        <v-dialog v-model="isShowModePopup" scrollable width="55%">
            <OperatingMode v-if="isShowModePopup" @handlePopup="handlePopup" />
        </v-dialog>
    </div>
</template>

<script>
import { getDepartment, getCalendarData, delCalendar, getTeamList, getClassesList } from './service.js';
import SetCalendar from './components/setCalendar.vue';
import ClassesPopup from './components/classesPopup.vue';
import TeamPopup from './components/teamPopup.vue';
import OperatingMode from './components/operatingMode.vue';
import SetDay from './components/setDay.vue';
export default {
    components: {
        SetCalendar,
        ClassesPopup,
        TeamPopup,
        OperatingMode,
        SetDay
    },
    provide() {
        return {
            currentDeparment: () => {
                return { id: this.currentDeparment.id, name: this.currentDeparment.name };
            }
        };
    },
    data() {
        return {
            classList: [],
            teamList: [],
            // items: ['班次管理', '班组管理', '运转模式'],
            searchDate: '',
            menu1: false,
            focus: '',
            type: 'month',
            selectedEvent: {},
            selectedElement: null,
            selectedOpen: false,
            events: [],
            departmentData: [],
            isShowSetPopup: false,
            isShowClassesPopup: false,
            isShowTeamPopup: false,
            isShowModePopup: false,
            isShowSetDayPopup: false,
            currentDeparment: {},
            currentMonth: '',
            calendarData: [],
            isCheckeds: {},
            selected: [],
            editDayData: [],
            editDay: ''
        };
    },
    computed: {
        items() {
            return [this.$t('DFM_RL._BCGL'), this.$t('DFM_RL._BZGL'), this.$t('DFM_RL._YZMS')];
        },
        locale() {
            return this.$store.state.app.locale || 'zh';
        },
        isDisabled() {
            return !(this.currentDeparment && this.currentDeparment.id);
        }
    },
    created() {
        this.getClassList();
        this.getTeamList();
        this.getDepartmentData();
    },
    mounted() {
        this.$refs.calendar.checkChange();
    },
    methods: {
        async getClassList() {
            let resp = await getClassesList({ key: '' });
            this.classList = resp.response;
        },
        showClassName(id) {
            let classes = this.classList.find(item => item.ID === id);
            return classes ? classes.Name : '';
        },
        showTeamName(id) {
            let team = this.teamList.find(item => item.ID === id);
            return team ? team.Name : '';
        },
        async getTeamList() {
            let resp = await getTeamList({ key: '' });
            this.teamList = resp.response;
        },
        closePopup() {
            this.isShowSetDayPopup = false;
        },
        search() {
            this.focus = this.searchDate + '-01';
        },
        checkAll() {
            let flag = false;
            for (const key in this.isCheckeds) {
                this.$set(this.isCheckeds, key, !this.isCheckeds[key]);
                if (this.isCheckeds[key]) {
                    flag = true;
                }
                this.$forceUpdate();
            }
            if (flag) {
                this.calendarData.forEach(item => {
                    this.selected.push(item.ID);
                });
            } else {
                this.selected = [];
            }
        },
        calendarShowArr(date) {
            if (!this.calendarData.length) {
                return '';
            }
            let arr = [];
            arr = this.calendarData.filter(item => {
                let current = item.Starttime.substr(0, 10);
                return current == date;
            });

            return arr;
        },
        changeCheck(item, type) {
            if (type) {
                this.selected.push(item.ID);
            } else {
                this.selected = this.selected.filter(id => id !== item.ID);
            }
        },
        delCalendar() {
            if (!this.selected.length) {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'error' });
                return false;
            }
            this.$confirms({
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    await delCalendar(this.selected);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
                    this.selected = [];
                    this.getdata();
                })
                .catch(() => { });
        },
        handlePopup(boolean, type) {
            switch (type) {
                case 'set':
                    this.isShowSetPopup = boolean;
                    break;
                case 'classes':
                    this.isShowClassesPopup = boolean;
                    break;
                case 'team':
                    this.isShowTeamPopup = boolean;
                    break;
                case 'mode':
                    this.isShowModePopup = boolean;
                    break;
            }
        },
        setCalendar() {
            this.isShowSetPopup = true;
        },
        async getdata() {
            let resp = await getCalendarData({ MODELID: this.currentDeparment.id, months: this.currentMonth });
            this.calendarData = resp.response;
            this.calendarData.forEach(item => {
                this.isCheckeds[item.Starttime.substr(0, 10) + item.ID] = false;
                this.$forceUpdate();
            });
        },
        getTreeNode(node) {
            this.currentDeparment = node;
            this.getdata();
        },
        async getDepartmentData() {
            let resp = await getDepartment();
            this.departmentData = resp.response;
        },
        handleMore(ind) {
            switch (ind) {
                case 0:
                    this.isShowClassesPopup = true;
                    break;
                case 1:
                    this.isShowTeamPopup = true;
                    break;
                case 2:
                    this.isShowModePopup = true;
                    break;
            }
        },
        viewDay({ date }) {
            if (!this.calendarData.length) return false;
            this.editDay = date;
            this.editDayData = this.calendarData.filter(item => {
                let current = item.Starttime.substr(0, 10);
                return current == date;
            });
            if (date.substr(0, 7) == this.currentMonth) {
                this.isShowSetDayPopup = true;
            }
        },
        getEventColor(event) {
            return event.color;
        },
        setToday() {
            this.focus = '';
        },
        prev() {
            this.$refs.calendar.prev();
        },
        next() {
            this.$refs.calendar.next();
        },
        getEvents({ start, end }) {
            this.currentMonth = start.date.substr(0, 7);
            if (this.currentDeparment.id) {
                this.getdata();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.calendar {
    display: flex;
}

.main {
    width: 100%;
    // height: 100%;
    border: 1px solid rgba($color: #000000, $alpha: 0.1);
    // overflow-y: scroll;
}

.calendar-content {
    width: 100%;

    // height: 100%;
    .v-calendar-monthly.v-calendar-weekly.v-calendar.theme--light {
        min-height: 400px;
    }
}

.date-item {
    // display: flex;
    // flex-direction: column;
    // align-items: center;
    font-size: 0;
    transform: translateY(-5px);

    // .date-item-inner:hover {
    //     background: #99ffcc;
    // }
    .check-and-classes {
        display: flex;
        align-items: center;
    }

    .v-input--checkbox {
        ::v-deep(.v-input__control) {
            .v-messages.theme--light {
                display: none;
            }

            .v-input__slot {
                margin-bottom: 0;

                .v-input--selection-controls__input {
                    margin-right: 0;
                }

                .v-icon {
                    font-size: 18px;
                }
            }
        }
    }

    p,
    span {
        font-size: 12px;
    }
}
</style>

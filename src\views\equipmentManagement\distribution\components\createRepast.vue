<template>
    <v-dialog v-model="showDialog" max-width="1080px">
        <v-card v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                <!-- 设备录入 -->
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form" v-model="valid">
                    <v-card class="ma-1" outlined>
                        <v-card-title class="">{{ $t('TPM_SBGL_SBTZGL._SBXX') }}</v-card-title>
                        <v-card-text>
                            <v-row class="pt-8">
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3" v-for="(item, index) in SbxxList" :key="index">
                                    <v-text-field v-if="item.type == 'input'" :id="item.id + 'SbxxList'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                                    <v-autocomplete
                                        v-if="item.type == 'select'"
                                        :id="item.id + 'SbxxList'"
                                        clearable
                                        v-model="item.value"
                                        :items="item.options"
                                        item-text="ItemName"
                                        item-value="ItemValue"
                                        :label="item.label"
                                        clear
                                        dense
                                        outlined
                                    ></v-autocomplete>
                                    <el-date-picker v-if="item.type == 'date'" :placeholder="item.label" v-model="item.value" :id="item.id + 'SbxxList'" :type="item.datetype"></el-date-picker>
                                </v-col>
                            </v-row>
                        </v-card-text>
                    </v-card>
                    <v-card class="ma-1" outlined>
                        <v-card-title class="">{{ $t('TPM_SBGL_SBTZGL._SBSM') }}</v-card-title>
                        <v-card-text>
                            <v-row class="pt-8">
                                <v-col class="py-0 px-3" cols="12" sm="3" md="3" v-for="(item, index) in SbsmList" :key="index">
                                    <v-text-field v-if="item.type == 'input'" :id="item.id + 'SbsmList'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                                    <v-autocomplete
                                        v-if="item.type == 'select'"
                                        :id="item.id + 'SbsmList'"
                                        clearable
                                        v-model="item.value"
                                        :items="item.options"
                                        item-text="ItemName"
                                        item-value="ItemValue"
                                        :label="item.label"
                                        clear
                                        dense
                                        outlined
                                    ></v-autocomplete>
                                    <v-menu
                                        v-if="item.type == 'date' || item.type == 'datetime'"
                                        :ref="'menu' + index"
                                        v-model="menu[index]"
                                        :close-on-content-click="false"
                                        :nudge-right="40"
                                        transition="scale-transition"
                                        offset-y
                                        max-width="290px"
                                        min-width="290px"
                                    >
                                        <template #activator="{ on, attrs }">
                                            <v-text-field
                                                v-model="item.value"
                                                :clearable="item.isClearable ? item.isClearable : true"
                                                outlined
                                                dense
                                                :label="item.label"
                                                readonly
                                                v-bind="attrs"
                                                v-on="on"
                                            ></v-text-field>
                                        </template>
                                        <v-date-picker :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></v-date-picker>
                                    </v-menu>
                                </v-col>
                            </v-row>
                        </v-card-text>
                    </v-card>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
export default {
    data() {
        return {
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            menu: [],
            SbxxList: [
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipCode'),
                    value: '',
                    id: 'EquipCode',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Name'),
                    value: '',
                    id: 'Name',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.SubName'),
                    value: '',
                    id: 'SubName',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Equipnature'),
                    value: '',
                    option: [],
                    id: 'Equipnature',
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipType'),
                    value: '',
                    option: [],
                    id: 'EquipType',
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipGroup'),
                    value: '',
                    option: [],
                    id: 'EquipGroup',
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.SuperiorEquit'),
                    value: '',
                    option: [],
                    id: 'SuperiorEquit',
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Factory'),
                    value: '',
                    id: 'Factory',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Specmodel'),
                    value: '',
                    id: 'Specmodel',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Department'),
                    value: '',
                    option: [],
                    id: 'Department',
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Area'),
                    value: '',
                    option: [],
                    id: 'Area',
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Group'),
                    value: '',
                    option: [],
                    id: 'Group',
                    type: 'select'
                }
            ],
            SbsmList: [
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Accountingmethod'),
                    value: '',
                    option: [],
                    id: 'Accountingmethod',
                    type: 'select'
                },
                // {
                //     label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.WorkCenter'),
                //     value: '',
                //     id: 'WorkCenter',
                //     type: 'input'
                // },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.CostCenter'),
                    value: '',
                    id: 'CostCenter',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Assetnumber'),
                    value: '',
                    id: 'Assetnumber',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.AssetName'),
                    value: '',
                    id: 'AssetName',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.OldCode'),
                    value: '',
                    id: 'OldCode',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.serialnumber'),
                    value: '',
                    id: 'serialnumber',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Licenseplatenumber'),
                    value: '',
                    id: 'Licenseplatenumber',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Factorycode'),
                    value: '',
                    id: 'Factorycode',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipItem'),
                    value: '',
                    id: 'EquipItem',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.brand'),
                    value: '',
                    id: 'brand',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.isImported'),
                    value: '',
                    option: [],
                    id: 'isImported',
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.manufacturer'),
                    value: '',
                    id: 'manufacturer',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.supplier'),
                    value: '',
                    id: 'supplier',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.producer'),
                    value: '',
                    id: 'producer',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipSize'),
                    value: '',
                    id: 'EquipSize',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipWeight'),
                    value: '',
                    id: 'EquipWeight',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.FixLimit'),
                    value: '',
                    id: 'FixLimit',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Manufacturingdate'),
                    value: '',
                    id: 'Manufacturingdate',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.dateproduction'),
                    value: '',
                    id: 'dateproduction',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.useYear'),
                    value: '',
                    id: 'useYear',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Expectedscrapdate'),
                    value: '',
                    id: 'Expectedscrapdate',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Scrapdate'),
                    value: '',
                    id: 'Scrapdate',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipValue'),
                    value: '',
                    id: 'EquipValue',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Number'),
                    value: '',
                    id: 'Number',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Unit'),
                    value: '',
                    option: [],
                    id: 'Unit',
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Keyequipmentstatistics'),
                    value: '',
                    id: 'Keyequipmentstatistics',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Status'),
                    value: '',
                    option: [],
                    id: 'Status',
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.UseStatus'),
                    value: '',
                    option: [],
                    id: 'UseStatus',
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.UseStatusTag'),
                    value: '',
                    id: 'UseStatusTag',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.factoryDate'),
                    value: '',
                    id: 'factoryDate',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.TestDate'),
                    value: '',
                    id: 'TestDate',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.IncomeDate'),
                    value: '',
                    id: 'IncomeDate',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Storagelocation'),
                    value: '',
                    id: 'Storagelocation',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.position'),
                    value: '',
                    id: 'position',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Nowposition'),
                    value: '',
                    id: 'Nowposition',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Insidelocation'),
                    value: '',
                    id: 'Insidelocation',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Inventorysituation'),
                    value: '',
                    id: 'Inventorysituation',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Firstmaintenancetime'),
                    value: '',
                    id: 'Firstmaintenancetime',
                    type: 'datetime'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.personresponsible'),
                    value: '',
                    id: 'personresponsible',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.equipmentSupervisor'),
                    value: '',
                    id: 'equipmentSupervisor',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.LastFirstmaintenancetime'),
                    value: '',
                    id: 'LastFirstmaintenancetime',
                    type: 'datetime'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Comment'),
                    value: '',
                    id: 'Comment',
                    type: 'input'
                }
            ]
        };
    },
    computed: {
        locale() {
            return this.$store.state.app.locale || 'zh';
        }
    },
    watch: {},
    methods: {
        closeDatePicker(index) {
            this.$set(this.menu, index, false);
        }
    }
};
</script>

<style lang="scss" scoped>
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
}

.ant-calendar-picker {
    ::v-deep input {
        border-color: #9e9e9e;
    }

    ::v-deep i {
        color: #757575;
    }
}
</style>

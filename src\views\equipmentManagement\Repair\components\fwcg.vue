<template>
    <div>
        <Tables
            :page-options="pageOptions"
            ref="Tables"
            :footer="false"
            :showSelect="false"
            :loading="loading"
            :btn-list="btnList"
            :clickFun="clickFun"
            tableHeight="calc(50vh - 50px) !important"
            table-name="TPM_SBGL_SBWXGD"
            :headers="fwcgtopColum"
            :desserts="desserts"
        ></Tables>

        <Tables
            :page-options="pageOptions"
            ref="recordTable"
            :footer="false"
            :showSelect="false"
            :loading="loading2"
            :btn-list="[]"
            tableHeight="calc(50vh - 50px) !important"
            table-name="TPM_SBGL_SBWXGD"
            :headers="fwcgdownColum"
            :desserts="desserts2"
        ></Tables>
    </div>
</template>

<script>
import { GetRepairServiceGetList, GetRepairAcceptanceItem } from '@/api/equipmentManagement/NewRepair.js';
import { fwcgtopColum, fwcgdownColum } from '@/columns/equipmentManagement/Repair.js';
export default {
    components: {},
    data() {
        return {
            fwcgtopColum,
            loading: false,
            loading2: false,
            desserts: [],
            desserts2: [],
            fwcgdownColum,
            tableItem: {},
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            StatusList: []
        };
    },
    computed: {
        btnList() {
            return [];
        }
    },
    async created() {
        this.StatusList = await this.$getNewDataDictionary('RepairServiceStatus');
    },
    methods: {
        //  查看BOM详情
        clickFun(data) {
            this.tableItem = data;
            this.$refs.Tables.selected = [data];
            this.MyGetRepairAcceptanceItem();
        },
        async MyGetRepairServiceGetList(row) {
            let params = {
                Factory: this.$route.query.Factory ? this.$route.query.Factory : '2010',
                RepairWo: ''
            };
            if (row) {
                params.RepairWo = row.RepairWo;
            }
            this.loading = true;
            let res = await GetRepairServiceGetList(params);
            let mydata = res.response;
            for (let k in mydata) {
                let item = mydata[k];
                item.RequestByName = await this.$getPerson(item.RequestBy);
                this.StatusList.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
            }
            this.desserts = res.response;
            this.loading = false;
        },
        async MyGetRepairAcceptanceItem() {
            let params = {
                SupplierServiceId: this.tableItem.ID
            };
            this.loading2 = true;
            let res = await GetRepairAcceptanceItem(params);
            this.desserts2 = res.response;
            this.loading2 = false;
        }
    }
};
</script>

<style></style>

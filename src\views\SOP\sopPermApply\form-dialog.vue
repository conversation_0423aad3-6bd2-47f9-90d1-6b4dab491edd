<template>
    <el-dialog :title="dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')" :visible.sync="dialogVisible" width="700px"
      :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
      @close="dialogVisible = false">
      <el-form ref="dialogForm" :model="dialogForm" label-width="130px">
       

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="主键">{{dialogForm.id}}</el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="申请人ID" prop="applicantId">
              <el-select v-model="dialogForm.applicantId" placeholder="请选择申请人ID">
                <el-option v-for="item in  applicantIdOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="申请对象ID" prop="targetId">
              <el-select v-model="dialogForm.targetId" placeholder="请选择申请对象ID">
                <el-option v-for="item in  targetIdOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="对象类型(1-目录 2-文档)" prop="targetType">
              <el-select v-model="dialogForm.targetType" placeholder="请选择对象类型(1-目录 2-文档)">
                <el-option v-for="item in  targetTypeOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="申请权限级别" prop="requestPerm">
              <el-select v-model="dialogForm.requestPerm" placeholder="请选择申请权限级别">
                <el-option v-for="item in  requestPermOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="审批状态(0-待处理 1-通过 2-拒绝)" prop="approveStatus">
              <el-select v-model="dialogForm.approveStatus" placeholder="请选择审批状态(0-待处理 1-通过 2-拒绝)">
                <el-option v-for="item in  approveStatusOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="审批人ID" prop="approverId">
              <el-select v-model="dialogForm.approverId" placeholder="请选择审批人ID">
                <el-option v-for="item in  approverIdOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="审批时间" prop="approveTime">
              <el-date-picker v-model="dialogForm.approveTime" type="datetime" placeholder="选择日期时间"></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="创建时间" prop="createdate">
              <el-date-picker v-model="dialogForm.createdate" type="datetime" placeholder="选择日期时间"></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="创建人ID">{{dialogForm.createuserid}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="修改时间">{{dialogForm.modifydate}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="修改人ID">{{dialogForm.modifyuserid}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="时间戳">{{dialogForm.updatetimestamp}}</el-form-item>
          </el-col>
    
          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="删除标记(0-未删 1-已删)">{{dialogForm.deleted}}</el-form-item>
          </el-col>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small"
          @click="submit()">确定
        </el-button>
      </div>
    </el-dialog>
  </template>
  

<script>
  import {
    getSopPermApplyDetail,
    saveSopPermApplyForm
  } from "@/api/SOP/sopPermApply";

  export default {
    components:{
      
    },
    data() {
      return {
        dialogForm: {},
        dialogVisible: false,
        formLoading: false,
        lineOptions: [],
        targetLineOptions: [],
        currentRow: {},
        matInfo:{}
      }
    },
    mounted() {
    },
    methods: {
      submit() {
        saveSopPermApplyForm(this.dialogForm).then(res=>{
          this.$message.success(res.msg)
          this.$emit('saveForm')
          this.dialogVisible = false
        })
      },
      show(data) {
        this.dialogForm = {}
        this.currentRow = data
        this.dialogVisible = true
        this.$nextTick(_ => {
          if(data.ID){
            this.getDialogDetail(data.ID)
          }
        })
      },
      getDialogDetail(id){
        getSopPermApplyDetail(id).then(res => {
          this.dialogForm = res.response
        })
      },
    }
  }
  </script>
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';
let DFM = 'baseURL_DFM';


// 保养列表
export function GetMaintainItemPageList(data) {
    const api = '/api/MaintainItem/GetListByDeviceCategoryId';
    return getRequestResources(baseURL, api, 'post', data);
}

// 保养列表分页
export function GetMaintainGetPageList(data) {
    const api = '/api/MaintainItem/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}

// 保养删除
export function GetMaintainItemDelete(data) {
    const api = '/api/MaintainItem/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}

// 保养新增
export function GetMaintainItemSaveForm(data) {
    const api = '/api/MaintainItem/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
// 保养项目新增
export function GetMaintainWoItemSaveForm(data) {
    const api = '/api/MaintainWoItem/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}

// 保养新增
export function ImportMaintainItemData(data) {
    const api = '/api/MaintainItem/ImportData';
    return getRequestResources(baseURL, api, 'post', data);
}
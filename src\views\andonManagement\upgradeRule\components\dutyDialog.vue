<template>
    <!-- 告警升级规则 -->
    <v-dialog v-model="dialog" persistent max-width="800px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{desserts[0]?desserts[0].GroupName: ''}}详情({{operaObj.MainAlarmName}}-{{operaObj.SubAlarmName}}-{{operaObj.EventLevel}})
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="mt-4">
                <Tables :headers="headers" :loding="loding" :dictionaryList="dictionaryList" :showSelect="false" tableHeight="460px" :desserts="desserts" :footer="false" :page-options="pageOptions"></Tables>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions>
                <span></span>
                <v-spacer></v-spacer>
                <v-btn color="normal" @click="closeForm">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { dutyColum } from '@/columns/andonManagement/upgradeRule.js';
import { getAlarmgroupPostList } from '@/api/andonManagement/alarmGroup.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        needOnTypes: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            loding: false,
            dialog: false,
            headers: dutyColum,
            desserts: [],
            pageOptions: {},
        };
    },
    computed: {
        dictionaryList(){
            return [
                {arr: this.needOnTypes, key: 'IsNeedOn', val: 'ItemValue', text: 'ItemName'}
            ]
        }        
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal) {
                   this.getSubDataList(this.operaObj.Duty)  
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        // 获取接警组详情
        async getSubDataList(GroupCode) {
            this.loding = true
            const res = await getAlarmgroupPostList({ GroupCode })
            const { success, response } = res || {};
            if(success){
                this.desserts = response || []
            }
            this.loding = false
        },
        //关闭
        closeForm() {
            this.dialog = false;
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>
<template>
  <el-dialog
    title="选择目录负责人"
    width="800px"
    :visible.sync="visible"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :modal="true"
    @closed="handleClose"
  >
    <div class="search-area">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="keywords"
            placeholder="请输入关键字"
            clearable
            size="small"
          />
        </el-col>
        <el-col :span="16">
          <el-button type="primary" size="small" @click="getList">查询</el-button>
          <el-button size="small" @click="keywords = ''">重置</el-button>
        </el-col>
      </el-row>
    </div>

    <el-table
      ref="table"
      v-loading="loading"
      :data="tableList"
      height="320"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="DepartmentName" label="部门" />
      <el-table-column prop="LoginName" label="用户工号" /> <!-- 使用LoginName字段显示工号 -->
      <el-table-column prop="UserName" label="用户名" />
      <!-- 移除原RealName列 -->
    </el-table>

    <el-pagination
      class="pagination"
      :current-page="pageOptions.page"
      :page-sizes="pageOptions.pageSizeitems"
      :page-size="pageOptions.pageSize"
      :total="pageOptions.total"
      layout="total, sizes, prev, pager, next"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <div slot="footer" class="dialog-footer">
      <el-checkbox v-model="isChecked">确定并不再提示</el-checkbox>
      <div class="dialog-footer__buttons">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getUserList } from '@/api/systemManagement/userManagement'

export default {
  name: 'SelectUser',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      keywords: '', // 搜索关键字
      loading: false, // 加载状态
      tableList: [], // 用户列表数据
      pageOptions: {
        page: 1,
        pageSize: 10,
        pageSizeitems: [10, 20, 50, 100],
        total: 0
      },
      isChecked: false // 不再提示复选框
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getList() // 对话框显示时加载数据
      }
    }
  },
  methods: {
    // 获取用户列表
    async getList() {
      this.loading = true
      try {
        const params = {
          pageIndex: this.pageOptions.page,
          pageSize: this.pageOptions.pageSize,
          keywords: this.keywords
        }
        const res = await getUserList(params)
        if (res.success) {
          this.tableList = res.response.data || []
          this.pageOptions.total = res.response.dataCount || 0
        } else {
          this.$message.error(res.msg || '获取用户列表失败')
        }
      } catch (error) {
        console.error('获取用户列表失败:', error)
        this.$message.error('获取用户列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 处理选择变化
    handleSelectionChange(selection) {
      // 可以在这里处理选择变化逻辑
    },
    
    // 分页大小变化
    handleSizeChange(size) {
      this.pageOptions.pageSize = size
      this.getList()
    },
    
    // 当前页变化
    handleCurrentChange(page) {
      this.pageOptions.page = page
      this.getList()
    },
    
    // 关闭对话框
    handleClose() {
      this.$emit('update:visible', false)
    },
    
    // 提交表单
    submitForm() {
      try {
        const selection = this.$refs.table.selection
        if (selection.length > 0) {
          const user = selection[0]
          console.log('Selected user data:', user)
          
          const userData = {
            ID: user.id || user.ID,
            UserName: user.name || user.UserName,
            LoginName: user.loginName || user.LoginName,
            DepartmentName: user.deptName || user.DepartmentName
          }
          
          this.$emit('select-user', userData)
          this.$emit('update:visible', false)
        } else {
          this.$message.warning('请选择负责人')
        }
      } catch (error) {
        console.error('提交用户选择失败:', error)
        this.$message.error('选择负责人失败: ' + (error.message || '未知错误'))
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.search-area {
  margin-bottom: 15px;
}
.pagination {
  margin-top: 15px;
  text-align: right;
}
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &__buttons {
    display: flex;
    gap: 10px;
  }
}
</style>
<template>
  <div class="quality-parameters">
    <div class="search-box pd5">
      <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
        <el-form-item :label="$t('GLOBAL._SSL')">
          <el-input clearable v-model="searchForm.Key"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" @click="getSearchBtn()">{{ $t('GLOBAL._CX') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="success" icon="el-icon-circle-plus-outline" @click="showDialog({})">
            {{ $t('GLOBAL._XZ') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-box">
      <el-table 
        :data="tableData" 
        style="width: 100%" 
        :height="tableHeight"
        border
        @sort-change="handleSortChange"
        :empty-text="$t('GLOBAL._NO_DATA')"
      >
        <el-table-column prop="ParameterName" label="检验项目名称" resizable sortable min-width="120" align="center"></el-table-column>
      
         <el-table-column prop="OperationId" label="OperationId" resizable sortable min-width="120" align="center"></el-table-column>
       <el-table-column prop="OperationName" label="OperationName" resizable sortable min-width="120" align="center"></el-table-column>

         <el-table-column prop="ReturnDcs" label="是否回传DCS" resizable sortable min-width="120" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.ReturnDcs === '1'" type="success">是</el-tag>
              <el-tag v-else type="danger">否</el-tag>
            </template>
        </el-table-column>

        <el-table-column prop="Remark" label="备注" resizable sortable min-width="200"></el-table-column>

        <el-table-column prop="operation" width="160" :label="$t('GLOBAL._ACTIONS')" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="showDialog(scope.row)">{{ $t('GLOBAL._BJ') }}</el-button>
            <el-button size="mini" type="text" @click="delRow(scope.row)">{{ $t('GLOBAL._SC') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-pagination 
      class="pagination" 
      background 
      :current-page="searchForm.pageIndex" 
      :page-size="searchForm.pageSize"
      layout="->, total, prev, pager, next" 
      :total="total" 
      @current-change="handleCurrentChange" 
    />

    <quality-parameters-dialog @saveForm="getSearchBtn" ref="formDialog"></quality-parameters-dialog>
  </div>
</template>

<script>
import QualityParametersDialog from './quality-parameters-dialog'
import { getQualityParametersList, deleteQualityParameter } from '@/api/productionManagement/productBOMMaterialDetails'

export default {
  name: 'QualityParameters',
  components: {
    QualityParametersDialog
  },
  props: {
    sapSegmentMaterialId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      searchForm: {
        Key: '',
        pageIndex: 1,
        pageSize: 10
      },
      total: 0,
      tableData: [],
      loading: false,
      tableHeight: 500 // 默认高度，将在mounted和窗口调整时更新
    }
  },
  watch: {
    sapSegmentMaterialId: {
      handler(newVal) {
        if (newVal) {
          this.getTableData();
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.calculateTableHeight()
      // 监听窗口大小变化，重新计算表格高度
      window.addEventListener('resize', this.calculateTableHeight)
    })
  },
  beforeDestroy() {
    // 移除事件监听器
    window.removeEventListener('resize', this.calculateTableHeight)
  },
  methods: {
    showDialog(row) {
      // 打印行数据，帮助调试
      console.log('编辑质检参数行数据:', row);
      
      // 创建一个新对象，避免直接修改原始行数据
      const editData = { ...row };
      
      // 确保 sapSegmentMaterialId 字段存在
      editData.sapSegmentMaterialId = this.sapSegmentMaterialId;
      
      // 确保字段名格式正确（大写字母开头）
      if (row.id) {
        // 如果是编辑现有记录
        console.log('编辑现有质检参数记录，ID:', row.id);
      } else {
        // 如果是新增记录
        console.log('新增质检参数记录');
      }
      
      // 打印传递给对话框的数据
      console.log('传递给质检参数对话框的数据:', editData);
      
      this.$refs.formDialog.show(editData);
    },
    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },
    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },
    delRow(row) {
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        this.loading = true
        try {
          // 传递ID数组作为参数
          const res = await deleteQualityParameter([row.ID])
          if (res.success) {
            this.$message.success('删除成功')
            this.getTableData()
          } else {
            this.$message.error(res.message || '删除失败')
          }
        } catch (error) {
          console.error('删除工艺质检参数失败:', error)
          this.$message.error('删除失败，请稍后重试')
        } finally {
          this.loading = false
        }
      }).catch(err => {
        console.log(err)
      })
    },
    getTableData() {
      if (!this.sapSegmentMaterialId) return;
      
      this.loading = true
      const params = {
        Key: this.searchForm.Key,
        pageIndex: this.searchForm.pageIndex,
        pageSize: this.searchForm.pageSize,
        sapSegmentMaterialId: this.sapSegmentMaterialId
      }
      
      getQualityParametersList(params).then(res => {
        try {
          if (res && res.success) {
            // 数据在res.response.data中
            const responseData = res.response && res.response.data;
            
            // 确保responseData存在且包含rows属性
            if (responseData && Array.isArray(responseData.rows)) {
              // 确保数据字段名与表格列属性名一致
              this.tableData = responseData.rows.map(item => {
                // 打印原始数据，帮助调试
                console.log('原始质检参数数据项:', item);
                return item;
              });
              this.total = responseData.total || 0
            } else if (responseData && Array.isArray(responseData)) {
              // 如果返回的是数组而不是包含rows的对象
              this.tableData = responseData.map(item => {
                console.log('原始质检参数数据项:', item);
                return item;
              });
              this.total = responseData.length || 0
            } else {
              // 如果数据结构不符合预期，设置为空数组
              console.warn('工艺质检参数数据结构不符合预期:', responseData)
              this.tableData = []
              this.total = 0
            }
            
            // 数据加载完成后重新计算表格高度
            this.$nextTick(() => {
              this.calculateTableHeight()
            })
          } else {
            this.$message.error(res?.message || '获取数据失败')
            this.tableData = []
            this.total = 0
          }
        } catch (parseError) {
          console.error('解析工艺质检参数数据失败:', parseError)
          this.$message.error('解析数据失败，请稍后重试')
          this.tableData = []
          this.total = 0
        } finally {
          this.loading = false
        }
      }).catch(error => {
        console.error('获取工艺质检参数列表失败:', error)
        this.$message.error('获取数据失败，请稍后重试')
        this.tableData = []
        this.total = 0
        this.loading = false
      })
    },
    handleSortChange({ prop, order }) {
      // 处理表格排序
      if (!prop || !order) {
        this.tableData = [...this.tableData]
        return
      }

      this.tableData.sort((a, b) => {
        let aValue = a[prop] === undefined || a[prop] === null ? '' : a[prop]
        let bValue = b[prop] === undefined || b[prop] === null ? '' : b[prop]

        // 如果值是数字，进行数字比较
        if (!isNaN(Number(aValue)) && !isNaN(Number(bValue))) {
          return order === 'ascending' ? Number(aValue) - Number(bValue) : Number(bValue) - Number(aValue)
        }

        // 字符串比较
        aValue = String(aValue).toLowerCase()
        bValue = String(bValue).toLowerCase()
        
        if (order === 'ascending') {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
        } else {
          return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
        }
      })
    },
    calculateTableHeight() {
      this.$nextTick(() => {
        try {
          // 获取窗口可视区域高度
          const windowHeight = window.innerHeight
          
          // 获取表格容器的位置
          const tableBox = this.$el.querySelector('.table-box')
          if (!tableBox) return
          
          const tableBoxTop = tableBox.getBoundingClientRect().top
          
          // 获取分页器的高度
          const pagination = this.$el.querySelector('.pagination')
          const paginationHeight = pagination ? pagination.offsetHeight : 40
          
          // 计算表格可用高度 (从表格顶部到窗口底部减去分页器高度)
          const padding = 20 // 底部边距
          const availableHeight = windowHeight - tableBoxTop - paginationHeight - padding
          
          // 设置表格高度，确保最小高度为300px
          this.tableHeight = Math.max(availableHeight, 300)
          
          // 如果表格数据为空，确保表格仍然填充到底部
          if (this.tableData.length === 0) {
            // 添加额外的空间以确保表格底部在界面底部
            const emptyTableMinHeight = windowHeight - tableBoxTop - paginationHeight - padding
            this.tableHeight = Math.max(emptyTableMinHeight, this.tableHeight)
          }
        } catch (error) {
          console.error('计算表格高度时出错:', error)
          // 出错时使用默认高度
          this.tableHeight = 500
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.quality-parameters {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .pd5 {
    padding: 5px;
  }

  .search-box {
    margin-bottom: 16px;
    
    ::v-deep .el-form--inline {
      height: 32px;
    }
  }

  .table-box {
    flex: 1;
    margin-bottom: 16px;
    height: calc(100% - 80px);
    display: flex;
    flex-direction: column;
    
    ::v-deep .el-table {
      flex: 1;
      border-radius: 6px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
      border: 1px solid #e8e8e8;
      height: 100%;

      th {
        background-color: #f7f8fa;
        font-weight: 500;
        color: #303133;
      }

      tr:hover > td {
        background-color: #f5f7fa !important;
      }

      // 确保空数据时表格仍然填充空间
      .el-table__empty-block {
        min-height: 300px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
      }
      
      .el-table__fixed-right {
        height: 100% !important;
        box-shadow: -2px 0 8px rgba(0, 0, 0, 0.06);
      }
    }
  }

  .pagination {
    margin-top: 16px;
    padding: 0 16px;
  }
}

::v-deep .el-button {
  border-radius: 4px;
  padding: 8px 16px;

  &--success {
    background-color: #52c41a;
    border-color: #52c41a;

    &:hover {
      background-color: #73d13d;
      border-color: #73d13d;
    }
  }
  
  &--text {
    padding: 2px 6px;
    
    &:hover {
      color: #409EFF;
      background-color: rgba(64, 158, 255, 0.1);
      border-radius: 2px;
    }
  }
}

// 响应式布局
@media screen and (max-width: 768px) {
  .quality-parameters {
    padding: 10px;
    
    .table-box {
      margin-bottom: 10px;
    }
    
    .pagination {
      margin-top: 10px;
    }
  }
}

/* 删除确认对话框样式 */
::v-deep .delete-confirm-dialog {
  .el-message-box__header {
    padding: 15px;
    background-color: #f7f8fa;
    border-bottom: 1px solid #ebeef5;
  }
  
  .el-message-box__title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }
  
  .el-message-box__content {
    padding: 20px;
    font-size: 14px;
    color: #606266;
  }
  
  .el-message-box__btns {
    padding: 10px 15px;
    text-align: right;
    
    button {
      padding: 9px 15px;
      font-size: 12px;
      border-radius: 4px;
      margin-left: 10px;
    }

    .el-button--primary {
      background-color: #409EFF;
      border-color: #409EFF;
      color: #fff;
      
      &:hover {
        background-color: #66b1ff;
        border-color: #66b1ff;
      }
    }
  }
}
</style>
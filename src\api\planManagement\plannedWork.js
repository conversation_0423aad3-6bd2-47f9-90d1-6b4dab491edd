import { getRequestResources } from '@/api/fetch';
const baseURL3 = 'baseURL_30015'

// 查询物料配方版本
export function getSapprodversion(data) {
  const api = '/ppm/Sapprodversion/GetList'
  return getRequestResources(baseURL3, api, 'post', data);
}

// 查询bom明细
export function getOrderBom(data) {
  const api = '/ppm/Formulaschedule/GetOrderBom'
  return getRequestResources(baseURL3, api, 'post', data);
}

// 查询bom
export function getOrderBomByOrderId(data) {
  const api = '/ppm/Formulaschedule/GetOrderBomByOrderId'
  return getRequestResources(baseURL3, api, 'post', data);
}
// 计算计划工单需求量
export function computeOrderRequire(data) {
  const api = '/ppm/Formulaschedule/ComputeOrderRequire'
  return getRequestResources(baseURL3, api, 'post', data);
}
// 获取包装工单
export function getSapPackOrder(data) {
  const api = '/ppm/Sappackorder/GetSapPackOrder'
  return getRequestResources(baseURL3, api, 'post', data);
}

// 同配方合并
export function mergeFormulaOrder(data) {
  const api = '/ppm/Formulaschedule/MergeFormulaOrder'
  return getRequestResources(baseURL3, api, 'post', data);
}
//上传工单到SAP
export function getUpLoadToSAP(data){
  const api = '/ppm/Formulaschedule/SendOrderToSap'
  return getRequestResources(baseURL3, api, 'post', data);
}

// 同配方拆分
export function splitFormulaOrder(data) {
  const api = '/ppm/Formulaschedule/SplitFormulaOrder'
  return getRequestResources(baseURL3, api, 'post', data);
}

// 修改配方版本
export function changeOrderRecipeVersion(data) {
  const api = '/ppm/Formulaschedule/ChangeOrderRecipeVersion'
  return getRequestResources(baseURL3, api, 'post', data);
}

// 生成计划工单
export function createPlanOrder(data) {
  const api = '/ppm/Formulaschedule/ComputeOrderRequire '
  return getRequestResources(baseURL3, api, 'post', data);
}
// 列表
export function getPageList(data) {
  const api = '/ppm/PlanOrder/GetPageList'
  return getRequestResources(baseURL3, api, 'post', data);
}
// 删除
export function delFormulaschedule(data) {
  const api = '/ppm/Formulaschedule/DeletePlanOrder'
  return getRequestResources(baseURL3, api, 'post', data);
}
// 删除子工单喉头
export function DeleteBatchOrder(data) {
  const api = '/ppm/Formulaschedule/DeleteBatchOrder'
  return getRequestResources(baseURL3, api, 'post', data);
}
// 获取生产版本
export function getSapprodversionByLine(data) {
  const api = '/ppm/Formulaschedule/GetSapprodversionByLine'
  return getRequestResources(baseURL3, api, 'post', data);
}
//获取工单信息
export function getFormulaInfo(id) {
  const api = '/ppm/Formulaschedule/GetFormulaInfo/' + id
  return getRequestResources(baseURL3, api, 'get');
}
//计算
export function getFormulascheduleCompute(data) {
  const api = '/ppm/Formulaschedule/FormulascheduleCompute'
  return getRequestResources(baseURL3, api, 'post', data);
}

// 修改工单日期
export function UpdateFormulaProduceDate(data) {
  const api = '/ppm/Formulaschedule/UpdateFormulaProduceDate'
  return getRequestResources(baseURL3, api, 'post', data);
}
// 取消排产
export function setCancelFormula(data) {
  const api = '/ppm/Formulaschedule/CancelFormula'
  return getRequestResources(baseURL3, api, 'post', data);
}
//获取喉头清单
export function getOrderThroatList(data) {
  const api = '/ppm/Workorderthroat/GetPageList'
  return getRequestResources(baseURL3, api, 'post', data);
}

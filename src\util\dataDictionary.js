//获取数据字段
// import {GetDFMYesNo} from "@/api/systemManagement/dataDictionary";
// export async function  EscapeText(code,val){
//     let { response } = await GetDFMYesNo({ItemCode: code})
//     return response
// }
//获取数据字段
import {dataItemDetailGetList} from "@/api/common";
import {GetDFMYesNo} from "@/api/systemManagement/dataDictionary";
let EscapeTextList = {}
export function  EscapeText(code){
    GetDFMYesNo({ItemCode: code}).then(res=>{
        EscapeTextList[code]= res.response
    })
}
export function getTableHead(keys,option){
    let arr = []
    for (let key in keys) {
        let optionItem = option.find(e=>e.code == key)
        let obj = {}
        if(optionItem){
            obj = {
                field: key,
                label: keys[key],
                ...optionItem
            }
        }else{
            obj = {
                field: key,
                label: keys[key],
            }
        }
        arr.push(obj)
    }
    return arr
}

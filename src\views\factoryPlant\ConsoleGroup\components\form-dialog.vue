<template>
  <el-dialog :title="dialogForm.ID ? $t('GLOBAL._BJ'): $t('GLOBAL._XZ')" :visible.sync="dialogVisible" width="600px"
    :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
    @close="dialogVisible = false">
    <el-form  ref="dialogForm" :model="dialogForm" label-width="110px">
      <el-form-item label="Description">
        <el-input v-model="dialogForm.Description" :maxlength="20" placeholder="" />
      </el-form-item>
      <el-form-item label="Type">
        <el-select style="width: 100%" filterable v-model="dialogForm.Type" placeholder="">
          <el-option v-for="(item, index) in TypeList" :key="index" :label="item.ItemName" :value="item.ItemValue">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="ConsoleGroup">
        <el-input v-model="dialogForm.ConsoleGroup" :maxlength="20" placeholder="" />
      </el-form-item>
      <el-form-item label="SortOrder">
        <el-input-number style="width: 100%" size="small" v-model="dialogForm.SortOrder" :min="0" placeholder="" ></el-input-number>
      </el-form-item>
      <el-form-item label="Status">
        <el-switch v-model="dialogForm.Status"  active-color="#13ce66" inactive-color="#909399" active-value="1" inactive-value="0" ></el-switch>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogVisible = false">{{$t('GLOBAL._QX')}}</el-button>
      <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small"
        @click="submit()">{{$t('GLOBAL._QD')}}
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  addEquipmentGroup,
  addLabelCountry,
  addLabelPrinterSize, getEquipmentGroupDetail,
  getLabelPrinterSizeDetail
} from "@/api/systemManagement/labelPrint";
import {GetDFMYesNo} from "@/api/systemManagement/dataDictionary";

export default {
  name: 'Add',
  data() {
    return {
      dialogForm: {},
      dialogVisible: false,
      formLoading: false,
      TypeList:[]
    }
  },
  mounted() {
  },
  methods: {
    submit() {
      addEquipmentGroup(this.dialogForm).then(res => {
      const statusValue=res.success?'success':'error'
      this.$message[statusValue](res.msg)
        this.$emit('saveForm')
        this.dialogVisible = false
      })
    },
    show(data) {
      this.dialogForm = {}
      this.dialogVisible = true
      this.$nextTick(_ => {
        console.log(data)
        if(data.ID)this.getDialogDetail(data.ID)
      })
      this.getTypeList()
    },
    getDialogDetail(id){
      getEquipmentGroupDetail(id).then(res => {
        this.dialogForm = res.response
      })
    },
    getTypeList() {
      GetDFMYesNo({ItemCode: 'EquipmentGroupType'}).then(res => {
        this.TypeList = res.response
      })
    },
  }
}
</script>
<style scoped>

</style>

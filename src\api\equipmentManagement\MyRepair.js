import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';
let DFM = 'baseURL_DFM';



//工单状态
export function GetRepairOrderStatus(data) {
    const api = '/api/DataItemDetail/GetDetailTree?itemCode=RepairOrderStatus';
    return getRequestResources(DFM, api, 'post', data);
}
// 工单类型
export function GetRepairOrderType(data) {
    const api = '/api/DataItemDetail/GetDetailTree?itemCode=RepairOrderType';
    return getRequestResources(DFM, api, 'post', data);
}

// 工单来源
export function GetRepairOrderSource(data) {
    const api = '/api/DataItemDetail/GetDetailTree?itemCode=RepairOrderSource';
    return getRequestResources(DFM, api, 'post', data);
}
// 工单列表
export function GetRepairOrderPageList(data) {
    const api = '/api/RepairOrder/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}
// 工单列表导出
export function GetRepairOrderExportData(data) {
    const api = '/api/RepairOrder/ExportData';
    return getRequestResources(baseURL, api, 'post', data);
}
// 工单列表删除
export function GetRepairOrderDelete(data) {
    const api = '/api/RepairOrder/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
// 工单列表取消
export function GetRepairOrderCancel(data) {
    const api = '/api/RepairOrder/Cancel';
    return getRequestResources(baseURL, api, 'post', data);
}
// 工单列表确认
export function GetRepairOrderConfirm(data) {
    const api = '/api/RepairOrder/Confirm';
    return getRequestResources(baseURL, api, 'post', data);
}

// 工单列表保存
export function GetRepairOrderSaveForm(data) {
    const api = '/api/RepairOrder/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}

// 工单文件上传
export function GetRepairOrderUploadFile(data) {
    const api = '/api/RepairOrder/UploadFile';
    return getRequestResources(baseURL, api, 'post', data);
}
//工单详情
export function GetRepairOrderEntity(data, params) {
    const api = `/api/RepairOrder/GetEntity/id=${params}`;
    return getRequestResources(baseURL, api, 'get', data);
}
//工单文件获取
export function GetRepairOrderGetFileUrl(data, params) {
    const api = `/api/RepairOrder/GetFileUrl?fileName=${params}`;
    return getRequestResources(baseURL, api, 'get', data);
}

//设备列表
export function GetDevicePageList(data) {
    const api = '/api/Device/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}


//设备列表
export function GetRepairRecordListByWoId(data) {
    const api = '/api/RepairRecord/GetListByWoId';
    return getRequestResources(baseURL, api, 'post', data);
}
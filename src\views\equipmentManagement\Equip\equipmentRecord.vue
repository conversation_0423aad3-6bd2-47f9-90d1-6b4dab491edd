<template>
    <div>
        <v-card class="ma-1">
            <v-tabs v-model="tab" background-color="transparent">
                <v-tab @click="changeTab(0)" key="0">{{ $t('TPM_SBGL_SBTZGL._BGLL') }}</v-tab>
                <v-tab @click="changeTab(1)" key="1">{{ $t('TPM_SBGL_SBTZGL._DJJL') }}</v-tab>
                <v-tab @click="changeTab(2)" key="2">{{ $t('TPM_SBGL_SBTZGL._BYJL') }}</v-tab>
                <v-tab @click="changeTab(3)" key="3">{{ $t('TPM_SBGL_SBTZGL._WXJL') }}</v-tab>
                <v-tab @click="changeTab(4)" key="4">{{ $t('TPM_SBGL_SBTZGL._BJSY') }}</v-tab>
            </v-tabs>
            <v-tabs-items v-model="tab">
                <v-tab-item>
                    <Tables
                        :showSelect="false"
                        :footer="false"
                        ref="recordTable"
                        :page-options="pageOptions"
                        :loading="loading"
                        tableHeight="calc(100vh - 180px)"
                        table-name="TPM_SBGL_SBTZGL_SBLL"
                        :headers="EquipRecordColum"
                        :desserts="desserts1"
                    >
                        <template #Content="{ item }">
                            <el-tooltip class="tooltipitem" effect="dark" :disabled="item.Content.length < 80" :content="item.Content" placement="top-start">
                                <span>{{ item.Content.length > 80 ? item.Content.substring(0, 80) + '...' : item.Content }}</span>
                            </el-tooltip>
                        </template>
                    </Tables>
                </v-tab-item>
                <v-tab-item>
                    <Tables
                        :showSelect="false"
                        :footer="false"
                        ref="recordTable"
                        :page-options="pageOptions"
                        :loading="loading"
                        tableHeight="calc(100vh - 180px)"
                        table-name="TPM_SBGL_SBTZGL_SBLL"
                        :headers="EquipSpotCheckWoColum"
                        :desserts="desserts2"
                    ></Tables>
                </v-tab-item>
                <v-tab-item>
                    <Tables
                        :showSelect="false"
                        :footer="false"
                        ref="recordTable"
                        :page-options="pageOptions"
                        :loading="loading"
                        tableHeight="calc(100vh - 180px)"
                        table-name="TPM_SBGL_SBTZGL_SBLL"
                        :headers="EquipMaintainWoColum"
                        :desserts="desserts3"
                    ></Tables>
                </v-tab-item>
                <v-tab-item>
                    <Tables
                        :showSelect="false"
                        :footer="false"
                        ref="recordTable"
                        :page-options="pageOptions"
                        :loading="loading"
                        tableHeight="calc(100vh - 180px)"
                        table-name="TPM_SBGL_SBTZGL_SBLL"
                        :headers="EquipRepairRecordColum"
                        :desserts="desserts4"
                    ></Tables>
                </v-tab-item>
                <v-tab-item>
                    <Tables
                        :showSelect="false"
                        :footer="false"
                        ref="recordTable"
                        :page-options="pageOptions"
                        :loading="loading"
                        tableHeight="calc(100vh - 180px)"
                        table-name="TPM_SBGL_SBTZGL_SBLL"
                        :headers="EquipPartsHistoryDetailColum"
                        :desserts="desserts5"
                    ></Tables>
                </v-tab-item>
                <!-- <v-tab-item>
                    <equipmentBom ref="equipmentBom" :rowtableItem="rowtableItem"></equipmentBom>
                </v-tab-item>
                <v-tab-item>
                    <equipmentFile ref="equipmentFile" :rowtableItem="rowtableItem"></equipmentFile>
                </v-tab-item>
                <v-tab-item>
                    <equipmentRecord ref="equipmentRecord" :rowtableItem="rowtableItem"></equipmentRecord>
                </v-tab-item> -->
            </v-tabs-items>
        </v-card>
    </div>
</template>

<script>
import { EquipRecordColum, EquipSpotCheckWoColum, EquipRepairRecordColum, EquipPartsHistoryDetailColum, EquipMaintainWoColum } from '@/columns/equipmentManagement/Equip.js';
import { HistoryGetPageList } from '@/api/equipmentManagement/EquipParts.js';
import { GetListSpotCheckWo, GetListMaintainWo, GetListRepairRecord, GetListPartsHistoryDetail } from '@/api/equipmentManagement/Equip.js';
import { GetPersonList } from '@/api/equipmentManagement/Equip.js';

export default {
    props: {
        rowtableItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            tab: 0,
            EquipRecordColum,
            EquipSpotCheckWoColum,
            EquipMaintainWoColum,
            EquipRepairRecordColum,
            EquipPartsHistoryDetailColum,
            SpotCheckWoStatus: [],
            DeviceMngData: [],
            MaintainByData: [],
            MaintainStatus: [],
            RepairStatusList: [],
            PartStatuslist: [],
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500],
                orderByFileds: 'OperateDate asc'
            },
            desserts1: [],
            desserts2: [],
            desserts3: [],
            desserts4: [],
            desserts5: [],
            deleteList: []
        };
    },
    async created() {
        this.PartStatuslist = await this.$getNewDataDictionary('PartOutstockStatus');
        this.RepairStatusList = await this.$getNewDataDictionary('RepairOrderStatus');
        let DeviceMng = await GetPersonList('DeviceMng');
        this.DeviceMngData = DeviceMng.response[0].ChildNodes;
        this.DeviceMngData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        let MaintainBy = await GetPersonList('MaintainBy');
        this.MaintainByData = MaintainBy.response[0].ChildNodes;
        this.MaintainByData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.MaintainStatus = await this.$getNewDataDictionary('MaintainPlanStatus');
        this.SpotCheckWoStatus = await this.$getNewDataDictionary('SpotCheckStatus');
    },
    methods: {
        changeTab(v) {
            switch (v) {
                case 0:
                    setTimeout(() => {
                        this.getRecordList(this.rowtableItem);
                    }, 10);
                    break;
                case 1:
                    setTimeout(() => {
                        this.GetSpotCheckWo(this.rowtableItem);
                    }, 10);
                    break;
                case 2:
                    setTimeout(() => {
                        this.GetMaintainWo(this.rowtableItem);
                    }, 10);
                    break;
                case 3:
                    setTimeout(() => {
                        this.GetRepairRecord(this.rowtableItem);
                    }, 10);
                    break;
                case 4:
                    setTimeout(() => {
                        this.GetPartsHistoryDetail(this.rowtableItem);
                    }, 10);
                    break;
            }
        },
        async GetSpotCheckWo(data) {
            let params = {
                DeviceId: data.ID
            };
            this.loading = true;
            try {
                let resp = await GetListSpotCheckWo(params);
                resp.response.forEach(item => {
                    this.SpotCheckWoStatus.forEach(it => {
                        if (item.Status == it.ItemValue) {
                            item.Status = it.ItemName;
                        }
                    });
                });
                this.loading = false;
                this.desserts2 = resp.response;
            } catch {
                this.loading = false;
            }
        },
        async GetMaintainWo(data) {
            let params = {
                DeviceId: data.ID
            };
            this.loading = true;
            try {
                let resp = await GetListMaintainWo(params);
                resp.response.forEach(item => {
                    this.MaintainStatus.forEach(it => {
                        if (item.Status == it.ItemValue) {
                            item.Status = it.ItemName;
                        }
                    });
                    this.MaintainByData.forEach(it => {
                        if (item.MaintainBy == it.ItemValue) {
                            item.MaintainBy = it.ItemName;
                            item.MaintainByValue = it.ItemValue;
                        }
                    });
                    this.DeviceMngData.forEach(it => {
                        if (item.Manager == it.ItemValue) {
                            item.Manager = it.ItemName;
                            item.ManagerValue = it.ItemValue;
                        }
                    });
                });
                this.loading = false;
                this.desserts3 = resp.response;
            } catch {
                this.loading = false;
            }
        },
        async GetRepairRecord(data) {
            let params = {
                DeviceId: data.ID
            };
            this.loading = true;
            try {
                let resp = await GetListRepairRecord(params);
                resp.response.forEach(item => {
                    this.RepairStatusList.forEach(it => {
                        if (item.Status == it.ItemValue) {
                            item.Status = it.ItemName;
                        }
                    });
                });
                this.loading = false;
                this.desserts4 = resp.response;
            } catch {
                this.loading = false;
            }
        },
        async GetPartsHistoryDetail(data) {
            let params = {
                DeviceId: data.ID
            };
            this.loading = true;
            try {
                let resp = await GetListPartsHistoryDetail(params);
                resp.response.forEach(item => {
                    this.PartStatuslist.forEach(it => {
                        if (item.Status == it.ItemValue) {
                            item.Status = it.ItemName;
                        }
                    });
                });
                this.loading = false;
                this.desserts5 = resp.response;
            } catch {
                this.loading = false;
            }
        },
        async getRecordList(data) {
            console.log(data, 123);
            let params = {
                DeviceId: '',
                pageIndex: 1,
                pageSize: 1000,
                orderByFileds: 'ID desc'
            };
            if (data) {
                params.DeviceId = data.ID;
            }
            this.loading = true;
            try {
                let resp = await HistoryGetPageList(params);
                this.loading = false;
                this.desserts1 = resp.response.data;
            } catch {
                this.loading = false;
            }
        }
    }
};
</script>

<style></style>

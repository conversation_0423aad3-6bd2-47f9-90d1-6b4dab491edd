// 点检项目
export const keepPlanColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '项目代码',
    Namevalue: "ProjectCode",
    value: 'Code',
    width: 150,
    sortable: true
},
{
    text: '项目名称',
    Namevalue: "ProjectName",
    value: 'Name',
    width: 150,
    sortable: true
}, {
    text: '分类',
    Namevalue: "Classify",
    value: 'Type',
    width: 100,
    sortable: true
}, {
    text: '点检周期',
    Namevalue: "Inscycle",
    value: 'Cycle',
    width: 100,
    sortable: true
}, {
    text: '点检频次',
    Namevalue: "Insfrequency",
    value: 'Frequency',
    width: 100,
    sortable: true
}, {
    text: '执行时间',
    Namevalue: "executiontime",
    value: 'ExecutionTime',
    width: 150,
    sortable: true
}, {
    text: '检验标准',
    Namevalue: "CheckStandard",
    value: 'CheckStandard',
    width: 200,
    sortable: true
},
{
    text: '措施方法',
    Namevalue: "Methods",
    value: 'Method',
    width: 180,
    sortable: true
},
{
    text: '上限',
    Namevalue: "UpperLimit",
    value: 'LowerBound',
    width: 160,
    sortable: true
},
{
    text: '下限',
    Namevalue: "LowerLimit",
    value: 'UpperBound',
    width: 160,
    sortable: true
},
{
    text: '输入方式',
    Namevalue: "InputType",
    value: 'InputType',
    width: 160,
    sortable: true
}, {
    text: '点检用时',
    Namevalue: "Inspectiontime",
    value: 'SpotPeriod',
    width: 160,
    sortable: true
}, {
    text: '文件',
    Namevalue: "File",
    value: 'FileName',
    width: 160,
    sortable: true
},
{
    text: '备注',
    Namevalue: "Remark",
    value: 'Remark',
    width: 160,
    sortable: true
},
{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 160,
    sortable: true
}
];
// 点检项目
export const keepPlanNoActionsColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '项目代码',
    Namevalue: "ProjectCode",
    value: 'Code',
    width: 150,
    sortable: true
},
{
    text: '项目名称',
    Namevalue: "ProjectName",
    value: 'Name',
    width: 150,
    sortable: true
}, {
    text: '分类',
    Namevalue: "Classify",
    value: 'Type',
    width: 100,
    sortable: true
}, {
    text: '点检周期',
    Namevalue: "Inscycle",
    value: 'Cycle',
    width: 100,
    sortable: true
}, {
    text: '点检频次',
    Namevalue: "Insfrequency",
    value: 'Frequency',
    width: 100,
    sortable: true
}, {
    text: '执行时间',
    Namevalue: "executiontime",
    value: 'ExecutionTime',
    width: 150,
    sortable: true
}, {
    text: '检验标准',
    Namevalue: "CheckStandard",
    value: 'CheckStandard',
    width: 200,
    sortable: true
},
{
    text: '措施方法',
    Namevalue: "Methods",
    value: 'Method',
    width: 180,
    sortable: true
},
{
    text: '上限',
    Namevalue: "UpperLimit",
    value: 'LowerBound',
    width: 160,
    sortable: true
},
{
    text: '下限',
    Namevalue: "LowerLimit",
    value: 'UpperBound',
    width: 160,
    sortable: true
},
{
    text: '输入方式',
    Namevalue: "InputType",
    value: 'InputType',
    width: 160,
    sortable: true
}, {
    text: '点检用时',
    Namevalue: "Inspectiontime",
    value: 'SpotPeriod',
    width: 160,
    sortable: true
}, {
    text: '文件',
    Namevalue: "File",
    value: 'FileName',
    width: 160,
    sortable: true
},
{
    text: '备注',
    Namevalue: "Remark",
    value: 'Remark',
    width: 160,
    sortable: true
},

];
export const unkeepPlanColum2 = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '项目代码',
    Namevalue: "ProjectCode",
    value: 'Code',
    width: 150,
    sortable: true
},
{
    text: '项目名称',
    Namevalue: "ProjectName",
    value: 'Name',
    width: 150,
    sortable: true
}, {
    text: '分类',
    Namevalue: "Classify",
    value: 'Type',
    width: 100,
    sortable: true
}, {
    text: '保养周期',
    Namevalue: "byzq",
    value: 'Cycle',
    width: 100,
    sortable: true
}, {
    text: '保养频率',
    Namevalue: "bypl",
    value: 'Frequency',
    width: 100,
    sortable: true
}, {
    text: '是否停机',
    Namevalue: "IsStop",
    value: 'IsStop',
    width: 100,
    sortable: true
}, {
    text: '检验标准',
    Namevalue: "CheckStandard",
    value: 'CheckStandard',
    width: 200,
    sortable: true
},
{
    text: '措施方法',
    Namevalue: "Methods",
    value: 'Method',
    width: 180,
    sortable: true
},
{
    text: '保养用时',
    Namevalue: "UsingTime",
    value: 'UsingTime',
    width: 160,
    sortable: true
},
{
    text: '备注',
    Namevalue: "Remark",
    value: 'Remark',
    width: 160,
    sortable: true
},
{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 160,
    sortable: true
}
];
// 保养
export const unkeepPlanColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '项目代码',
    Namevalue: "ProjectCode",
    value: 'Code',
    width: 150,
    sortable: true
},
{
    text: '项目名称',
    Namevalue: "ProjectName",
    value: 'Name',
    width: 150,
    sortable: true
}, {
    text: '计划保养时间',
    Namevalue: "PlanMaintainDate",
    value: 'PlanMaintainDate',
    width: 150,
    sortable: true
}, {
    text: '实际保养时间',
    Namevalue: "ActualFinishTime",
    value: 'MaintainDate',
    width: 150,
    sortable: true
}, {
    text: '保养人',
    Namevalue: "MaintainBy",
    value: 'MaintainBy',
    width: 160,
    sortable: true
}, {
    text: '状态',
    Namevalue: "Status",
    value: 'Status',
    width: 160,
    sortable: true
}, {
    text: '分类',
    Namevalue: "Classify",
    value: 'Type',
    width: 100,
    sortable: true
}, {
    text: '保养周期',
    Namevalue: "byzq",
    value: 'Cycle',
    width: 100,
    sortable: true
}, {
    text: '保养频率',
    Namevalue: "bypl",
    value: 'Frequency',
    width: 100,
    sortable: true
}, {
    text: '是否停机',
    Namevalue: "IsStop",
    value: 'IsStop',
    width: 100,
    sortable: true
}, {
    text: '检验标准',
    Namevalue: "CheckStandard",
    value: 'CheckStandard',
    width: 200,
    sortable: true
},
{
    text: '措施方法',
    Namevalue: "Methods",
    value: 'Method',
    width: 180,
    sortable: true
},
{
    text: '输入方式',
    Namevalue: "InputType",
    value: 'InputType',
    width: 180,
    sortable: true
},
{
    text: '保养用时',
    Namevalue: "UsingTime",
    value: 'UsingTime',
    width: 160,
    sortable: true
},
{
    text: '保养结果',
    Namevalue: "Context",
    value: 'Context',
    width: 160,
    sortable: true
},
{
    text: '备注',
    Namevalue: "Remark",
    value: 'Remark',
    width: 160,
    sortable: true
},

{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 160,
    sortable: true
}
];
// 保养计划
export const keepListBColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '维修工单号',
    width: 150,
    Namevalue: "Wo",
    value: 'Wo',
    sortable: true
},
{
    text: '设备编码',
    width: 100,
    Namevalue: "DeviceId",
    value: 'DeviceCode',
    sortable: true
},
{
    text: '设备名称',
    width: 160,
    Namevalue: "DeviceCode",
    value: 'DeviceName',
    sortable: true
},
{
    text: '状态',
    width: 160,
    Namevalue: 'Status',
    value: 'Status',
    sortable: true
},
// {
//     text: '设备类型',
//     width: 120,
//     Namevalue: 'DeviceCategory',
//     value: 'Type',
//     sortable: true
// },
{
    text: '上次保养时间',
    width: 150,
    Namevalue: 'FinishMaintainDate',
    value: 'FinishMaintainDate',
    sortable: true
},
{
    text: '计划保养时间',
    width: 150,
    Namevalue: 'PlanMaintainDate',
    value: 'PlanMaintainDate',
    sortable: true
},
{
    text: '实际开始时间',
    width: 150,
    Namevalue: 'ActualStartTime',
    value: 'StartMaintainDate',
    sortable: true
},
{
    text: '实际结束时间',
    width: 150,
    Namevalue: 'ActualFinishTime',
    value: 'FinishMaintainDate',
    sortable: true
},
{
    text: '保养用时',
    width: 150,
    Namevalue: 'UsingTime',
    value: 'UsingTime',
    sortable: true
},
{
    text: '区域',
    width: 150,
    Namevalue: 'Area',
    value: 'Area',
    sortable: true
},
{
    text: '分组',
    width: 120,
    Namevalue: 'Groups',
    value: 'Groups',
    sortable: true
},
{
    text: '责任人',
    width: 160,
    Namevalue: 'MaintainBy',
    value: 'MaintainBy',
    sortable: true
},
{
    text: '设备主管',
    width: 160,
    Namevalue: 'Manager',
    value: 'Manager',
    sortable: true
},
{
    text: '创建时间',
    width: 160,
    Namevalue: 'CreateDate',
    value: 'CreateDate',
    sortable: true
},
{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 240,
    sortable: true
}
];
// 点检计划
export const keepListColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
// {
//     text: 'EAM编码',
//     width: 120,
//     value: 'DeviceId',
//     sortable: true
// },
{
    text: '设备编号',
    width: 160,
    Namevalue: "DeviceCode",
    value: 'DeviceCode',
    sortable: true
},
{
    text: '设备名称',
    width: 160,
    Namevalue: "DeviceName",
    value: 'DeviceName',
    sortable: true
},
{
    text: '点检状态',
    width: 140,
    Namevalue: "McStatus",
    value: 'Status',
    sortable: true
},
{
    text: '计划点检时间',
    width: 160,
    Namevalue: "CheckPlanTime",
    value: 'PlanCheckDate',
    sortable: true
},
{
    text: '实际完成时间',
    width: 160,
    Namevalue: "ActualFinishTime",
    value: 'FinishCheckDate',
    sortable: true
},
{
    text: '工厂',
    width: 150,
    Namevalue: "factory",
    value: 'Factory',
    sortable: true
}, {
    text: '区域',
    width: 160,
    Namevalue: "area",
    value: 'Area',
    sortable: true
}, {
    text: '分组',
    width: 160,
    Namevalue: "group",
    value: 'Groups',
    sortable: true
}, {
    text: '责任人',
    width: 120,
    Namevalue: "PersonName",
    value: 'CheckBy',
    sortable: true
}, {
    text: '设备主管',
    width: 120,
    Namevalue: "equipmentSupervisor",
    value: 'Manager',
    sortable: true
}, {
    text: '备注',
    width: 160,
    Namevalue: "Remark",
    value: 'Remark',
    sortable: true
}, {
    text: '创建时间',
    width: 160,
    Namevalue: "CreateDate",
    value: 'CreateDate',
    sortable: true
},
// {
//     text: '设备类型',
//     width: 160,
//     value: 'DeviceCategory',
//     sortable: true
// },
// {
//     text: '班次',
//     width: 120,
//     value: 'Shift',
//     sortable: true
// },


{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 240,
    sortable: true
}
];
// 点检计划-任务明细
export const keepListdetilsColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
}, {
    text: '项目编号',
    width: 160,
    Namevalue: "ProjectCode",
    value: 'Code',
    sortable: true
}, {
    text: '项目名称',
    width: 160,
    Namevalue: "McProject",
    value: 'Name',
    sortable: true
}, {
    text: '分类',
    width: 80,
    Namevalue: "Classify",
    value: 'Type',
    sortable: true
}, {
    text: '执行时间',
    width: 150,
    Namevalue: "ImplementTime",
    value: 'ExecutionTime',
    sortable: true
}, {
    text: '状态',
    width: 100,
    Namevalue: "Status",
    value: 'Status',
    sortable: true
}, {
    text: '点检时间',
    width: 150,
    Namevalue: "PlanCheckDate",
    value: 'PlanCheckDate',
    sortable: true
}, {
    text: '点检时间',
    width: 150,
    Namevalue: "Inspectiontime",
    value: 'CheckDate',
    sortable: true
}, {
    text: '点检人',
    width: 100,
    Namevalue: "McUser",
    value: 'CheckBy',
    sortable: true
}, {
    text: '检验标准',
    width: 120,
    Namevalue: "CheckStandard",
    value: 'CheckStandard',
    sortable: true
}, {
    text: '措施方法',
    width: 120,
    Namevalue: "Methods",
    value: 'Method',
    sortable: true
}, {
    text: '上限',
    width: 100,
    Namevalue: "UpperLimit",
    value: 'UpperBound',
    sortable: true
}, {
    text: '下限',
    width: 100,
    Namevalue: "LowerLimit",
    value: 'LowerBound',
    sortable: true
}, {
    text: '输入方式',
    width: 100,
    Namevalue: "InputType",
    value: 'InputType',
    sortable: true
}, {
    text: '点检照片',
    width: 100,
    Namevalue: "CheckFile",
    value: 'CheckFileName',
    sortable: true
}, {
    text: '文件',
    width: 100,
    Namevalue: "FilePath",
    value: 'FileName',
    sortable: true
},
{
    text: '点检结果',
    width: 140,
    Namevalue: "Context",
    value: 'Context',
    sortable: true
},
{
    text: '备注',
    width: 140,
    Namevalue: "Remark",
    value: 'Remark',
    sortable: true
},
{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 160,
    sortable: true
}
];
// 点检计划-任务明细
export const keepListdetilsColum2 = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
}, {
    text: '项目编号',
    width: 160,
    Namevalue: "ProjectCode",
    value: 'ProjectCode',
    sortable: true
}, {
    text: '项目名称',
    width: 160,
    Namevalue: "McProject",
    value: 'McProject',
    sortable: true
}, {
    text: '分类',
    width: 80,
    Namevalue: "Classify",
    value: 'Classify',
    sortable: true
}, {
    text: '点检周期',
    width: 100,
    Namevalue: "CheckCycle",
    value: 'CheckCycle',
    sortable: true
}, {
    text: '点检频次',
    width: 100,
    Namevalue: "CheckTime",
    value: 'CheckTime',
    sortable: true
}, {
    text: '执行时间',
    width: 150,
    Namevalue: "ImplementTime",
    value: 'ImplementTime',
    sortable: true
}, {
    text: '检验标准',
    width: 120,
    Namevalue: "CheckStandard",
    value: 'CheckStandard',
    sortable: true
}, {
    text: '措施方法',
    width: 120,
    Namevalue: "Methods",
    value: 'Methods',
    sortable: true
}, {
    text: '上限',
    width: 100,
    Namevalue: "UpperLimit",
    value: 'UpperLimit',
    sortable: true
}, {
    text: '下限',
    width: 100,
    Namevalue: "LowerLimit",
    value: 'LowerLimit',
    sortable: true
}, {
    text: '输入方式',
    width: 100,
    Namevalue: "InputType",
    value: 'InputType',
    sortable: true
}, {
    text: '点检用时',
    width: 100,
    Namevalue: "Cost",
    value: 'Cost',
    sortable: true
}, {
    text: '文件',
    width: 100,
    Namevalue: "File",
    value: 'File',
    sortable: true
},
{
    text: '备注',
    width: 140,
    value: 'Remark',
    sortable: true
},
];
// 保养计划-任务明细
export const keepListdetilsBColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '项目编号',
    width: 160,
    Namevalue: "ProjectCode",
    value: 'DeviceCode',
    sortable: true
}, {
    text: '项目名称',
    width: 160,
    Namevalue: "ProjectName",
    value: 'DeviceName',
    sortable: true
}, {
    text: '分类',
    width: 80,
    Namevalue: "Classify",
    value: 'Type',
    sortable: true
}, {
    text: '检验标准',
    width: 120,
    Namevalue: "CheckStandard",
    value: 'CheckStandard',
    sortable: true
}, {
    text: '措施方法',
    width: 120,
    Namevalue: "Methods",
    value: 'Method',
    sortable: true
}, {
    text: '输入方式',
    width: 100,
    Namevalue: "InputType",
    value: 'InputType',
    sortable: true
},
{
    text: '备注',
    width: 240,
    value: 'Remark',
    sortable: true
},
{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 140,
    sortable: true
}
];
// 保养计划-备件明细
export const keepListBjBColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '备件编号',
    width: 160,
    Namevalue: "SparePartsCode",
    value: 'PartsCode',
    sortable: true
}, {
    text: '备件名称',
    width: 160,
    Namevalue: "SparePartsName",
    value: 'PartsName',
    sortable: true
}, {
    text: '状态',
    width: 120,
    Namevalue: "status",
    value: 'Status',
    sortable: true
},
{
    text: '申请单号',
    width: 120,
    Namevalue: "requestNo",
    value: "HistoryNo",
    sortable: true
}, {
    text: '备件类型',
    width: 120,
    Namevalue: "SType",
    value: 'PartsType',
    sortable: true
}, {
    text: '规格型号',
    width: 150,
    Namevalue: "SparePartsSpec",
    value: 'PartsModel',
    sortable: true
}, {
    text: '批次号',
    width: 150,
    Namevalue: "Batch",
    value: 'BatchCode',
    sortable: true
}, {
    text: '供应商',
    width: 100,
    Namevalue: "supplier",
    value: 'Supplier',
    sortable: true
}, {
    text: '数量',
    width: 100,
    Namevalue: "number",
    value: 'Qty',
    sortable: true
},

{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 100,
    sortable: true
}
];
export const keepListBjBColum2 = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '备件编号',
    width: 160,
    Namevalue: "SparePartsCode",
    value: 'PartsCode',
    sortable: true
}, {
    text: '备件名称',
    width: 160,
    Namevalue: "SparePartsName",
    value: 'PartsName',
    sortable: true
}, {
    text: '备件类型',
    width: 120,
    Namevalue: "SType",
    value: 'PartsType',
    sortable: true
}, {
    text: '规格型号',
    width: 150,
    Namevalue: "SparePartsSpec",
    value: 'PartsModel',
    sortable: true
}, {
    text: '批次号',
    width: 150,
    Namevalue: "Batch",
    value: 'BatchCode',
    sortable: true
}, {
    text: '供应商',
    width: 100,
    Namevalue: "supplier",
    value: 'Supplier',
    sortable: true
}, {
    text: '数量',
    width: 100,
    Namevalue: "number",
    value: 'Qty',
    sortable: true
},
{
    text: '状态',
    width: 120,
    Namevalue: "status",
    value: 'Status',
    sortable: true
},
{
    text: '申请单号',
    width: 120,
    Namevalue: "requestNo",
    value: "HistoryNo",
    sortable: true
},
];
// 保养记录
export const keepListlogColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '保养项目',
    value: 'Name',
    width: 130,
    sortable: true
},
{
    text: '保养标准',
    width: 120,
    value: 'Itemstandardvalue',
    sortable: true
},
{
    text: '保养实测',
    value: 'Itemactualvalue',
    width: 160,
    sortable: true
},
{
    text: '执行状态',
    width: 140,
    value: 'Status',
    sortable: true
},
{
    text: '备注',
    width: 140,
    value: 'Remark',
    sortable: true
},
{
    text: '责任人',
    width: 140,
    value: 'Owner',
    sortable: true
},
{
    text: '实际保养开始时间',
    width: 160,
    value: 'Actualstarttime',
    sortable: true
},
{
    text: '实际保养结束时间',
    width: 160,
    value: 'Actualendtime',
    sortable: true
},
{
    text: '实际保养工时（小时）',
    width: 160,
    value: 'Actualmaintenancetime',
    sortable: true,
    align: 'right'
},
{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 160,
    sortable: true
}
];
// 点检规则
export const keeprulesColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '点检项目',
    width: 100,
    value: 'McProject',
    sortable: true
},
{
    text: '点检周期',
    dictionary: true,
    width: 120,
    value: 'McCycle',
    sortable: true
},
{
    text: '执行时间',
    width: 180,
    value: 'ImplementTime',
    sortable: true
},
{
    text: '是否启用',
    align: 'center',
    dictionary: true,
    width: 180,
    value: 'StartUsing',
    sortable: true
},
{
    text: '备注',
    width: 140,
    value: 'Remark',
    sortable: true
},

{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 160,
    sortable: true
}
];
// 保养规则
export const keeprulesBColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '保养项目',
    width: 100,
    value: 'MaintainProject',
    sortable: true
},
{
    text: '保养周期(天)',
    width: 120,
    value: 'MaintenancePeriod',
    sortable: true
},
{
    text: '备注',
    width: 140,
    value: 'Remark',
    sortable: true
},
{
    text: '状态',
    width: 120,
    value: 'Isenable',
    sortable: true
},
{
    text: '保养时长',
    width: 120,
    value: 'Duration',
    sortable: true
},
// {
//     text: '开始时间',
//     width: 140,
//     value: 'Startime',
//     sortable: true
// },
// {
//     text: '结束时间',
//     width: 140,
//     value: 'Endtime',
//     sortable: true
// },

{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 160,
    sortable: true
}
];

// 保养计划-任务新增设备清单
export const keepListSBColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '设备编号',
    width: 120,
    Namevalue: "EquipCode",
    value: 'Code',
    sortable: true
},
{
    text: '设备名称',
    Namevalue: "Name",
    value: 'Name',
    width: 130,
    sortable: true
},
{
    text: '设备别名',
    Namevalue: "SubName",
    value: 'Alias',
    width: 130,
    sortable: true
},
{
    text: '工厂',
    Namevalue: "Factory",
    value: 'Factory',
    width: 130,
    sortable: true
},
{
    text: '所属部门',
    Namevalue: "Department",
    value: 'Deparment',
    width: 130,
    sortable: true
},
{
    text: '区域',
    value: 'Area',
    Namevalue: "Area",
    width: 130,
    sortable: true
},
{
    text: '分组',
    value: 'Groups',
    Namevalue: "Group",
    width: 130,
    sortable: true
},
{
    text: '资产编号',
    Namevalue: "Assetnumber",
    value: 'AssetsNo',
    width: 130,
    sortable: true
},
{
    text: '设备位号',
    Namevalue: "EquipItem",
    value: 'DeviceBin',
    width: 150,
    sortable: true
}, {
    text: '设备分类',
    Namevalue: "EquipType",
    value: 'DeviceCategoryId',
    width: 120,
    sortable: true
},
{
    text: '设备类别',
    value: 'DeviceClass',
    Namevalue: "EquipGroup",
    width: 120,
    sortable: true
}, {
    text: '品牌',
    Namevalue: "brand",
    value: 'Brand',
    width: 120,
    sortable: true
},
{
    text: '规格型号',
    value: 'Model',
    Namevalue: "Specmodel",
    width: 150,
    sortable: true
}, {
    text: '存放地点',
    value: 'StoragePosition',
    Namevalue: "Storagelocation",
    width: 150,
    sortable: true
}, {
    text: '责任人',
    value: 'Header',
    Namevalue: "personresponsible",
    width: 150,
    sortable: true
}, {
    text: '设备主管',
    value: 'Manager',
    Namevalue: "equipmentSupervisor",
    width: 150,
    sortable: true
}, {
    text: '备注',
    width: 140,
    value: 'Remark',
    sortable: true
},
]

// 保养规则
export const keepListBYXMColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '保养项目代码',
    width: 140,
    Namevalue: "MaintainProjectCode",
    value: 'Code',
    sortable: true
},
{
    text: '保养项目名称',
    width: 140,
    Namevalue: "MaintainProjectName",
    value: 'Name',
    sortable: true
},
{
    text: '分类',
    width: 100,
    value: 'Type',
    sortable: true
},
{
    text: '保养周期',
    width: 120,
    Namevalue: "MaintenancePeriod",
    value: 'Cycle',
    sortable: true
},
{
    text: '保养频率',
    width: 120,
    Namevalue: "MaintenanceTime",
    value: 'Frequency',
    sortable: true
},
{
    text: '是否停机',
    width: 120,
    Namevalue: "isStop",
    value: 'IsStop',
    sortable: true
},
{
    text: '检验标准',
    width: 120,
    Namevalue: 'CheckStandard',
    value: 'CheckStandard',
    sortable: true
},
{
    text: '措施方法',
    width: 120,
    value: 'Method',
    Namevalue: 'Methods',
    sortable: true
},
{
    text: '保养用时',
    width: 120,
    Namevalue: 'Duration',
    value: 'UsingTime',
    sortable: true
},
{
    text: '备注',
    width: 140,
    value: 'Remark',
    sortable: true
},
];
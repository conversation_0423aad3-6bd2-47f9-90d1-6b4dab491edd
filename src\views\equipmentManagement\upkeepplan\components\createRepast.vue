<template>
    <v-dialog v-model="showDialog" max-width="720px">
        <v-card class="" v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-autocomplete
                                v-model="form.McProjectId"
                                :loading="loading"
                                :items="items"
                                item-value="ID"
                                item-text="McProject"
                                :search-input.sync="form.McProject"
                                flat
                                outlined
                                dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJGZ.McProject')"
                            ></v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-select
                                v-model="form.McCycle"
                                :items="mcCyclelist"
                                item-text="ItemName"
                                item-value="ItemValue"
                                outlined
                                dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJGZ.McCycle')"
                            ></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-select
                                v-model="form.StartUsing"
                                :items="StartUsinglist"
                                item-text="ItemName"
                                item-value="ItemValue"
                                outlined
                                dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJGZ.StartUsing')"
                            ></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form.ImplementTime" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJGZ.ImplementTime')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="form.Remark" rows="2" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJGZ.Remark')"></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'edit'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._BJ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-autocomplete
                            v-model="editedItem.McProjectId"
                            :loading="loading"
                            :items="items"
                            item-value="ID"
                            item-text="McProject"
                            :search-input.sync="editedItem.McProject"
                            flat
                            outlined
                            dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJGZ.McProject')"
                        ></v-autocomplete>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-select
                            v-model="editedItem.McCycle"
                            :items="mcCyclelist"
                            item-text="ItemName"
                            item-value="ItemValue"
                            outlined
                            dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJGZ.McCycle')"
                        ></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-select
                            v-model="editedItem.StartUsing"
                            :items="StartUsinglist"
                            item-text="ItemName"
                            item-value="ItemValue"
                            outlined
                            dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJGZ.StartUsing')"
                        ></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.ImplementTime" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJGZ.ImplementTime')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea v-model="editedItem.Remark" rows="2" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJGZ.Remark')"></v-textarea>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { DeviceMcProjectGetList, DeviceMcRuleSavesForm } from '@/api/equipmentManagement/upkeep.js';
const StartUsinglist = [
    { ItemName: '是', ItemValue: '1' },
    { ItemName: '否', ItemValue: '0' }
];
export default {
    props: {
        mcCyclelist: {
            type: Array,
            default: () => []
        },
        repastTypelist: {
            type: Array,
            default: () => []
        },
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            valid: false,
            showDialog: false,
            classcheckbox: true,
            StartUsinglist,
            strbatchNo: '',
            items: [],
            form: {
                McProject: '',
                McProjectId: '',
                McCycle: '',
                ImplementTime: '',
                StartUsing: '1',
                Remark: ''
            }
        };
    },
    computed: {
        editedItem() {
            const { McProject, McProjectId, McCycle, ImplementTime, StartUsing, Remark } = this.tableItem;
            return {
                McProject,
                McProjectId,
                McCycle,
                ImplementTime,
                StartUsing,
                Remark
            };
        }
    },
    watch: {
        search(val) {
            val && val !== this.form.McProjectId && this.querySelections(val);
        }
    },
    created() {
        this.querySelections();
    },
    methods: {
        closeEquip() {
            this.showDialog = false;
            this.$refs.form.reset();
        },

        async querySelections(v) {
            this.loading = true;
            const res = await DeviceMcProjectGetList({ key: v, pcategory: '点检' });
            let { success, response } = res;
            if (success) {
                this.items = response;
                this.loading = false;
            }
        },
        async addSave(type) {
            const paramsKey = Object.keys(this.form);
            const paramsObj = type == 'add' ? this.form : this.editedItem;
            let params = {};
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            if (type == 'edit') {
                params.ID = this.tableItem.ID;
            }
            const res = await DeviceMcRuleSavesForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.$parent.RepastInfoGetPage();
                this.showDialog = this.classcheckbox ? false : true;
            }
        }
    }
};
</script>

<template>
    <v-card>
        <v-card-title class="headline primary lighten-2" primary-title>{{ $t('DFM_GYLX._TJGYLXMX') }}</v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid">
                <v-row class="mt-5">
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX.ProcCode')" :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" required dense outlined v-model="form.ProcCode"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX.ProcName')" :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" required dense outlined v-model="form.ProcName"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX.Version')" dense outlined v-model="form.Version"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select
                            :items="ProcTypeList"
                            item-text="ItemName"
                            item-value="ItemValue"
                            no-data-text="暂无数据"
                            clearable
                            dense
                            v-model="form.ProcType"
                            outlined
                            :label="$t('DFM_GYLX.ProcType')"
                            placeholder="请选择工序类型"
                        />
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX.Unit')" dense outlined v-model="form.Unit"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-menu ref="menu1" v-model="menu1" :close-on-content-click="false" transition="scale-transition" offset-y max-width="290px" min-width="290px">
                            <template #activator="{ on, attrs }">
                                <v-text-field v-model="form.EffectStart" readonly :label="$t('DFM_GYLX.EffectStart')" persistent-hint outlined dense v-bind="attrs" v-on="on"></v-text-field>
                            </template>
                            <v-date-picker v-model="form.EffectStart" no-title @input="menu1 = false"></v-date-picker>
                        </v-menu>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-menu ref="menu2" v-model="menu2" :close-on-content-click="false" transition="scale-transition" offset-y max-width="290px" min-width="290px">
                            <template #activator="{ on, attrs }">
                                <v-text-field v-model="form.EffectEnd" readonly :label="$t('DFM_GYLX.EffectEnd')" persistent-hint outlined dense v-bind="attrs" v-on="on"></v-text-field>
                            </template>
                            <v-date-picker v-model="form.EffectEnd" no-title @input="menu2 = false"></v-date-picker>
                        </v-menu>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX.Timb')" dense outlined v-model="form.Timb"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX.Runm')" dense outlined v-model="form.Runm"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX.Runl')" dense outlined v-model="form.Runl"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX.Setl')" dense outlined v-model="form.Setl"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX.Movd')" dense outlined v-model="form.Movd"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX.Qued')" dense outlined v-model="form.Qued"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select
                            :items="JbcdList"
                            item-text="ItemName"
                            item-value="ItemValue"
                            no-data-text="暂无数据"
                            clearable
                            dense
                            v-model="form.Jbcd"
                            outlined
                            :label="$t('DFM_GYLX.Jbcd')"
                            placeholder="请选择工作类型"
                        />
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select
                            :items="StatusList"
                            item-text="ItemName"
                            item-value="ItemValue"
                            no-data-text="暂无数据"
                            clearable
                            dense
                            v-model="form.Status"
                            outlined
                            :label="$t('DFM_GYLX.Status')"
                            placeholder="请选择状态"
                        />
                    </v-col>
                    <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <v-textarea :label="$t('DFM_GYLX.Description')" v-model="form.Description" :value="form.Description" outlined height="70"></v-textarea>
                    </v-col>
                    <v-col class="pt-0 pb-0" :cols="12" :lg="12">
                        <v-textarea :label="$t('DFM_GYLX.Notes')" v-model="form.Notes" :value="form.Notes" outlined height="70"></v-textarea>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>

        <v-divider></v-divider>

        <v-card-actions>
            <!-- <v-spacer></v-spacer> -->
            <v-checkbox class="mr-auto" v-model="isChecked" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
// import DatePicker from '@/components/DatePicker.vue';
import { savePathDetail, getDataDictionary } from '../service';
export default {
    components: {},
    props: {
        currentSelectId: {
            type: String,
            default: ''
        },
        selectDetailObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            valid: false,
            menu1: false,
            menu2: false,
            isChecked: true,
            ProcTypeList: [],
            JbcdList: [],
            StatusList: [],
            form: {
                ProcCode: '', // 工序编码
                ProcName: '', //工序名称
                Version: '', // 版本
                ProcType: '', //类型
                Unit: '', //经营单位
                EffectStart: '', //生效自
                EffectEnd: '', //生效至
                Timb: '', //工时基准
                Runm: '', //运行机器
                Runl: '', //运行人工
                Setl: '', //设置人工
                Movd: '', //搬运小时数
                Qued: '', //排队小时数
                Jbcd: '', //工作类型
                Description: '', //描述
                Notes: '', //备注
                Status: '' // 状态
            }
        };
    },
    async created() {
        console.log('------', this.selectDetailObj);
        await this.getProcTypeList();
        await this.getJbcdList();
        await this.getStatusList();
        if (this.selectDetailObj && this.selectDetailObj.ID) {
            for (const key in this.form) {
                this.form[key] = this.selectDetailObj[key];
            }
            this.form.ID = this.selectDetailObj.ID;
        }
    },
    methods: {
        parseDate(date) {
            if (!date) return null;
            const [month, day, year] = date.split('/');
            return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        },
        // 获取工序类型列表
        async getProcTypeList() {
            let resp = await getDataDictionary({ lang: 'cn', itemCode: 'ProcType' });
            this.ProcTypeList = resp.response;
        },
        closePopup() {
            this.$emit('handlePopup', false, 'detail');
        },
        async getJbcdList() {
            let resp = await getDataDictionary({ lang: 'cn', itemCode: 'WorkType' });
            this.JbcdList = resp.response;
        },
        async getStatusList() {
            let resp = await getDataDictionary({ lang: 'cn', itemCode: 'DFMActiveStatus' });
            this.StatusList = resp.response;
        },
        resetForm() {
            this.form = {
                ProcCode: '', // 工序编码
                ProcName: '', //工序名称
                Version: '', // 版本
                ProcType: '', //类型
                Unit: '', //经营单位
                EffectStart: '', //生效自
                EffectEnd: '', //生效至
                Timb: '', //工时基准
                Runm: '', //运行机器
                Runl: '', //运行人工
                Setl: '', //设置人工
                Movd: '', //搬运小时数
                Qued: '', //排队小时数
                Jbcd: '', //工作类型
                Description: '', //描述
                Notes: '', //备注
                Status: '' // 状态
            };
        },
        // 表单提交
        async submitForm() {
            if (!this.$refs.form.validate()) return false;

            let resp = await savePathDetail({ RoutingId: this.currentSelectId, ...this.form });
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
            this.resetForm();
            this.$emit('getDetailTableList');
            if (this.isChecked) {
                this.isChecked = !this.isChecked;
                this.$emit('handlePopup', false, 'detail');
            }
        }
    }
};
</script>

<!-- eslint-disable vue/valid-v-slot -->
<template>
    <v-card>
        <v-card-title class="headline primary lighten-2" primary-title>{{ $t('DFM_RL._BZGL') }}</v-card-title>
        <v-card-text>
            <v-row class="mt-4">
                <v-col :cols="12" :lg="4" class="pt-0 pb-0 d-flex">
                    <v-text-field :label="$t('DFM_RL._MC')" dense outlined v-model="teamName"></v-text-field>
                    <v-btn color="primary" class="ml-3" @click="getdata">{{ $t('GLOBAL._CX') }}</v-btn>
                </v-col>
                <v-col :cols="12" :lg="6" class="pt-0 pb-0 ml-auto text-right">
                    <v-btn icon color="primary" @click="getdata">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" class="ml-2" @click="addTeam()">{{ $t('GLOBAL._XZ') }}</v-btn>
                </v-col>
            </v-row>
            <v-data-table :items-per-page='999' :headers="headers" hide-default-footer dense height="200"
                :loading="loading" :items="teamList">
                <template #item.actions="{ item }">
                    <v-btn v-for="(list, index) in btnList" :key="index" text small class="mx-0 px-0"
                        @click="handleClick(item, list.code)" :color="list.type">{{ list.text }}</v-btn>
                </template>
            </v-data-table>
        </v-card-text>
        <v-divider></v-divider>

        <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>

        <!-- 添加班组 -->
        <v-dialog scrollable v-model="editTeamPopup" width="35%">
            <EditTeam :editObj="editObj" @getdata="getdata" v-if="editTeamPopup" type="team"
                @closeEditPopup="closeEditPopup" />
        </v-dialog>
    </v-card>
</template>

<script>
import EditTeam from './editPopup.vue';
import { getTeamList, deleteTeam } from '../service';
export default {
    components: {
        EditTeam
    },
    props: {
        currentDeparment: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            teamName: '',
            loading: false,
            teamList: [],
            editObj: {},
            editTeamPopup: false
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: ''
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: ''
                }
            ];
        },
        headers() {
            return [
                { text: this.$t('DFM_RL._MC'), value: 'Name', width: 100 },
                { text: this.$t('DFM_RL._JC'), value: 'Shortname', width: 100 },
                { text: this.$t('DFM_RL._XH'), value: 'Sequence', width: 80 },
                // { text: '有效', value: 'Enabledmark', width: 80 },
                { text: this.$t('DFM_RL._BZ'), value: 'Description', width: 150 },
                { text: this.$t('DFM_RL._CZ'), value: 'actions', width: 120 }
            ];
        }
    },
    created() {
        this.getdata();
    },
    methods: {
        handleClick(item, type) {
            switch (type) {
                case 'edit':
                    this.editObj = item;
                    this.editTeamPopup = true;
                    break;
                case 'delete':
                    this.delData(item.ID);
                    break;
            }
        },
        async delData(id) {
            this.$confirms({
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    await deleteTeam([id]);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
                    this.getdata();
                })
                .catch();
        },
        async getdata() {
            this.loading = true;
            try {
                let resp = await getTeamList({ key: this.teamName, modelid: this.currentDeparment.id });
                this.teamList = resp.response;
                this.loading = false;
            } catch {
                this.loading = false;
            }
        },
        addTeam() {
            this.editObj = {};
            this.editTeamPopup = true;
        },
        closePopup() {
            this.$emit('handlePopup', false, 'team');
        },
        closeEditPopup() {
            this.editTeamPopup = false;
        }
    }
};
</script>

<style>

</style>
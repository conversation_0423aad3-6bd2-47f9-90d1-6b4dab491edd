import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_MATERIAL'
// 关系建模

//不分页获取仓库列表
export function getAgvWarehouseList(data) {
    const api =  '/materail/AgvwarehouseManage/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取AGV仓库列表
export function getAgvwarehouseManageList(data) {
    const api =  '/materail/AgvwarehouseManage/GetWarehouseList'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取AGV仓库库区
export function getWarehouseareaList(data) {
    const api =  '/materail/AgvwarehouseManage/GetWarehouseareaList'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取MES仓库列表
export function getWarehouseManageList(data) {
    const api =  '/materail/WarehouseManage/GetPageList'
    return getRequestResources(baseURL, api, 'post',  data);
}
//不分页获取关系建模列表
export function getWarehouseListByMescode(data) {
    const api =  '/materail/AgvWarehouse/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//分页获取关系建模列表
export function getAgvWarehousePageList(data) {
    const api =  '/materail/AgvWarehouse/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑关系建模
export function AgvWarehouseSaveForm(data) {
    const api =  '/materail/AgvWarehouse/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除关系建模
export function DeleteAgvWarehouse(data) {
    const api =  '/materail/AgvWarehouse/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

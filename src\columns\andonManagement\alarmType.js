// 告警类型
export const alarmTypeColumns = [
    // {
    //     label: '序号',
    //     prop: 'Index',
    //     width: '60px'
    // },

    
    { label: '编码', prop: 'AlarmCode', width: '140px' },
    { label: '名称', prop: 'AlarmName', width: '120px' },
    { label: '问题等级', prop: 'ProblemLevel', width: '100px', type: 'template', template: 'ProblemLevel' },
    { label: '处置类型', prop: 'DealType', width: '100px', type: 'template', template: 'DealType' },
    { label: '发送告警通知', prop: 'MessagePostTag', width: '120px', type: 'template', template: 'MessagePostTag' },
    { label: '发送关警通知', prop: 'OverMessagePostTag', width: '120px', type: 'template', template: 'OverMessagePostTag' },
    { label: '消息模板', prop: 'MessageTemplate', width: '210px', type: 'template', template: 'MessageTemplate' },
    { label: '消息关闭模板', prop: 'OverMessageTemplate', width: '210px', type: 'template', template: 'OverMessageTemplate' },
    { label: 'Uwb消息模板', prop: 'UwbMessageTemplate', width: '200px', type: 'template', template: 'UwbMessageTemplate' },
    // { label: '描述', prop: 'Description', width: '200px' },
    // { label: '最新修改时间', prop: 'ModifyDate', width: '160px' },
    // { label: '最新修改人', prop: 'ModifyUserId', width: '160px' },
    // { label: '物料组', prop: 'MaterialGroup', type: 'template', template: 'MaterialGroup', width: '130px' },
    {
        label: '有效',
        prop: 'Enable',
        width: '90px',
        align: 'center',
        headerAlign: 'center',
        type: 'template',
        template: 'Enable'
    },
    {
        label: '操作',
        prop: 'actions',
        type: 'template',
        width: '150px',
        template: 'actions'
    }
];

// 告警类型
export const alarmSetColumns = [
    { label: '编码', prop: 'PropertyCode', value: "PropertyCode" },
    { label: '属性名称', prop: 'Remark', value: "Remark" },
    { label: '值', prop: 'PropertyValue', value: "PropertyValue", type: 'template', template: 'ProblemLevel' },
    { label: '操作', prop: 'actions', value: "actions", type: 'template', template: 'DealType' },
]
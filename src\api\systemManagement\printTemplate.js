import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url
const baseURL1 = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url
//获取模板列表
export function PrintTplGetPageList(data) {
    return request({
        url: baseURL + '/api/PrintTpl/GetPageList',
        method: 'post',
        data
    });
}

//新增&保存
export function PrintTplSaveForm(data) {
    return request({
        url: baseURL + '/api/PrintTpl/SaveForm',
        method: 'post',
        data
    });
}
//保存模板
export function PrintTplSaveTPL(data) {
    return request({
        url: baseURL + '/api/PrintTpl/SaveTPL',
        method: 'post',
        data
    });
}

// 删除
export function PrintTplDelete(data) {
    return request({
        url: baseURL + '/api/PrintTpl/Delete',
        method: 'post',
        data
    });
}

// 打印属性扩展
// 获取属性列表
export function PrintTplPropertyGetPageList(data) {
    return request({
        url: baseURL + '/api/PrintTplProperty/GetPageList',
        method: 'post',
        data
    });
}
// 保存
export function PrintTplPropertySaveForm(data) {
    return request({
        url: baseURL + '/api/PrintTplProperty/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function PrintTplPropertyDelete(data) {
    return request({
        url: baseURL + '/api/PrintTplProperty/Delete',
        method: 'post',
        data
    });
}

// 告警类型
<template>
    <v-dialog v-model="dialog" persistent max-width="980px">
    <v-card>
        <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
            {{ operaObj.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ') }}
            <v-icon @click="closePopup">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid" class="mt-8">
                <v-row>
                    <!-- 编码 -->
                    <v-col :cols="12" :lg="4">
                        <v-text-field v-model="form.AlarmCode" :label="$t('$vuetify.dataTable.ANDON_BJLXGL.AlarmCode')" :rules="rules.AlarmCode" required dense outlined></v-text-field>
                    </v-col>
                    <!-- 名称 -->
                    <v-col :cols="12" :lg="4">
                        <v-text-field v-model="form.AlarmName" :label="$t('$vuetify.dataTable.ANDON_BJLXGL.AlarmName')" :rules="rules.AlarmName" required dense outlined></v-text-field>
                    </v-col>
                    <!-- 大类 -->
                    <v-col :cols="12" :lg="4">
                        <v-select
                            v-model="form.ParentId"
                            :rules="rules.ParentId"
                            :items="typeRootList"
                            item-text="AlarmName"
                            item-value="ID"
                            :label="$t('$vuetify.dataTable.ANDON_BJLXGL.ParentId')"
                            :disabled="!!operaObj.ID && !form.ParentId"
                            persistent-hint
                            return-object
                            dense
                            outlined
                            @change="changeV"
                        ></v-select>
                    </v-col>
                    <!-- 问题等级 -->
                    <v-col :cols="12" :lg="4">
                        <v-select
                            v-model="form.ProblemLevel"
                            :rules="rules.ProblemLevel"
                            :items="problemLevelList"
                            item-text="ItemName"
                            item-value="ItemValue"
                            :label="$t('$vuetify.dataTable.ANDON_BJLXGL.ProblemLevel')"
                            persistent-hint
                            @change="changeP"
                            dense
                            outlined
                        ></v-select>
                    </v-col>
                    <!-- 处置类型 -->
                    <v-col :cols="12" :lg="4">
                        <v-select
                            v-model="form.DealType"
                            :rules="rules.DealType"
                            :items="dealTypeList"
                            item-text="ItemName"
                            item-value="ItemValue"
                            :label="$t('$vuetify.dataTable.ANDON_BJLXGL.DealType')"
                            clearable
                            dense
                            outlined
                        ></v-select>
                    </v-col>
                    <!-- 图标 -->
                    <v-col :cols="12" :lg="4">
                        <v-text-field v-model="form.Icon" :label="$t('$vuetify.dataTable.ANDON_BJLXGL.Icon')" required dense outlined></v-text-field>
                    </v-col>
                    <!-- 排序 -->
                    <v-col :cols="12" :lg="4">
                        <v-text-field :rules="rules.Sort" v-model="form.Sort" :label="$t('$vuetify.dataTable.ANDON_BJLXGL.Sort')" required dense outlined></v-text-field>
                    </v-col>
                    <!-- 发送告警通知 -->
                    <v-col :cols="12" :lg="4">
                        <v-select
                            v-model="form.MessagePostTag"
                            :items="postTagList"
                            item-text="ItemName"
                            item-value="ItemValue"
                            :label="$t('$vuetify.dataTable.ANDON_BJLXGL.MessagePostTag')"
                            dense
                            outlined
                        ></v-select>
                    </v-col>
                    <!-- 发送关警通知 -->
                    <v-col :cols="12" :lg="4">
                        <v-select
                            v-model="form.OverMessagePostTag"
                            :items="postTagList"
                            item-text="ItemName"
                            item-value="ItemValue"
                            :label="$t('$vuetify.dataTable.ANDON_BJLXGL.OverMessagePostTag')"
                            dense
                            outlined
                        ></v-select>
                    </v-col>
                    <!-- 消息模板 -->
                    <v-col :cols="12" :lg="12">
                        <v-textarea v-model="form.MessageTemplate" :rules="rules.MessageTemplate" rows="4" :label="$t('$vuetify.dataTable.ANDON_BJLXGL.MessageTemplate')" dense outlined></v-textarea>
                    </v-col>
                    <!-- 消息关闭模板 -->
                    <v-col :cols="12" :lg="12">
                        <v-textarea v-model="form.OverMessageTemplate" :rules="rules.OverMessageTemplate" rows="4" :label="$t('$vuetify.dataTable.ANDON_BJLXGL.OverMessageTemplate')" dense outlined></v-textarea>
                    </v-col>
                    <!-- 消息模板 -->
                    <v-col :cols="12" :lg="12">
                        <v-textarea v-model="form.UwbMessageTemplate" :rules="rules.UwbMessageTemplate" rows="4" :label="$t('$vuetify.dataTable.ANDON_BJLXGL.UwbMessageTemplate')" dense outlined></v-textarea>
                    </v-col>
                    <v-col :cols="12" :lg="12">
                        <v-textarea v-model="form.Description" rows="2" :label="$t('$vuetify.dataTable.ANDON_BJLXGL.Description')" dense outlined></v-textarea>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>

        <v-divider></v-divider>

        <v-card-actions>
            <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
    </v-dialog>
</template>

<script>
import { AlarmTypeSaveForm } from '@/api/andonManagement/alarmType.js';
const   MT = '${CurrentName}：您负责的 [${LineName}] - [${Station}] 于 [${CreateDate}] 发生了：[${Content}]，请于收到消息起 [${Duration}] 分钟到指定产线处理，超时将升级至 [${PostName}]。目前处于 [${CurLevel}]级告警 ,当前报警信息 [${IsUpgrade}] 升级。',
        OMT = '${CurrentName}：您负责的 [${LineName}]发生的 [${MainAlarmType}]-[${SubAlarmType}] 已于[${OverDate}]关闭。';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        typeRootList: {
            type: Array,
            default: () => []
        },
        dealTypeList: {
            type: Array,
            default: () => []
        },
        problemLevelList: {
            type: Array,
            default: () => []
        },
        AndonMessageTemplateList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            dialog: false,
            valid: true,
            checkbox: true,
            form: {
                ID: '',
                AlarmCode: '',
                AlarmName: '',
                ParentId: '',
                ParentCode: '',
                Description: '',
                ProblemLevel: '',
                MessageTemplate: '',
                OverMessageTemplate: '',
                UwbMessageTemplate: '',
                Icon: '',
                DealType: '',
                Sort: '',
                MessagePostTag:  '0',
                OverMessagePostTag: '0'
            },
            rules: {
                Sort: [v => !!v || v === 0 || this.$t('GLOBAL._MANDATORY')],
                AlarmCode: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                ProblemLevel: [v => !(this.form.ParentId && !v ) || this.$t('GLOBAL._MANDATORY')],
                MessageTemplate: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                OverMessageTemplate: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                UwbMessageTemplate: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                AlarmName: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            },
            postTagList: [
                {ItemName: '是', ItemValue: '1'},
                {ItemName: '否', ItemValue: '0'}
            ]
        };
    },
    watch: {
        operaObj: {
            handler(curVal) {
                for (const key in this.form) {
                    if (Object.hasOwnProperty.call(this.form, key)) {
                        this.form[key] = curVal[key];
                    }
                }
                this.form.Icon = curVal.icon;
                const { MessagePostTag, OverMessagePostTag } = curVal;
                // 如果没有值则默认给否
                this.form.MessagePostTag = MessagePostTag || '0';
                this.form.OverMessagePostTag = OverMessagePostTag || '0';
                // 消息模板赋值
                let MessageTemplate = '', OverMessageTemplate = '';
                this.AndonMessageTemplateList.forEach(e => {
                    if(e.ItemValue == 1) MessageTemplate = e.Description;
                    if(e.ItemValue == 2) OverMessageTemplate = e.Description;
                });
                this.form.MessageTemplate = this.operaObj.MessageTemplate || MessageTemplate || MT;
                this.form.UwbMessageTemplate = this.operaObj.UwbMessageTemplate || MessageTemplate || MT;
                this.form.OverMessageTemplate = this.operaObj.OverMessageTemplate || OverMessageTemplate || OMT;
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        changeV(){ 
            this.form.Icon = ''
        },
        changeP(){
            this.$refs.form.validate()
        },
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                const { ParentId } = this.form
                let o = {}
                const { AlarmCode, ID } = ParentId || {}
                if(AlarmCode){
                    o = {ParentId: ID, ParentCode: AlarmCode}
                }
                const res = await AlarmTypeSaveForm({...this.form, ...o});
                const { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    this.$refs.form.reset();
                    this.$emit('handlePopup', 'refresh');
                    if (this.operaObj.ID || this.checkbox) {
                        this.closePopup()
                    }
                }
            }
        },
        closePopup() {
            this.$refs.form.reset();
            this.dialog = false;
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-4.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>

export function getlineBardata(xdata, LineData, Bardata) {
    let option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        legend: {
            show: true
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                data: xdata,
                axisPointer: {
                    type: 'shadow'
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '数量',
                axisLine: {
                    show: false,
                },
                splitLine: {
                    show: false,
                },
                axisLabel: {
                    formatter: '{value}'
                }
            },
            {
                type: 'value',
                name: '累计占比',
                axisLine: {
                    show: false,
                },
                axisLabel: {
                    formatter: '{value} %'
                }
            }
        ],
        series: [
            {
                name: '数量',
                type: 'bar',
                barMaxWidth: "80px",
                itemStyle: {
                    color: "#3dcd58"
                },
                tooltip: {
                    valueFormatter: function (value) {
                        return value;
                    }
                },
                data: Bardata
            },
            {
                name: '累计占比',
                type: 'line',
                yAxisIndex: 1,
                itemStyle: {
                    color: "#0074e0"
                },
                label: {
                    show: true,
                    formatter: (e) => {
                        return e.value + ' %';
                    },
                },
                tooltip: {
                    valueFormatter: function (value) {
                        return value + ' %';
                    }
                },
                data: LineData
            }
        ]
    };
    return option
}
<template>
    <div>
        <v-card>
            <v-card-title class="headline primary lighten-2" primary-title>
                {{ this.editItemObj.ID ? '编辑事故' : '新增事故' }}
            </v-card-title>
            <v-card-text style="padding: 0 10px;">
                <v-form ref="form" v-model="valid">
                    <v-row class="mt-5">
                        <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                            <v-menu :close-on-content-click="true" :nudge-right="40" transition="scale-transition"
                                offset-y max-width="290px" min-width="290px">
                                <template #activator="{ on, attrs }">
                                    <v-text-field v-model="form.PresentDate" :clearable="true" outlined dense
                                        label="发起日期" readonly v-bind="attrs" v-on="on">
                                    </v-text-field>
                                </template>
                                <v-date-picker v-model="form.PresentDate" placeholder="发起日期" :locale="locale">
                                </v-date-picker>
                            </v-menu>
                        </v-col>
                        <v-col :cols="12" :lg="6" class="pt-0 pb-0  mb-5 ">
                            <Treeselect @select="getTeamList" disableBranchNodes v-model="form.PresentProdProcessId"
                                placeholder="事故所在产线" noChildrenText="暂无数据" noOptionsText="暂无数据"
                                :default-expand-level="4" :normalizer="normalizer" :options="EquipmentProductLineTree"
                                :rules="rules" />
                        </v-col>
                        <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                            <!-- <Treeselect v-model="form.TeamId" placeholder="发现班组" noChildrenText="暂无数据"
                                noOptionsText="暂无数据" :default-expand-level="4" :normalizer="normalizer"
                                :options="EquipmentTeamTree" @select="handleChangeSelectTree" :rules="rules" /> -->
                            <v-select v-model="form.TeamId" :items="teamList" label="发现班组" item-text="Name"
                                item-value="ID" required dense outlined></v-select>
                        </v-col>
                        <v-col :cols="12" :lg="6" class="pt-0 pb-0 ">
                            <!-- <Treeselect
                                v-model="form.PresentUserId"
                                placeholder="提出人"
                                noChildrenText="暂无数据"
                                noOptionsText="暂无数据"
                                :default-expand-level="4"
                                :normalizer="normalizer"
                                :options="companyTree"
                                @select="handleChangeSelectTree"
                                :rules="rules"
                            />  -->
                            <!-- <v-select 
                                v-model="form.PresentUserId" 
                                :items="StaffList" 
                                label="提出人"
                                item-text="Name" 
                                item-value="Code"
                                required dense outlined
                                ></v-select> -->
                            <v-autocomplete v-model="form.PresentUserId" :items="StaffList" item-text="Name"
                                item-value="Code" clearable dense outlined label="提出人" placeholder="提出人">
                            </v-autocomplete>
                        </v-col>
                        <v-col :lg="6" class="pt-0 pb-0 ">
                            <v-select v-model="form.ClassfyCode" :items="ClassfyCodeList" label="事故类型"
                                item-text="ItemName" item-value="ItemValue" required dense outlined></v-select>
                        </v-col>
                        <v-col :cols="12" :lg="6" class="pt-0 pb-0 ">
                            <Treeselect v-model="form.ResponsibleDepartmentId" disableBranchNodes placeholder="责任部门"
                                noChildrenText="暂无数据" noOptionsText="暂无数据" :default-expand-level="4"
                                :normalizer="normalizer" :options="EquipmentProductLineTree"
                                @select="handleChangeSelectTree" :rules="rules" />
                            <!-- <v-select v-model="form.ResponsibleDepartmentId" :items="DepartmentList" label="责任部门"
                                item-text="Fullname" item-value="ID" required dense outlined></v-select> -->
                        </v-col>
                        <v-col :cols="12" :lg="6" class="pt-0 pb-0 ">
                            <!-- <Treeselect
                                v-model="form.ResponsibleUserId"
                                placeholder="负责人"
                                noChildrenText="暂无数据"
                                noOptionsText="暂无数据"
                                :default-expand-level="4"
                                :normalizer="normalizer"
                                :options="companyTree"
                                @select="handleChangeSelectTree"
                                :rules="rules"
                            />  -->
                            <!-- <v-select 
                                v-model="form.ResponsibleUserId" 
                                :items="StaffList" 
                                label="负责人"
                                item-text="Name" 
                                item-value="Code"
                                required dense outlined
                                ></v-select> -->
                            <v-autocomplete v-model="form.ResponsibleUserId" :items="StaffList" item-text="Name"
                                item-value="Code" clearable dense outlined label="负责人" placeholder="负责人">
                            </v-autocomplete>
                        </v-col>

                        <v-col :cols="12" :lg="6" class="pt-0 pb-0  ">
                            <v-menu :close-on-content-click="true" :nudge-right="40" transition="scale-transition"
                                offset-y max-width="290px" min-width="290px">
                                <template #activator="{ on, attrs }">
                                    <v-text-field v-model="form.PlanFinishDate" :clearable="true" outlined dense
                                        label="计划完成日期" readonly v-bind="attrs" v-on="on">
                                    </v-text-field>
                                </template>
                                <v-date-picker v-model="form.PlanFinishDate" placeholder="计划完成日期" :locale="locale">
                                </v-date-picker>
                            </v-menu>
                        </v-col>
                        <v-col v-if="editItemObj.ID" :cols="12" :lg="6" class="pt-0 pb-0   ">
                            <v-menu :close-on-content-click="true" :nudge-right="40" transition="scale-transition"
                                offset-y max-width="290px" min-width="290px">
                                <template #activator="{ on, attrs }">
                                    <v-text-field v-model="form.ActualFinishDate" :clearable="true" outlined dense
                                        label="实际完成日期" readonly v-bind="attrs" v-on="on">
                                    </v-text-field>
                                </template>
                                <v-date-picker v-model="form.ActualFinishDate" placeholder="实际完成日期" :locale="locale">
                                </v-date-picker>
                            </v-menu>
                        </v-col>
                        <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                            <v-textarea v-model="form.AccidentDesc" clearable label="问题描述" outlined dense
                                required></v-textarea>
                        </v-col>
                        <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                            <v-textarea v-model="form.MeasureDesc" clearable label="采取的措施" outlined dense
                                required></v-textarea>
                        </v-col>

                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions style="justify-content: flex-end;">
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="cancel">{{ $t('GLOBAL._QX') }}</v-btn>
            </v-card-actions>
        </v-card>
    </div>
</template>
<script>
import dayjs from 'dayjs'
import { AddSafeAccident, getTeam } from '@/views/simManagement/sim1/service.js';

export default {
    props: {
        curTeamTreeObj: {
            type: Object,
            default: () => { }
        },
        searchFormObj: {
            type: Object,
            default: () => { }
        },
        editItemObj: {
            type: Object,
            default: () => { }
        },
    },
    data() {
        return {
            teamList: [],
            valid: false,//校验
            // isChecked: true,//确定并关闭
            form: {
                ResponsiblePlantId: '',
                PresentPlantId: '',
                PresentProdProcessId: this.curTeamTreeObj?.ProductionLineCode || undefined, // 事故所在产线
                ClassfyCode: '',
                ID: undefined,
                PresentDate: dayjs().format('YYYY-MM-DD'),// 提起时间
                // PresentDepartmentId:  undefined,//提起部门
                PresentUserId: this.$store.getters.getUserinfolist[0].UserNo || undefined,//提出人
                ResponsibleDepartmentId: undefined,// 责任部门ID
                ResponsibleUserId: undefined,// 责任人ID
                TeamId: this.curTeamTreeObj?.TeamCode || undefined,//发现班组ID
                PlanFinishDate: '',//计划完成日期
                ActualFinishDate: dayjs().format('YYYY-MM-DD'),//实际完成日期
                AccidentDesc: '',//事故描述
                MeasureDesc: '',//采取的措施
            },
            rules: [v => !!v || this.$t('GLOBAL._MANDATORY')],

            departmentData: [],
            normalizer(node) {
                return {
                    id: node.id,
                    label: node.name,
                    children: node.children
                };
            },
        }
    },
    computed: {
        locale() {
            return this.$store.state.app.locale || 'zh';
        },
        companyTree() {
            return this.$store.state.sim.companyTree
        },
        //工序
        EquipmenList() {
            return this.$store.state.sim.EquipmenList
        },
        EquipmentTeamTree() {
            return this.$store.state.sim.EquipmentTeamTree
        },
        //产线树
        EquipmentProductLineTree() {
            return this.$store.getters.EquipmentProductLineTree
        },
        StaffList() {
            return this.$store.state.sim.StaffList
        },
        DepartmentList() {
            return this.$store.state.sim.DepartmentList
        },
        ClassfyCodeList() {
            return this.$store.state.sim.ClassfyCodeList
        }
    },
    async created() {
        if (!this.editItemObj?.ID && this.curTeamTreeObj?.ProductionLineCode) {
            this.form.ResponsiblePlantId = this.curTeamTreeObj?.FactoryCode
            this.form.PresentPlantId = this.curTeamTreeObj?.FactoryCode
            let resp = await getTeam({ modelid: this.curTeamTreeObj?.ProductionLineCode, "key": "" })
            this.teamList = resp.response
        }
        this.echo()
        // this.form.TeamId = this.searchFormObj.PresentDepartmentId
    },
    methods: {
        async getTeamList(val) {
            this.form.ResponsiblePlantId = val.parentId
            this.form.PresentPlantId = val.parentId
            let { id } = val
            let resp = await getTeam({ modelid: id, "key": "" })
            this.teamList = resp.response
        },
        // 编辑回显
        echo() {
            if (this.editItemObj && this.editItemObj.ID) {
                for (const key in this.form) {
                    if (this.editItemObj[key]) {
                        this.form[key] = this.editItemObj[key];
                    }
                }
                if (this.form.PresentProdProcessId) {
                    this.getTeamList(this.form.PresentProdProcessId)
                }
            }
        },
        cancel() {
            this.$emit('closePopup');
        },
        async submitForm() {
            // let params = 
            let res = await AddSafeAccident(this.form)
            if (res.success) {
                this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
                this.$emit('addDown');
            }
        },
        handleChangeSelectTree(val) {

        },
    }
}
</script>
<style></style>

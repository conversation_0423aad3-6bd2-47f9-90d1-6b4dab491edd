<!DOCTYPE html>
<html lang="en">

<head>
  <meta http-equiv="pragma" content="no-cache">
  <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="expires" content="0">
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <!--meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests"-->

  <title></title>
  <!-- <script src="https://unpkg.com/echarts@4.0.4/dist/echarts.min.js"></script> -->
  <script src="/font/iconfont.js"></script>
  <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon">
  <meta name="description" content="数字化工厂管理平台">
  <meta name="keywords" content="数字化工厂管理平台">
  <link href="./static/css/print-lock.css" media="print" rel="stylesheet">
  <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vxe-table@legacy/lib/style.css"> -->
  <style>
    .v-snack {
      z-index: 99999 !important;
    }
  </style>
</head>

<body>
  <noscript>
    <strong>We're sorry but vma doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
  </noscript>
  <div id="app"></div>

  <!-- <script async src="https://www.googletagmanager.com/gtag/js?id=UA-3881136-11"></script> -->
  <script>
    window.onload = function () {
      let titleHtml = document.getElementsByTagName('title')[0]
      try {
        let language = JSON.parse(window.sessionStorage.vma).app.locale
        titleHtml.innerText = language == 'cn' ? '数字工厂' : 'Digital Factory'
      } catch {
        titleHtml.innerText = '数字工厂'
      }
    }
    // window.dataLayer = window.dataLayer || [];
    // function gtag() { dataLayer.push(arguments); }
    // gtag('js', new Date());
    // gtag('config', 'UA-3881136-11');
  </script>
  <!-- <script type="text/javascript">window.$crisp=[];window.CRISP_WEBSITE_ID="aca57ccd-5047-463a-a43d-9b8c58368e78";(function(){d=document;s=d.createElement("script");s.src="https://client.crisp.chat/l.js";s.async=1;d.getElementsByTagName("head")[0].appendChild(s);})();</script>   -->
</body>

</html>
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM'

// 物料分类表单保存
export function saveClassify(data) {
    const api = '/api/Category/SaveForm'
    return getRequestResources(baseURL, api, 'post', data)
}

// 查询物料分类列表
export function getClassifyList(data) {
    const api = '/api/Category/GetPageList'
    return getRequestResources(baseURL, api, 'post', data)
}

// 删除物料分类
export function delClassify(data) {
    const api = '/api/Category/Delete'
    return getRequestResources(baseURL, api, 'post', data)
}

//获取数据字典明细 根据分类编号
export function getDataDictionary(data) {
    const api = '/api/DataItemDetail/GetList?lang=cn&&itemCode=' + data.itemCode
    return getRequestResources(baseURL, api, 'post', data)
}

// 查询物料分类下拉框列表
export function getSelectClassifyList(data) {
    const api = '/api/Category/GetList?Identities=' + data.Identities
    return getRequestResources(baseURL, api, 'post', data)
}

// 获取套件列表
export function GetMaterialClassList(data) {
    const api = '/api/Material/GetMaterialClassList'
    return getRequestResources(baseURL, api, 'post', data)
}


// 更新套件
export function SaveMaterialMapping(data) {
    const api = '/api/Material/SaveMaterialMapping'
    return getRequestResources(baseURL, api, 'post', data)
}

// 获取属性列表
export function GetPropertyValuePageList(data) {
    const api = '/api/MaterialPropertyValue/GetPageList'
    return getRequestResources(baseURL, api, 'post', data)
}
// 新增属性列表
export function MaterialSaveForm(data) {
    const api = '/api/MaterialPropertyValue/SaveForm'
    return getRequestResources(baseURL, api, 'post', data)
}

// 删除属性
export function MaterialDelete(data) {
    const api = '/api/MaterialPropertyValue/Delete'
    return getRequestResources(baseURL, api, 'post', data)
}

// 获取厂别下拉框数据
export function getFactoryList(data) {
    const api = '/api/Equipment/GetListByLevel'
    return getRequestResources(baseURL, api, 'post', data)
}

// 获取单位下拉框数据
export function getUnitList(data) {
    const api = '/api/Unitmanage/GetList'
    return getRequestResources(baseURL, api, 'post', data)
}

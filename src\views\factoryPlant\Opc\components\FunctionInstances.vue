<template>
    <div class="function-instances">
        <div class="search-row">
            <v-row>
                <v-col :cols="12" :lg="2">
                    <!-- <a-select @change="getdata" style="width:100%" v-model="machine" placeholder="Machine">
                        <a-select-option value="General">General</a-select-option>
                    </a-select> -->
                    <a-tree-select @change="getdata" v-model="machine" style="width: 100%"
                        :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                        :replaceFields="{ children: 'children', title: 'name', key: 'id', value: 'id' }"
                        :tree-data="equipTree" placeholder="Machine">
                        <!-- <template #title="{ key, value }">
                            <span style="color: #08c" v-if="key === '0-0-1'">Child Node1 {{ value }}</span>
                        </template> -->
                    </a-tree-select>
                </v-col>
                <v-col :cols="12" :lg="2">
                    <a-select show-search @change="getdata" style="width:100%" v-model="functions" placeholder="Function">
                        <a-select-option v-for="item in funcList" :key="item.ID" :value="item.ID">{{ item.Name
                            }}</a-select-option>
                    </a-select>
                </v-col>
                <v-col :cols="12" :lg="2">
                    <a-select @change="getdata" style="width:100%" v-model="enabled" placeholder="Enabled">
                        <a-select-option value="true">Enabled</a-select-option>
                        <a-select-option value="false">Disabled</a-select-option>
                    </a-select>
                </v-col>
            </v-row>
        </div>
        <div class="tool-row">
            <v-row>
                <v-col :cols="12" :lg="3">
                    <a-input-search v-model="keywords" enter-button placeholder="Quick Search" @search="getdata" />
                </v-col>
                <v-col :cols="12" :lg="9" class="pl-0">
                    <v-btn @click="getdata">
                        <v-icon left>mdi-cached</v-icon>
                        Refresh</v-btn>
                    <v-btn class="ml-3" @click="reset()">
                        <v-icon left>mdi-cached</v-icon>
                        Reset</v-btn>
                    <v-btn class="ml-3" color="primary" @click="openPopup()">
                        <v-icon left>mdi-plus</v-icon>
                        New</v-btn>
                    <v-btn class="ml-3" @click="handleStatus(true)">
                        <v-icon left>mdi-check</v-icon>
                        Enable</v-btn>
                    <v-btn class="ml-3" @click="handleStatus(false)">
                        <v-icon left>mdi-cancel</v-icon>
                        Disable</v-btn>
                    <!-- <v-btn class="ml-3">
                        <v-icon left>mdi-download</v-icon>
                        Export</v-btn>
                    <v-btn class="ml-3" color="primary">
                        <v-icon left>mdi-upload</v-icon>
                        Import</v-btn> -->
                </v-col>
            </v-row>
        </div>
        <div class="table-box" style="height:calc(100vh - 200px)">
            <vxe-table height="auto" :expand-config="{
                    accordion: true, lazy: true, reserve: true,
                    loadMethod: getPropertyList,
                }" :loading="loading" size="mini" border resizable ref="xTree" :data="tableList">
                <vxe-column v-for="(column, index) in functionInstancesColumn" :key="index" :width="column.width"
                    :field="column.field" :title="column.title" :type="column.type">
                    <template #default="{ row }">
                        <span v-if="column.field == 'Action'">
                            <v-icon @click="editFunctionInstance(row)" style="cursor: pointer;" color="#3dcd58"
                                size="18">mdi-pencil</v-icon>
                            <v-icon @click="handleDel(row)" style="margin-left:5px;cursor: pointer;" color="#3dcd58"
                                size="18">mdi-delete</v-icon>
                        </span>
                        <span v-else-if="column.field == 'text'">
                            <v-icon @click="openProperty(row)" style="cursor: pointer;" color="primary"
                                size="18">mdi-clipboard-text-outline</v-icon>
                        </span>
                        <span v-else-if="column.field == 'OpcFunctionId'">
                            {{ getFuncText(row.OpcFunctionId) }}
                        </span>
                        <span v-else-if="column.field == 'EquipmentId'">
                            {{ getEquipText(row.EquipmentId) }}
                        </span>
                        <span style="display:inline-block;width:85px"
                            v-else-if="['IsEnabled', 'Notifications'].indexOf(column.field) !== -1">
                            <span class="enabled" v-if="row[column.field] == 'true'">True</span>
                            <span class="disabled" v-if="row[column.field] == 'false'">False</span>
                        </span>
                        <span v-else>{{ row[column.field] }}</span>
                    </template>
                    <template #content="{ row }">
                        <div class="expand-box" style="padding:10px">
                            <vxe-table ref="expandTable" border resizable :data="row.childData">
                                <vxe-column v-for="(column, index) in newPropertiesColumns" :key="index"
                                    :width="column.width" :field="column.field" :title="column.title"
                                    :type="column.type">
                                    <template #default="{ row }">
                                        <span v-if="column.field == 'IsBound' || column.field == 'IsSubscribed'">{{
                    row[column.field]
                        == '1' ? '是' : '否'
                }}</span>
                                        <span v-else-if="column.field == 'Name'">
                                            <v-icon @click="editProperty(row)" style="cursor: pointer;" color="primary"
                                                size="18">mdi-clipboard-text-outline</v-icon>
                                            {{ row[column.field] }}</span>
                                        <span v-else>{{ row[column.field] }}</span>
                                    </template>
                                </vxe-column>
                            </vxe-table>
                        </div>
                    </template>
                </vxe-column>
            </vxe-table>
        </div>
        <a-modal :visible="isShowPopup" :title="popupTitle" @cancel="handleCancel" @ok="handleOk">
            <functionInstancesPopup v-if="isShowPopup" :editItemObj="editItemObj" :funcList="funcList"
                :machineList="machineList" ref="functionInstancesPopup">
            </functionInstancesPopup>
        </a-modal>
        <a-modal :visible="isShowEditPopup" :title="'Edit Function Property Instance'" @cancel="isShowEditPopup = false"
            @ok="saveProperty()">
            <editInstanceProperty :currentProperty="currentProperty" v-if="isShowEditPopup" ref="editProperty">
            </editInstanceProperty>
        </a-modal>
        <a-drawer placement="right" width="50%" :closable="false" :visible="isShowDrawer"
            :after-visible-change="afterVisibleChange">
            <instanceProperty @updateExpandData="updateExpandData" @closeDrawer="closeDrawer" :currentObj="currentObj"
                v-if="isShowDrawer" />
        </a-drawer>
    </div>
</template>

<script>
import { functionInstancesColumn, functionInstancesExpandColumn, newPropertiesColumns } from '@/columns/factoryPlant/Opc.js'
import functionInstancesPopup from './functionInstancesPopup.vue'
import instanceProperty from './instanceProperty.vue'
import editInstanceProperty from './editInstanceProperty.vue'
import { GetEquipmentTree } from '@/api/factoryPlant/physicalModel.js';
import { getOpcFuncNoPage, updateFunctionInstanceProperty, addFunctionInstance, getFunctionInstanceList, delFunctionInstance, getNewInstanceProperty, getFunctionNoPage, updateFunctionInstance } from '../service'
import Util from '@/util';
export default {
    components: {
        functionInstancesPopup,
        instanceProperty,
        editInstanceProperty
    },
    data() {
        return {
            editItemObj: {},
            currentProperty: {},
            isShowEditPopup: false,
            currentObj: {},
            machineList: [],
            funcList: [],
            newPropertiesColumns,
            functionInstancesExpandColumn,
            expandDataList: [],
            isShowDrawer: false,
            loading: false,
            isShowPopup: false,
            popupTitle: 'New Function Instance',
            functionInstancesColumn,
            machine: undefined,
            functions: undefined,
            enabled: undefined,
            keywords: '',
            tableList: [],
            equipTree: []
        }
    },
    computed: {

    },
    async created() {
        this.getdata()
        this.getFunc()
        this.getEquipTree()
        this.machineList = await Util.GetEquipmenByLevel('Unit')
    },
    methods: {
        editFunctionInstance(data) {
            this.editItemObj = data
            this.isShowPopup = true
        },
        async saveProperty() {
            let params = this.$refs.editProperty.form
            try {
                const resp = await updateFunctionInstanceProperty([params])
                this.isShowEditPopup = false
                this.$nextTick(() => {
                    this.$store.commit('SHOW_SNACKBAR', { text: '修改成功', color: 'success' });
                    let data = this.$refs.xTree.getRowExpandRecords()[0]
                    this.$refs.xTree.reloadRowExpand(data)
                })
            } catch {
                console.log('error')
            }
        },
        editProperty(data) {
            this.currentProperty = data
            this.isShowEditPopup = true
        },
        updateExpandData(data) {
            // let data = this.$refs.xTree.getRowExpandRecords()[0]
            // console.log("data====", data)
            let keys = Object.keys(data)
            if (keys.indexOf('childData') !== -1) {
                this.$refs.xTree.reloadRowExpand(data)
            }
        },
        async changeExpand({ expanded, column, columnIndex, row }) {
            if (expanded) {
                let resp = await getNewInstanceProperty({ opcFunctionInstanceId: row.ID })
                row.childData = resp.response
            }
            return true
        },
        // 获取树形数据
        async getEquipTree() {
            // this.loading = true
            const res = await GetEquipmentTree({});
            // this.loading = false
            if (res.success) {
                this.equipTree = res.response;
                // this.$refs.xTree.setAllTreeExpand(true)
            } else {
                this.equipTree = [];
            }
        },
        async handleStatus(boolean) {
            let list = this.$refs.xTree.getCheckboxRecords()
            if (list.length == 0) {
                this.$store.commit('SHOW_SNACKBAR', { text: '请选中数据', color: 'error' });
                return false
            }
            let arr = JSON.parse(JSON.stringify(list))
            arr.map(item => {
                item.IsEnabled = boolean
                return item
            })
            try {
                let resp = await updateFunctionInstance(arr)
                this.getdata()
                this.$store.commit('SHOW_SNACKBAR', { text: '修改成功', color: 'success' });
            } catch {
                this.getdata()
                console.log('error')
            }

        },
        openProperty(row) {
            this.currentObj = row
            this.currentObj.FunctionName = this.funcList.find(item => item.ID == row.OpcFunctionId)?.Name
            this.isShowDrawer = true
        },
        getFuncText(id) {
            return this.funcList.find(item => item.ID == id)?.Name || ''
        },
        getEquipText(id) {
            return this.machineList.find(item => item.ID == id)?.EquipmentName || ''
        },
        async getFunc() {
            let resp = await getFunctionNoPage({})
            this.funcList = resp.response
        },
        async getPropertyList({ row }) {
            console.log("row-----", row)
            let resp = await getNewInstanceProperty({ opcFunctionInstanceId: row.ID })
            row.childData = resp.response
        },
        handleDel(data) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: '确认要删除此项吗？',
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await delFunctionInstance([data.ID]);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.getdata()
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        closeDrawer() {
            this.isShowDrawer = false
        },
        afterVisibleChange(val) {
            console.log("val=====", val)
        },
        reset() {
            this.keywords = ''
            this.machine = undefined
            this.functions = undefined
            this.enabled = undefined
            this.getdata()
        },
        openPopup() {
            this.editItemObj = {}
            this.isShowPopup = true
        },
        handleCancel() {
            this.isShowPopup = false
        },
        async handleOk() {
            let params = this.$refs.functionInstancesPopup.form
            const resp = await addFunctionInstance(params)
            this.isShowPopup = false
            this.$nextTick(() => {
                this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });
                this.getdata()
            })
        },
        async getdata() {
            this.loading = true
            try {
                let resp = await getFunctionInstanceList({
                    EquipmentId: this.machine,
                    OpcFunctionId: this.functions,
                    IsEnabled: this.enabled
                })
                this.loading = false
                this.$refs.xTree.removeCheckboxRow()
                this.tableList = resp.response
                // this.tableList = [{ Function: '1212', ID: '12121' }]
            } catch {
                this.loading = false
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.function-instances {
    border: 1px solid #eee;
    border-radius: 5px;

    .search-row {
        padding: 5px 3px;
        background: #f5f5f5;
        border-bottom: 1px solid #eee;
    }

    .tool-row {
        padding: 5px 3px;
        background: #f5f5f5;
    }
}

::v-deep .ant-drawer-body {
    height: 100%;
    padding: 12px 10px 24px;
}

.enabled {
    padding: 1px 5px 2px;
    border-radius: 2px;
    color: #fff;
    background: #008000;
}

.disabled {
    padding: 1px 5px 2px;
    border-radius: 2px;
    color: #fff;
    background: #ff0000;
}
</style>

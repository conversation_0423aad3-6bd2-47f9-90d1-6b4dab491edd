import { getRequestResources } from '@/api/fetch';
import request from '@/util/request';
const QUALITY = 'baseURL_QUALITY',
  DFM = 'baseURL_DFM',
  SHIFT = 'baseURL_SHIFT',
  SIM = 'baseURL_KPI',
  SIM1 = 'SSO_URL'

// 获取数据源下拉框接口
export async function getDataSourceList(data) {
  const api = '/simapi/Kpi/GetKpiList'
  // 根据请求参数生成唯一的缓存键
  const cacheKey = `dataSourceListCache_${JSON.stringify(data)}`;

  try {
    const res = await getRequestResources(SIM, api, 'post', data);
    if (res && res.success) {
      // 请求成功，将结果存入缓存
      localStorage.setItem(cacheKey, JSON.stringify(res.response));
      return res;
    } else {
      // 请求成功但业务逻辑失败，尝试从缓存获取
      const cachedData = localStorage.getItem(cacheKey);
      if (cachedData) {
        console.warn('getDataSourceList 请求业务失败，从缓存中获取数据。');
        return { success: true, response: JSON.parse(cachedData), msg: '从缓存中获取数据' };
      }
      return res; // 缓存中没有，返回原始失败结果
    }
  } catch (error) {
    // 请求失败，从缓存中获取数据
    console.error('getDataSourceList 请求失败，尝试从缓存中获取数据:', error);
    const cachedData = localStorage.getItem(cacheKey);
    if (cachedData) {
      return { success: true, response: JSON.parse(cachedData), msg: '从缓存中获取数据' };
    }
    throw error; // 缓存中没有，抛出原始错误
  }
}

// 查询配置接口
export function getqueryZ(data) {
  const api = '/simapi/PageConfig/GetEntityByCode/' + data.code
  return getRequestResources(SIM, api, 'get', data)
}

// 查询配置接口
export function getqueryZsim2(data) {
  const api = '/simapi/PageConfig/GetEntityByCode/' + data.code
  return getRequestResources(SIM, api, 'get', data)
}

// 查询配置接口
export function getqueryLcr(data) {
  const api = '/simapi/PageConfigDetail/GetEntityByCode/' + data.code
  return getRequestResources(SIM, api, 'get', data)
}


// 配置保存接口
export function getInsert(data) {
  const api = '/simapi/PageConfig/SaveForm'
  return getRequestResources(SIM, api, 'post', data)
}

// 配置保存接口
export function getInsert1(data) {
  console.log(data);

  const api = '/simapi/PageConfigDetail/SaveForm?SimLevel=' + data.Simlevel
  return getRequestResources(SIM, api, 'post', data)
}


// 获取图表数据
export function getChartStructure(data) {
  const api = '/simapi/KpiChartConfig/GetChartStructure'
  return getRequestResources(SIM, api, 'post', data)
}

// 获取配置信息图表数据
export function getPageListConfig(data) {
  const api = '/simapi/PageConfig/GetPageList'
  return getRequestResources(SIM, api, 'post', data)
}

// 获取编辑1-2数据
export function getEditEntityByCodeone(data) {
  const api = '/simapi/PageConfig/GetEntity/' + data.code
  return getRequestResources(SIM, api, 'get')
}

// 获取编辑3-5数据
export function getEditEntityByCode(data) {
  const api = '/simapi/PageConfigDetail/GetEntityByCode/' + data.code
  return getRequestResources(SIM, api, 'get')
}

// 获取表格数据
export function getTableList(data) {
  const api = '/simapi/KpiTableConfig/QuerypositionResultBySql'
  return getRequestResources(SIM, api, 'post', data)
}

// SIM看板配置删除接口
export function delConfigPage(data) {
  const api = '/simapi/PageConfig/Delete'
  return getRequestResources(SIM, api, 'post', data)
}

// Excel导入
export function ImportExcel(simlevel, postion, data) {
  const api = '/simapi/PageConfig/ImportKpiExcel?postion=' + postion + '&simlevel=' + simlevel
  return getRequestResources(SIM, api, 'post', data)
}

// Excel导出
// export function deriveExcel(simlevel, position, chktime, TimeDimension, TeamCode, Mark, Shift) {
//   const api = '/simapi/Kpitag/GetKpiExcel?simlevel=' + simlevel + '&position=' + position + '&chktime=' + chktime + '&TimeDimension=' + TimeDimension + '&TeamCode=' + TeamCode + '&Mark=' + Mark + '&Shift=' + Shift  // return getRequestResources(SIM, api, 'get', data)
//   return request({
//     url: api,
//     method: 'post',
//     data,
//     responseType: 'blob'
//   })
// }
export function deriveExcel(data) {
  const api = '/simapi/Kpitag/GetKpiExcel'
  // return request({
  //   url: api,
  //   method: 'post',
  //   data,
  //   responseType: 'blob'
  // })
  return getRequestResources(SIM, api, 'post', data)
}

// 问题列表导出
export function deriveExcel1(data) {
  const api = '/simapi/SimProblem/GetKpiExcel'
  // return request({
  //   url: api,
  //   method: 'post',
  //   data,
  //   responseType: 'blob'
  // })
  return getRequestResources(SIM, api, 'post', data)
}

// 质量数据 导出
export function deriveExcelzzlss(data) {
  const api = '/simapi/Kpitag/GetKpiExcelForQMS'
  // return request({
  //   url: api,
  //   method: 'post',
  //   data,
  //   responseType: 'blob'
  // })
  return getRequestResources(SIM, api, 'post', data)
}

// 获取tree
export function getTree(data) {
  const api = '/simapi/PlantModel/GetEquipmentTeamTree'
  return getRequestResources(SIM, api, 'post', data)
}

// 获取弹窗数据
export function getDodown(data) {
  console.log(data, 'kkk');
  const api = '/simapi/Kpitag/DodownloadKpiValue'
  return getRequestResources(SIM, api, 'post', data)
}

// 转换图片
export function getImageUel(item, data) {
  const api = '/simapi/PageConfig/GetFileUrl?fileName=' + item
  return getRequestResources(SIM, api, 'get', data)
}

// 物理建模数据查询
export function queryEquipmentTree(data) {
  const api = '/api/Equipment/GetEquipmentTree'
  return getRequestResources(DFM, api, 'post', data)
}

// 用户查询
export function queryUser(data) {
  const api = '/api/Role/GetList'
  return getRequestResources(DFM, api, 'post', data)
}

// 菜单查询
export function queryMenu(data) {
  const api = '/api/Menu/GetMenuTree'
  return getRequestResources(DFM, api, 'post', data)
}

// 获取权限数据
export function getDataPermission(data) {
  const api = '/api/DataPermission/GetPageList'
  return getRequestResources(DFM, api, 'post', data)
}

// 权限保存
export function saveJurisdiction(data) {
  const api = '/api/DataPermission/SaveForm'
  return getRequestResources(DFM, api, 'post', data)
}

// 权限删除
export function delList(data) {
  const api = '/api/DataPermission/Delete'
  return getRequestResources(DFM, api, 'post', data)
}

// 数据字典 颜色配置查询
export function getColorList(data) {
  const api = '/api/DataItemDetail/GetPageList'
  return getRequestResources(DFM, api, 'post', data)
}

// 数据查询 物理模型查询
export function queryKpiData(data) {
  const api = '/simapi/Kpitag/GetKpiTagList'
  return getRequestResources(SIM, api, 'post', data)
}

//数据查询
export function queryTagValuePageList(data) {
  const api = '/simapi/Kpitag/GetTagValuePageList'
  return getRequestResources(SIM, api, 'post', data)
}

//获取数据字典明细 根据分类编号
export function getDataDictionary(data) {
  const api = '/api/DataItemDetail/GetList?lang=cn&&itemCode=' + data.itemCode
  return getRequestResources(DFM, api, 'post', data)
}

// 指标数据导入
export function ImportKpiTagExcel(tagCode, data) {
  const api = '/simapi/PageConfig/ImportKpiTagExcel?tagCode=' + tagCode
  return getRequestResources(SIM, api, 'post', data)
}

// 成本中心投入物料管理主页数据查询接口
export function getTeamWlList(data) {
  const api = '/simapi/TeamMaterial/GetPageList'
  return getRequestResources(SIM, api, 'post', data)
}

//成本中心投入物料管理主页数据查询 删除接口
export function deleteTeam(data) {
  const api = '/simapi/TeamMaterial/Delete'
  return getRequestResources(SIM, api, 'post', data)
}

// 成本中心投入物料管理 新增保存接口
export function saveTrForm(data) {
  const api = '/simapi/TeamMaterial/SaveForm'
  return getRequestResources(SIM, api, 'post', data)
}

// 复制保存接口
export function saveCopyConfig(data) {
  const api = '/simapi/PageConfig/CopyConfig'
  return getRequestResources(SIM, api, 'post', data)
}

// 获取token接口
export function getauthorizeCode(data) {
  const api = '/api/Login/CallBack'
  return getRequestResources(DFM, api, 'post', data)
}

// 追踪事项日志查询接口
export function getProblemLog(data) {
  const api = '/simapi/SimProblem/GetProblemLogList'
  return getRequestResources(SIM, api, 'post', data)
}

//模型数据右侧查询
export function getPageListmx(data) {
  const api = '/simapi/KpiValue/GetPageList'
  return getRequestResources(SIM, api, 'post', data)
}

// 查询成品料号列表
export function getMaterialList(data) {
  const api = '/api/Material/GetPageList'
  return getRequestResources(DFM, api, 'post', data)
}

// 根据特定日期重新计算
export function getReCalFromDate(data) {
  const api = '/simapi/KpiValue/ReCalFromDate'
  return getRequestResources(SIM, api, 'post', data)
}

// 模型目标值设置查询接口
export function getListTarGet(key, mark, factory, data) {
  const api = '/simapi/KpiValue/GetListTarGet?key=' + key + '&mark=' + mark + '&factory=' + factory
  return getRequestResources(SIM, api, 'post', data)
}

// 模型目标值设置保存接口
export function saveTargetForm(data) {
  const api = '/simapi/KpiValue/SaveTargetForm'
  return getRequestResources(SIM, api, 'post', data)
}

// 模型目标值删除
export function deleteTarget(data) {
  const api = '/simapi/KpiValue/DeleteTarget'
  return getRequestResources(SIM, api, 'post', data)
}

// 获取字典分类列表
export function getClassifyList(key, data) {
  const api = '/api/DataItem/GetClassifyList?key=' + key
  return getRequestResources(DFM, api, 'post', data)
}

// SIM3升级通知
export function getSim3LevelUp(data) {
  const api = '/simapi/SimProblem/Sim3LevelUp'
  return getRequestResources(DFM, api, 'post', data)
}

// 获取用户
export function getStaffByDepartId(data) {
  const api = '/api/Userinfo/GetPageList'
  return getRequestResources(SIM, api, 'post', data)
}
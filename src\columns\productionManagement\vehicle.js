export const vehicleColum = [
    {
        text: '序号',
        value: 'Index',
        width: '80px'
    },
    // { text: '组别', value: 'GroupStatus', width: '160px' },
    { text: '载具码', value: 'VehicleCode', width: '200px' },
    { text: '单体码', value: 'Sn', width: '200px' },
    { text: '产品码', value: 'BoxSn', width: '200px' },
    { text: '状态', value: 'BindStatusTxt', width: '160px' },
    // { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    // { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '绑定时间', value: 'BindTime', width: '160px' }
    // { text: '创建人', value: 'CreateUserId', width: '120px' },
    // {
    //     text: '操作',
    //     width: '120',
    //     align: 'center',
    //     value: 'actions',
    // }
];
<template>
  <div
    class="zBox"
    :style="zBox"
    ref="zBox"
  >
    <!-- 标题区域 -->
    <div :style="bigBox_top">
      <div
        class="searchBox"
        style="display: flex;"
      >
        <!-- <searchPage
          :Searchconfigs="Searchconfig"
          :key="searchPageKey"
        /> -->
        <el-date-picker
          v-model="BaseTime"
          type="date"
          placeholder="选择日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          @change="handleDateChange"
          :append-to-body="false"
          :picker-options="setDisabled"
        >
        </el-date-picker>
        <!-- <Treeselect
          style="width: 100%;height: 60px;"
          v-model="form.TeamId"
          placeholder="班组"
          noChildrenText="暂无数据"
          noOptionsText="暂无数据"
          :default-expand-level="4"
          :normalizer="normalizer"
          :options="OriEquipmentTeamTree"
          disableBranchNodes
          @select="handleCheck1"
        /> -->
        <Treeselect
          style="width: 100%;height: 60px;"
          v-model="form.TeamId"
          placeholder="所属部门"
          noChildrenText="暂无数据"
          noOptionsText="暂无数据"
          :default-expand-level="4"
          :normalizer="normalizer"
          :options="EquipmentProductLineTree1"
          disableBranchNodes
          @select="handleCheck1"
        />
      </div>
      <div class="charTitBox">{{bulletinBoardTit}}</div>
      <div class="timeBox">{{timeStr}}</div>
      <div
        class="imgBox"
        @click="screen"
      >
        <img
          v-show="fullscreen"
          style="width:100%;height:100%;cursor: pointer;"
          src="../simNew1/image/suox.png"
        >
        <img
          v-show="!fullscreen"
          style="width:100%;height:100%;cursor: pointer;"
          src="../simNew1/image/qpimg.png"
        >
      </div>
    </div>

    <!-- 搜索区域 -->
    <!-- <div class="searchBox">
      <searchPage
        :searchData="searchData"
        @searchClick="clickSearch"
      />
    </div> -->

    <!-- 图表区域 -->
    <div
      class="charBox"
      v-if="barTransverseChartFlag"
    >
      <div
        class="chartBox_l"
        :style="leftStyle"
      >
        <div
          :style="item.style"
          v-for="(item,index) in Leftjosn"
          :key="index"
          :id="`chart-${index+1}`"
          class="box123"
        >
          <!-- 九宫格 -->
          <!-- :Position="searchFormObj.PresentDepartmentId + '-SIM2-Table-2'" -->
          <template v-if="item.OlineType.Encode == '11'">
            <!-- <div
              style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div> -->
            <div
              class="titimgbox"
              @click="routeChange(item.routePage?.TagCode)"
            >
              <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
              <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
            </div>
            <viewTable3
              :list="list"
              :searchFormObj="searchFormObj"
              :Position="item.Order + '-'"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel1="simlevel"
              :BaseTime="BaseTime"
              :Order="item.Order"
              :fullscreen="fullscreen"
              :titlekey="item.ModularName"
              :backgroundImg="zBox.backgroundImage"
            ></viewTable3>
          </template>
          <!-- andon -->
          <template v-if="item.OlineType.Encode == '14'">
            <div
              style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div>
            <andon
              :id1="`chart-${index + 1}`"
              :Order="item.Order"
            />
          </template>
          <!-- 环形图 -->
          <template v-if="item.OlineType.Encode == '6'">

            <circularChart
              style="height: 92%;"
              :id1="`chart-${index + 1}-1`"
              :Order="item.Order"
              :title="item.ModularName"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- 柱状+折线图 -->
          <template v-if="item.OlineType.Encode == '4'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <barLine
              style="height: 92%;"
              :id1="`chart-${index + 1}-1`"
              :Order="item.Order"
              :title="item.ModularName"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- 渐变色柱状图 -->
          <template v-if="item.OlineType.Encode == '2'">
            <!-- <div style="color: #fff;">{{ item.title }}</div> -->
            <!-- <barChart
              :title="item.ModularName"
              :id1="`chart-${index+1}`"
              :list="item.listData"
            /> -->
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <barChart
              style="height: 92%;"
              :title="item.ModularName"
              :id1="`chart-${index+1}-1`"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
              :colorList="colorList"
              :cxStatus="item.TargetValue?.Fullname"
            />
          </template>
          <!-- 横向柱状图 -->
          <template v-if="item.OlineType.Encode == '3'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <barTransverseChart
              style="height: 92%;"
              :title="item.ModularName"
              :id1="`chart-${index+1}-1`"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- 折线图 -->
          <template v-if="item.OlineType.Encode == '1'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <lineChart
              style="height: 92%;"
              :title="item.ModularName"
              :id1="`chart-${index+1}-1`"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
              :colorList="colorList"
            />
          </template>

          <!-- 饼图 -->
          <template v-if="item.OlineType.Encode == '5'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <pieChart
              style="height: 92%;"
              :title="item.ModularName"
              :id1="`chart-${index+1}-1`"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- 表格 -->
          <template v-if="item.OlineType.Encode == '8'">
            <!--  <div
               style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div> -->
            <tableCom
              style="margin-top: 10px;"
              :id1="`chart-${index+1}-1`"
              :Order="item.Order"
              :tableHeight="item.tableHeight"
              :title="item.ModularName"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- <template v-if="item.OlineType.Encode == '8' && item.Order == 'sim2-' + '*********14' + '-' + '*********' + '-left-' + (parseFloat(rightIndex)+ (index+1)) + '-1'">
             <div
               style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div>
            <viewTable
              :searchFormObj="searchFormObj"
              :Position="searchFormObj.PresentDepartmentId + '-SIM1-' + 'Table-' + curOutputKPIIndex3"
              @tableHandle1="tableHandle"
            >
            </viewTable>
          </template> -->

          <!-- 旋转展示 -->
          <!-- <template v-if="item.OlineType.Encode == '旋转'">
             <div
               style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div>
            <rotateChart
              :title="item.ModularName"
              :id1="`chart-${index+1}`"
              :Order="item.Order"
            />
          </template> -->
          <!-- 仪表盘展示 -->
          <template v-if="item.OlineType.Encode == '7'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <meterChart
              style="height: 92%;"
              :title="item.ModularName"
              :id1="`chart-${index+1}-1`"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- tab区域展示 -->
          <template v-if="item.OlineType.Encode == '12'">
            <!-- <div  style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;">{{ item.title }}</div> -->
            <tabChart
              :id1="`chart-${(index + 1)}-1`"
              :list="item.tabCardNum"
              :indexTab="index+1"
              :region1="regionLeft"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :backgroundImg="zBox.backgroundImage"
              :colorList="colorList"
            />
            />
          </template>
          <!-- 安全十字展示 -->
          <template v-if="item.OlineType.Encode == '13'">
            <!-- <div
              style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div> -->
            <div
              class="titimgbox"
              @click="routeChange(item.routePage?.TagCode)"
            >
              <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
              <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
            </div>
            <!-- <securityChart :searchFormObj="searchFormObj"></securityChart> -->
            <safe
              :key="safekey"
              :bcode="form.TeamId1"
              :fullscreen="fullscreen"
              :searchFormObj="searchFormObj"
              :curTeamTreeObj="curTeamTreeObj"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Order="item.Order"
              :backgroundImg="zBox.backgroundImage"
            ></safe>
          </template>
          <!-- 文字描述展示 -->
          <template v-if="item.OlineType.Encode == '10'">
            <!-- <div
              style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange1(item.routePage?.TagCode,item.listParams,item.OlineType.Encode,item.Order)"
            >{{ item.ModularName }}</div> -->
            <div
              class="titimgbox"
              @click="routeChange1(item.routePage?.TagCode,item.listParams,item.OlineType.Encode,item.Order)"
            >
              <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
              <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
            </div>
            <mattersNeeding
              :itemCode="item.itemCode"
              :id1="`chart-${index+1}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- 列别组件 -->
          <template v-if="item.OlineType.Encode == '9'">
            <!-- <div
              style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange1(item.routePage?.TagCode,item.listParams,item.OlineType.Encode,item.Order)"
            >{{ item.ModularName }}</div> -->
            <div
              class="titimgbox"
              @click="routeChange1(item.routePage?.TagCode,item.listParams,item.OlineType.Encode,item.Order)"
            >
              <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
              <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
            </div>
            <columnChart
              :itemCode="item.itemCode"
              :id1="`chart-${index+1}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- <template v-if="item.OlineType.Encode == '11'">
             <div
               style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div>
            <keyIndicators
              :id1="`chart-${index + 1}`"
              :Order="item.Order"
            />
          </template> -->
        </div>
      </div>
      <div
        class="chartBox_c"
        :style="centerStyle"
        v-if="tabChartFlag"
      >
        <div
          v-for="(item,index) in Conterjosn"
          :key="index"
          :id="`chart-${centerIndex + (index + 1)}`"
          :style="item.style"
          class="box123"
        >
          <!-- 九宫格 -->
          <template v-if="item.OlineType.Encode == '11'">
            <div
              class="titimgbox"
              @click="routeChange(item.routePage?.TagCode)"
            >
              <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
              <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
            </div>
            <viewTable3
              :list="list"
              :searchFormObj="searchFormObj"
              :Position="item.Order + '-'"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :fullscreen="fullscreen"
              :titlekey="item.ModularName"
              :backgroundImg="zBox.backgroundImage"
            ></viewTable3>
          </template>
          <!-- andon -->
          <template v-if="item.OlineType.Encode == '14'">
            <div style="color: #fff;">{{ item.ModularName }}</div>
            <andon
              :id1="`chart-${centerIndex + (index+1)}`"
              :Order="item.Order"
            />
          </template>
          <!-- 环形图 -->
          <template v-if="item.OlineType.Encode == '6'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <circularChart
              style="height: 92%;"
              :id1="`chart-${centerIndex + (index+1)}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- 柱状+折线图 -->
          <template v-if="item.OlineType.Encode == '4'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <barLine
              style="height: 92%;"
              :id1="`chart-${centerIndex + (index+1)}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- 渐变色柱状图 -->
          <template v-if="item.OlineType.Encode == '2'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <barChart
              style="height: 92%;"
              :id1="`chart-${centerIndex + (index+1)}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
              :colorList="colorList"
              :cxStatus="item.TargetValue?.Fullname"
            />
          </template>
          <!-- 横向柱状图 -->
          <template v-if="item.OlineType.Encode == '3'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <barTransverseChart
              style="height: 92%;"
              :id1="`chart-${centerIndex + (index+1)}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- 折线图 -->
          <template v-if="item.OlineType.Encode == '1'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <lineChart
              style="height: 92%;"
              :id1="`chart-${centerIndex + (index+1)}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
              :colorList="colorList"
            />
          </template>

          <!-- 饼图 -->
          <template v-if="item.OlineType.Encode == '5'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <pieChart
              style="height: 92%;"
              :id1="`chart-${centerIndex + (index+1)}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- 表格 -->
          <template v-if="item.OlineType.Encode == '8'">
            <!--  <div
               style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div> -->
            <tableCom
              :id1="`chart-${centerIndex + (index+1)}-1`"
              :Order="item.Order"
              :tableHeight="item.tableHeight"
              :title="item.ModularName"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- <template v-if="item.OlineType.Encode == '8' && item.Order == 'sim2-' + '*********14' + '-' + '*********' + '-center-' + (parseFloat(rightIndex)+ (index+1)) + '-1'">
             <div
               style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div>
            <viewTable
              :searchFormObj="searchFormObj"
              :Position="searchFormObj.PresentDepartmentId + '-SIM1-' + 'Table-' + curOutputKPIIndex3"
              @tableHandle1="tableHandle"
            >
            </viewTable>
          </template> -->

          <!-- 旋转展示 -->
          <!-- <template v-if="item.OlineType.Encode == 'rotate'">
             <div
               style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div>
            <rotateChart
             :id1="`chart-${centerIndex + (index+1)}`"
              :Order="item.Order"
              :title="item.ModularName"
            />
          </template> -->
          <!-- 仪表盘展示 -->
          <template v-if="item.OlineType.Encode == '7'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <meterChart
              style="height: 92%;"
              :id1="`chart-${centerIndex + (index+1)}-1`"
              :Order="item.Order"
              :title="item.ModularName"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- tab区域展示 -->
          <template v-if="item.OlineType.Encode == '12'">
            <!-- <div  style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;">{{ item.title }}</div> -->
            <tabChart
              :id1="`chart-${centerIndex + (index + 1)}-1`"
              :list="item.tabCardNum"
              :indexTab="(centerIndex + (index+1))"
              :region1="regionCenter"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :backgroundImg="zBox.backgroundImage"
              :colorList="colorList"
            />
          </template>
          <!-- 安全十字展示 -->
          <template v-if="item.OlineType.Encode == '13'">
            <div
              class="titimgbox"
              @click="routeChange(item.routePage?.TagCode)"
            >
              <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
              <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
            </div>
            <!-- <securityChart :searchFormObj="searchFormObj"></securityChart> -->
            <safe
              :bcode="form.TeamId1"
              :fullscreen="fullscreen"
              :searchFormObj="searchFormObj"
              :curTeamTreeObj="curTeamTreeObj"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Order="item.Order"
              :backgroundImg="zBox.backgroundImage"
            ></safe>
          </template>
          <!-- 文字描述展示 -->
          <template v-if="item.OlineType.Encode == '10'">
            <div
              class="titimgbox"
              @click="routeChange1(item.routePage?.TagCode,item.listParams,item.OlineType.Encode,item.Order)"
            >
              <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
              <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
            </div>
            <mattersNeeding
              :itemCode="item.itemCode"
              :id1="`chart-${centerIndex + (index+1)}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- 列别组件 -->
          <template v-if="item.OlineType.Encode == '9'">
            <div
              class="titimgbox"
              @click="routeChange1(item.routePage?.TagCode,item.listParams,item.OlineType.Encode,item.Order)"
            >
              <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
              <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
            </div>
            <columnChart
              :itemCode="item.itemCode"
              :id1="`chart-${centerIndex + (index+1)}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- <template v-if="item.OlineType.Encode == '11'">
            <div
              style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div>
            <keyIndicators
              :id1="`chart-${centerIndex + (index+1)}`"
              :Order="item.Order"
            />
          </template> -->
        </div>
      </div>
      <div
        class="chartBox_r"
        :style="rightStyle"
      >
        <div
          :style="item.style"
          v-for="(item,index) in Rightjosn"
          :key="index"
          :id="`chart-${rightIndex + (index+1)}`"
          class="box123"
        >
          <!-- 九宫格 -->
          <template v-if="item.OlineType.Encode == '11'">
            <div
              class="titimgbox"
              @click="routeChange(item.routePage?.TagCode)"
            >
              <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
              <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
            </div>
            <viewTable3
              :Order="item.Order"
              :list="list"
              :searchFormObj="searchFormObj"
              :Position="item.Order + '-'"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :fullscreen="fullscreen"
              :titlekey="item.ModularName"
              :backgroundImg="zBox.backgroundImage"
            ></viewTable3>
          </template>
          <template v-if="item.OlineType.Encode == '14'">
            <div
              style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div>
            <andon
              :id1="`chart-${rightIndex + (index + 1)}`"
              :Order="item.Order"
            />
          </template>
          <!-- 环形图 -->
          <template v-if="item.OlineType.Encode == '6'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <circularChart
              style="height: 92%;"
              :id1="`chart-${rightIndex + (index + 1)}-1`"
              :Order="item.Order"
              :title="item.ModularName"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- 柱状+折线图 -->
          <template v-if="item.OlineType.Encode == '4'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <barLine
              style="height: 92%;"
              :id1="`chart-${rightIndex + (index + 1)}-1`"
              :Order="item.Order"
              :title="item.ModularName"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- 柱状图 -->
          <template v-if="item.OlineType.Encode == '2'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <barChart
              style="height: 92%;"
              :id1="`chart-${rightIndex + (index + 1)}-1`"
              :Order="item.Order"
              :title="item.ModularName"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
              :colorList="colorList"
              :cxStatus="item.TargetValue?.Fullname"
            />
          </template>
          <!-- 横向柱状图 -->
          <template v-if="item.OlineType.Encode == '3'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <barTransverseChart
              style="height: 92%;"
              :id1="`chart-${rightIndex + (index + 1)}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- 折线图 -->
          <template v-if="item.OlineType.Encode == '1'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <lineChart
              style="height: 92%;"
              :id1="`chart-${rightIndex + (index + 1)}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
              :colorList="colorList"
            />
          </template>

          <!-- 饼图 -->
          <template v-if="item.OlineType.Encode == '5'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <pieChart
              style="height: 92%;"
              :id1="`chart-${rightIndex + (index + 1)}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- 表格 -->
          <template v-if="item.OlineType.Encode == '8'">
            <!--  <div
               style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div> -->
            <tableCom
              :id1="`chart-${rightIndex + (index + 1)}-1`"
              :Order="item.Order"
              :tableHeight="item.tableHeight"
              :title="item.ModularName"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- <template v-if="item.OlineType.Encode == '8'">
             <div
               style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div>
            <viewTable
              :searchFormObj="searchFormObj"
              :Position="searchFormObj.PresentDepartmentId + '-SIM1-' + 'Table-' + curOutputKPIIndex3"
              @tableHandle1="tableHandle"
            >
            </viewTable>
          </template> -->

          <!-- 旋转展示 -->
          <!-- <template v-if="item.OlineType.Encode == '旋转'">
             <div
               style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div>
            <rotateChart
              :key="rotateChartKey"
              :id1="`chart-${rightIndex + (index + 1)}`"
              :list="item.listData"
              :title="item.ModularName"
            />
          </template> -->
          <!-- 仪表盘展示 -->
          <template v-if="item.OlineType.Encode == '7'">
            <!-- <div style="display: flex;width: 100%;height: 30px;">
              <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
              <div style="width: 50%;display: flex;">
                <dayMonIndex />
              </div>
            </div> -->
            <meterChart
              style="height: 92%;"
              :id1="`chart-${rightIndex + (index + 1)}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- tab区域展示 -->
          <template v-if="item.OlineType.Encode == '12'">
            <div
              style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div>
            <tabChart
              :indexTab="rightIndex + (index+1)"
              :region1="regionRight"
              :list="item.tabCardNum"
              :id1="`chart-${rightIndex + (index + 1)}-1`"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :backgroundImg="zBox.backgroundImage"
              :colorList="colorList"
            />
          </template>
          <!-- 安全十字展示 -->
          <template v-if="item.OlineType.Encode == '13'">
            <div
              class="titimgbox"
              @click="routeChange(item.routePage?.TagCode)"
            >
              <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
              <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
            </div>
            <!-- <securityChart :searchFormObj="searchFormObj"></securityChart> -->
            <safe
              :bcode="form.TeamId1"
              :fullscreen="fullscreen"
              :searchFormObj="searchFormObj"
              :curTeamTreeObj="curTeamTreeObj"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Order="item.Order"
              :backgroundImg="zBox.backgroundImage"
            ></safe>
          </template>
          <!-- 文字描述展示 -->
          <template v-if="item.OlineType.Encode == '10'">
            <div
              class="titimgbox"
              @click="routeChange1(item.routePage?.TagCode,item.listParams,item.OlineType.Encode,item.Order)"
            >
              <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
              <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
            </div>
            <mattersNeeding
              :itemCode="item.itemCode"
              :id1="`chart-${rightIndex + (index + 1)}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- 列别组件 -->
          <template v-if="item.OlineType.Encode == '9'">
            <div
              class="titimgbox"
              @click="routeChange1(item.routePage?.TagCode,item.listParams,item.OlineType.Encode,item.Order)"
            >
              <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
              <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
            </div>
            <columnChart
              :itemCode="item.itemCode"
              :id1="`chart-${rightIndex + (index + 1)}-1`"
              :title="item.ModularName"
              :Order="item.Order"
              :exhibitionType="item.exhibitionType.Fullname"
              :simlevel="simlevel"
              :BaseTime="BaseTime"
              :Dimension="item.DimensionList1"
              :routeList="item.routePage?.TagCode"
              :backgroundImg="zBox.backgroundImage"
            />
          </template>
          <!-- <template v-if="item.OlineType.Encode == '11'">
            <div
              style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div>
            <keyIndicators
              :id1="`chart-${rightIndex + (index + 1)}`"
              :Order="item.Order"
            />
          </template> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import dayjs from 'dayjs';
import { getqueryZsim2, getqueryLcr, getChartStructure, getImageUel, getClassifyList, getColorList, getTree } from '@/api/simConfig/simconfignew.js';
export default {
  components: {
    barChart: () => import('@/views/simManagement/simNew1/components/barChart.vue'),
    lineChart: () => import('@/views/simManagement/simNew1/components/lineChart.vue'),
    pieChart: () => import('@/views/simManagement/simNew1/components/pieChart.vue'),
    tableCom: () => import('@/views/simManagement/simNew1/components/tableCom.vue'),
    barTransverseChart: () => import('@/views/simManagement/simNew1/components/barTransverseChart.vue'),
    // barStereoscopicChart: () => import('@/views/simManagement/simNew1/components/barStereoscopicChart.vue'),
    // rotateChart: () => import('@/views/simManagement/simNew1/components/rotateChart.vue'),
    meterChart: () => import('@/views/simManagement/simNew1/components/meterChart.vue'),
    // securityChart: () => import('@/views/simManagement/simNew1/components/securityChart.vue'),
    // searchPage: () => import('@/views/simManagement/simNew1/components/searchPage.vue'),
    tabChart: () => import('@/views/simManagement/simNew1/components/tabChart.vue'),
    mattersNeeding: () => import('@/views/simManagement/simNew1/components/mattersNeeding.vue'),
    columnChart: () => import('@/views/simManagement/simNew1/components/columnChart.vue'),
    // keyIndicators: () => import('@/views/simManagement/simNew1/components/keyIndicators.vue'),
    barLine: () => import('@/views/simManagement/simNew1/components/barLine.vue'),
    circularChart: () => import('@/views/simManagement/simNew1/components/circularChart.vue'),
    andon: () => import('@/views/simManagement/simNew1/components/andon.vue'),
    safe: () => import('@/views/simManagement/simNew1/components/safe.vue'),

    viewTable3: () => import('@/views/simManagement/simNew1/components/viewTable3.vue'),
    // dayMonIndex: () => import('@/views/simManagement/simNew1/components/dayMonIndex.vue'),

    // viewTable: () => import('@/views/simManagement/simNew1/components/viewTable.vue'),
  },
  data: () => ({
    EquipmentProductLineTree1: [],
    colorList: [],
    safekey: 1,
    setDisabled: {
      disabledDate(time) {
        return time.getTime() > Date.now();
      },
    },
    form: {
      TEAM: '',
      TeamId: null,
      TeamId1: ''
    },
    normalizer(node) {
      return {
        id: node.id,
        label: node.name,
        children: node.children
      };
    },
    simlevel: '',
    list1: [{ tabName: "日", id: 0 }, { tabName: "月", id: 1 }],
    tabId: 0,
    tableTitle: '',
    regionLeft: 'left',
    regionCenter: 'center',
    regionRight: 'right',
    curOutputKPIIndex3: 0,
    list: [],
    tabChartFlag: false,
    tabChartFlag1: false,
    barTransverseChartFlag: false,
    conterFlag: false,
    leftBr: false,
    leftLine: false,
    leftPie: false,
    barFlage: false,
    curConfig: {},
    BaseTime: '',
    TeamCode: '*********14',
    ProductionLineCode: '*********',
    FactoryCode: 'A118024',
    curOutputKPIIndexbar: 0,
    // 查询条件
    Searchconfig: [],
    searchPageKey: 1,
    // 左侧数据源
    Leftjosn: [],
    barChartKey: 1,
    // 右侧数据源
    Rightjosn: [],
    rightIndex: 0,
    // 中间区域数据源
    Conterjosn: [],
    centerIndex: 0,

    tabChartKey: 1,
    barTransverseChartKey: 1,
    lineChartKey: 1,
    pieChartKey: 1,
    tableCom: 1,
    rotateChartKey: 1,
    meterChart: 1,
    mattersNeedingKey: 1,
    columnChartKey: 1,
    keyIndicatorsKey: 1,
    tableComKey: 1,
    seriesList: [],
    productseries: '',
    factory: '',
    searchData: {},
    withdrawTime: [],
    formInline: {
      user: '',
      region: ''
    },
    searchFormObj: {
      simLevel: 'SIM2',
      PresentDepartmentId: '',//默认为第一个班组ID
      date: dayjs(new Date()).format('YYYY-MM-DD'),
      timestamp: new Date().getTime()
    },
    leftStyle: {
      width: '',
      height: '',
      backgroundImage: '',
      backgroundSize: '100%',
    },
    centerStyle: {
      width: '',
      height: '',
    },
    rightStyle: {
      width: '',
      height: '',
    },
    bacImage: '',
    barList: [],
    bulletinBoardTit: '', //福华化学即时化管理中台
    // bulletinBoardTit: '测试看板',
    fullscreen: false,
    zBox: {
      width: '100%',
      height: '100%',
      backgroundImage: '',
      backgroundSize: '100%',
      padding: '2px 20px'
    },
    bigBox_top: {
      width: "100%",
      height: "8%",
      // fontSize: '22px',
      color: '#fff',
      display: 'flex',
      // justifyContent: 'center',
      // alignItems: 'center',
      // height: '5%',
      paddingTop: "12px",
      paddingBottom: "12px"
    },
    days: [],
    timeStr: '',
  }),
  computed: {
    EquipmentProductLineTree() {
      return this.$store.getters.EquipmentProductLineTree
    },
    ...mapGetters(['getUserinfolist']),
    ...mapGetters(['DefaultDepartmentPermission']),
    ...mapGetters(['DepartmentPermission']),
    curAuth() {
      return this.$store.getters.getUserinfolist[0]
    },
    curAuth1() {
      return this.$store.getters.DefaultDepartmentPermission
    },
    curAuth2() {
      return this.$store.getters.DepartmentPermission
    },
    OriEquipmentTeamTree() {
      return this.$store.state.sim.OriEquipmentTeamTree
    },
    firstTeam() {
      let info = this.$store.getters.getFactoryInfo
      let obj = info.find(item => item.Level == 'Team')
      if (obj && obj.EquipmentId) {
        return { id: obj.EquipmentId }
      } else {
        let target = {}
        for (let i = 0; i < this.$store.getters.flatEquipmentTeam.length; i++) {
          const element = this.$store.getters.flatEquipmentTeam[i];
          if (element.extendField === 'Team') {
            target = element
            break
          }
        }
        return target
      }
    },
    searchInputs() {
      return [
        {
          value: dayjs(new Date()).format('YYYY-MM-DD'),
          type: 'date',
          icon: 'mdi-account-check',
          label: this.$t('GLOBAL._DATE'),
          placeholder: '',
          key: 'date'
        }
      ]
    },
    //当前班组Parent数据
    curTeamTreeObj() {
      let ProductionLineCode, FactoryCode
      let TeamCodeParent = this.$store.getters.flatEquipmentTeam.find(item => item.id === this.searchFormObj.PresentDepartmentId)
      if (TeamCodeParent) {
        ProductionLineCode = this.$store.getters.flatEquipmentTeam.find(item => item.id === TeamCodeParent.parentId)
      }
      if (ProductionLineCode) {
        FactoryCode = this.$store.getters.flatEquipmentTeam.find(item => item.id === ProductionLineCode.parentId)
      }
      return {
        TeamCode: this.searchFormObj.PresentDepartmentId,
        ProductionLineCode: ProductionLineCode?.id,
        FactoryCode: JSON.parse(localStorage.getItem('jurisdiction')) == '68' ? '425' : '13'
      }
    }
  },
  watch: {
    $route(to, from) {
      this.form.TeamId = ''
      this.$store.dispatch('getEquipmentTeamTree', "Team");
      this.$store.dispatch('EquipmentProductLineTree');
      // localStorage.setItem('jurisdiction3', JSON.stringify(this.$store.state.sim.OriEquipmentTeamTree))
      localStorage.setItem('jurisdiction2', JSON.stringify(this.$store.getters.EquipmentProductLineTree))
      if (to.path == '/simManagement/simNew2') {

        this.EquipmentProductLineTree1 = []
        JSON.parse(localStorage.getItem('jurisdiction2')).map(item => {
          item.children.map(child => {
            if (localStorage.getItem('jurisdiction1').split(',').includes(child.id)) {
              this.EquipmentProductLineTree1.push(child);
            }
          });
        });
        this.getColor()
        console.log(JSON.stringify(this.$route.query) != '{}' && this.$route.query?.code, 'rrrrrrrrrrrrrrrrrr')
        if (JSON.stringify(this.$route.query) != '{}' && this.$route.query?.code) {
          this.queryChartDatasim2(this.$route.query.code)
          const today = new Date();
          today.setDate(today.getDate() - 1);
          const year = today.getFullYear();
          const month = ('0' + (today.getMonth() + 1)).slice(-2);
          const day = ('0' + today.getDate()).slice(-2);
          this.BaseTime = `${year}-${month}-${day}`;
          this.form.TeamId = this.$route.query.code.split('-')[1]
          this.form.TeamId1 = localStorage.getItem('jurisdiction')
        } else {
          // this.form.TeamId = this.getUserinfolist[0].Departmentid
          // this.queryChartDatasim2('SIM2' + '-' + this.getUserinfolist[0].Departmentid)
          // this.form.TeamId1 = this.getUserinfolist[0].Departmentid
          if (localStorage.getItem('cost')) {
            this.form.TeamId = localStorage.getItem('cost')
            this.form.TeamId1 = localStorage.getItem('cost')
            this.queryChartDatasim2('SIM2' + '-' + localStorage.getItem('cost'))
          } else {
            this.form.TeamId = localStorage.getItem('jurisdiction')
            this.form.TeamId1 = localStorage.getItem('jurisdiction')
            this.queryChartDatasim2('SIM2' + '-' + this.form.TeamId)
          }
          // else {
          //   this.form.TeamId = this.getUserinfolist[0].Departmentid
          //   this.form.TeamId1 = this.getUserinfolist[0].Departmentid
          //   this.queryChartDatasim2('SIM2' + '-' + this.getUserinfolist[0].Departmentid)
          // }
        }
      }
    },
    firstTeam: {
      handler(nv) {
        this.safekey++
        this.searchFormObj.PresentDepartmentId = nv.id
      },
      immediate: true
    },
    curTeamTreeObj: {
      handler(nv) {
        this.$store.dispatch('getLineList', { FactoryCode: JSON.parse(localStorage.getItem('jurisdiction')) == '68' ? '425' : '13' });
      },
      deep: true
    },
  },

  created() {
    this.$store.dispatch('getEquipmentTeamTree', "Team");
    this.$store.dispatch('getCompanyTree');
    this.$store.dispatch('EquipmentProductLineTree');
    this.$store.dispatch('getDepartmentList')
    this.$store.dispatch('getTeamList')
    // this.$store.dispatch('getEquipmentTeamTree', "Team");
    // this.$store.dispatch('EquipmentProductLineTree');
    // localStorage.setItem('jurisdiction3', JSON.stringify(this.$store.state.sim.OriEquipmentTeamTree))
    // localStorage.setItem('jurisdiction2', JSON.stringify(this.$store.getters.EquipmentProductLineTree))

    this.getColor()
    this.EquipmentProductLineTree1 = []
    JSON.parse(localStorage.getItem('jurisdiction2')).map(item => {
      item.children.map(child => {
        if (localStorage.getItem('jurisdiction1').split(',').includes(child.id)) {
          this.EquipmentProductLineTree1.push(child);
        }
      });
    });
    this.form.TeamId = this.getUserinfolist[0].Departmentid;
    const today = new Date();
    today.setDate(today.getDate() - 1);
    const year = today.getFullYear();
    const month = ('0' + (today.getMonth() + 1)).slice(-2);
    const day = ('0' + today.getDate()).slice(-2);
    this.BaseTime = `${year}-${month}-${day}`;
    if (JSON.stringify(this.$route.query) === '{}') {
      if (localStorage.getItem('cost')) {
        this.form.TeamId = localStorage.getItem('cost')
        this.form.TeamId1 = localStorage.getItem('cost')
      } else {
        this.form.TeamId = localStorage.getItem('jurisdiction')// this.getUserinfolist[0].Departmentid
        this.form.TeamId1 = localStorage.getItem('jurisdiction')
      }
    } else {
      this.form.TeamId = localStorage.getItem('cost') // this.$route.query.code.split('-')[1]
      this.form.TeamId1 = this.$route.query.code.split('-')[1]// this.getUserinfolist[0].Departmentid
    }



    if (this.$route.query.type == 2) {
      this.form.TeamId = this.$route.query.code.split('-')[1]
      this.$store.getters.EquipmentProductLineTree.map(el => {
        if (el.children == undefined) {
          this.form.TeamId1 = localStorage.getItem('jurisdiction')
        } else {
          el.children.map(item => {
            if (this.$route.query.code.split('-')[1] == item.id) {
              this.form.TeamId1 = item.id
            }
          })
        }
      })
      this.queryChartDatasim2(this.$route.query.code)

    } else {
      if (localStorage.getItem('cost')) {
        this.form.TeamId = localStorage.getItem('cost')
        this.form.TeamId1 = localStorage.getItem('cost')
        this.queryChartDatasim2('SIM2-' + localStorage.getItem('cost'))
      } else {
        this.form.TeamId = localStorage.getItem('jurisdiction')// this.getUserinfolist[0].Departmentid
        this.form.TeamId1 = localStorage.getItem('jurisdiction')
        this.queryChartDatasim2('SIM2-' + this.form.TeamId)
      }
    }





    // this.$store.dispatch('getSimLvlist');
    // this.$store.dispatch('getEventLevelList');
    // this.$store.dispatch('getCloseStatuslist');
    // // this.$store.dispatch('getCompanyList');
    // this.$store.dispatch('getCompanyTree');
    // this.$store.dispatch('getTargetEDRList');

    // this.$store.dispatch('getDepartmentList')
    // this.$store.dispatch('getTeamList')
    // this.$store.dispatch('getUnitList');

    // this.$store.dispatch('getSegmentList');
    // this.$store.dispatch('getStaffList');
    // this.$store.dispatch('getClassfyCodeList');
    // this.$store.dispatch('getTimeDimension');
  },
  activated() {
    this.$store.dispatch('EquipmentProductLineTree');
    this.$store.dispatch('getEquipmentTeamTree', "Team");
  },
  mounted() {
    // this.$store.dispatch('getEquipmentTeamTree', "Team");
    // this.$store.dispatch('EquipmentProductLineTree');
    // localStorage.setItem('jurisdiction3', JSON.stringify(this.$store.state.sim.OriEquipmentTeamTree))
    // localStorage.setItem('jurisdiction2', JSON.stringify(this.$store.getters.EquipmentProductLineTree))
    this.bacImage = { backgroundImage: 'url("./image/bj1.jpg")' }
    this.getCountDown();
    if (this.timer) {
      clearInterval(this.timer);
    }
    this.timer = setInterval(() => {
      setTimeout(this.getCountDown(), 0);
    }, 1000);
    // this.queryChartDatasim2()
  },
  methods: {
    async getColor() {
      this.colorList = []
      let params = {
        "lang": "cn",
        "key": "",
        "RootId": "02312312-2261-6900-163e-0370f6000000",
        "itemCode": "EchartColorList",
        "pageIndex": 1,
        "pageSize": 100
      }
      let res = await getColorList(params)

      if (res.success) {
        res.response.data.map(el => {
          this.colorList.push(el.ItemValue)
        })
      }
    },
    handleCheck1(data) {
      this.form.TEAM = data.name
      this.form.TeamId = data.id
      this.form.TeamId1 = data.id
      localStorage.setItem('cost', data.id);
      this.queryChartDatasim2('SIM2-' + data.id)
    },
    routeChange(e) {
      if (e) {
        this.$router.push({ path: e })
      }
    },
    async routeChange1(e, e1, e2, e3) {
      let ID = ''
      if (e) {
        let res = await getClassifyList(e1);
        if (res.success) {
          ID = res.response[0].ID
          this.$router.push({ path: e, query: { RootId: ID, itemCode: e1 } })
        }

        // if (e2 == '9') {
        //   if (e3.split('-')[0] == 'SIM1') {
        //     this.$router.push({ path: e, query: { RootId: '02312312-2261-6900-163e-0370f6000000', itemCode: e1 } })
        //   }
        //   if (e3.split('-')[0] == 'SIM2') {
        //     this.$router.push({ path: e, query: { RootId: '02312312-2261-6900-163e-0370f6000000', itemCode: e1 } })
        //   }
        // }
        // if (e2 == '10') {
        //   this.$router.push({ path: e, query: { RootId: '02312312-2261-6900-163e-0370f6000000', itemCode: e1 } })
        // }
      }
      // if (e) {
      //   if (e2 == '9') {
      //     if (e3.split('-')[0] == 'SIM1') {
      //       this.$router.push({ path: e, query: { RootId: '02312312-2261-6900-163e-0370f6000000', itemCode: e1 } })
      //     }
      //     if (e3.split('-')[0] == 'SIM2') {
      //       this.$router.push({ path: e, query: { RootId: '02312312-2261-6900-163e-0370f6000000', itemCode: e1 } })
      //     }
      //   }
      //   if (e2 == '10') {
      //     this.$router.push({ path: e, query: { RootId: '02312312-2261-6900-163e-0370f6000000', itemCode: e1 } })
      //   }
      // }
    },
    handleDateChange(e) {
      this.BaseTime = e
      this.queryChartDatasim2(this.$route.query.code)
    },
    tableHandle(data) {
      this.tableTitle = data
    },
    searchForm(form) {

    },
    tabClick(item) {
      this.tabId = item.id
    },
    async queryChartDatasim2(code) {
      if (code == undefined) {
        code = 'SIM2-' + this.form.TeamId
      }
      const params = {
        "code": code
      }
      const res = await getqueryZsim2(params);
      if (res.response != null) {
        // console.log(res.response, '插叙回来的第一批数据看看sim2');
        // console.log(JSON.parse(res.response.Searchconfig), '插叙回来的第一批数据看看');
        this.bulletinBoardTit = res.response.Olinetit
        // this.Searchconfig = JSON.parse(res.response.Searchconfig)
        this.simlevel = res.response.Simlevel

        const res1 = await getImageUel(res.response.Baroundimg);
        this.zBox.backgroundImage = `url('${res1.response}')`


        // this.searchPageKey++
        this.queryChartlcrData(res.response.ID)
      } else {
        this.bulletinBoardTit = ''
        this.simlevel = ''
        this.searchFormObj.PresentDepartmentId = ''
        this.zBox.backgroundImage = ''
        this.Leftjosn = []
        this.Conterjosn = []
        this.Rightjosn = []
        this.leftStyle.backgroundImage = ''
        this.centerStyle.backgroundImage = ''
        this.rightStyle.backgroundImage = ''
      }
    },

    async queryChartlcrData(id) {
      this.Leftjosn = []
      this.Conterjosn = []
      this.Rightjosn = []
      const params = {
        "code": id,
      }
      const res = await getqueryLcr(params);
      this.leftStyle.width = JSON.parse(res.response.Leftjosn).leftWidth || ''
      this.leftStyle.height = JSON.parse(res.response.Leftjosn).leftHeight || ''
      if (JSON.parse(res.response.Leftjosn).img1 != null && JSON.parse(res.response.Leftjosn).img1 != '') {
        const res1 = await getImageUel(JSON.parse(res.response.Leftjosn).img1);
        this.leftStyle.backgroundImage = `url('${res1.response}')`
      }

      this.Leftjosn = JSON.parse(res.response.Leftjosn).listChart || []
      if (this.Leftjosn.length > 0) {
        this.Leftjosn.map(async (item, index) => {
          const res = await getImageUel(item.deailFileList[0]?.name);
          item.style.backgroundImage = `url('${res.response}')`
        })
      }

      console.log(JSON.parse(res.response.Conterjosn), '0987654')
      this.centerStyle.width = JSON.parse(res.response.Conterjosn).centerWidth || ''
      this.centerStyle.height = JSON.parse(res.response.Conterjosn).centerHeight || ''
      if (JSON.parse(res.response.Conterjosn).img1 != null && JSON.parse(res.response.Conterjosn).img1 != '') {
        const res2 = await getImageUel(JSON.parse(res.response.Conterjosn).img1);
        this.centerStyle.backgroundImage = `url('${res2.response}')`
      }

      this.centerIndex = this.Leftjosn.length || 0
      this.Conterjosn = JSON.parse(res.response.Conterjosn).listChart || []
      // this.Conterjosn.map(el2 => {
      //   el2.tabCardNum.map(el3 => {
      //     if (el3.TabTitle == '设备') {
      //       console.log(el3);

      //     }
      //   })

      // })
      if (this.Conterjosn.length > 0) {
        this.Conterjosn.map(async (item, index) => {
          const res = await getImageUel(item.deailFileList[0]?.name);
          item.style.backgroundImage = `url('${res.response}')`
        })
      }





      this.barTransverseChartFlag = true
      this.tabChartFlag = true
      this.tabChartFlag1 = true


      this.rightIndex = (this.Leftjosn.length + this.Conterjosn.length) || 0
      this.Rightjosn = JSON.parse(res.response.Rightjosn).listChart || []
      this.rightStyle.width = JSON.parse(res.response.Rightjosn).rightWidth || ''
      this.rightStyle.height = JSON.parse(res.response.Rightjosn).rightHeight || ''
      if (JSON.parse(res.response.Rightjosn).img1 != '' && JSON.parse(res.response.Rightjosn).img1 != null) {
        const res3 = await getImageUel(JSON.parse(res.response.Rightjosn).img1);
        this.rightStyle.backgroundImage = `url('${res3.response}')`
      }

      if (this.Rightjosn.length > 0) {
        this.Rightjosn.map(async (item, index) => {
          const res = await getImageUel(item.deailFileList[0].name);
          item.style.backgroundImage = `url('${res.response}')`
        })
      }


      this.tableComKey++
      this.tabChartKey++
    },
    // 点击全屏
    screen() {
      let element = this.$refs.zBox
      if (this.fullscreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      } else {
        if (element.requestFullscreen) {
          element.requestFullscreen();
        } else if (element.webkitRequestFullScreen) {
          element.webkitRequestFullScreen();
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
          // IE11
          element.msRequestFullscreen();
        }
      }
      this.fullscreen = !this.fullscreen;
    },
    getCountDown() {
      let year = new Date().getFullYear();
      let month = (new Date().getMonth() + 1).toString().padStart(2, '0');
      let day = new Date().getDate().toString().padStart(2, '0');
      let hour = new Date().getHours().toString().padStart(2, '0');
      let minute = new Date().getMinutes().toString().padStart(2, '0');
      let second = new Date().getSeconds().toString().padStart(2, '0');
      this.timeStr = year + '年' + month + '月' + day + '日' + hour + '时' + minute + '分' + second + '秒';
    },
    clickSearch(data) {

    },

  },

}
</script>
<style scoped>
* {
    margin: 0;
    padding: 0;
}
.zBox {
    width: 100%;
    height: 100%;
    background-size: 100% 100% !important;
    overflow: hidden;
}
.imgBox {
    width: 40px;
    height: 40px;
    margin-top: 10px;
}
.searchBox {
    width: 100%;
    height: 5%;
    color: #fff;
    margin-top: 15px;
}
.charBox {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
    height: calc(100% - 5%);
}
.searchBox {
    flex: 1.06;
}
.charTitBox {
    flex: 1;
    text-align: center;
    line-height: 60px;
    font-size: 30px;
    font-weight: bold;
    letter-spacing: 1px;
}
.timeBox {
    color: #fff;
    font-size: 16px;
    flex: 1;
    line-height: 60px;
    text-align: right;
}
.box123 {
    background-size: 100% 100% !important;
    overflow: hidden;
}
.chartBox_c {
    background-size: 100% 100% !important;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: space-around;
}
.chartBox_l,
.chartBox_r {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: space-around;
    background-size: 100% 100% !important;
}

.titName {
    color: #fff;
    font-size: 18px;
    margin-right: 10px;
    font-weight: 600;
    line-height: 30px;
    cursor: pointer;
}
.active {
    width: 50px;
    height: 30px;
    color: #409eff;
    font-weight: 800;
    border-right: 2px solid #409eff;
    box-shadow: 0px 0px 10px #fff;
    border-radius: 5px;
    font-size: 20px;
    line-height: 30px;
    text-align: center;
}
/deep/ .vue-treeselect__label {
    color: #000000 !important;
}
/deep/ .vue-treeselect__option-arrow-container {
    color: #000000 !important;
}
/deep/ .vue-treeselect .vue-treeselect__control {
    border: 1px solid rgba(0, 0, 0, 0.47) !important;
    height: 40px !important;
}
.titimgbox {
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    width: 200px;
    height: 30px;
    line-height: 30px;
    border-radius: 5px;
    /* background-image: linear-gradient(to right, #056be0 0%, #000b61 100%); */
    display: flex;
    /* border: 1px solid #fff; */
    /* box-shadow: 0px 0px 7px 0px #fff; */
    /* overflow: hidden; */
}
</style>
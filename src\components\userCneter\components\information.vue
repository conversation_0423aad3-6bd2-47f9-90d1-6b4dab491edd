<template>
    <v-form ref="form" v-model="valid">
        <v-row class="mt-5">
            <v-col :cols="12" class="pt-0 pb-0">
                <v-text-field :label="$t('DFM_YHGL.LoginName')" readonly dense outlined v-model="getUserinfolist[0].LoginName"></v-text-field>
            </v-col>
            <v-col :cols="12" class="pt-0 pb-0">
                <v-text-field :label="$t('DFM_YHGL.UserNo')" readonly dense outlined v-model="getUserinfolist[0].UserNo"></v-text-field>
            </v-col>
            <v-col :cols="12" class="pt-0 pb-0">
                <v-text-field :label="$t('DFM_YHGL.UserName')" readonly dense outlined v-model="getUserinfolist[0].UserName"></v-text-field>
            </v-col>
            <v-col :cols="12" class="pt-0 pb-6">
                <v-text-field :label="$t('DFM_YHGL._XZZZ')" readonly dense outlined v-model="getUserinfolist[0].PostName"></v-text-field>
            </v-col>
            <v-col :cols="12" class="pt-0 pb-0">
                <v-text-field :label="$t('DFM_YHGL.Tel')" readonly dense outlined v-model="getUserinfolist[0].Tel"></v-text-field>
            </v-col>
            <v-col :cols="12" class="pt-0 pb-0">
                <v-text-field :label="$t('DFM_YHGL.Email')" readonly dense outlined v-model="getUserinfolist[0].EMAIL"></v-text-field>
            </v-col>
            <v-col class="pt-0 pb-0" :cols="12" :lg="12">
                <v-textarea :label="$t('DFM_YHGL.Remark')" readonly v-model="getUserinfolist[0].Remark" :value="getUserinfolist[0].Remark" outlined height="90"></v-textarea>
            </v-col>
        </v-row>
    </v-form>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
    data: () => ({
        type: 'password',
        valid: false,
        departmentData: [],
    }),
    computed: {
        ...mapGetters(['getUserinfolist'])
    }
};
</script>

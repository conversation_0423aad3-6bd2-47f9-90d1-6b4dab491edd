import { getRequestResources } from '@/api/fetch';
import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = 'baseURL_ANDON'
const baseAndonURL = configUrl[process.env.VUE_APP_SERVE].baseURL_ANDON;
//告警记录

//获取告警分页列表
export function AlarmRecordPageList(data) {
    const api =  '/andon/AlarmRecord/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

//获取告警详情列表
export function GetListByEventNo(data) {
    const api =  '/andon/AlarmTrace/GetListByEventNo'
    return getRequestResources(baseURL, api, 'post', data, true);
}

//获取升级路线列表
export function UpgradeRuleGetList(data) {
    const api =  '/andon/UpgradeRule/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}

//获取升级路线列表
export function AlarmHistoryGetList(data) {
    const api =  '/andon/AlarmHistory/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取报警详情
export function AlarmRecordGetEntity(data) {
    const api =  '/andon/AlarmRecord/GetEntity/' + data.id
    return getRequestResources(baseURL, api, 'get');
}

//获取报警列表
export function AlarmRecordGetXList(data) {
    const api =  '/andon/AlarmRecord/GetXList'
    return getRequestResources(baseURL, api, 'post', data);
}

//获取报警记录详情
export function AlarmRecordGetDetailListById(data) {
    const api =  '/andon/AlarmRecord/GetDetailListById'
    return getRequestResources(baseURL, api, 'post', data);
}

//根据产线code获取工段颜色
export function GetUgrecySectionCountByLineCode(data) {
    const api =  '/andon/AlarmRecord/GetUgrecySectionCountByLineCode'
    return getRequestResources(baseURL, api, 'get', data);
}

//根据工段code获取工站颜色
export function GetUgrecyStationCountBySectionCode(data) {
    const api =  '/andon/AlarmRecord/GetUgrecyStationCountBySectionCode'
    return getRequestResources(baseURL, api, 'get', data);
}

//主动获取推送
export function SendUgrecyLineCount(data) {
    const api =  '/andon/AlarmRecord/SendUgrecyLineCount'
    return getRequestResources(baseURL, api, 'get', data);
}
//Excel导出
export function ExportAlarmRecordToExcel(data) {
    return request({
        url: baseAndonURL + '/andon/AlarmRecord/ExportAlarmRecordToExcel',
        method: 'post',
        data,
        responseType: 'blob'
    })
}
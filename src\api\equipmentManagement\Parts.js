import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';
let DFM = 'baseURL_DFM';


// 备件主数据
export function GetPartsPageList(data) {
    const api = '/api/Parts/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}
// 备件主数据
export function GetPartsListByKeyword(data) {
    const api = '/api/Parts/GetListByKeyword';
    return getRequestResources(baseURL, api, 'post', data,true);
}

// 备件类型
export function GetPartType(data) {
    const api = '/api/DataItemDetail/GetDetailTree?itemCode=PartType';
    return getRequestResources(DFM, api, 'post', data);
}

// 备件删除
export function GetPartDelete(data) {
    const api = '/api/Parts/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}

// 备件库存新增
export function GetPartSaveForm(data) {
    const api = '/api/Parts/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
// 备件库存Sap
export function PartsImportMasterDataFromSap(data) {
    const api = '/api/Parts/ImportMasterDataFromSap';
    return getRequestResources(baseURL, api, 'post', data);
}
// 备件库存主数据
export function GetPartsInventoryPageList(data) {
    const api = '/api/PartsInventory/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}

// 备件库存类型
export function GetPartsInventoryType(data) {
    const api = '/api/DataItemDetail/GetDetailTree?itemCode=PartType';
    return getRequestResources(DFM, api, 'post', data);
}
// 备件库存新增
export function GetPartsInventorySaveForm(data) {
    const api = '/api/PartsInventory/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
// 备件库存删除
export function GetPartsInventoryDelete(data) {
    const api = '/api/PartsInventory/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
// 备件库存导入
export function GetPartsInventoryImportData(data, key) {
    const api = `/api/PartsInventory/ImportData?factory=${key}`;
    return getRequestResources(baseURL, api, 'post', data);
}



//备件发放管理列表
export function GetPartsHistoryDetailtPageList(data) {
    const api = '/api/PartsHistoryDetail/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}


//备件发放管理删除
export function GetPartsHistoryDetailtDelete(data) {
    const api = '/api/PartsHistoryDetail/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}

//备件发放管理取消
export function GetPartsHistoryDetailtCancel(data) {
    const api = '/api/PartsHistoryDetail/Cancel';
    return getRequestResources(baseURL, api, 'post', data);
}
//备件发放管理发放
export function GetPartsHistoryDetailtRelease(data) {
    const api = '/api/PartsHistoryDetail/Release';
    return getRequestResources(baseURL, api, 'post', data);
}
//备件发放管理退回
export function GetPartsHistoryDetailtReturn(data) {
    const api = '/api/PartsHistoryDetail/Return';
    return getRequestResources(baseURL, api, 'post', data);
}
//备件发放管理采购
export function GetPartsHistoryDetailtPurchase(data) {
    const api = '/api/PartsHistoryDetail/Purchase';
    return getRequestResources(baseURL, api, 'post', data);
}
// 备件发放管理状态
export function GetPartOutstockStatus(data) {
    const api = '/api/DataItemDetail/GetDetailTree?itemCode=PartOutstockStatus';
    return getRequestResources(DFM, api, 'post', data);
}






//备件发放管理列表
export function GetPartsAlarmPageList(data) {
    const api = '/api/PartsAlarm/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetPartsAlarmDelete(data) {
    const api = '/api/PartsAlarm/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}


export function GetPartsAlarmSaveForm(data) {
    const api = '/api/PartsAlarm/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}

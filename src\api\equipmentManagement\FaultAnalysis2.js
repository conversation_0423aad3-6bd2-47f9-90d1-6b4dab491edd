import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';

// 稳定性
export function DeviceChartGetFailureRate(data) {
    const api = '/api/DeviceChart/GetFailureRate';
    return getRequestResources(baseURL, api, 'post', data);
}

// 设备故障原因分类
export function DeviceChartGetReasonCategory(data) {
    const api = '/api/DeviceChart/GetReasonCategory';
    return getRequestResources(baseURL, api, 'post', data);
}

// 故障性质
export function DeviceChartGetStopNature(data) {
    const api = '/api/DeviceChart/GetStopNature';
    return getRequestResources(baseURL, api, 'post', data);
}

// 平均修复时间
export function DeviceChartGetBarChartMTTR(data) {
    const api = '/api/DeviceChart/GetBarChartMTTR';
    return getRequestResources(baseURL, api, 'post', data);
}

// 平均无故障工作时间
export function DeviceChartGetBarChartMTBF(data) {
    const api = '/api/DeviceChart/GetBarChartMTBF';
    return getRequestResources(baseURL, api, 'post', data);
}
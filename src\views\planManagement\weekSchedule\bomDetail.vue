<template>
  <div>
    <el-drawer class="drawer" :visible.sync="drawer" :direction="'rtl'" :before-close="handleClose"
      :append-to-body="false" size="80%">
      <div slot="title" class="title-box">
        <span>{{ `${currentRow.LineCode}-${currentRow.Factory}：${currentRow.MaterialCode}-${currentRow.MaterialName}：${currentRow.OrderNo}` }}</span>
      </div>
      <div class="InventorySearchBox">
        <div class="searchbox pd5">
          <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
            <el-form-item :label="$t('GLOBAL._SSL')">
              <el-input clearable v-model="searchForm.Key"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-search" @click="getTableData">{{ $t('GLOBAL._CX') }}</el-button>
            </el-form-item>
            <!-- <el-form-item>
              <el-button size="small" type="success" icon="el-icon-circle-plus-outline" @click="showDialog({})">{{
                $t('GLOBAL._XZ') }}
              </el-button>
            </el-form-item> -->
          </el-form>
        </div>
      </div>
      <div class="table-box">
        <el-table v-loading="loading" :data="tableData" element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading" style="width: 100%" height="83vh">
          <el-table-column prop="operation" width="100" :label="$t('GLOBAL._ACTIONS')" align="center">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="showDialog(scope.row)" :disabled="scope.row.Status == 'NotSplit' ? true : false">{{ $t('GLOBAL._TD') }}</el-button>
            </template>
          </el-table-column>
          <el-table-column v-for="(item, index) in tableHead" :key="index" :prop="item.field" :label="item.label">
            <template slot-scope="scope">
              <span v-if="['PercentQuantity', 'AdjustPercentQuantity'].includes(item.field)">
                {{ scope.row[item.field] ? `${scope.row[item.field]}` : '-' }}
              </span>
              <span v-else-if="item.field === 'Status'"> {{ status[scope.row[item.field]] }} </span>
              <span v-else> {{ scope.row[item.field] }} </span>
            </template>
          </el-table-column>

        </el-table>
      </div>
    </el-drawer>
    <BomDetailForm @saveForm="getTableData" ref="text" />
  </div>
</template>

<script>
import { getWeekScheduleBomList } from '@/api/planManagement/weekSchedule'
import BomDetailForm from './bomDetailForm'
export default {
  name: 'BomDetail',
  components: {
    BomDetailForm
  },
  data() {
    return {
      searchForm: {},
      drawer: false,
      tableData: [],
      tableHead: [
        {code: 'SegmentName', width: 100, align: 'center'},  
        {code: 'Sort', width: 80, align: 'center'},
        {code: 'MaterialCode', width: 150, align: 'left'},
        {code: 'MaterialName', width: 180, align: 'left'},
        {code: 'MaterialType', width: 100, align: 'left'},
        {code: 'InsteadMaterialCode', width: 150, align: 'left'},
        {code: 'InsteadMaterialName', width: 180, align: 'left'},
        {code: 'Unit', width: 100, align: 'center'},
        {code: 'StandardQuantity', width: 100, align: 'left'},
        {code: 'PlanQuantity', width: 100, align: 'left'},
        {code: 'Remark', width: 180, align: 'left'},
      ],
      loading: false,
      hansObjDrawer: this.$t('WeekSchedule.bomDetail'),
      sapSegmentMaterialId: 0,
      currentRow: {},
      status: {
        '1': 'Disable',
        '2': 'Released',
        '3': 'Pending_Release'
      }
    }
  },
  methods: {
    show(val) {
      this.currentRow = val
      this.sapSegmentMaterialId = val.ID
      this.drawer = true
      this.initTableHead()
      this.getTableData()
    },
    handleClose() {
      this.drawer = false
    },
    async getTableData() {
      const { response } = await getWeekScheduleBomList({
        WeekScheduleId: this.currentRow.ID,
        ...this.searchForm
      })
      this.tableData = response
    },
    initTableHead() {
      this.tableHead = []
      for (let key in this.hansObjDrawer) {
        this.tableHead.push({ field: key, label: this.hansObjDrawer[key] })
      }
    },
    showDialog(row) {
      this.$refs.text.show(row,this.currentRow.MaterialVersionId)
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer {
  :deep(.el-drawer__body) {
    padding-top: 10px;
    background-color: #FFFFFF;
    overflow-y: hidden
  }

  :deep(.el-form--inline) {
    height: 32px;
  }

  .title-box {
    font-size: 18px;
    color: #909399;
  }

  .pd5 {
    padding: 5px;
  }

  .table-box {
    padding: 0 10px;

    :deep(.el-button.is-disabled) {
      background-color: transparent !important;
      border: 0 !important;
    }

    i {
      margin-right: 5px;
      font-size: 15px !important;
      color: #67c23a;
    }
  }
}
</style>
import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url
//  人员排班列表
export function StaffSchedulingPageList(data) {
    return request({
        url: baseURL + '/api/StaffScheduling/GetPageList',
        method: 'post',
        data
    });
}
export function StaffSchedulingList(data) {
    return request({
        url: baseURL + '/api/StaffScheduling/GetList',
        method: 'post',
        data
    });
}
// 更新人员班次
export function SetStaffTeam(data) {
    return request({
        url: baseURL + '/api/StaffScheduling/SetStaffTeam',
        method: 'post',
        data
    });
}
// 人员删除
export function StaffSchedulingDelete(data) {
    return request({
        url: baseURL + '/api/StaffScheduling/Delete',
        method: 'post',
        data
    });
}
// 生成人员排班
export function GenerateStaffScheduling(data) {
    return request({
        url: baseURL + '/api/StaffScheduling/GenerateStaffScheduling',
        method: 'post',
        data
    });
}
// 人员排班 借调 缺勤 人数统计
export function StaffSchedulingGetStaffCount(data) {
    return request({
        url: baseURL + '/api/StaffScheduling/GetStaffCount',
        method: 'post',
        data
    });
}

//人员借调
export function StaffChangeLineSaveForm(data) {
    return request({
        url: baseURL + '/api/StaffChangeLine/SaveForm',
        method: 'post',
        data
    });
}

//人员归还
export function StaffReturn(data) {
    return request({
        url: baseURL + '/api/StaffScheduling/StaffReturn',
        method: 'post',
        data
    });
}

// 确认工时
export function handleConfirmTime(data) {
    return request({
        url: baseURL + '/api/StaffScheduling/ConfirmTimes',
        method: 'post',
        data
    });
}

export function getAttendanceTimeDetail(params) {
    return request({
        url: baseURL + '/api/StaffScheduling/GetStaffWorkTimeDetail?id=' + params.id,
        method: 'post',
    });
}

// 重新排班
export function resetClasses(data) {
    return request({
        url: baseURL + '/api/StaffScheduling/GenerateStaffScheduling?lineCode=' + data.lineCode,
        method: 'post',
        data
    });
}
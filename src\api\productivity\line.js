import { getRequestResources } from '@/api/fetch';
import request from '@/util/request';
const baseURL_30015 = 'baseURL_30015'
const DFM = 'baseURL_DFM'
const uploadHeaders = { 'authorization':'Bearer ' + JSON.parse(sessionStorage.getItem('vma')).auth.access_token}
//Line规格
export function GetLineSalesContainer(data) {
    const api = '/api/ProdtgtContainer/GetSalesContainer'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Line规格群组
export function GetLineSalesContainerGrp(data) {
    const api = '/api/ProdtgtContainer/GetSalesContainerGrp'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//物料模型
export function GetEquipmentListByLevel(data) {
    const api = '/api/Equipment/GetEquipmentTree';
    return getRequestResources(DFM, api, 'post', data);
}
//Line新增
export function GetLineSaveForm(data) {
    const api = '/api/ProdtgtContainer/SaveForm'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Line删除
export function GetLineDelete(data) {
    const api = '/api/ProdtgtContainer/Delete'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Line列表
export function GetLinePageList(data) {
    const api = '/api/ProdtgtContainer/GetPageList'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Line导入
export function GetLineImportData(data) {
    const api = '/api/ProdtgtContainer/ImportData'
    return getRequestResources(baseURL_30015, api, 'post', data,false,false,false,uploadHeaders);
}
//Line导入
export function GetLineImportBoxData(data) {
    const api = '/api/ProdtgtContainer/ImportBoxData'
    return getRequestResources(baseURL_30015, api, 'post', data,false,false,false,uploadHeaders);
}
export function ExportData(url, data) {
    return request({
        url: url,
        method: 'post',
        data,
        responseType: 'blob'
    });
}

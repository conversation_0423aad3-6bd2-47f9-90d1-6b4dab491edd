export function getbarstack(data, key) {
    let unit = "%"
    if (key) {
        unit = key
    }
    let option = {
        color: ["#78BE20", "#3366cc", "#cc6633", "#33ffff", "#BEBBF5"],
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function (params) {
                var relVal = params[0].name
                for (var i = 0, l = params.length; i < l; i++) {
                    if (params[i].value != 0 && params[i].value) {
                        relVal += '<br/>' + params[i].marker + params[i].seriesName + " : " + params[i].value + unit
                    }
                }
                return relVal
            }
        },
        textStyle: {
            color: '#fff',
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [

            {
                type: 'category',
                data: data.xdata,
                // nameTextStyle: {
                //     color: '#2C3E50',
                //     fontSize: 14,
                // },
                axisLabel: {
                    color: '#DCDFE6',
                    fontSize: 12
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: false,
                },
                axisLine: {
                    show: false,
                },
            }
        ],
        yAxis: [{
            name: unit,
            type: 'value',
            axisLabel: {
                color: 'rgb(196, 194, 194)',
                fontSize: 13
            },
            axisTick: {
                show: false,
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: 'rgb(196, 194, 194)',
                    type: 'dashed' //背景色为虚线
                }
            },
            axisLine: {
                show: false,
            },
        }],
        series: []
    };
    data.data.forEach((item, index) => {
        let thedata = [0, 0, 0]
        data.xdata.forEach((it, inx) => {
            if (it == item.stack) {
                thedata[inx] = item.data
            }
        })
        let obj = {
            name: item.name,
            type: 'bar',
            stack: "key",
            data: thedata,
            barMaxWidth: "100px",
            itemStyle: {
                color: item.color
            },
            label: {
                show: true,
                color: "black",
                formatter: function (params) {
                    if (params.value != 0) {
                        return params.value + unit
                    } else {
                        return ""
                    }
                }
            }
        }
        option.series.push(obj)
    })
    return option
}
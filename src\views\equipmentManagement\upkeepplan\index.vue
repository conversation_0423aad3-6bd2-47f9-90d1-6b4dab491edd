<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'SBDJGZ_MBXZ'" @click="templateDownload('spotCheckRules')">{{
                        $t('GLOBAL._MBXZ') }}</v-btn>
                    <v-btn color="primary" v-has="'SBDJGZ_DR'" @click="handleImport('spotCheckRules')">{{ $t('GLOBAL._DR')
                    }}</v-btn>
                    <v-btn color="primary" v-has="'SBDJGZ_ADD'" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="primary" v-has="'SBDJGZ_ALLREMOVE'" :disabled="!deleteList.length"
                        @click="btnClickEvet('delete')">{{
                            $t('GLOBAL._PLSC') }}</v-btn>
                </div>
                <Tables :page-options="pageOptions" :loading="loading" :btn-list="btnList"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'" table-name="TPM_SBGL_SBDJGZ"
                    :headers="keeprulesColum" :dictionaryList="dictionaryList" :desserts="desserts"
                    @selectePages="selectePages" @tableClick="tableClick" @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"></Tables>
                <createRepast ref="createRepast" :dialogType="dialogType" :mcCyclelist="mcCyclelist" :tableItem="tableItem">
                </createRepast>
            </v-card>
        </div>

        <div class="loading-box" v-if="importLoading">
            <a-spin tip="导入中..." :spinning="importLoading"></a-spin>
        </div>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';

import { DeviceMcRuleGetPagesList, DeviceMcRulesDelete } from '@/api/equipmentManagement/upkeep.js';
import { keeprulesColum } from '@/columns/equipmentManagement/upkeep.js';
const StartUsinglist = [
    { ItemName: '是', ItemValue: '1' },
    { ItemName: '否', ItemValue: '0' }
];
import equipment from '@/mixins/equipment'
export default {
    name: 'RepastModel',
    components: {
        createRepast: () => import('./components/createRepast.vue')
    },
    mixins: [equipment],
    data() {
        return {
            // tree 字典数据
            StartUsinglist,
            loading: true,
            showFrom: false,
            papamstree: {
                key: null,
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            keeprulesColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            hasChildren: {}, // 新增字典详情判断-子节点才能新增

            //
            mcCyclelist: [] // 维修周期
        };
    },
    computed: {
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'key',
                    label: this.$t('TPM_SBGL_SBDJGZ.McProject'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    icon: 'mdi-account-check',
                    // label: '工段',
                    label: this.$t('TPM_SBGL_SBDJGZ.MaintainCycle'),
                    placeholder: '',
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.mcCyclelist, 'ItemValue', 'ItemName'),
                    key: 'MaintainCycle'
                }
            ];
        },
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBDJGZ_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBDJGZ_DELETE'
                }
            ];
        },
        dictionaryList() {
            return [
                { arr: this.mcCyclelist, key: 'McCycle', val: 'ItemValue', text: 'ItemName' },
                { arr: this.StartUsinglist, key: 'StartUsing', val: 'ItemValue', text: 'ItemName' }
            ];
        }
    },
    mounted() {
        this.RepastInfoGetPage();
        this.GetMcCyclelist();
    },
    methods: {
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value)
            this.RepastInfoGetPage();
        },
        getdata() {
            this.RepastInfoGetPage();
        },
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                ...this.papamstree
            };
            this.loading = true;
            const res = await DeviceMcRuleGetPagesList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },

        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'warning' });
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await DeviceMcRulesDelete(params);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.RepastInfoGetPage();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        },
        // 获取设备维修周期
        async GetMcCyclelist() {
            const res = await this.$getDataDictionary('Cycle');
            this.mcCyclelist = res || [];
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}

.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

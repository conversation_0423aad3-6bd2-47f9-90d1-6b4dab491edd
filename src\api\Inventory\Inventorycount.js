import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Inventory'
const baseURL2 = 'baseURL_MATERIAL'

export function GetPageList(data) {
    const api = '/api/VerifiyDetail/GetPageList_YL'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetList(data) {
    const api = '/api/VerifiyDetail/GetYL_DetailByID'
    return getRequestResources(baseURL, api, 'post', data);
}
export function AddDetail_YL(data) {
    const api = '/api/VerifiyDetail/AddDetail_YL'
    return getRequestResources(baseURL, api, 'post', data);
}
export function ModifyDetail_YL(data) {
    const api = '/api/VerifiyDetail/ModifyDetail_YL'
    return getRequestResources(baseURL, api, 'post', data);
}
export function UpInVentAll(data) {
    const api = '/api/VerifiyDetail/UpInVentAll'
    return getRequestResources(baseURL, api, 'post', data);
}
export function SapReportWork(data) {
    const api = '/api/VerifiyDetail/SapReportWork'
    return getRequestResources(baseURL, api, 'post', data);
}
export function Add_YL(data) {
    const api = '/api/VerifiyDetail/Add_YL'
    return getRequestResources(baseURL, api, 'post', data);
}
export function Delete_YL(data) {
    const api = '/api/VerifiyDetail/Delete_YL'
    return getRequestResources(baseURL, api, 'post', data);
}
export function ModifyDetailQS_YL(data) {
    const api = '/api/VerifiyDetail/ModifyDetailQS_YL'
    return getRequestResources(baseURL, api, 'post', data);
}
export function NewAddDetail_YL(data) {
    const api = '/api/VerifiyDetail/NewAddDetail_YL'
    return getRequestResources(baseURL, api, 'post', data);
}

// export function Reverse(data) {
//     const api = '/api/ProductionSummaryDetailsView/Reverse'
//     return getRequestResources(baseURL, api, 'post', data);
// }
// export function GetReasonCode(data) {
//     const api = '/api/Dataitemdetail/GetReasonCode'
//     return getRequestResources(baseURL, api, 'post', data);
// }
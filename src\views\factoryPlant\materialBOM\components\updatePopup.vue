<template>
    <v-card>
        <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
            {{ operaObj.ID ? $t('DFM_WLBOMGL.editBOM') : $t('DFM_WLBOMGL.addBOM') }}
            <v-icon @click="closePopup">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid" class="mt-8">
                <v-row>
                    <v-col :cols="12" :lg="6">
                        <!-- label="类型" -->
                        <v-text-field v-model="form.BomType" :label="$t('DFM_WLBOMGL.BomType')" :rules="rules.BomType" required dense outlined></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <!-- label="成品料号" -->
                        <v-select
                            v-model="form.MaterialCode"
                            :rules="rules.MaterialCode"
                            :items="formSelectCode"
                            item-text="name"
                            item-value="value"
                            :label="$t('$vuetify.dataTable.DFM_WLBOMGL.MaterialCode')"
                            persistent-hint
                            return-object
                            dense
                            outlined
                            @change="changeProductNo"
                        ></v-select>
                    </v-col>
                    <!-- <v-col :cols="12" :lg="6">
                        <v-text-field v-model="form.RoutingType" label="成品料号" disabled dense outlined></v-text-field>
                    </v-col> -->
                    <v-col :cols="12" :lg="6">
                        <!-- label="成品名称" -->
                        <v-text-field v-model="form.MaterialName" :label="$t('$vuetify.dataTable.DFM_WLBOMGL.MaterialName')" disabled dense outlined></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <!-- label="版本号"  -->
                        <v-text-field v-model="form.Version" :label="$t('DFM_WLBOMGL.Version')" dense outlined></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <!-- label="BOM使用标志"  -->
                        <v-text-field v-model="form.BomUsage" :label="$t('$vuetify.dataTable.DFM_WLBOMGL.BomUsage')" required dense outlined></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <!-- label="替代BOM"  -->
                        <v-text-field v-model="form.AltBom" :label="$t('$vuetify.dataTable.DFM_WLBOMGL.AltBom')" dense outlined></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <!-- label="数量" -->
                        <v-text-field v-model="form.Quantity" :label="$t('$vuetify.dataTable.DFM_WLBOMGL.Quantity')" :rules="rules.Quantity" dense outlined></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <!-- label="单位" -->
                        <!-- <v-text-field v-model="form.Uom" :label="$t('$vuetify.dataTable.DFM_WLBOMGL.Uom')" required dense outlined></v-text-field> -->
                        <v-select
                            v-model="form.Uom"
                            :items="unitList"
                            item-text="Name"
                            item-value="Name"
                            no-data-text="暂无数据"
                            clearable
                            dense
                            required
                            outlined
                            :label="$t('$vuetify.dataTable.DFM_WLBOMGL.Uom')"
                        />
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <!-- label="状态" -->
                        <!-- <v-text-field v-model="form.Status" :label="$t('$vuetify.dataTable.DFM_WLBOMGL.Status')" required dense outlined></v-text-field> -->
                        <v-select
                            v-model="form.Status"
                            :items="statusList"
                            item-text="name"
                            item-value="value"
                            :label="$t('$vuetify.dataTable.DFM_WLBOMGL.Status')"
                            persistent-hint
                            dense
                            outlined
                        ></v-select>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <v-menu v-model="startTimeMenu" :close-on-content-click="false" transition="scale-transition" offset-y max-width="290px" min-width="290px">
                            <template #activator="{ on, attrs }">
                                <!-- label="有效期自" -->
                                <v-text-field v-model="form.EffectStart" readonly :label="$t('$vuetify.dataTable.DFM_WLBOMGL.EffectStart')" outlined dense v-bind="attrs" v-on="on"></v-text-field>
                            </template>
                            <v-date-picker v-model="form.EffectStart" :day-format="date => date.split('-')[2]" locale="zh-cn" no-title @input="startTimeMenu = false"></v-date-picker>
                        </v-menu>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <v-menu v-model="endTimeMenu" :close-on-content-click="false" transition="scale-transition" offset-y max-width="290px" min-width="290px">
                            <template #activator="{ on, attrs }">
                                <!-- label="有效期至" -->
                                <v-text-field v-model="form.EffectEnd" readonly :label="$t('$vuetify.dataTable.DFM_WLBOMGL.EffectEnd')" outlined dense v-bind="attrs" v-on="on"></v-text-field>
                            </template>
                            <v-date-picker v-model="form.EffectEnd" :day-format="date => date.split('-')[2]" locale="zh-cn" no-title @input="endTimeMenu = false"></v-date-picker>
                        </v-menu>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>

        <v-divider></v-divider>

        <v-card-actions>
            <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { saveForm } from '@/api/factoryPlant/materialBOM.js';
import { finishedProductNo } from '@/util/publicConstants.js';
export default {
    props: {
        unitList: {
            type: Array,
            default: () => []
        },
        operaObj: {
            type: Object,
            default: () => {}
        },
        formSelectCode: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            statusList: [
                {
                    name: '禁用',
                    value: '0'
                },
                {
                    name: '启用',
                    value: '1'
                },
                {
                    name: '新建',
                    value: '2'
                }
            ],
            valid: true,
            checkbox: true,
            startTimeMenu: false,
            endTimeMenu: false,
            form: {
                ID: '',
                MaterialCode: '',
                AltBom: '',
                BomUsage: '',
                MaterialName: '',
                Uom: '',
                Status: '1',
                Quantity: '',
                EffectStart: '',
                EffectEnd: '',
                Version: '',
                BomType: '',
                Factory: '40012008'
            },
            rules: {
                MaterialCode: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                Quantity: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                BomType: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            },
            finishedProductNo
        };
    },
    watch: {
        operaObj: {
            handler(curVal) {
                for (const key in this.form) {
                    if (Object.hasOwnProperty.call(this.form, key)) {
                        this.form[key] = curVal[key] || this.form[key];
                    }
                }
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        // 表单提交
        async submitForm() {
            this.form.Factory = '40012008';
            if (this.$refs.form.validate()) {
                const res = await saveForm(this.form);
                const { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    if (this.operaObj.ID || this.checkbox) {
                        this.$emit('handlePopup', 'refresh');
                    } else {
                        this.$refs.form.reset();
                    }
                }
            }
        },
        closePopup() {
            this.$emit('handlePopup', 'close');
        },
        changeProductNo(o) {
            const { name, value } = o;
            this.form.MaterialCode = value;
            this.form.MaterialName = name;
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>

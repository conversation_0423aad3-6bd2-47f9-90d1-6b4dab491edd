<template>
    <el-dialog :title="dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')" :visible.sync="dialogVisible" width="700px"
      :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
      @close="dialogVisible = false">
      <el-form ref="dialogForm" :model="dialogForm" label-width="130px">
       

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="主键">{{dialogForm.id}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="关联文档ID">{{dialogForm.docId}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="操作类型(1-创建 2-修改 3-删除)">{{dialogForm.operationType}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="变更前值(JSON格式)">{{dialogForm.oldValue}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="变更后值(JSON格式)">{{dialogForm.newValue}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="操作人ID">{{dialogForm.operatorId}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="操作时间">{{dialogForm.operateTime}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="客户端IP">{{dialogForm.clientIp}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="创建时间">{{dialogForm.createdate}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="创建人ID">{{dialogForm.createuserid}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="修改时间">{{dialogForm.modifydate}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="修改人ID">{{dialogForm.modifyuserid}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="时间戳">{{dialogForm.updatetimestamp}}</el-form-item>
          </el-col>
    
          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="删除标记(0-未删 1-已删)">{{dialogForm.deleted}}</el-form-item>
          </el-col>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small"
          @click="submit()">确定
        </el-button>
      </div>
    </el-dialog>
  </template>
  

<script>
  import {
    getSopAuditDetail,
    saveSopAuditForm
  } from "@/api/SOP/sopAudit";

  export default {
    components:{
      
    },
    data() {
      return {
        dialogForm: {},
        dialogVisible: false,
        formLoading: false,
        lineOptions: [],
        targetLineOptions: [],
        currentRow: {},
        matInfo:{}
      }
    },
    mounted() {
    },
    methods: {
      submit() {
        saveSopAuditForm(this.dialogForm).then(res=>{
          this.$message.success(res.msg)
          this.$emit('saveForm')
          this.dialogVisible = false
        })
      },
      show(data) {
        this.dialogForm = {}
        this.currentRow = data
        this.dialogVisible = true
        this.$nextTick(_ => {
          if(data.ID){
            this.getDialogDetail(data.ID)
          }
        })
      },
      getDialogDetail(id){
        getSopAuditDetail(id).then(res => {
          this.dialogForm = res.response
        })
      },
    }
  }
  </script>
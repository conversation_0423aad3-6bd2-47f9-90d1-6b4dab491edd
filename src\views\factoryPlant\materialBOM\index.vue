// 物料BOM管理
<template>
    <div class="meterial-bom-view">
        <div class="meterial-bom-main">
            <SearchForm ref="contactTorm" :searchinput="searchinput" :show-from="showFrom" @searchForm="searchForm" />
            <v-card outlined>
                <div class="form-btn-list">
                    <v-btn class="float-left mx-4" icon @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        <!-- 搜索栏 -->
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary">
                        <v-icon @click="getDataList">mdi-cached</v-icon>
                    </v-btn>
                    <!-- {{ $t('GLOBAL._XZ') }} -->
                    <v-btn color="primary" v-has="'WLBOMGL_ADD'" @click="operaClick({})">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <!-- 批量删除 -->
                    <v-btn color="primary" v-has="'WLBOMGL_ALLREMOVE'" @click="deleteItems()">{{ $t('GLOBAL._PLSC')
                    }}</v-btn>
                    <v-btn color="primary" v-has="'WLBOMGL_TB'" @click="openAsyncPopup()">{{ $t('GLOBAL._TB') }}</v-btn>
                </div>
                <Tables :table-height="tableHeight" :headers="headers" :desserts="desserts" :loading="loading"
                    :page-options="pageOptions" :btn-list="btnList" :click-fun="selectedPath"
                    :current-select-id="currentSelectId" table-name="DFM_WLBOMGL" @selectePages="selectePages"
                    @itemSelected="selectedItems" @toggleSelectAll="selectedItems" @tableClick="tableClick">
                    <template #Status="{ item }">
                        {{ item.Status == '0' ? '禁用' : item.Status == '1' ? '启用' : item.Status == '2' ? '新建' : '' }}
                    </template>
                </Tables>
            </v-card>
            <v-card class="mt-5">
                <div class="form-btn-list mt-0">
                    <v-row>
                        <v-col :cols="4" :lg="4">
                            <v-tabs>
                                <!-- BOM明细列表 -->
                                <v-tab>{{ $t('DFM_WLBOMMX.WLBOMGL') }}</v-tab>
                            </v-tabs>
                        </v-col>
                        <v-col class="d-flex align-center" style="justify-content: end" :cols="8" :lg="8">
                            <v-icon @click="$refs.product.getDataList()" class="mr-2">mdi-cached</v-icon>
                            <v-btn color="primary" @click="isfold = !isfold">{{ $t('GLOBAL.UnfoldFold') }}</v-btn>
                            <v-btn color="primary" v-has="'WLBOMGL_XQADD'" class="ml-2 mr-2" @click="operaDetailPop('')">{{
                                $t('GLOBAL._XZ')
                            }}</v-btn>
                            <!-- <v-btn color="primary" class="mr-2" @click="operaDetailPop('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn> -->
                        </v-col>
                    </v-row>
                </div>
                <Product ref="product" :unitList="unitList" :isfold="isfold" :clickFlag="clickFlag"
                    :current-select-id="currentSelectId" :current-select="currentSelect" />
            </v-card>
            <!-- 添加/编辑物料BOM -->
            <v-dialog v-model="isUpdate" scrollable width="720px">
                <updatePopup :unitList="unitList" v-if="isUpdate" :opera-obj="operaObj" :form-select-code="formSelectCode"
                    @handlePopup="handlePopup" />
            </v-dialog>

            <v-dialog persistent scrollable width="400px" v-model="isShowAsyncPopup">
                <v-card>
                    <v-card-title class="headline primary lighten-2" primary-title> {{ $t('GLOBAL._TB')
                    }}</v-card-title>
                    <v-card-text>
                        <v-row class="mt-3 mb-5">
                            <v-col :cols="12" lg="12">
                                <v-text-field :label="$t('DFM_WLBOMGL._WLH')" dense outlined color="primary"
                                    v-model="materialCode"></v-text-field>
                            </v-col>
                        </v-row>
                    </v-card-text>
                    <v-divider></v-divider>

                    <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn color="primary" :loading="btnLoading" @click="handleSynchronization()">{{ $t('GLOBAL._QD')
                        }}</v-btn>
                        <v-btn color="normal" @click="isShowAsyncPopup = false">{{ $t('GLOBAL._GB') }}</v-btn>
                    </v-card-actions>
                </v-card>
            </v-dialog>
        </div>
    </div>
</template>
<script>
import { GetPageList, DeleteMaterials, getSelectClassifyList, GetClassifyTree, getUnitList, doSynchronization } from '@/api/factoryPlant/materialBOM.js';
import { materialBOMColum } from '@/columns/factoryPlant/materialManagement.js';
export default {
    name: 'ReasonDetail',
    components: {
        updatePopup: () => import('./components/updatePopup.vue'),
        Product: () => import('./components/product.vue')
    },
    data() {
        return {
            btnLoading: false,
            materialCode: '',
            isShowAsyncPopup: false,
            isfold: false,
            clickFlag: '',
            showFrom: false,
            deleteId: '',
            isUpdate: false,
            operaObj: {},
            headers: materialBOMColum,
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            selectedList: [],
            desserts: [],
            tableHeight: 'calc(50vh - 190px)',
            searchParams: {},
            currentSelectId: '',
            currentSelect: {}, //选中的table
            currentMaterialCode: '',
            // 新增弹框   上级物料 子物料号 集合
            formSelectCode: [], //成品料号
            unitList: []
        };
    },
    computed: {
        //查询条件
        searchinput() {
            return [
                // '工厂'
                // {
                //     key: 'Factory',
                //     value: '40012008',
                //     icon: '',
                //     label: this.$t('DFM_WLBOMGL.Factory')
                // },
                // 料号'
                {
                    key: 'MaterialCode',
                    value: '',
                    icon: '',
                    label: this.$t('DFM_WLBOMGL.LHMC')
                }
            ];
        },
        btnList() {
            return [
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary', authCode: 'WLBOMGL_EDIT' },
                { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'red', authCode: 'WLBOMGL_DELETE' }
            ];
        }
    },
    mounted() {
        this.getUnitList();
        this.getDataList();
        this.GetClassifyCode();
    },
    methods: {
        openAsyncPopup() {
            this.materialCode = ''
            this.isShowAsyncPopup = true
        },
        async handleSynchronization() {
            if (!this.materialCode) {
                this.$store.commit('SHOW_SNACKBAR', { text: "物料号不能为空", color: 'error' });
                return false
            }
            this.btnLoading = true
            try {
                await doSynchronization({ materialCode: this.materialCode, bVersion: '1', sVersion: '' })
                this.$store.commit('SHOW_SNACKBAR', { text: "同步成功！", color: 'primary' });
                this.btnLoading = false
                this.getDataList()
                this.isShowAsyncPopup = false
            } catch {
                this.btnLoading = false
            }
        },
        async getUnitList() {
            let resp = await getUnitList({});
            this.unitList = resp.response;
        },
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        // 操作栏按钮
        tableClick(item, type) {
            switch (type) {
                // 编辑
                case 'edit':
                    this.operaClick(item);
                    break;
                // 删除
                case 'delete':
                    this.deleteId = item.ID;
                    this.sureDelete();
                    break;
                default:
                    break;
            }
        },
        //点击表格行
        selectedPath(o) {
            this.clickFlag = new Date().getTime().toString();
            this.currentSelect = o;
            this.currentSelectId = o.ID;
        },
        // 获取全部表格数据
        async getDataList() {
            this.loading = true;
            let params = {
                materialCode: this.searchParams.MaterialCode,
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await GetPageList(params);
            const { success, response } = res || {};
            const { data, dataCount, page } = response || {};
            if (success) {
                this.desserts = data || [];
                this.selectedPath(this.desserts.length > 0 ? this.desserts[0] : {});
                this.pageOptions.total = dataCount;
                this.pageOptions.page = page;
            } else {
                this.desserts = [];
                this.selectedPath({});
            }
            this.loading = false;
        },
        // 查询数据
        searchForm(v) {
            console.log(v);
            this.searchParams = v;
            this.getDataList();
        },
        // 新增/编辑BOM
        operaClick(v) {
            this.operaObj = v;
            this.isUpdate = true;
        },
        // 批量删除
        deleteItems() {
            if (this.selectedList.length > 0) {
                this.deleteId = '';
                this.sureDelete();
            } else {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'error' });
            }
        },
        // 操作BOM明细
        operaDetailPop(type) {
            if (type === 'delete') {
                if (this.$refs.product.selectedList.length === 0) {
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'error' });
                } else {
                    this.$refs.product.sureDelete();
                }
            } else {
                if (this.currentSelectId) {
                    this.$refs.product.operaObj = {};
                    this.$refs.product.isUpdateDetail = true;
                } else {
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('DFM_WLBOMGL.selectBom'), color: 'error' });
                }
            }
        },
        handlePopup(type, data) {
            switch (type) {
                case 'refresh':
                    this.isUpdate = false;
                    this.getDataList();
                    break;
                case 'close':
                    this.isUpdate = false;
                    break;
                default:
                    break;
            }
        },
        // 确认删除
        sureDelete() {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    const params = [];
                    if (this.deleteId) {
                        params.push(this.deleteId);
                    } else {
                        this.selectedList.forEach(e => {
                            params.push(e.ID);
                        });
                    }
                    const res = await DeleteMaterials(params);
                    this.selectedList = [];
                    this.deleteId = '';
                    const { success, msg } = res;
                    if (success) {
                        this.pageOptions.pageCount = 1;
                        this.getDataList();
                        this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },

        // 获取成品料号
        async GetClassifyCode() {
            const res = await GetClassifyTree();
            let { response, success } = res;
            if (success) {
                this.formSelectCode = response;
            }
        }
    }
};
</script>
<style lang="scss">
.meterial-bom-main {
    .v-data-table__wrapper {
        max-height: 400px;
        overflow: auto;
    }
}
</style>
<style lang="scss" scoped>
.meterial-bom-view {
    display: flex;
    width: 100%;
    // padding: 12px 0;

    .meterial-bom-main {
        width: 100%;

        .col-lg-4.col-12.btn-box {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-left: auto;
            text-align: right;
            padding-right: 30px;

            .v-btn:nth-child(n + 1) {
                margin-left: 15px;
            }
        }
    }
}
</style>

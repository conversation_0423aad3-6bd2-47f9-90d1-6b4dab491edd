export const kpiColumn = [
    { text: '序号', value: 'Name', prop: "DataName", sortable: true },
    { text: '成品料号', value: 'Type', prop: "DateType" },
    { text: '产品名称', value: 'Model', prop: "ModelRefName" },
    { text: '成品料号版本', value: 'Year', width: 150 },
    { text: '备注', value: 'Month', width: 150 },
    { text: '最近修改时间', value: 'Value', prop: "Tgt", width: 160 },
    { text: '最近修改人', value: 'UnitName', width: 120 },
    { text: '操作', value: 'actions', width: 150 },
];
export const safeColumn = [
    { text: '序号', value: 'EvenType', prop: "Itemname", sortable: true },
    { text: '成品料号', value: 'ConfirmDate', prop: "DateOfOccurrence" },
    { text: '产品名称', value: 'Model', prop: "ModelRefName" },
    { text: '成品料号版本', value: 'Comment', prop: "Comments" },
    { text: '操作', value: 'actions', width: 150 },
];

export const kpiLineColumn = [
    { text: '产品名称', value: 'Model', prop: "ModelRefName" },
    { text: '序号', value: 'Line', prop: "LineId", width: 160, sortable: true },
    { text: '成品料号', value: 'Spec', prop: "SalesContainer", width: 160 },
    { text: '产品名称', value: 'SpecGroup', prop: "SalesContainerGrp", width: 160 },
    { text: '成品料号版本', value: 'Year', width: 150 },
    { text: '备注', value: 'Month', width: 150 },
    // { text: '最近修改时间', value: 'BudgetType', prop: "LossType" },
    { text: '最近修改人', value: 'BudgetOutput', prop: "Tgt" },
    { text: '最近修改人', value: 'UnitName', width: 120 },
    { text: '操作', value: 'actions', width: 150 },

];

export const kpiSauceColumn = [
    { text: '序号', value: 'Saucecategory', sortable: true, prop: "SauceType" },
    { text: '成品料号版本', value: 'Year', width: 150 },
    { text: '备注', value: 'Month', width: 150 },
    { text: '最近修改时间', value: 'Target', prop: "Tgt" },
    { text: '最近修改人', value: 'UnitName' },
    { text: '操作', value: 'actions', width: 150 },

];


export const materialAnnualtargetColumn = [
    { text: '序号', value: 'MaterialGroup', sortable: true, prop: "Item" },
    { text: '成品料号版本', value: 'Year' },
    { text: '最近修改时间', value: 'Target', prop: "Tgt" },
    { text: '最近修改人', value: 'UnitName' },
    { text: '操作', value: 'actions', width: 150 },

];

export const packagingAnnualtargetColumn = [
    { text: '序号', value: 'MaterialBigGroup', sortable: true, prop: "Item" },
    { text: '序号', value: 'MaterialSmallGroup', sortable: true, prop: "ItemGroup" },
    { text: '成品料号版本', value: 'Year' },
    { text: '最近修改时间', value: 'Target', prop: "Tgt" },
    { text: '最近修改人', value: 'UnitName' },
    { text: '操作', value: 'actions', width: 150 },

];

export const packagingbylineColumn = [
    { text: '序号', value: 'Line', sortable: true, prop: "LineName" },
    { text: '产品名称', value: 'Model', prop: "LineCode" },
    { text: '成品料号版本', value: 'Year' },
    { text: '最近修改时间', value: 'Target', prop: "Tgt" },
    { text: '最近修改人', value: 'UnitName' },
    { text: '操作', value: 'actions', width: 150 },

];

export const lineshutdownrateColumn = [
    { text: '序号', value: 'Line', sortable: true, prop: "LineName" },
    { text: '产品名称', value: 'Model', prop: "ModelRefName" },
    { text: '成品料号版本', value: 'Year' },
    { text: '最近修改时间', value: 'Target', prop: "Tgt" },
    { text: '最近修改人', value: 'UnitName' },
    { text: '操作', value: 'actions', width: 150 },

];

export const GroupSetColumn = [
    { text: '序号', value: 'SpecCode', sortable: true, prop: "SalesContainer" },
    { text: '产品名称', value: 'SpecName', prop: "Description" },
    { text: '成品料号版本', value: 'SpecGroup', prop: "ItemCode" },
    { text: '序号', value: 'detail', sortable: true, prop: "detail" },

];
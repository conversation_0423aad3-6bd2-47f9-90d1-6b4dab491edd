import request from '@/util/request';
import { getRequestResources } from '@/api/fetch';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TPM; // 配置服务url
const baseURL2 = 'baseURL_EQUIPMENT';
const baseURL3 = `baseURL_Resource`
let DFM = 'baseURL_DFM';
//获取设备列表
export function EquipGetPageList(data) {
    return request({
        url: baseURL + '/tpm/Equip/GetPageList',
        method: 'post',
        data
    });
}
//获取设备列表不分页
export function EquipGetList(data) {
    return request({
        url: baseURL + '/tpm/Equip/GetList',
        method: 'post',
        data
    });
}
//新增&保存
export function EquipSaveForm(data) {
    return request({
        url: baseURL + '/tpm/Equip/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function EquipDelete(data) {
    return request({
        url: baseURL + '/tpm/Equip/Delete',
        method: 'post',
        data
    });
}

// 关键件
export function EquipKeyPartsGetPageList(data) {
    return request({
        url: baseURL + '/tpm/EquipKeyParts/GetPageList',
        method: 'post',
        data
    });
}
//新增&保存
export function EquipKeyPartsSaveForm(data) {
    return request({
        url: baseURL + '/tpm/EquipKeyParts/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function EquipKeyPartsDelete(data) {
    return request({
        url: baseURL + '/tpm/EquipKeyParts/Delete',
        method: 'post',
        data
    });
}

// 设备备件
export function SparepartGetPageList(data) {
    return request({
        url: baseURL + '/tpm/Sparepart/GetPageList',
        method: 'post',
        data
    });
}
//新增&保存
export function SparepartSaveForm(data) {
    return request({
        url: baseURL + '/tpm/Sparepart/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function SparepartDelete(data) {
    return request({
        url: baseURL + '/tpm/Sparepart/Delete',
        method: 'post',
        data
    });
}

// 设备指标
export function EquipTargetGetPageList(data) {
    return request({
        url: baseURL + '/tpm/EquipTarget/GetPageList',
        method: 'post',
        data
    });
}
//新增&保存
export function EquipTargetSaveForm(data) {
    return request({
        url: baseURL + '/tpm/EquipTarget/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function EquipTargetDelete(data) {
    return request({
        url: baseURL + '/tpm/EquipTarget/Delete',
        method: 'post',
        data
    });
}

// Bom DeviceAccessories
export function DeviceAccessoriesGetPageList(data) {
    return request({
        url: baseURL + '/tpm/DeviceAccessories/GetPageList',
        method: 'post',
        data
    });
}
// BOM tree
export function DeviceAccessoriesGetTreeList(data) {
    return request({
        url: baseURL + '/tpm/DeviceAccessories/GetDeviceAccessoriesTree',
        method: 'post',
        data
    });
}
//新增&保存
export function DeviceAccessoriesSaveForm(data) {
    return request({
        url: baseURL + '/tpm/DeviceAccessories/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function DeviceAccessoriesDelete(data) {
    return request({
        url: baseURL + '/tpm/DeviceAccessories/Delete',
        method: 'post',
        data
    });
}

// 导入
export function EquipImport(data) {
    return request({
        url: baseURL + '/tpm/Equip/ImportExcel',
        method: 'post',
        data
    });
}

// 设备履历列表
export function EquipRecordGetPageList(data) {
    return request({
        url: baseURL + '/tpm/EquipHistory/GetPageList',
        method: 'post',
        data
    })
}

// 设备履历保存
export function saveEquipRecordForm(data) {
    return request({
        url: baseURL + '/tpm/EquipHistory/SaveForm',
        method: 'post',
        data
    })
}

// 设备履历删除
export function delEquipRecord(data) {
    return request({
        url: baseURL + '/tpm/EquipHistory/Delete',
        method: 'post',
        data
    })
}
// 设备台账导出
export function EquipmentExport(data) {
    return request({
        url: baseURL + '/tpm/Equip/ByList',
        method: 'post',
        data,
        responseType: 'blob'
    })
}



//设备状态
export function GetDeviceStatus(data) {
    const api = '/api/DataItemDetail/GetDetailTree?itemCode=DeviceStatus';
    return getRequestResources(DFM, api, 'post', data);
}

//设备分类
export function GetDeviceCategory(data) {
    const api = '/api/DeviceCategory/GetList';
    return getRequestResources(baseURL2, api, 'post', data);
}
//设备列表
export function GetDevicePageList(data) {
    const api = '/api/Device/GetPageList';
    return getRequestResources(baseURL2, api, 'post', data);
}

//导出
export function DeviceImport(data) {
    const api = `/api/Device/ImportData`;
    return getRequestResources(baseURL2, api, 'post', data);
}
//导出
export function PartImport(data) {
    const api = `/api/Parts/ImportData`;
    return getRequestResources(baseURL2, api, 'post', data);
}
//设备保存
export function DeviceSaveForm(data) {
    const api = '/api/Device/SaveForm';
    return getRequestResources(baseURL2, api, 'post', data);
}


//设备删除
export function DeviceDelete(data) {
    const api = '/api/Device/Delete';
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetListCostCenter(data) {
    const api = '/api/CostCenter/GetList';
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetListAssetMaster(data) {
    const api = '/api/AssetMaster/GetList';
    return getRequestResources(baseURL2, api, 'post', data);
}
//点检记录
export function GetListSpotCheckWo(data) {
    const api = '/api/SpotCheckWo/GetList';
    return getRequestResources(baseURL2, api, 'post', data);
}
//保养记录
export function GetListMaintainWo(data) {
    const api = '/api/MaintainWo/GetList';
    return getRequestResources(baseURL2, api, 'post', data);
}
//维修记录
export function GetListRepairRecord(data) {
    const api = '/api/RepairRecord/GetList';
    return getRequestResources(baseURL2, api, 'post', data);
}
//备件使用
export function GetListPartsHistoryDetail(data) {
    const api = '/api/PartsHistoryDetail/GetList';
    return getRequestResources(baseURL2, api, 'post', data);
}




//导出公共方法
export function GetExportData(url, data) {
    return request({
        url: url,
        method: 'post',
        data,
        responseType: 'blob'
    });
}

//获取人员公共方法
export function GetPersonList(code) {
    const api = `/api/ReasontreeDetail/GetReasonTreeDetailTree?reasonTreeCode=${code}&level=1`;
    return getRequestResources(baseURL3, api, 'post');
}
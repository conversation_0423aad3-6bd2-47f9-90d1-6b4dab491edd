<template>
    <v-dialog v-model="showDialog" max-width="1080px">
        <v-card>
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ dialogType == 'add' ? $t('GLOBAL._XZ') : dialogType == 'edit' ? $t('GLOBAL._BJ') : $t('GLOBAL._CheckFile') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8" v-if="dialogType == 'add' || dialogType == 'edit'">
                        <v-col class="py-0 px-3" cols="12" :sm="item.sm ? item.sm : 3" :md="item.sm ? item.sm : 3" v-for="(item, index) in SbxxList" :key="index">
                            <v-text-field v-if="item.type == 'input'" :disabled="item.disabled" :id="item.id + 'SbxxList'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                            <v-autocomplete
                                :disabled="item.disabled"
                                v-if="item.type == 'select'"
                                :id="item.id + 'SbxxList'"
                                clearable
                                :multiple="item.multiple"
                                v-model="item.value"
                                :items="item.option"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="item.label"
                                clear
                                dense
                                outlined
                            ></v-autocomplete>
                            <v-menu
                                v-if="item.type == 'date' || item.type == 'datetime'"
                                :ref="'menu' + index"
                                v-model="menu[index]"
                                :close-on-content-click="false"
                                :nudge-right="40"
                                transition="scale-transition"
                                offset-y
                                max-width="290px"
                                min-width="290px"
                            >
                                <template #activator="{ on, attrs }">
                                    <v-text-field
                                        v-model="item.value"
                                        :clearable="item.isClearable ? item.isClearable : true"
                                        outlined
                                        dense
                                        :label="item.label"
                                        readonly
                                        v-bind="attrs"
                                        v-on="on"
                                    ></v-text-field>
                                </template>
                                <v-date-picker :disabled="item.disabled" :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></v-date-picker>
                            </v-menu>
                            <div class="textfieldbox">
                                <v-text-field
                                    v-model="item.value"
                                    :clearable="item.isClearable ? item.isClearable : true"
                                    outlined
                                    dense
                                    v-if="item.type == 'time'"
                                    :label="item.label"
                                    readonly
                                ></v-text-field>
                                <el-date-picker
                                    :disabled="item.disabled"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    v-if="item.type == 'time'"
                                    v-model="item.value"
                                    type="datetime"
                                    :placeholder="item.label"
                                ></el-date-picker>
                            </div>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="3" md="3" v-if="dialogType == 'add' || dialogType == 'edit'">
                            <el-upload ref="upload" class="upload-demo" :auto-upload="false" action="" :on-change="FileChange" :on-remove="FileRemove" multiple :limit="1" :file-list="FileList">
                                <v-btn color="primary">{{ $t('$vuetify.dataTable.TPM_SBGL_WDBX.sctp/sp') }}</v-btn>
                            </el-upload>
                        </v-col>
                    </v-row>
                    <v-row class="pt-8" v-if="dialogType == 'ck'">
                        <v-col class="py-0 px-3" cols="12" sm="12" md="12">
                            <img :src="imgsrc" style="width: 100%" />
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn v-if="dialogType == 'add' || dialogType == 'edit'" color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { GetRepairOrderSaveForm, GetRepairOrderGetFileUrl, GetRepairOrderUploadFile, GetRepairOrderEntity } from '@/api/equipmentManagement/MyRepair.js';
import { Message, MessageBox } from 'element-ui';
export default {
    props: {
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        },
        SourceList: {
            type: Array,
            default: () => []
        },
        TypeList: {
            type: Array,
            default: () => []
        },
        DevList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            menu: [],
            FileList: [],
            FilePath: '',
            SbxxList: [
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.bxsb'),
                    value: '',
                    id: 'DeviceId',
                    sm: 6,
                    option: this.DevList,
                    required: true,
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.gdlx'),
                    value: '',
                    id: 'Type',
                    sm: 6,
                    option: [],
                    required: true,
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.gdly'),
                    value: '',
                    id: 'Source',
                    disabled: true,
                    sm: 6,
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.gzxx'),
                    value: '',
                    option: [],
                    sm: 6,
                    type: 'select',
                    id: 'Phenomenon'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.bxlr'),
                    value: '',
                    sm: 12,
                    id: 'Description',
                    required: true,
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.bxsj'),
                    value: '',
                    sm: 4,
                    required: true,
                    id: 'ReportDate',
                    type: 'time'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.qwkssj'),
                    value: '',
                    sm: 4,
                    id: 'RequestStartDate',
                    type: 'time'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.qwwcsj'),
                    value: '',
                    sm: 4,
                    id: 'RequestFinishDate',
                    type: 'time'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.jjd'),
                    value: '',
                    option: [],
                    required: true,
                    sm: 4,
                    type: 'select',
                    id: 'Urgency'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.dbzg'),
                    value: '',
                    option: [],
                    sm: 4,
                    multiple: true,
                    required: true,
                    type: 'select',
                    id: 'DutyManager'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.sftj'),
                    value: '',
                    required: true,
                    option: [
                        {
                            ItemName: '是',
                            ItemValue: '是'
                        },
                        {
                            ItemName: '否',
                            ItemValue: '否'
                        }
                    ],
                    sm: 4,
                    type: 'select',
                    id: 'IsStop'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.wxzg'),
                    value: '',
                    option: [],
                    sm: 4,
                    type: 'select',
                    required: true,
                    id: 'RepairManager'
                }
            ],
            imgsrc: '',
            FileName: ''
        };
    },
    computed: {
        locale() {
            return this.$store.state.app.locale || 'zh';
        }
    },
    watch: {},
    mounted() {
        this.SbxxList.forEach(item => {
            if (item.required) {
                item.label = item.label + '*';
            }
        });
    },
    methods: {
        clearFiles() {
            this.FileList = [];
            this.$refs.upload.clearFiles();
        },
        GetDetail(row) {
            this.getFile(row.FileFullpath);
        },
        async getFile(name) {
            let res = await GetRepairOrderGetFileUrl('', name);
            window.open(res.response);
            // this.imgsrc = res.response;
            // this.showDialog = true;
        },
        async FileChange(File) {
            this.FileName = File.name;
            const formData = new FormData();
            formData.append('file', File.raw);
            let res = await GetRepairOrderUploadFile(formData);
            this.FilePath = res.response.FileFullpath;
        },
        async addSave() {
            let flag = this.SbxxList.some(item => {
                if (item.required) {
                    return item.value == '' || item.value == null;
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            let obj = {};
            if (this.dialogType == 'edit') {
                obj = this.tableItem;
            }
            this.SbxxList.forEach(item => {
                if (item.id != '') {
                    if (item.id == 'DeviceId') {
                        obj.DeviceId = item.value.split('|')[0];
                        obj.DeviceCode = item.value.split('|')[1];
                        obj.LineCode = item.value.split('|')[2];
                    } else if (item.id == 'DutyManager') {
                        if (item.value.length == 0) {
                            obj[item.id] = item.value;
                        } else {
                            obj[item.id] = item.value.join('|');
                        }
                    } else {
                        obj[item.id] = item.value;
                    }
                }
            });
            obj.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            obj.FileFullpath = this.FilePath;
            obj.FileName = this.FileName;
            const res = await GetRepairOrderSaveForm(obj);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.$parent.getTableData();
                this.FilePath = '';
                this.FileName = '';
                this.showDialog = this.classcheckbox ? false : true;
            }
        },
        FileRemove(File) {
            this.FilePath = '';
            this.FileName = '';
        },
        closeEquip() {
            this.showDialog = false;
            // this.$refs.form.reset();
        },
        closeDatePicker(index) {
            this.$set(this.menu, index, false);
        }
    }
};
</script>
<style lang="scss">
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
    .el-date-editor {
        width: 100%;
        position: absolute;
        opacity: 0;
        top: 0;
        .el-input__inner {
            font-size: 16px;
            height: 32.5px;
            color: rgba(0, 0, 0, 0.87);
            border: 1px solid #9e9e9e;
        }
        .el-input__icon {
            line-height: 32.5px;
        }
    }
}
</style>

<style lang="scss" scoped>
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
    .textfieldbox {
        position: relative;
    }
}

.ant-calendar-picker {
    ::v-deep input {
        border-color: #9e9e9e;
    }

    ::v-deep i {
        color: #757575;
    }
}
</style>

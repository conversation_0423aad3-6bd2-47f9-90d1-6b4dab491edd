export const stopRecordColumns = [
    {
        text: '产线',
        value: 'Line',
        width: 100,
        sortable: true
    },
    {
        text: '工段',
        value: 'Segment',
        width: 100,
        sortable: true
    },
    {
        text: '工站',
        value: 'Unit',
        width: 120,
        sortable: true
    },
    {
        text: '设备',
        value: 'MachineCode',
        width: 140,
        sortable: true
    },
    {
        text: '班组',
        value: 'Shift',
        width: 90,
        sortable: true
    },
    {
        text: '停机原因',
        value: 'Reason',
        width: 120,
        sortable: true
    },
    {
        text: '停机时长(分钟)',
        value: 'Difftime',
        width: 140,
        sortable: true,
        align: 'right'
    },
    {
        text: '损失量',
        value: 'LossQty',
        width: 120,
        sortable: true,
        align: 'right'
    },
    {
        text: '开始时间',
        value: 'StartTime',
        width: 160,
        sortable: true
    },
    {
        text: '结束时间',
        value: 'EndTime',
        width: 160,
        sortable: true
    },
    {
        text: '',
        value: 'actions1',
        width: 0,
        sortable: false
    },
]

export const stopDetailRecordColumns = [
    {
        text: '产线',
        value: 'Line',
        width: 100,
        sortable: true
    },
    {
        text: '工段',
        value: 'Segment',
        width: 100,
        sortable: true
    },
    {
        text: '工站',
        value: 'Unit',
        width: 120,
        sortable: true
    },
    {
        text: '设备',
        value: 'MachineCode',
        width: 140,
        sortable: true
    },
    // {
    //     text: '停机原因',
    //     value: 'StopReason',
    //     width: 120,
    //     sortable: true
    // },
    {
        text: '开始时间',
        value: 'StartTime',
        width: 160,
        sortable: true
    },
    {
        text: '结束时间',
        value: 'EndTime',
        width: 160,
        sortable: true
    },
    {
        text: '时长(分钟)',
        value: 'Duration',
        width: 120,
        sortable: true,
        align: 'right'
    },
    {
        text: '损失量',
        value: 'LossNum',
        width: 120,
        sortable: true,
        align: 'right'
    },
    {
        text: '返工量',
        value: 'ReworkNum',
        width: 120,
        sortable: true,
        align: 'right'
    },
    {
        text: '备注',
        value: 'Remark',
        width: 140,
        sortable: true
    },
    {
        text: '操作',
        value: 'actions',
        width: 120,
        sortable: false
    },
]
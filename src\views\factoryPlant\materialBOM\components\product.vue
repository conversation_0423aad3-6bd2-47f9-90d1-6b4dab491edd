<template>
    <div class="product-module">
        <v-card class="mt-1">
            <tree-table ref="treeTable" max-height="auto" class="tb-cate" border stripe :is-fold="isfold" get-checked-prop
                :data="desserts" :columns="initHead" :expand-type="false" :selection-type="false">
                <!-- 操作 -->
                <template slot="actions" slot-scope="scope">
                    <v-btn color="primary" v-has="'WLBOMGL_XQEDIT'" text small class="mx-0 px-0 mr-2"
                        @click="tableClick(scope.row, 'edit')">{{
                            $t('GLOBAL._BJ')
                        }}</v-btn>
                    <v-btn color="red" v-has="'WLBOMGL_XQDELETE'" text small class="mx-0 px-0 mr-2"
                        @click="tableClick(scope.row, 'delete')">{{
                            $t('GLOBAL._SC')
                        }}</v-btn>
                </template>
                <template slot="MaterialGroup" slot-scope="scope">
                    {{ showMaterialGroupText(scope.row.MaterialGroup) }}
                </template>
            </tree-table>
        </v-card>
        <!-- 添加/编辑物料BOM明细 -->
        <v-dialog v-model="isUpdateDetail" scrollable width="720px">
            <updateDetailPopup v-bind="$attrs" v-if="isUpdateDetail" :opera-obj="operaObj"
                :current-select-id="currentSelectId" @handlePopup="handlePopup" />
        </v-dialog>
    </div>
</template>

<script>
import { materialBOMDetail } from '@/columns/factoryPlant/materialManagement.js';
import { GetDetailPageList, DeleteMaterialsDetail, getSelectClassifyList } from '@/api/factoryPlant/materialBOM.js';
export default {
    components: {
        updateDetailPopup: () => import('../components/updateDetailPopup.vue')
    },
    props: {
        isfold: {
            type: Boolean,
            default: false
        },
        clickFlag: {
            type: String,
            default: ''
        },
        currentSelectId: {
            type: String,
            default: ''
        },
        currentSelect: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            deleteId: '',
            isUpdateDetail: false,
            materialBOMDetail,
            operaObj: {},
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            selectedList: [],
            desserts: [],
            tableHeight: '320px',
            materialTeamList: []
        };
    },
    computed: {
        initHead() {
            let headList = [];
            headList = this.materialBOMDetail.map(item => {
                item.label = this.$t(`$vuetify.dataTable.DFM_WLBOMMX.${item.prop}`); //  表表头对象名称
                return item;
            });
            return headList;
        },
        btnList() {
            return [
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary' },
                { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'red' }
            ];
        }
    },
    watch: {
        clickFlag: {
            handler() {
                this.getDataList()
            },
            deep: true,
            immediate: true
        }
    },
    created() {
        this.getMaterialTeamList();
    },
    methods: {
        showMaterialGroupText(code) {
            let text = '';
            let data = this.materialTeamList.find(item => item.Code == code);
            if (data) text = data.Name;
            return text;
        },
        async getMaterialTeamList() {
            let resp = await getSelectClassifyList({ Identities: 'MaterialGroup' });
            this.materialTeamList = resp.response;
        },
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        // 操作栏按钮
        tableClick(item, type) {
            switch (type) {
                // 编辑
                case 'edit':
                    this.operaClick(item);
                    break;
                // 删除
                case 'delete':
                    this.deleteId = item.ID;
                    this.sureDelete();
                    break;
                default:
                    break;
            }
        },
        // 获取全部表格数据
        async getDataList() {
            if (!this.currentSelectId) return;
            this.loading = true;
            let params = {
                // materialCode: this.currentSelect.MaterialCode,
                // factory: this.currentSelect.Factory,
                // version: this.currentSelect.Version,
                materialBomId: this.currentSelectId
                // pageIndex: this.pageOptions.pageCount,
                // pageSize: this.pageOptions.pageSize
            };
            const res = await GetDetailPageList(params);
            const { success, response } = res || {};
            // const { data, dataCount, page } = response || {};
            if (success) {
                this.desserts = response;
                // this.pageOptions.total = dataCount;
                // this.pageOptions.page = page;
            } else {
                this.desserts = [];
            }
            this.loading = false;
        },
        // 新增/编辑BOM
        operaClick(v) {
            this.operaObj = v;
            if (v) {
                console.log("21313")
            }
            this.isUpdateDetail = true;
        },
        handlePopup(type, data) {
            switch (type) {
                case 'refresh':
                    this.isUpdateDetail = false;
                    this.getDataList();
                    break;
                case 'close':
                    this.isUpdateDetail = false;
                    break;
                default:
                    break;
            }
        },
        // 确认删除
        sureDelete() {
            console.log(this.deleteId);
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            }).then(async () => {
                const params = [];
                if (this.deleteId) {
                    params.push(this.deleteId);
                } else {
                    this.selectedList.forEach(e => {
                        params.push(e.ID);
                    });
                }
                console.log(params);
                const res = await DeleteMaterialsDetail(params);
                this.selectedList = [];
                this.deleteId = '';
                const { success, msg } = res;
                if (success) {
                    this.pageOptions.pageCount = 1;
                    this.getDataList();
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.tb-cate {
    height: calc(50vh - 90px);
}

.tb-cate {
    position: relative !important;

    ::v-deep .zk-table__header-wrapper {
        position: absolute !important;
        z-index: 66 !important;
        overflow-y: scroll !important;
    }

    ::v-deep .zk-table__body-wrapper {
        margin-top: 40px !important;
        height: calc(50vh - 130px) !important;
        overflow-y: scroll !important;
    }
}
</style>

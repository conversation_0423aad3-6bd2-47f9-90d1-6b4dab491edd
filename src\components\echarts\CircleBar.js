export function getCircleBar(data) {
    console.log(data,2323)
    let option;
    option = {
        tooltip: {
            trigger: 'item'
        },
        color: ["#21EF9A", "#19CDF4"],
        legend: {
            textStyle: {
                color: "#fff"
            },
            left: 'left'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        series: [{
            name: 'Access From',
            type: 'pie',
            radius: '70%',
            data: data,
            label: {
                formatter: '{b}: {c}'
              },
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };
    return option
}
<template>
    <v-container v-if="showFrom && searchinput.length" class="px-0 py-3 mx-0 mt-2 justify-start coommon-search-form">
        <v-form ref="form" v-model="valid" class="inline-block" :key="formKey">
            <v-row>
                <v-col :cols="20" class="px-5 pt-4 pb-0">
                    <v-row :key="randonRow">
                        <v-col
                            v-for="(item, index) in searchinput"
                            :key="index"
                            class="px-1 py-0"
                            :cols="24"
                            :lg="item.type == 'checkbox' ? 6 : 3"
                            :md="item.type == 'checkbox' ? 6 : 3"
                        >
                            <v-autocomplete
                                v-if="item.type == 'select'"
                                v-model="item.value"
                                :multiple="item.multiple"
                                :items="item.selectData"
                                item-text="ItemName"
                                :item-value="item.key == 'Status' ? 'ItemValue' : item.byValue ? item.byValue : 'ItemName'"
                                clearable
                                dense
                                outlined
                                :label="item.label"
                                @change="selectChange(item)"
                                :placeholder="item.placeholder"
                            >
                                <template v-if="item.isShowCustom" #selection="{ item }">
                                    <span class="pr-2" style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden; cursor: pointer; z-index: 7" :title="item.value + ' | ' + item.label">
                                        {{ item.value + ' | ' + item.label }}
                                    </span>
                                </template>
                                <template v-if="item.isShowCustom" #item="data">
                                    <v-list-item-content>
                                        <v-list-item-subtitle v-html="data.item.value"></v-list-item-subtitle>
                                        <v-list-item-title v-html="data.item.label"></v-list-item-title>
                                    </v-list-item-content>
                                </template>
                            </v-autocomplete>
                            <v-combobox
                                v-else-if="item.type == 'combobox'"
                                v-model="item.value"
                                :items="item.selectData"
                                :search-input.sync="item.search"
                                item-text="label"
                                item-value="value"
                                hide-selected
                                :label="item.label"
                                @change="selectChange(item)"
                                persistent-hint
                                clearable
                                dense
                                outlined
                            >
                                <template #no-data>
                                    <v-list-item>
                                        <v-list-item-content>no data</v-list-item-content>
                                    </v-list-item>
                                </template>
                            </v-combobox>
                            <a-select
                                :maxTagCount="1"
                                :default-value="item.value"
                                v-else-if="item.type == 'customSelect'"
                                style="width: 100%"
                                mode="tags"
                                @change="
                                    value => {
                                        setData(item, value, index);
                                    }
                                "
                                :placeholder="item.label"
                                :options="item.selectData"
                            ></a-select>
                            <!-- <el-select style="width:100%" size="small" multiple filterable allow-create
                                v-else-if="item.type == 'customSelect'" @change="(value) => { setData(item, value, index) }"
                                :value="item.value" :placeholder="item.label">
                                <el-option v-for="itm in item.selectData" :key="itm.value" :label="itm.label"
                                    :value="itm.value">
                                </el-option>
                            </el-select> -->

                            <!-- 日期 -->
                            <v-menu
                                v-else-if="item.type == 'date'"
                                :ref="'menu' + index"
                                v-model="menu[index]"
                                :close-on-content-click="false"
                                :nudge-right="40"
                                transition="scale-transition"
                                offset-y
                                max-width="290px"
                                min-width="290px"
                            >
                                <template #activator="{ on, attrs }">
                                    <v-text-field
                                        v-model="item.value"
                                        :clearable="item.isClearable ? item.isClearable : true"
                                        outlined
                                        dense
                                        :label="item.label"
                                        readonly
                                        v-bind="attrs"
                                        v-on="on"
                                    ></v-text-field>
                                </template>
                                <v-date-picker :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></v-date-picker>
                            </v-menu>
                            <a-date-picker
                                :placeholder="item.label"
                                v-else-if="item.type == 'time'"
                                :default-value="item.value"
                                format="YYYY-MM-DD HH:mm:ss"
                                show-time
                                :locale="$dateLocale"
                                inputReadOnly
                                @change="
                                    (i, j) => {
                                        setData(i, j, index);
                                    }
                                "
                            />
                            <Treeselect
                                v-else-if="item.type == 'tree'"
                                v-model="item.value"
                                :placeholder="item.label"
                                noChildrenText="暂无数据"
                                noOptionsText="暂无数据"
                                :default-expand-level="4"
                                :normalizer="item.normalizer"
                                :options="item.selectData"
                                disableBranchNodes
                            />
                            <!-- <a-space direction="vertical" style="width: 100%" v-else-if="item.type == 'time'" >
                                <a-date-picker show-time v-model="item.value" @change="onChange"  :placeholder="item.label"/>
                            </a-space> -->
                            <!-- <v-text-field :label="item.label" v-model="item.value" dense outlined type="datetime-local"></v-text-field> -->
                            <v-checkbox class="mt-0" v-model="item.value" v-else-if="item.type == 'checkbox'" :label="item.label"></v-checkbox>
                            <v-text-field v-else :type="item.type ? item.type : 'text'" v-model.trim="item.value" dense outlined :label="item.label" :placeholder="item.placeholder" />
                        </v-col>
                        <v-col v-if="searchinput.length == 2 || searchinput.length == 1" class="text-lg-left px-4 py-0" :cols="4" :lg="4" :md="4">
                            <v-btn color="primary" @click="handleSubmitForm">{{ $t('GLOBAL._CX') }}</v-btn>
                            <v-btn class="ml-4" @click="handleCancelForm">{{ $t('GLOBAL._CZ') }}</v-btn>
                        </v-col>
                    </v-row>
                </v-col>
                <v-col v-if="searchinput.length > 2" class="text-lg-right px-4 py-0" :cols="24" :lg="3">
                    <v-btn color="primary" @click="handleSubmitForm">{{ $t('GLOBAL._CX') }}</v-btn>
                    <v-btn class="ml-4" @click="handleCancelForm">{{ $t('GLOBAL._CZ') }}</v-btn>
                </v-col>
            </v-row>
        </v-form>
    </v-container>
</template>

<script>
import Util from '@/util';
export default {
    components: {},
    props: {
        searchinput: {
            type: Array,
            default: () => []
        },
        showFrom: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            selected: [],
            itemTimeIndex: '',
            formKey: `form${new Date().getTime()}`,
            valid: true,
            menu: [],
            randonRow: new Date().getTime() + '',
            initValue: {}
        };
    },
    computed: {
        // dateRangeText() {
        //     return this.dates.join('~');
        // },
        locale() {
            return this.$store.state.app.locale || 'zh';
        }
    },
    watch: {
        searchinput(cur) {
            cur.forEach(e => {
                if (e.type == 'combobox' || e.type == 'select') {
                    const ev = typeof e.value;
                    if (e.value && (ev == 'string' || ev == 'number')) {
                        const o = e.selectData.find(i => i.value == e.value);
                        e.value = o || null;
                    }
                }
            });
            this.randonRow = new Date().getTime() + '';
        }
    },
    mounted() {
        this.searchinput.forEach((item, index) => {
            if (item.value) this.initValue[index + ''] = item.value;
            if (item.type == 'date') {
                this.menu[index] = false;
            }
        });
    },
    methods: {
        limitSelectNum(item) {
            console.log(item);
        },
        setData(i, j, n) {
            this.searchinput.forEach((item, k) => {
                if (k == n) this.$set(item, 'value', j);
            });
        },
        closeDatePicker(index) {
            this.$set(this.menu, index, false);
        },
        // 下拉框选择
        selectChange(item) {
            // 查询条件'
            if (item.multiple) {
                if (item.value.length == item.max) {
                    item.selectData.forEach(option => {
                        if (!item.value.includes(option.ItemValue)) {
                            option.disabled = true;
                        }
                    });
                } else {
                    // 启用所有选项
                    item.selectData.forEach(option => {
                        option.disabled = false;
                    });
                }
                return;
            }
            let params = {};
            this.searchinput.forEach(item => {
                const { value } = item.value || {};
                // params[item.key] = value || item.value;
                if (value !== null && value !== undefined && value !== '') {
                    params[item.key] = value;
                } else {
                    params[item.key] = item.value;
                }
            });
            this.$emit('selectChange', item, params);
            this.formKey = `form${new Date().getTime()}`;
        },
        // 重置
        handleCancelForm() {
            this.searchinput.forEach((item, index) => {
                item.value = '';
            });
            this.formKey = `form${new Date().getTime()}`;
            this.handleSubmitForm();
        },
        resetValue() {
            this.searchinput.forEach((item, index) => {
                item.value = this.initValue[index + ''] || '';
            });
            this.formKey = `form${new Date().getTime()}`;
        },
        handleSubmitForm() {
            Util.setSearchTarget();
            // 查询
            let params = {};
            this.searchinput.forEach(item => {
                const { value } = item.value || {};
                if (value !== null && value !== undefined && value !== '') {
                    params[item.key] = value;
                } else {
                    params[item.key] = item.value;
                }
            });
            this.$emit('searchForm', params);
        }
    }
};
</script>
<style lang="scss" scoped>
.coommon-search-form {
    .ant-calendar-picker {
        width: 100%;
    }

    .ant-input {
        border-color: rgba(0, 0, 0, 0.8);
    }

    .ant-calendar-picker:hover {
        border-color: rgba(0, 0, 0, 0.8);
    }
}

::v-deep .v-select__selections {
    input {
        position: absolute;
    }
}

::v-deep .ant-select-selection {
    height: 32px;
    border-color: #9e9e9e;
    color: #000;
}

::v-deep .ant-select-selection__rendered ul {
    padding-left: 0 !important;
}

.ant-calendar-picker {
    ::v-deep input {
        border-color: #9e9e9e;
    }

    ::v-deep i {
        color: #757575;
    }
}

::v-deep input::-webkit-input-placeholder {
    color: #757575;
}
</style>
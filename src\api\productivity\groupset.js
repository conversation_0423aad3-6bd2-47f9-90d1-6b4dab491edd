import { getRequestResources } from '@/api/fetch';
const baseURL_30015 = 'baseURL_30015'
const DFM = 'baseURL_DFM'

//kpi列表
export function GetSalescontainerPageList(data) {
    const api = '/ppm/Salescontainer/GetPageList'
    return getRequestResources(baseURL_30015, api, 'post', data);
}

//kpi列表保存
export function GetSalescontainerSaveForm(data) {
    const api = '/ppm/Salescontainer/SaveForm'
    return getRequestResources(baseURL_30015, api, 'post', data);
}


//kpi列表删除
export function GetSalescontainerDelete(data) {
    const api = '/ppm/Salescontainer/Delete'
    return getRequestResources(baseURL_30015, api, 'post', data);
}




//规格下拉
export function MyGetSalescontainer(data) {
    const api = '/ppm/Salescontainer/GetSalescontainer'
    return getRequestResources(baseURL_30015, api, 'post', data);
}


//规格群组下拉
// export function MyGetSalescontainerGrp(data) {
//     const api = '/ppm/Salescontainer/GetSalescontainerGrp'
//     return getRequestResources(baseURL_30015, api, 'post', data);
// }

//规格群组下拉
export function MyGetSalescontainerGrp(data) {
     const api = '/api/DataItemDetail/GetList?lang=cn&&itemCode=' + 'SalesContainerGroup'
     return getRequestResources(DFM, api, 'post', data)
}
    
<template>
  <div class="table-wrapper">
    <div style="display: flex;width: 100%;height: 30px;">
      <!-- <div
        style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;cursor: pointer;"
        @click="routeChange()"
      >{{ title }}</div> -->
      <div
        class="titimgbox"
        @click="routeChange(item.routePage?.TagCode)"
      >
        <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
        <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ title }}</div>
      </div>
      <div style="width: 50%;display: flex;">
        <dayMonIndex
          @showChack="getExceltable"
          :simlevel='simlevel'
          :position="Order"
          :Dimension="Dimension"
          :id1="id1"
          :BaseTime="BaseTime"
          :titletbale="title"
          :backgroundImg="backgroundImg"
          :echarstType="8"
        />
      </div>
    </div>
    <div
      class="styleTable"
      style="width: 96%;margin: 0 auto;"
    >
      <div
        class="tableDayBox_l"
        style="display: none;"
      >
        <div class="decq">当日出勤</div>
        <div class="tableDayBox_l_t">丙</div>
        <div class="tableDayBox_l_t">14/15</div>
      </div>
      <el-table
        :data="tableData"
        border
        :show-overflow-tooltip="true"
        :height="tableHeight"
        style="width: auto;color:#fff; font-size: 12px;font-weight: bold;overflow-y: auto;"
        :header-cell-style="{background:'#fafafa',textAlign: 'center',fontSize:'16px',}"
      >
        <el-table-column
          v-for="(header, index) in Object.keys(tableData[0])"
          :key="index"
          :prop="header"
          :label="header"
          header-align="center"
          align="center"
          show-overflow-tooltip
          style="color: #fff;"
        >
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { getqueryZ, getqueryLcr, getChartStructure, getTableList } from '@/api/simConfig/simconfignew.js';

export default {
  components: {
    dayMonIndex: () => import('@/views/simManagement/simNew1/components/dayMonIndex.vue'),
  },
  props: {
    Order: {
      type: String,
      default: ''
    },
    tableHeight: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    routeList: {
      type: String,
      default: ''
    },
    backgroundImg: {
      type: String,
      default: ''
    },
    id1: {
      type: String,
      default: ''
    },
    Dimension: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      intervalId: null,
      tableData: [{}],
      columns: [],
    }
  },

  created() {
    this.getBarList()
  },
  methods: {
    routeChange() {
      if (this.routeList != '' && this.routeList != null && this.routeList != undefined) {
        this.$router.push({ path: `${this.routeList}` })
      }
    },
    getExceltable() {
      this.getBarList()
    },

    async getBarList() {
      let params =
      {
        "simLevel": this.$route.path == '/simManagement/simSpot' ? 'SIM2' : this.Order.split('-')[0],
        "position": [
          this.Order
        ],
        "paramList": [
          this.simlevel,
          this.BaseTime
        ]
      }
      let res = await getTableList(params)
      if (res.success && res.response != null) {
        this.tableData = res.response[0].positionResult
      }
    },
  },
  beforeDestroy() {
    clearInterval(this.intervalId);
  },
}
</script>
<style scoped>
.styleTable /deep/.el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}
.styleTable /deep/ .el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}

.styleTable /deep/ .el-table tr {
    background-color: transparent !important;
    border: none;
}
.styleTable /deep/ .el-table--enable-row-transition .el-table__body td,
.el-table .cell {
    background-color: transparent !important;
}
.styleTable /deep/ .el-table th.el-table__cell {
    background-color: transparent !important;
    color: #fff;
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
    width: 15px;
    height: 0px;
}
.titimgbox {
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    width: 50%;
    height: 30px;
    line-height: 30px;
    border-radius: 5px;
    /* background-image: linear-gradient(to right, #056be0 0%, #000b61 100%); */
    display: flex;
    /* border: 1px solid #fff; */
    /* box-shadow: 0px 0px 7px 0px #fff; */
    /* overflow: hidden; */
}
</style>
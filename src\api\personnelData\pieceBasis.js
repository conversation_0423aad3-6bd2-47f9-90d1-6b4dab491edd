import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_SHIFT; // 配置服务url
//  薪资标准列表
export function WageStandardGetPageList(data) {
    return request({
        url: baseURL + '/shift/WageStandard/GetPageList',
        method: 'post',
        data
    });
}
// 删除
export function WageStandardDelete(data) {
    return request({
        url: baseURL + '/shift/WageStandard/Delete',
        method: 'post',
        data
    });
}
// 新增
export function WageStandardSaveForm(data) {
    return request({
        url: baseURL + '/shift/WageStandard/SaveForm',
        method: 'post',
        data
    });
}

//计件标准
export function PieceBasisGetPageList(data) {
    return request({
        url: baseURL + '/shift/PieceBasis/GetPageList',
        method: 'post',
        data
    });
}
// 删除
export function PieceBasisDelete(data) {
    return request({
        url: baseURL + '/shift/PieceBasis/Delete',
        method: 'post',
        data
    });
}
// 新增
export function PieceBasisSaveForm(data) {
    return request({
        url: baseURL + '/shift/PieceBasis/SaveForm',
        method: 'post',
        data
    });
}



//超产津贴标准
export function AllowanceStandardGetPageList(data) {
    return request({
        url: baseURL + '/shift/AllowanceStandard/GetPageList',
        method: 'post',
        data
    });
}
// 删除
export function AllowanceStandardDelete(data) {
    return request({
        url: baseURL + '/shift/AllowanceStandard/Delete',
        method: 'post',
        data
    });
}
// 新增
export function AllowanceStandardSaveForm(data) {
    return request({
        url: baseURL + '/shift/AllowanceStandard/SaveForm',
        method: 'post',
        data
    });
}

// 导入
export function doImport(data) {
    return request({
        url: baseURL + '/shift/PieceBasis/Improt',
        method: 'post',
        data
    });
}
export const processBasedColum = [
    // { label: '选择', prop: 'selectBox', width: '80px', type: 'template', template: 'selectBox' },
    { label: '序号', value: 'Index', width: '100px' },
    { label: '工序名称', value: 'ProcName', width: '190px' },
    { label: '工序代码', value: 'ProcCode', width: '140px' },
    { label: '工序组', value: 'ParentId', width: '140px', dictionary: true },
    // { label: '父级', prop: 'ParentCode', width: '140px' },
    // { label: '是否生成工单', value: 'IsCreateOrder', width: '160px' },
    { label: '标准加工时间', value: 'StandardTime', width: '180px' },
    { label: '标准加工单位', value: 'StandardUom', width: '160px' },
    { label: '准备时间', value: 'PrepareTime', width: '140px' },
    { label: '运输时间', value: 'TransTime', width: '140px' },
    { label: '标准用工人数', value: 'StandardPeronNum', width: '140px', semicolonFormat: true },
    { label: '设计产能', value: 'UnitCapacity', width: '140px' },
    { label: '描述', value: 'Description', width: '140px' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        label: '操作',
        width: '160',
        align: 'center',
        value: 'actions',
    }
];

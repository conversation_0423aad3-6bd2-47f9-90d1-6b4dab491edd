.v-modal {
    z-index: 2000 !important;
}

.el-message {
    z-index: 99999999 !important;
}
.v-dialog{
    background: #fff;
}
.el-message-box__wrapper {
    z-index: 99999999 !important;

}

.el-tooltip__popper.is-dark {
    z-index: 99999999 !important;
}

.dictionary-view .el-drawer__wrapper {
    z-index: 2050 !important;
}

.dictionary-view .el-dialog__wrapper {
    z-index: 2050 !important;
}

el- .v-application .v-autocomplete__content.v-menu__content {
    z-index: 2060 !important;
}

.v-data-table__wrapper tr td:first-child {
    background: inherit !important;
}

.v-data-table__wrapper tr td:last-child {
    background: #fff;
}

.dictionary-view .OverRow .text-start {
    background: #ebfdee !important;
}

.dictionary-view .OverRow .text-center {
    background: #ebfdee !important;
}

.v-application {
    .v-overlay {
        z-index: 201 !important;
    }

    .v-dialog__content {
        z-index: 2050 !important;
    }

    .v-snack__wrapper.theme--dark {
        background: #4caf50;
    }

    .v-treeview-node {
        width: fit-content;
        min-width: 100%;
        /* width: 350px; */
    }

    .creatTable {
        ::-webkit-scrollbar-thumb {
            background-color: #c0cedc;
            border-radius: 2px;
            outline: 0px solid #fff;
            outline-offset: -2px;
            border: 4px solid #fff;
        }

        .allCreatTable {
            display: flex;
            width: 100%;
            height: 545px;

            .CreatTableTitle {
                padding: 6px;
                height: 31px;
                width: 170px;
            }

            .v-data-table__wrapper {
                width: 100%;
            }

            .allCreatTableLeft {
                width: 24%;

                .tree-card {
                    max-width: 320px !important;
                    min-height: auto;
                    max-height: 590px;
                    overflow-y: auto;
                    overflow-x: auto;
                }
            }

            .allCreatTableRight {
                width: 76%;
                display: flex;
                flex-direction: column;
                height: 100%;

                .allCreatTablesingle {
                    width: 100%;
                    height: 50%;
                }
            }
        }

        .v-card__actions {
            display: flex;
            flex-direction: row-reverse;
        }
    }
}

.EquipmentSearch {

    display: flex;
    margin: 10px 0 0 10px;
    .addForm {
        margin-right: 5px;
    }
}

.el-date-picker {
    z-index: 2060 !important;
}

.el-date-range-picker {
    z-index: 2060 !important;
}

.el-select-dropdown {
    z-index: 2060 !important;
}

.textfieldbox {
    position: relative;
}

.textfieldbox .el-input {
    width: 100% !important;
}

.textfieldbox .el-date-editor {
    position: absolute;
    opacity: 0;
    top: 0;
}
import { getRequestResources } from '@/api/fetch';
const TPM = 'baseURL_TPM', DFM = 'baseURL_DFM'

// 
export function getTreeData(data) {
    const api = '/api/Equipment/GetEquipmentTree'
    return getRequestResources(DFM, api, 'post', data)
}
// 
export function getPointCheckList(data) {
    const api = '/tpm/MaintainCheck/FindMaintainCheckResult'
    return getRequestResources(TPM, api, 'post', data)
}
export function getMaintainList(data) {
    const api = '/tpm/Maintain/FindMaintainResult'
    return getRequestResources(TPM, api, 'post', data)
}
// 点检详情
export function getPointCheckDetail(data) {
    const api = '/tpm/MaintainCheckDetail/GetPageList'
    return getRequestResources(TPM, api, 'post', data)
}
// 保养详情
export function getMaintainDetail(data) {
    const api = '/tpm/MaintainDetail/GetPageList'
    return getRequestResources(TPM, api, 'post', data)
}
<template>
  <div>
    <div style="display: flex;width: 100%;height: 30px;">
      <div
        style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;cursor: pointer;"
        @click="routeChange()"
      >{{ title }}</div>
      <div style="width: 50%;display: flex;">
        <dayMonIndex
          :simlevel='simlevel'
          :position="Order"
          @showChack="getExcelpie"
          :Dimension="Dimension"
          :Particle="Particle"
          :id1="id1"
          :BaseTime="BaseTime"
          :titlemete="title"
          :backgroundImg="backgroundImg"
          :echarstType="7"
        />
      </div>
    </div>
    <div
      :id="id1"
      style="width:100%;height:100%;margin-top: 10px;"
    ></div>
    <!-- <div
      v-if="!showData"
      style="font-size: 14px;color: #fff;text-align: center;margin-top: -100px;"
    >暂无数据</div> -->
    <!-- <keyIndicatorslist
      ref="keyIndicatorsref"
      :exhibitionType="exhibitionType"
      :jtitle="title"
      :simlevel="simlevel"
      :Order="Order"
      :isSql="0"
      :BaseTime="BaseTime"
      :barName="barName"
    ></keyIndicatorslist> -->

    <keyIndicatorslistnew
      ref="keyIndicatorsrefnew"
      v-if="keyIndicatorslistnewShow"
      v-model="showkeyIndicatorslistnew"
      :exhibitionType="exhibitionType"
      :jtitle="title"
      :simlevel="simlevel"
      :Order="Order"
      :isSql="0"
      :BaseTime="BaseTime"
      :barName="barName"
      @keynew="heandleKeypie"
      :backgroundImg="backgroundImg"
    ></keyIndicatorslistnew>
  </div>
</template>
<script>
import { getqueryZ, getqueryLcr, getChartStructure } from '@/api/simConfig/simconfignew.js';

export default {
  components: {
    dayMonIndex: () => import('@/views/simManagement/simNew1/components/dayMonIndex.vue'),
    // keyIndicatorslist: () => import('@/views/simManagement/simNew1/components/keyIndicatorslist.vue'),
    keyIndicatorslistnew: () => import('@/views/simManagement/simNew1/components/keyIndicatorslistnew.vue'),
  },
  props: {
    id1: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    Order: {
      type: String,
      default: ''
    },
    configFlag: {
      type: String,
      default: ''
    },
    exhibitionType: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    Dimension: {
      type: Array,
      default: () => []
    },
    routeList: {
      type: String,
      default: () => ''
    },
    backgroundImg: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      keyIndicatorslistnewShow: false,
      showkeyIndicatorslistnew: false,
      barName: '',
      showData: true,
      Particle: '',
      jtitle: '',
      chartPie: null,
      pieTime: null,
      curConfig: {},
      // BaseTime: '2024-07-01',
      // TeamCode: 'A1180240614',
      // ProductionLineCode: 'A11802406',
      // FactoryCode: 'A118024',
      barTransverseChart: null,
      scrollTime: null,
      //当前时间颗粒度
      curShift: {
        KpiValues: []
      },
      myShiftList: [],
      id: '',
      yAxisOption: {
        type: 'value',
        // show: false
        axisLine: {
          show: false
        },
        axisLabel: {
          show: true
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      },
    }
  },
  computed: {
    //x轴配置
    xAxisOption() {
      let list = ['7', '6', '5', '4', '3', '2', '1']
      if (this.curShift.ChartData && this.curShift.ChartData.x) {
        list = this.curShift.ChartData.x.map(item => {
          // let month = dayjs(item).$M+1
          // let day = dayjs(item).$D
          if (['日', '周'].includes(this.curShift.TimeDimension)) {
            let month = item.split('-')[1]
            let day = item.split('-')[2]
            // 这里需要匹配颗粒度,来输出不同的x轴数据.
            // return `${month}-${day}`
            return `${month}-${day}`
          } else if (['月', '季度'].includes(this.curShift.TimeDimension)) {
            let label = item.split('年')[1]
            return label
          } else {
            return item
          }
        })
        // list.reverse()
        // list = this.curShift.ChartData.x
      }
      return {
        type: 'category',
        // boundaryGap: false,
        axisLine: {
          onZero: false,
          lineStyle: {
            color: '#888'
          },
        },
        axisLabel: {
          interval: 0,
          color: '#000'
        },
        data: list
      }
    },
    //目标值配置
    lineVisualMap() {
      // let list = []
      let obj = {
        show: false,
        // seriesIndex: 0,
        dimension: this.curConfig.ChartType === '3' ? '0' : '1',
        // dimension: 0,
        pieces: [
          {
            gt: 0,
            // lte: kpi.targetValue,
            lte: this.curShift.TargetValue,
            // color: kpi.noReachColor
            color: this.curShift.BelowTargetColer
            // color: 'red'
          },
          {
            // gt: kpi.targetValue,
            gt: this.curShift.TargetValue,
            // lte: 200,
            // color: kpi.reachColor
            color: this.curShift.AboveTargetColor
            // color: 'yellow'

          },
        ],
        // outOfRange: {
        //   color: '#999'
        // }
      }
      return [obj]

      // return [obj,obj2]
    },
    //折线图 Series  柱状图Series
    lineSeries() {
      //区分横竖
      let axisMarkLine = this.curConfig.ChartType === '5'
        ? [{ xAxis: this.curShift.TargetValue || '' }]
        : [{ yAxis: this.curShift.TargetValue || '' }]

      // let obj2 = {
      //   name: `${this.curShift.KpiName}实际值`,
      //   type: ['2','3'].includes(this.curConfig.ChartType)?'bar':'line',
      //   symbol: 'circle',
      //   symbolSize: 4,
      //   data: this.curShift.KpiValues.map(item=>item.DataValue),
      //   markLine: {//目标值线条
      //     silent: true,
      //     lineStyle: {
      //       color: this.curShift.TargetColor || 'gray'
      //       // color: 'red'
      //     },
      //     data: axisMarkLine
      //     // data: [{xAxis: 20 }]
      //   }
      // }
      let list = []
      Object.keys(this.curShift.ChartData).forEach(key => {
        if (['x', 'x', '目标值'].includes(key)) {
          return
        }
        let obj = {
          // name: `${this.curShift.KpiName}实际值`,
          // name: key.split(':')[1],
          name: `${key}实际值`,
          type: ['2', '3'].includes(this.curConfig.ChartType) ? 'bar' : 'line',
          symbol: 'circle',
          symbolSize: 4,
          // data: this.curShift.KpiValues.map(item=>item.DataValue),
          data: this.curShift.ChartData[key],
          markLine: {//目标值线条
            silent: true,
            lineStyle: {
              color: this.curShift.TargetColor || 'gray'
              // color: 'red'
            },
            data: axisMarkLine
            // data: [{xAxis: 20 }]
          }
        }
        list.push(obj)
      })
      return list
      // return [obj2]
    },
    //折线图配置
    lineOption() {
      return {
        symbol: 'circle',
        tooltip: {
          trigger: 'axis',
          extraCssText: 'z-index:999',
          axisPointer: {
            type: 'shadow',
          }
        },
        // legend: {
        //   data: this.lineLegend
        // },
        grid: {
          left: '5%',
          right: '6%',
          bottom: '3%',
          top: 20,
          containLabel: true
        },
        // visualMap: this.lineVisualMap,
        xAxis: this.xAxisOption,
        yAxis: this.yAxisOption,
        series: this.lineSeries
      }
    },
    bar1Option() {
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          }
        },
        // legend: {
        //   data: this.lineLegend
        // },
        grid: {
          left: '5%',
          right: '4%',
          bottom: '3%',
          top: 20,
          containLabel: true
        },
        // visualMap: this.lineVisualMap,
        xAxis: this.xAxisOption,
        yAxis: this.yAxisOption,
        series: this.lineSeries
      }
    },
    bar2Option() {
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          }
        },
        // legend: {
        //   data: this.lineLegend
        // },
        grid: {
          left: '5%',
          right: '4%',
          bottom: '3%',
          top: 20,
          containLabel: true
        },
        // visualMap: this.lineVisualMap,
        xAxis: this.yAxisOption,
        yAxis: this.xAxisOption,
        series: this.lineSeries
      }
    },
    //饼图配置
    pieOption() {

      return {
        tooltip: {
          trigger: 'item'
        },
        // legend: {
        //   orient: 'vertical',
        //   left: 'left'
        // },
        series: [
          {
            name: `${this.curShift.KpiName}`,
            type: 'pie',
            radius: '50%',
            // data: this.xAxisOption.data.map((item,index)=>{
            //   return {
            //     value: this.KPIList[0].practicalValueList[index],
            //     name: item
            //   }
            // }),
            data: this.curShift.KpiValues.map(item => {
              return {
                value: item.DataValue,
                name: item.DataTime
              }
            }),
            // [
            //   { value: 1048, name: 'Search Engine' },
            //   { value: 735, name: 'Direct' },
            //   { value: 580, name: 'Email' },
            //   { value: 484, name: 'Union Ads' },
            //   { value: 300, name: 'Video Ads' }
            // ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
    },
    //右侧时间列表
    // myTimeList(){
    //   return this.curConfig.timeTypeList.map(item=>{
    //     let target = this.mapList.find(foo=>foo.value == item)
    //     return target
    //   })
    // },
    //左侧班次列表
    // myShiftList(){
    //   if(this.curTime.name == '班次'){
    //     return this.shiftList1
    //   }else if(['日班','月班'].includes(this.curTime.name) ){
    //     return this.shiftList2
    //   }else{
    //     return []
    //   }
    // }
  },
  created() {
    this.getBarList()
  },
  methods: {
    heandleKeypie() {
      this.keyIndicatorslistnewShow = false
    },
    routeChange() {
      this.$router.push({ path: `${this.routeList}` })
      // this.$router.push({ path: 'simNew2', query: { code: item.Simcode } });
    },
    // openPopup() {
    //   if (this.configFlag == '是') {
    //     this.$refs.keyIndicatorsref.showDialog = true;
    //   }
    // },
    getExcelpie(data) {
      this.Particle = data
      this.getBarList()
    },
    async getBarList() {
      let params = {
        "Position": this.Order,
        "BaseTime": this.BaseTime,
        "TeamCode": this.simlevel,
        // "ProductionLineCode": this.ProductionLineCode,
        // "FactoryCode": this.FactoryCode
      }
      let { response } = await getChartStructure(params)
      console.log(response, '仪表盘数据');

      this.curConfig = response
      if (this.curConfig?.ChartConfigs != null) {
        // this.id = this.curConfig.ID;
        // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
        this.curConfig?.ChartConfigs.map(item => {
          item.KpiName = this.curConfig?.ChartConfigs.KpiName
          if (item.KpiValues[0]) {
            item.KpiCode = item.KpiValues[0].KpiCode
            item.TargetValue = item.KpiValues[0].TargetValue || 0
          }
        })
        //图表配置整体赋值
        // this.curConfig = response
        //时间颗粒度列表
        this.myShiftList = this.curConfig?.ChartConfigs.filter(item => {
          return item.TargetVisible === 1
        })
        //默认激活第一个时间颗粒度
        if (this.Particle != '') {
          this.myShiftList.map((el, index) => {
            if (this.Particle == el.TimeDimension) {
              this.curShift = this.myShiftList[index]
              this.query1(true)
            } else {
              this.query1(false)
            }
          })
        } else {
          this.curShift = this.myShiftList[0]
          this.Particle = this.curShift.TimeDimension
          this.query1(true)
        }
      }
    },
    // query1(data) {
    //   this.showData = data
    //   if (data == false) {
    //     this.chartPie.clear()
    //     return
    //   }

    //   this.chartPie = document.getElementById(this.id1);
    //   var myChart = this.$echarts.init(this.chartPie);
    //   // var bingData = [
    //   //   { value: 10, name: '事项1', itemStyle: { color: '#646DD5' } },
    //   //   { value: 5, name: '事项2', itemStyle: { color: '#4391F4' } },
    //   //   { value: 5, name: '事项3', itemStyle: { color: '#38BBE5' } },
    //   //   { value: 10, name: '事项4', itemStyle: { color: '#69D6FD' } },
    //   //   { value: 5, name: '事项5', itemStyle: { color: '#36C6A0' } },
    //   //   // { value: 5, name: 'M6', itemStyle: { color: '#4472c4' } },
    //   //   // { value: 10, name: 'M7', itemStyle: { color: '#70ad47' } },
    //   //   // { value: 5, name: 'M8', itemStyle: { color: '#255e91' } }
    //   // ]
    //   // var DataSum = 0
    //   // for (var i = 0; i < bingData.length; i++) {
    //   //   DataSum += parseInt(bingData[i].value);
    //   // }
    //   var colorList = ['#ff9900', '#255e91', '#70ad47', '#4472c4', '#36C6A0', '#646DD5', '#1155cc', '#e69138', '#9900ff', '#93c47d', '#ff9900', '#674ea7', '#00ff00', '#e69138', '#69D6FD'];

    //   var option
    //   option = {
    //     tooltip: {
    //       trigger: 'item'
    //     },
    //     // legend: {
    //     //   orient: 'vertical',
    //     //   left: 'left'
    //     // },
    //     series: [
    //       {
    //         name: `${this.curShift.KpiName}`,
    //         type: 'pie',
    //         radius: '50%',
    //         // data: this.xAxisOption.data.map((item,index)=>{
    //         //   return {
    //         //     value: this.KPIList[0].practicalValueList[index],
    //         //     name: item
    //         //   }
    //         // }),
    //         itemStyle: {
    //           color: function () {
    //             return (
    //               'rgb(' +
    //               [
    //                 Math.round(Math.random() * 270),
    //                 Math.round(Math.random() * 370),
    //                 Math.round(Math.random() * 400)
    //               ].join(',') +
    //               ')'
    //             );
    //           },
    //           borderRadius: 8
    //         },
    //         data: this.curShift.KpiValues.map((item, index) => {
    //           return {
    //             value: item.DataValue,
    //             name: item.DataTime,
    //             itemStyle: `color:${colorList[index]}`
    //           }
    //         }),
    //         // [
    //         //   { value: 1048, name: 'Search Engine' },
    //         //   { value: 735, name: 'Direct' },
    //         //   { value: 580, name: 'Email' },
    //         //   { value: 484, name: 'Union Ads' },
    //         //   { value: 300, name: 'Video Ads' }
    //         // ],
    //         emphasis: {
    //           itemStyle: {
    //             shadowBlur: 10,
    //             shadowOffsetX: 0,
    //             shadowColor: 'rgba(0, 0, 0, 0.5)'
    //           }
    //         }
    //       }
    //     ]
    //   }
    //   var that = this
    //   myChart.off('click');
    //   myChart.on('click', function (param) {
    //     console.log(param.name, that.configFlag, 'param');
    //     // if (that.configFlag == '是') {
    //     //   that.barName = param.name
    //     //   that.$refs.keyIndicatorsref.flagChange()
    //     //   that.$refs.keyIndicatorsref.showDialog = true;
    //     // }
    //     that.barName = param.name
    //     // that.$refs.keyIndicatorsref.flagChange()
    //     // that.$refs.keyIndicatorsref.showDialog = true;
    //     that.keyIndicatorslistnewShow = true
    //     that.showkeyIndicatorslistnew = true
    //   });
    //   myChart.setOption(option, true);
    //   //自动切换顺序
    //   // var cityIndex = 0;
    //   // this.pieTime = setInterval(function () {
    //   //   if (cityIndex < bingData.length) {
    //   //     myChart.dispatchAction({ type: 'downplay', seriesIndex: 0 });
    //   //     myChart.dispatchAction({ type: 'highlight', seriesIndex: 0, dataIndex: cityIndex });
    //   //     myChart.dispatchAction({ type: 'showTip', seriesIndex: 0, dataIndex: cityIndex });
    //   //     cityIndex++
    //   //   } else {
    //   //     cityIndex = 0;
    //   //   }
    //   // }, 4000);
    //   window.addEventListener("resize", () => {
    //     myChart.resize()
    //   }, false);
    // },
    query1() {
      this.chartMeter = document.getElementById(this.id1);
      var myChart = this.$echarts.init(this.chartMeter);

      var option
      option = {
        // title: {
        //   text: this.title,
        //   textStyle: { // 标题样式
        //     color: '#fff'
        //   }
        // },
        'series': [
          {
            'name': '个人指标',
            'type': 'gauge',
            'radius': '100%',
            'startAngle': '240',
            'endAngle': '-60',
            // 图表的刻度分隔段数
            'splitNumber': 5,
            // 图表的轴线相关
            'axisLine': {
              'show': true,
              'lineStyle': {
                'color': [
                  [
                    0.9,
                    new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                      offset: 0,
                      color: '#FFD900'
                    },
                    {
                      offset: 1,
                      color: '#FF8000'
                    }
                    ])
                  ],
                  [1, '#56606E']
                ],
                'width': 15
              }
            },
            // 图表的刻度及样式
            'axisTick': {
              'lineStyle': {
                'color': '#0F1318',
                'width': 2
              },
              'length': 15,
              'splitNumber': 1
            },
            // 图表的刻度标签(20、40、60等等)
            'axisLabel': {
              'distance': -8,
              'textStyle': {
                'color': '#fff'
              }
            },
            // 图表的分割线
            'splitLine': {
              'show': false
            },
            // 图表的指针
            'pointer': {
              'show': false
            },
            // 图表的数据详情
            'detail': {
              'formatter': function (params) {
                return '{title|' + '数值}' + '\n\n' + '{score|' + 100 + '}'
              },
              'offsetCenter': [0, 0],
              'rich': {
                'title': {
                  'fontSize': 24,
                  'color': '#fff',
                  'lineHeight': 30
                },
                'score': {
                  'fontSize': 30,
                  'color': '#fff'
                }
              }
            },
            // 图表的标题
            'title': {
              'offsetCenter': [0, '90%'],
              'color': '#fff',
              'fontSize': 20
            },
            'data': [{
              'name': '完成',
              'value': 31
            }]
          },
          {
            'name': '外层线',
            'type': 'gauge',
            'radius': '72%',
            'startAngle': '240',
            'endAngle': '-60',
            'center': ['50%', '50%'],
            'axisLine': {
              'lineStyle': {
                'width': 1,
                'color': [[1, '#56606E']]
              }
            },
            'splitLine': {
              'length': -6,
              'lineStyle': {
                'opacity': 0
              }
            },
            'axisLabel': {
              'show': false
            },
            'axisTick': {
              'splitNumber': 1,
              'lineStyle': {
                'opacity': 0
              }
            },
            'detail': {
              'show': false
            },
            'pointer': {
              'show': false
            }
          }
        ]
      }
      let tempVal = 0
      clearInterval(this.clockChartTimer)
      this.clockChartTimer = setInterval(() => {
        if (tempVal > this.myIvstrAbility) {
          clearInterval(this.clockChartTimer)
          // 最后转到最终数据的地方
          option.series[0].data[0].value = this.myIvstrAbility
          option.series[0].axisLine.lineStyle.color[0][0] = this.myIvstrAbility / 100
          // 使用刚指定的配置项和数据显示图表
          myChart.setOption(option)
          // 初始化渲染完成
          this.renderCompleted = true
          return
        }
        option.series[0].data[0].value = tempVal
        option.series[0].axisLine.lineStyle.color[0][0] = tempVal / 100
        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option)
        tempVal++
      }, 20)
      // 此处监听浏览器的resize，重新渲染图表
      let that = this
      window.addEventListener("resize", function () {
        clearTimeout(that.resizeTimer)
        that.resizeTimer = setTimeout(() => {
          myChart.resize()
        }, 500)
      })
    }
  },
  beforeDestroy() {
    clearInterval(this.pieTime);
  },
}
</script>
<style lang="scss" scoped>
#chartPie {
    width: 100%;
    height: 100%;
}
</style>
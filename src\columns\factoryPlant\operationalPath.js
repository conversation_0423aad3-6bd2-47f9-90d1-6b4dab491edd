export const operationalPath = [
    { text: '序号', value: 'Index', width: 50 },
    { text: '路线代码', value: 'RoutingCode', width: 100, sortable: true },
    { text: '路线名称', value: 'RoutingName', width: 100, sortable: true },
    { text: '路线版本', value: 'Version', width: 100, sortable: true },
    { text: '工艺类型', value: 'RoutingType', width: 100, sortable: true },
    { text: '生效自', value: 'EffectStart', width: 150 },
    { text: '生效至', value: 'EffectEnd', width: 150 },
    { text: '状态', value: 'Status', width: 100 },
    { text: '描述', value: 'Description', width: 200, sortable: true },
    { text: '备注', value: 'Notes', width: 150, sortable: true },
    { text: '操作', value: 'actions', align: 'center', width: 80, sortable: true }
];
export const operationalPathProduct = [
    { text: '序号', value: 'Index', width: 50 },
    { text: '成品料号', value: 'MaterialCode', width: 100 },
    { text: '产品名称', value: 'MaterialName', width: 150 },
    { text: '成品料号版本', value: 'MaterialVersion', width: 100 },
    { text: '备注', value: 'Remark', width: 150 },
    { text: '操作', align: 'center', value: 'actions', width: 80 }
];
export const operationalPathDetail = [
    { text: '', value: 'Index', width: 50 },
    { text: '工序编号', value: 'ProcCode', width: 100 },
    { text: '工序名称', value: 'ProcName', width: 100 },
    { text: '版本', value: 'Version', width: 100 },
    { text: '工序类型', value: 'ProcType', width: 100 },
    { text: '经营单位', value: 'Unit', width: 100 },
    { text: '生效自', value: 'EffectStart', width: 150 },
    { text: '生效至', value: 'EffectEnd', width: 150 },
    { text: '工时基准', value: 'Timb', width: 150 },
    { text: '运行机器', value: 'Runm', width: 150 },
    { text: '运行人工', value: 'Runl', width: 100 },
    { text: '设置人工', value: 'Setl', width: 100 },
    { text: '搬运小时数', value: 'Movd', width: 120 },
    { text: '排队小时数', value: 'Qued', width: 120 },
    { text: '操作', align: 'center', value: 'actions', width: 80 }
];

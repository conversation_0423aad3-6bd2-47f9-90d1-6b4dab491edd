<template>
    <v-dialog v-model="dialog" persistent max-width="600px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ this.$t('ANDON_BJJL.upgrade') }}
                <v-icon @click="closePopup">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="mt-2">
                <div class="stepbox">
                    <el-steps direction="vertical" :active="stepList.length">
                        <el-step v-for="(item, index) in stepList" :key="index" :title="item.AlarmStatus">
                            <template slot="icon">
                                <div>
                                    {{ stepList.length - index }}
                                </div>
                            </template>
                            <template slot="description">
                                <div class="stepContent">
                                    {{ item.AlarmContent }}
                                </div>
                                <div class="stepDate">
                                    {{ item.CreateDate }}
                                </div>
                            </template>
                        </el-step>
                    </el-steps>
                </div>

                <!-- <Tables
                    ref="tablePath"
                    :current-select-id="currentSelectId"
                    itemKey="EventLevel"
                    :dictionaryList="dictionaryList"
                    :showSelect="false"
                    table-height="calc(100vh - 260px)"
                    :loading="loading"
                    :headers="upgradeColumns"
                    :desserts="desserts"
                    :footer="false"
                    :page-options="pageData"
                    table-name="ANDON_BJJL"
                ></Tables> -->
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script>
import { AlarmHistoryGetList } from '@/api/andonManagement/alarmRecord.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        productLineList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            dialog: '',
            stepList: [],
            loading: false
        };
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal) {
                    console.log(this.operaObj);
                    this.getData();
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        async getData() {
            let params = {
                id: this.operaObj.ID
            };
            const res = await AlarmHistoryGetList(params);
            const { success, response } = res;
            if (success) {
                this.stepList = response;
            }
        },
        closePopup() {
            this.dialog = false;
        }
    }
};
</script>

<style lang="scss" >
.stepbox {
    display: flex;
    width: 100%;
    min-height: 100px;
    align-items: center;
    padding: 20px 0 20px 180px;
    .el-steps {
        width: 300px !important;
    }
}
::v-deep .v-sheet.v-card:not(.v-sheet--outlined) {
    box-shadow: none;
}
</style>
import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_SHIFT; // 配置服务url

//获取列表
export function MesMapUwbGetPageList(data) {
    return request({
        url: baseURL + '/shift/MesMapUwb/GetPageList',
        method: 'post',
        data
    });
}
//新增
export function MesMapUwbSaveForm(data) {
    return request({
        url: baseURL + '/shift/MesMapUwb/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function MesMapUwbDelete(data) {
    return request({
        url: baseURL + '/shift/MesMapUwb/Delete',
        method: 'post',
        data
    });
}

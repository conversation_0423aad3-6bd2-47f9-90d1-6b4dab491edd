//数据字典转码
// import { Encode } from '../util/dictionaryEncod';
// export function dictionaryEncod(item, type) {
//     const typeName = item.find(obj => obj.type === type);
//     return typeName ? typeName.name : null;
// }

//格式化
export function toFixed(val, len) {
    return val && val != 0 ? Number(val).toFixed(len) : '-';
}

//数值过千加逗号，并保留几位小数
export function numFormat(num, len) {
    var isNegative = num >= 0 ? false : true;
    if (isNegative) {
        //是否负数
        num = Math.abs(Number(num));
    }
    var c =
        num && num != 0
            ? Number(num)
                  .toFixed(len)
                  .replace(/(\d)(?=(\d{3})+\.)/g, '$1,')
            : '0';
    c = '$' + c;
    if (isNegative) {
        //是否负数
        c = '-' + c;
    }
    return c;
}

export default {
    data() {
        return {
            searchSegmentList: [],
            searchWorkstationList: []
        }
    },
    methods: {
        selectChange(item, data) {
            let key = item.key;
            let id = '';
            if (key != 'Line' && key != 'Segment') return false;
            switch (key) {
                case 'Line':
                    this.searchSegmentList = [];
                    this.searchWorkstationList = [];
                    if (item.value) {
                        id = item.value.ID;
                        this.searchSegmentList = this.segmentList.filter(item => item.ParentId == id);
                    }
                    break;
                case 'Segment':
                    this.searchWorkstationList = [];
                    if (item.value) {
                        id = item.value.ID;
                        this.searchWorkstationList = this.workstationList.filter(item => item.ParentId == id);
                    }
                    break;
            }
            for (const key in data) {
                this.searchInputs.map(item => {
                    if (item.key == key) {
                        console.log(item);
                        item.value = data[key];
                    }
                });
            }
        },
    },
}
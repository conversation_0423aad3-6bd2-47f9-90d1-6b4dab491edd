<template>
    <div class="instance-property">
        <div class="title">
            <span>{{ currentObj.FunctionName }}</span>
            <span>
                <v-icon @click="close()">mdi-close</v-icon>
            </span>
        </div>
        <v-divider style="margin-top:10px"></v-divider>
        <div class="content" style="margin-top:10px">
            <v-row class="tool-row">
                <v-col :cols="12" :lg="3">
                    <a-input-search v-model="keywords" enter-button placeholder="Quick Search" @search="onSearch" />
                </v-col>
                <v-col :cols="12" :lg="9" class="pl-0">
                    <a-button @click="getdata">
                        <v-icon left>mdi-cached</v-icon>
                        Refresh</a-button>
                    <a-button :disabled="tableList.length == 0" @click="save()" style="margin-left:10px" type="primary">
                        Save</a-button>
                    <!-- <a-button style="margin-left:10px" type="primary">
                        Reset Instance</a-button> -->
                </v-col>
            </v-row>
            <div class="table" style="margin-top:10px">
                <vxe-table class="mytable-scrollbar" height="600px" :loading="loading" size="mini" border resizable
                    ref="table" :data="tableList">
                    <vxe-column v-for="(  column, index  ) in   columns  " :key="index" :field="column.field"
                        :title="column.title" :width="column.width">
                        <template #default="{ row }">
                            <vxe-input v-if="column.field == 'OpcTagId'" size="mini"
                                v-model="row[column.field]"></vxe-input>
                            <span v-else>{{ row[column.field] }}</span>
                        </template>
                    </vxe-column>
                </vxe-table>
            </div>
        </div>
    </div>
</template>

<script>
import { getNewInstanceProperty, updateFunctionInstanceProperty } from '../service'
export default {
    props: {
        currentObj: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            keywords: '',
            loading: false,
            tableList: []
        }
    },
    computed: {
        columns() {
            return [
                { field: 'Name', title: 'Name' },
                { field: 'OpcTagName', title: 'Tag Name' },
            ]
        }
    },
    created() {
        this.getdata()
    },
    methods: {
        async save() {
            try {
                let resp = await updateFunctionInstanceProperty(this.tableList)
                this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });
                this.$emit('updateExpandData', this.currentObj)
            } catch {
                this.getdata()
            }
        },
        async getdata() {
            let resp = await getNewInstanceProperty({ opcFunctionInstanceId: this.currentObj.ID })
            this.tableList = resp.response
        },
        onSearch() {
          console.log(this.currentObj)
        },
        close() {
            this.$emit('closeDrawer')
        }
    }
}
</script>

<style lang="scss" scoped>
.title {
    display: flex;
    justify-content: space-between;
    font-size: 18px;
    font-weight: 600;
}

.instance-property {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.content {
    flex: 1;
}
</style>

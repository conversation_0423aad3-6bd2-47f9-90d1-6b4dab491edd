<template>
    <v-dialog v-model="updateDialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ operaObj.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ') }}
                <v-icon @click="updateDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" class="mt-8">
                    <v-row>
                        <!-- 产线 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.Productline" :rules="rules.Productline" :label="$t('$vuetify.dataTable.DFM_SCKPIMBSD.Productline')" dense outlined></v-text-field>
                        </v-col>
                        <!-- 类型 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.Kpitype"
                                :rules="rules.Kpitype"
                                :items="KpitypeList"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="$t('$vuetify.dataTable.DFM_SCKPIMBSD.Kpitype')"
                                persistent-hint
                                dense
                                outlined
                            ></v-select>
                        </v-col>
                        <!-- 最大值 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.Maxvalue" :rules="rules.Maxvalue" :label="$t('$vuetify.dataTable.DFM_SCKPIMBSD.Maxvalue')" dense outlined></v-text-field>
                        </v-col>
                        <!-- 最小值 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.Minvalue" :rules="rules.Minvalue" :label="$t('$vuetify.dataTable.DFM_SCKPIMBSD.Minvalue')" dense outlined></v-text-field>
                        </v-col>
                        <!-- 是否启用 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.Enable"
                                :rules="rules.Enable"
                                :items="EnableList"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="$t('$vuetify.dataTable.DFM_SCKPIMBSD.Enable')"
                                persistent-hint
                                dense
                                outlined
                            ></v-select>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" class="request-loading" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn color="normal" @click="updateDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { KpiTargetSaveForm } from '@/api/factoryPlant/KPIGoaSetting.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        KpitypeList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            checkbox: true,
            valid: true,
            updateDialog: false,
            form: {
                ID: '',
                Productline: '',
                Kpitype: '',
                Maxvalue: '',
                Minvalue: '',
                Enable: '1'
            },
            rules: {
                Productline: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                Maxvalue: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                Minvalue: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                Kpitype: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            },
            parentCodeList: [],
            EnableList: [
                {ItemValue: 0, ItemName: '否'},
                {ItemValue: 1, ItemName: '是'}
            ]
        };
    },
    watch: {
        updateDialog: {
            handler(curVal) {
                if(curVal) {
                    for (const key in this.form) {
                        if (Object.hasOwnProperty.call(this.form, key)) {
                            this.form[key] = this.operaObj[key];
                        }
                    }
                    if((typeof this.form.Enable) != 'number' ) this.form.Enable = 1
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                const res = await KpiTargetSaveForm({...this.form });
                const { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    this.updateDialog = !this.checkbox;
                    this.$emit('handlePopup', 'refresh'); 
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>

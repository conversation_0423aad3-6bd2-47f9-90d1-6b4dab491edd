<template>
    <div class="vxe-table-wrap">
        <vxe-toolbar ref="xToolbar" :custom="isShowCustom">
            <template #buttons>
                <vxe-button size="mini" v-if="isShowSearch" @click="showSearch()">
                    {{ $t('GLOBAL._SSL') }}
                </vxe-button>
                <vxe-button size="mini" content="新增" @click="addData()"></vxe-button>
                <vxe-button size="mini" content="删除" @click="deleteData()"></vxe-button>
                <vxe-button size="mini" content="更新" @click="updateData()"></vxe-button>
                <slot name="btns">
                </slot>
            </template>
        </vxe-toolbar>
        <vxe-table auto-resize @checkbox-change="checkChange" :show-footer="isShowFooter" :footer-method="footerMethod"
            :loading="loading" ref="vxeTable" :id="tableId" class="mytable-scrollbar" :row-config="{ isHover: isHover }"
            :custom-config="{ storage: true }" :column-config="{ useKey: true, resizable: true }" v-bind="$attrs"
            :height="height" v-on="$listeners" border size="mini" :data="tableList">
            <vxe-column v-for="(column, index) in columns" show-overflow="tooltip" :type="column.type"
                :width="column.width" :fixed="column.fixed" :key="index" :field="column.field" :title="column.title">
                <template #default="{ row }">
                    <span v-if="column.field == 'action'">
                        <vxe-button v-for="btn in btnList" v-has="btn.authCode" :key="btn.code" type="text"
                            @click="tableClick(btn.code, row)" :status="btn.type" :content="btn.text"></vxe-button>
                        <!-- <vxe-button type="text" @click="tableClick('delete', row)" status="danger"
                            content="删除"></vxe-button> -->
                    </span>
                    <span v-else>
                        <slot name="cell" :item="{ value: row[column.field], field: column.field }">
                            {{ row[column.field] }}
                        </slot>
                    </span>
                </template>
            </vxe-column>
        </vxe-table>
        <vxe-pager border size="medium" :page-sizes="pageOptions.pageSizeitems" :current-page="pageOptions.pageIndex"
            :page-size="pageOptions.pageSize" :total="pageOptions.total"
            :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
            @page-change="handlePageChange">
        </vxe-pager>
    </div>
</template>

<script>
import Sortable from 'sortablejs'
import VXETable from 'vxe-table'
export default {
    props: {
        isShowSearch: {
            type: Boolean,
            default: false
        },
        isShowFooter: {
            type: Boolean,
            default: false
        },
        loading: {
            type: Boolean,
            default: false
        },
        tableId: {
            type: String,   // isShowCustom设置为true时， tableId为必传项 否则表格列无法存储本地
            default: ''
        },
        isShowCustom: {
            type: Boolean,
            default: false
        },
        height: {
            type: String,
            default: '350px'
        },
        tableList: {
            type: Array,
            default: () => []
        },
        columns: {
            type: Array,
            default: () => []
        },
        isHover: {
            type: Boolean,
            default: true
        },
        pageOptions: {
            type: Object,
            default: () => {
                return {
                    total: 200,
                    pageIndex: 1,
                    pageSize: 20,
                    pageSizeitems: [20, 50, 100, 500]
                }
            }
        },
        btnList: {
            type: Array,
            default: function () {
                return [
                    {
                        text: this.$t('GLOBAL._BJ'),
                        code: 'edit',
                        type: 'primary',
                        icon: ''
                    },
                    {
                        text: this.$t('GLOBAL._SC'),
                        code: 'delete',
                        type: 'danger',
                        icon: ''
                    }
                ];
            }
        },
    },
    data() {
        return {
            selecteds: []
        }
    },
    watch: {
        tableList: {
            handler(nv, ov) {
                this.$refs.vxeTable.setAllCheckboxRow(false)
                this.selecteds = []
            },
            deep: true
        }
    },
    created() {
        this.$nextTick(() => {
            // 手动将表格和工具栏进行关联
            this.$refs.vxeTable.connect(this.$refs.xToolbar)
        })
    },
    mounted() {
        this.columnDrop()
    },
    beforeDestroy() {
        if (this.sortable) {
            this.sortable.destroy()
        }
    },
    methods: {
        getCheckboxAllData() {
            return this.$refs.vxeTable.getCheckboxRecords(true)
        },
        showSearch() {
            this.$emit('showSearch')
        },
        // 表格选中事件
        checkChange({ records }) {
            this.selecteds = records
        },
        addData() {
            this.$emit('add')
        },
        deleteData() {
            this.$emit('delete', this.selecteds)
        },
        sumNum(list, field) {
            let count = 0
            list.forEach(item => {
                count += Number(item[field])
            })
            return count
        },
        footerMethod({ columns, data }) {
            const footerData = [
                columns.map((column, _columnIndex) => {
                    if (_columnIndex === 0) {
                        return '合计'
                    }
                    if (['age', 'num'].includes(column.property)) {
                        return this.sumNum(data, column.property)
                    }
                    return '-'
                })
            ]
            return footerData
        },
        handlePageChange({ currentPage, pageSize }) {
            this.$emit('handleChangePage', currentPage, pageSize)
        },
        updateData() {
            this.$emit('update')
        },
        tableClick(type, item) {
            this.$emit('tableClick', type, item)
        },
        columnDrop() {
            this.$nextTick(() => {
                const $table = this.$refs.vxeTable
                this.sortable = Sortable.create($table.$el.querySelector('.body--wrapper>.vxe-table--header .vxe-header--row'), {
                    handle: '.vxe-header--column',
                    onEnd: ({ item, newIndex, oldIndex }) => {
                        const { fullColumn, tableColumn } = $table.getTableColumn()
                        const targetThElem = item
                        const wrapperElem = targetThElem.parentNode
                        const newColumn = fullColumn[newIndex]
                        if (newColumn.fixed || fullColumn[oldIndex].fixed) {
                            const oldThElem = wrapperElem.children[oldIndex]
                            // 错误的移动
                            if (newIndex > oldIndex) {
                                wrapperElem.insertBefore(targetThElem, oldThElem)
                            } else {
                                wrapperElem.insertBefore(targetThElem, oldThElem ? oldThElem.nextElementSibling : oldThElem)
                            }
                            VXETable.modal.message({ content: '固定列不允许拖动，即将还原操作！', status: 'error' })
                            return
                        }
                        // 获取列索引 columnIndex > fullColumn
                        const oldColumnIndex = $table.getColumnIndex(tableColumn[oldIndex])
                        const newColumnIndex = $table.getColumnIndex(tableColumn[newIndex])
                        // 移动到目标列
                        const currRow = fullColumn.splice(oldColumnIndex, 1)[0]
                        fullColumn.splice(newColumnIndex, 0, currRow)
                        $table.loadColumn(fullColumn)
                    }
                })
            })
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep .vxe-footer--row {
    font-weight: 600;
}

/*滚动条整体部分*/
.mytable-scrollbar ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

/*滚动条的轨道*/
.mytable-scrollbar ::-webkit-scrollbar-track {
    background-color: #FFFFFF;
}

/*滚动条里面的小方块，能向上向下移动*/
.mytable-scrollbar ::-webkit-scrollbar-thumb {
    background-color: #bfbfbf;
    border-radius: 5px;
    border: 1px solid #F1F1F1;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
}

.mytable-scrollbar ::-webkit-scrollbar-thumb:hover {
    background-color: #A8A8A8;
}

.mytable-scrollbar ::-webkit-scrollbar-thumb:active {
    background-color: #787878;
}

/*边角，即两个滚动条的交汇处*/
.mytable-scrollbar ::-webkit-scrollbar-corner {
    background-color: #FFFFFF;
}

::v-deep .vxe-header--row .vxe-header--column.col--fixed {
    cursor: no-drop;
}
</style>
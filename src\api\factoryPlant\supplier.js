import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url
//获取全部数据
export function getSupplierList(data) {
    return request({
        url: baseURL + '/api/Supplier/GetPageList',
        method: 'post',
        data
    });
}
// 新增和编辑
export function getSaveForm(data) {
    return request({
        url: baseURL + '/api/Supplier/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function getDelete(data) {
    return request({
        url: baseURL + `/api/Supplier/Delete`,
        method: 'post',
        data
    });
}

// dictionary: true, isEditCell: true
export const warehouseModelingColum = [
    {
        text: '序号',
        value: 'Index',
        width: '60px'
    },
    { text: '仓库编号', value: 'WarehouseCode', width: '140px' },
    { text: '仓库名称', value: 'WarehouseName', width: '160px' },
    { text: '仓库类型', value: 'WarehouseType', width: '120px', dictionary: true },
    { text: '产品线', value: 'ProductLine', width: '140px' },
    { text: '简称', value: 'Abbreviation', width: '140px' },
    { text: '楼层', value: 'Floor', width: '140px', dictionary: true },
    { text: '仓库描述', value: 'Description', width: '200px' },
    { text: 'SAP库位', value: 'SapPositionName', width: '180px' },
    { text: 'SAP库位编码', value: 'SapPosition', width: '160px' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '140px'
    }
];
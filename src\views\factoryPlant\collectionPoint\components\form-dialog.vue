<template>
  <el-dialog :title="dialogForm.ID ? '编辑' : '新增'" :visible.sync="dialogVisible" width="600px"
    :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
    @close="dialogVisible = false">
    <el-form :rules="rules" ref="dialogForm" :model="dialogForm" label-width="110px">
      <el-form-item label="点位名称" prop="Name">
        <el-input v-model="dialogForm.Name" placeholder="" />
      </el-form-item>
      <el-form-item label="点位地址" prop="Tag">
        <el-input v-model="dialogForm.Tag" placeholder="" />
      </el-form-item>
      <el-form-item label="采集类型" prop="Type">
        <el-select style="width: 100%" v-model="dialogForm.Type" placeholder="请选择">
          <el-option v-for="(item, index) in typeOptions" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="通讯站点" prop="ServerId">
        <el-select style="width: 100%" v-model="dialogForm.ServerId" placeholder="请选择">
          <el-option v-for="(item, index) in this.$parent.stationOptions" :key="index" :label="item.Name" :value="item.ID">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否存储" prop="IsSave">
        <el-select style="width: 100%" v-model="dialogForm.IsSave" placeholder="请选择">
          <el-option v-for="(item, index) in saveOptions" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="存储周期(秒)">
        <el-input v-model="dialogForm.SaveCyc" :maxlength="20" placeholder="" />
      </el-form-item> -->
      <el-form-item label="计算公式">
        <el-input v-model="dialogForm.Formula" placeholder="" />
      </el-form-item>
      <el-form-item label="点位描述">
        <el-input type="textarea" v-model="dialogForm.Describe" placeholder="" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogVisible = false">取 消</el-button>
      <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small"
        @click="submit()">确定
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { influxOpcTagSaveForm } from "@/api/factoryPlant/collectionPoint.js";
export default {
  name: 'Dialog',
  data() {
    return {
      dialogForm: {},
      dialogVisible: false,
      formLoading: false,
      typeOptions: [{
        label: '周期性',
        value: 0
      }, {
        label: '变化时',
        value: 1
      }],
      saveOptions: [{
        label: '否',
        value: false
      }, {
        label: '是',
        value: true
      }],
      rules: {
        Tag: [
          { required: true, message: '请输入点位地址', trigger: 'blur' }
        ],
        Name: [
          { required: true, message: '请输入点位名称', trigger: 'blur' }
        ],
        Type: [
          { required: true, message: '请选择采集类型', trigger: 'change' }
        ],
        ServerId: [
          { required: true, message: '请选择通讯站点', trigger: 'change' }
        ],
        IsSave:[
          { required: true, message: '请选择是否存储', trigger: 'change' }
        ],
      }
    }
  },
  mounted() {
  },
  methods: {
    submit() {
      this.$refs.dialogForm.validate(async (valid) => {
        if (valid) {
          const { msg } = await influxOpcTagSaveForm({
            EquipmentId: this.$parent.currentTree.id,
            ...this.dialogForm
          })
          this.$message.success(msg)
          this.$emit('SaveForm')
          this.dialogVisible = false
        }
      });
    },
    show(data) {
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.dialogForm = {
          ...data
        }
        this.$refs.dialogForm.resetFields()
      })
    }
  }
}
</script>

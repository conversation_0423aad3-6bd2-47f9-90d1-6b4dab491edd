export const puretoneTestColum = [
    // {
    //     text: '序号',
    //     value: 'Index',
    //     sortable: true
    // },
    {
        text: '产品线',
        value: 'FullLineName',
        width: 140,
        sortable: true
    },
    {
        text: '工段',
        value: 'CompanyName',
        width: 140,
        sortable: true
    },
    {
        text: '工单号',
        width: 120,
        value: 'WoCode',
        sortable: true
    },
    {
        text: '产品料号',
        width: 120,
        value: 'MaterialCode',
        sortable: true
    },
    {
        text: '产品描述',
        width: 180,
        value: 'MaterialDescription',
        sortable: true
    },
    {
        text: '测试批号',
        width: 120,
        value: 'BatchNo',
        sortable: true
    },
    {
        text: '批次数量(PCS)',
        width: 140,
        value: 'BatchQuantity',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: '不良数量(PCS)',
        width: 140,
        value: 'BatchBadQuantity',
        sortable: true
    },
    {
        text: '类型',
        width: 80,
        value: 'TestGrade',
        sortable: true
    },
    {
        text: '状态',
        width: 100,
        value: 'Status',
        sortable: true
    },
    {
        text: '接收时间',
        width: 160,
        value: 'CreateDate',
        sortable: true
    },
    { text: '操作', align: 'center', width: 180, value: 'actions' }
];
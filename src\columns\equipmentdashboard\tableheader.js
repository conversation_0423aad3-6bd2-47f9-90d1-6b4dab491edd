export const jdrwheader = [
    { text: '位置', value: 'Loaction', prop: "Area" },
    { text: '设备名称', value: 'EquipmentName', prop: "DeviceName" },
    { text: '点检项目', value: 'McProject', prop: "Wo" },
    { text: '状态', value: 'status', prop: "Status" },
    { text: '责任人', value: 'PersonName', prop: "CheckBy" },
];

export const byrwheader = [
    { text: '位置', value: 'Loaction', prop: "Area" },
    { text: '设备名称', value: 'EquipmentName', prop: "DeviceName" },
    { text: '保养项目', value: 'McProject', prop: "Wo" },
    { text: '计划保养时间', value: 'jhbysj', prop: "PlanMaintainDate" },
    { text: '状态', value: 'status', prop: "Status" },
    { text: '责任人', value: 'PersonName', prop: "MaintainBy" },
];

export const dxrwheader = [
    { text: '产线', value: 'Line', prop: "Line" },
    { text: '计划大修时间', value: 'jhdxsj', prop: "PlanStartDate" },
    { text: '大修内容', value: 'dxnr', prop: "OverhaulPlanContent"  },
    { text: '任务内容', value: 'rwnr', prop: "OverhaulContent" },
    { text: '状态', value: 'status', prop: "Status" },
    { text: '责任人', value: 'PersonName', prop: "TaskBy" },
];


export const wxrwheader = [
    { text: '维修工单号', value: 'RepairWo', prop: "RepairWo" },
    { text: '位置', value: 'Loaction', prop: "LineCode" },
    { text: '设备名称', value: 'EquipmentName', prop: "DeviceName" },
    { text: '报修等级', value: 'McProject', prop: "Urgency" },
    { text: '故障现象', value: 'jhbysj', prop: "Description" },
    // { text: '故障描述', value: 'status', prop: "Remark" },
    { text: '已报修(分钟)', value: 'PersonName', prop: "ReportDate" },
    { text: '状态', value: 'status', prop: "Status" },
    { text: '接单人', value: 'PersonName', prop: "ReceiveBy" },
];


export const jjrwheader = [
    { text: 'ABC分类', value: 'Line', prop: "Type" },
    { text: '检定方法', value: 'jhdxsj', prop: "VerifyMethod" },
    { text: '类别', value: 'dxnr', prop: "Category" },
    { text: '最终期限', value: 'rwnr', prop: "ExpirationDateEnd" },
    { text: '状态', value: 'status', prop: "Status" },
    { text: '责任人', value: 'PersonName', prop: "VerifyByName" },
];


export const jqbyrwheader = [
    { text: '设备编码', value: 'jhdxsj', prop: "DeviceCode" },
    { text: '存放位置', value: 'Area', prop: "Area" },
    { text: '计划保养日期', value: 'PlanMaintainDate', prop: "PlanMaintainDate" },
    { text: '实际开始时间', value: 'ActualStartTime', prop: "StartMaintainDate" },
    { text: '实际结束时间', value: 'ActualFinishTime', prop: "FinishMaintainDate" },
    { text: '状态', value: 'Status', prop: "Status" },
    { text: '责任人', value: 'PersonName', prop: "MaintainBy" },
];
export const cqsbheader = [
    { text: '设备编码', value: 'jhdxsj', prop: "DeviceCode" },
    { text: '设备名称', value: 'dxnr', prop: "DeviceName" },
    { text: '所在区域', value: 'rwnr', prop: "Area" },
    { text: '计划保养日期', value: 'status', prop: "PlanMaintainDate" },
    { text: '责任人', value: 'PersonName', prop: "MaintainBy" },
];

export const wxfxheader = [
    { text: '设备编码', value: 'DeviceCode' },
    { text: '设备名称', value: 'DeviceName' },
    { text: '故障代码', value: 'Urgency' },
    { text: '录入时间', value: 'ReportDate' },
    { text: '责任人', value: 'ReceiveBy' },
];

export const wxscheader = [
    { text: '设备编码', value: 'DeviceCode' },
    { text: '设备名称', value: 'DeviceName' },
    { text: '开始时间', value: 'StartDate' },
    { text: '结束时间', value: 'FinishDate' },
    { text: '维修时长', value: 'RepairDuration' },
    { text: '责任人', value: 'ReceiveBy' },
];
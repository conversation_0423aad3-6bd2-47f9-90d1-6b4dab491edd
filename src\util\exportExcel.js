/**
 * 
 * @param {*} data  // 导出数据
 * @param {{key: string, name: string}} columns //key: 每列对应key值, name: 每列表头名字
 */
function exportExcel(data, columns) {
    //列标题
    let headRowStr = ''
    columns.forEach(item => {
        headRowStr += `<td>${item.name}</td>`
    })
    let head = `<tr>${headRowStr}</tr>`;

    let tbody = "";//内容
    for (let item in data) {
        let rowStr = ''
        columns.forEach(itm => {
            if (data[item][itm.key]) {
                rowStr += `<td>${data[item][itm.key] + '\t'}</td>`
            } else {
                rowStr += `<td></td>`
            }
        });
        tbody += `<tr>${rowStr}</tr>`
    }
    let str = head + tbody;//头部跟身体内容连接
    //Worksheet名
    let worksheet = 'Sheet1'
    let uri = 'data:application/vnd.ms-excel;base64,';

    //下载的表格模板数据
    let template = `<html xmlns:o="urn:schemas-microsoft-com:office:office" 
     xmlns:x="urn:schemas-microsoft-com:office:excel" 
     xmlns="http://www.w3.org/TR/REC-html40">
     <head><meta charset="utf-8"><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet>
       <x:Name>${worksheet}</x:Name>
       <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet>
       </x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->
       </head><body><table>${str}</table></body></html>`;
    //下载模板
    // window.location.href = uri + base64(template)
    dataURIToBlob(uri + base64(template), callback)
}

//输出base64编码
function base64(s) {
    return window.btoa(unescape(encodeURIComponent(s)))
}

function dataURIToBlob(dataURI, callback) {
    var binStr = atob(dataURI.split(",")[1]),
        len = binStr.length,
        arr = new Uint8Array(len);

    for (var i = 0; i < len; i++) {
        arr[i] = binStr.charCodeAt(i);
    }

    callback(new Blob([arr]));
}

var callback = function (blob) {
    var a = document.createElement("a");
    a.download = "数据" + ".xls";
    a.innerHTML = "download";
    a.href = URL.createObjectURL(blob);
    a.click();
};

export { exportExcel }
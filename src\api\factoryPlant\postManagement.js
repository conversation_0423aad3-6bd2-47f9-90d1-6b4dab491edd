import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url

//获取岗位列表数据
export function GetPageList(data) {
    return request({
        url: baseURL + '/api/Post/GetPageList',
        method: 'post',
        data
    });
}
//获取岗位列表数据
export function GetLists(data) {
    return request({
        url: baseURL + '/api/Post/GetList',
        method: 'post',
        data
    });
}
//新增岗位数据
export function PostSaveForm(data) {
    return request({
        url: baseURL + '/api/Post/SaveForm',
        method: 'post',
        data
    });
}
//删除岗位数据
export function PostDelete(data) {
    return request({
        url: baseURL + '/api/Post/Delete',
        method: 'post',
        data
    });
}
// 数据导入
export function doImport(data) {
    return request({
        url: baseURL + '/api/Post/ImportData',
        method: 'post',
        data
    });
}

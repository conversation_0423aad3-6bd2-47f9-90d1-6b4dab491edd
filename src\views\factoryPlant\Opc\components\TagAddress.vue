<template>
    <div class="tag-address">
        <div class="search-row">
            <v-row>
                <v-col :cols="12" :lg="2">
                    <a-input v-model="server" :placeholder="$t('DFM_OPC._Server')" />
                </v-col>
                <v-col :cols="12" :lg="2">
                    <a-input v-model="group" :placeholder="$t('DFM_OPC._Group')" />
                </v-col>
                <v-col :cols="12" :lg="2">
                    <a-input v-model="tagName" :placeholder="$t('DFM_OPC._TagName')" />
                </v-col>
                <v-col :cols="12" :lg="3">
                    <v-btn color="primary" @click="getdata">
                        <v-icon left>mdi-cached</v-icon>
                        {{ $t('DFM_OPC._Refresh') }}
                    </v-btn>
                </v-col>
            </v-row>
        </div>
        <div class="tool-row">
            <v-row>
                <v-col :cols="12" :lg="3">
                    <a-input-search v-model="keywords" enter-button :placeholder="$t('DFM_OPC._QuickSearch')" @search="getdata" />
                </v-col>
                <v-col :cols="12" :lg="9" class="pl-0">
                    <v-btn class="ml-3" color="primary" @click="openPopup('Server')">
                        <v-icon left>mdi-plus</v-icon>
                        {{ $t('DFM_OPC._NewServer') }}
                    </v-btn>
                    <v-btn class="ml-3" color="primary" @click="openPopup('Group')">
                        <v-icon left>mdi-plus</v-icon>
                        {{ $t('DFM_OPC._NewGroup') }}
                    </v-btn>
                    <v-btn class="ml-3" color="primary" @click="openPopup('Tag')">
                        <v-icon left>mdi-plus</v-icon>
                        {{ $t('DFM_OPC._NewTag') }}
                    </v-btn>
                    <!-- <v-btn class="ml-3">
                        <v-icon left>mdi-download</v-icon>
                        {{ $t('DFM_OPC._Export') }}</v-btn>
                    <v-btn class="ml-3" color="primary">
                        <v-icon left>mdi-upload</v-icon>
                        {{ $t('DFM_OPC._Import') }}</v-btn> -->
                </v-col>
            </v-row>
        </div>
        <div class="table-box" style="height: calc(100vh - 200px)">
            <vxe-table
                height="auto"
                :row-config="{ keyField: 'Id' }"
                :loading="loading"
                size="mini"
                border
                resizable
                ref="xTree"
                :tree-config="{
                    transform: true,
                    reserve: true,
                    expandAll: true,
                    rowField: 'Id',
                    parentField: 'ParentId',
                    iconOpen: 'vxe-icon-square-minus-fill',
                    iconClose: 'vxe-icon-square-plus-fill'
                }"
                :data="treeData"
            >
                <vxe-column v-for="(column, index) in tagAddressColumns" :tree-node="column.field == 'Name'" :key="index" :width="column.width" :field="column.field" :title="column.title">
                    <template #default="{ row }">
                        <span v-if="column.field == 'Action'">
                            <v-icon @click="handleDel(row)" style="cursor: pointer" color="#3dcd58" size="18">mdi-delete</v-icon>
                        </span>
                        <span @click="handleEdit(row)" style="color: #3dcd58; cursor: pointer" v-else-if="column.field == 'Name'">{{ row[column.field] }}</span>
                        <span v-else>{{ row[column.field] }}</span>
                    </template>
                </vxe-column>
            </vxe-table>
        </div>
        <a-modal :visible="isShowPopup" :title="popupTitle" @cancel="handleCancel" @ok="handleOk">
            <tagAddressPopup v-if="isShowPopup" ref="tagAddressPopup" :currentItem="currentItem" :type="type"></tagAddressPopup>
        </a-modal>
    </div>
</template>

<script>
import { tagAddressColumns } from '@/columns/factoryPlant/Opc.js';
import tagAddressPopup from './tagAddressPopup.vue';
import { addServer, addGroup, addTag, getTagAddressTree, delServer, delGroup, delTag } from '../service';
export default {
    components: {
        tagAddressPopup
    },
    data() {
        return {
            isEdit: false,
            currentItem: {},
            type: 'server',
            isShowPopup: false,
            tagAddressColumns,
            server: '',
            group: '',
            tagName: '',
            keywords: '',
            treeData: [],
            loading: false
        };
    },
    computed: {
        popupTitle() {
            const type = this.type;
            let text = '';
            if (this.isEdit) {
                text = type == 'Server' ? 'Edit Server' : type == 'Group' ? 'Edit Group' : type == 'Tag' ? 'Edit Tag' : '';
            }
            if (!this.isEdit) {
                text = type == 'Server' ? 'New Server' : type == 'Group' ? 'New Group' : type == 'Tag' ? 'New Tag' : '';
            }
            return text;
        }
    },
    created() {
        this.getdata();
    },
    methods: {
        handleEdit(data) {
            this.isEdit = true;
            this.type = data.NodeType;
            this.currentItem = data;
            this.isShowPopup = true;
        },
        handleDel(data) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: '确认要删除此项吗？',
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let type = data.NodeType;
                    let res;
                    type == 'Server' && (res = await delServer([data.Id]));
                    type == 'Group' && (res = await delGroup([data.Id]));
                    type == 'Tag' && (res = await delTag([data.Id]));
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.getdata();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        handleCancel() {
            this.isShowPopup = false;
        },
        async handleOk() {
            let params = {};
            try {
                switch (this.type) {
                    case 'Server':
                        params = this.$refs.tagAddressPopup.serverForm;
                        await addServer(params);
                        break;
                    case 'Group':
                        params = this.$refs.tagAddressPopup.groupForm;
                        await addGroup(params);
                        break;
                    case 'Tag':
                        params = this.$refs.tagAddressPopup.tagForm;
                        await addTag(params);
                        break;
                }
                this.isShowPopup = false;
                this.$nextTick(() => {
                    this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });
                    this.getdata();
                });
            } catch {
                console.log('error');
            }
        },
        openPopup(val) {
            this.currentItem = {};
            this.isEdit = false;
            this.isShowPopup = true;
            this.type = val;
        },
        async getdata() {
            this.loading = true;
            try {
                let resp = await getTagAddressTree({
                    Name: this.keywords,
                    WhereServerDesc: this.server,
                    WhereGroupName: this.group,
                    WhereTagName: this.tagName
                });
                this.treeData = resp.response;
                this.loading = false;
            } catch {
                this.loading = false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.tag-address {
    border: 1px solid #eee;
    border-radius: 5px;

    .search-row {
        padding: 5px 3px;
        background: #f5f5f5;
        border-bottom: 1px solid #eee;
    }

    .tool-row {
        padding: 5px 3px;
        background: #f5f5f5;
    }
}
</style>
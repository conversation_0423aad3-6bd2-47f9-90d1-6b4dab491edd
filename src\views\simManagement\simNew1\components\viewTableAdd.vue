<template>
  <div>
    <v-card>
      <v-card-title
        class="headline primary lighten-2"
        primary-title
      >
        KPI模型选择
      </v-card-title>
      <v-card-text style="padding: 0;">
        <vxe-table
          :data="tableList"
          :show-footer="false"
          :loading="false"
          ref="vxeTable"
          class="mytable-scrollbar"
          :row-config="{ isHover: true,height:50 }"
          :column-config="{ resizable: true }"
          v-bind="$attrs"
          height="auto"
          v-on="$listeners"
          border
          size="mini"
          :radio-config="{ highlight: true }"
        >
          <vxe-column
            type="radio"
            width="40"
          ></vxe-column>
          <vxe-column
            show-overflow="title"
            field="KpiName"
            title="KPI模型名称"
          ></vxe-column>
          <vxe-column
            show-overflow="title"
            field="KpiCode"
            title="KPI模型Code"
          ></vxe-column>
          <vxe-column
            show-overflow="title"
            field="Unit"
            title="单位"
          ></vxe-column>
          <vxe-column
            show-overflow="title"
            field="SqlText"
            title="SQL"
          ></vxe-column>
          <vxe-column
            show-overflow="title"
            field="Description"
            title="描述"
          ></vxe-column>
          <vxe-column
            show-overflow="title"
            field="isFlag"
            title="开启弹窗"
            min-width="100"
          >
            <template v-slot="{ row }">
              <v-radio-group
                v-model="row.isFlag"
                row
              >
                <v-radio
                  v-for="option in options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                  @change="radioChange(option)"
                ></v-radio>
              </v-radio-group>
            </template>
          </vxe-column>
          <vxe-column
            show-overflow="title"
            field="Description"
            title="弹窗数据源"
            min-width="100"
          >
            <template v-slot="{ row }">
              <v-radio-group
                v-model="row.configList"
                row
                style="margin-top:-5px;"
              >
                <!-- <v-radio
                  v-for="item in dutyListData"
                  :key="item.TagName"
                  :label="item.TagName"
                  :value="item.TagCode"
                  @change="radioChange(item)"
                ></v-radio> -->
                <v-select
                  :items="dutyListData"
                  label="请选择"
                  v-model="row.configList"
                ></v-select>
              </v-radio-group>
            </template>
          </vxe-column>
        </vxe-table>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-actions style="justify-content: flex-end;">
        <v-btn
          color="primary"
          @click="submitForm"
        >{{ $t('GLOBAL._QD') }}</v-btn>
        <v-btn @click="closePopup">{{ $t('GLOBAL._QX') }}</v-btn>
      </v-card-actions>
    </v-card>
  </div>
</template>
<script>
// import { GetSafePageList } from '@/views/simManagement/sim1/service.js';
import { GetKpiList, SaveConfig, GetChartStructure } from '@/views/simManagement/components/chartsConfig/service.js';
import { QueryResultBySql } from '@/views/kpi/modelManagement/service.js';
// import { GetChartStructure, delChartConfig } from '@/views/simManagement/components/chartsConfig/service.js';
import { getDataSourceList, getEditEntityByCode } from '@/api/simConfig/simconfignew.js';

// src/views/kpi/modelManagement/service.js
export default {
  name: "ViewTableAdd",
  components: {},
  props: {
    KpiChartId: {
      type: String,
      default: ''
    },
    searchFormObj: {
      type: Object,
      default: () => { }
    },
    curDay: {
      type: Object,
      default: () => { }
    },
    Position: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      radio: '1',
      id: undefined,
      tableList: [],
      options: [
        { label: '是', value: '1' },
        { label: '否', value: '0' },
      ],
      dutyListData: []
    }
  },
  computed: {},
  watch: {
    // 'searchFormObj':{
    //     handler(nv,ov){
    //         this.getList()
    //     },
    //     deep: true,
    //     immediate: true
    // }
    // Position: {
    //     handler(nv) {
    //         this.getIDFn()
    //     },
    //     immediate: true
    // },
  },
  created() {
    this.GetKpiListFn()
    this.query()
    // this.getList()
    // console.log('created');

  },
  mounted() {
    // console.log('mounted');

  },
  methods: {
    async query() {
      const params = {
        "SIMLevel": "SIM1",
        "KpiType": ''
      }
      const res = await getDataSourceList(params);
      res.response.map(el => {
        this.dutyListData.push({
          "text": el.KpiName,
          "value": el.KpiCode
        })
      })
    },
    // radioChange(e) {
    //   if (e.value == '0') {

    //   }

    // },
    //获取列表
    async GetKpiListFn() {
      let params = {
        // SimLevel: 'Sim1',
        // "KpiType": "CalculationType"
      }
      let { response } = await GetKpiList(params)
      if (!response) return
      this.tableList = response.filter(item => {
        return item.CalculationType == "1"
      })
      this.tableList.map(el => {
        el.isFlag = '1'
        el.configList = ''
      })
    },
    //获取ID
    async getIDFn() {
      let params = {
        Position: this.Position
      }
      let { response } = await GetChartStructure(params)
      this.id = response.ID
    },
    //确定
    async submitForm() {
      let selectRecords = this.$refs.vxeTable.getRadioRecord()
      if (!selectRecords) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
        return false;
      }
      this.SaveConfigFn(selectRecords)
    },
    //保存
    async SaveConfigFn(selectRecords) {
      let params = {
        ID: this.KpiChartId,
        // SimLevel: this.$route.name,
        SimLevel: 'SIM1',
        Position: this.Position,
        KpiName: selectRecords.KpiName,
        KpiCode: selectRecords.KpiCode,
        IsPopu: selectRecords.isFlag,
      }
      let { status, msg } = await SaveConfig(params)
      if (status === 200) {
        this.$emit('closePopup');
        this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
        // this.addListFn()
        this.$emit('addListFn');
      }

      if (selectRecords.isFlag == '1' && (selectRecords.configList != '' || selectRecords.configList != null || selectRecords.configList != undefined)) {
        let params1 = {
          ID: this.KpiChartId,
          // SimLevel: this.$route.name,
          SimLevel: 'SIM1',
          Position: this.Position + '-1',
          KpiName: selectRecords.KpiName,
          KpiCode: selectRecords.KpiCode,
          IsSql: selectRecords.configList
        }
        const res = await SaveConfig(params1)
        if (res.status === 200) {
          this.$emit('closePopup');
          this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
          // this.addListFn()
          this.$emit('addListFn');
        }
      }

    },
    //根据Position获取SQL
    async getSQLFn() {
      let params = {
        Position: this.Position
      }
      let { response } = await GetChartStructure(params)
      this.id = response.ID
      //替换SQL占位符
      let curSQL = response.SqlText

      //测试用数据
      curSQL = "select t.NAME as '班组名称', t.BOX_TEAM_CODE as 'Box班组代码', t.SHORTNAME as '简称' from DFM_M_TEAM t where t.id = '#' and t.CREATEDATE < CONVERT(date, '$')"
      //替换占位符
      curSQL = this.replaceFirst(curSQL, '#', this.searchFormObj.PresentDepartmentId)
      curSQL = this.replaceFirst(curSQL, '$', this.searchFormObj.date)
      this.getListFn(curSQL)
    },
    //根据SQl获取表格数据
    async getListFn(curSQL) {
      let params = {
        "sqlText": curSQL
      }
      let { response } = await QueryResultBySql(params)
      this.$emit('addListFn', response);
    },
    //取消
    closePopup() {
      this.$emit('closePopup');
    },
    // async getList(){
    //     let params = {
    //         "TeamId": this.searchFormObj.PresentDepartmentId,
    //         "QueryStartDate": this.searchFormObj.date,
    //         "pageIndex": 1,
    //         "pageSize": 10,
    //     }
    //     let res = await GetSafePageList(params)
    // }
    replaceFirst(str, searchValue, replaceValue) {
      // return str.replace(new RegExp(searchValue), replaceValue);
      return str.replace(searchValue, replaceValue);
    }
  },

}
</script>
<style lang="less" scoped></style>
<template>
    <div class="dictionary-view">
        <template v-if="viewType == 'flowChart'">
            <flowChart @changeView="changeView" :treeData="treeData" />
        </template>
        <template v-else>
            <TreeView :items="treeData" :title="$t('DFM_WLMX._WLMX')" :activeKey="activeKey"
                @clickClassTree="clickClassTree"></TreeView>
            <div class="dictionary-main">
                <SearchForm :searchinput="searchinput" :show-from="showFrom" @searchForm="searchForm" />
                <v-card class="ma-1">
                    <div class="form-btn-list">
                        <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                            <v-icon>{{ 'mdi-table-search' }}</v-icon>
                            {{ $t('GLOBAL._SSL') }}
                        </v-btn>
                        <v-btn color="primary" v-has="'WLMX_QHST'" text @click="viewType = 'flowChart'">切换视图</v-btn>
                        <v-btn icon color="primary" @click="GetEquipmentPageList">
                            <v-icon>mdi-cached</v-icon>
                        </v-btn>
                        <v-btn color="primary" v-has="'WLMX_ADD'" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ')
                        }}</v-btn>
                        <v-btn color="primary" v-has="'WLMX_DC'" @click="dataExport()">{{ $t('GLOBAL._EXPORT') }}</v-btn>
                        <v-btn color="error" v-has="'WLMX_ALLREMOVE'" :disabled="deleteList.length < 1"
                            @click="btnClickEvet('delete')">{{
                                $t('GLOBAL._PLSC') }}</v-btn>
                    </div>
                    <Tables :page-options="pageOptions" :loading="loading"
                        :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'" :btn-list="btnList"
                        table-name="DFM_WLMX" :headers="headers" :desserts="desserts" @selectePages="selectePages"
                        @tableClick="tableClick" @itemSelected="SelectedItems" @toggleSelectAll="SelectedItems"></Tables>
                    <physicalModel-dialog ref="physicalModel" :rootitems="rootitems" :treeDatas="treeData"
                        :dialog-type="dialogType" :table-item="tableItem" :delete-list="deleteList"
                        :has-children="hasChildren"></physicalModel-dialog>
                    <AttributeDialog ref="attribute"></AttributeDialog>
                </v-card>
            </div>
        </template>
    </div>
</template>
<script>
import { mixins } from '@/util/mixins.js';
import { GetNoPageList } from '@/api/systemManagement/dataDictionary.js';
import { GetEquipmentTree, GetEquipmentPageList, EquipmentDelete, EquipmentAttrExport } from '@/api/factoryPlant/physicalModel.js';
import { physicalModelColum } from '@/columns/factoryPlant/physicalModel.js';
import { PrintTplGetPageList } from '@/api/systemManagement/printTemplate.js';
const searchinputs = [
    {
        value: '',
        key: 'ItemName',
        icon: 'mdi-account-check',
        label: '',
        placeholder: '请输入项目名称/项目值'
    }
];
export default {
    name: 'PhysicalModel',
    components: {
        PhysicalModelDialog: () => import('./components/physicalModelDialog.vue'),
        AttributeDialog: () => import('./components/attributeDialog.vue'),
        flowChart: () => import('./components/flowChart.vue')
    },
    mixins: [mixins],
    data() {
        return {
            viewType: 'model',
            // tree 字典数据
            loading: true,
            showFrom: false,
            treeData: [],
            papamstree: {
                RootId: null,
                itemCode: null,
                level: null,
                lang: null,
                key: null,
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            searchinput: searchinputs,
            headers: physicalModelColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            deleteId: '', //单个删除
            hasChildren: {}, // 新增字典详情判断-子节点才能新增

            // 新增属性列表
            rootitems: [],
            activeKey: ''
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL.PrintBarcode'),
                    code: 'dy',
                    type: 'primary',
                    icon: '',
                    showKey: 'Level',
                    showList: ['Unit']
                },
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'WLMX_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'WLMX_DELETE'
                }
            ];
        }
    },
    mounted() {
        this.GetEquipmentTree();
        this.GetEquipmentPageList();
        this.GetNoPageList();
        this.GetPrintTpl();
    },
    methods: {
        async GetPrintTpl() {
            let params = {
                type: 'equipmentCode',
                pageIndex: 1,
                pageSize: 10
            };
            const res = await PrintTplGetPageList(params);
            let { success, response } = res;
            if (success) {
                // 处理有效模板
                let data = response?.data.filter(item => item.Status == 1) ?? [];
                let datas = data[0]?.TplJson;
                this.dataTemplate = JSON.parse(datas);
            }
        },
        // 切换视图
        changeView() {
            this.viewType = 'model';
        },
        // 查询数据
        searchForm(value) {
            this.papamstree.key = value.ItemName;
            this.GetEquipmentPageList();
        },
        // 树状点击获取
        clickClassTree(v) {
            // 判断是否还有节点
            this.hasChildren = v;
            this.papamstree.level = v.extendField;
            this.papamstree.key = '';
            this.papamstree.RootId = v.id;
            this.papamstree.itemCode = v.value;
            this.papamstree.pageIndex = 1;
            this.activeKey = v.parentId;            
            this.GetEquipmentPageList();
        },
        // 获取树形数据
        async GetEquipmentTree() {
            let papams = {
                // lang: this.lang
                level: ''
            };
            const res = await GetEquipmentTree(papams);
            if (res.success) {
                this.treeData = res.response;
                this.GetEquipmentPageList();
            } else {
                this.treeData = [];
            }
        },
        // 树形数据点击
        async GetEquipmentPageList() {
            this.activeKey = '';
            let params = {
                lang: this.lang,
                orderByFileds: '',
                Key: this.papamstree.key,
                ParentId: this.papamstree.RootId,
                pageIndex: this.papamstree.pageIndex,
                DataItemCode: 'EquipmentLevel',
                pageSize: this.papamstree.pageSize
            };
            this.loading = true;
            const res = await GetEquipmentPageList(params);
            if (res.success) {
                this.loading = false;
                let data = res.response.data || [];
                data.forEach((item, index) => {
                    item.index = (res.response.page - 1) * res.response.pageSize + index + 1;
                });
                this.desserts = res.response.data;
                this.pageOptions.total = res.response.dataCount;
                this.pageOptions.page = res.response.page;
                this.pageOptions.pageCount = res.response.pageCount;
                this.pageOptions.pageSize = res.response.pageSize;
            } else {
                this.desserts = [];
            }
        },

        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.physicalModel.dialog = true;
                    // this.$refs.physicalModel.initFrom();
                    return;

                case 'delete':
                    if (this.deleteList.length) {
                        this.deleteId = '';
                        this.deltable();
                    } else {
                        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'warning' });
                    }
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'dy':
                    this.PrintTemplateFn({ ...item, Code: item.EquipmentCode, Name: item.ParentName, Eqmodel: "" });
                    // this.$refs.physicalModel.initFrom();
                    return;
                case 'edit':
                    this.$refs.physicalModel.dialog = true;
                    return;
                case 'Enabled':
                    this.$refs.physicalModel.editSubmit('Enabled', item);
                    return;
                case 'attribute':
                    this.$refs.attribute.dialog = true;
                    this.$refs.attribute.initData(item);
                    return;
                case 'delete':
                    this.deleteId = item.ID;
                    this.deltable();
                    return;
            }
        },
        // 删除
        deltable() {
            let params = [], arr = [];
            if (this.deleteId) {
                params.push(this.deleteId);
                arr.push(this.tableItem.EquipmentName);
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                    arr.push(item.EquipmentName);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: arr,
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await EquipmentDelete(params);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.GetEquipmentPageList();
                        this.GetEquipmentTree();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.GetEquipmentPageList();
        },

        // 获取属性编码
        async GetNoPageList() {
            this.rootitems = [];
            let papams = {
                lang: this.lang,
                itemCode: 'EquipmentLevel'
            };
            const res = await GetNoPageList(papams);
            if (res.success) {
                console.log(res.success);
                res.response.forEach(item => {
                    this.rootitems.push({ text: item.ItemName, value: item.ItemValue });
                });
            } else {
                this.rootitems = [];
            }
        },
        //excle导出
        async dataExport(){
            let levelList = {
                "Area":"AreaCode",
                "ProductLine":"ProductLineCode",
                "Segment":"SegmentCode",
                "Unit":"UnitCode"
            };
            let level = this.papamstree.level;  
            let params = {};
            if (Object.prototype.hasOwnProperty.call(levelList, level)) {                
                params = {
                    [levelList[level]]: this.papamstree.itemCode
                };                
            }  
            this.loading = true;
            let resp = await EquipmentAttrExport({ ...params, responseType: 'blob' })
            let blob = new Blob([resp], {
                type: "application/octet-stream",
            });
            var link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = ((this.papamstree.itemCode == "" || this.papamstree.itemCode == null || this.papamstree.itemCode == undefined) ? "" : (this.papamstree.itemCode + "_")) + "物理模型信息.xls";
            link.click();
            //释放内存
            window.URL.revokeObjectURL(link.href);
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: hidden;
    }
}
</style>

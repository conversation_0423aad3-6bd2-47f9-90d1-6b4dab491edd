<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm ref="search" :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'SBBYRW_SCBYRW'" @click="handleCreatePlan()">{{ $t('TPM_SBGL_SBBYJH._SCBYRW') }}</v-btn>
                    <v-btn color="primary" v-has="'SBBYRW_ALLREMOVE'" :disabled="!deleteList.length" @click="btnClickEvet('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn>
                    <v-btn color="primary" v-has="'SBBYRW_DC'" @click="handleExport">{{ $t('GLOBAL._EXPORT') }}</v-btn>
                </div>
                <!-- :currentSelectId="rowtableItem.ID" -->
                <Tables
                    :dictionaryList="dictionaryList"
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    ref="Tables"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_SBBYJH"
                    :headers="keepListBColum"
                    :clickFun="clickFun"
                    :desserts="desserts"
                    @selectePages="selectePages"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"
                ></Tables>
            </v-card>

            <el-drawer size="80%" :title="drawTitle" :wrapperClosable="false" :visible.sync="detailShow" direction="rtl">
                <v-card class="ma-1">
                    <v-tabs v-model="tab" background-color="transparent">
                        <v-tab @click="changeTab(0)" key="0">{{ $t('TPM_SBGL_SBBYJH._XMMX') }}</v-tab>
                        <v-tab @click="changeTab(1)" key="1">{{ $t('TPM_SBGL_SBBYJH._BJMX') }}</v-tab>
                    </v-tabs>
                    <v-tabs-items v-model="tab">
                        <v-tab-item>
                            <div class="form-btn-list">
                                <v-btn color="primary" v-has="'SBBYRW_XMMX_ADD'" @click="adddetail()" :disabled="rowtableItem.Status == '已保养'">{{ $t('GLOBAL._XZ') }}</v-btn>
                                <v-btn color="primary" v-has="'SBBYRW_XMMX_PLBY'" @click="toby()" :disabled="!Flag">{{ $t('TPM_SBGL_SBBYJH._PLBY') }}</v-btn>
                            </div>
                            <Tables
                                :page-options="pageOptions2"
                                :loading="loading2"
                                :btn-list="btnList2"
                                tableHeight="calc(100vh - 220px)"
                                table-name="TPM_SBGL_SBBYJH_XQ"
                                :headers="unkeepPlanColum"
                                :desserts="desserts2"
                                @selectePages="selectePages2"
                                @tableClick="tableClick"
                                @itemSelected="SelectedItems2"
                                @toggleSelectAll="SelectedItems2"
                            ></Tables>
                        </v-tab-item>
                        <v-tab-item>
                            <div class="form-btn-list">
                                <v-btn color="primary" v-has="'SBBYRW_BJMX_ADD'" @click="bjAdd">{{ $t('GLOBAL._XZ') }}</v-btn>
                                <!-- :disabled="!SqFlag" -->
                                <v-btn color="primary" v-has="'SBBYRW_BJMX_SQBJ'" :disabled="!SqFlag" @click="GetRequest">{{ $t('GLOBAL._SQBJ') }}</v-btn>
                                <v-btn color="primary" v-has="'SBBYRW_BJMX_ALLREMOVE'" :disabled="!deleteList3.length" @click="bjDelect">{{ $t('GLOBAL._PLSC') }}</v-btn>
                            </div>
                            <Tables
                                :footer="false"
                                :page-options="pageOptions3"
                                :loading="loading3"
                                :btn-list="btnList3"
                                tableHeight="calc(100vh - 220px)"
                                table-name="TPM_SBGL_SBBYJH_XQ"
                                :headers="keepListBjBColum"
                                :desserts="desserts3"
                                @selectePages="selectePages3"
                                @tableClick="tableClick"
                                @itemSelected="SelectedItems3"
                                @toggleSelectAll="SelectedItems3"
                            ></Tables>
                        </v-tab-item>
                    </v-tabs-items>
                </v-card>
            </el-drawer>
            <createRepast
                ref="createRepast"
                @loadData2="loadData2"
                :statusId="statusId"
                :rowtableItem="rowtableItem"
                :WoId="rowtableItem.ID"
                :dialogType="dialogType"
                :DeviceId="rowtableItem.DeviceId"
                :tableItem="tableItem"
            ></createRepast>
            <el-dialog :title="$t('GLOBAL._SQBJ')" :visible.sync="RequestModel" width="30%">
                <div class="addForm">
                    <v-text-field v-model="user" disabled outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.Creator') + '*'"></v-text-field>
                </div>
                <div class="addForm">
                    <v-text-field v-model="userDate" :clearable="true" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.CreateDate') + '*'" readonly></v-text-field>
                    <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="userDate" type="datetime" :placeholder="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.CreateDate')"></el-date-picker>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="RequestModel = false">取 消</el-button>
                    <el-button type="primary" @click="RequestSave()">确 定</el-button>
                </span>
            </el-dialog>
            <v-dialog v-model="isShowcreatTable" persistent scrollable width="70%">
                <creatTable ref="creatTable" @closePopup="isShowcreatTable = false" @loadData="loadData" v-if="isShowcreatTable" />
            </v-dialog>
        </div>
        <div class="loading-box" v-if="importLoading">
            <a-spin tip="导入中..." :spinning="importLoading"></a-spin>
        </div>
        <BjcreateRepast ref="BjcreateRepast" @loadData="loadData3" :Wo="rowtableItem.Wo" :dialogType="dialogType" :tableItem="tableItem" :rowtableItem="rowtableItem"></BjcreateRepast>
        <editRepast ref="editRepast" @loadData="loadData" :DeviceCategoryId="rowtableItem.ID" :dialogType="dialogType" :tableItem="tableItem"></editRepast>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';

import {
    GetMaintainWoPageList,
    GetMaintainWoDelete,
    GetMaintainWoItemPageList,
    GetMaintainWoItemDelete,
    GetCreateMaintainWoByItems,
    GetMaintainWoStatus,
    GetMaintainWoExport,
    GetPartsHistoryDetailGetList,
    GetPartsHistoryDetailDelete,
    GetPartsHistoryDetailRequest,
    GetMaintains,
    GetMaintain
} from '@/api/equipmentManagement/upkeeplistB.js';
import { EquipmentGetEquipmentTree } from '@/api/common.js';
import { keepListBColum, unkeepPlanColum, keepListBjBColum } from '@/columns/equipmentManagement/upkeep.js';
import moment from 'moment';

import { configUrl } from '@/config';
import { GetExportData, GetPersonList } from '@/api/equipmentManagement/Equip.js';
import { Message, MessageBox } from 'element-ui';

export default {
    name: 'RepastModel',
    components: {
        editRepast: () => import('./components/edit.vue'),
        createRepast: () => import('./components/createRepast.vue'),
        creatTable: () => import('./components/creatTable.vue'),
        BjcreateRepast: () => import('./components/BjcreateRepast.vue')
    },
    data() {
        return {
            detailShow: false,
            drawTitle: '',
            importLoading: false,
            tab: 0,
            isBatchUpkeep: false,
            isShowBatchEdit: false,
            Flag: false,
            stateList: [
                { name: '已保养', value: '1' },
                { name: '未保养', value: '0' }
            ],
            loading: true,
            loading2: false,
            loading3: false,
            showFrom: false,
            papamstree: {
                DeviceCode: '',
                AssetName: '',
                DeviceName: '',
                Status: '',
                CheckBy: '',
                PlanMaintainDateFrom: '', //  开始时间
                PlanMaintainDateTo: '', // 结束时间
                pageIndex: 1,
                pageSize: 20,
                orderByFileds: 'CreateDate desc'
            },
            //查询条件
            keepListBColum,
            unkeepPlanColum,
            keepListBjBColum,
            desserts: [],
            desserts2: [],
            desserts3: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            pageOptions2: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            pageOptions3: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            tableItemBj: {}, // 选择操作数据
            deleteList: [], //批量选中
            deleteList2: [], //批量选中
            deleteList3: [], //批量选中
            rowtableItem: {},
            statusList: [],
            PartOutstockStatus: [],
            DeviceMngData: [],
            MaintainByData: [],
            statusId: '',
            //保养状态
            isShowcreatTable: false,
            SqFlag: false,
            RequestModel: false,
            user: this.$store.getters.getUserinfolist[0].LoginName,
            userDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        };
    },
    computed: {
        dictionaryList() {
            return [{ arr: this.stateList, key: 'Status', val: 'value', text: 'name' }];
        },
        searchinputs() {
            return [
                        {
                    value: '',
                    key: 'Wo',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBBYJH.Wo'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBBYJH.Wo')
                },
                {
                    value: '',
                    key: 'DeviceCode',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipCode'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipCode')
                },
                {
                    value: '',
                    key: 'DeviceName',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Name'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Name')
                },
                {
                    value: '',
                    key: 'PlanMaintainDateFrom',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBDXJH.jhksrq'),
                    placeholder: this.$t('TPM_SBGL_SBDXJH.jhksrq')
                },
                {
                    value: '',
                    key: 'PlanMaintainDateTo',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBDXJH.jhjsrq'),
                    placeholder: this.$t('TPM_SBGL_SBDXJH.jhjsrq')
                },
                {
                    value: '',
                    key: 'AssetName',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.AssetName'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.AssetName')
                },
                {
                    value: '',
                    key: 'MaintainBy',
                    selectData: this.MaintainByData,
                    type: 'select',
                    byValue: 'ItemValue',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.PersonName'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.PersonName')
                },

                {
                    value: '',
                    key: 'Status',
                    selectData: this.statusList,
                    type: 'select',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Status'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Status')
                }
            ];
        },
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    showList: ['未保养', '保养中'],
                    showKey: 'Status',
                    authCode: 'SBBYRW_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    showList: ['未保养', '保养中'],
                    showKey: 'Status',
                    icon: '',
                    authCode: 'SBBYRW_DELETE'
                }
            ];
        },
        btnList2() {
            return [
                {
                    text: this.$t('GLOBAL.Maintenance'),
                    code: 'Maintenance',
                    showList: ['未保养'],
                    showKey: 'Status',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBBYRW_XMMX_BY'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete2',
                    showList: ['未保养', '保养中'],
                    showKey: 'Status',
                    type: 'red',
                    icon: '',
                    authCode: 'SBBYRW_XMMX_DELETE'
                }
            ];
        },
        btnList3() {
            return [
                // {
                //     text: this.$t('GLOBAL._BJ'),
                //     code: 'editBOM',
                //     type: 'primary',
                //     icon: ''
                // },
                // {
                //     text: this.$t('GLOBAL._BJ'),
                //     code: 'edit',
                //     type: 'primary',
                //     icon: '',
                //     authCode: 'SBBYJH_EDIT'
                // },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete3',
                    showList: ['未申请'],
                    showKey: 'Status',
                    type: 'red',
                    icon: '',
                    authCode: 'SBBYRW_BJMX_DELET'
                }
            ];
        }
    },
    async mounted() {
        let DeviceMng = await GetPersonList('DeviceMng');
        this.DeviceMngData = DeviceMng.response[0].ChildNodes;
        this.DeviceMngData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        let MaintainBy = await GetPersonList('MaintenanceGroup');
        this.MaintainByData = MaintainBy.response[0].ChildNodes;
        this.MaintainByData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.statusList = await this.$getNewDataDictionary('MaintainPlanStatus');
        this.PartOutstockStatus = await this.$getNewDataDictionary('PartOutstockStatus');
        this.RepastInfoGetPage();
    },
    methods: {
        bjAdd() {
            this.dialogType = 'add';
            this.$refs.BjcreateRepast.Code = '';
            this.$refs.BjcreateRepast.Name = '';
            this.$refs.BjcreateRepast.Model = '';
            this.$refs.BjcreateRepast.Listform.Qty = "";
            this.$refs.BjcreateRepast.material = [];
            this.$refs.BjcreateRepast.materialList = [];
            this.$refs.BjcreateRepast.showDialog = true;
            this.$refs.BjcreateRepast.getData();
        },
        async toby() {
            let res = await GetMaintains(this.deleteList2);
            this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
            this.deleteList2 = [];
            this.RepastInfoLogGetPage();
            this.RepastInfoGetPage();
        },
        loadData2() {
            this.RepastInfoLogGetPage();
        },
        loadData3() {
            this.MyGetPartsHistoryDetailGetList();
        },
        adddetail() {
            this.statusList.some(item => {
                if (item.ItemName == this.rowtableItem.Status) {
                    this.statusId = item.ID;
                }
            });
            this.dialogType = 'add';
            this.$refs.createRepast.showDialog = true;
        },
        loadData() {
            this.RepastInfoGetPage();
        },
        async handleImport() {
            let _this = this;
            let input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xls,.xlsx';
            input.click();
            let Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            input.onchange = async function () {
                let file = input.files[0];
                let formdata = new FormData();
                formdata.append('file', file);
                _this.importLoading = true;
                try {
                    let res = await GetMaintainWoExport(formdata, Factory);
                    _this.$store.commit('SHOW_SNACKBAR', { text: res.response });
                    _this.RepastInfoGetPage();
                    _this.importLoading = false;
                } catch {
                    _this.importLoading = false;
                }
            };
        },
        async handleExport() {
            let params = {
                ...this.papamstree
            };
            const baseUrl3 = configUrl[process.env.VUE_APP_SERVE]['baseURL_EQUIPMENT'] + `/api/MaintainWo/ExportData`;
            let res = await GetExportData(baseUrl3, params);
            let binaryData = [];
            binaryData.push(res);
            const url = window.URL.createObjectURL(new Blob(binaryData));
            console.log(url);
            const link = document.createElement('a');
            link.href = url;
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            let fileName = `设备保养任务${formattedDateTime}.xlsx`;
            document.body.appendChild(link);
            link.setAttribute('download', fileName);
            link.click();
            window.URL.revokeObjectURL(link.href);
        },
        handleCreatePlan() {
            this.isShowcreatTable = true;
            setTimeout(() => {
                this.$refs.creatTable.PlanStartDate = '';
            }, 500);
            // let resp = await GetCreateMaintainWoByItems({});
            // this.$store.commit('SHOW_SNACKBAR', { text: resp.msg, color: 'success' });
            // this.RepastInfoGetPage();
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.RepastInfoGetPage();
        },
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                ...this.papamstree
            };
            this.loading = true;
            const res = await GetMaintainWoPageList(params);
            let { success, response } = res;
            response.data.forEach(item => {
                this.statusList.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                this.MaintainByData.forEach(it => {
                    if (item.MaintainBy == it.ItemValue) {
                        item.MaintainBy = it.ItemName;
                        item.MaintainByValue = it.ItemValue;
                    }
                });
                this.DeviceMngData.forEach(it => {
                    if (item.Manager == it.ItemValue) {
                        item.Manager = it.ItemName;
                        item.ManagerValue = it.ItemValue;
                    }
                });
            });
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        async bjDelect() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItemBj.hasOwnProperty('ID')) {
                params = [this.tableItemBj.ID];
            } else {
                this.deleteList3.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await GetPartsHistoryDetailDelete(params);
                    if (res.success) {
                        this.tableItemBj = {};
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.MyGetPartsHistoryDetailGetList();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        GetRequest() {
            this.user = this.$store.getters.getUserinfolist[0].LoginName;
            this.userDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.RequestModel = true;
        },
        async RequestSave() {
            if (this.user == '' || this.userDate == null) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            this.deleteList3.forEach(item => {
                item.DeviceId = this.rowtableItem.DeviceId;
                item.Requester = this.user;
                item.RequestDate = this.userDate;
            });
            let res = await GetPartsHistoryDetailRequest(this.deleteList3);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.MyGetPartsHistoryDetailGetList();
                this.RequestModel = false;
            }
        },
        //备件列表
        async MyGetPartsHistoryDetailGetList() {
            let params = {
                ReferNo: this.rowtableItem.Wo,
                Factory: this.$route.query.Factory ? this.$route.query.Factory : '2010'
            };
            this.loading3 = true;
            const res = await GetPartsHistoryDetailGetList(params);
            let { success, response } = res;
            response.forEach(item => {
                this.PartOutstockStatus.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
            });
            if (success) {
                this.loading3 = false;
                this.desserts3 = response || {} || [];
            }
        },
        changeTab(v) {
            switch (v) {
                case 0:
                    setTimeout(() => {
                        this.RepastInfoLogGetPage();
                    }, 10);
                    break;
                case 1:
                    setTimeout(() => {
                        this.MyGetPartsHistoryDetailGetList();
                    }, 10);
                    break;
            }
        },
        clickFun(data) {
            // this.$refs.Tables.selected = [data];
            this.tableItem = data || {};
            this.rowtableItem = data || {};
            console.log(this.rowtableItem);
            this.drawTitle = this.rowtableItem.DeviceName + ' | ' + this.rowtableItem.DeviceCode;
            this.detailShow = true;
            switch (this.tab) {
                case 0:
                    setTimeout(() => {
                        this.RepastInfoLogGetPage();
                    }, 10);
                    break;
                case 1:
                    setTimeout(() => {
                        this.MyGetPartsHistoryDetailGetList();
                    }, 10);
                    break;
            }
        },
        // 明细列表查询
        async RepastInfoLogGetPage() {
            if (!this.rowtableItem.ID) {
                return false;
            }
            let params = {
                WoId: this.rowtableItem.ID
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading2 = true;
            const res = await GetMaintainWoItemPageList(params);
            let { success, response } = res;
            response.forEach(item => {
                this.statusList.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
            });
            if (success) {
                this.loading2 = false;
                this.desserts2 = response || {} || [];
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    break;
                case 'upkeep':
                    this.isBatchUpkeep = true;
                    this.$refs.maintenanceDialog.updateDialog = true;
                    console.log(val);
                    break;
                case 'delete':
                    this.deltable(val);
                    break;
                case 'edit':
                    this.isShowBatchEdit = true;
                    break;
            }
        },
        // 表单操作
        async tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.$refs.editRepast.SbxxList.forEach(item => {
                        for (let k in this.tableItem) {
                            if (item.id == k) {
                                if (k == 'MaintainBy') {
                                    item.value = this.tableItem.MaintainByValue;
                                } else if (k == 'Manager') {
                                    item.value = this.tableItem.ManagerValue;
                                } else {
                                    item.value = this.tableItem[k];
                                }
                            }
                        }
                    });
                    this.$refs.editRepast.SbxxList[9].options = this.DeviceMngData;
                    this.$refs.editRepast.SbxxList[10].options = this.MaintainByData;
                    this.$refs.editRepast.showDialog = true;
                    return;
                case 'newAdd':
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable(type);
                    return;
                case 'Maintenance':
                    this.deleteList2 = [];
                    if (this.tableItem.InputType == '不录入') {
                        let res = await GetMaintain(this.tableItem);
                        if (res.success) {
                            this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
                            this.RepastInfoLogGetPage();
                            this.RepastInfoGetPage();
                        }
                        // this.deleteList2.push(this.tableItem);
                        // this.toby();
                    } else {
                        this.$prompt(`${this.$t('TPM_SBGL_SBDJJH.InputContextMaintenance')}`, `${this.$t('GLOBAL._TS')}`, {
                            confirmButtonText: `${this.$t('GLOBAL._QD')}`,
                            cancelButtonText: `${this.$t('GLOBAL._QX')}`
                        })
                            .then(async ({ value }) => {
                                this.tableItem.Context = value;
                                let res = await GetMaintain(this.tableItem);
                                if (res.success) {
                                    this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
                                    this.RepastInfoLogGetPage();
                                    this.RepastInfoGetPage();
                                }
                            })
                            .catch(() => {});
                    }
                    return;
                case 'delete2':
                    this.deltable(type);
                    return;
                case 'delete3':
                    this.tableItemBj = item;
                    this.deltable(type);
                    return;
                case 'upkeep':
                    this.isBatchUpkeep = false;
                    this.$refs.maintenanceDialog.updateDialog = true;
                    return;
            }
        },
        // 删除
        deltable(type) {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                let list = type == 'delete' ? this.deleteList : type == 'delete2' ? this.deleteList2 : this.deleteList3;
                list.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    if (type == 'delete') {
                        let res = await GetMaintainWoDelete(params);
                        if (res.success) {
                            this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                            this.RepastInfoGetPage();
                        }
                    } else if (type == 'delete2') {
                        let res = await GetMaintainWoItemDelete(params);
                        if (res.success) {
                            this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                            this.RepastInfoLogGetPage();
                        }
                    } else if (type == 'delete3') {
                        this.bjDelect();
                    }

                    this.tableItem = {};
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            this.deleteList = [...item];
        },
        SelectedItems2(item) {
            this.deleteList2 = [...item];
            if (this.deleteList2.length == 0) {
                this.Flag = false;
            } else {
                this.Flag = this.deleteList2.every(item => {
                    return item.Status == '未保养' && item.InputType == '不录入';
                });
            }
        },
        SelectedItems3(item) {
            this.deleteList3 = [...item];
            if (this.deleteList3.length == 0) {
                this.SqFlag = false;
            } else {
                this.SqFlag = this.deleteList3.every(item => {
                    return item.Status == '未申请';
                });
            }
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        },
        selectePages2(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoLogGetPage();
        },
        selectePages3(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            // this.RepastInfoLogGetPage();
        },
        handlePopup() {
            this.RepastInfoLogGetPage();
        }
    }
};
</script>
<style lang="scss">
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}
.addForm {
    position: relative;
    .el-date-editor {
        width: 100%;
        position: absolute;
        opacity: 0;
        top: 0;
        .el-input__inner {
            font-size: 16px;
            height: 32.5px;
            color: rgba(0, 0, 0, 0.87);
            border: 1px solid #9e9e9e;
        }
        .el-input__icon {
            line-height: 32.5px;
        }
    }
}
</style>

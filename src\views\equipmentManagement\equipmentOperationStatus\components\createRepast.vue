<template>
    <v-dialog v-model="showDialog" max-width="980px">
        <v-card class="" v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                新增
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Failuretype" outlined dense label="故障来源"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Repairtime" type="datetime-local" outlined dense label="故障时间"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Productionid" outlined dense label="产线"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Name" outlined dense label="设备名称"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Code" outlined dense label="设备编号"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea label="故障描述" v-model="form.Failuredescription" auto-grow outlined rows="4" row-height="30"></v-textarea>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea label="维修记录" v-model="form.Solutiondescription" auto-grow outlined rows="4" row-height="30"></v-textarea>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Maintenanceperson" outlined dense label="维修人"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Tools" outlined dense label="使用备件"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Sparepartuseid" outlined dense label="备件单号"></v-text-field>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'edit'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                修改
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Productionid" outlined dense label="故障来源"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Productiondate" type="datetime-local" outlined dense label="故障时间"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Productionid" outlined dense label="产线"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Name" outlined dense label="设备名称"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Code" outlined dense label="设备编号"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea label="故障描述" auto-grow outlined rows="4" row-height="30"></v-textarea>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea label="维修记录" auto-grow outlined rows="4" row-height="30"></v-textarea>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Typename" outlined dense label="维修人"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Typename" outlined dense label="使用备件"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Typename" outlined dense label="备件单号"></v-text-field>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions pa-4 class="lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { RepairSaveForm } from '@/api/equipmentManagement/Repair.js';
export default {
    props: {
        repastTypelist: {
            type: Array,
            default: () => []
        },
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            form: {
                Failuretype: '',
                Repairtime: '',
                Productionid: '',
                Name: '',
                Code: '',
                Failuredescription: '',
                Solutiondescription: '',
                Maintenanceperson: '',
                Tools: '',
                Sparepartuseid: ''
            }
        };
    },
    computed: {
        editedItem() {
            return {
                Failuretype: this.tableItem.Failuretype,
                Repairtime: this.tableItem.Repairtime,
                Productionid: this.tableItem.Productionid,
                Name: this.tableItem.Name,
                Code: this.tableItem.Code,
                Failuredescription: this.tableItem.Failuredescription,
                Solutiondescription: this.tableItem.Solutiondescription,
                Maintenanceperson: this.tableItem.Maintenanceperson,
                Tools: this.tableItem.Tools,
                Sparepartuseid: this.tableItem.Sparepartuseid
            };
        }
    },

    methods: {
        closeEquip() {
            this.showDialog = false;
            this.$refs.form.reset();
        },
        async addSave(type) {
            const paramsObj = type == 'add' ? this.form : this.editedItem;
            let params = {
                Failuretype: paramsObj.Failuretype,
                Repairtime: paramsObj.Repairtime,
                Productionid: paramsObj.Productionid,
                Name: paramsObj.Name,
                Code: paramsObj.Code,
                Failuredescription: paramsObj.Failuredescription,
                Solutiondescription: paramsObj.Solutiondescription,
                Maintenanceperson: paramsObj.Maintenanceperson,
                Tools: paramsObj.Tools,
                Sparepartuseid: paramsObj.Sparepartuseid
            };
            if (type == 'edit') {
                params.ID = this.tableItem.ID;
            }
            const res = await RepairSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.$parent.RepastInfoGetPage();
                this.showDialog = this.classcheckbox ? false : true;
            }
        }
    }
};
</script>

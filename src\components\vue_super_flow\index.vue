<template>
    <v-container class="loan-work-queue" grid-list-xl fluid>
        <div class="super-flow-demo1" :style="'background-color:' + canvasBg">
            <div v-if="modeList && modeList.length" class="node-container align-center">
                <div class="d-flex ml-6 mr-2">
                    <div class="super-flow-search prv-s-k" v-show="modeList.length > 0">
                        <v-text-field v-model="listModel" class="mt-3 mb-0" label="" dense @keyup="inputModelList">
                            <v-icon slot="append" @click="searchModelList">mdi-table-search</v-icon>
                        </v-text-field>
                    </div>
                    <v-tabs class="pa-0 ma-0">
                        <v-tab class="pa-0 ma-0" v-for="(item, index) in modelInitial" :key="index" :title="item.label">
                            <span :title="item.label" style="color: rgba(0, 0, 0, 0.54) !important"
                                class="node-item ellipsis" @mousedown="evt => nodeItemMouseDown(evt, item.value)">
                                {{ item.label }}
                            </span>
                        </v-tab>
                    </v-tabs>
                </div>
            </div>
            <div ref="flowContainer" class="flow-container" :class="isNotShowC ? 'isShowC' : ''">
                <super-flow ref="superFlow" :draggable="draggable" :linkAddable="linkAddable"
                    :graph-menu="allSelect ? graphMenu : []" :node-menu="nodeMenu" :link-menu="linkMenu"
                    :link-base-style="linkBaseStyle" :link-style="linkStyle" :link-desc="linkDesc" :node-list="nodeList"
                    :link-list="linkList">
                    <template #node="{ meta }">
                        <div :class="meta.color == 1 ? 'flow-node-active' : 'flow-node-start'" class="flow-node ellipsis"
                            :style="nodeLeft ? 'cursor: pointer;' : ''" @mouseup="nodeMouseUp" @click="nodeClick(meta)">
                            <div class="node-content" :title="meta.name">{{ meta.name }}</div>
                        </div>
                    </template>
                </super-flow>
            </div>
        </div>
        <v-dialog v-model="drawerConf.visible" width="480px">
            <v-card>
                <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                    编辑
                    <v-icon @click="drawerConf.cancel">mdi-close</v-icon>
                </v-card-title>
                <v-card-text class="mt-4">
                    <v-form v-show="drawerConf.type === drawerType.node" ref="nodeSetting" v-model="valid">
                        <v-row>
                            <v-col :cols="12" :lg="12">
                                <v-text-field v-model="nodeSetting.name" disabled label="名称" required dense
                                    outlined></v-text-field>
                            </v-col>
                            <v-col :cols="12" :lg="12">
                                <v-select v-model="nodeSetting.IsCreateBatch" :items="IsCreateBatchList" item-text="name"
                                    item-value="ID" label="是否创建批次" dense outlined></v-select>
                            </v-col>
                        </v-row>
                    </v-form>
                    <v-form v-show="drawerConf.type === drawerType.link" ref="linkSetting" v-model="valid">
                        <v-row>
                            <v-col :cols="12" :lg="12">
                                <v-text-field v-model="linkSetting.desc" label="备注" required dense outlined></v-text-field>
                            </v-col>
                        </v-row>
                    </v-form>
                </v-card-text>
                <v-divider></v-divider>
                <v-card-actions class="d-flex jusify-end">
                    <v-btn color="primary" @click="settingSubmit">{{ $t('GLOBAL._QD') }}</v-btn>
                    <v-btn color="normal" @click="drawerConf.cancel">{{ $t('GLOBAL._GB') }}</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </v-container>
</template>

<script>
import { GetId } from '@/api/factoryPlant/processRoute.js';
import SuperFlow from 'vue-super-flow';
import 'vue-super-flow/lib/index.css';

const drawerType = {
    node: 0,
    link: 1
};

export default {
    components: {
        SuperFlow
    },
    props: {
        // 左键点击node节点
        nodeLeft: {
            type: String,
            default: ''
        },
        // 画布背景色
        canvasBg: {
            type: String,
            default: ''
        },
        // 是否需要获取主键ID
        isGetId: {
            type: Boolean,
            default: true
        },
        // 模块数组
        nodeList: {
            type: Array,
            default: () => []
        },
        // 线条数组
        linkList: {
            type: Array,
            default: () => []
        },
        // 单个节点是否可拖拽,新增编辑时为true
        draggable: {
            type: Boolean,
            default: true
        },
        // 连线是否可操作
        linkAddable: {
            type: Boolean,
            default: true
        },
        // 整个画板是否可全选移动,查看内容时为true方便移动
        allSelect: {
            type: Boolean,
            default: false
        },
        // 模板数组
        modeList: {
            type: Array,
            default: () => []
        },
        showMenuList: {
            type: Array,
            default: () => []
        },
        showLinkMenu: {
            type: Boolean,
            default: false
        },
        isNotShowC: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            nodeName: '',
            IsCreateBatchList: [
                { ID: 0, name: '否' },
                { ID: 1, name: '是' }
            ],
            valid: true,
            graphMenu: [
                [
                    {
                        label: '全选',
                        selected: graph => {
                            graph.selectAll();
                        }
                    }
                ]
            ],
            // 模块数组
            // nodeList: [
            //     // {
            //     //     id: 1,
            //     //     coordinate: [400, 120],
            //     //     width: 120,
            //     //     height: 40,
            //     //     meta: {
            //     //         label: 'start',
            //     //         name: 'start',
            //     //         type: 'start'
            //     //     }
            //     // },
            //     // {
            //     //     id: 2,
            //     //     coordinate: [360, 235],
            //     //     width: 200,
            //     //     height: 40,
            //     //     meta: {
            //     //         label: 'process',
            //     //         name: 'process',
            //     //         type: 'process'
            //     //     }
            //     // }
            // ],
            // 线条数组
            // linkList: [
            //     // {
            //     //     id: 4,
            //     //     startAt: [60, 40],
            //     //     startId: 1,
            //     //     endAt: [100, 0],
            //     //     endId: 2,
            //     //     meta: null
            //     // }
            // ],
            drawerType,
            // 打开模块操作
            drawerConf: {
                title: '',
                visible: false,
                type: null,
                info: null,
                open: (type, info) => {
                    const conf = this.drawerConf;
                    conf.visible = true;
                    conf.type = type;
                    conf.info = info;
                    if (conf.type === drawerType.node) {
                        conf.title = 'NODE';
                        // if (this.$refs.nodeSetting) this.$refs.nodeSetting.reset();
                        this.$set(this.nodeSetting, 'name', info.meta.name);
                        this.$set(this.nodeSetting, 'IsCreateBatch', info.meta.IsCreateBatch);
                    } else {
                        conf.title = 'LINK';
                        if (this.$refs.linkSetting) this.$refs.linkSetting.reset();
                        this.$set(this.linkSetting, 'desc', info.meta ? info.meta.desc : '');
                    }
                },
                cancel: () => {
                    this.drawerConf.visible = false;
                    if (this.drawerConf.type === drawerType.node) {
                        this.$refs.nodeSetting.resetValidation();
                    } else {
                        this.$refs.linkSetting.clearValidate();
                    }
                }
            },
            linkSetting: {
                desc: ''
            },
            nodeSetting: {
                name: '',
                IsCreateBatch: ''
            },

            dragConf: {
                isDown: false,
                isMove: false,
                offsetTop: 0,
                offsetLeft: 0,
                clientX: 0,
                clientY: 0,
                ele: null,
                info: null
            },
            // 模板样式
            nodeItemList: [
                {
                    label: 'start',
                    value: () => ({
                        width: 90,
                        height: 30,
                        meta: {
                            label: 'start',
                            name: 'start',
                            type: 'start'
                        }
                    })
                },
                {
                    label: 'process',
                    value: () => ({
                        width: 90,
                        height: 30,
                        meta: {
                            label: 'process',
                            name: 'process',
                            type: 'process'
                        }
                    })
                },
                {
                    label: 'if',
                    value: () => ({
                        width: 128,
                        height: 128,
                        meta: {
                            label: 'if',
                            name: 'if',
                            type: 'if'
                        }
                    })
                }
            ],
            nodeMenu: [],
            nodeMenuList: [
                [
                    {
                        // 添加、编辑子工序
                        label: this.$t('GLOBAL.subprocesses'),
                        nodeMenuType: 'addChild',
                        selected: node => {
                            this.$emit('fromVueSuperFlow', 'addChild', node);
                        }
                    },
                    {
                        // 编辑
                        label: this.$t('GLOBAL._BJ'),
                        nodeMenuType: 'edit',
                        selected: node => {
                            this.drawerConf.open(drawerType.node, node);
                        }
                    },
                    {
                        // 查看子工序
                        label: this.$t('GLOBAL.subprocesses'),
                        nodeMenuType: 'showChild',
                        selected: node => {
                            this.$emit('fromVueSuperFlow', 'showChild', node);
                        }
                    },
                    {
                        // 查看详情（资源信息）
                        label: this.$t('GLOBAL.resourceInformation'),
                        nodeMenuType: 'toView',
                        selected: node => {
                            this.$emit('fromVueSuperFlow', 'toView', node);
                        }
                    },
                    {
                        // 删除
                        label: this.$t('GLOBAL._SC'),
                        nodeMenuType: 'delete',
                        selected: node => {
                            node.remove();
                            const num = this.showMenuList.indexOf('addChild');
                            if (num > -1) this.$emit('fromVueSuperFlow', 'delete');
                        }
                    },
                    {
                        // 查看工段
                        label: '查看详情',
                        nodeMenuType: 'viewChild',
                        selected: node => {
                            this.$emit('fromVueSuperFlow', 'viewChild', node);
                        }
                    },
                    {
                        // 查看andon事件
                        label: 'andon事件',
                        nodeMenuType: 'viewAndon',
                        selected: node => {
                            this.$emit('fromVueSuperFlow', 'viewAndon', node);
                        }
                    }
                ]
            ],
            linkMenu: [
                [
                    {
                        // 编辑
                        label: this.$t('GLOBAL._BJ'),
                        selected: link => {
                            this.drawerConf.open(drawerType.link, link);
                        }
                    },
                    {
                        // 删除
                        label: this.$t('GLOBAL._SC'),
                        selected: link => {
                            link.remove();
                        }
                    }
                ]
            ],
            linkBaseStyle: {
                color: '#6bc76a', // line 颜色
                hover: '#FF0000', // line hover 的颜色
                textColor: '#666666', // line 描述文字颜色
                textHover: '#FF0000', // line 描述文字 hover 颜色
                font: '14px Arial', // line 描述文字 字体设置 参考 canvas font
                dotted: false, // 是否是虚线
                lineDash: [4, 4], // 虚线时生效，虚线长度和间隔长度
                background: 'rgba(255,255,255,0.6)' // 描述文字背景色
            },
            fontList: ['14px Arial', 'italic small-caps bold 12px arial'],
            listModel: '',
            modelInitial: [],
            scrollLeft: 0,
            scrollTop: 0
        };
    },
    watch: {
        showMenuList: {
            handler(curVal) {
                const arr = this.nodeMenuList[0];
                const a = [];
                arr.forEach(e => {
                    if (curVal.indexOf(e.nodeMenuType) > -1) a.push(e);
                });
                this.nodeMenu[0] = a;
            },
            deep: true,
            immediate: true
        },
        modeList: {
            handler(curVal) {
                this.listModel = '';
                this.modelInitial = [...curVal];
            },
            deep: true,
            immediate: true
        }
    },
    mounted() {
        document.addEventListener('mousemove', this.docMousemove);
        document.addEventListener('mouseup', this.docMouseup);
        this.$once('hook:beforeDestroy', () => {
            document.removeEventListener('mousemove', this.docMousemove);
            document.removeEventListener('mouseup', this.docMouseup);
        });
        if (this.showLinkMenu) this.linkMenu = [];
        // 监听div的滚动事件，同时给变量赋值
        // document.getElementsByClassName('super-flow')[0].addEventListener('scroll', e =>{
        //     const { scrollLeft, scrollTop } = e?.target;
        //     this.scrollLeft = scrollLeft;
        //     this.scrollTop = scrollTop;
        // })
    },
    methods: {
        inputModelList(e) {
            if (e.keyCode == 13) {
                this.searchModelList();
            }
        },
        searchModelList() {
            if (this.listModel == '') {
                this.modelInitial = [...this.modeList];
            } else {
                this.modelInitial = [];
                this.modeList.forEach(e => {
                    if (e.label.indexOf(this.listModel) > -1) this.modelInitial.push(e);
                });
            }
        },
        // 点击node节点
        nodeClick(node) {
            if (this.nodeLeft) {
                this.$emit('fromVueSuperFlow', this.nodeLeft, node);
            }
        },
        // 保存流程图
        saveFlow() {
            const nodeList = this.$refs.superFlow.toJSON().nodeList;
            const linkList = this.$refs.superFlow.toJSON().linkList;
            const num = this.showMenuList.indexOf('addChild');
            this.$emit('fromVueSuperFlow', 'save', nodeList, linkList, num > -1 ? '1' : '2');
        },
        linkStyle(link) {
            if (link.meta && link.meta.desc === '1') {
                return {
                    color: 'red',
                    hover: '#FF00FF',
                    dotted: true
                };
            } else {
                return {};
            }
        },
        linkDesc(link) {
            return link.meta ? link.meta.desc : '';
        },
        settingSubmit() {
            const conf = this.drawerConf;
            if (this.drawerConf.type === drawerType.node) {
                if (!conf.info.meta) conf.info.meta = {};
                Object.keys(this.nodeSetting).forEach(key => {
                    this.$set(conf.info.meta, key, this.nodeSetting[key]);
                });
                this.$refs.nodeSetting.reset();
            } else {
                if (!conf.info.meta) conf.info.meta = {};
                Object.keys(this.linkSetting).forEach(key => {
                    this.$set(conf.info.meta, key, this.linkSetting[key]);
                });
                this.$refs.linkSetting.reset();
            }
            conf.visible = false;
        },
        nodeMouseUp(evt) {
            // console.log(evt);
            // const { x, y } = evt;
            // const super_flow__menu = document.getElementsByClassName('super-flow__menu')[0] || {};
            // const { top, left } = super_flow__menu;
            // if(this.scrollLeft > 0 || this.scrollTop > 0){
            //     super_flow__menu.style.position = '';
            //     super_flow__menu.style.top = this.scrollTop + top + x + 'px';
            //     super_flow__menu.style.left = 1920 + 'px !important';
            //     console.log(super_flow__menu.style.left);
            // }
            // console.log(super_flow__menu);
            evt.preventDefault();
        },
        docMousemove({ clientX, clientY }) {
            const conf = this.dragConf;
            if (conf.isMove) {
                conf.ele.style.top = clientY - conf.offsetTop + 'px';
                conf.ele.style.left = clientX - conf.offsetLeft + 'px';
            } else if (conf.isDown) {
                // 鼠标移动量大于 5 时 移动状态生效
                conf.isMove = Math.abs(clientX - conf.clientX) > 5 || Math.abs(clientY - conf.clientY) > 5;
            }
        },
        docMouseup({ clientX, clientY }) {
            const conf = this.dragConf;
            conf.isDown = false;
            if (conf.isMove) {
                const { top, right, bottom, left } = this.$refs.flowContainer.getBoundingClientRect();
                // 判断鼠标是否进入 flow container
                if (clientX > left && clientX < right && clientY > top && clientY < bottom) {
                    // 获取拖动元素左上角相对 super flow 区域原点坐标
                    const coordinate = this.$refs.superFlow.getMouseCoordinate(clientX - conf.offsetLeft, clientY - conf.offsetTop);
                    // 添加节点
                    this.$refs.superFlow.addNode({
                        coordinate,
                        ...conf.info
                    });
                }
                conf.isMove = false;
            }
            if (conf.ele) {
                conf.ele.remove();
                conf.ele = null;
            }
        },
        // 选择模板按下鼠标时
        async nodeItemMouseDown(evt, infoFun) {
            let info = infoFun();
            const { clientX, clientY, currentTarget } = evt;
            const { top, left } = evt.currentTarget.getBoundingClientRect();
            // 获取主键ID
            if (this.isGetId) {
                const res = await GetId();
                const { success, response } = res || {};
                if (!success) return;
                info = { ...info, id: response };
            }
            const conf = this.dragConf;
            const ele = currentTarget.cloneNode(true);
            Object.assign(this.dragConf, {
                offsetLeft: clientX - left,
                offsetTop: clientY - top,
                clientX: clientX,
                clientY: clientY,
                info,
                ele,
                isDown: true
            });
            ele.style.position = 'fixed';
            ele.style.margin = '0';
            ele.style.top = clientY - conf.offsetTop + 'px';
            ele.style.left = clientX - conf.offsetLeft + 'px';
            console.log(this.dragConf)
            this.$el.appendChild(this.dragConf.ele);
        }
    }
};
</script>
<style lang="scss">
.super-flow-demo1,
.super-flow,
.super-flow-base-demo {

    // 滚动条样式
    ::-webkit-scrollbar-thumb {
        background-color: #c0cecc;
    }
}

.prv-s-k {
    .theme--light.v-text-field>.v-input__control>.v-input__slot:before {
        border-color: gainsboro;
    }

    .v-text-field__details {
        display: none;
    }
}

.node-container {
    .v-tabs-slider-wrapper {
        display: none !important;
    }

    .v-tabs {
        width: 90%;
    }

    .v-tabs-bar {
        height: 40px;
    }

    .row {
        margin-bottom: -20px;
    }

    .col-2,
    .clo-10 {
        padding: 8px 0 0 10px;
    }
}
</style>
<style lang="scss" scoped>
.super-flow-search {
    width: 140px;
    margin-right: 10px;
}

.loan-work-queue {
    width: 100%;
    height: 100%;
    padding: 0;
    // background: #fff;
    // border-top: 1px solid #ddd;
    // border-bottom: 1px solid #ddd;
    border-radius: 3px;
    // overflow: auto;
}

.style-title {
    margin-bottom: 20px;
}

.ellipsis {
    width: 100%;
    height: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-wrap: break-word;
}

.link-base-style-form {
    .v-form-item {
        margin-bottom: 12px;
    }

    padding-top: 30px;
}

.super-flow-demo1 {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
    overflow: auto;

    >.node-container {
        width: 100%;
        background-color: #ffffff;
        border-top: 1px solid #f5f5f5;
    }

    >.flow-container {
        width: 100%;
        height: 100%;
        // overflow: auto;
        position: relative;
    }

    .super-flow__node {
        .flow-node {
            box-sizing: border-box;
            width: 100%;
            height: 100%;
            line-height: 30px;
            padding: 0 3px;
            font-size: 12px;
            color: #fff;

            &:hover {
                box-shadow: 1px 2px 10px rgba(0, 0, 0, 0.7);
            }

            .node-content {
                text-align: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    /*开始节点样式*/
    .ellipsis.flow-node-start {
        // background: #55ABFC;
        background: #6bc76a;
        border-radius: 10px;
        border: 1px solid #b4b4b4;
    }

    /*选中节点样式*/
    .ellipsis.flow-node-active {
        // background: #55ABFC;
        background: red;
        border-radius: 10px;
        border: none;
    }

    /*流程节点样式*/
    .ellipsis.flow-node-process {
        position: relative;
        // background: #30B95C;
        background: #b6c6e7;
        border: 1px solid #b4b4b4;
    }

    /*条件节点样式*/
    .ellipsis.flow-node-if {
        width: 120px;
        height: 120px;
        position: relative;
        top: 24px;
        left: 24px;
        // background: #BC1D16;
        background: #b6e3e7;
        border: 1px solid #b4b4b4;
        transform: rotateZ(45deg); //倾斜
        // .node-content {
        //     position: absolute;
        //     top: 50%;
        //     left: 20px;
        //     width: 100%;
        //     transform: rotateZ(-45deg) translateY(-75%);
        // }
    }

    /*结束节点样式*/
    .ellipsis.flow-node-end {
        // background: #000;
        background: #299999;
        border-radius: 10px;
        border: 1px solid #b4b4b4;
    }
}

.node-item {
    font-size: 12px;
    display: inline-block;
    margin: 4px 5px;
    height: 26px;
    width: 80px;
    background-color: #ffffff;
    line-height: 26px;
    border: 1px solid rgba(0, 0, 0, 0.3);
    cursor: pointer;
    user-select: none;
    text-align: center;
    z-index: 6;

    &:hover {
        box-shadow: 1px 1px 8px rgba(0, 0, 0, 0.4);
    }
}

// .super-flow {
//     overflow: auto;
// }
// .super-flow-base-demo {
//     width: 100%;
//     height: 450px;
//     margin: 0 auto;
//     overflow: scroll;
// }
.super-flow {
    overflow: auto;
}

.isShowC {
    .super-flow {
        width: 3200px;
        height: 600px;
    }
}
</style>
<style lang="scss">
.link-base-style-form .v-form-item__label {
    text-align: left;
    margin-left: 20px;
}

.link-base-style-form {
    border: none;
}

.super-flow-demo1 .super-flow__node {
    border: none;
    background: none;
    box-shadow: none;
}

.super-flow-demo1 {
    .col-9 {
        flex: 0;
        max-width: 100%;
    }
}

.super-flow__menu {
    margin-top: -30px !important;
}
</style>

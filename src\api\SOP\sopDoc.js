import request from '@/util/request'
import { configUrl } from '@/config'
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM

// 获取SOP文档列表
export function getSopDocList(data) {
    return request({
        url: baseURL + '/api/SopDoc/GetPageList',
        method: 'post',
        data
    })
}

// 获取SOP文档详情
export function getSopDocDetail(id) {
    return request({
        url: baseURL + '/api/SopDoc/GetEntity',
        method: 'post',
        data: id
    })
}

// 保存SOP文档
export function saveSopDocForm(data) {
    return request({
        url: baseURL + '/api/SopDoc/SaveForm',
        method: 'post',
        data
    })
}

// 批量添加SOP文档
export function batchAddSopDoc(data) {
    return request({
        url: baseURL + '/api/SopDoc/BatchAdd',
        method: 'post',
        data
    })
}

// 删除SOP文档
export function delSopDoc(data) {
    return request({
        url: baseURL + '/api/SopDoc/Delete',
        method: 'post',
        data
    })
}

// 下载SOP文档
export function downloadSopDoc(id) {
    return request({
        url: baseURL + '/api/SopDoc/Download/' + id,
        method: 'get',
        responseType: 'blob'
    })
}

import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TRACE; // 配置服务url
//获取测试列表
export function BatchGetPageList(data) {
    return request({
        url: baseURL + '/trace/Batch/GetTestBatchPageList',
        method: 'post',
        data
    });
}
//确认接受
export function wocyintoBatch(data) {
    return request({
        url: baseURL + '/trace/wo/cyintoBatch',
        method: 'post',
        data
    });
}

//开始检测
export function wocyintoBatchStatus(data) {
    return request({
        url: baseURL + '/trace/wo/cyintoBatchStatus',
        method: 'post',
        data
    });
}
//次品录入
export function wocyintoBatchBadQuantity(data) {
    return request({
        url: baseURL + '/trace/wo/cyintoBatchBadQuantity',
        method: 'post',
        data
    });
}
//检测完成
export function wocyendBatchCheck(data) {
    return request({
        url: baseURL + '/trace/wo/cyendBatchCheck',
        method: 'post',
        data
    });
}




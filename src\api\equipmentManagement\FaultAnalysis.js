import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';

// 产线
export function DeviceChartStopByLine(data) {
    const api = '/api/DeviceChart/StopByLine';
    return getRequestResources(baseURL, api, 'post', data, true);
}
// 设备
export function DeviceChartStopByDevice(data) {
    const api = '/api/DeviceChart/StopByDevice';
    return getRequestResources(baseURL, api, 'post', data, true);
}
// 子系统
export function DeviceChartStopByPart(data) {
    const api = '/api/DeviceChart/StopByPart';
    return getRequestResources(baseURL, api, 'post', data, true);
}
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';

export function GetOverhaulWoPageList(data) {
    const api = '/api/OverhaulWo/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetOverhaulWoDelete(data) {
    const api = '/api/OverhaulWo/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetOverhaulWoSaveForm(data) {
    const api = '/api/OverhaulWo/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetOverhaulWoStart(data) {
    const api = '/api/OverhaulWo/Start';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetOverhaulWoCancel(data) {
    const api = '/api/OverhaulWo/Cancel';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetOverhaulWoFinish(data) {
    const api = '/api/OverhaulWo/Finish';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetOverhaulWoEvaluate(data) {
    const api = '/api/OverhaulWo/Evaluate';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetRepairOrderList(data) {
    const api = '/api/RepairOrder/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetRepairOrderSaveForm(data) {
    const api = '/api/RepairOrder/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetRepairOrderUploadFile(data) {
    const api = '/api/RepairOrder/UploadFile';
    return getRequestResources(baseURL, api, 'post', data);
}

//工单文件获取
export function GetRepairOrderGetFileUrl(data, params) {
    const api = `/api/RepairOrder/GetFileUrl?fileName=${params}`;
    return getRequestResources(baseURL, api, 'get', data);
}
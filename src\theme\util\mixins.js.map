{"version": 3, "sources": ["../../src/util/mixins.ts"], "names": [], "mappings": ";;;;;;;AACA;;;;AADA;AAKc,SAAU,MAAV,GAA2C;AAAA,oCAAtB,IAAsB;AAAtB,IAAA,IAAsB;AAAA;;AACvD,SAAO,aAAI,MAAJ,CAAW;AAAE,IAAA,MAAM,EAAE;AAAV,GAAX,CAAP;AACD", "sourcesContent": ["/* eslint-disable max-len, import/export, no-use-before-define */\nimport Vue, { VueConstructor } from 'vue'\n\nexport default function mixins<T extends VueConstructor[]> (...args: T): ExtractVue<T> extends infer V ? V extends Vue ? VueConstructor<V> : never : never\nexport default function mixins<T extends Vue> (...args: VueConstructor[]): VueConstructor<T>\nexport default function mixins (...args: VueConstructor[]): VueConstructor {\n  return Vue.extend({ mixins: args })\n}\n\n/**\n * Returns the instance type from a VueConstructor\n * Useful for adding types when using mixins().extend()\n */\nexport type ExtractVue<T extends VueConstructor | VueConstructor[]> = T extends (infer U)[]\n  ? UnionToIntersection<\n    U extends VueConstructor<infer V> ? V : never\n  >\n  : T extends VueConstructor<infer V> ? V : never\n\ntype UnionToIntersection<U> =\n  (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never\n"], "sourceRoot": "", "file": "mixins.js"}
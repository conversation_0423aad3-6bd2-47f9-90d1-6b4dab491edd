<template>
    <v-dialog v-model="dialog" persistent max-width="720px">
        <!-- 新增 -->
        <v-card ref="form">
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                新增字典
                <v-icon @click="dialog = false">mdi-close</v-icon>
            </v-card-title>
            <!-- 表单内容 -->
            <v-card-text class="mt-7">
                <v-container>
                    <v-form ref="form" v-model="valid">
                        <v-row>
                            <v-col class="py-0 px-3" cols="12">
                                <v-text-field ref="causeCode" v-model="formModel.causeCode" :rules="[v => !!v || '项目名不能为空']" outlined dense label="项目名" required></v-text-field>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12">
                                <v-text-field ref="causeName" v-model="formModel.causeName" :rules="[v => !!v || '项目值不能为空']" outlined dense label="项目值" required></v-text-field>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12">
                                <v-text-field ref="causeType" v-model="formModel.causeType" outlined dense label="排序"></v-text-field>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12">
                                <v-container class="px-0" fluid>
                                    <v-checkbox v-model="checkbox" label="有效"></v-checkbox>
                                </v-container>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12">
                                <v-textarea outlined name="input-7-4" label="备注"></v-textarea>
                            </v-col>
                        </v-row>
                    </v-form>
                </v-container>
            </v-card-text>
            <v-card-actions class="grey lighten-3">
                <v-checkbox label="确定并关闭窗口"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSubmit">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="dialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { getSaveForm, getDelete } from '@/api/factoryPlant/supplier.js';
export default {
    name: 'DataDictionaryDialog',
    props: {
        editList: {
            type: Object,
            default: () => {}
        },
        deleteList: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            // 提交表单数据
            valid: true,
            dialog: false,
            checkbox: false,
            formModel: {
                causeCode: null,
                causeName: null,
                causeType: null
            }
        };
    },
    computed: {
        editformModel() {
            return {
                causeCode: this.editList.causeCode,
                causeName: this.editList.causeName,
                causeType: this.editList.causeType
            };
        }
    },
    methods: {
        // this.$refs.form.reset(); 表单重置

        //新增
        async addSubmit() {
            let params = {
                isOutsourcing: this.isOutsourcing,
                supplierCode: this.supplierCode,
                supplierName: this.supplierName,
                tel: this.tel,
                address: this.address,
                remark: this.remark,
                id: '',
                createUserId: 'text'
            };
            let res = await getSaveForm(params);
            let { status, success, msg } = res;
            alert(msg);
            this.dialog = false;
            this.$parent.supplierList();
        },
        //编辑
        async editSubmit() {
            let params = {
                isOutsourcing: this.editList.IsOutsourcing,
                supplierCode: this.editList.SupplierCode,
                supplierName: this.editList.SupplierName,
                tel: this.editList.Tel,
                address: this.editList.Address,
                remark: this.editList.Remark,
                id: this.editList.ID,
                createUserId: 'text',
                updateTimeStamp: this.editList.UpdateTimeStamp,
                modifyUserId: this.editList.ModifyUserId
            };
            let res = await getSaveForm(params);
            let { status, success, msg } = res;
            this.dialog = false;
            this.$parent.supplierList();
        },
        // 删除
        async delSubmit() {
            await getDelete([this.deleteList.ID]);
            this.dialog = false;
            this.$parent.supplierList();
        }
    }
};
</script>
<style lang="scss" scoped>
.v-text-field {
    margin-left: 20px;
}
.col-12 {
    padding: 0;
}
.v-sheet.v-card {
    border-radius: 10px;
}
</style>

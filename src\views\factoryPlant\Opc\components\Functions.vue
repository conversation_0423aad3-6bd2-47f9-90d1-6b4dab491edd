<template>
    <div class="functions">
        <v-row class="tool-row">
            <v-col :cols="12" :lg="3">
                <a-input-search v-model="keywords" enter-button placeholder="Quick Search" @search="onSearch" />
            </v-col>
            <v-col :cols="12" :lg="6" class="pl-0">
                <v-btn @click="getdata">
                    <v-icon left>mdi-cached</v-icon>
                    Refresh</v-btn>
                <v-btn style="margin-left:10px" @click="handleAddFunction()" color="primary">
                    <v-icon left>mdi-plus</v-icon>
                    New</v-btn>
            </v-col>
        </v-row>
        <div class="table-box mt-3" style="height:calc(100vh - 200px)">
            <vxe-table height="auto" class="mytable-scrollbar" :loading="loading" size="mini" border resizable
                ref="table" :data="tableList">
                <vxe-column v-for="(column, index) in opcColumns" :key="index" :min-width="column.minWidth"
                    :width="column.width" :field="column.field" :title="column.title" :fixed="column.fixed">
                    <template #default="{ row }">
                        <div style="display:inline-block;width:85px"
                            v-if="['LogTransactions', 'BlockRead', 'AllowMultipleInstances'].indexOf(column.field) !== -1">
                            <span class="enabled" v-if="row[column.field] == '1'"> <v-icon size="16">mdi-check</v-icon>
                                &nbsp;Enabled</span>
                            <span class="disabled" v-if="row[column.field] == '0'"><v-icon size="15">mdi-cancel</v-icon>
                                &nbsp;Disabled</span>
                        </div>
                        <span v-else-if="column.field == 'Action'"><v-icon @click="editFunction(row)"
                                style="cursor: pointer;" color="#3dcd58" size="18">mdi-pencil</v-icon>
                            <v-icon @click="handleDel(row)" style="cursor: pointer;margin-left:5px" color="#3dcd58"
                                size="18">mdi-delete</v-icon>
                        </span>
                        <div v-else-if="column.field == 'Name'"><v-icon @click="openBomDownload(row)"
                                style="cursor: pointer;" size="18" color="#3dcd58">mdi-file-document-outline</v-icon>
                            {{
                row[column.field] }}
                        </div>
                        <span v-else>{{ row[column.field] }}</span>
                    </template>
                </vxe-column>
            </vxe-table>
            <vxe-pager border size="medium" :loading="loading2" :page-sizes="pageOptions.pageSizeitems"
                :current-page="pageOptions.pageIndex" :page-size="pageOptions.pageSize" :total="pageOptions.total"
                :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
                @page-change="handlePageChange">
            </vxe-pager>
        </div>
        <!-- <v-dialog v-model="isShowBomDownload" persistent scrollable width="55%">
            <bomDownload v-if="isShowBomDownload" :currentObj="currentObj" />
        </v-dialog> -->
        <!-- <a-drawer placement="right" :closable="false" v-model:visible="isShowBomDownload">
            <bomDownload v-if="isShowBomDownload" :currentObj="currentObj" />
        </a-drawer> -->
        <a-drawer placement="right" width="50%" :closable="false" :visible="isShowBomDownload"
            :after-visible-change="afterVisibleChange">
            <bomDownload @openProperty="openProperty" @closeDrawer="closeDrawer" v-if="isShowBomDownload"
                :currentObj="currentObj" />
        </a-drawer>

        <a-modal :visible="isShowPopup" :title="'New Function'" @cancel="handleCancel" @ok="handleOk">
            <addFunction v-if="isShowPopup" :editItemObj="editItemObj" ref="addFunction"></addFunction>
        </a-modal>

        <a-drawer placement="right" width="50%" :closable="false" :visible="isShowDrawer"
            :after-visible-change="afterVisibleChange">
            <instanceProperty @closeDrawer="closePropertyDrawer" :currentObj="itemObj" v-if="isShowDrawer" />
        </a-drawer>
    </div>
</template>

<script>
import { opcColumns } from '@/columns/factoryPlant/Opc.js'
import { getOpcFunc, addOpcFunction, delOpcFunction } from '../service'
import bomDownload from './bomDownload.vue'
import addFunction from './addFunction.vue'
import instanceProperty from './instanceProperty.vue'
export default {
    components: {
        bomDownload,
        addFunction,
        instanceProperty
    },
    data() {
        return {
            editItemObj: {},
            itemObj: {},
            isShowDrawer: false,
            isShowPopup: false,
            currentObj: {},
            isShowBomDownload: false,
            loading2: false,
            opcColumns,
            keywords: '',
            loading: false,
            tableList: [],
            pageOptions: {
                total: 200,
                pageIndex: 1,
                pageSize: 20,
                pageSizeitems: [20, 50, 100, 500]
            }
        }
    },
    provide() {
        return {
            openProperty: this.openProperty
        }
    },
    created() {
        this.getdata()
    },
    methods: {
        handleDel(data) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: '确认要删除此项吗？',
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await delOpcFunction([data.ID]);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.getdata()
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        handleAddFunction() {
            this.editItemObj = {}
            this.isShowPopup = true
        },
        editFunction(data) {
            this.editItemObj = data
            this.isShowPopup = true
        },
        openProperty(data) {
            this.itemObj = data
            this.isShowDrawer = true
        },
        closePropertyDrawer() {
            this.isShowDrawer = false
        },
        handleCancel() {
            this.isShowPopup = false
        },
        async handleOk() {
            let params = this.$refs.addFunction.form
            const resp = await addOpcFunction(params)
            this.isShowPopup = false
            this.$nextTick(() => {
                this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });
                this.getdata()
            })
        },
        closeDrawer() {
            this.isShowBomDownload = false
        },
        afterVisibleChange(val) {
            console.log("val=====", val)
        },
        openBomDownload(data) {
            this.currentObj = data
            this.isShowBomDownload = true
        },
        handlePageChange({ currentPage, pageSize }) {
            this.pageOptions.pageIndex = currentPage
            if (this.pageOptions.pageSize != pageSize) this.pageOptions.pageIndex = 1
            this.pageOptions.pageSize = pageSize
            this.getdata()
        },
        onSearch() {
            this.pageOptions.pageIndex = 1
            this.getdata()
        },
        async getdata() {
            this.loading = true
            try {
                let resp = await getOpcFunc({
                    key: this.keywords,
                    pageIndex: this.pageOptions.pageIndex,
                    pageSize: this.pageOptions.pageSize
                })
                this.pageOptions.total = resp.response.dataCount
                this.tableList = resp.response.data
                this.loading = false
                // this.tableList = [{
                //     Name: 'AssignDestination',
                //     Description: 'Standard interface for verifying pallet information.',
                //     OpcActionClassId: 'DMOOPC AssignDestination',
                //     Timeout: '30000',
                //     Properties: '6',
                //     Triggers: '1',
                //     LogTransactions: true,
                //     BlockRead: false,
                //     AllowMultipleInstances: false
                // }]
            } catch {
                this.loading = false
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.enabled,
.disabled {
    color: #fff;
    display: flex;
    align-items: center;
    border-radius: 4px;
    padding: 0 8px;

    .v-icon {
        color: #fff;
    }
}

.enabled {
    background: #008000;
}

.disabled {
    background: #ff0000;
}

::v-deep .ant-drawer-body {
    height: 100%;
    padding: 12px 10px 24px;
}
</style>
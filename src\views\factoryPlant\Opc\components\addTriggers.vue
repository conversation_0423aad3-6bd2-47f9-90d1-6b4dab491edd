<template>
    <div class="add-trigger">
        <a-form :model="form" :label-col="{ span: 8, }" :wrapper-col="{ span: 14, }">
            <a-form-item label="Trigger Type">
                <a-input v-model="form.TriggerType" />
            </a-form-item>
            <a-form-item label="Property">
                <a-select show-search v-model="form.OpcFunctionPropertyId" placeholder="please select your property">
                    <a-select-option v-for="item in propertyList" :key="item.ID" :value="item.ID">{{ item.Name
                    }}</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="Trigger Condition">
                <a-input v-model="form.TriggerCondition" />
            </a-form-item>
            <a-form-item label="Off Condition">
                <a-input v-model="form.OffCondition" />
            </a-form-item>
            <a-form-item label="Frequency">
                <a-input v-model="form.Frequency" />
            </a-form-item>
            <a-form-item label="Trigger Action Class">
                <a-input v-model="form.OpcActionClassId" />
                <!-- <a-select v-model="form.OpcActionClassId" placeholder="please select your trigger action class">
                    <a-select-option v-for="item in actionClassList" :key="item.ID" :value="item.ID">{{ item.ClassName
                    }}</a-select-option>
                </a-select> -->
            </a-form-item>
        </a-form>
    </div>
</template>

<script>
import { getActionClass } from '../service.js'
export default {
    props: {
        editItemObj: {
            type: Object,
            default: () => { }
        },
        actionClassList: {
            type: Array,
            default: () => []
        },
        propertyList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            form: {
                TriggerType: '',
                TriggerCondition: '',
                OffCondition: '',
                Frequency: '',
                OpcActionClassId: undefined,
                OpcFunctionPropertyId: undefined,

            }
        }
    },
    created() {
        if (this.editItemObj && this.editItemObj.ID) {
            for (const key in this.form) {
                this.form[key] = this.editItemObj[key]
            }
            this.form.ID = this.editItemObj.ID
        }
    },
    methods: {

    }
}
</script>

<style lang="scss" scoped>
.ant-row.ant-form-item {
    margin-bottom: 10px;
}
</style>

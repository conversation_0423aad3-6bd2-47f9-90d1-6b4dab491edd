// 多次停机规则
<template>
    <div class="line-side-view">
        <div class="line-side-main overflow-auto">
            <SearchForm ref="contactTorm" class="mt-2" :searchinput="searchinput" :show-from="showFrom"
                @searchForm="searchForm" />
            <v-card outlined>
                <div class="form-btn-list">
                    <!-- 搜索栏 -->
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary">
                        <v-icon @click="getDataList">mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'DCTJBJGZ_ADD'" @click="operaClick({})">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="error" v-has="'DCTJBJGZ_ALLREMOVE'" @click="sureItems()"
                        :disabled="selectedList.length == 0">{{ $t('GLOBAL._PLSC')
                        }}</v-btn>
                </div>
                <Tables :headers="headers" :desserts="desserts" :loading="loading" :page-options="pageOptions"
                    :btn-list="btnList" :dictionaryList="dictionaryList" table-name="ANDON_DCTJBJ"
                    @selectePages="selectePages" @itemSelected="selectedItems" @toggleSelectAll="selectedItems"
                    @tableClick="tableClick"></Tables>
            </v-card>
            <update-dialog ref="updateDialog" :opera-obj="operaObj" :productLineList="productLineList"
                @handlePopup="handlePopup"></update-dialog>
        </div>
    </div>
</template>
<script>
import { MultistopRuleGetPageList, MultistopRuleDelete } from '@/api/andonManagement/multipleStopRule.js';
import { multipleStopRule } from '@/columns/andonManagement/multipleStopRule.js';
import Util from '@/util'
export default {
    name: 'MultipleStopRule',
    components: {
        UpdateDialog: () => import('./components/updateDialog.vue')
    },
    data() {
        return {
            operaObj: {},
            showFrom: false,
            headers: multipleStopRule,
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            deleteId: [],
            selectedList: [],
            desserts: [],
            searchParams: {},
            productLineList: []
        }
    },
    computed: {
        //查询条件
        searchinput() {
            return [
                // 工段
                {
                    key: 'ProductionLineCode',
                    icon: '',
                    type: 'select',
                    selectData: this.$changeSelectItems(this.productLineList, 'EquipmentCode', 'EquipmentName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_DCTJBJ.ProductionLineCode')
                },
            ];
        },
        btnList() {
            return [
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary', authCode: 'DCTJBJGZ_EDIT' },
                { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'error', authCode: 'DCTJBJGZ_DELETE' }
            ];
        },
        dictionaryList() {
            return [
                { arr: this.productLineList, key: 'ProductionLineCode', val: 'EquipmentCode', text: 'EquipmentName' }
            ]
        }
    },
    created() {
        this.initData()
    },
    mounted() {
        this.getDataList();
    },
    methods: {
        async initData() {
            // 获取工段列表
            this.productLineList = await Util.GetEquipmenByLevel('Line');
        },
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        // 操作栏按钮
        tableClick(item, type) {
            switch (type) {
                // 编辑
                case 'edit':
                    this.operaClick(item);
                    break;
                // 删除
                case 'delete':
                    this.deleteId = item?.ID
                    this.sureDelete()
                    break;
                default:
                    break;
            }
        },
        // 获取全部表格数据
        async getDataList() {
            this.loading = true;
            let params = {
                ...this.searchParams,
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await MultistopRuleGetPageList(params);
            const { success, response } = res || {};
            const { data, dataCount, page } = response || {};
            this.desserts = []
            if (success && data) {
                const arr = data || [];
                arr.forEach(e => {
                    this.desserts.push({ ...e, actionsBtnShow: 'Status' })
                });
                this.pageOptions.total = dataCount;
                this.pageOptions.page = page;
            } else {
                this.desserts = [];
            }
            this.loading = false;
        },
        // 查询数据
        searchForm(v) {
            this.searchParams = v;
            this.getDataList();
        },
        // 新增
        operaClick(o) {
            this.operaObj = o || {};
            this.$refs.updateDialog.dialog = true;
        },
        // 批量删除
        sureItems() {
            this.deleteId = ''
            this.sureDelete();
        },
        // 删除二次确认
        sureDelete() {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    const params = [];
                    if (this.deleteId) {
                        params.push(this.deleteId);
                    } else {
                        this.selectedList.forEach(e => {
                            params.push(e.ID);
                        });
                    }
                    const res = await MultistopRuleDelete(params);
                    this.selectedList = [];
                    this.deleteId = '';
                    const { success, msg } = res;
                    if (success) {
                        this.pageOptions.pageCount = 1;
                        this.getDataList();
                        this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 根据子组件返回来值
        handlePopup(type, data) {
            this.getDataList();
            // switch (type) {
            //     case 'refresh':
            //         this.getDataList();
            //         break;
            //     case 'detail':
            //         this.receivedorderid = data?.ID
            //         this.$refs.materailDetailDialog.dialog = true;
            //         break;
            //     default:
            //         break;
            // }
        }
    }
};
</script>
<style lang="scss" scoped>
.line-side-view {
    display: flex;

    .line-side-main {
        flex: 1;
        width: 100%;

        .v-data-table {
            width: 100%;
        }
    }
}
</style>
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Inventory'
const baseURL2 = 'baseURL_MATERIAL'

export function GetDestinationList(data) {
    const api = '/api/Container/GetDestinationList'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetConStatusList(data) {
    const api = '/api/Container/GetConStatusList'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetBin(data) {
    const api = '/api/BatchPalletView/GetBin'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetBins(data) {
    const api = '/api/BatchPalletView/QueryBinS'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMachine(data) {

    const api = '/api/BatchPalletView/GetMachine'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetPageList(data) {
    const api = '/api/BatchPalletView/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetPageListDetail(data) {
    const api = '/api/BatchPalletView/GetPageListDetail'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetListSelect(data) {
    const api = '/api/BatchPalletView/GetListSelect'
    return getRequestResources(baseURL, api, 'post', data, true);
}
export function TransferContainerByContainer(data) {
    const api = '/api/Container/TransferContainerByContainer'
    return getRequestResources(baseURL, api, 'post', data);
}

export function TransferContainer(data) {
    const api = '/api/Container/TransferContainer'
    return getRequestResources(baseURL, api, 'post', data);
}
export function SaveDistribution(data) {
    const api = '/api/BatchPalletView/SaveDistribution'
    return getRequestResources(baseURL, api, 'post', data);
}
export function DeletePag(data) {
    const api = '/api/BatchPalletView/DeletePag'
    return getRequestResources(baseURL, api, 'post', data, true);
}

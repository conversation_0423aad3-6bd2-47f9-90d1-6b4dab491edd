import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';

// 设备树状结构
export function GetDeviceTree(data) {
    const api = '/api/DeviceCategory/GetTree';
    return getRequestResources(baseURL, api, 'post', data, true);
}
// 设备列表
export function GetDevicePageList(data) {
    const api = '/api/DeviceCategory/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}
// 设备保存
export function DeviceSaveForm(data) {
    const api = '/api/DeviceCategory/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
// 设备删除
export function DeviceDelete(data) {
    const api = '/api/DeviceCategory/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}

//BOM列表
export function GetDeviceBomPageList(data) {
    const api = '/api/DeviceCategoryBom/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}

//资料列表
export function GetDeviceDocPageList(data) {
    const api = '/api/DeviceCategoryDoc/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
html {
    font-size: 14px !important;
}

.v-list-item .v-list-item__icon {
    margin: 6px 0 !important;
}

.v-list--dense .v-list-item {
    min-height: 36px !important;
}

.v-treeview--dense .v-treeview-node__root {
    min-height: 36px !important;
}
.v-treeview--dense .v-treeview-node__root .v-treeview-node__level{
    width: 24px !important
}
.v-btn,
.v-tab {
    text-transform: none !important
}

.v-list-group--no-action>.v-list-group__items>.v-list-item {
    padding-left: 40px !important
}

.v-application--is-ltr .v-list-item__icon:first-child {
    margin-right: 18px !important;
}

.v-navigation-drawer--custom-mini-variant .v-list-item {
    padding-right: 10px !important;
}

.form-btn-list {
    display: block;
    text-align: right;
    padding: 12px;
    .v-btn {
        margin-right: 12px;
        text-transform: none !important
    }
}
.the-child-table-menu {
    .v-btn {
        margin-right: 12px;
    }
}

// 表格高度处理

.v-data-table>.v-data-table__wrapper>table>tbody>tr>td,
.v-data-table>.v-data-table__wrapper>table>thead>tr>td,
.v-data-table>.v-data-table__wrapper>table>tfoot>tr>td {
    height: 30px !important;
}

.v-data-table__wrapper>table>thead>tr>th,
.v-data-table>.v-data-table__wrapper>table>tfoot>tr>th {
    height: 34px !important;
    // background: var(--v-primary-lighten3) !important;
    // background-color: #ccc !important;
    // box-shadow: 1px 1px 2px #ccc !important;
}

.v-data-table>.v-data-table__wrapper>table>tbody>tr:hover,
.v-data-table>.v-data-table__wrapper>table>thead>tr:hover,
.v-data-table>.v-data-table__wrapper>table>tfoot>tr:hover {
    background: var(--v-primary-lighten5) !important;

}

.v-btn:not(.v-btn--round).v-size--default {
    height: 30px !important;
}

.v-btn:not(.v-btn--round).v-size--small {
    min-width: 22px !important;
}

.v-text-field--filled.v-input--dense.v-text-field--single-line>.v-input__control>.v-input__slot,
.v-text-field--filled.v-input--dense.v-text-field--outlined>.v-input__control>.v-input__slot,
.v-text-field--filled.v-input--dense.v-text-field--outlined.v-text-field--filled>.v-input__control>.v-input__slot,
.v-text-field--full-width.v-input--dense.v-text-field--single-line>.v-input__control>.v-input__slot,
.v-text-field--full-width.v-input--dense.v-text-field--outlined>.v-input__control>.v-input__slot,
.v-text-field--full-width.v-input--dense.v-text-field--outlined.v-text-field--filled>.v-input__control>.v-input__slot,
.v-text-field--outlined.v-input--dense.v-text-field--single-line>.v-input__control>.v-input__slot,
.v-text-field--outlined.v-input--dense.v-text-field--outlined>.v-input__control>.v-input__slot,
.v-text-field--outlined.v-input--dense.v-text-field--outlined.v-text-field--filled>.v-input__control>.v-input__slot,
.v-input--dense.v-text-field--outlined>.v-input__control>.v-input__slot,
.v-input__control>.v-input__slot>.v-select__slot>.v-select__selections {
    min-height: 32px !important;
}

.v-select.v-text-field--outlined:not(.v-text-field--single-line).v-input--dense .v-select__selections {
    padding: 0 !important;
}

.v-text-field--enclosed.v-input--dense:not(.v-text-field--solo).v-text-field--outlined .v-input__append-inner {
    margin-top: 4px !important;
}

.v-input .v-label {
    line-height: 14px !important;
}

.v-select-list .v-list-item {
    // display: inline-block !important;
    // width: 24px !important;
    margin: auto !important;
    // padding: 0 !important;
    height: 24px !important;
}

.vue-treeselect {
    .vue-treeselect__control {
        border: 1px solid rgba(0, 0, 0, 0.47) !important;
        height: 32px !important;
    }
}

.zk-table__body-wrapper .zk-table__body .zk-table__body-row {
    height: 30px !important;
}

.zk-table__cell-inner {
    padding: 2px 16px !important;
}

.mdi-checkbox-blank-outline,
.mdi-checkbox-marked,
.mdi-radiobox-blank,
.mdi-radiobox-marked,
.mdi-minus-box {
    font-size: 18px !important;
}

.v-pagination__navigation,
.v-pagination__item,
.v-pagination__navigation {
    min-width: 26px !important;
    height: 26px !important;
}

.super-flow-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 4px 0 4px 8px;
}

.super-flow-breadcrumbs {
    display: flex;
    align-items: center;
    background: white;

    span {
        display: flex;
    }

    .v-icon.v-icon {
        font-size: 16px;
        margin: 0 6px;
    }

    .activ-class {
        cursor: pointer;
        color: #00d063;
    }
}

.zk-table {
    tr {

        td:last-child,
        th:last-child {
            table-layout: fixed;
            position: sticky;
            right: 0;
            background: #ffffff;
            overflow: hidden;
            text-align: center;
        }

        td:last-child {
            .zk-table__cell-inner {
                display: flex;
                justify-content: space-around;
            }
        }
    }

    tr:last-child {
        th {
            background: #f8f8f9;
        }
    }

    tr td:last-child:hover {
        background: var(--v-primary-lighten5) !important;
    }

    .v-input__slot {
        margin-bottom: 0;
        justify-content: center;
    }

    .v-messages {
        display: none;
    }
}

.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
}

// 滚动条样式
::-webkit-scrollbar {
    width: 12px;
    background-color: #fff;

}

:hover ::-webkit-scrollbar-track-piece {

    /*鼠标移动上去再显示滚动条*/

    background-color: #fff;

    /* 滚动条的背景颜色 */

    border-radius: 2px;

    /* 滚动条的圆角宽度 */

}



:hover::-webkit-scrollbar-thumb:hover {

    background-color: #c0cecc;

}

:hover::-webkit-scrollbar-thumb:vertical,
.v-data-table ::-webkit-scrollbar-thumb,
.zk-table ::-webkit-scrollbar-thumb {

    background-color: #c0cedc;

    border-radius: 2px;

    outline: 0px solid #fff;

    outline-offset: -2px;

    border: 4px solid #fff;

}

.v-dialog.v-dialog--active {
    margin: 0;
}


.v-treeview {
    height: calc(100vh - 100px) !important;
    overflow: auto !important;
    min-width:250px
}
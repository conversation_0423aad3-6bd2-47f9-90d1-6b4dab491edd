import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TRACE; // 配置服务url
// // 列表查询
export function BatchGetPageList(data) {
    return request({
        url: baseURL + '/trace/Batch/GetCombineBatchPageList',
        method: 'post',
        data
    });
}
// 首页 列表
export function hpBatchData(data) {
    return request({
        url: baseURL + '/trace/wo/hpBatchData',
        method: 'post',
        data
    });
}
//扫描录入校验A-B类
export function hpSingleBatchScanData(data) {
    return request({
        url: baseURL + '/trace/wo/hpSingleBatchScanData',
        method: 'post',
        data
    });
}
//合批列表
export function hpBatchScanData(data) {
    return request({
        url: baseURL + '/trace/wo/hpBatchScanData',
        method: 'post',
        data
    });
}
// 合并批次
export function hpBatchCommit(data) {
    return request({
        url: baseURL + '/trace/wo/hpBatchCommit',
        method: 'post',
        data
    });
}
// 合





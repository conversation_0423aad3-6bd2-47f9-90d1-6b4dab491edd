import { format } from 'date-fns';
import { humanReadableFileSize } from 'vuetify/lib/util/helpers';
import { UnitmanageGetList, MaterialGetList, GetNewDetailTree, dataItemDetailGetList, dataItemDetailGetList2, EquipmenGetListByLevel, DepartmentGetListByLevel } from '@/api/common.js';
import { getReasonInfoList } from '@/api/factoryPlant/reasonInfo.js';

const formatDate = (d, dateFormat = 'yyyy-MM-dd') => {
    return format(d, dateFormat);
};

const randomElement = (arr = []) => {
    return arr[Math.floor(Math.random() * arr.length)];
};

const kebab = str => {
    return (str || '').replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
};

const bytes = byte => {
    return humanReadableFileSize(byte);
};

const toggleFullScreen = () => {
    let doc = window.document;
    let docEl = doc.documentElement;

    let requestFullScreen = docEl.requestFullscreen || docEl.mozRequestFullScreen || docEl.webkitRequestFullScreen || docEl.msRequestFullscreen;
    let cancelFullScreen = doc.exitFullscreen || doc.mozCancelFullScreen || doc.webkitExitFullscreen || doc.msExitFullscreen;

    if (!doc.fullscreenElement && !doc.mozFullScreenElement && !doc.webkitFullscreenElement && !doc.msFullscreenElement) {
        requestFullScreen.call(docEl);
    } else {
        cancelFullScreen.call(doc);
    }
};

/**
 * 去除对象中所有符合条件的对象
 * @param {Object} obj 来源对象
 * @param {Function} fn 函数验证每个字段
 */
function compactObj(obj, fn) {
    for (var i in obj) {
        if (typeof obj[i] === 'object') {
            compactObj(obj[i], fn);
        }
        if (fn(obj[i])) {
            delete obj[i];
        }
    }
}

// 删除空对象 删除'', null, undefined
function isEmpty(foo) {
    if (typeof foo === 'object') {
        for (var i in foo) {
            return false;
        }
        return true;
    } else {
        return foo === '' || foo === null || foo === undefined;
    }
}

const removeEmpty = o => {
    compactObj(o, isEmpty);
};

// 语言转换
const changLange = (o, s, fn) => {
    const headList = o.map(item => {
        const k = `$vuetify.dataTable.${s}.${item.prop}`;
        if (s && item.prop && k) {
            item.text = fn(k);
        }
        return item;
    });
    return headList || o;
};

// 传值修改
const changeSelectItems = (a, v, l) => {
    const b = [];
    if (Array.isArray(a) && a) {
        a.forEach(i => {
            b.push({ ...i, label: i[l], value: i[v] });
        });
    }
    return b;
};

// 格式化年计划  月计划  日计划数据
const initPlanData = (list, dateKey) => {
    // 获取数据中的所有要展示的日期列
    let dateArr = [];
    list.forEach(item => {
        dateArr.push(item[dateKey]);
    });
    // 日期去重
    dateArr = [...new Set(dateArr)].sort();
    //  根据日期把数据格式化
    let dataArr = [];
    list.forEach(item => {
        //获取当前物料号在dataArr中的位置
        let ind = dataArr.findIndex(itm => itm.ProductionCode == item.ProductionCode);
        // 如果当前物料号已经在dataArr中存在 则把当前数据的数量添加到物料对象上  反之则将当前数据push进dataArr中
        if (ind !== -1) {
            dataArr[ind][item[dateKey]] = item.Quantity;
        } else {
            item[item[dateKey]] = item.Quantity;
            dataArr.push(item);
        }
    });
    return {
        dateArr,
        dataArr
    };
};

let dictionaryTree = {};
let newdictionaryTree = {};
let myperson = []
let personFlag = false
// 数据字典缓存
const getNewDataDictionary = async code => {
    if (!(newdictionaryTree[code] && newdictionaryTree[code][0])) {
        try {
            let params = {
                itemCode: code
            };
            let res = await GetNewDetailTree(params);
            const { success, response } = res || {};
            if (response && success) newdictionaryTree[code] = response || [];
        } catch (error) {
            console.log(error);
        }
    }
    return newdictionaryTree[code];
};
// 获取人员信息
const getpersonList = async code => {
    if (code == null || code == "") {
        return
    }
    let params = {
        key: "",
        orderByFileds: "",
        pageIndex: 1,
        pageSize: 100000
    }
    let Name = ""
    if (!personFlag) {
        let res = await getReasonInfoList(params);
        personFlag = true
        const { success, response } = res || {};
        if (response && success) {
            myperson = response.data
        }
    }
    if (myperson.length == 0) {
        Name = ""
    } else {
        let arr = myperson.filter(item => {
            return item.ReasontreeCode == code
        })
        if (arr.length != 0) {
            Name = arr[0].ReasontreeName
        } else {
            Name = code
        }
    }
    return Name
};
// 数据字典不缓存
const getNewDataDictionaryNoCache = async code => {
    let params = {
        itemCode: code
    };
    let res = await GetNewDetailTree(params);
    // console.log(res.response)
    return res.response
};
// 获取数据字典
const getDataDictionary2 = async code => {
    let params = {
        itemCode: code
    };
    let res = await dataItemDetailGetList2(params)
    // console.log(res.response)
    return res.response
};
// 获取数据字典
const getDataDictionary = async code => {
    if (!(dictionaryTree[code] && dictionaryTree[code][0])) {
        try {
            const res = await dataItemDetailGetList(code);
            const { success, response } = res || {};
            if (response && success) dictionaryTree[code] = response || [];
        } catch (error) {
            console.log(error);
        }
    }
    return dictionaryTree[code];
};

// 获取单位列表
const getUnitList = async () => {
    let arr = [];
    try {
        const res = await UnitmanageGetList();
        const { success, response } = res || {};
        if (response && success) arr = response;
    } catch (error) {
        console.log(error);
    }
    return arr;
};

// 获取物料列表
const getMaterialList = async code => {
    let arr = [];
    try {
        const res = await MaterialGetList(code);
        const { success, response } = res || {};
        if (response && success) arr = response;
    } catch (error) {
        console.log(error);
    }
    return arr;
};

// 获取物理模型
// Plant:工厂 Area:车间 ProductLine:产线 Segment:工段 Unit:工作单元
const GetEquipmenByLevel = async key => {
    let arr = [];
    try {
        const res = await EquipmenGetListByLevel({ key });
        const { success, response } = res || {};
        if (response && success) arr = response;
    } catch (error) {
        console.log(error);
    }
    return arr;
};
// 获取部门等级
// Group集团 Plant公司 Department部门 Post岗位 Team班组
const GetDepartmentByLevel = async Level => {
    let arr = [];
    try {
        const res = await DepartmentGetListByLevel({ Level });
        const { success, response } = res || {};
        if (response && success) arr = response;
    } catch (error) {
        console.log(error);
    }
    return arr;
};

// 根据字典和key, val返回值
const getDictionaryVal = (s, arr, k, v) => {
    let obj = null;
    try {
        obj = arr.find(i => i[k] == s);
    } catch (error) {
        console.log(error);
    }
    return obj ? obj[v] : s;
};

// 加千位符
const formatNum = num => {
    if (isNaN(parseFloat(num))) return '';
    //str 字符类型的数字
    let thousandsReg = /(\d)(?=(\d{3})+$)/g;
    const numArr = (num + '').split('.');
    numArr[0] = numArr[0].replace(thousandsReg, '$1,');
    return numArr.join('.');
};

const findChildren = (treeData, o, n) => {
    for (var i = 0; i < treeData.length; i++) {
        console.log(treeData[i][o], o);
        if (treeData[i][o].length) {
            treeData[i][n] = findChildren(treeData[i][o]);
        } else {
            delete treeData[i][o];
        }
    }
    return treeData;
};
// 数组处理
const deleteTreeData = (treeData, o, n) => {
    for (var i = 0; i < treeData.length; i++) {
        if (treeData[i][o].length) {
            treeData[i][n] = findChildren(treeData[i][o]);
        } else {
            delete treeData[i][o];
        }
    }
    return treeData;
};

// 添加查询标志
const setSearchTarget = () => {
    sessionStorage.searchTarget = 'resetPages';
};
// 获取查询标志
const getSearchTarget = () => {
    const searchTarget = sessionStorage.searchTarget;
    return searchTarget == 'resetPages' ? true : false;
};
// 移除查询标志
const removeSearchTarget = () => {
    sessionStorage.searchTarget = '';
};

export default {
    randomElement,
    toggleFullScreen,
    formatDate,
    bytes,
    kebab,
    removeEmpty,
    changLange,
    changeSelectItems,
    getDataDictionary,
    getNewDataDictionary,
    getpersonList,
    getNewDataDictionaryNoCache,
    getDataDictionary2,
    initPlanData,
    getDictionaryVal,
    getUnitList,
    getMaterialList,
    GetEquipmenByLevel,
    formatNum,
    GetDepartmentByLevel,
    deleteTreeData,
    setSearchTarget,
    removeSearchTarget,
    getSearchTarget
};
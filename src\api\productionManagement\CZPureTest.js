import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_TRACE'

//根据产线ID获取工单+工序列表
export function WoSnGetWoByCompanyId(data) {
    const api = '/trace/WoSn/GetWoByCompanyId'
    return getRequestResources(baseURL, api, 'post', data);
}

//提交SN数据
export function WoSnCommitSnData(data) {
    const api = '/trace/WoSn/CommitSnData'
    return getRequestResources(baseURL, api, 'post', data);
}

//获取表格
export function WoSnGetPageList(data) {
    const api = '/trace/WoSn/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

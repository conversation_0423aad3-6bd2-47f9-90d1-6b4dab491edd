import request from '@/util/request'
import { configUrl } from '@/config'
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM

// 获取文档目录分页列表
export function getSopDirList(data) {
    return request({
        url: baseURL + '/api/SopDir/GetPageList',
        method: 'post',
        data
    })
}

// 获取文档目录树结构
export function getSopDirTree(data) {
    return request({
        url: baseURL + '/api/SopDir/GetTreeList',
        method: 'get',
        data
    })
}

// 获取文档目录详情
export function getSopDirDetail(id) {
    return request({
        url: baseURL + '/api/SopDir/GetEntity',
        method: 'post',
        data: id
    })
}

// 保存文档目录
export function saveSopDirForm(data) {
    return request({
        url: baseURL + '/api/SopDir/SaveForm',
        method: 'post',
        data
    })
}

// 删除文档目录
export function delSopDir(data) {
    return request({
        url: baseURL + '/api/SopDir/Delete',
        method: 'post',
        data
    })
}

{"version": 3, "sources": ["../../src/util/dateTimeUtils.ts"], "names": [], "mappings": ";;;;;;;;AAAA,SAAS,aAAT,CAAwB,IAAxB,EAAwD;AAAA,MAAlB,KAAkB,uEAAV,CAAU;AAAA,MAAP,GAAO,uEAAD,CAAC;AACtD,MAAI,IAAJ;;AACA,MAAI,IAAI,GAAG,GAAP,IAAc,IAAI,IAAI,CAA1B,EAA6B;AAC3B,IAAA,IAAI,GAAG,IAAI,IAAJ,CAAS,IAAI,CAAC,GAAL,CAAS,IAAT,EAAe,KAAf,EAAsB,GAAtB,CAAT,CAAP;;AACA,QAAI,QAAQ,CAAC,IAAI,CAAC,cAAL,EAAD,CAAZ,EAAqC;AACnC,MAAA,IAAI,CAAC,cAAL,CAAoB,IAApB;AACD;AACF,GALD,MAKO;AACL,IAAA,IAAI,GAAG,IAAI,IAAJ,CAAS,IAAI,CAAC,GAAL,CAAS,IAAT,EAAe,KAAf,EAAsB,GAAtB,CAAT,CAAP;AACD;;AAED,SAAO,IAAP;AACD;;AAED,SAAS,eAAT,CAA0B,IAA1B,EAAwC,cAAxC,EAAgE,cAAhE,EAAsF;AACpF,MAAM,uBAAuB,GAAG,IAAI,cAAJ,GAAqB,cAArD;AACA,MAAM,kBAAkB,GAAG,CAAC,IAAI,aAAa,CAAC,IAAD,EAAO,CAAP,EAAU,uBAAV,CAAb,CAAgD,SAAhD,EAAJ,GAAkE,cAAnE,IAAqF,CAAhH;AAEA,SAAO,CAAC,kBAAD,GAAsB,uBAAtB,GAAgD,CAAvD;AACD;;AAED,SAAS,SAAT,CAAoB,IAApB,EAAkC,KAAlC,EAAiD,GAAjD,EAA8D,cAA9D,EAAoF;AAClF,MAAI,SAAS,GAAG,CAAC,CAAD,EAAI,EAAJ,EAAQ,EAAR,EAAY,EAAZ,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,EAAoC,GAApC,EAAyC,GAAzC,EAA8C,GAA9C,EAAmD,GAAnD,EAAwD,KAAxD,CAAhB;;AACA,MAAI,KAAK,GAAG,CAAR,IAAa,UAAU,CAAC,IAAD,CAA3B,EAAmC;AACjC,IAAA,SAAS;AACV;;AAED,SAAO,SAAS,GAAG,GAAnB;AACD;;AAED,SAAS,WAAT,CAAsB,IAAtB,EAAoC,cAApC,EAA4D,cAA5D,EAAkF;AAChF,MAAM,UAAU,GAAG,eAAe,CAAC,IAAD,EAAO,cAAP,EAAuB,cAAvB,CAAlC;AACA,MAAM,cAAc,GAAG,eAAe,CAAC,IAAI,GAAG,CAAR,EAAW,cAAX,EAA2B,cAA3B,CAAtC;AACA,MAAM,UAAU,GAAG,UAAU,CAAC,IAAD,CAAV,GAAmB,GAAnB,GAAyB,GAA5C;AAEA,SAAO,CAAC,UAAU,GAAG,UAAb,GAA0B,cAA3B,IAA6C,CAApD;AACD;;AAEK,SAAU,UAAV,CAAsB,IAAtB,EAAoC,KAApC,EAAmD,GAAnD,EAAgE,cAAhE,EAAwF,oBAAxF,EAAoH;AACxH,MAAM,UAAU,GAAG,eAAe,CAAC,IAAD,EAAO,cAAP,EAAuB,oBAAvB,CAAlC;AACA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAL,CAAU,CAAC,SAAS,CAAC,IAAD,EAAO,KAAP,EAAc,GAAd,EAAmB,cAAnB,CAAT,GAA8C,UAA/C,IAA6D,CAAvE,CAAb;;AAEA,MAAI,IAAI,GAAG,CAAX,EAAc;AACZ,WAAO,IAAI,GAAG,WAAW,CAAC,IAAI,GAAG,CAAR,EAAW,cAAX,EAA2B,oBAA3B,CAAzB;AACD,GAFD,MAEO,IAAI,IAAI,GAAG,WAAW,CAAC,IAAD,EAAO,cAAP,EAAuB,oBAAvB,CAAtB,EAAoE;AACzE,WAAO,IAAI,GAAG,WAAW,CAAC,IAAD,EAAO,cAAP,EAAuB,oBAAvB,CAAzB;AACD,GAFM,MAEA;AACL,WAAO,IAAP;AACD;AACF;;AAEK,SAAU,UAAV,CAAsB,IAAtB,EAAkC;AACtC,SAAS,IAAI,GAAG,CAAP,KAAa,CAAd,IAAqB,IAAI,GAAG,GAAP,KAAe,CAArC,IAA6C,IAAI,GAAG,GAAP,KAAe,CAAnE;AACD", "sourcesContent": ["function createUTCDate (year: number, month = 0, day = 1) {\n  let date\n  if (year < 100 && year >= 0) {\n    date = new Date(Date.UTC(year, month, day))\n    if (isFinite(date.getUTCFullYear())) {\n      date.setUTCFullYear(year)\n    }\n  } else {\n    date = new Date(Date.UTC(year, month, day))\n  }\n\n  return date\n}\n\nfunction firstWeekOffset (year: number, firstDayOfWeek: number, firstDayOfYear: number) {\n  const firstWeekDayInFirstWeek = 7 + firstDayOfWeek - firstDayOfYear\n  const firstWeekDayOfYear = (7 + createUTCDate(year, 0, firstWeekDayInFirstWeek).getUTCDay() - firstDayOfWeek) % 7\n\n  return -firstWeekDayOfYear + firstWeekDayInFirstWeek - 1\n}\n\nfunction dayOfYear (year: number, month: number, day: number, firstDayOfWeek: number) {\n  let dayOfYear = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334][month]\n  if (month > 1 && isLeapYear(year)) {\n    dayOfYear++\n  }\n\n  return dayOfYear + day\n}\n\nfunction weeksInYear (year: number, firstDayOfWeek: number, firstDayOfYear: number) {\n  const weekOffset = firstWeekOffset(year, firstDayOfWeek, firstDayOfYear)\n  const weekOffsetNext = firstWeekOffset(year + 1, firstDayOfWeek, firstDayOfYear)\n  const daysInYear = isLeapYear(year) ? 366 : 365\n\n  return (daysInYear - weekOffset + weekOffsetNext) / 7\n}\n\nexport function weekNumber (year: number, month: number, day: number, firstDayOfWeek: number, localeFirstDayOfYear: number): number {\n  const weekOffset = firstWeekOffset(year, firstDayOfWeek, localeFirstDayOfYear)\n  const week = Math.ceil((dayOfYear(year, month, day, firstDayOfWeek) - weekOffset) / 7)\n\n  if (week < 1) {\n    return week + weeksInYear(year - 1, firstDayOfWeek, localeFirstDayOfYear)\n  } else if (week > weeksInYear(year, firstDayOfWeek, localeFirstDayOfYear)) {\n    return week - weeksInYear(year, firstDayOfWeek, localeFirstDayOfYear)\n  } else {\n    return week\n  }\n}\n\nexport function isLeapYear (year: number): boolean {\n  return ((year % 4 === 0) && (year % 100 !== 0)) || (year % 400 === 0)\n}\n"], "sourceRoot": "", "file": "dateTimeUtils.js"}
<!-- eslint-disable vue/valid-v-slot -->
// table共用组件
<template>
    <div class="common-table-view">
        <!-- 表单 -->
        <v-data-table
            v-model="selected"
            :headers="handlHeaders()"
            :items="desserts"
            :loading="dataLoading"
            :page.sync="pageInfo.page"
            :items-per-page="pageInfo.itemsPerPage"
            hide-default-footer
            :show-select="tableInfo.showSelect"
            :item-key="tableInfo.itemKey"
        >
            <template #item.theSerialNumber="{ index }">
                <span>{{ index + 1 }}</span>
            </template>
            <template #item.tableCellSwitch="{ item }">
                <v-switch :v-model="item[item.tableCellType]" dense class="ma-0 pa-0 mb-0" @click="clickSwitch(item)"></v-switch>
            </template>
            <template #item.tableCellView="{ item }">
                <v-btn text small color="primary" @click="toView(item)">查看</v-btn>
            </template>
            <template #item.tableCellImg="{ item }">
                <v-img :alt="item[item.tableCellType]" height="20" contain width="40" src="https://cdn.vuetifyjs.com/images/parallax/material2.jpg"></v-img>
            </template>
            <template #item.actions="{ item }">
                <v-btn v-for="(i, ind) in btnList" :key="ind" :loading="i.loading" text small color="primary" @click="opearFn(i.code, item)">{{ i.title }}</v-btn>
            </template>
        </v-data-table>
        <!-- 分页 -->
        <div v-if="tableInfo.pagination" class="page">
            <span>共{{ pageInfo.total }}条</span>
            <v-col class="d-flex align-center" cols="0" sm="0">
                <v-select small :items="pageInfo.pageNumberItems" label="10" dense solo @change="selecteLimitPage"></v-select>
            </v-col>
            <v-spacer></v-spacer>
            <v-pagination v-model="pageInfo.page" :total-visible="pageInfo.totalVisible" :length="Math.ceil(pageInfo.total / pageInfo.itemsPerPage)" @input="changePage"></v-pagination>
            <v-spacer></v-spacer>
            <v-col class="d-flex align-center mr-2" cols="auto">
                <!-- 分页跳转 -->
                <v-btn text small color="primary" @click="jumpPage">跳转</v-btn>
                <span>到</span>
                <input id="pageInput" v-model.number="pageInfo.pageInput" placeholder="页码" type="text" />
                <span>页</span>
            </v-col>
        </div>
    </div>
</template>

<script>
const tableDefaultInfo = {
        showSelect: true, // 默认显示选择框
        itemKey: '', // 表格的item-key
        pagination: true // 是否展示分页
    },
    pageDefaultInfo = {
        totalVisible: '5',
        page: 1,
        pageCount: 1, // 当前页
        itemsPerPage: 10, // 每页条数
        pageInput: 1, // 跳转哪一页
        pageNumberItems: ['10', '20', '50', '100', '500'],
        total: 0 // 总条数
    };
export default {
    name: 'Pagination',
    props: {
        // 列表数据加载
        getDataList: {
            type: Function,
            default: () => {}
        },
        //表头
        headers: {
            type: Array,
            default: () => []
        },
        // 表格相关信息,页面必传，因为要用到itemKey
        tableObj: {
            type: Object,
            required: true,
            default: () => {}
        },
        // 分页相关信息
        pageObj: {
            type: Object,
            default: () => {}
        },
        // 操作列
        btnList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            dataLoading: false,
            formInputs: ['tableCellSwitch', 'tableCellView', 'tableCellImg'],
            desserts: [], // 表格数据
            selected: [], // 勾选数据
            pageInfo: {}, // 分页相关数据
            tableInfo: {}, // 表格相关数据
            dataSource: {} // 服务端返回的数据源，方便父组件调用
        };
    },
    created() {
        this.tableInfo = this.getInfoObj(this.tableObj, tableDefaultInfo);
        this.pageInfo = this.getInfoObj(this.pageObj, pageDefaultInfo);
        this.dataInit();
    },
    methods: {
        // 数据初始化
        async dataInit() {
            const parmas = {
                pageIndex: this.pageInfo.pageCount,
                pageSize: this.pageInfo.itemsPerPage
            };
            this.dataLoading = true;
            const res = await this.getDataList(parmas);
            this.dataLoading = false;
            const { response } = res || {};
            this.dataSource = response;
            const { data, dataCount } = response || {};
            this.desserts = this.handlDesserts(data || []);
            this.pageInfo.total = dataCount || 0;
        },
        // 重组分页和表格默认展示的值
        getInfoObj(doo, oo) {
            let obj = JSON.parse(JSON.stringify(oo));
            for (const key in doo) {
                if (Object.hasOwnProperty.call(doo, key)) {
                    obj[key] = doo[key];
                }
            }
            return obj;
        },
        clickSwitch(e) {
            console.log(e.tableCellType, e[e.tableCellType]);
        },
        sortBy() {
            console.log(1234);
        },
        // 表头处理
        handlHeaders() {
            console.log(this.headers);
            const arr = [];
            this.headers.forEach(i => {
                const obj = { ...i };
                const { value, tableCellType, sortable } = i;
                if (this.formInputs.indexOf(tableCellType) > -1) {
                    i.value = tableCellType;
                    i.tableCellType = value;
                }
                arr.push({ ...obj, sortable: typeof sortable === 'undefined' ? false : sortable });
            });
            return arr;
        },
        // 表格的数据处理
        handlDesserts(data) {
            data.forEach(e => {
                this.headers.forEach(i => {
                    const { tableCellType, value } = i;
                    if (this.formInputs.indexOf(value) > -1) {
                        e.tableCellType = tableCellType;
                    }
                });
            });
            return data;
        },
        //操作栏事件操作
        opearFn(code, item) {
            this.$emit('toFather', code, item);
        },
        // 选择分页码
        selecteLimitPage(val) {
            this.pageInfo.pageCount = 1;
            this.pageInfo.page = 1;
            this.pageInfo.pageInput = 1;
            this.pageInfo.itemsPerPage = val / 1;
            this.dataInit();
        },
        // 点击分页
        changePage(val) {
            this.pageInfo.pageCount = val;
            this.dataInit();
        },
        // 跳转页码
        jumpPage() {
            const pageInput = this.pageInfo.pageInput;
            if (typeof pageInput === 'number') {
                if (!(pageInput > 0)) {
                    this.$store.commit('SHOW_SNACKBAR', { text: '跳转页码必须大于0,请重新输入', color: 'error' });
                    return;
                }
                if (!/(^[1-9]\d*$)/.test(pageInput)) {
                    this.$store.commit('SHOW_SNACKBAR', { text: '跳转页码必须是正整数', color: 'error' });
                    return;
                }
                const ma = Math.ceil(this.pageInfo.total / this.pageInfo.itemsPerPage);
                if (pageInput > ma) {
                    this.$store.commit('SHOW_SNACKBAR', { text: `跳转页码最大是${ma},请重新输入`, color: 'error' });
                    return;
                }
                this.pageInfo.pageCount = pageInput;
                this.pageInfo.page = pageInput;
                this.dataInit();
            } else {
                this.$store.commit('SHOW_SNACKBAR', { text: '页码只能是正整数！', color: 'error' });
            }
        }
    }
};
</script>
<style lang="scss">
.common-table-view {
    .input__slot {
        margin-bottom: 0;
    }
    .v-messages {
        min-height: 0;
    }
}
</style>
<style lang="scss" scoped>
.common-table-view {
    min-width: 100%;
}
.page {
    display: flex;
    align-items: center;
    margin-left: 12px;
}
.v-text-field {
    width: 60px;
    margin-top: 20px;
    margin-right: 10px;
}
.v-pagination {
    margin-left: 20px;
}
span {
    margin-right: 4px;
    font-size: 14px;
}
#pageInput {
    max-width: 48px;
    border: 1px solid #bdbdbd;
    text-align: center;
    min-height: 34px;
    outline: none;
    padding: 0 4px;
    border-radius: 5px;
}
::v-deep .v-select__selections {
    position: absolute;
}
</style>

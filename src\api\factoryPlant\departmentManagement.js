import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url

//获取组织模型Tree数据
export function DepartmentGetTree(data) {
    return request({
        url: baseURL + '/api/Department/GetTree',
        method: 'post',
        data
    });
}
// 点击Tree获取详情table
export function DepartmentGetPageList(data) {
    return request({
        url: baseURL + '/api/Department/GetPageList',
        method: 'post',
        data
    });
}
// 新增部门信息&修改
export function DepartmentSaveForm(data) {
    return request({
        url: baseURL + '/api/Department/SaveForm',
        method: 'post',
        data
    });
}
// 删除列表
export function DepartmentDelete(data) {
    return request({
        url: baseURL + '/api/Department/Delete',
        method: 'post',
        data
    });
}

// 属性扩展
export function DepartmentGetDepartmentPropertyList(data) {
    return request({
        url: baseURL + '/api/Department/GetDepartmentPropertyList',
        method: 'post',
        params: data
    });
}
// 删除属性扩展
export function DeleteDepartmentProperty(data) {
    return request({
        url: baseURL + '/api/Department/DeleteDepartmentProperty',
        method: 'post',
        data
    });
}
// 列表查询
export function DepartmentGetDepartmentClassList(data) {
    return request({
        url: baseURL + '/api/Department/GetDepartmentClassList',
        method: 'post',
        params: data
    });
}
// 新增& 修改
export function DepartmentSaveDepartmentProperty(data) {
    return request({
        url: baseURL + '/api/Department/SaveDepartmentProperty',
        method: 'post',
        data
    });
}
// 删除
export function EquipmentAttrDelete(data) {
    return request({
        url: baseURL + '/api/EquipmentPropertyValue/Delete',
        method: 'post',
        data
    });
}

// 保存组织建模套件
export function DepartmentSaveDepartmentClassMapping(data) {
    return request({
        url: baseURL + '/api/Department/SaveDepartmentClassMapping',
        method: 'post',
        data
    });
}

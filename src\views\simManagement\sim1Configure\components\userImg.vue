<template>
    <v-dialog v-model="visible" width="780" style="backgroundcolor: #aaa">
        <v-card class="pa-2">
            <v-card-text>
                <v-row class="pa-4">
                    <v-col cols="12" sm="6" md="6" style="height: 300px">
                        <vue-cropper
                            ref="cropper"
                            :img="options.img"
                            :info="true"
                            :auto-crop="options.autoCrop"
                            :auto-crop-width="options.autoCropWidth"
                            :auto-crop-height="options.autoCropHeight"
                            :fixed-box="options.fixedBox"
                            @realTime="realTime"
                        ></vue-cropper>
                    </v-col>
                    <v-col cols="12" sm="6" md="6" style="height: 300px" class="d-flex">
                        <div :style="previews.div" class="avatar-upload-preview ma-auto">
                            <img :src="previews.url" :style="previews.img" />
                        </div>
                    </v-col>
                </v-row>
                <v-row class="mt-5">
                    <v-col cols="12" sm="4" md="4">
                        <v-btn depressed color="primary" style="position: relative">
                            <v-file-input
                                @change="beforeUpload"
                                accept="image/*"
                                style="position: absolute; z-index: 999; width: 200px; height: 66px; flex: none; background: none; opacity: 0"
                            ></v-file-input>
                            <v-icon class="pr-1">mdi-cloud-upload</v-icon>
                            {{ $t('SHIFT_RYSJ_EYZSJ._XZTX') }}
                        </v-btn>
                    </v-col>
                    <v-col cols="10" sm="8" md="8">
                        <v-btn @click="changeScale(1)">{{ $t('SHIFT_RYSJ_EYZSJ._FD') }}</v-btn>
                        <v-btn class="ml-2" @click="changeScale(-1)">{{ $t('SHIFT_RYSJ_EYZSJ._SX') }}</v-btn>
                        <v-btn class="ml-2" @click="rotateLeft">{{ $t('SHIFT_RYSJ_EYZSJ._YXZ') }}</v-btn>
                        <v-btn class="ml-2" @click="rotateRight">{{ $t('SHIFT_RYSJ_EYZSJ._ZXZ') }}</v-btn>
                    </v-col>
                </v-row>
                <v-row class="d-flex align-end flex-column mt-6">
                    <span>
                        <v-btn color="primary" @click="finish('blob')">{{ $t('GLOBAL._BC') }}</v-btn>
                        <v-btn class="ml-2" color="" @click="close">{{ $t('GLOBAL._QX') }}</v-btn>
                    </span>
                </v-row>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>
<script>
export default {
    name: 'AvatarModal',
    props: {
        tableItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            visible: false,
            id: null,
            options: {
                img: '', // 需要剪裁的图片
                autoCrop: true, // 是否默认生成截图框
                autoCropWidth: 140, // 默认生成截图框的宽度
                autoCropHeight: 140, // 默认生成截图框的长度
                fixedBox: true // 是否固定截图框的大小 不允许改变
            },
            previews: {} // 裁剪之后的结果
        };
    },
    methods: {
        // 头像回显
        urlImgLoad(a) {
            this.options.img = a;
        },
        edit(id) {
            this.visible = true;
            this.id = id;
        },
        close() {
            this.id = null;
            this.options.img = '';
            this.visible = false;
        },
        cancelHandel() {
            this.close();
        },
        // 图片放大/缩小
        changeScale(num) {
            num = num || 1;
            this.$refs.cropper.changeScale(num);
        },
        // 左旋转
        rotateLeft() {
            this.$refs.cropper.rotateLeft();
        },
        // 右旋转
        rotateRight() {
            this.$refs.cropper.rotateRight();
        },
        beforeUpload(file) {
            const reader = new FileReader();
            // 转化为base64
            reader.readAsDataURL(file);
            reader.onload = () => {
                // 获取到需要剪裁的图片 展示到剪裁框中
                this.options.img = reader.result;
            };
            return false;
        },
        // 上传图片（点击保存按钮）
        finish(type) {
            this.$refs.cropper.getCropData(data => {
                this.visible = false;
                // data为base64图片，供接口使用
                this.$emit('getImgData', data);
            });
        },
        // 裁剪之后的数据
        realTime(data) {
            this.previews = data;
        }
    }
};
</script>
<style lang="scss" scoped>
// ::v-deep v-dialog {
//     background: red !important;
// }
.avatar-upload-preview {
    overflow: hidden !important;
    border-radius: 50%;
}
</style>

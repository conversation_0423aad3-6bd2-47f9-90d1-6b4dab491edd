<template>
  <div class="table-content">
    <SearchForm
      class="mt-1"
      :show-from="isShowSearch"
      :searchinput="searchInputs"
      @searchForm="searchForm"
    />
    <el-form v-if="isShowSearch">
      <el-form-item label="状态">
        <el-select
          v-model="searchFormObj.State"
          multiple
          placeholder="请选择"
          style="width: 72.5%;"
        >
          <el-option
            v-for="item in statusList"
            :key="item.value"
            :label="item.ItemName"
            :value="item.ItemValue"
          >
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <!-- <Header :workData="workData"></Header> -->
    <!-- <div class="form-btn-list">
            <v-btn color="primary" v-has="'JYXM_DRMBXZ'">{{ $t('DFM_JYXM._DRMBXZ') }}</v-btn>
        </div> -->
    <vxe-toolbar ref="xToolbar">
      <template #buttons>
        <!-- <vxe-button
          size="mini"
          icon="vxe-icon-search"
          :content="isShowSearch ? '隐藏' : '显示'"
          @click="isShowSearch = !isShowSearch"
        ></vxe-button> -->
        <v-btn
          icon
          class="float-left mx-4"
          @click="isShowSearch = !isShowSearch"
        >
          <v-icon>{{ 'mdi-table-search' }}</v-icon>
          {{ $t('GLOBAL._SSL') }}
        </v-btn>
        <v-btn
          style="margin-right:10px;margin-left:10px;"
          color="primary"
          @click="add()"
        >{{ $t('GLOBAL._XZ') }}</v-btn>
        <v-btn
          style="margin-right:10px;"
          color="primary"
          @click="modify()"
        >{{ $t('GLOBAL._XG') }}</v-btn>
        <v-btn
          style="margin-right:10px;"
          color="primary"
          @click="upgrade()"
        >{{ $t('GLOBAL._SJ') }}</v-btn>
        <v-btn
          style="margin-right:10px;"
          color="primary"
          @click="close()"
        >{{ $t('GLOBAL._GB') }}</v-btn>
        <v-btn
          style="margin-right:10px;"
          color="primary"
          @click="reject()"
        >{{ $t('GLOBAL._BH') }}</v-btn>
        <v-btn
          style="margin-right:10px;"
          color="primary"
          @click="cancel()"
        >{{ $t('GLOBAL._QX') }}</v-btn>
        <v-btn
          color="primary"
          @click="device()"
        >导出</v-btn>
        <!-- <v-btn
          style="margin-right:10px;"
          color="primary"
          @click="reject()"
        >{{ $t('GLOBAL._BH') }}</v-btn>
        <v-btn
          style="margin-right:10px;"
          color="primary"
          @click="cancel()"
        >{{ $t('GLOBAL._QX') }}</v-btn>
        <v-btn
          style="margin-right:10px;"
          color="primary"
          @click="reply()"
        >{{ $t('GLOBAL._ZDXD') }}</v-btn>
        <v-btn
          color="primary"
          @click="refuse()"
        >{{ $t('GLOBAL._JJ') }}</v-btn> -->

        <!-- <vxe-button
          size="mini"
          content="接受"
          @click="accept()"
        ></vxe-button> -->
        <!-- <vxe-button
          size="mini"
          content="申请验收"
          @click="applyFor()"
        ></vxe-button> -->
        <!-- <vxe-button
          size="mini"
          content="回复"
          @click="reply()"
        ></vxe-button> -->
        <!-- <vxe-button
          size="mini"
          content="关闭"
          @click="close()"
        ></vxe-button>
        <vxe-button
          size="mini"
          content="驳回"
          @click="reject()"
        ></vxe-button> -->
        <!-- <vxe-button
          size="mini"
          content="取消"
          @click="accept()"
        ></vxe-button>
        <vxe-button
          size="mini"
          content="制定行动"
          @click="accept()"
        ></vxe-button>
        <vxe-button
          size="mini"
          content="拒绝"
          @click="refuse()"
        ></vxe-button> -->
        <!-- <vxe-button size="mini" content="驳回" @click="reject()"></vxe-button> -->
        <!-- <vxe-button size="mini" content="合理化建议" @click="suggest()"></vxe-button> -->
        <!-- <vxe-button size="mini" status="primary" :content="$t('DFM_JYXM._DRMBXZ')"></vxe-button> -->
        <!-- <v-btn color="primary" v-has="'JYXM_DRMBXZ'">{{ $t('DFM_JYXM._DRMBXZ') }}</v-btn> -->
      </template>
    </vxe-toolbar>
    <!-- 表格 -->
    <!-- <a-table :dataSource="dataSource" :columns="columns" :pagination="pagination" bordered/> -->
    <div class="table-box">
      <vxe-table
        ref="vxeTable"
        :data="tableList"
        :show-footer="false"
        :loading="false"
        class="mytable-scrollbar"
        :row-config="{ isHover: true }"
        :column-config="{ resizable: true }"
        v-bind="$attrs"
        height="720"
        v-on="$listeners"
        border
        size="mini"
        @checkbox-change="handleCheckboxChange"
        style="font-size: 20px;"
      >
        <vxe-table-column
          fixed="left"
          type="checkbox"
          width="60"
        ></vxe-table-column>
        <!--  show-overflow="title" -->
        <vxe-column
          v-for="(column, index) in columns"
          :type="column.type"
          :width="column.width"
          :fixed="column.fixed"
          :key="index"
          :sortable="column.sortable"
          :field="column.value"
          :title="column.text"
        >
          <!-- <template #default="{ row }">
            <span v-if="column.field == 'PresentUserId'">
              {{ getStaffName(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'PresentDepartmentId'">
              {{ getDepartmentName(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'PresentProdProcessId'">
              {{ getPresentProdProcessName(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'ReceiveDepartmentId'">
              {{ getDepartmentName(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'ReceiveUserId'">
              {{ getStaffName(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'TargetEDRCode'">
              {{ getTargetEDRName(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'CurrentLevel'">
              {{ getCurrentLevel(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'Severity'">
              {{ getSeverity(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'Closed'">
              {{ getCloseStatus(row[column.field]) }}
            </span>
            <span v-else>{{ row[column.field] }}</span>
          </template> -->
        </vxe-column>
      </vxe-table>
      <vxe-pager
        :current-page.sync="pageOptions.pageIndex"
        :page-size.sync="pageOptions.pageSize"
        :page-sizes="pageOptions.pageSizeitems"
        :total="pageOptions.total"
        @page-change="getListFn"
        size="medium"
      />
      <!-- <Tables
        :page-options="pageOptions"
        :tableHeight="isShowSearch ? 'calc(100vh - 232px)' : 'calc(100vh - 174px)'"
        :loading="loading"
        :headers="headers"
        :desserts="columns"
        @selectePages="selectePages"
        @itemSelected="SelectedItems"
        @toggleSelectAll="SelectedItems"
      >
      </Tables> -->
    </div>

    <!--新增 -->
    <v-dialog
      v-if="showInfoDialog"
      v-model="showInfoDialog"
      scrollable
      persistent
      width="55%"
    >
      <InfoDialog
        v-if="showInfoDialog"
        :key="InfoDialogkey"
        :curTeamTreeObj="curTeamTreeObj"
        :editItemObj="editItemObj"
        :searchFormObj="searchFormObj"
        @getdata="getListFn"
        @closePopup="closePopup"
      >
      </InfoDialog>
    </v-dialog>
    <!-- 回复 -->
    <!-- <v-dialog
      v-model="showReplyDialog"
      scrollable
      persistent
      width="55%"
    >
      <ReplyDialog
        v-if="showReplyDialog"
        :editItemObj="editItemObj"
        @getdata="getListFn"
        @closePopup="closeReplyPopup"
      >
      </ReplyDialog>
    </v-dialog> -->
    <!-- 升级 -->
    <v-dialog
      v-if="showupgradeDialog"
      v-model="showupgradeDialog"
      scrollable
      persistent
      width="30%"
    >
      <upgradeDialog
        v-if="showupgradeDialog"
        :id="ids"
        :simLevel1="simLevels"
        @closeup="closeupshow"
      >
      </upgradeDialog>
    </v-dialog>
  </div>
</template>

<script>
import { deriveExcel1 } from '@/api/simConfig/simconfignew.js';
import { mapGetters } from 'vuex';
import { getPageList, Accept, Refuse, ApplyFor, getPersonalWork, getCurrentDepartStaff, LevelUp, Cancel, Reject, getProblemSelectList } from "./service"
import { Close } from "@/views/simManagement/sim1/service.js"
import dayjs from 'dayjs';
import InfoDialog from "@/views/simManagement/sim1/components/infoDialog.vue"
import { sim4TableList, sim4TableLists } from '@/columns/factoryPlant/tableHeaders';
import upgradeDialog from './upgradeDialog'

export default {
  name: "SIM3",
  components: {
    InfoDialog,
    upgradeDialog
    // Header: () => import('./components/header.vue'),
  },
  props: {},
  data() {
    return {
      matchedNames: [],
      EquipmentProductLineTree1: [],
      ids: '',
      simLevels: '',
      showupgradeDialog: false,
      selectedOption: '',
      productLineList: [],
      loading: false,
      tableItem: {}, // 选择操作数据
      selectList: [], //批量选中
      pageOptions: {
        total: 0,
        pageIndex: 1,
        pageSize: 20,
        pageSizeitems: [50, 150, 300, 500]
      },
      paramsList: {
        key: '',
        pageCount: 1,
        pageSize: 20
      },
      departStaff: [],
      statusList: [],
      workData: {},
      isShowSearch: false,
      searchFormObj: {
        ReceiveDepartmentId: "",//责任部门
        ReceiveUserId: "",//责任人
        StartTime: '',
        EndTime: '',
        simLevel: 'SIM3',
        problemType: '',
        PresentDepartmentId: '',
        State: ['0', '1', '4', '3'],
      },
      tableList: [],
      columns: [
        { text: '发起日期', value: 'PresentDate', width: 130 },
        { text: '提出人', value: 'PresentUser', width: 160 },
        { text: '问题类型', value: 'TargetEDRCode', width: 120 },
        { text: '问题来源', value: 'ProblemSources', width: 100 },
        { text: '当前等级', value: 'CurrentLevel', width: 100 },
        { text: '当前状态', value: 'States', width: 100 },
        { text: '问题描述', value: 'ProblemDesc', width: 300 },
        { text: '原因分析', value: 'REASON', width: 300 },
        { text: '临时对策', value: 'ActualEDR', width: 200 },
        { text: '根本对策', value: 'REMAKR', width: 200 },
        { text: '要求完成时间', value: 'PlanFinishDate', width: 150 },
        { text: '责任人', value: 'ReceiveUser', width: 160 },
        { text: '预估完成时间', value: 'EstimateFinishDate', width: 210 },
        { text: '实际完成时间', value: 'ActualFinishDate', width: 150 },
        { text: '提出部门', value: 'PresentDepartment', width: 180 },
        { text: '责任部门', value: 'ReceiveDepartment', width: 180 },
        { text: '问题编码', value: 'ProblemNumber', width: 120 },
      ],
      showReplyDialog: false,
      showInfoDialog: false,
      editItemObj: {},
      InfoDialogkey: 1,
    };
  },
  computed: {
    headers() {
      let headList = [];
      headList = sim4TableList.map(item => {
        if (item.value == 'data-table-expand') {
          item.text = ''; // 展开
        } else {
          item.text = this.$t(`$vuetify.dataTable.SIM4_TABLE.${item.value}`); //  表表头对象名称
        }
        return item;
      });
      return headList;
    },
    //当前班组Parent数据
    curTeamTreeObj() {
      let info = this.$store.getters.getFactoryInfo
      return {
        TeamCode: info.find(item => item.Level == "Team")?.EquipmentId || '',
        ProductionLineCode: info.find(item => item.Level == "ProductLine")?.EquipmentId || '',
        FactoryCode: info.find(item => item.Level == "Area")?.EquipmentId || ''
      }
    },
    ...mapGetters(['getUserinfolist']),
    StaffList() {
      return this.$store.state.sim.StaffList
    },
    eventLevelList() {
      return this.$store.state.sim.eventLevelList
    },
    DepartmentList() {
      return this.$store.state.sim.DepartmentList
    },
    //工序
    UnitList() {
      return this.$store.state.sim.UnitList
    },
    //部门
    LineList() {
      return this.$store.state.sim.LineList
    },
    SegmentList() {
      return this.$store.state.sim.SegmentList
    },
    simLvList() {
      return this.$store.state.sim.simLvList
    },
    closeStatusList() {
      return this.$store.state.sim.closeStatusList
    },
    // 问题等级
    TargetEDRList() {
      return this.$store.state.sim.TargetEDRList
    },
    searchInputs() {
      return [
        {
          value: null, //默认值
          icon: 'mdi-account-check',
          label: '提起部门',
          placeholder: '',
          type: 'tree',
          // selectData: this.$changeSelectItems(this.lineList, 'EquipmentCode', 'EquipmentName'),
          selectData: this.EquipmentProductLineTree1,
          normalizer(node) {
            return {
              id: node.id,
              label: node.name,
              children: node.children
            };
          },
          key: 'PresentDepartmentId'
        },
        {
          value: '',
          icon: 'mdi-account-check',
          label: '责任部门',
          placeholder: '',
          type: 'select',
          selectData: this.$changeSelectItems(this.productLineList, 'Key', 'Value'),
          key: 'ReceiveDepartmentId'
        },
        {
          value: '',
          icon: 'mdi-account-check',
          label: '责任人',
          // label: `${this.$t('DFM_JYXM.ProductionCode')}/${this.$t('DFM_JYXM.TestItem')}`,
          placeholder: '',
          type: 'select',
          selectData: this.$changeSelectItems(this.departStaff, 'Key', 'Value'),
          key: 'ReceiveUserId'
        },
        // {
        //   value: '',
        //   icon: 'mdi-account-check',
        //   label: '状态',
        //   type: 'combobox',
        //   placeholder: '',
        //   selectData: this.$changeSelectItems(this.statusList, 'ItemValue', 'ItemName'),
        //   key: 'State',
        // },
        {
          value: '',
          type: 'date',
          icon: 'mdi-account-check',
          // label: this.$t('DFM_SJDR._KSRQ'),
          label: '开始日期',
          placeholder: '',
          key: 'StartTime'
        },
        {
          value: '',
          type: 'date',
          icon: 'mdi-account-check',
          // label: this.$t('DFM_SJDR._KSRQ'),
          label: '结束日期',
          placeholder: '',
          key: 'EndTime'
        },
        {
          value: '',
          icon: 'mdi-account-check',
          label: '问题类型',
          placeholder: '',
          type: 'select',
          selectData: this.$changeSelectItems(this.TargetEDRList, 'ItemValue', 'ItemName'),
          key: 'problemType'
        },
      ]
    }
  },
  watch: {
    curTeamTreeObj: {
      handler(nv) {
        this.$store.dispatch('getLineList', { FactoryCode: nv?.FactoryCode });
      },
      deep: true,
      immediate: true
    }
  },
  async created() {
    this.$store.dispatch('getEquipmentTeamTree', "Team");
    this.$store.dispatch('getSimLvlist');
    this.$store.dispatch('getEventLevelList');
    this.$store.dispatch('getCloseStatuslist');
    // this.$store.dispatch('getCompanyList');
    this.$store.dispatch('getCompanyTree');
    this.$store.dispatch('getTargetEDRList');

    this.$store.dispatch('getDepartmentList')
    this.$store.dispatch('getTeamList')
    this.$store.dispatch('getUnitList');
    this.$store.dispatch('getSegmentList');
    this.$store.dispatch('getStaffList');
    this.$store.dispatch('getClassfyCodeList');
    this.getDepartStaff()
    this.statusList = await this.$getDataDictionary('ProblemStatus')

    this.searchFormObj.ReceiveDepartmentId = '' // this.curTeamTreeObj.ProductionLineCode
    this.searchFormObj.ReceiveUserId = '' // this.getUserinfolist[0].LoginName
    this.searchFormObj.State.map(item => {
      this.statusList.map(el => {
        if (item == el.ItemValue) {
          this.matchedNames.push(el.ItemName);
        }
      })
    });
    this.getListFn()
    this.getWorkData()
    this.getSelectList();
  },
  activated() { },
  mounted() {
    this.EquipmentProductLineTree1 = []
    JSON.parse(localStorage.getItem('jurisdiction2')).map(item => {
      item.children.map(child => {
        if (localStorage.getItem('jurisdiction1').split(',').includes(child.id)) {
          this.EquipmentProductLineTree1.push(child);
        }
      });
    });
  },
  methods: {
    async device() {
      var StateList = []
      if (this.searchFormObj.State) {
        StateList = this.searchFormObj.State.map(el => {
          return el.ItemName || []
        })
      }
      this.showInfoDialog = false
      let params1 = {
        "ProblemTag": '',
        "ProblemSource": 'SIM3',
        "ReceiveDepartmentId": this.searchFormObj.ReceiveDepartmentId,
        // "ReceiveUserId": this.searchFormObj.ReceiveUserId,
        "ReceiveUserId": this.searchFormObj.ReceiveUserId,
        // "ProcessingUserId": this.getUserinfolist[0].LoginName,
        "StartTime": this.searchFormObj.StartTime,
        "EndTime": this.searchFormObj.EndTime,
        "StateList": StateList.length <= 0 ? ['待处理', '处理中', '待验收', '已回复'] : StateList,
        "pageIndex": this.pageOptions.page,
        "pageSize": this.pageOptions.pageSize,
        "EdrCode": this.searchFormObj.problemType,
        "responseType": 'blob'
      }
      let resp = await deriveExcel1(params1);
      let blob = new Blob([resp], {
        type: "application/octet-stream",
      });
      var link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.download = "SIM3追踪事项.xls";
      link.click();
      //释放内存
      window.URL.revokeObjectURL(link.href);
    },
    closeupshow() {
      this.showupgradeDialog = false
      this.getListFn()
    },
    handleCheckboxChange({ checked, records }) {
      console.log(checked, records);
      this.tableItem = {};
      this.selectList = records;

    },
    async getSelectList() {
      let resp = await getProblemSelectList()
      var dictList = resp.response
      this.departStaff = dictList['user']
      this.productLineList = dictList['depart']
    },
    // 新增
    add() {
      this.editItemObj = {}
      this.InfoDialogkey++
      this.showInfoDialog = true
    },
    // 修改
    modify() {
      console.log(this.selectList, 'this.selectList');

      if (this.selectList.length <= 0) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'red' });
        return false;
      }
      if (this.selectList.length > 1) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('只能选择一条数据'), color: 'red' });
        return false;
      }
      if (this.selectList[0].CreateUserId == 'FH-SAP') {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('SAP设备问题无法操作'), color: 'red' });
        return false;
      }
      this.editItemObj = this.selectList[0]
      this.InfoDialogkey++
      this.showInfoDialog = true
    },
    // 升级
    upgrade() {
      let curAuthID = this.getUserinfolist[0].LoginName
      if (this.selectList.length <= 0) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'red' });
        return false;
      }
      if (this.selectList.length > 1) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('只能选择一条数据'), color: 'red' });
        return false;
      }
      if (this.selectList[0].CreateUserId == 'FH-SAP') {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('SAP设备问题无法操作'), color: 'red' });
        return false;
      }
      // if (this.selectList[0].ProblemSources == 'SIM3') {
      //   this.$store.commit('SHOW_SNACKBAR', { text: this.$t('当前层级无法升级'), color: 'red' });
      //   return false;
      // }
      this.ids = this.selectList[0].ID
      if (this.selectList[0].CurrentLevel == 'SIM1') {
        this.simLevels = 'SIM2'
      }
      if (this.selectList[0].CurrentLevel == 'SIM2') {
        this.simLevels = 'SIM3'
      }
      if (this.selectList[0].CurrentLevel == 'SIM3') {
        this.simLevels = 'SIM3'
      }
      this.showupgradeDialog = true
      // this.$confirms({
      //   // message: this.$t('GLOBAL._COMFIRM'),
      //   message: '请确认是否升级',
      //   confirmText: this.$t('GLOBAL._QD'),
      //   cancelText: this.$t('GLOBAL._QX')
      //   // message: this.$t('GLOBAL._COMFIRM'),

      // }).then(async () => {
      //   var simLevel
      //   if (this.selectList[0].CurrentLevel == 'SIM1') {
      //     simLevel = 'SIM2'
      //   }
      //   if (this.selectList[0].CurrentLevel == 'SIM2') {
      //     simLevel = 'SIM3'
      //   } else {
      //     simLevel = ''
      //   }
      //   let params = {
      //     "id": this.selectList[0].ID,
      //     "simLevel": simLevel
      //   }
      //   let res = await LevelUp(params)
      //   this.$store.commit('SHOW_SNACKBAR', { text: '提交成功', color: 'green' });
      //   this.getListFn()

      // })
    },
    // 关闭
    close() {
      if (this.selectList.length <= 0) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'red' });
        return false;
      }
      if (this.selectList.length > 1) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('只能选择一条数据'), color: 'red' });
        return false;
      }
      if (this.selectList[0].CreateUserId == 'FH-SAP') {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('SAP设备问题无法操作'), color: 'red' });
        return false;
      }
      let curAuthID = this.getUserinfolist[0].LoginName
      // let selectRecords = this.$refs.vxeTable.getCheckboxRecords()
      if (this.selectList.length <= 0) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'red' });
        return false;
      } else if (this.selectList[0].CreateUserId !== curAuthID) {
        this.$store.commit('SHOW_SNACKBAR', { text: "只有提出人可以关闭", color: 'red' });
        return false;
      } else if (this.selectList[0].States !== '待验收') {
        this.$store.commit('SHOW_SNACKBAR', { text: "只有待验收状态才可关闭", color: 'red' });
        return false;
      }
      this.$confirms({
        message: this.$t('GLOBAL._COMFIRM_GB'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        let params = {
          id: this.selectList[0].ID
        }
        let res = await Close(params)
        this.$store.commit('SHOW_SNACKBAR', { text: '提交成功', color: 'success' });
        this.getListFn()
      })
    },
    // 驳回
    reject() {
      let curAuthID = this.getUserinfolist[0].LoginName
      if (this.selectList.length <= 0) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'red' });
        return false;
      }
      if (this.selectList.length > 1) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('只能选择一条数据'), color: 'red' });
        return false;
      }
      if (this.selectList[0].CreateUserId == 'FH-SAP') {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('SAP设备问题无法操作'), color: 'red' });
        return false;
      }
      this.$confirms({
        // message: this.$t('GLOBAL._COMFIRM'),
        message: '请确认是否驳回',
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
        // message: this.$t('GLOBAL._COMFIRM'),

      }).then(async () => {
        let params = {
          problemId: this.selectList[0].ID,
          userId: curAuthID
        }
        let res = await Reject(params)
        this.$store.commit('SHOW_SNACKBAR', { text: '提交成功', color: 'success' });
        this.getListFn()

      })
    },
    // 取消
    cancel() {
      let curAuthID = this.getUserinfolist[0].LoginName
      if (this.selectList.length <= 0) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'red' });
        return false;
      }
      if (this.selectList.length > 1) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('只能选择一条数据'), color: 'red' });
        return false;
      }
      if (this.selectList[0].CreateUserId == 'FH-SAP') {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('SAP设备问题无法操作'), color: 'red' });
        return false;
      }
      this.$confirms({
        // message: this.$t('GLOBAL._COMFIRM'),
        message: '请确认是否取消',
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
        // message: this.$t('GLOBAL._COMFIRM'),

      }).then(async () => {
        let params = {
          problemId: this.selectList[0].ID,
          userId: curAuthID
        }
        let res = await Cancel(params)
        this.$store.commit('SHOW_SNACKBAR', { text: '提交成功', color: 'success' });
        this.getListFn()

      })
    },
    // 制定行动
    reply() {
      let curAuthID = this.getUserinfolist[0].LoginName
      if (this.selectList.length <= 0) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'red' });
        return false;
      } else if (this.selectList.length > 1) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('只能选择一条数据'), color: 'red' });
        return false;
      } else if (this.selectList[0].ReceiveUserId !== curAuthID) {
        this.$store.commit('SHOW_SNACKBAR', { text: "只有责任人可以回复", color: 'red' });
        return false;
      }
      this.editItemObj = this.selectList[0]
      this.showReplyDialog = true
    },
    //拒绝
    refuse() {
      let curAuthID = this.getUserinfolist[0].LoginName
      if (this.selectList.length <= 0) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'red' });
        return false;
      } else if (this.selectList.length > 1) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('只能选择一条数据'), color: 'red' });
        return false;
      } else if (this.selectList[0].ReceiveUserId !== curAuthID) {
        this.$store.commit('SHOW_SNACKBAR', { text: "只有责任人可以拒绝", color: 'red' });
        return false;
      }
      // this.editItemObj = selectRecords[0]
      // this.showInfoDialog = true
      this.$confirms({
        // message: this.$t('GLOBAL._COMFIRM'),
        message: '请确认是否拒绝',
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
        // message: this.$t('GLOBAL._COMFIRM'),

      }).then(async () => {
        let params = {
          problemId: this.selectList[0].ID,
          userId: curAuthID
        }
        let res = await Refuse(params)
        this.$store.commit('SHOW_SNACKBAR', { text: '提交成功', color: 'success' });
        this.getListFn()

      })
    },
    // 选择数据
    SelectedItems(item) {
      this.tableItem = {};
      this.selectList = item;
    },
    selectePages(v) {
      console.log(v);

      this.pageOptions.pageIndex = v.pageCount;
      this.pageOptions.pageSize = v.pageSize;
      this.getListFn()
    },







    async getDepartStaff() {
      let resp = await getCurrentDepartStaff()
      this.departStaff = resp.response
    },
    async getWorkData() {
      let resp = await getPersonalWork({ ReceiveUserId: this.searchFormObj.ReceiveUserId, Date: dayjs().format('YYYY-MM-DD') })
      if (resp.response) {
        this.workData = resp.response
      } else {
        this.workData = {}
      }
    },
    //根据部门 code匹配名字
    getDepartmentName(code) {
      let target = this.LineList.find(item => item.DepartMentCode == code)
      if (target) {
        return target.DepartMentName
      }
      return code
    },
    //根据工序Code匹配名字
    getPresentProdProcessName(id) {
      let target = this.SegmentList.find(item => item.EquipmentCode == id)
      if (target) {
        return target.EquipmentName
      }
      return id
    },
    //根据员工Code匹配名字
    getStaffName(staffId) {
      let target = this.StaffList.find(item => item.Code == staffId)
      if (target) {
        // return target.StaffName
        return target.Name
      }
      return staffId
    },
    //根据问题代码 id匹配名字
    getTargetEDRName(id) {
      let target = this.TargetEDRList.find(item => item.ItemValue == id)
      if (target) {
        return target.ItemName
      }
      return id
    },

    //是否关闭
    getCloseStatus(param) {
      let target = this.closeStatusList.find(item => item.ItemValue == param)
      return target?.ItemName || param
    },
    getSeverity(id) {
      let target = this.eventLevelList.find(item => item.ItemValue == id)
      if (target) {
        return target.ItemName
      }
      return id
    },
    //当前等级
    getCurrentLevel(currentLevel) {
      let target = this.simLvList.find(item => item.ItemValue == currentLevel)
      return target?.ItemName || currentLevel
    },
    async getListFn() {
      var StateList = []
      if (this.searchFormObj.State) {
        StateList = this.searchFormObj.State.map(el => {
          return el.ItemName || []
        })
      }
      this.showInfoDialog = false
      let params1 = {
        "ProblemTag": '',
        "ProblemSource": 'SIM3',
        "ReceiveDepartmentId": this.searchFormObj.ReceiveDepartmentId,
        // "ReceiveUserId": this.searchFormObj.ReceiveUserId,
        "ReceiveUserId": this.searchFormObj.ReceiveUserId,
        // "ProcessingUserId": this.getUserinfolist[0].LoginName,
        "StartTime": this.searchFormObj.StartTime,
        "EndTime": this.searchFormObj.EndTime,
        "StateList": this.matchedNames,
        "pageIndex": this.pageOptions.pageIndex,
        "pageSize": this.pageOptions.pageSize,
        "EdrCode": this.searchFormObj.problemType,
        "PresentDepartmentId": this.searchFormObj.PresentDepartmentId
      }
      //合并参数
      // let params = Object.assign(params1, this.$props.searchFormObj)
      let { response } = await getPageList(params1)
      response.data.map((el, index) => {
        el.PresentDepartmentId2 = this.getDepartmentName(el.PresentDepartmentId)
        el.ReceiveDepartmentId1 = this.getDepartmentName(el.ReceiveDepartmentId)
        el.TargetEDRCode = this.getTargetEDRName(el.TargetEDRCode)
        el.CurrentLevel = this.getCurrentLevel(el.CurrentLevel)
        el.ReceiveUserId = this.getStaffName(el.ReceiveUserId)
        // el.ReceiveDepartmentId = this.getDepartmentName(el.ReceiveDepartmentId)
      })
      // this.columns = response.data
      this.pageOptions.total = response.dataCount
      //处理时间字段
      response.data.forEach(item => {
        item.PlanFinishDate = dayjs(item.PlanFinishDate).format('YYYY-MM-DD');
        if (item.EstimateFinishDate != null && item.EstimateFinishDate != undefined && item.EstimateFinishDate != '') {
          item.EstimateFinishDate = dayjs(item.EstimateFinishDate).format('YYYY-MM-DD');
        }
        if (item.ActualFinishDate != null && item.ActualFinishDate != undefined && item.ActualFinishDate != '') {
          item.ActualFinishDate = dayjs(item.ActualFinishDate).format('YYYY-MM-DD');
        }
        item.PresentDate = dayjs(item.PresentDate).format('YYYY-MM-DD');
        item.CreateDate = dayjs(item.CreateDate).format('YYYY-MM-DD');
        item.ModifyDate = dayjs(item.ModifyDate).format('YYYY-MM-DD');
      });
      this.tableList = response.data
    },
    searchForm(form) {
      this.matchedNames = []
      this.searchFormObj.ReceiveDepartmentId = form.ReceiveDepartmentId
      this.searchFormObj.ReceiveUserId = form.ReceiveUserId
      this.searchFormObj.StartTime = form.StartTime
      this.searchFormObj.EndTime = form.EndTime
      // this.searchFormObj.State = form.State
      this.searchFormObj.problemType = form.problemType
      this.searchFormObj.PresentDepartmentId = form.PresentDepartmentId
      this.searchFormObj.State.map(item => {
        this.statusList.map(el => {
          if (item == el.ItemValue) {
            this.matchedNames.push(el.ItemName);
          }
        })
      });
      this.getListFn()
    },
    //接受
    accept() {
      let curAuthID = this.getUserinfolist[0].LoginName
      let selectRecords = this.$refs.vxeTable.getCheckboxRecords()
      if (selectRecords.length <= 0) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'green' });
        return false;
      } else if (selectRecords[0].ReceiveUserId !== curAuthID) {
        this.$store.commit('SHOW_SNACKBAR', { text: "只有责任人可以接受", color: 'green' });
        return false;
      }
      // this.editItemObj = selectRecords[0]
      // this.showInfoDialog = true
      this.$confirms({
        // message: this.$t('GLOBAL._COMFIRM'),
        message: '请确认是否接受',
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
        // message: this.$t('GLOBAL._COMFIRM'),
        // message: '请确认是否接受',
        // confirmText: this.$t('GLOBAL._QD'),
        // cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        let params = {
          problemId: selectRecords[0].ID,
          userId: curAuthID
        }
        let res = await Accept(params)
        this.$store.commit('SHOW_SNACKBAR', { text: '提交成功', color: 'success' });
        this.getListFn()
        // let params = {
        //   problemId: selectRecords[0].ID,
        //   userId: curAuthID
        // }
        // let res = await Accept(params)
        // this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
        // this.getListFn()
      })
    },

    //申请验收
    applyFor() {
      let curAuthID = this.getUserinfolist[0].LoginName
      let selectRecords = this.$refs.vxeTable.getCheckboxRecords()
      if (selectRecords.length <= 0) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'green' });
        return false;
      } else if (selectRecords[0].ReceiveUserId !== curAuthID) {
        this.$store.commit('SHOW_SNACKBAR', { text: "只有责任人可以申请验收", color: 'green' });
        return false;
      }
      // this.editItemObj = selectRecords[0]
      // this.showInfoDialog = true
      this.$confirms({
        // message: this.$t('GLOBAL._COMFIRM'),
        message: '请确认是否申请验收',
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
        // message: this.$t('GLOBAL._COMFIRM'),

      }).then(async () => {
        let params = {
          problemId: selectRecords[0].ID,
          userId: curAuthID
        }
        let res = await ApplyFor(params)
        this.$store.commit('SHOW_SNACKBAR', { text: '提交成功', color: 'success' });
        this.getListFn()

      })
    },

    // 获取组织范围
    closeReplyPopup() {
      this.showReplyDialog = false
      this.editItemObj = {}
    },
    closePopup() {
      this.showInfoDialog = false
      this.editItemObj = {}
    },
  }
};
</script>
<style scoped>
.titName {
    flex: 1;
    height: 40px;
    font-size: 20px;
    color: #000;
    text-align: center;
    cursor: pointer;
    width: auto;
    height: 40px;
    text-align: center;
    line-height: 40px;
}
.active {
    width: auto;
    height: 40px;
    text-align: center;
    line-height: 40px;
    background: #3dcd58;
    color: #fff;
    font-weight: bold;
    border-radius: 5px;
    font-size: 20px;
}
.table-content {
    height: 100%;
}
.table-box {
    height: calc(100% - 240px);
}
/deep/ .vxe-checkbox--icon::before {
    color: #5fea72 !important;
}
/deep/ .vxe-icon-checkbox-checked-fill::before {
    color: #5fea72 !important;
}
</style>
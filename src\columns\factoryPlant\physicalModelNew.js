export const physicalModelNewColumns = [
    { field: 'name', title: 'Name', width: 200 },
    { field: 'extendField', title: 'Type' },
    { field: 'EquipmentFunctionList', title: 'Functions' },
    // { field: 'Shift', title: 'Shift' },
    { field: 'Properties', title: 'Properties' },
    // { field: 'IdFields', title: 'Id Fields' },
    // { field: 'SAP', title: 'SAP' },
    // { field: 'OPC', title: 'OPC' },
    // { field: 'Other', title: 'Other' },
]
export const functColumns = [
    { type: 'checkbox', width: 60 },
    { field: 'FunctionCode', title: 'Function编号' },
    { field: 'FunctionName', title: 'Function描述' },
]
export const properColumns = [
    { field: 'PropertyName', title: 'Name' },
    { field: 'PropertyCode', title: 'Code' },
    // { field: 'DefaultValue', title: 'Default Value' },
    { field: 'ActualValue', title: 'Value' },
]
export const actionColumns = [
    { field: 'icon', title: '', width: 50 },
    { field: 'ActionName', title: 'Name' },
    { field: 'ActionCode', title: 'Code' },
    // { field: 'ActionType', title: 'Type' },
    // { field: 'ActionActiveStatus', title: 'Status' },
    { field: 'Action', title: '' },
]
export const actionProperColumns = [
    { field: 'FunctionName', title: 'Function' },
    { field: 'ActionName', title: 'Action Name' },
    { field: 'PropertyName', title: 'Property Name' },
    { field: 'PropertyCode', title: 'Property Code' },
    { field: 'ActualValue', title: 'Value' },
]
export const interlockColumn = [
    // { type: 'checkbox', width: 60 },
    { field: 'InterLockCode', title: 'Name' },
    { field: 'InterLockName', title: 'Description' },
    { field: 'ActualStatus', title: 'Status' },
]

export const bottlenecksColumns = [
    { field: 'MaterialGroupId', title: 'Material Group' },
    { field: 'MaterialCode', title: 'Material' },
    { field: 'EffectiveStartDate', title: 'Effective Start', width: 140 },
    { field: 'EffectiveEndDate', title: 'Effective End', width: 140 },
    { field: 'Machines', title: 'Machines' },
    { field: 'Action', title: '', width: 60, fixed: "right" },
]
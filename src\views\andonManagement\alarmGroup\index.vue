// 告警升级规则
<template>
    <div class="line-side-view">
        <div class="line-side-main overflow-auto">
            <SearchForm ref="contactTorm" class="mt-2" :searchinput="searchinput"
                :show-from="showFrom" @searchForm="searchForm" />
            <v-card outlined class="mt-2">
                <div class="form-btn-list pt-1 pb-2">
                    <!-- 搜索栏 -->
                    <!-- <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn> -->
                    <v-btn icon color="primary">
                        <v-icon @click="getDataList">mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" @click="operaClick('main', {})">新增</v-btn>
                    <v-btn color="primary" @click="sureItems('main')" :disabled="selectedList.length == 0">{{ $t('GLOBAL._PLSC') }}</v-btn>
                </div>
                <Tables :headers="headers" :tableHeight="tableHeight" :desserts="desserts" :loading="loading" :page-options="pageOptions"
                    :btn-list="btnList" @selectePages="selectePages" :click-fun="clickTr" :current-select-id="currentSelectId"
                    itemKey="GroupCode" @itemSelected="selectedItems" @toggleSelectAll="selectedItems" @tableClick="tableClick"></Tables>
            </v-card>
            <v-card outlined class="mt-4">
                <div class="form-btn-list pt-1 pb-1">
                    <v-btn :disabled="!currentSelectId" icon color="primary">
                        <v-icon @click="getSubDataList">mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" :disabled="!currentSelectId" @click="operaClick('sub', {})">添加</v-btn>
                    <v-btn color="primary" :disabled="!(currentSelectId && subList.length > 0)" @click="sureItems('sub')">批量移除</v-btn>
                </div>
                <Tables :headers="alarmGroupPostColum" :tableHeight="tableHeight" :desserts="subDesserts" :loading="subLoding" :btn-list="subBtnList" @selectePages="selectePages"
                    :footer="false" :dictionaryList="dictionaryList" @itemSelected="selectedSubItems" @toggleSelectAll="selectedSubItems" @tableClick="tableClick" :page-options="subPageOptions"></Tables>
            </v-card>
            <update-dialog ref="updateDialog" :opera-obj="operaObj" @handlePopup="handlePopup"></update-dialog>
            <detailsDialog ref="detailsDialog" :needOnTypes="needOnTypes" :dutyList="dutyList" :opera-obj="operaObj" @handlePopup="handlePopup"/>
        </div>
    </div>
</template>
<script>
import { getAlarmgroupList, DeleteAlarmgroup, getAlarmgroupPostList, DeleteAlarmgroupPost, AlarmgroupPostSaveForm } from '@/api/andonManagement/alarmGroup.js';
import { alarmGroupColum, alarmGroupPostColum } from '@/columns/andonManagement/alarmGroup.js';
import Util from "@/util"
export default {
    name: 'UpgradeRule',
    components: {
        UpdateDialog: () => import('./components/updateDialog.vue'),
        detailsDialog: () => import('./components/detailsDialog.vue')
    },
    data() {
        return {
            operaObj: {},
            showFrom: false,
            headers: alarmGroupColum,
            alarmGroupPostColum,
            loading: false,
            subLoding: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            subPageOptions: {
                page: 1, // 当前页码
                pageSize: 20 // 一页数据
            },
            tableHeight: 'calc(50vh - 120px)',
            deleteId: [],
            deleteType: '',
            selectedList: [],
            deleteList: [],
            subList: [],
            desserts: [],
            subDesserts: [],
            searchParams: {},
            dutyList: [],
            currentSelectId: '',
            currentSelectName: '',
            needOnTypes: []
        }
    },
    computed: {
        //查询条件
        searchinput() {
            return [
                  // 关键字
                {
                    key: 'key',
                    icon: '',
                    value: '',
                    label: '关键字'
                }
            ];
        },
        btnList() {
            return [
                { text: "编辑", icon: '', code: 'edit', type: 'primary' },
                { text: '删除', icon: '', code: 'delete', type: 'error'  }
            ];
        },       
        subBtnList() {
            return [
                { text: "编辑", icon: '', code: 'subEdit', type: 'primary' },
                { text: '移除', icon: '', code: 'subDelete', type: 'error'  }
            ];
        },
        dictionaryList(){
            return [
                {arr: this.needOnTypes, key: 'IsNeedOn', val: 'ItemValue', text: 'ItemName'},
                {arr: this.needOnTypes, key: 'IsRelatedPline', val: 'ItemValue', text: 'ItemName'}
            ]
        }         
    },
    async created(){
        this.needOnTypes = await this.$getDataDictionary('andon_IsNeedOn')
        await this.getDutyList();
        await this.getDataList();
        if(this.desserts.length > 0) this.clickTr(this.desserts[0])
    },
    methods: {
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        // 获取详情表格的勾选数据
        selectedSubItems(item) {
            this.subList = [...item];
        },
        //点击表格行
        async clickTr(o) {
            const { GroupCode, GroupName } = o
            this.currentSelectId = GroupCode;
            this.currentSelectName = GroupName
            this.getSubDataList()
        },
        // 操作栏按钮
        tableClick(item, type) {
            this.deleteId = item?.ID
            switch (type) {
                // 编辑
                case 'edit':
                    this.operaClick('main', item);
                    break;
                // 删除
                case 'delete':
                    this.deleteType = 'main'
                    this.sureDelete()
                    break;
                // 编辑详情
                case 'subEdit':
                    this.operaClick('sub', item);
                    break;
                // 编辑详情单元格
                case 'editCell':
                    this.editTableCell(item);
                    break;
                // 删除详情
                case 'subDelete':
                    this.deleteType = 'sub'
                    this.sureDelete()
                    break;
                default:
                    break;
            }
        },
        // 新增/编辑表格单元格
        async editTableCell(o) {
            const res = await AlarmgroupPostSaveForm({ ...o })
            const { success, msg } = res || {};
            if (success) this.$store.commit('SHOW_SNACKBAR', { text: msg || 'success', color: 'success' });
        },
        // 获取职位列表
        async getDutyList() {
            this.dutyList = await Util.GetDepartmentByLevel('post');
        },
        // 获取接警组详情
        async getSubDataList() {
            this.subLoding = true
            const res = await getAlarmgroupPostList({ GroupCode: this.currentSelectId })
            const { success, response } = res || {};
            if(success){
                this.subDesserts = response || []
            }
            this.subLoding = false
        },
        // 获取接警组数据
        async getDataList() {
            this.loading = true;
            let params = {
                ...this.searchParams,
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await getAlarmgroupList(params);
            const { success, response } = res || {};
            const { data, dataCount, page } = response || {};
            this.desserts = []
            if (success && data) {
                const arr = data || [];
                arr.forEach(e => {
                    this.desserts.push({...e, actionsBtnShow: 'Status'})
                });
                this.pageOptions.total = dataCount;
                this.pageOptions.page = page;
            } else {
                this.desserts = [];
            }
            this.loading = false;
        },
        // 查询数据
        searchForm(v) {
            this.searchParams = v;
            this.getDataList();
        },
        // 新增
        operaClick(type, o) {
            this.operaObj = o || {};
            if(type != 'main') this.operaObj = {...this.operaObj, GroupName: this.currentSelectName,
             GroupCode: this.currentSelectId}
            this.$refs[type == 'main'?'updateDialog': 'detailsDialog'].dialog = true;
        },
        // 批量删除
        sureItems(type){
            this.deleteType = type
            this.deleteList = type=='main'?this.selectedList: this.subList
            if (this.deleteList.length > 0) {
                this.deleteId = ''
                this.sureDelete();
            } else {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'error' });
            }
        },
        // 删除二次确认
        sureDelete() {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    const params = [];
                    if (this.deleteId) {
                        params.push(this.deleteId);
                    } else {
                        this.deleteList.forEach(e => {
                            params.push(e.ID);
                        });
                    }
                    const fn = this.deleteType == 'main'?DeleteAlarmgroup: DeleteAlarmgroupPost
                    const res = await fn(params);
                    this.deleteList = [];
                    this.deleteId = '';
                    const { success, msg } = res;
                    if (success) {
                        if (this.deleteType == 'main') {
                            this.pageOptions.pageCount = 1;
                            this.getDataList()
                        } else {
                            this.getSubDataList()
                        }
                        this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 根据子组件返回来值
        handlePopup(type, data) {
            console.log(type);
            switch (type) {
                case 'refresh':
                    this.getSubDataList();
                    break;

                default:
                    this.getDataList();
                    break;
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.line-side-view {
    display: flex;

    .line-side-main {
        flex: 1;
        width: 100%;

        .v-data-table {
            width: 100%;
        }
    }
}
</style>
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Inventory'
const baseURL2 = 'baseURL_MATERIAL'

//table数据
export function GetRequestIiViewPageList(data) {
    const api = '/api/RequestIiView/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetMaterialType(data) {
    const api = '/api/Category/GetList?Identities=MaterialType'
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetMaterialSelectListClass(data) {
    const api = '/api/MaterialInventory/GetMaterialSelectListClass'
    return getRequestResources(baseURL, api, 'get', data);
}
export function GetMSelectListClass(data) {
    const api = '/api/MaterialInventory/GetMSelectListByClass'
    return getRequestResources(baseURL, api, 'get', data);
}

export function AddData(data) {
    const api = '/api/RequestInventoryView/SearchRequestInsertData'
    return getRequestResources(baseURL, api, 'post', data);
}
export function SaveData(data) {
    const api = '/api/RequestInventoryView/AddRequestInventory'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetRequestinfo(data) {
    const api = '/api/RequestIiView/GetRequestInfo'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetRequestDetailList(data) {
    const api = '/api/RequestIiView/GetRequestDetailList'
    return getRequestResources(baseURL, api, 'post', data);
}
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_TRACE'

//检查载具码Sn
export function VehicleSnCheckVehicleSn(data) {
    const api =  '/trace/VehicleSn/CheckVehicleSn'
    return getRequestResources(baseURL, api, 'post', data);
}


//载具码绑定SN
export function VehicleSnBindVehicleSn(data) {
    const api =  '/trace/VehicleSn/BindVehicleSn'
    return getRequestResources(baseURL, api, 'post', data);
}

//检查载具码BoxSn
export function VehicleSnCheckVehicleBoxSn(data) {
    const api =  '/trace/VehicleSn/CheckVehicleBoxSn'
    return getRequestResources(baseURL, api, 'post', data);
}

//载具码绑定产品码
export function VehicleSnBindVehicleBoxSn(data) {
    const api =  '/trace/VehicleSn/BindVehicleBoxSn'
    return getRequestResources(baseURL, api, 'post', data);
}

//删除载具码绑定SN
export function VehicleSnDelete(data) {
    const api =  '/trace/VehicleSn/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

//获取载具列表
export function VehicleSnGetPageList(data) {
    const api =  '/trace/VehicleSn/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

//载具列表解绑
export function VehicleSnUnBindData(data) {
    const api =  '/trace/VehicleSn/UnBindData'
    return getRequestResources(baseURL, api, 'post', data);
}



import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Inventory'

//table数据
export function getTabelData(data) {
    const api = '/api/Container/GetPageViewList'
    return getRequestResources(baseURL, api, 'post', data);
}

//table数据
export function GetConClassList(data) {
    const api = '/api/Container/GetConClassList'
    return getRequestResources(baseURL, api, 'post', data);
} //table数据
export function GetConStatusList(data) {
    const api = '/api/Container/GetConStatusList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetDestinationList(data) {
    const api = '/api/Container/GetDestinationList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetViewEntity(data) {
    const api = `/api/Container/GetViewEntity`
    return getRequestResources(baseURL, api, 'get', data);
}
export function ChangeContainerState(data) {
    const api = '/api/Container/ChangeContainerState'
    return getRequestResources(baseURL, api, 'post', data);
}
export function TransferContainerByContainer(data) {
    const api = '/api/Container/TransferContainerByContainer'
    return getRequestResources(baseURL, api, 'post', data);
}
export function ClearContainer(data) {
    const api = '/api/Container/ClearContainer'
    return getRequestResources(baseURL, api, 'post', data);
}
export function TransferContainer(data) {
    const api = '/api/Container/TransferContainer'
    return getRequestResources(baseURL, api, 'post', data);
}
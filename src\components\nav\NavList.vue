<template>
    <v-list dense>
        <template v-for="(item, key) in items">
            <v-list-group v-if="hasChild(item)" :key="key" :title="item.title" no-action :to="item.path"
                :value="computeGroupExpanded(item, $route)">
                <template #prependIcon>
                    <v-icon v-text="item.icon" />
                </template>
                <template #activator>
                    <v-list-item-content>
                        <v-list-item-title v-text="item.title" />
                    </v-list-item-content>
                </template>
                <v-list-item v-if="item.MEMU_TYPE == 1">
                    <nav-list :items="item.children"></nav-list>
                </v-list-item>
                <!-- <nav-list v-if="item.MEMU_TYPE == 1" :items="item"></nav-list> -->
                <nav-list-item v-else v-for="child in item.children" v-show="!mini" :key="'c' + child.path"
                    :item="child" />
            </v-list-group>
            <nav-list-item v-else :key="'nav' + key" :item="item" />
        </template>
    </v-list>
</template>

<script>
import NavListItem from './NavListItem';
export default {
    components: {
        NavListItem
    },
    name: 'NavList',
    props: {
        mini: Boolean,
        items: {
            type: Array,
            default: () => []
        }
    },
    methods: {
        hasChild(item) {
            return Array.isArray(item.children) && item.children.length > 0;
        },
        computeGroupExpanded(item, $route) {
            return $route.matched.map(item => item.path).includes(item.path);
        }
    }
};
</script>

<template>
    <v-form ref="form" v-model="valid">
        <v-row class="mt-5" no-gutters>
            <v-col :cols="12" class="pt-0 pb-0">
                <v-text-field label="旧密码" :type="type" :rules="OldPasswordRules" required dense outlined v-model="form.OldPassword">
                    <template #append>
                        <v-icon style="cursor: pointer" @click="() => (type === 'password' ? (type = 'text') : (type = 'password'))">
                            {{ type === 'password' ? 'mdi-eye-off' : 'mdi-eye' }}
                        </v-icon>
                    </template>
                </v-text-field>
            </v-col>
            <v-col :cols="12" class="pt-0 pb-0">
                <v-text-field label="新密码" :type="type1" :rules="PasswordRules" required dense outlined v-model="form.Password">
                    <template #append>
                        <v-icon style="cursor: pointer" @click="() => (type1 === 'password' ? (type1 = 'text') : (type1 = 'password'))">
                            {{ type1 === 'password' ? 'mdi-eye-off' : 'mdi-eye' }}
                        </v-icon>
                    </template>
                </v-text-field>
            </v-col>
            <v-col :cols="12" class="pt-0 pb-0">
                <v-text-field label="重复新密码" :rules="Password1Rules" :type="type2" required dense outlined v-model="form.Password1">
                    <template #append>
                        <v-icon style="cursor: pointer" @click="() => (type2 === 'password' ? (type2 = 'text') : (type2 = 'password'))">
                            {{ type2 === 'password' ? 'mdi-eye-off' : 'mdi-eye' }}
                        </v-icon>
                    </template>
                </v-text-field>
            </v-col>
            <!-- <v-col :cols="12" class="pt-0 pb-0">
                <v-text-field label="验证码" dense outlined v-model="form.UserName"></v-text-field>
            </v-col> -->
        </v-row>
        <div class="d-flex justify-center">
            <v-btn color="primary" @click="submitForm">提交</v-btn>
        </div>
    </v-form>
</template>
<script>
import { mapGetters } from 'vuex';
import { saveUserForm } from '@/api/common.js';
export default {
    data() {
        return {
            valid: true,
            type: 'password',
            type1: 'password',
            type2: 'password',
            form: {
                OldPassword: '',
                Password: '',
                Password1: ''
            },
            OldPasswordRules: [v => !!v || '旧密码不能为空'],
            PasswordRules: [v => !!v || '新密码不能为空'],
            Password1Rules: [v => this.isConfirmPassword(v) || '新密码与确认密码不一致']
        };
    },
    computed: {
        ...mapGetters(['getUserinfolist'])
    },
    methods: {
        // 密码是否一致
        isConfirmPassword(value) {
            return this.form.Password === value;
        },
        // 密码加密
        isPassword(value) {
            let reg = /^(?![A-z0-9]+$)(?=.[^%&',;=?$\x22])(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9]).{8,20}$/;
            return reg.test(value);
        },
        async submitForm() {
            const fromRes = await this.$refs.form.validate();

            if (fromRes) {
                let params = {
                    OldPassword: this.form.OldPassword,
                    Password: this.form.Password,
                    ID: this.getUserinfolist[0].ID
                };
                const res = await saveUserForm(params);
                let { success } = res || {};
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: '修改成功', color: 'success' });
                    this.$store.commit('SHOW_SNACKBAR', { text: '请重新登录', color: 'success' });
                    sessionStorage.clear();
                    localStorage.clear();
                    this.$store.dispatch('logout');
                    setTimeout(() => {
                        this.$router.push('/auth/login');
                    }, 1000);
                }
            }
        }
    }
};
</script>
<style lang="scss" scoped></style>

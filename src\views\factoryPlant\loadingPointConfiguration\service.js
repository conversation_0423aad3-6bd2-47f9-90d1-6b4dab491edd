import { getRequestResources } from '@/api/fetch';
const DFM = 'baseURL_DFM'

// 查询成品料号列表
export function getMaterialList(data) {
    const api = '/api/Material/GetPageList'
    return getRequestResources(DFM, api, 'post', data)
}
// 查询单位列表数据
export function getUnitList(data) {
    const api = '/api/Unitmanage/GetList'
    return getRequestResources(DFM, api, 'post', data)
}
// 表单提交
export function saveForm(data) {
    const api = '/api/RelMaterialTag/SaveForm'
    return getRequestResources(DFM, api, 'post', data)
}
// 查询物料分类下拉框列表
export function getSelectClassifyList(data) {
    const api = '/api/Category/GetList?Identities=' + data.Identities
    return getRequestResources(DFM, api, 'post', data)
}
// 查询列表数据
export function getLoadingPointConfigList(data) {
    const api = '/api/RelMaterialTag/GetPageList'
    return getRequestResources(DFM, api, 'post', data)
}
// 删除数据
export function delLoadingPointConfig(data) {
    const api = '/api/RelMaterialTag/Delete'
    return getRequestResources(DFM, api, 'post', data)
}
//  导入
export function doImport(data) {
    const api = '/api/RelMaterialTag/ImportExcel'
    return getRequestResources(DFM, api, 'post', data)
}
// 批量禁用 启用
export function handleChangeEnable(data) {
    const api = '/api/RelMaterialTag/UpdateIsenabled'
    return getRequestResources(DFM, api, 'post', data)
}
// 导出
export function doExport(data) {
    const api = '/api/RelMaterialTag/ExportExcelTemplates'
    return getRequestResources(DFM, api, 'post', data)
}
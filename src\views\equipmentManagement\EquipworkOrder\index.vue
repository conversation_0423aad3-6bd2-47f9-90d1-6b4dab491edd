<template>
    <div class="pa-1">
        <!-- <div class="mb-4">信息通知</div> -->
        <div class="d-flex flex-sm-row">
            <v-row>
                <v-col cols="12" md="7">
                    <!-- 功能模块入口 -->
                    <div class="me-auto">
                        <v-card height="320" class="mb-4 pa-2">
                            <div class="infoMessage ma-2">
                                <!-- <h4>基本信息</h4> -->
                                <h2 class="ml-4">{{ getUserinfolist[0].LoginName }},你好</h2>
                            </div>
                            <div class="infoEqumint">
                                <h4>{{ $t('TPM_SBGL_SBGLZY._SBZT') }}</h4>
                                <div>
                                    <equip-list></equip-list>
                                </div>
                            </div>
                        </v-card>
                        <v-card height="320">
                            <!-- 信息通知 -->
                            <v-tabs v-model="tab1" bg-color="primary">
                                <v-tab value="1">{{ $t('TPM_SBGL_SBGLZY._XXLX') }}</v-tab>
                                <v-tab value="2">{{ $t('TPM_SBGL_SBGLZY._SBWH') }}</v-tab>
                                <v-tab value="3">{{ $t('TPM_SBGL_SBGLZY._SBBY') }}</v-tab>
                            </v-tabs>
                            <v-tabs-items v-model="tab1">
                                <v-tab-item class="tablist">
                                    <v-row>
                                        <v-col class="d-flex flex-wrap">
                                            <v-card width="100" height="100"
                                                class="ma-2 d-flex justify-center bg-surface-variant card-bg"
                                                to="/equipmentManagement/Equip">
                                                <div class="ma-auto">
                                                    <v-icon x-large>{{ 'mdi-layers-triple-outline' }}</v-icon>
                                                    <div>设备录入</div>
                                                </div>
                                            </v-card>
                                            <v-card width="100" height="100"
                                                class="ma-2 d-flex justify-center bg-surface-variant card-bg"
                                                to="/equipmentManagement/spareParts">
                                                <div class="ma-auto">
                                                    <v-icon x-large>{{ 'mdi-tag' }}</v-icon>
                                                    <div>备件管理</div>
                                                </div>
                                            </v-card>
                                            <v-card width="100" height="100"
                                                class="ma-2 d-flex justify-center bg-surface-variant card-bg"
                                                to="/equipmentManagement/equipmentReasonTree">
                                                <div class="ma-auto">
                                                    <v-icon x-large>{{ 'mdi-book-open-blank-variant' }}</v-icon>
                                                    <div>知识库</div>
                                                </div>
                                            </v-card>
                                        </v-col>
                                    </v-row>
                                </v-tab-item>
                                <v-tab-item class="tablist">
                                    <v-row>
                                        <v-col class="d-flex flex-wrap">
                                            <v-card width="100" height="100"
                                                class="ma-2 d-flex justify-center bg-surface-variant card-bg"
                                                to="/equipmentManagement/Repair">
                                                <div class="ma-auto">
                                                    <v-icon x-large>{{ 'mdi-layers-triple-outline' }}</v-icon>
                                                    <div>维修工单</div>
                                                </div>
                                            </v-card>
                                            <v-card width="100" height="100"
                                                class="ma-2 d-flex justify-center bg-surface-variant card-bg"
                                                to="/equipmentManagement/RepairPlan">
                                                <div class="ma-auto">
                                                    <v-icon x-large>{{ 'mdi-layers-triple-outline' }}</v-icon>
                                                    <div>维修记录</div>
                                                </div>
                                            </v-card>
                                            <v-card width="100" height="100"
                                                class="ma-2 d-flex justify-center bg-surface-variant card-bg"
                                                to="/equipmentManagement/RepairPlan">
                                                <div class="ma-auto">
                                                    <v-icon x-large>{{ 'mdi-layers-triple-outline' }}</v-icon>
                                                    <div>维修成本</div>
                                                </div>
                                            </v-card>
                                        </v-col>
                                    </v-row>
                                </v-tab-item>
                                <v-tab-item class="tablist">
                                    <v-row>
                                        <v-col class="d-flex flex-wrap">
                                            <v-card width="100" height="100"
                                                class="ma-2 d-flex justify-center bg-surface-variant card-bg"
                                                to="/equipmentManagement/upkeeplistB">
                                                <div class="ma-auto">
                                                    <v-icon x-large>{{ 'mdi-pencil-box-multiple' }}</v-icon>
                                                    <div>保养计划</div>
                                                </div>
                                            </v-card>
                                            <v-card width="100" height="100"
                                                class="ma-2 d-flex justify-center bg-surface-variant card-bg"
                                                to="/equipmentManagement/upkeepplanB">
                                                <div class="ma-auto">
                                                    <v-icon x-large>{{ 'mdi-table-search' }}</v-icon>
                                                    <div>保养规则</div>
                                                </div>
                                            </v-card>
                                            <v-card width="100" height="100"
                                                class="ma-2 d-flex justify-center bg-surface-variant card-bg"
                                                to="/equipmentManagement/upkeeplist">
                                                <div class="ma-auto">
                                                    <v-icon x-large>{{ 'mdi-table-search' }}</v-icon>
                                                    <div>点检计划</div>
                                                </div>
                                            </v-card>
                                            <v-card width="100" height="100"
                                                class="ma-2 d-flex justify-center bg-surface-variant card-bg"
                                                to="/equipmentManagement/upkeepplan">
                                                <div class="ma-auto">
                                                    <v-icon x-large>{{ 'mdi-table-search' }}</v-icon>
                                                    <div>点检规则</div>
                                                </div>
                                            </v-card>
                                        </v-col>
                                    </v-row>
                                </v-tab-item>
                            </v-tabs-items>
                        </v-card>
                    </div>
                </v-col>
                <!-- 代办信息 -->
                <v-col cols="12" md="5">
                    <v-card class="">
                        <!-- 信息通知 -->
                        <v-tabs v-model="tab" bg-color="primary">
                            <v-tab @click="RepastInfoGetPage(0)" value="0">{{ $t('TPM_SBGL_SBGLZY._DB') }}</v-tab>
                            <v-tab @click="RepastInfoGetPage(2)" value="2">{{ $t('TPM_SBGL_SBGLZY._JXZ') }}</v-tab>
                            <v-tab @click="RepastInfoGetPage(1)" value="1">{{ $t('TPM_SBGL_SBGLZY._YB') }}</v-tab>
                        </v-tabs>
                        <v-tabs-items v-model="tab">
                            <v-tab-item>
                                <Tables :footer="false" :page-options="pageOptions" :loading="loading" :btn-list="btnList1"
                                    tableHeight="calc(80vh - 168px)" table-name="TPM_SBGL_SBGLZY" :headers="EquipColum"
                                    :desserts="desserts" @tableClick="tableClick"></Tables>
                            </v-tab-item>
                            <v-tab-item>
                                <Tables :footer="false" :page-options="pageOptions" :loading="loading" :btn-list="btnList2"
                                    tableHeight="calc(80vh - 168px)" table-name="TPM_SBGL_SBGLZY" :headers="EquipColum"
                                    :desserts="desserts" @tableClick="tableClick"></Tables>
                            </v-tab-item>
                            <v-tab-item>
                                <Tables :footer="false" :page-options="pageOptions" :loading="loading" :btn-list="btnList3"
                                    tableHeight="calc(80vh - 168px)" table-name="TPM_SBGL_SBGLZY" :headers="EquipColum"
                                    :desserts="desserts" @tableClick="tableClick"></Tables>
                            </v-tab-item>
                        </v-tabs-items>
                        <createRepast ref="createRepast" :equipStatuslist="equipStatuslist" :dialogType="dialogType"
                            :tableItem="tableItem"></createRepast>
                    </v-card>
                </v-col>
            </v-row>
        </div>
        <v-sheet class="mx-auto mt-4" elevation="8" height="180"></v-sheet>
    </div>
</template>
<script>
import equipList from './components/equipList.vue';
import { mapGetters } from 'vuex';
import { EquipColum } from '@/columns/equipmentManagement/EquipworkOrder.js';
import { DeviceRepairWoGetPageList } from '@/api/equipmentManagement/Repair.js';
export default {
    name: 'EquipworkOrder',
    components: { equipList, createRepast: () => import('./components/createRepast.vue') },
    data() {
        return {
            model: null,
            tab: null,
            tab1: null,
            desserts: [],
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            EquipColum,
            papamstree: {
                key: null,
                repairstatus: '0', // 设备维修状态
                pageIndex: 1,
                pageSize: 20
            },
            equipStatuslist: [], //维修状态
            dialogType: '', // 弹窗类型
            tableItem: {} // 选择操作数据
        };
    },
    computed: {
        ...mapGetters(['getUserinfolist']),
        dictionaryList() {
            return [];
        },
        btnList1() {
            return [
                {
                    text: this.$t('TPM_SBGL_SBGLZY._WH'),
                    code: 'repair',
                    type: 'primary',
                    icon: ''
                }
            ];
        },
        btnList2() {
            return [
                {
                    text: this.$t('TPM_SBGL_SBGLZY._XQ'),
                    code: 'working',
                    type: 'primary',
                    icon: ''
                }
            ];
        },
        btnList3() {
            return [
                {
                    text: this.$t('TPM_SBGL_SBGLZY._XQ'),
                    code: 'over',
                    type: 'primary',
                    icon: ''
                }
            ];
        }
    },
    mounted() {
        this.RepastInfoGetPage();
    },
    methods: {
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            console.log(item.AbnormalDesc);
            switch (type) {
                case 'repair':
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'working':
                    this.$refs.createRepast.showDialog = true;
                    this.$refs.createRepast.RepastInfologGetPage(this.tableItem);
                    return;
                case 'over':
                    this.$refs.createRepast.showDialog = true;
                    this.$refs.createRepast.RepastInfologGetPage(this.tableItem);
                    return;
            }
        },
        // 列表查询
        async RepastInfoGetPage(val) {
            let params = {
                deviceid: '',
                reasons: '',
                type: '',
                planworkuser: '',
                inputtype: '',
                repairstatus: val || this.papamstree.repairstatus, // 订单状态
                pageIndex: this.papamstree.pageIndex,
                pageSize: this.papamstree.pageSize
            };
            this.loading = true;
            const res = await DeviceRepairWoGetPageList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.infoMessage {
    height: 100px;

    .fs-color {
        font-size: 16px;
        font-weight: 600;
        color: var(--v-primary-base);
    }
}

.infoEqumint {
    height: 200px;
}

.tablist {
    height: 230px;
    overflow: auto;

    .card-bg {
        background: var(--v-primary-lighten5);
    }
}
</style>

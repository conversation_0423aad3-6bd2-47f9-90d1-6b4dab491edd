import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Inventory'

//table数据
export function getTransferTabelData(data) {
    const api = '/api/TransferHistoryView/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetPageListByConID(data) {
    const api = '/api/TransferHistoryView/GetPageListByConID'
    return getRequestResources(baseURL, api, 'post', data);
}

export function getReverse(data) {
    const api = '/api/MaterialInventory/TranferHisReverse'
    return getRequestResources(baseURL, api, 'post', data);
}
//退货给WMS
export function ReturnWMS(data) {
    const api = '/api/MaterialInventory/ReturnWMS'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetTransferHistorySapData(data) {
    const api = '/api/TransferHistoryView/GetSapData'
    return getRequestResources(baseURL, api, 'post', data);
}


export function DeletePallet(data) {
    const api = '/api/MaterialPreparationView/MPreparationTransfer_DeletePallet'
    return getRequestResources(baseURL, api, 'post', data);
}

<template>
  <div>
    <v-btn
      style="margin-bottom: 5px;"
      color="primary"
      @click="getConfigList()"
    >{{ $t('GLOBAL._CX') }}</v-btn>

    <v-btn
      style="margin-left: 5px;margin-bottom: 5px;"
      color="primary"
      @click="btnClickEvet('add')"
    >{{ $t('GLOBAL._XZ') }}</v-btn>

    <v-card outlined>
      <Tables
        :page-options="pageOptions"
        :tableHeight="showFrom ? 'calc(100vh - 232px)' : 'calc(100vh - 174px)'"
        :loading="loading"
        :btn-list="btnList"
        table-name="SIM_CONFIG"
        :headers="simColums"
        :desserts="desserts"
        @selectePages="selectePages"
        @tableClick="tableClick"
        @itemSelected="SelectedItems"
        @toggleSelectAll="SelectedItems"
      >
        <!-- <template #UserAvatar="{ item }">
          <div>
            <img
              style="border-radius: 50%; margin-top: 4px"
              width="26px"
              :src="item.UserAvatar"
              alt=""
            />
          </div>
        </template>
        <template #IsPiecework="{ item }">
          <div>
            {{ item.IsPiecework ? '是' : '否' }}
          </div>
        </template>
        <template #IsFulltime="{ item }">
          <div>
            {{ item.IsFulltime ? '是' : '否' }}
          </div>
        </template> -->
      </Tables>
      <v-dialog
        v-if="createConferencecopyflag"
        v-model="createConferencecopyflag"
        scrollable
        persistent
        width="85%"
      >
        <createConferencecopy
          ref="createConferencecopy"
          :dialogType="dialogType"
          :selectList="selectList"
          :tableItem="tableItem"
          :departmentData="departmentData"
          :OneId="OneId"
          :TwoId="TwoId"
          :simlevel="simlevel"
          @checkCrea="creaHandle"
        ></createConferencecopy>
      </v-dialog>
    </v-card>
    <copyTeam
      ref="copyRef"
      :OneId="OneId"
      :Moudelname="Moudelname"
      @checkCopy="handleCopy"
    />
  </div>
</template>
<script>
import { StaffGetPageList, StaffDelete, StaffImprot } from '@/api/simConfig/basicdata.js';
import { simColums } from '@/columns/simConfig/basicdata.js';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM;
import { getPageListConfig, delConfigPage } from '@/api/simConfig/simconfignew.js';
export default {
  components: {
    createConferencecopy: () => import('./components/createConferencecopy.vue'),
    copyTeam: () => import('./components/copyTeam.vue')
  },
  data() {
    return {
      Moudelname: '',
      createConferencecopyflag: false,
      CopyId: '',
      simlevel: '',
      OneId: '',
      TwoId: '',
      fileInfo: null,
      baseURL,
      showFrom: false,
      desserts: [],
      // 弹窗数据
      dialogType: '', // 弹窗类型
      tableItem: {}, // 选择操作数据
      selectList: [], //批量选中
      pageOptions: {
        total: 0,
        page: 1, // 当前页码
        pageSize: 20, // 一页数据
        pageCount: 1, // 页码分页数
        pageSizeitems: [10, 20, 50, 100, 500]
      },
      loading: true,
      btnloding: false,
      paramsList: {
        key: '',
        pageIndex: 1,
        pageSize: 20
      },
      simColums,
      departmentData: [] // 组织结构
    };
  },
  computed: {
    btnList() {
      return [
        {
          text: this.$t('GLOBAL._BJ'),
          code: 'edit',
          type: 'primary',
          icon: '',
          // authCode: 'RYZSJ_EDIT'
        },
        {
          text: this.$t('GLOBAL._SC'),
          code: 'delete',
          type: 'red',
          icon: '',
          // authCode: 'RYZSJ_DELETE'
        },
        {
          text: this.$t('GLOBAL._CK'),
          code: 'look',
          type: 'primary',
          icon: '',
          // authCode: 'RYZSJ_DELETE'
        },
        {
          text: this.$t('GLOBAL._FZ'),
          code: 'copy',
          type: 'primary',
          icon: '',
          // authCode: 'RYZSJ_DELETE'
        }
      ];
    },
    // searchinput() {
    //   return [
    //     {
    //       value: '',
    //       key: 'ItmeName',
    //       icon: 'mdi-account-check',
    //       label: '',
    //       placeholder: '输入模块名称'
    //     },
    //     {
    //       value: '',
    //       key: 'ItmeName',
    //       icon: 'mdi-account-check',
    //       label: '',
    //       placeholder: '输入看板标题'
    //     },
    //   ];
    // }
  },
  created() {
    this.getConfigList();
  },
  methods: {
    handleCopy() {
      this.$refs.copyRef.copyDialog = false
      this.getConfigList()
    },
    creaHandle() {
      // this.$refs.createConferencecopy.showDialog = false
      this.createConferencecopyflag = false
      this.getConfigList()
    },
    // searchForm(val) {
    //   this.paramsList.key = val.ItmeName;
    //   this.paramsList.pageIndex = 1;
    //   this.staffGetPageList();
    // },
    async getConfigList() {
      let params = {
        key: this.paramsList.key,
        pageIndex: this.pageOptions.page,
        pageSize: this.pageOptions.pageSize
      };
      this.loading = true;
      const res = await getPageListConfig(params);
      this.loading = false
      if (res.success) {
        this.desserts = res.response.data || []
        this.pageOptions.total = res.response.dataCount;
        this.pageOptions.page = res.response.page;
        this.pageOptions.pageCount = res.response.pageCount;
        this.pageOptions.pageSize = res.response.pageSize;
      }
    },
    // 按钮操作
    btnClickEvet(val) {
      this.dialogType = 'add';
      this.createConferencecopyflag = true
      // this.$refs.createConferencecopy.showDialog = true;
      // switch (val) {
      //   case 'download':
      //     window.open(baseURL + '/api/Staff/DownLoadTemplate');
      //     return;
      //   case 'add':
      //     this.tableItem = {};
      //     this.dialogType = 'add';
      //     this.$refs.createConferencecopy.showDialog = true;
      //     return;
      //   case 'delete':
      //     this.delSubmit();
      //     return;
      // }
    },
    // 表单操作
    tableClick(item, type) {
      this.Moudelname = item.Moudelname
      this.OneId = item.ID
      this.TwoId = item.ID
      this.tableItem = item;
      this.dialogType = type;
      if (type == 'edit') {
        this.simlevel = item.Moudelname
        this.createConferencecopyflag = true
        // this.$refs.createConferencecopy.showDialog = true;
        // this.$refs.createConferencecopy.getOneList()
      }
      if (type == 'delete') {
        this.delSubmit();
      }
      if (type == 'look') {
        if (item.Moudelname == 'SIM1') {
          this.$router.push({ path: 'simNew1', query: { code: item.Simcode, type: 1 } });
        }
        if (item.Moudelname == 'SIM2') {
          this.$router.push({ path: 'simNew2', query: { code: item.Simcode, type: 2 } });
        }
        if (item.Moudelname == 'SIM1.5') {
          this.$router.push({ path: 'simSpot', query: { code: item.Simcode, type: 2 } });
        }
      }
      if (type == 'copy') {
        this.CopyId = item.ID
        this.$refs.copyRef.copyDialog = true
        this.$refs.copyRef.save()
      }

      // switch (type) {
      //   case 'delete':
      //     // this.delSubmit();
      //     return;
      //   case 'edit':
      //     this.$refs.createConferencecopy.showDialog = true;
      //     return;
      // }
    },
    // 选择数据
    SelectedItems(item) {
      this.tableItem = {};
      this.selectList = item;
    },
    selectePages(v) {
      this.pageOptions.page = v.pageCount;
      this.pageOptions.pageSize = v.pageSize;
      this.getConfigList();
    },
    // 删除
    delSubmit() {
      let params = [];
      // eslint-disable-next-line no-prototype-builtins
      if (this.tableItem.hasOwnProperty('ID')) {
        params = [this.tableItem.ID];
      } else {
        this.selectList.forEach(item => {
          params.push(item.ID);
        });
      }
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      })
        .then(async () => {
          let res = await delConfigPage(params);
          if (res.success) {
            this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
            this.getConfigList();
          }
        })
        .catch(err => {
          console.log(err);
        });
    }
  }
};
</script>
<style lang="scss" scoped></style>

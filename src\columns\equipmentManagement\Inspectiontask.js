export const InspectiontaskColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
}, {
    text: '检定方法',
    value: 'VerifyMethod',
    Namevalue: "jdff",
    width: 150,
    sortable: true
}, {
    text: 'ABC分类',
    value: 'Type',
    Namevalue: "abcfl",
    width: 150,
    sortable: true
}, {
    text: '类别',
    value: 'Category',
    Namevalue: "lb",
    width: 100,
    sortable: true
},  {
    text: '状态',
    value: 'Status',
    Namevalue: "zt",
    width: 100,
    sortable: true
}, {
    text: '检验责任人',
    value: 'VerifyByName',
    Namevalue: "jyzrr",
    width: 120,
    sortable: true
}, {
    text: '有效期开始',
    value: 'ExpirationDateStart',
    Namevalue: "yxqks",
    width: 150,
    sortable: true
}, {
    text: '有效期结束',
    value: 'ExpirationDateEnd',
    Namevalue: "yxqjs",
    width: 150,
    sortable: true
}, {
    text: '最终完成日期',
    value: 'LastFinishDate',
    Namevalue: "zzwcrq",
    width: 150,
    sortable: true
}, {
    text: '数量',
    value: 'Qty',
    Namevalue: "sl",
    width: 120,
    sortable: true
}, {
    text: '开始日期',
    value: 'StartDate',
    Namevalue: "ksrq",
    width: 120,
    sortable: true
}, {
    text: '结束日期',
    value: 'FinishDate',
    Namevalue: "jsrq",
    width: 120,
    sortable: true
}, {
    text: '创建人',
    value: 'CreateUserName',
    Namevalue: "cjr",
    width: 120,
    sortable: true
}, {
    text: '创建时间',
    value: 'CreateDate',
    Namevalue: "jyzrr",
    width: 120,
    sortable: true
},{
    text: '操作',
    value: 'actions',
    Namevalue: "actions",
    width: 150,
    sortable: true
}]

export const InspectiontaskColum2 = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
}, {
    text: '编号',
    value: 'MeasureNo',
    Namevalue: "bh",
    width: 150,
    sortable: true
}, {
    text: '设备名称',
    value: 'Name',
    Namevalue: "sbmc",
    width: 150,
    sortable: true
}, {
    text: '规格',
    value: 'Model',
    Namevalue: "gg",
    width: 100,
    sortable: true
}, {
    text: '检验结果',
    value: 'Result',
    Namevalue: "jyjg",
    width: 100,
    sortable: true
}, {
    text: '检验状态',
    value: 'Status',
    Namevalue: "jyzt",
    width: 100,
    sortable: true
}, {
    text: '检验日期',
    value: 'CalibrateDate',
    Namevalue: "jyrq",
    width: 150,
    sortable: true
}, {
    text: '有效期',
    value: 'NewExpirationDate',
    Namevalue: "yxq",
    width: 100,
    sortable: true
}, {
    text: '合格证号',
    value: 'CertificateNo',
    Namevalue: "hgzh",
    width: 150,
    sortable: true
}, {
    text: '文件',
    value: 'Filename',
    Namevalue: "file",
    width: 150,
    sortable: true
}, {
    text: '检验周期',
    value: 'CalibrateCycle',
    Namevalue: "jyzq",
    width: 150,
    sortable: true
}, {
    text: '检定部门',
    value: 'VerifyDepartment',
    Namevalue: "jdbm",
    width: 120,
    sortable: true
}, {
    text: '使用部门',
    value: 'UsingDepartment',
    Namevalue: "sybm",
    width: 120,
    sortable: true
}, {
    text: '分部门',
    value: 'SubDepartment',
    Namevalue: "fbm",
    width: 120,
    sortable: true
}, {
    text: '放置地点',
    value: 'StorageLocation',
    Namevalue: "fzdd",
    width: 120,
    sortable: true
}, {
    text: '操作',
    value: 'actions',
    Namevalue: "actions",
    width: 150,
    sortable: true
}]


export const InspectiontaskColum3 = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
}, {
    text: '编号',
    value: 'MeasureNo',
    Namevalue: "bh",
    width: 150,
    sortable: true
}, {
    text: '设备名称',
    value: 'Name',
    Namevalue: "sbmc",
    width: 150,
    sortable: true
}, {
    text: '规格',
    value: 'Model',
    Namevalue: "gg",
    width: 100,
    sortable: true
}, {
    text: '检验结果',
    value: 'Result',
    Namevalue: "jyjg",
    width: 100,
    sortable: true
}, {
    text: '检验状态',
    value: 'Status',
    Namevalue: "jyzt",
    width: 100,
    sortable: true
}, {
    text: '检验日期',
    value: 'CalibrateDate',
    Namevalue: "jyrq",
    width: 150,
    sortable: true
}, {
    text: '有效期',
    value: 'NewExpirationDate',
    Namevalue: "yxq",
    width: 100,
    sortable: true
}, {
    text: '合格证号',
    value: 'CertificateNo',
    Namevalue: "hgzh",
    width: 150,
    sortable: true
},{
    text: '检验周期',
    value: 'CalibrateCycle',
    Namevalue: "jyzq",
    width: 150,
    sortable: true
}, {
    text: '检定部门',
    value: 'VerifyDepartment',
    Namevalue: "jdbm",
    width: 120,
    sortable: true
}, {
    text: '使用部门',
    value: 'UsingDepartment',
    Namevalue: "sybm",
    width: 120,
    sortable: true
}, {
    text: '分部门',
    value: 'SubDepartment',
    Namevalue: "fbm",
    width: 120,
    sortable: true
}, {
    text: '放置地点',
    value: 'StorageLocation',
    Namevalue: "fzdd",
    width: 120,
    sortable: true
}]
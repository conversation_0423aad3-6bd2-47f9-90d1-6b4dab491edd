<template>
    <div class="instances">
        <v-row class="tool-row">
            <v-col :cols="12" :lg="3">
                <a-input-search v-model="keywords" enter-button placeholder="Quick Search" @search="onSearch" />
            </v-col>
            <v-col :cols="12" :lg="9" class="pl-0">
                <v-btn @click="getdata">
                    <v-icon left>mdi-cached</v-icon>
                    Refresh</v-btn>
                <v-btn class="ml-3" @click="addInstance()" color="primary">
                    <v-icon left>mdi-plus</v-icon>
                    New</v-btn>
                <!-- <v-btn class="ml-3">
                    <v-icon left>mdi-download</v-icon>
                    Export</v-btn>
                <v-btn class="ml-3" color="primary">
                    <v-icon left>mdi-upload</v-icon>
                    Import</v-btn> -->
            </v-col>
        </v-row>
        <div class="table" style="margin-top:10px">
            <vxe-table height="500px" :loading="loading" size="mini" border resizable ref="table" :data="tableList">
                <vxe-column v-for="(column, index) in instancesColumns" :key="index" :field="column.field"
                    :title="column.title" :width="column.width">
                    <template #default="{ row }">
                        <span v-if="column.field == 'EquipmentId'">
                            {{ getEquipText(row.EquipmentId) }}
                        </span>
                        <span v-else-if="column.field == 'Action'">
                            <v-icon @click="handleDel(row)" style="cursor: pointer;" color="#3dcd58"
                                size="18">mdi-delete</v-icon>
                        </span>
                        <span v-else-if="column.field == 'text'">
                            <v-icon @click="handleOpenProperty(row)" style="cursor: pointer;" color="#3dcd58"
                                size="18">mdi-clipboard-text-outline</v-icon>
                        </span>
                        <span style="display:inline-block;width:85px"
                            v-else-if="['IsEnabled', 'Notifications'].indexOf(column.field) !== -1">
                            <span class="enabled" v-if="row[column.field] == 'true'">True</span>
                            <span class="disabled" v-if="row[column.field] == 'false'">False</span>
                        </span>
                        <span v-else>{{ row[column.field] }}</span>
                    </template>
                </vxe-column>
            </vxe-table>
        </div>

        <a-modal :visible="isShowPopup" :title="popupTitle" @cancel="handleCancel" @ok="handleOk">
            <functionInstancesPopup v-if="isShowPopup" :funcId="currentObj.ID" :machineList="machineList"
                ref="functionInstancesPopup">
            </functionInstancesPopup>
        </a-modal>
    </div>
</template>

<script>
import { instancesColumns } from '@/columns/factoryPlant/Opc.js'
import { getFunctionInstanceListById, delFunctionInstance, addFunctionInstance } from '../service'
import functionInstancesPopup from './functionInstancesPopup.vue'
import Util from '@/util';
export default {
    components: {
        functionInstancesPopup
    },
    props: {
        currentObj: {
            type: Object,
            default: () => { }
        }
    },
    inject: ['openProperty'],
    data() {
        return {
            popupTitle: 'New Function Instance',
            isShowPopup: false,
            instancesColumns,
            loading: false,
            keywords: '',
            tableList: [],
            machineList: []
        }
    },
    async created() {
        this.getdata()
        this.machineList = await Util.GetEquipmenByLevel('Unit')
    },
    methods: {
        handleOpenProperty(data) {
            let obj = data
            obj.FunctionName = this.currentObj.Name
            this.openProperty(obj)
        },
        handleCancel() {
            this.isShowPopup = false
        },
        async handleOk() {
            let params = this.$refs.functionInstancesPopup.form
            const resp = await addFunctionInstance(params)
            this.isShowPopup = false
            this.$nextTick(() => {
                this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });
                this.getdata()
            })
        },
        addInstance() {
            this.isShowPopup = true
        },
        handleDel(data) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: '确认要删除此项吗？',
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await delFunctionInstance([data.ID]);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.getdata()
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        getEquipText(id) {
            return this.machineList.find(item => item.ID == id)?.EquipmentName || ''
        },
        onSearch() {
            this.getdata()
        },
        async getdata() {
            let resp = await getFunctionInstanceListById({ OpcFunctionId: this.currentObj.ID, key: this.keywords })
            this.tableList = resp.response
        }
    }
}
</script>

<style lang="scss" scoped>
.tool-row {
    background: #f5f5f5;
    padding: 0 3px;
}

.ant-input-search {
    ::v-deep [type=button] {
        background: #3dcd58;
        border-color: #3dcd58;
    }
}

::v-deep .primary {
    background: #3dcd58 !important;
}

.ml-3 {
    margin-left: 6px;
}

.enabled {
    padding: 1px 5px 2px;
    border-radius: 2px;
    color: #fff;
    background: #008000;
}

.disabled {
    padding: 1px 5px 2px;
    border-radius: 2px;
    color: #fff;
    background: #ff0000;
}
</style>
import { DeviceMcProjectImport, maintainRuleImport, spockRuleImport } from '@/api/equipmentManagement/upkeep.js'
import { DeviceRepairProjectImport } from '@/api/equipmentManagement/equipmentReasonTree.js'
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE]['baseURL_TPM']
const templateUrl = {
    maintenanceItem: `${baseURL}/tpm/DeviceMcProject/ImportExcelTemplates`,
    spotCheckItem: `${baseURL}/tpm/DeviceMcProject/ImportExcelTemplates`,
    maintenanceRules: `${baseURL}/tpm/MaintainRule/ImportExcelTemplates`,
    spotCheckRules: `${baseURL}/tpm/DeviceMcRule/ImportExcelTemplates`,
    equipmentReasonTree: `${baseURL}/tpm/DeviceRepairProject/ImportExcelTemplates`,
}
const importApis = {
    maintenanceItem: DeviceMcProjectImport,
    spotCheckItem: DeviceMcProjectImport,
    maintenanceRules: maintainRuleImport,
    spotCheckRules: spockRuleImport,
    equipmentReasonTree: DeviceRepairProjectImport
}
export default {
    data() {
        return {
            importLoading: false
        }
    },
    methods: {
        templateDownload(key) {
            window.open(templateUrl[key], '_blank');
        },
        handleImport(key) {
            let _this = this;
            let input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xls,.xlsx';
            input.click();
            input.onchange = async function () {
                let file = input.files[0];
                let formdata = new FormData();
                formdata.append('file', file);

                _this.importLoading = true;
                try {
                    await importApis[key](formdata);
                    _this.$store.commit('SHOW_SNACKBAR', { text: '导入成功', color: 'success' });
                    _this.getdata();
                    _this.importLoading = false;
                } catch {
                    _this.importLoading = false;
                }
            };
        },
    },
}
<template>
  <div style="width: 100%;height: 100%;">
    <div
      :style="item.style"
      v-for="(item,index) in divsData"
      :key="index"
      :id="`chart-${index+1}`"
      class="box123"
    >

      <!-- 渐变色柱状图 -->
      <template v-if="item.type == 'bar' && item.Order == index+1">
        <div style="color: #fff;">{{ item.title }}</div>
        <barChart
          :id1="`chart-${index+1}`"
          :list="item.listData"
        />
      </template>
      <!-- 横向柱状图 -->
      <template v-if="item.type == 'hxbar' && item.Order == index+1">
        <div style="color: #fff;">{{ item.title }}</div>
        <barTransverseChart
          :key="index+1"
          :id1="`chart-${index+1}`"
          :list="item.listData"
          :styles="barStyle"
        />
      </template>
      <!-- 折线图 -->
      <template v-if="item.type == 'line' && item.Order == index+1">
        <div style="color: #fff;">{{ item.title }}</div>
        <lineChart
          :key="index+1"
          :id1="`chart-${index+1}`"
          :list="item.listData"
        />
      </template>

      <!-- 饼图 -->
      <template v-if="item.type == 'pie' && item.Order == index+1">
        <div style="color: #fff;">{{ item.title }}</div>
        <pieChart
          :key="index+1"
          :id1="`chart-${index+1}`"
          :list="item.listData"
        />
      </template>
      <!-- 表格 -->
      <template v-if="item.type == 'table' && item.Order == index+1">
        <div style="color: #fff;font-size: 18px;font-weight: bold;">{{ item.title }}</div>
        <tableCom
          :key="index+1"
          :id1="`chart-${index+1}`"
          :list="item.listData"
        />
      </template>

      <!-- 旋转展示 -->
      <template v-if="item.type == 'rotate' && item.Order == index+1">
        <div style="color: #fff;font-size: 18px;font-weight: bold;">{{ item.title }}</div>
        <rotateChart
          :key="index+1"
          :id1="`chart-${index+1}`"
          :list="item.listData"
        />
      </template>
      <!-- 仪表盘展示 -->
      <template v-if="item.type == 'meter' && item.Order == index+1">
        <div style="color: #fff;font-size: 18px;font-weight: bold;">{{ item.title }}</div>
        <meterChart
          :key="index+1"
          :id1="`chart-${index+1}`"
          :list="item.listData"
        />
      </template>
      <!-- tab区域展示 -->
      <template v-if="item.type == 'tab' && item.Order == index+1">
        <!-- <div style="color: #fff;font-size: 18px;font-weight: bold;">{{ item.title }}</div> -->
        <tabChart
          :key="index+1"
          :list="item.listData"
        />
      </template>
      <!-- 安全十字展示 -->
      <template v-if="item.type == 'security' && item.Order == index+1">
        <div style="color: #fff;font-size: 18px;font-weight: bold;">{{ item.title }}</div>
        <securityChart :searchFormObj="searchFormObj"></securityChart>
      </template>
      <!-- 文字描述展示 -->
      <template v-if="item.type == 'mattersNeeding' && item.Order == index+1">
        <div style="color: #fff;font-size: 18px;font-weight: bold;">{{ item.title }}</div>
        <mattersNeeding
          :key="index+1"
          :id1="`chart-${index+1}`"
          :list="item.listData"
        />
      </template>
      <!-- 列别组件 -->
      <template v-if="item.type == 'columnChart' && item.Order == index+1">
        <div style="color: #fff;font-size: 18px;font-weight: bold;">{{ item.title }}</div>
        <columnChart
          :key="index+1"
          :id1="`chart-${index+1}`"
          :list="item.listData"
        />
      </template>
    </div>
  </div>
</template>
<script>
export default {
  components: {
    barChart: () => import('@/views/simManagement/simNew1/components/barChart.vue'),
    lineChart: () => import('@/views/simManagement/simNew1/components/lineChart.vue'),
    pieChart: () => import('@/views/simManagement/simNew1/components/pieChart.vue'),
    tableCom: () => import('@/views/simManagement/simNew1/components/tableCom.vue'),
    barTransverseChart: () => import('@/views/simManagement/simNew1/components/barTransverseChart.vue'),
    // barStereoscopicChart: () => import('@/views/simManagement/simNew1/components/barStereoscopicChart.vue'),
    rotateChart: () => import('@/views/simManagement/simNew1/components/rotateChart.vue'),
    meterChart: () => import('@/views/simManagement/simNew1/components/meterChart.vue'),
    securityChart: () => import('@/views/simManagement/simNew1/components/securityChart.vue'),
    // searchPage: () => import('@/views/simManagement/simNew1/components/searchPage.vue'),
    tabChart: () => import('@/views/simManagement/simNew1/components/tabChart.vue'),
    mattersNeeding: () => import('@/views/simManagement/simNew1/components/mattersNeeding.vue'),
    columnChart: () => import('@/views/simManagement/simNew1/components/columnChart.vue'),
  },
  data: () => ({
    divsData: [
      { style: { width: '100%', height: '33%', backgroundSize: '100%', padding: '15px', boxSizing: 'border-box', marginBottom: '16px', }, title: '柱状图', type: "bar", Order: 1, listData: [10, 20, 30, 40] },
      // { style: { width: '100%', height: '33%', backgroundSize: '100%', padding: '10px', boxSizing: 'border-box', marginBottom: '16px', }, title: '旋转', type: "rotate", Order: 2, listData: [10, 20, 320, 40] },
      {
        style: { width: '100%', height: '33%', backgroundSize: '100%', padding: '15px', boxSizing: 'border-box', marginBottom: '16px', }, title: 'tab切换', type: "tab", Order: 2, listData: [{ tabName: "设备", id: 0 }, { tabName: "质量", id: 1 }, { tabName: "生产", id: 2 }]
      },
      {
        style: { width: '100%', height: '33%', backgroundSize: '100%', padding: '15px', boxSizing: 'border-box', marginBottom: '16px', }, title: '安全十字图', type: "line", Order: 3, listData: [
          { value: 1048, name: 'Search Engine' },
          { value: 735, name: 'Direct' },
          { value: 580, name: 'Email' },
          { value: 484, name: 'Union Ads' },
          { value: 300, name: 'Video Ads' }
        ]
      },
    ],
  })
}
</script>
<style lang="scss" scoped>
* {
    margin: 0;
    padding: 0;
}
.zBox {
    width: 100%;
    height: 100%;
    // padding: 2px 20px;
    // box-sizing: border-box;
    background: url('../image/bj.png') no-repeat 0 0;
    background-size: 100% 100% !important;
}
.imgBox {
    width: 40px;
    height: 40px;
    margin-top: 10px;
}
.searchBox {
    width: 100%;
    height: 5%;
    color: #fff;
    margin-top: 15px;
}
.charBox {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
    height: calc(100% - 10.5%);
}
.searchBox {
    flex: 1.06;
}
.charTitBox {
    flex: 1;
    text-align: center;
    line-height: 60px;
    font-size: 30px;
    font-weight: bold;
    background: url('../image/titimg.png') no-repeat 0 0;
    background-size: 100% 100% !important;
    letter-spacing: 1px;
}
.timeBox {
    color: #fff;
    font-size: 16px;
    flex: 1;
    line-height: 60px;
    text-align: right;
}
.box123 {
    background: url('../image/bk10.png') no-repeat 0 0;
    background-size: 100% 100% !important;
    // overflow: hidden;
    // overflow-y: auto;
}
</style>
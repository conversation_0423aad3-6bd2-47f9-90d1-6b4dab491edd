<template>
  <div>
    <div style="display: flex;width: 100%;height: 30px;">
      <!-- <div
        style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;cursor: pointer;"
        @click="routeChange()"
      >{{ title1 }}</div> -->
      <div
        class="titimgbox"
        @click="routeChange()"
      >
        <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
        <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ title1 }}</div>
      </div>
      <div style="width: 30%;display: flex;">
        <dayMonIndex
          :key="dayMonIndexkey"
          :simlevel='simlevel'
          :position="Order"
          @showChack="getExcelbar"
          @showChack1="getExcelbar1"
          :Dimension="Dimension"
          :Particle="Particle"
          :BaseTime="BaseTime"
          :id1="id1"
          :echarstType="2"
          :title="title"
          :backgroundImg="backgroundImg"
          :legendData="legendData"
          :resultArray1="resultArray1"
          :colorList="colorList"
          :cxStatus="cxStatus"
        />
      </div>
    </div>
    <div
      ref="barRef"
      :id="id1"
      style="width:100%;height:100%;"
    ></div>
    <!-- <div
      v-if="!showData"
      style="font-size: 14px;color: #fff;text-align: center;margin-top: -100px;"
    >暂无数据</div> -->
    <!-- <keyIndicatorslist
      ref="keyIndicatorsref"
      :exhibitionType="exhibitionType"
      :jtitle="title"
      :simlevel="simlevel"
      :Order="Order"
      :isSql="0"
      :BaseTime="BaseTime"
      :barName="barName"
    ></keyIndicatorslist> -->

    <keyIndicatorslistnew
      :key="keyIndicatorslistnewkey"
      ref="keyIndicatorsrefnew"
      v-if="keyIndicatorslistnewShow"
      v-model="showkeyIndicatorslistnew"
      :exhibitionType="exhibitionType"
      :jtitle="title"
      :simlevel="simlevel"
      :Order="Order"
      :isSql="isSql1"
      :BaseTime2="BaseTime"
      :BaseTime="BaseTime1"
      :barName="barName"
      :backgroundImg="backgroundImg"
      :Particle="Particle1"
      :legendData="legendData"
      :seriesName1="seriesName1"
      @keynew="heandleKeybar"
    ></keyIndicatorslistnew>

  </div>
</template>
<script>
// import { getChartStructure } from '@/api/simConfig/simconfignew.js';
import { getqueryZ, getqueryLcr, getChartStructure, getTableList } from '@/api/simConfig/simconfignew.js';
import { legend, textStyle } from 'echarts/lib/theme/dark';
import { color } from 'echarts/lib/theme/light';

export default {
  components: {
    dayMonIndex: () => import('@/views/simManagement/simNew1/components/dayMonIndex.vue'),
    // keyIndicatorslist: () => import('@/views/simManagement/simNew1/components/keyIndicatorslist.vue'),
    keyIndicatorslistnew: () => import('@/views/simManagement/simNew1/components/keyIndicatorslistnew.vue'),
  },
  props: {
    id1: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    Order: {
      type: String,
      default: ''
    },
    configFlag: {
      type: String,
      default: ''
    },
    exhibitionType: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    Dimension: {
      type: Array,
      default: () => []
    },
    routeList: {
      type: String,
      default: ''
    },
    backgroundImg: {
      type: String,
      default: ''
    },
    colorList: {
      type: Array,
      default: () => []
    },
    cxStatus: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    result1: false,
    seriesName1: '',
    labelShow: false,
    legendData1: [],
    BaseTime1: '',
    dayMonIndexkey: 0,
    resultArray1: [],
    keyIndicatorslistnewkey: 0,
    isSql1: '',
    Particle1: '',
    legendDatanew: [],
    legendData: [],
    title1: '',
    keyIndicatorslistnewShow: false,
    showkeyIndicatorslistnew: false,
    barName: '',
    showData: true,
    Particle: '',
    jtitle: '',
    // BaseTime: '2024-07-01',
    // TeamCode: 'A1180240614',
    // ProductionLineCode: 'A11802406',
    // FactoryCode: 'A118024',
    curConfig: {},
    chartBar: null,
    styless: {
      width: '100%',
      height: '100%',
      backgroundImage: 'url("https://img1.baidu.com/it/u=2756664614,3290369440&fm=253&fmt=auto&app=138&f=JPEG?w=753&h=500")',
      backgroundSize: '100%',

    },
    //当前时间颗粒度
    curShift: {
      KpiValues: []
    },
    myShiftList: [],
    id: '',
    yAxisOption: {
      type: 'value',
      // show: false
      axisLine: {
        show: false
      },
      axisLabel: {
        show: true
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
  }),
  computed: {
    //x轴配置
    xAxisOption() {
      let list = ['7', '6', '5', '4', '3', '2', '1']
      if (this.curShift?.ChartData && this.curShift?.ChartData.x) {
        list = this.curShift.ChartData.x.map(item => {
          // let month = dayjs(item).$M+1
          // let day = dayjs(item).$D
          if (['日', '周'].includes(this.curShift.TimeDimension)) {
            let month = item.split('-')[1]
            let day = item.split('-')[2]
            // 这里需要匹配颗粒度,来输出不同的x轴数据.
            // return `${month}-${day}`
            return `${item.split('-')[0]}-${month}-${day}`
          } else if (['月', '季度'].includes(this.curShift.TimeDimension)) {
            // let label = item.split('年')[1]
            let label = item.split('年')[0] + '-' + item.split('年')[1].split('月')[0]
            return label
          } else {
            return item
          }
        })
        // list.reverse()
        // list = this.curShift.ChartData.x
      }
      return {
        type: 'category',
        // boundaryGap: false,
        axisLine: {
          onZero: false,
          lineStyle: {
            color: '#fff'
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff'
        },
        data: list
      }
    },
    lineSeries() {

      if (this.curShift.ChartData.x.length <= 0) {
        this.$nextTick(() => {
          const dom = document.getElementById(this.id1);
          dom.innerHTML = '<div class="noDataBox">暂无数据</div>';
          dom.removeAttribute('_echarts_instance_');
          return
        })
      }
      let dataObject = []
      if (localStorage.getItem('list') != null) {
        dataObject = JSON.parse(localStorage.getItem('list'));
      }
      const resultArray = [];
      if (dataObject[this.Order]) {
        resultArray.push(...dataObject[this.Order]);
        [...new Set(resultArray)]
      }
      this.resultArray1 = [...new Set(resultArray)] // eslint-disable-line

      //区分横竖
      let axisMarkLine = this.curConfig.ChartType === '2'
        ? [{ xAxis: this.curShift?.TargetValue || '' }]
        : [{ yAxis: this.curShift?.TargetValue || '' }]
      let list = []
      let legendData = []
      Object.keys(this.curShift.ChartData).map(el => {
        legendData.push(el)
        const sorted = legendData.sort((a, b) => {
          const aParts = a.split(';');
          const bParts = b.split(';');
          const mainPartA = aParts.slice(0, -1).join(';');
          const mainPartB = bParts.slice(0, -1).join(';');
          return mainPartA.localeCompare(mainPartB);
        });
        this.legendData = [...new Set(sorted)]
        this.legendData1 = [...new Set(sorted)]
        //   this.legendData = this.legendData.filter(item => {
        //   return!item.includes('目标值') &&!item.includes('上限') &&!item.includes('下限');
        // });

      })
      console.log(this.legendData, 'resultArray1resultArray1resultArray1resultArray1resultArray1');
      let result = [];
      this.legendData1.map(item => {
        this.resultArray1.map(el => {
          // if (item.includes(el)) {
          //   result.push(item);
          // }
          if (item.includes(el + '_')) {
            result.push(item);
          } else if (item == el) {
            result.push(item);
          }
        })
      });
      const result2 = [];
      result.map(item => {
        if (result2.indexOf(item) === -1) {
          result2.push(item);
        }
      });
      this.resultArray1 = result2 // eslint-disable-line

      console.log(this.resultArray1, 'this.resultArray1123456789');


      if (this.resultArray1.length > 0) {
        this.labelShow = true // eslint-disable-line
        this.legendData = this.legendData.filter(item => { // eslint-disable-line
          return !item.includes('目标值') && !item.includes('上限') && !item.includes('下限');
        });
        var colors = this.colorList
        this.dayMonIndexkey++ // eslint-disable-line
        this.resultArray1.map((key, index) => {
          // if (['x', 'x', '目标值'].includes(key)) {
          //   return
          // }





          let values
          let targetValues
          if (!(key.includes('_目标值'))) {
            const targetField = key + "_目标值";
            values = this.curShift.ChartData[key];
            targetValues = this.curShift.ChartData[targetField];
            console.log(this.curShift.ChartData[targetField]);

          }

          var that = this





          let obj = {
            // name: `${this.curShift.KpiName}实际值`,
            // name: key.split(':')[1],
            name: `${key}`,
            type: ['2', '3'].includes(this.curConfig.ChartType) ? 'bar' : 'line',
            symbol: 'circle',
            symbolSize: 14,
            itemStyle: {
              normal: {
                // color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                //   offset: 0,
                //   color: "#4391F4" // 0% 处的颜色
                // }, {
                //   offset: 1,
                //   color: "#6B74E4" // 100% 处的颜色
                // }], false),
                // color: function (params) {
                //   let r, g, b;
                //   do {
                //     r = Math.floor(Math.random() * 256);
                //     g = Math.floor(Math.random() * 256);
                //     b = Math.floor(Math.random() * 256);
                //   } while ((r > 200 && g < 50 && b < 50) || // 类似红色的范围判断
                //   (r > 150 && g < 100 && b < 100) ||
                //     (r > 100 && g < 150 && b < 150));
                //   return `rgb(${r},${g},${b})`;
                // },
                // color: colors[index % colors.length],
                color: function (params) {
                  // console.log(that.curShift.ChartData, 'paramsparamsparamsparamsparams');

                  if (targetValues != undefined) {
                    // console.log(targetValues, 'paramsparamsparams');
                    const currentValue = params.value;
                    const targetValue = targetValues[0];
                    console.log(that.cxStatus, 'TargetValueTargetValueTargetValueTargetValuekkkkkkkkkkkkqqqqqqqqqqqqqqqq');
                    console.log(currentValue, targetValue, 'currentValue > targetValuecurrentValue > targetValue');
                    if (that.cxStatus == '大于') {
                      if (currentValue > targetValue) {
                        return 'yellow';
                      } else {
                        return 'rgb(76, 209, 55)';
                      }
                    }
                    if (that.cxStatus == '小于') {
                      if (currentValue < targetValue) {
                        return 'yellow';
                      } else {
                        return 'rgb(76, 209, 55)';
                      }
                    }
                    // if (currentValue > targetValue) {
                    //   return 'red'
                    // } else {
                    //   return colors[index % colors.length];
                    // }

                  } else {
                    return colors[index % colors.length];
                  }


                  // let currentValue
                  // let currentValue1
                  // let abc
                  // let abc1
                  // if (key.includes('_目标')) {
                  //   if (params.seriesType == 'line') {
                  //     currentValue1 = params.value;
                  //   }
                  // }
                  // if (params.seriesType == 'bar') {
                  //   abc = params.seriesName;
                  //   // abc1 = params.value;

                  //   if (that.curShift.ChartData[abc][params.dataIndex] < currentValue1) {
                  //     return that.cxColor;
                  //   } else {
                  //     return colors[index % colors.length];
                  //   }
                  // }

                },
                barBorderRadius: [4, 4, 4, 4],
              }
            },

            // data: this.curShift.KpiValues.map(item=>item.DataValue),
            data: this.curShift.ChartData[key],
            label: {
              show: this.labelShow,
              position: 'top',
              textStyle: {
                color: '#fff'
              }
            },
            // markLine: {//目标值线条
            //   silent: true,
            //   lineStyle: {
            //     color: this.curShift.TargetColor || 'gray'
            //     // color: 'red'
            //   },
            //   data: axisMarkLine
            //   // data: [{xAxis: 20 }]
            // }
          }
          list.push(obj)
        })
      }
      if (this.legendDatanew.length <= 0 && this.resultArray1.length <= 0) {
        // console.log(this.legendData, 'this.legendData123');


        // 111
        // const filteredArray = this.legendData.filter(item => item !== 'x');
        // const dataLength = filteredArray.length;
        // // if (dataLength < 2) {
        // //   this.checkResult = false;
        // //   return;
        // // }

        // let allContained = true;
        // for (let i = 0; i < dataLength - 1; i++) {
        //   const element = filteredArray[i];
        //   let isContained = false;
        //   for (let j = i + 1; j < dataLength; j++) {
        //     if (filteredArray[j].indexOf(element) !== -1) {
        //       isContained = true;
        //       break;
        //     }
        //   }
        //   if (!isContained) {
        //     allContained = false;
        //     break;
        //   }
        // }
        // this.result1 = allContained; // eslint-disable-line 

        // 222




        // console.log(this.result1, this.legendData.length, 'this.result1this.result1');
        if (this.result1 == true) {
          this.labelShow = true // eslint-disable-line 
        } else if (this.legendData.length == 2) {
          this.labelShow = true // eslint-disable-line
        } else {
          this.labelShow = false // eslint-disable-line
        }

        var colors1 = this.colorList
        if (this.curShift?.ChartData) {
          Object.keys(this.curShift?.ChartData).map((key, index) => {
            legendData.push(key)
            const sorted = legendData.sort((a, b) => {
              const aParts = a.split(';');
              const bParts = b.split(';');
              const mainPartA = aParts.slice(0, -1).join(';');
              const mainPartB = bParts.slice(0, -1).join(';');
              return mainPartA.localeCompare(mainPartB);
            });
            this.legendData = [...new Set(sorted)]
            this.legendData1 = [...new Set(sorted)]
            this.legendData = this.legendData.filter(item => {
              return !item.includes('目标值') && !item.includes('上限') && !item.includes('下限');
            });

            if (['x', 'x', '目标值'].includes(key)) {
              return
            }


            let values
            let targetValues
            if (!(key.includes('_目标值'))) {
              const targetField = key + "_目标值";
              values = this.curShift.ChartData[key];
              targetValues = this.curShift.ChartData[targetField];
              // console.log(this.curShift.ChartData[targetField]);

            }

            var that = this



            let obj = {
              // name: `${this.curShift.KpiName}实际值`,
              // name: key.split(':')[1],
              name: `${key}`,
              type: ['2', '3'].includes(this.curConfig.ChartType) ? 'bar' : 'line',
              symbol: 'circle',
              symbolSize: 14,
              itemStyle: {
                normal: {
                  color: function (params) {
                    if (targetValues != undefined) {
                      // console.log(targetValues, 'paramsparamsparams');
                      const currentValue = params.value;
                      const targetValue = targetValues[0];
                      // console.log(that, 'TargetValueTargetValueTargetValueTargetValuekkkkkkkkkkkkqqqqqqqqqqqqqqqq');
                      console.log(currentValue > targetValue, 'currentValue > targetValuecurrentValue > targetValue');
                      if (currentValue > targetValue) {
                        return 'red'
                      } else {
                        return colors[index % colors.length];
                      }
                    } else {
                      return color[index % color.length];
                    }


                    // let currentValue
                    // let currentValue1
                    // let abc
                    // let abc1
                    // if (key.includes('_目标')) {
                    //   if (params.seriesType == 'line') {
                    //     currentValue1 = params.value;
                    //   }
                    // }
                    // if (params.seriesType == 'bar') {
                    //   abc = params.seriesName;
                    //   // abc1 = params.value;

                    //   if (that.curShift.ChartData[abc][params.dataIndex] < currentValue1) {
                    //     return that.cxColor;
                    //   } else {
                    //     return colors[index % colors.length];
                    //   }
                    // }

                  },
                  // color: function (params) {
                  //   let r, g, b;
                  //   do {
                  //     r = Math.floor(Math.random() * 256);
                  //     g = Math.floor(Math.random() * 256);
                  //     b = Math.floor(Math.random() * 256);
                  //   } while ((r > 200 && g < 50 && b < 50) || // 类似红色的范围判断
                  //   (r > 150 && g < 100 && b < 100) ||
                  //     (r > 100 && g < 150 && b < 150));
                  //   return `rgb(${r},${g},${b})`;
                  // },
                  // color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  //   offset: 0,
                  //   color: "#4391F4" // 0% 处的颜色
                  // }, {
                  //   offset: 1,
                  //   color: "#6B74E4" // 100% 处的颜色
                  // }], false),
                  // color: colors1[index % colors1.length],
                  barBorderRadius: [4, 4, 4, 4],
                }
              },

              // data: this.curShift.KpiValues.map(item=>item.DataValue),
              data: this.curShift.ChartData[key],
              label: {
                show: this.labelShow,
                position: 'top',
                textStyle: {
                  color: '#fff'
                }
              },
              // markLine: {//目标值线条
              //   silent: true,
              //   lineStyle: {
              //     color: this.curShift.TargetColor || 'gray'
              //     // color: 'red'
              //   },
              //   data: axisMarkLine
              //   // data: [{xAxis: 20 }]
              // }
            }
            list.push(obj)
          })
        }
      }

      list.map(el => {
        if (el.name.includes('目标值') || el.name.includes('上限') || el.name.includes('下限')) {
          el.type = 'line'
          el.label.show = false
        }
      })
      return list
    },
  },
  created() {
    // console.log(this.Dimension, 'DimensionDimensionDimension');

    this.getBarList()
  },
  mounted() {

  },

  methods: {
    heandleKeybar() {
      this.keyIndicatorslistnewShow = false
    },
    routeChange() {
      if (this.routeList != '' && this.routeList != null && this.routeList != undefined) {
        this.$router.push({ path: `${this.routeList}` })
      }
      // this.$router.push({ path: 'simNew2', query: { code: item.Simcode } });
    },
    // openPopup() {
    //   if (this.configFlag == '是') {
    //     this.$refs.keyIndicatorsref.showDialog = true;
    //   }
    // },
    getExcelbar(data) {
      this.Particle = data
      this.getBarList()
    },
    getExcelbar1(data) {
      // console.log(data, 'data');
      // console.log(this.legendData1, 'this.legendData1');
      let result = [];
      this.legendData1.map(item => {
        // console.log(item, 'item');

        data.map(el => {
          if (item.includes(el + '_')) {
            result.push(item);
          } else if (item == el) {
            result.push(item);
          }
        })
      });
      // console.log(result, 'resultresult');

      // var data1 = []
      // this.legendData.map(item => {
      //   console.log(item, 'item');

      //   data.map(el => {
      //     console.log(el, 'el');

      //     if (el.includes(item)) {
      //       data1.push(item)
      //     }
      //   })
      // })
      // this.legendDatanew = result
      const result2 = [];
      result.map(item => {
        if (result2.indexOf(item) === -1) {
          result2.push(item);
        }
      });
      this.legendDatanew = result2
      // console.log(this.legendDatanew, 'this.legendDatanew ');

      this.getBarList()
    },
    async getBarList() {
      let params = {
        "Position": this.Order,
        "BaseTime": this.BaseTime,
        "TeamCode": this.simlevel,
        // "ProductionLineCode": this.ProductionLineCode,
        // "FactoryCode": this.FactoryCode
      }
      let { response } = await getChartStructure(params)
      this.curConfig = response
      if (this.curConfig.IsSql == '1') {
        this.getBarList1()
      } else {
        if (this.curConfig?.ChartConfigs != null) {
          if (this.curConfig.Unit == undefined || this.curConfig.Unit == '' || this.curConfig.Unit == null) {
            this.title1 = this.title
          } else {
            // this.title1 = this.title + '(' + this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit + ')'
            this.title1 = this.title + '(' + this.curConfig.Unit + ')'
          }
          // this.id = this.curConfig.ID;
          // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
          this.curConfig.ChartConfigs.map(item => {
            item.KpiName = this.curConfig.ChartConfigs.KpiName
            if (item.KpiValues[0]) {
              item.KpiCode = item.KpiValues[0].KpiCode
              item.TargetValue = item.KpiValues[0].TargetValue || 0
            }
          })
          //图表配置整体赋值
          // this.curConfig = response
          //时间颗粒度列表
          this.myShiftList = this.curConfig?.ChartConfigs.filter(item => {
            return item.TargetVisible === 1
          })
          //默认激活第一个时间颗粒度
          if (this.Particle != '') {
            this.myShiftList.map((el, index) => {
              if (this.Particle == el.TimeDimension) {
                this.curShift = this.myShiftList[index]
                this.$nextTick(() => {
                  this.query1(true)
                })
              } else {
                this.$nextTick(() => {
                  this.query1(true)
                })
              }
            })
          } else {
            this.curShift = this.myShiftList[0]
            this.Particle = this.curShift?.TimeDimension
            this.$nextTick(() => {
              this.query1(true)
            })
          }
        } else {
          this.title1 = this.title
        }
      }

    },
    async getBarList1() {
      let params = {
        "simLevel": this.Order.split('-')[0],
        "position": [
          this.Order
        ],
        "paramList": [
          this.simlevel,
          this.BaseTime
        ]
      }
      let { response } = await getTableList(params)
      this.curConfig = response
      if (this.curConfig?.ChartConfigs != null) {
        if (this.curConfig.Unit == undefined || this.curConfig.Unit == '' || this.curConfig.Unit == null) {
          this.title1 = this.title
        } else {
          // this.title1 = this.title + '(' + this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit + ')'
          this.title1 = this.title + '(' + this.curConfig.Unit + ')'
        }
        // this.id = this.curConfig.ID;
        // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
        this.curConfig.ChartConfigs.map(item => {
          item.KpiName = this.curConfig.ChartConfigs.KpiName
          if (item.KpiValues[0]) {
            item.KpiCode = item.KpiValues[0].KpiCode
            item.TargetValue = item.KpiValues[0].TargetValue || 0
          }
        })
        //图表配置整体赋值
        // this.curConfig = response
        //时间颗粒度列表
        this.myShiftList = this.curConfig?.ChartConfigs.filter(item => {
          return item.TargetVisible === 1
        })
        //默认激活第一个时间颗粒度
        if (this.Particle != '') {
          this.myShiftList.map((el, index) => {
            if (this.Particle == el.TimeDimension) {
              this.curShift = this.myShiftList[index]
              this.$nextTick(() => {
                this.query1(true)
              })
            } else {
              this.$nextTick(() => {
                this.query1(false)
              })
            }
          })
        } else {
          this.curShift = this.myShiftList[0]
          this.Particle = this.curShift.TimeDimension
          this.$nextTick(() => {
            this.query1(true)
          })
        }
      } else {
        this.title1 = this.title
      }

    },
    query1(data) {
      this.chartBar = this.$echarts.init(document.getElementById(this.id1));
      var option
      var that1 = this
      option = {
        symbol: 'circle',
        // tooltip: {
        //   trigger: 'axis',
        //   // extraCssText: 'z-index:99999',
        //   axisPointer: {
        //     type: 'shadow',
        //   },
        //   confine: true,
        //   extraCssText: 'max - width: none; overflow: visible;',
        // },
        tooltip: {
          axisPointer: {
            type: 'shadow',
          },
          confine: true,
          extraCssText: 'max - width: none; overflow: visible;',
          formatter: function (params) {
            // console.log(params, 'paramsparams');
            that.seriesName1 = params.seriesName

            if (that1.Particle == '日') {
              let abc = []
              let abc1 = []
              let name1 = ''
              if (that1.curConfig.listShift.length > 0) {
                that1.curConfig.listShift.map(el => {
                  if ((el.WorkDate.split(' ')[0].split('-')[1] + '-' + el.WorkDate.split(' ')[0].split('-')[2]) == params.name) {
                    abc1.push(el)
                  }
                })
              }
              let msg = ''
              Object.keys(that1.curConfig.ChartConfigs[0].ChartData).map((el, index) => {
                if (el.includes(params.seriesName + '_')) {
                  abc.push(el + '：' + that1.curConfig.ChartConfigs[0].ChartData[el][params.dataIndex])
                } else if (el == params.seriesName) {
                  abc.push(el + '：' + that1.curConfig.ChartConfigs[0].ChartData[el][params.dataIndex])
                }
              })
              abc1.map(el => {
                // console.log(params.seriesName.includes(el.WorkShift), 'params.seriesName.includes(el.WorkShift)');

                if (params.seriesName.includes(el.WorkShift)) {
                  name1 = params.seriesName + '-' + el.WorkNum
                }
                //  else {
                //   name1 = params.seriesName
                // }
              })
              // console.log(name1, 'name1name1name1');

              msg += (name1 == '' ? params.seriesName : name1) + '：' + params.value + "<br>";
              // console.log(abc, abc.slice(1), 'abc.slice(1)');

              // abc.slice(1).map((el, index) => {
              //   msg += el + "<br>";
              // })
              return msg
            } else {
              let msg = ''
              let arr = []
              let abc = []
              that1.curConfig.ChartConfigs.map((el, index) => {
                if (el.TimeDimension == that1.Particle) {
                  arr.push(el)
                }
              })
              arr.map((el, index) => {
                Object.keys(el.ChartData).map((el1, index1) => {
                  if (el1.includes(params.seriesName + '_')) {
                    abc.push(el1 + '：' + arr[0].ChartData[el1][params.dataIndex])
                  }
                })
              })
              msg += (params.seriesName + params.value) + "<br>";
              // msg += params.seriesName + '：' + params.value + "<br>";
              // abc.map((el, index) => {
              //   msg += el + "<br>";
              // })
              return msg
            }
          }
        },
        //   data: this.lineLegend
        // },
        grid: {
          left: '5%',
          right: '6%',
          bottom: '3%',
          top: '11%',
          containLabel: true
        },

        // visualMap: this.lineVisualMap,
        xAxis: this.xAxisOption,
        yAxis: {
          type: 'value',
          // show: false
          axisLine: {
            show: true,
            lineStyle: {
              color: '#fff'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff" //X轴文字颜色
            },
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        series: this.lineSeries
      }
      var that = this
      that.chartBar.off('click');
      that.chartBar.on('click', function (param) {
        // console.log(param, 'paramparamparam');

        that.BaseTime1 = param.name
        // if (that.configFlag == '是') {
        //   that.barName = param.name
        //   that.$refs.keyIndicatorsref.flagChange()
        //   that.$refs.keyIndicatorsref.showDialog = true;
        // }
        that.barName = param.seriesName

        // that.$refs.keyIndicatorsref.flagChange()
        // that.$refs.keyIndicatorsref.showDialog = true;
        that.Particle1 = that.Particle
        that.isSql1 = that.curConfig.IsSql
        that.keyIndicatorslistnewShow = true
        that.showkeyIndicatorslistnew = true
      });

      this.chartBar.setOption(option, true);
      window.addEventListener("resize", () => {
        this.chartBar.resize()
      }, false);
    },
    // query() {
    //   this.chartBar = this.$echarts.init(document.getElementById(this.id1));
    //   var option
    //   option = {
    //     title: {
    //       text: this.title,
    //       textStyle: { // 标题样式
    //         color: '#fff'
    //       }
    //     },
    //     tooltip: {
    //       trigger: "item"
    //     },
    //     legend: {
    //       right: 0,
    //       data: [
    //         {
    //           name: "目标值",
    //           textStyle: {
    //             color: "white"
    //           }
    //         },
    //         {
    //           name: "实际值",
    //           textStyle: {
    //             color: "white"
    //           }
    //         },
    //       ]
    //     },
    //     grid: {
    //       top: '15%',
    //       bottom: '1%',
    //       right: '2%',
    //       left: '5%',
    //       containLabel: true
    //     },
    //     toolbox: {
    //       show: true,
    //     },
    //     calculable: true,
    //     xAxis: [
    //       {
    //         type: 'category',
    //         axisLine: {
    //           lineStyle: {
    //             color: "#fff"
    //           }
    //         },
    //         splitLine: {
    //           show: false
    //         },
    //         data: ['10.10', '10.11', '10.12'],//['10.10', '10.11', '10.12', '10.13', '10.14', '10.15'],
    //         // data: this.nianData.map(item => item.MONTH),
    //         axisLabel: {
    //           show: true,
    //           textStyle: {
    //             color: "#fff" //X轴文字颜色
    //           },
    //         },
    //       }
    //     ],
    //     yAxis: [
    //       {
    //         type: 'value',
    //         splitLine: {
    //           show: false
    //         },
    //         axisLine: {
    //           lineStyle: {
    //             color: "#fff"
    //           }
    //         },
    //         axisLabel: {
    //           show: true,
    //           textStyle: {
    //             color: "#fff" //X轴文字颜色
    //           },
    //         },
    //       },
    //       // {
    //       //   //右边百分比部分
    //       //   name: '百分比',
    //       //   type: "value",
    //       //   position: "right",
    //       //   axisLine: {
    //       //     lineStyle: {
    //       //       color: "#fff"
    //       //     }
    //       //   },
    //       //   axisTick: {
    //       //     show: false,
    //       //   },

    //       //   axisLabel: {
    //       //     textStyle: {
    //       //       color: "#fff",
    //       //     },
    //       //     show: true,
    //       //     interval: "auto",
    //       //     formatter: "{value}%",
    //       //   },
    //       //   show: true,
    //       //   splitLine: {  //网格线
    //       //     show: false
    //       //   }
    //       // }
    //     ],
    //     series: [

    //       {
    //         name: '目标值',
    //         tooltip: {
    //           show: false
    //         },
    //         type: 'bar',
    //         barWidth: 10,
    //         itemStyle: {
    //           normal: {
    //             barBorderRadius: [4, 4, 4, 4],
    //             color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
    //               offset: 0,
    //               color: "#6B74E4" // 0% 处的颜色
    //             }, {
    //               offset: 1,
    //               color: "#6B74E4" // 100% 处的颜色
    //             }], false)
    //           }
    //         },
    //         // data: this.nianData.map(item => item.PLANVALUE),
    //         data: this.list,
    //         barGap: '30%'

    //       },
    //       {
    //         name: '实际值',
    //         tooltip: {
    //           show: false
    //         },
    //         type: 'bar',
    //         barWidth: 10,
    //         itemStyle: {
    //           normal: {
    //             barBorderRadius: [4, 4, 4, 4],
    //             color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
    //               offset: 0,
    //               color: "#4391F4" // 0% 处的颜色
    //             }, {
    //               offset: 1,
    //               color: "#4391F4" // 100% 处的颜色
    //             }], false)
    //           }
    //         },
    //         // data: this.nianData.map(item => item.OUTNUM),
    //         data: [5, 20, 10, 18, 20, 20],
    //         barGap: '30%'
    //       },
    //     ]
    //   };
    //   this.chartBar.setOption(option, true);
    //   window.addEventListener("resize", () => {
    //     this.chartBar.resize()
    //   }, false);
    // }
  },
}
</script>
<style lang="scss" scoped>
::v-deep .noDataBox {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
}
.titimgbox {
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    width: 70%;
    height: 30px;
    line-height: 30px;
    border-radius: 5px;
    /* background-image: linear-gradient(to right, #056be0 0%, #000b61 100%); */
    display: flex;
    /* border: 1px solid #fff; */
    /* box-shadow: 0px 0px 7px 0px #fff; */
    /* overflow: hidden; */
}
</style>
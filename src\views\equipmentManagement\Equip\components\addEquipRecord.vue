<template>
    <v-card>
        <v-card-title class="text-h6 justify-space-between primary lighten-2">
            <!-- 设备录入 -->
            {{ $t('GLOBAL._XZ') }}
            <v-icon @click="closePopup()">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid">
                <v-row class="mt-4">
                    <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <v-text-field v-model="form.Action" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.Behavior')"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <v-textarea v-model="form.Content" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.Remark')"></v-textarea>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions class="pa-5 lighten-3">
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="submitForm()">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn @click="closePopup()">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { HistorySaveForm } from '@/api/equipmentManagement/EquipParts.js';

export default {
    props: {
        rowtableItem: {
            type: Object,
            default: () => {}
        },
        editItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            valid: false,
            form: {
                DeviceId: '',
                Action: '',
                Content: '',
                Operator: '',
                OperateDate: new Date(),
                Remark:''
            }
        };
    },
    created() {
        if (this.editItem && this.editItem.ID) {
            for (const key in this.form) {
                this.form[key] = this.editItem[key];
            }
            this.form.ID = this.editItem.ID;
        }
    },
    methods: {
        async submitForm() {
            let resp = await HistorySaveForm(this.form);
            this.$store.commit('SHOW_SNACKBAR', { text: '保存成功！', color: 'success' });
            this.$emit('getdata');
            this.$nextTick(() => {
                this.$emit('closePopup');
            });
        },
        closePopup() {
            this.$emit('closePopup');
        }
    }
};
</script>

<style></style>

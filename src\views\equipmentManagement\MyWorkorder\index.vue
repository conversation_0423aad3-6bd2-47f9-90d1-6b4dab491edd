<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <!-- @click="RepastInfoGetPage" -->
                    <v-btn icon color="primary" @click="getTableData">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    :clickFun="clickFun"
                    ref="Tables"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_WDGD"
                    :headers="MyWorkorderColum"
                    :desserts="desserts"
                    @tableClick="tableClick"
                    @selectePages="selectePages"
                ></Tables>
                <createRepast ref="createRepast" :dialogType="dialogType" :tableItem="tableItem"></createRepast>
            </v-card>
            <el-drawer size="80%" :title="rowtableItem.RepairWo" :wrapperClosable="false" :visible.sync="detailShow" direction="rtl">
                <v-card class="ma-1">
                    <v-tabs v-model="tab" background-color="transparent">
                        <v-tab @click="changeTab(0)" key="0">{{ $t('TPM_SBGL_SBWXGD._BJ') }}</v-tab>
                        <v-tab @click="changeTab(1)" key="1">{{ $t('TPM_SBGL_SBWXGD._FWCG') }}</v-tab>
                    </v-tabs>
                    <v-tabs-items v-model="tab">
                        <v-tab-item><bj ref="bj"></bj></v-tab-item>
                        <v-tab-item><fwcg ref="fwcg" :rowtableItem="rowtableItem"></fwcg></v-tab-item>
                    </v-tabs-items>
                </v-card>
            </el-drawer>
            <el-dialog :title="addTitle" :visible.sync="addModel" width="30%">
                <div class="addForm" v-if="dialogType != 'gp'">
                    <v-text-field v-model="user" disabled outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.Creator') + '*'"></v-text-field>
                </div>
                <div class="addForm" v-if="dialogType != 'gp'">
                    <v-text-field v-model="userDate" :clearable="true" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.CreateDate') + '*'" readonly></v-text-field>
                    <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="userDate" type="datetime" :placeholder="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.CreateDate')"></el-date-picker>
                </div>
                <div class="addForm" v-if="dialogType == 'gp'">
                    <v-autocomplete
                        clearable
                        v-model="gpr"
                        :items="gprList"
                        item-text="ItemName"
                        item-value="ItemValue"
                        :label="$t('$vuetify.dataTable.TPM_SBGL_WDGD.jdr') + '*'"
                        clear
                        dense
                        outlined
                    ></v-autocomplete>
                </div>
                <div class="addForm" v-for="(item, index) in gpList" :key="index" v-show="dialogType == 'gp'">
                    <v-text-field v-model="item.value" :clearable="item.isClearable ? item.isClearable : true" outlined disabled dense :label="item.label" readonly></v-text-field>
                    <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="item.value" type="datetime" disabled :placeholder="item.label"></el-date-picker>
                </div>

                <span slot="footer" class="dialog-footer">
                    <el-button @click="addModel = false">取 消</el-button>
                    <el-button type="primary" @click="Save()">确 定</el-button>
                </span>
            </el-dialog>
            <el-dialog :title="$t('TPM_SBGL_WDGD._WXJL')" id="FlexForm" :visible.sync="RepairRecordModel" width="45%">
                <div class="addForm FlexAddForm" v-for="(item, index) in wxjlList" :key="index">
                    <v-text-field
                        :disabled="item.disabled"
                        v-if="item.type == 'number'"
                        :id="item.id + 'SbxxList'"
                        onkeyup="value=value.replace(/\D/g,'')"
                        type="text"
                        v-model="item.value"
                        outlined
                        dense
                        :label="item.label"
                    ></v-text-field>
                    <!-- RepairRecordDesc -->
                    <v-text-field v-if="item.type == 'RepairRecordDesc'" :id="item.id + 'SbxxList'" v-model="item.value" outlined dense :label="item.label">
                        <template v-slot:append>
                            <v-icon style="cursor: pointer" @click="getDescModelShow()">mdi-magnify</v-icon>
                        </template>
                    </v-text-field>

                    <v-text-field :disabled="item.disabled" v-if="item.type == 'input'" :id="item.id + 'SbxxList'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                    <v-autocomplete
                        v-if="item.type == 'select'"
                        :id="item.id + 'SbxxList'"
                        clearable
                        v-model="item.value"
                        :items="item.option"
                        item-text="ItemName"
                        item-value="ItemValue"
                        :label="item.label"
                        clear
                        dense
                        outlined
                    ></v-autocomplete>
                    <v-menu
                        v-if="item.type == 'date' || item.type == 'datetime'"
                        :ref="'menu' + index"
                        v-model="menu[index]"
                        :close-on-content-click="false"
                        :nudge-right="40"
                        transition="scale-transition"
                        offset-y
                        max-width="290px"
                        min-width="290px"
                    >
                        <template #activator="{ on, attrs }">
                            <v-text-field
                                v-model="item.value"
                                :clearable="item.isClearable ? item.isClearable : true"
                                outlined
                                dense
                                :label="item.label"
                                readonly
                                v-bind="attrs"
                                v-on="on"
                            ></v-text-field>
                        </template>
                        <v-date-picker :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></v-date-picker>
                    </v-menu>
                    <div class="textfieldbox">
                        <v-text-field
                            v-model="item.value"
                            :clearable="item.isClearable ? item.isClearable : true"
                            outlined
                            dense
                            v-if="item.type == 'time'"
                            :label="item.label"
                            readonly
                        ></v-text-field>
                        <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-if="item.type == 'time'" v-model="item.value" type="datetime" :placeholder="item.label"></el-date-picker>
                    </div>
                    <el-radio-group v-model="item.value" v-if="item.type == 'radio'">
                        <div class="textlabel">{{ item.label }}:</div>
                        <el-radio :label="it.value" :key="ind" v-for="(it, ind) in item.radiolist">{{ it.label }}</el-radio>
                    </el-radio-group>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="RepairRecordModel = false">取 消</el-button>
                    <el-button type="primary" @click="RepairRecordSave()">确 定</el-button>
                </span>
            </el-dialog>
            <el-dialog :title="$t('TPM_SBGL_WDGD._XGGGXX')" :visible.sync="gzxxModel" width="30%">
                <div class="addForm">
                    <v-autocomplete
                        clearable
                        v-model="gzxx"
                        :items="gzxxList"
                        item-text="ItemName"
                        item-value="ItemValue"
                        :label="$t('$vuetify.dataTable.TPM_SBGL_WDGD.gzxx') + '*'"
                        clear
                        dense
                        outlined
                    ></v-autocomplete>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="gzxxModel = false">取 消</el-button>
                    <el-button type="primary" @click="gzxxSave()">确 定</el-button>
                </span>
            </el-dialog>
            <el-dialog :title="$t('TPM_SBGL_WDGD._CJFWD')" :visible.sync="SaveFormModel" width="30%">
                <div class="addForm" v-for="(item, index) in SaveFormList" :key="index">
                    <v-text-field v-if="item.type == 'input'" :id="item.id + 'SbxxList'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                    <el-radio-group v-model="item.value" v-if="item.type == 'radio'">
                        <div class="textlabel">{{ item.label }}:</div>
                        <el-radio :label="it.value" :key="ind" v-for="(it, ind) in item.radiolist">{{ it.label }}</el-radio>
                    </el-radio-group>
                    <v-autocomplete
                        v-if="item.type == 'select'"
                        :multiple="item.multiple"
                        clearable
                        v-model="item.value"
                        :items="item.option"
                        item-text="ItemName"
                        item-value="ItemValue"
                        :label="item.label"
                        clear
                        dense
                        outlined
                    ></v-autocomplete>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="SaveFormModel = false">取 消</el-button>
                    <el-button type="primary" @click="SaveFormSave()">确 定</el-button>
                </span>
            </el-dialog>
            <v-dialog v-model="DescModel" max-width="1080px">
                <v-card>
                    <v-card-title class="text-h6 justify-space-between primary lighten-2">
                        <!-- 新增备品备件 -->
                        {{ $t('TPM_SBGL_WDGD.tjjy') }}
                        <v-icon @click="DescModel = false">mdi-close</v-icon>
                    </v-card-title>
                    <div class="EquipmentSearch">
                        <div class="addForm">
                            <v-text-field v-model="Description" outlined dense :label="$t('TPM_SBGL_WDGD.wtms')"></v-text-field>
                        </div>
                        <div class="addForm">
                            <v-text-field v-model="RepairRecordDesc" outlined dense :label="$t('TPM_SBGL_WDGD.wxgcms')"></v-text-field>
                        </div>
                        <div class="addForm">
                            <v-text-field v-model="Keyword" outlined dense :label="$t('TPM_SBGL_WDGD.gjz')"></v-text-field>
                        </div>
                        <div class="addForm">
                            <v-btn color="primary" @click="GetExperiencePageList()">{{ $t('GLOBAL._CX') }}</v-btn>
                        </div>
                        <div class="addForm">
                            <v-btn @click="ClearDesc()">{{ $t('GLOBAL._CZ') }}</v-btn>
                        </div>
                    </div>
                    <Tables
                        :footer="false"
                        :clickFun="descclickFun"
                        :page-options="pageOptions"
                        :showSelect="false"
                        :loading="loading2"
                        ref="BjTables"
                        :btn-list="btnList"
                        tableHeight="300px"
                        table-name="TPM_SBGL_WXJY"
                        :headers="ExperienceColum2"
                        :desserts="Descdesserts"
                    ></Tables>
                    <v-card-actions class="pa-5 lighten-3">
                        <v-btn color="primary" :disabled="JSON.stringify(CheckItem) == '{}'" @click="CheckRow()">{{ $t('GLOBAL._QD') }}</v-btn>
                        <v-btn @click="DescModel = false">{{ $t('GLOBAL._GB') }}</v-btn>
                    </v-card-actions>
                </v-card>
            </v-dialog>
        </div>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';
import {
    GetRepairRecordRepairRecord,
    GetRepairServiceSaveForm,
    GetRepairRecordPageList,
    GetRepairRecordReceive,
    GetRepairOrderSource,
    GetRepairOrderStatus,
    GetRepairRecordReject,
    GetRepairRecordReassign,
    GetRepairOrderType,
    GetRepairRecordSaveForm
} from '@/api/equipmentManagement/MyWorkorder.js';
import { MyWorkorderColum } from '@/columns/equipmentManagement/MyWorkorder.js';
import bj from './components/bj.vue';
import fwcg from './components/fwcg.vue';
import moment from 'moment';
import { Message, MessageBox } from 'element-ui';
import { GetPersonList } from '@/api/equipmentManagement/Equip.js';
import { GetExperienceList } from '@/api/equipmentManagement/Experience.js';
import { ExperienceColum2 } from '@/columns/equipmentManagement/Experience.js';

export default {
    name: 'RepastModel',
    components: {
        bj,
        fwcg,
        createRepast: () => import('./components/createRepast.vue')
    },
    data() {
        return {
            CheckItem: {},
            loading2: false,
            Descdesserts: [],
            ExperienceColum2,
            DescModel: false,
            Description: '',
            RepairRecordDesc: '',
            Keyword: '',
            // tree 字典数据
            detailShow: false,
            tab: 0,
            loading: false,
            showFrom: false,
            FaultPosition: [],
            FaultProperty: [],
            RepairProperty: [],
            gzxx: '',
            gzxxList: [],
            wxjlList: [
                {
                    label: this.$t('TPM_SBGL_WDGD.kscl') + ' *',
                    type: 'time',
                    require: true,
                    id: 'StartDate',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.ksjs') + ' *',
                    type: 'time',
                    require: true,
                    id: 'FinishDate',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.wxzgss'),
                    type: 'number',
                    id: 'RepairDuration',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.wxgcms') + ' *',
                    type: 'RepairRecordDesc',
                    id: 'RepairRecordDesc',
                    require: true,
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.yyfx'),
                    type: 'input',
                    id: 'Reason',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.yyfxjl'),
                    type: 'input',
                    id: 'ReasonResult',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.wxxz') + ' *',
                    type: 'select',
                    require: true,
                    option: [],
                    id: 'RepairNature',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.gzxz') + ' *',
                    require: true,
                    type: 'select',
                    option: [],
                    id: 'FaultNature',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.gzbw') + ' *',
                    require: true,
                    type: 'select',
                    option: [],
                    id: 'FaultCategory',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.wxsyby') + ' *',
                    require: true,
                    type: 'radio',
                    radiolist: [
                        {
                            label: '是',
                            value: '是'
                        },
                        {
                            label: '否',
                            value: '否'
                        }
                    ],
                    id: 'IsMaintained',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.zwwxjy') + ' *',
                    require: true,
                    type: 'radio',
                    radiolist: [
                        {
                            label: '是',
                            value: true
                        },
                        {
                            label: '否',
                            value: false
                        }
                    ],
                    id: 'IsExperience',
                    value: ''
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDGD.gzxx') + ' *',
                    require: true,
                    type: 'select',
                    option: [],
                    id: 'Phenomenon',
                    value: ''
                }
            ],
            papamstree: {
                RepairWo: '',
                Source: '',
                Type: '',
                Status: '',
                LineCode: '',
                ReportDateFrom: '',
                ReportDateTo: '',
                ModifyDateFrom:moment().weekday(1).format('YYYY/MM/DD'),
                ModifyDateTo: moment().weekday(7).format('YYYY/MM/DD'),
                ReceiveBy: this.$store.getters.getUserinfolist[0].LoginName,
                DeviceCode: '',
                DeviceName: '',
                orderByFileds: 'createdate desc',
                pageIndex: 1,
                pageSize: 20
            },
            gpList: [
                // {
                //     label: this.$t('TPM_SBGL_SBDXJH.qwkssj'),
                //     value: '',
                //     disabled: true,
                //     type: 'date',
                //     id: 'StartDate'
                // },
                // {
                //     label: this.$t('TPM_SBGL_SBDXJH.qwwcsj'),
                //     value: '',
                //     disabled: true,
                //     type: 'date',
                //     id: 'FinishDate'
                // },
                {
                    label: this.$t('TPM_SBGL_SBDXJH.jhksrq') + '*',
                    value: '',
                    disabled: true,
                    type: 'date',
                    require: true,
                    id: 'PlanStartDate'
                },
                {
                    label: this.$t('TPM_SBGL_SBDXJH.jhjsrq') + '*',
                    value: '',
                    disabled: true,
                    type: 'date',
                    require: true,
                    id: 'PlanFinishDate'
                }
            ],
            //查询条件
            MyWorkorderColum,
            desserts: [],
            StatusList: [],
            TypeList: [],
            RecordStatusList: [],
            SourceList: [],
            CarbonCopyData: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            user: this.$store.getters.getUserinfolist[0].LoginName,
            userDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
            addModel: false,
            addTitle: '',
            gpr: '',
            gprList: [],
            gzxxModel: false,
            rowtableItem: {},
            RepairMngData: [],
            ShifMngData: [],
            MaintenanceGroupData: [],
            RepairRecordModel: false,
            SaveFormModel: false,
            SaveFormList: [
                {
                    label: this.$t('TPM_SBGL_WDGD.xqyy') + ' *',
                    type: 'input',
                    require: true,
                    id: 'RequestReason',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.fwnr') + ' *',
                    type: 'input',
                    require: true,
                    id: 'RequestContent',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.xykxc') + ' *',
                    require: true,
                    type: 'radio',
                    radiolist: [
                        {
                            label: '是',
                            value: '是'
                        },
                        {
                            label: '否',
                            value: '否'
                        }
                    ],
                    id: 'IsLookSite',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.csr'),
                    type: 'select',
                    multiple: true,
                    option: [],
                    id: 'CarbonCopy',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.bz'),
                    type: 'input',
                    id: 'RequestRemark',
                    value: ''
                }
            ]
        };
    },
    computed: {
        btnList() {
            return [
                // showList: ['已派单'],
                // showKey: 'Status',
                // {
                //     text: this.$t('TPM_SBGL_WDGD._XGGGXX'),
                //     code: 'xgggxx',
                //     showList: ['进行中'],
                //     showKey: 'Status',
                //     type: 'primary',
                //     icon: '',
                //     authCode: ''
                // },
                {
                    text: this.$t('TPM_SBGL_WDGD._JD'),
                    code: 'jd',
                    type: 'primary',
                    showList: ['已派单'],
                    showKey: 'Status',
                    icon: '',
                    authCode: 'SBGLZY_WDGD_JD'
                },
                {
                    text: this.$t('TPM_SBGL_WDGD._GP'),
                    code: 'gp',
                    type: 'primary',
                    showList: ['已派单', '进行中'],
                    showKey: 'Status',
                    icon: '',
                    authCode: 'SBGLZY_WDGD_GP'
                },
                {
                    text: this.$t('TPM_SBGL_WDGD._TH'),
                    code: 'th',
                    showList: ['已派单', '进行中'],
                    showKey: 'Status',
                    type: 'red',
                    icon: '',
                    authCode: 'SBGLZY_WDGD_TH'
                },
                {
                    text: this.$t('TPM_SBGL_WDGD._WXJL'),
                    code: 'wxjl',
                    showList: ['进行中'],
                    showKey: 'Status',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_WDGD_WXJL'
                },
                {
                    text: this.$t('TPM_SBGL_WDGD._CJFWD'),
                    code: 'cjfwd',
                    showList: ['进行中', '待备件'],
                    showKey: 'Status',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_WDGD_CJFWD'
                }
            ];
        },
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'RepairWo',
                    label: this.$t('TPM_SBGL_WDBX.GDH'),
                    icon: 'mdi-account-check',
                    type: 'input',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'LineCode',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.LineCode'),
                    icon: 'mdi-account-check',
                    type: 'input',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Source',
                    label: this.$t('TPM_SBGL_WDGD._GDLY'),
                    icon: 'mdi-account-check',
                    selectData: this.SourceList,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Status',
                    label: this.$t('TPM_SBGL_WDGD._ZT'),
                    icon: 'mdi-account-check',
                    selectData: this.StatusList,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Type',
                    label: this.$t('TPM_SBGL_WDGD._GDLX'),
                    icon: 'mdi-account-check',
                    selectData: this.TypeList,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: this.$store.getters.getUserinfolist[0].LoginName,
                    key: 'ReceiveBy',
                    label: this.$t('TPM_SBGL_WDGD._JDR'),
                    icon: 'mdi-account-check',
                    selectData: this.gprList,
                    byValue: 'ItemValue',
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'DeviceName',
                    label: this.$t('TPM_SBGL_WDGD._SBMC'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'DeviceCode',
                    label: this.$t('TPM_SBGL_WDGD._SBBH'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'ReportDateFrom',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_WDGD._BXKSRQ'),
                    placeholder: this.$t('TPM_SBGL_WDGD._BXKSRQ')
                },
                {
                    value: '',
                    key: 'ReportDateTo',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_WDGD._BXJSRQ'),
                    placeholder: this.$t('TPM_SBGL_WDGD._BXJSRQ')
                },
                {
                    value: moment().weekday(1).format('YYYY/MM/DD'),
                    key: 'ModifyDateFrom',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_WDGD._GXKSRQ'),
                    placeholder: this.$t('TPM_SBGL_WDGD._GXKSRQ')
                },
                {
                    value: moment().weekday(7).format('YYYY/MM/DD'),
                    key: 'ModifyDateTo',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_WDGD._GXJSRQ'),
                    placeholder: this.$t('TPM_SBGL_WDGD._GXJSRQ')
                }
            ];
        }
    },
    async mounted() {
        let RepairMng = await GetPersonList('RepairMng');
        this.RepairMngData = RepairMng.response[0].ChildNodes;
        this.RepairMngData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        let ShiftMng = await GetPersonList('ShiftMng');
        this.ShifMngData = ShiftMng.response[0].ChildNodes;
        this.ShifMngData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        let MaintenanceGroup = await GetPersonList('MaintenanceGroup');
        this.MaintenanceGroupData = MaintenanceGroup.response[0].ChildNodes;
        this.MaintenanceGroupData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.gprList = this.MaintenanceGroupData;
        let RepairPhenomenon = await this.$getNewDataDictionary('RepairPhenomenon');
        this.wxjlList[this.wxjlList.length - 1].option = RepairPhenomenon;
        let CarbonCopy = await GetPersonList('CarbonCopy');
        this.CarbonCopyData = CarbonCopy.response[0].ChildNodes;
        this.CarbonCopyData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.SaveFormList[3].option = this.CarbonCopyData;
        this.FaultPosition = await this.$getNewDataDictionary('FaultPosition');
        this.FaultProperty = await this.$getNewDataDictionary('FaultProperty');
        this.RepairProperty = await this.$getNewDataDictionary('Repair Property');
        this.wxjlList.forEach(item => {
            switch (item.id) {
                case 'FaultCategory':
                    item.option = this.FaultPosition;
                    break;
                case 'FaultNature':
                    item.option = this.FaultProperty;
                    break;
                case 'RepairNature':
                    item.option = this.RepairProperty;
                    break;
            }
        });
        this.RecordStatusList = await this.$getNewDataDictionary('RepairRecordStatus');
        this.SourceList = await this.$getNewDataDictionary('RepairSource');
        this.StatusList = await this.$getNewDataDictionary('RepairOrderStatus');
        this.TypeList = await this.$getNewDataDictionary('RepairOrderType');
        this.getTableData();
    },
    methods: {
        ClearDesc() {
            this.Description = '';
            this.RepairRecordDesc = '';
            this.Keyword = '';
            this.GetExperiencePageList();
        },
        getDescModelShow() {
            this.CheckItem = {};
            this.ClearDesc();
            this.DescModel = true;
        },
        CheckRow() {
            this.DescModel = false;
            this.wxjlList[3].value = this.CheckItem.RepairRecordDesc;
        },
        descclickFun(item) {
            this.CheckItem = item;
            this.$refs.BjTables.selected = [item];
        },
        // 列表查询
        async GetExperiencePageList() {
            let params = {
                Description: this.Description,
                RepairRecordDesc: this.RepairRecordDesc,
                Keyword: this.Keyword
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading2 = true;
            const res = await GetExperienceList(params);
            console.log(res);
            let { success, response } = res;
            response.forEach(item => {
                this.FaultPosition.forEach(it => {
                    if (item.FaultCategory == it.ItemValue) {
                        item.FaultCategory = it.ItemName;
                        item.FaultCategoryValue = it.ItemValue;
                    }
                });
                this.FaultProperty.forEach(it => {
                    if (item.FaultNature == it.ItemValue) {
                        item.FaultNature = it.ItemName;
                        item.FaultNatureValue = it.ItemValue;
                    }
                });
            });
            if (success) {
                this.loading2 = false;
                this.Descdesserts = response || {} || [];
            }
        },
        async SaveFormSave() {
            let flag = this.SaveFormList.some(item => {
                if (item.require) {
                    return item.value == '' || item.value == null;
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            let obj = {};
            this.SaveFormList.forEach(item => {
                if (item.multiple) {
                    if (item.value.length == 0) {
                        obj[item.id] = '';
                    } else {
                        obj[item.id] = item.value.join('|');
                    }
                } else {
                    obj[item.id] = item.value;
                }
            });
            obj.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            obj.RepairWoId = this.tableItem.RepairWoId;
            obj.RepairWo = this.tableItem.RepairWo;
            obj.DeviceId = this.tableItem.DeviceId;
            obj.DeviceCode = this.tableItem.DeviceCode;
            obj.DeviceName = this.tableItem.DeviceName;
            let res = await GetRepairServiceSaveForm(obj);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.getTableData();
                this.SaveFormModel = false;
            }
        },
        async RepairRecordSave() {
            let flag = this.wxjlList.some(item => {
                if (item.require) {
                    return item.value === '' || item.value == null;
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            let obj = this.tableItem;
            let Phenomenon = '';
            this.wxjlList.forEach(item => {
                if (item.id == 'Phenomenon') {
                    Phenomenon = item.value;
                }
                if (item.type == 'number') {
                    item.value = Number(item.value);
                }
                obj[item.id] = item.value;
            });
            obj.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            obj.RepairManager = this.tableItem.RepairManagerValue;
            let res = await GetRepairRecordRepairRecord(obj, Phenomenon);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.getTableData();
                this.RepairRecordModel = false;
            }
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.getTableData();
        },
        changeTab(v) {
            switch (v) {
                case 0:
                    setTimeout(() => {
                        this.$refs.bj.MyGetPartsHistoryDetailPageList(this.rowtableItem);
                    }, 10);
                    break;
                case 1:
                    setTimeout(() => {
                        this.$refs.fwcg.MyGetRepairServiceGetList(this.rowtableItem, this.CarbonCopyData);
                    }, 10);
                    break;
            }
        },
        //  查看BOM详情
        clickFun(data) {
            this.tableItem = data;
            this.rowtableItem = data || {};
            this.detailShow = true;
            switch (this.tab) {
                case 0:
                    setTimeout(() => {
                        this.$refs.bj.MyGetPartsHistoryDetailPageList(this.rowtableItem);
                    }, 10);
                    break;
                case 1:
                    setTimeout(() => {
                        this.$refs.fwcg.MyGetRepairServiceGetList(this.rowtableItem, this.CarbonCopyData);
                    }, 10);
                    break;
            }
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.getTableData();
        },
        async getTableData() {
            let params = {
                ...this.papamstree
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading = true;
            const res = await GetRepairRecordPageList(params);
            let { success, response } = res;
            for (let k in response.data) {
                let item = response.data[k];
                item.ReportByName = await this.$getPerson(item.ReportBy);
                item.ReceiveByName = await this.$getPerson(item.ReceiveBy);
                item.ConfirmByName = await this.$getPerson(item.ConfirmBy);
                this.StatusList.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                this.RecordStatusList.forEach(it => {
                    if (item.RecordStatus == it.ItemValue) {
                        item.RecordStatus = it.ItemName;
                    }
                });
                this.FaultPosition.forEach(it => {
                    if (item.FaultCategory == it.ItemValue) {
                        item.FaultCategory = it.ItemName;
                    }
                });
                this.FaultProperty.forEach(it => {
                    if (item.FaultNature == it.ItemValue) {
                        item.FaultNature = it.ItemName;
                    }
                });
                this.RepairProperty.forEach(it => {
                    if (item.RepairNature == it.ItemValue) {
                        item.RepairNature = it.ItemName;
                    }
                });
                this.RepairMngData.forEach(it => {
                    if (item.RepairManager == it.ItemValue) {
                        item.RepairManager = it.ItemName;
                        item.RepairManagerValue = it.ItemValue;
                    }
                });
                let str = '';
                this.ShifMngData.forEach(it => {
                    if (item.DutyManager.indexOf('|') != -1) {
                        let arr = item.DutyManager.split('|');
                        arr.forEach(k => {
                            if (k == it.ItemValue) {
                                console.log(it.ItemName, 3);
                                str += it.ItemName + ' ';
                            }
                        });
                        item.DutyManagerName = str;
                        item.DutyManagerValue = arr.join('|');
                    } else {
                        if (item.DutyManager == it.ItemValue) {
                            item.DutyManagerName = it.ItemName;
                            item.DutyManagerValue = it.ItemValue;
                        }
                    }
                });
                // this.MaintenanceGroupData.forEach(it => {
                //     if (item.ReceiveBy == it.ItemValue) {
                //         item.ReceiveBy = it.ItemName;
                //         item.ReceiveByValue = it.ItemValue;
                //     }
                // });
            }
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        async gzxxSave() {
            if (this.gzxx == '') {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            let params = this.tableItem;
            params.Phenomenon = this.gzxx;
            let res = await GetRepairRecordSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.getTableData();
                this.gzxxModel = false;
            }
        },
        async Save() {
            let params = this.tableItem;
            let res;
            switch (this.dialogType) {
                case 'jd':
                    if (this.user == '' || this.userDate == null) {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    params.ReceiveBy = this.user;
                    params.ReceiveDate = this.userDate;
                    res = await GetRepairRecordReceive(params);
                    break;
                case 'th':
                    if (this.user == '' || this.userDate == null) {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    params.RejectBy = this.user;
                    params.RejectDate = this.userDate;
                    res = await GetRepairRecordReject(params);
                    break;
            }
            if (this.dialogType == 'gp') {
                if (this.gpr == '') {
                    Message({
                        message: `${this.$t('Inventory.ToOver')}`,
                        type: 'error'
                    });
                    return;
                }
                let myflag = this.gpList.some(item => {
                    if (item.required) {
                        return item.value == '' || item.value == null;
                    }
                });
                if (myflag) {
                    Message({
                        message: `${this.$t('Inventory.ToOver')}`,
                        type: 'error'
                    });
                    return;
                }
                params.ReceiveBy = this.gpr;
                this.gpList.forEach(item => {
                    params[item.id] = item.value;
                });
                res = await GetRepairRecordReassign(params);
            }
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.getTableData();
                this.addModel = false;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            this.user = this.$store.getters.getUserinfolist[0].LoginName;
            this.userDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            switch (type) {
                case 'gp':
                    this.gpr = '';
                    this.gpList.forEach(item => {
                        for (let k in this.tableItem) {
                            if (item.id == k) {
                                item.value = this.tableItem[k];
                            }
                        }
                    });
                    this.addTitle = this.$t('TPM_SBGL_WDGD._GP');
                    this.addModel = true;
                    return;
                case 'jd':
                    this.addTitle = this.$t('TPM_SBGL_WDGD._JD');
                    this.addModel = true;
                    return;
                case 'xgggxx':
                    this.gzxx = this.tableItem.Phenomenon;
                    this.gzxxModel = true;
                    return;
                case 'th':
                    this.addTitle = this.$t('TPM_SBGL_WDGD._TH');
                    this.addModel = true;
                    return;
                case 'wxjl':
                    this.wxjlList.forEach(item => {
                        item.value = '';
                    });
                    this.wxjlList[this.wxjlList.length - 1].value = this.tableItem.Phenomenon;
                    this.RepairRecordModel = true;
                    return;
                case 'cjfwd':
                    this.SaveFormList.forEach(item => {
                        item.value = '';
                    });
                    this.SaveFormModel = true;
                    return;
            }
        }
    }
};
</script>
<style lang="scss">
#FlexForm {
    .el-dialog__body {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        .FlexAddForm {
            width: 48%;
        }
    }
}
</style>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}

.addForm {
    .textfieldbox {
        .el-input {
            width: 100%;
        }
    }
    .textlabel {
        display: inline-flex;
        font-size: 16px;
        margin-right: 25px;
    }
    .el-radio-group {
        height: 40px;
        margin-top: 10px;
    }
    .el-radio__input.is-checked + .el-radio__label {
        color: #3dcd58;
    }
    .el-radio__input.is-checked .el-radio__inner {
        border-color: #3dcd58;
        background: #3dcd58;
    }
    .el-radio__label {
        font-size: 16px;
    }
}
.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

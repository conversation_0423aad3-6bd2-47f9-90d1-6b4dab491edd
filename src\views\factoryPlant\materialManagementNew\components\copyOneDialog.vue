<template>
    <v-dialog v-model="copyDialog" persistent max-width="720px">
        <!-- 新增 -->
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                {{ $t('DFM_WLGL.copyOne') }}
                <v-icon @click="copyDialog = false">mdi-close</v-icon>
            </v-card-title>
            <!-- 表单内容 -->
            <v-card-text class="mt-7">
                <v-container>
                    <v-form ref="form" v-model="valid">
                        <v-row>
                            <v-col class="py-0 px-3" cols="12">
                                <!-- label="料号" -->
                                <v-text-field v-model="form.Code" :label="$t('DFM_WLGL.Code')" :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" outlined dense required></v-text-field>
                            </v-col>
                        </v-row>
                    </v-form>
                </v-container>
            </v-card-text>
            <v-card-actions class="grey lighten-3">
                <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { saveForm } from '@/api/factoryPlant/material.js';
export default {
    name: 'DataDictionaryDialog',
    props: {
        materialObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            copyDialog: false,
            // 提交表单数据
            form: {
                Code: ''
            },
            valid: true,
            checkbox: true
        };
    },
    methods: {
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                delete this.materialObj.ID;
                const res = await saveForm({ ...this.materialObj, ...this.form });
                const { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    if (this.checkbox) {
                        this.$emit('handlePopup', 'opear');
                        this.copyDialog = false;
                    }else{
                        this.$refs.form.reset();
                    }
                }
            }
        },
        closePopup() {
            this.copyDialog = false;
        }
    }
};
</script>
<style lang="scss" scoped>
.v-text-field {
    margin-left: 20px;
}
.col-12 {
    padding: 0;
}
.v-sheet.v-card {
    border-radius: 10px;
}
</style>

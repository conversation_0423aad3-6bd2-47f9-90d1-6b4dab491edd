// import i18n from '@/plugins/i18n';
export const postManagementColum = [
    {
        text: '序号',
        value: 'Index',
        sortable: true
    },
    { text: '岗位名称', value: 'Name', width: 120, },
    { text: '岗位类型', value: 'Type', dictionary: true, width: 100, },
    { text: '班制', value: 'WorkHours', width: 90, },
    { text: '维修成本单价', value: 'PostPrice', width: 120 },
    { text: '描述', value: 'Description', width: 150 },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    { text: '操作', align: 'center', value: 'actions', sortable: true }
];

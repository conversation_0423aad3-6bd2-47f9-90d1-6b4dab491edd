export function getGaugeProgress(data) {
    let option;
    option = {
        series: [{
            type: 'gauge',
            startAngle: 180,
            endAngle: 0,
            min: 0,
            max: 100,
            splitNumber: 5,
            center: ['50%', '80%'],
            radius: '160%',
            pointer: {
                itemStyle: {
                    color: 'auto'
                },
                length: '50%',
            },
            axisLine: {
                lineStyle: {
                    width: 18,
                    color: [
                        [0.3, '#67e0e3'],
                        [0.7, '#37a2da'],
                        [1, '#5EF02B']
                    ]
                }
            },
            axisTick: {
                show: false,
            },
            splitLine: {
                distance: -18,
                length: 18,
                lineStyle: {
                    color: '#eee',
                    width: 1
                }
            },
            axisLabel: {
                color: '#fff',
                distance: 10,
                fontSize: 17,
            },
            detail: {
                width: '60%',
                lineHeight: 10,
                height: 20,
                fontSize: 20,
                color: '#fff',
                offsetCenter: [0, '20%'],
                valueAnimation: true,
                formatter: function(value) {
                    return value;
                },
            },
            data: [{
                value: 100
            }]
        }]
    };
    return option
}
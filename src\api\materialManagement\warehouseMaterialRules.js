import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_MATERIAL'
// 仓库物料规则

//不分页获取仓库列表
// export function getWarehouseList(data) {
//     const api =  '/materail/WarehouseManage/GetList'
//     return getRequestResources(baseURL, api, 'post', data);
// }
//分页获取仓库物料规则列表
export function getWarehouseMaterialManageList(data) {
    const api = '/materail/WarehouseMaterialManage/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑仓库物料规则
export function WarehouseMaterialManageSaveForm(data) {
    const api = '/materail/WarehouseMaterialManage/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除仓库物料规则
export function DeleteWarehouseMaterialManage(data) {
    const api = '/materail/WarehouseMaterialManage/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
// 根据产线获取物料列表 、
export function getMaterialByLine(data) {
    const api = '/materail/WarehouseMaterialManage/GetMaterialList'
    return getRequestResources(baseURL, api, 'post', data);
}
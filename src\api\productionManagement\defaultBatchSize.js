import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TRACE; // 配置服务url
const DFMURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM
//批次数量维护 列表
export function GetPageList(data) {
    return request({
        url: baseURL + '/trace/DefaultBatchQty/GetPageList',
        method: 'post',
        data
    });
}
// 查询接口:api/DefaultBatchQty/GetPageList
// 新增/修改:api/DefaultBatchQty/SaveForm
// 删除:api/DefaultBatchQty/Delete
//批次数量维护 新增
export function SaveForm(data) {
    return request({
        url: baseURL + '/trace/DefaultBatchQty/SaveForm',
        method: 'post',
        data
    });
}

// 批次数量维护 删除
export function Delete(data) {
    return request({
        url: baseURL + '/trace/DefaultBatchQty/Delete',
        method: 'post',
        data
    });
}

// 查询成品料号列表
export function getMaterialList(data) {
    return request({
        url: DFMURL + '/api/Material/GetPageList',
        method: 'post',
        data
    });
}
<template>
  <el-dialog
    title="选择目录负责人"
    :visible.sync="visible"
    width="800px"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :modal="true"
    @closed="$emit('close')"
  >
    <div class="search-area">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="keywords"
            placeholder="请输入关键字"
            clearable
            size="small"
          />
        </el-col>
        <el-col :span="16">
          <el-button type="primary" size="small" @click="getList">查询</el-button>
          <el-button size="small" @click="keywords = ''">重置</el-button>
        </el-col>
      </el-row>
    </div>

    <el-table
      ref="table"
      v-loading="loading"
      :data="tableList"
      height="320"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="ID" label="用户ID" />
      <el-table-column prop="UserName" label="用户名" />
      <el-table-column prop="RealName" label="真实姓名" />
      <el-table-column prop="DepartmentName" label="部门" />
    </el-table>

    <el-pagination
      class="pagination"
      :current-page="pageOptions.page"
      :page-sizes="pageOptions.pageSizeitems"
      :page-size="pageOptions.pageSize"
      :total="pageOptions.total"
      layout="total, sizes, prev, pager, next"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <div slot="footer" class="dialog-footer">
      <el-checkbox v-model="isChecked">确定并不再提示</el-checkbox>
      <div class="dialog-footer__buttons">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getUserList } from '@/api/systemManagement/userManagement'

export default {
  name: 'SelectUser',
  data() {
    return {
      visible: true,
      isChecked: true,
      keywords: '',
      loading: false,
      tableList: [],
      selected: [],
      pageOptions: {
        total: 0,
        page: 1,
        pageSize: 20,
        pageCount: 1,
        pageSizeitems: [10, 20, 50, 100, 500]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      try {
        const resp = await getUserList({
          UserName: this.keywords,
          pageIndex: this.pageOptions.page,
          pageSize: this.pageOptions.pageSize
        })
        this.selected = []
        this.tableList = resp.response.data
        this.pageOptions.pageCount = resp.response.pageCount
        this.pageOptions.total = resp.response.dataCount
      } catch (error) {
        console.error('获取用户列表失败:', error)
        this.$message.error('获取用户列表失败')
      } finally {
        this.loading = false
      }
    },
    handleSelectionChange(val) {
      this.selected = val
    },
    submitForm() {
      if (this.selected.length === 0) {
        this.$message({
          message: '请选择用户',
          type: 'warning'
        })
        return false
      }
      if (this.selected.length > 1) {
        this.$message({
          message: '只能选择一个用户',
          type: 'warning'
        })
        return false
      }
      this.$emit('select-user', this.selected[0])
      this.handleClose()
    },
    handleClose() {
      this.visible = false
      this.$emit('close')
    },
    handleSizeChange(val) {
      this.pageOptions.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageOptions.page = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.search-area {
  margin-bottom: 15px;
}
.pagination {
  margin-top: 15px;
  text-align: right;
}
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &__buttons {
    display: flex;
    gap: 10px;
  }
}
</style>
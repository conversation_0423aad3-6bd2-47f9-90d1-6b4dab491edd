// 报警记录列表
<template>
    <div class="line-side-view">
        <div class="line-side-main">
            <SearchForm ref="contactTorm" @selectChange="selectChange" class="mt-2" :searchinput="searchinput" :show-from="showFrom" @searchForm="searchForm" />
            <v-card outlined>
                <Tables
                    :showSelect="false"
                    :footer="false"
                    :headers="headers"
                    :desserts="desserts"
                    :loading="loading"
                    :tableHeight="'calc(40vh)'"
                    :page-options="pageOptions"
                    :btn-list="btnList"
                ></Tables>
            </v-card>
            <div style="margin-top: 10px; display: flex; width: 100%">
                <v-card outlined style="width: 40%">
                    <div id="echarts2" style="height: 40vh; width: 100%"></div>
                </v-card>
                <v-card outlined style="width: 60%; margin-left: 5px">
                    <div id="echarts" style="height: 40vh; width: 100%"></div>
                    <!-- <div id="echarts" style="height: 40vh; width: 100%"></div> -->
                </v-card>
            </div>
        </div>
    </div>
</template>
<script>
import { GetAlarmAnalysisReportData } from '@/api/andonManagement/AnalysisofAlarmCauses.js';
import { getAlarmTypeRootList, getAlarmTypeTreetList } from '@/api/andonManagement/alarmType.js';
import Util from '@/util';
import dayjs from 'dayjs';
import { configUrl } from '@/config';
import physicalModel from '@/mixins/physicalModel';
import { getbarstack } from '@/components/echarts/stackBar.js';
import { getsimplePie } from '@/components/echarts/SimplePie.js';

export default {
    name: 'UpgradeRule',
    mixins: [physicalModel],
    data() {
        return {
            headers: [],
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            showFrom: true,
            loading: false,
            deleteId: [],
            productLine: [],
            alarmTypeRootList: [],
            alarmTypeList: [],
            startTime: '',
            endTime: '',
            dialogVisible: false,
            fullImageDialogVisible: false,
            fullImageUrl: '',
            previewFiles: [],
            searchParams: {},
            AreaList: [],
            LineCodeList: [],
            equipmentList: [],
            unitList: [],
            eventStatusList: [],
            problemLevelList: [],
            myChart: null,
            option: {},
            myChart2: null,
            option2: {},
            Myheader: []
        };
    },
    computed: {
        btnList() {
            return [];
        },
        //查询条件
        searchinput() {
            return [
                // 开始时间
                {
                    key: 'startTime',
                    type: 'time',
                    icon: '',
                    value: this.startTime,
                    label: this.$t('GLOBAL.StartTime')
                },
                // 结束时间
                {
                    key: 'endTime',
                    type: 'time',
                    icon: '',
                    value: this.endTime,
                    label: this.$t('GLOBAL.EndTime')
                },
                // 产品线
                {
                    key: 'areacode',
                    icon: '',
                    type: 'combobox',
                    search: 'areacode',
                    linkageKey: 'firstLevel',
                    childrenLinkageArray: this.LineCodeList,
                    selectData: this.$changeSelectItems(this.AreaList, 'EquipmentCode', 'EquipmentName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.AreaCode')
                },
                // 工段
                {
                    key: 'Line',
                    icon: '',
                    type: 'combobox',
                    search: 'Line',
                    linkageKey: 'secondLevel',
                    childrenLinkageArray: this.unitList,
                    selectData: this.$changeSelectItems(this.secondLevel, 'EquipmentCode', 'EquipmentName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.ProductLineName')
                },
                // 工站
                {
                    key: 'unitcode',
                    icon: '',
                    type: 'combobox',
                    search: 'unitcode',
                    linkageKey: 'thirthLevel',
                    childrenLinkageArray: this.equipmentList,
                    selectData: this.$changeSelectItems(this.thirthLevel, 'EquipmentCode', 'EquipmentName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.UnitName')
                },
                // 设备
                {
                    key: 'equipmentCode',
                    icon: '',
                    type: 'combobox',
                    search: 'equipmentCode',
                    linkageKey: 'fourthLevel',
                    childrenLinkageArray: [],
                    selectData: this.$changeSelectItems(this.fourthLevel, 'EquipmentCode', 'EquipmentName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.EquipmentName')
                },
                // 状态
                {
                    key: 'eventStatus',
                    icon: '',
                    type: 'select',
                    selectData: this.$changeSelectItems(this.eventStatusList, 'ItemValue', 'ItemName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.state')
                },
                // 一级分类
                {
                    key: 'MainAlarmType',
                    icon: '',
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.alarmTypeRootList, 'AlarmCode', 'AlarmName'),
                    value: this.$store.state.searchForm.MainAlarmType,
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.MainAlarmType')
                }
            ];
        }
    },
    async created() {
        console.log('create', this.$store.state.searchForm.MainAlarmType);
        await this.init();
        await this.getAlarmTypeList();
        await this.getalarmTypeList();
        this.eventStatusList = await this.$getDataDictionary('alarmEventStatus');
        this.problemLevelList = await this.$getDataDictionary('problemLevel');
        this.getDataList();
    },
    methods: {
        async init() {
            const nowTime = new Date();
            const startTime = dayjs(nowTime).format('YYYY-MM-DD') + ' 00:00:00';
            const endTime = dayjs(nowTime).format('YYYY-MM-DD HH:mm:ss');
            this.startTime = startTime;
            this.endTime = endTime;
            this.searchParams = { startTime, endTime };
            // 获取工段
            this.LineCodeList = await Util.GetEquipmenByLevel('Line');
            // 产品线
            this.AreaList = await Util.GetEquipmenByLevel('Area');
            // 获取工站
            this.unitList = await Util.GetEquipmenByLevel('Segment');
            // 设备
            this.equipmentList = await Util.GetEquipmenByLevel('Unit');
        },
        // 获取告警类型列表
        async getalarmTypeList() {
            const res = await getAlarmTypeTreetList({});
            this.alarmTypeList = [];
            const { success, response } = res || {};
            if (response && success) {
                response.forEach(e => {
                    this.alarmTypeList.push(e);
                    const { children } = e;
                    if (children && children.length) {
                        children.forEach(i => {
                            this.alarmTypeList.push(i);
                        });
                    }
                });
            }
        },
        // 获取大类列表
        async getAlarmTypeList() {
            const res = await getAlarmTypeRootList({});
            const { success, response } = res || {};
            if (success) {
                this.alarmTypeRootList = response || [];
            } else {
                this.alarmTypeRootList = [];
            }
        },
        searchForm(v) {
            this.searchParams = v;
            this.getDataList();
        },
        creatChart() {
            let mydata = [];
            this.desserts.forEach(item => {
                for (let k in item) {
                    if (k != 'all' && k != 'Index' && k != 'MainAlarmType') {
                        let obj = {
                            name: k,
                            data: item[k],
                            stack: item.MainAlarmType
                        };
                        mydata.push(obj);
                    }
                }
            });
            let xdata = this.desserts.map(item => {
                return item.MainAlarmType;
            });
            let data = {
                xdata: xdata,
                data: mydata
            };
            let chartDom = document.getElementById(`echarts`);
            this.myChart = this.$echarts.init(chartDom);
            this.option = getbarstack(data, '次');
            this.option.xAxis[0].axisLabel.color = 'black';
            this.option.yAxis[0].axisLabel.color = 'black';
            this.myChart.setOption(this.option, true);
        },
        creatChart2() {
            let mydata = [];
            console.log(this.desserts);
            if (this.desserts.length > 0) {
                this.desserts.forEach(item => {
                    let obj = {
                        value: item.all,
                        name: item.MainAlarmType
                    };
                    mydata.push(obj);
                });
            }
            let chartDom = document.getElementById(`echarts2`);
            this.myChart2 = this.$echarts.init(chartDom);
            this.option2 = getsimplePie(mydata);
            this.myChart2.setOption(this.option2, true);
        },
        async getDataList() {
            const { startTime, endTime } = this.searchParams;
            let params = {
                ...this.searchParams,
                startTime: startTime || this.startTime,
                endTime: endTime || this.endTime
            };
            const res = await GetAlarmAnalysisReportData(params);
            const { success, response } = res || {};
            this.desserts = [];
            this.Myheader = [];
            let tabledata = [];
            let i = 0;
            let tableobj = {};
            if (success) {
                for (let k = 0; k < response.length; k++) {
                    if (i == 0) {
                        tableobj = {};
                    }
                    tableobj.MainAlarmType = response[k].MainAlarmType;
                    let objheader = [];
                    for (let o in tableobj) {
                        objheader.push(o);
                    }
                    let objheaderFlag = objheader.some(item => {
                        return item == response[k].RecordStatus;
                    });
                    if (objheaderFlag) {
                        tableobj[response[k].RecordStatus] = tableobj[response[k].RecordStatus] + response[k].Quantity;
                    } else {
                        tableobj[response[k].RecordStatus] = response[k].Quantity;
                    }
                    if (response[k + 1]) {
                        if (response[k].MainAlarmType == response[k + 1].MainAlarmType) {
                            i++;
                        } else {
                            i = 0;
                            tabledata.push(tableobj);
                        }
                    } else {
                        i = 0;
                        tabledata.push(tableobj);
                    }
                }
                tabledata.forEach(item => {
                    item.all = 0;
                    for (let k in item) {
                        if (k != 'all') {
                            if (typeof item[k] == 'number') {
                                item.all = item[k] + item.all;
                            }
                        }
                    }
                });
                this.desserts = tabledata || [];
                response.forEach(item => {
                    let flag = this.Myheader.some(it => {
                        return it.value == item.RecordStatus;
                    });
                    if (!flag) {
                        let obj = {
                            value: item.RecordStatus,
                            text: item.RecordStatus,
                            width: 100
                        };
                        this.Myheader.push(obj);
                    }
                });
                this.headers = [
                    {
                        text: '安灯类型',
                        value: 'MainAlarmType',
                        width: 150
                    },
                    ...this.Myheader,
                    {
                        text: '总计',
                        value: 'all',
                        width: 150
                    }
                ];
            } else {
                this.desserts = [];
            }
            this.creatChart();
            this.creatChart2();
        }
    }
};
</script>
<style lang="scss" scoped>
.line-side-view {
    display: flex;

    .line-side-main {
        flex: 1;
        width: 100%;

        .v-data-table {
            width: 100%;
        }
    }
}
</style>
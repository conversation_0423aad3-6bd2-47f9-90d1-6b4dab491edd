<template>
    <div class="operating-state">
        <v-tabs v-model="active" @change="changeTab">
            <v-tab v-for="state in stateCategory" :key="state.value">{{ state.name }}</v-tab>
        </v-tabs>
        <div class="content-box mt-5">
            <Tables v-if="active === 0 || active === 1" :headers="active === 0 ? operationColumns : haltColumns"
                :desserts="tableList" :page-options="pageOptions"></Tables>
        </div>
    </div>
</template>

<script>
import { operationColumns, haltColumns } from '@/columns/equipmentManagement/operatingState.js'
const stateCategory = [{ name: "运行", value: 0 }, { name: "停机", value: 1 }, { name: "时间线", value: 2 }]
export default {
    data() {
        return {
            tableList: [],
            active: 0,
            haltColumns,
            operationColumns,
            stateCategory,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
        }
    },
    created() { },
    methods: {
        changeTab() {
            console.log(this.active)
        }
    }
}
</script>

<style></style>
<template>
    <v-dialog scrollable persistent v-model="showDialog" max-width="980px">
        <v-card v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-autocomplete v-model="form.DeviceId" :loading="loading" :items="sbitems" item-value="ID"
                            item-text="Code" :search-input.sync="form.DeviceCode" flat outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH.DeviceCode')">
                            <template #item="data">
                                <template v-if="typeof data.item !== 'object'">
                                    <v-list-item-content v-text="data.item"></v-list-item-content>
                                </template>
                                <template v-else>
                                    <v-list-item-content>
                                        <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                        <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                    </v-list-item-content>
                                </template>
                            </template>
                        </v-autocomplete>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-select v-model="form.McStatus" :items="McStatuslist" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH.McStatus')"></v-select>
                    </v-col>

                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea v-model="form.Remark" rows="2" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH.Remark')"></v-textarea>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-4 lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card v-if="dialogType == 'edit'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._BJ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-autocomplete v-model="editedItem.DeviceId" :loading="loading" :items="sbitems" item-value="ID"
                            item-text="Code" :search-input.sync="editedItem.DeviceCode" flat outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH.DeviceCode')">
                            <template #item="data">
                                <template v-if="typeof data.item !== 'object'">
                                    <v-list-item-content v-text="data.item"></v-list-item-content>
                                </template>
                                <template v-else>
                                    <v-list-item-content>
                                        <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                        <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                    </v-list-item-content>
                                </template>
                            </template>
                        </v-autocomplete>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-select v-model="editedItem.McStatus" :items="McStatuslist" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH.McStatus')"></v-select>
                    </v-col>

                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea v-model="editedItem.Remark" rows="2" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH.Remark')"></v-textarea>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-4 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card v-if="dialogType == 'spotCheck'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('TPM_SBGL_SBDJJH._DJ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-autocomplete v-model="addform.McId" :loading="loading" :items="items" item-value="ID"
                            item-text="McProject" outlined disabled dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH_XQ.McProject')"></v-autocomplete>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="addform.McValue" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH_XQ.McValue')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="addform.McStatus" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH_XQ.McStatus')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="addform.McUser" disabled outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH_XQ.McUser')"></v-text-field>
                    </v-col>

                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="addform.Methods" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH_XQ.Methods')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="addform.Tools" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH_XQ.Tools')"></v-text-field>
                    </v-col>
                    <!-- <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="addform.LubeBatch" outlined dense label="润滑油批次"></v-text-field>
                    </v-col> -->

                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea v-model="addform.Remark" rows="2" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH_XQ.Remark')"></v-textarea>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-4 lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addnewSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card v-if="dialogType == 'recheck'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('TPM_SBGL_SBDJJH._FJ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-autocomplete v-model="addform.McId" :loading="loading" :items="items" item-value="ID"
                            item-text="McProject" :search-input.sync="addform.McProject" flat disabled outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH_XQ.McProject')"></v-autocomplete>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="addform.McValue" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH_XQ.McValue')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="addform.McStatus" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH_XQ.McStatus')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="addform.McUser" disabled outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH_XQ.McUser')"></v-text-field>
                    </v-col>

                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="addform.Methods" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH_XQ.Methods')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="addform.Tools" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH_XQ.Tools')"></v-text-field>
                    </v-col>
                    <!-- <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="addform.LubeBatch" outlined dense label="润滑油批次"></v-text-field>
                    </v-col> -->

                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea v-model="addform.Remark" rows="2" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJJH_XQ.Remark')"></v-textarea>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-4 lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addnewSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { DeviceMcProjectGetList, MaintainCheckSavesForm, MaintainCheckDetailSavesForm } from '@/api/equipmentManagement/upkeep.js';
import { EquipGetList } from '@/api/equipmentManagement/Equip.js';
import { StaffSiteGetList } from '@/api/peopleManagement/basicdata.js';
const McStatuslist = ['完成', '未完成'];
export default {
    props: {
        repastTypelist: {
            type: Array,
            default: () => []
        },
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            loading: true,
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            McStatuslist,
            peopleitems: [],
            sbitems: [],
            items: [],
            form: {
                DeviceCode: '',
                DeviceId: '',
                McStatus: '',
                // SpotCheckId: '',
                // TxFileRev: '',
                // TxFileCode: '',
                Remark: ''
            },
            addform: {
                McId: '',
                McProject: '',
                McValue: '',
                McStatus: '',
                McUser: '',
                Methods: '',
                Tools: '',
                // LubeBatch: '',
                Remark: '',
                ProjectCode: '',
                ID: ''
            }
        };
    },
    computed: {
        userInfo() {
            let userInfo = this.$store.state.auth.userinfolist[0];
            return userInfo;
        },
        editedItem() {
            if (this.dialogType != 'edit') return {};
            const { DeviceCode, DeviceId, McStatus, Remark } = this.tableItem;
            return {
                DeviceCode,
                DeviceId,
                McStatus,
                // SpotCheckId,
                // TxFileRev,
                // TxFileCode,
                Remark
            };
        }
    },
    watch: {
        showDialog(newval) {
            if (!newval) return false;
            if (this.dialogType == 'spotCheck' || this.dialogType == 'recheck') {
                for (const key in this.addform) {
                    this.addform[key] = this.tableItem[key];
                }

                this.addform.McUser = this.userInfo.UserName;
            }
            if (this.dialogType == 'add') {
                this.form = {
                    DeviceCode: '',
                    DeviceId: '',
                    McStatus: '',
                    Remark: ''
                };
            }
        },
        search(val) {
            val && val !== this.form.McProjectId && this.querySelections(val);
        }
    },
    created() {
        // this.querySelections();
        // this.getquerySelections();
        // this.queryPeoplelist();
    },
    methods: {
        closeEquip() {
            this.showDialog = false;
        },
        // 获取人员
        async queryPeoplelist() {
            this.loading = true;
            const res = await StaffSiteGetList({ key: '' });
            let { success, response } = res;
            if (success) {
                this.peopleitems = response;
                this.loading = false;
            }
        },
        async getquerySelections(v) {
            this.loading = true;
            const res = await EquipGetList({ key: v });
            let { success, response } = res;
            if (success) {
                this.sbitems = response;
                this.loading = false;
            }
        },
        async querySelections(v) {
            this.loading = true;
            const res = await DeviceMcProjectGetList({ key: v, pcategory: '点检' });
            let { success, response } = res;
            if (success) {
                this.items = response;
                this.loading = false;
            }
        },
        async addSave(type) {
            const paramsKey = Object.keys(this.form);
            const paramsObj = type == 'add' ? this.form : this.editedItem;
            let params = {};
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            if (type == 'edit') {
                params.ID = this.tableItem.ID;
            }
            const res = await MaintainCheckSavesForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                // this.$parent.$parent.RepastInfoGetPage();
                this.$parent.$parent.RepastInfoGetPage();
                this.showDialog = this.classcheckbox ? false : true;
            }
        },
        async addnewSave(type) {
            const paramsKey = Object.keys(this.addform);
            // const paramsObj = type == 'add' ? this.addform : this.editedItem;
            const paramsObj = this.addform;
            let params = {};
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            // params.McId = this.tableItem.ID;
            if (type == 'edit') {
                params.ID = this.tableItem.ID;
            }
            const res = await MaintainCheckDetailSavesForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.$parent.RepastInfoLogGetPage();
                this.showDialog = this.classcheckbox ? false : true;
            }
        }
    }
};
</script>

<template>
  <el-dialog
    :style="backgroundVar"
    :title="title1"
    :append-to-body="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="value"
    :before-close="handleClose1"
    lock-scroll
    :fullscreen="true"
  >
    <div
      :id="id1 + '-123'"
      style="width: 100%;height: 400px;"
    >
    </div>
    <!-- <div
      v-if="tableData.length>0"
      style="font-size: 18px;color:#fff;font-weight: bold;"
    >{{ barTitle }}:</div>
    <el-table
      v-if="tableData.length>0"
      :data="tableData"
      border
      style="width: 100%;margin-top: 20px;color:#fff; font-size: 12px;font-weight: bold;overflow-y: auto;"
      :header-cell-style="{background:'#fafafa',textAlign: 'center'}"
      :row-style="{height: '35px'}"
      show-overflow-tooltip
      :tooltip-effect="'dark'"
      :height="tableHeight"
    >
      <el-table-column
        prop="date"
        label="日期"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="value"
        label="值"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="target"
        label="目标值"
        align="center"
        show-overflow-tooltip
      />
    </el-table> -->
  </el-dialog>
</template>
<script>
import { getChartStructure, getTableList } from '@/api/simConfig/simconfignew.js';

export default {
  props: {
    // 是否显示弹出框
    value: {
      type: Boolean,
      default: false
    },
    position: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    id1: {
      type: String,
      default: ''
    },
    titlebartran: {
      type: String,
      default: ''
    },
    backgroundImg: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      title1: '',
      barTitle: '',
      tableData: [],
      tableHeight: 0,
      lineLegend: ['实际值', '目标值'],
      //当前时间颗粒度
      curShift: {
        KpiValues: []
      },
      myShiftList: [],
      chartBartc: null,
      yAxisOption: {
        type: 'value',
        // show: false
        axisLine: {
          show: false
        },
        axisLabel: {
          show: true
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      },
    }
  },
  computed: {
    backgroundVar() {
      return {
        '--background': this.backgroundImg
      }
    },
    //x轴配置
    xAxisOption() {
      let list = ['7', '6', '5', '4', '3', '2', '1']
      if (this.curShift.ChartData && this.curShift.ChartData.x) {
        list = this.curShift.ChartData.x.map(item => {
          // let month = dayjs(item).$M+1
          // let day = dayjs(item).$D
          if (['日', '周'].includes(this.curShift.TimeDimension)) {
            let month = item.split('-')[1]
            let day = item.split('-')[2]
            // 这里需要匹配颗粒度,来输出不同的x轴数据.
            // return `${month}-${day}`
            return `${month}-${day}`
          } else if (['月', '季度'].includes(this.curShift.TimeDimension)) {
            let label = item.split('年')[1]
            return label
          } else {
            return item
          }
        })
        // list.reverse()
        // list = this.curShift.ChartData.x
      }
      return {
        type: 'category',
        // boundaryGap: false,
        axisLine: {
          onZero: false,
          lineStyle: {
            color: '#fff'
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff'
        },
        data: list
      }
    },
    lineSeries() {
      if (this.curShift.ChartData.x.length <= 0) {
        this.$nextTick(() => {
          const dom = document.getElementById(this.id1 + '-123');
          dom.innerHTML = '<div class="noDataBox">暂无数据</div>';
          dom.removeAttribute('_echarts_instance_');
          return
        })
      }
      //区分横竖
      let axisMarkLine = this.curConfig.ChartType === '3'
        ? [{ xAxis: this.curShift.TargetValue || '' }]
        : [{ yAxis: this.curShift.TargetValue || '' }]

      // let obj2 = {
      //   name: `${this.curShift.KpiName}实际值`,
      //   type: ['2','3'].includes(this.curConfig.ChartType)?'bar':'line',
      //   symbol: 'circle',
      //   symbolSize: 4,
      //   data: this.curShift.KpiValues.map(item=>item.DataValue),
      //   markLine: {//目标值线条
      //     silent: true,
      //     lineStyle: {
      //       color: this.curShift.TargetColor || 'gray'
      //       // color: 'red'
      //     },
      //     data: axisMarkLine
      //     // data: [{xAxis: 20 }]
      //   }
      // }
      let list = []
      Object.keys(this.curShift.ChartData).forEach(key => {
        if (['x', 'x', '目标值'].includes(key)) {
          return
        }
        let obj = {
          // name: `${this.curShift.KpiName}实际值`,
          // name: key.split(':')[1],
          name: `${key}实际值`,
          type: ['2', '3'].includes(this.curConfig.ChartType) ? 'bar' : 'line',
          symbol: 'circle',
          symbolSize: 4,
          barWidth: 10,
          itemStyle: {
            normal: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                offset: 0,
                color: '#4391F4' // 0% 处的颜色
              }, {
                offset: 1,
                color: '#6B74E4' // 100% 处的颜色
              }], false),
              barBorderRadius: [6, 6, 6, 6]
            }
          },
          // data: this.curShift.KpiValues.map(item=>item.DataValue),
          data: this.curShift.ChartData[key],
          label: {
            show: true,
            position: 'top',
            textStyle: {
              color: '#fff'
            }
          },
          markLine: {//目标值线条
            silent: true,
            lineStyle: {
              color: this.curShift.TargetColor || 'gray'
              // color: 'red'
            },
            data: axisMarkLine
            // data: [{xAxis: 20 }]
          }
        }
        list.push(obj)
      })
      return list
    },
  },
  created() {
    this.getBarList()
  },
  mounted() {
    this.$nextTick(function () {
      this.tableHeight = window.innerHeight - 574;
      let self = this;
      window.onresize = function () {
        self.tableHeight = window.innerHeight - 574;
      }
    })
  },
  methods: {
    async getBarList() {
      let params = {
        "Position": this.position,
        "BaseTime": this.BaseTime,
        "TeamCode": this.simlevel,
        // "ProductionLineCode": this.ProductionLineCode,
        // "FactoryCode": this.FactoryCode
      }
      let { response } = await getChartStructure(params)
      this.curConfig = response
      if (this.curConfig?.ChartConfigs != null) {
        if (this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit == undefined) {
          this.title1 = this.titlebartran
        } else {
          this.title1 = this.titlebartran + '(' + this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit + ')'
        }
        // this.id = this.curConfig.ID;
        // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
        this.curConfig.ChartConfigs.map(item => {
          item.KpiName = this.curConfig.ChartConfigs.KpiName
          if (item.KpiValues[0]) {
            item.KpiCode = item.KpiValues[0].KpiCode
            item.TargetValue = item.KpiValues[0].TargetValue || 0
          }
        })
        //图表配置整体赋值
        // this.curConfig = response
        //时间颗粒度列表
        this.myShiftList = this.curConfig.ChartConfigs.filter(item => {
          return item.TargetVisible === 1
        })
        //默认激活第一个时间颗粒度
        this.curShift = this.myShiftList[0]
        this.query1()
        this.tableDataChange(this.curConfig.ChartConfigs[0]?.ChartData)
      } else {
        this.title1 = this.titlebartran
      }
    },
    async getBarList1() {
      let params = {
        "simLevel": this.position.split('-')[0],
        "position": [
          this.position
        ],
        "paramList": [
          this.simlevel,
          this.BaseTime
        ]
      }
      let { response } = await getTableList(params)
      this.curConfig = response
      if (this.curConfig?.ChartConfigs != null) {
        if (this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit == undefined) {
          this.title1 = this.titlebartran
        } else {
          this.title1 = this.titlebartran + '(' + this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit + ')'
        }
        // this.id = this.curConfig.ID;
        // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
        this.curConfig.ChartConfigs.map(item => {
          item.KpiName = this.curConfig.ChartConfigs.KpiName
          if (item.KpiValues[0]) {
            item.KpiCode = item.KpiValues[0].KpiCode
            item.TargetValue = item.KpiValues[0].TargetValue || 0
          }
        })
        //图表配置整体赋值
        // this.curConfig = response
        //时间颗粒度列表
        this.myShiftList = this.curConfig.ChartConfigs.filter(item => {
          return item.TargetVisible === 1
        })
        //默认激活第一个时间颗粒度
        this.curShift = this.myShiftList[0]
        this.query1()
        this.tableDataChange(this.curConfig.ChartConfigs[0]?.ChartData)
      } else {
        this.title1 = this.titlebartran
      }
    },
    tableDataChange(data) {
      if (data.length <= 0) return
      const keys = Object.keys(data);
      console.log(keys, 'keyskeyskeyskeys');
      this.barTitle = keys[0]
      const firstKey = keys[0];
      const secondKey = keys[1];
      const thirdKey = keys[2];
      const dataArray = data[firstKey];
      const dateArray = data[secondKey];
      const targetArray = data[thirdKey];
      this.tableData = [];
      for (let i = 0; i < dateArray.length; i++) {
        this.tableData.push({ date: dateArray[i], value: dataArray[i], target: targetArray[i] });
      }
    },
    query1() {
      if (this.chartBartc) {
        this.chartBartc.clear()
        return
      }
      this.chartBartc = this.$echarts.init(document.getElementById(this.id1 + '-123'));
      var option
      option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          }
        },
        legend: {
          data: this.lineSeries.map(item => item.name),
          textStyle: {
            color: '#fff'
          }
        },
        grid: {
          left: '5%',
          right: '4%',
          bottom: '3%',
          top: '20%',
          containLabel: true
        },
        // visualMap: this.lineVisualMap,
        xAxis: this.yAxisOption,
        yAxis: this.xAxisOption,
        series: this.lineSeries
      }
      this.chartBartc.setOption(option, true);
      window.addEventListener("resize", () => {
        this.chartBartc.resize()
      }, false);
    },
    handleClose1() {
      this.$emit('showCheck4')
    }
  }
}
</script>
<style scoped>
::v-deep .noDataBox {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
}
/deep/ .el-dialog__title {
    color: #fff !important;
}
/deep/ .el-dialog {
    background: var(--background) no-repeat 0 0;
    background-size: 100% 100% !important;
    overflow: hidden;
}
/deep/ .el-textarea__inner {
    font-size: 16px !important;
}
/deep/ .el-dialog__headerbtn .el-dialog__close {
    font-size: 22px;
}
/deep/ .el-dialog__header {
    border: none !important;
}
/deep/.el-textarea__inner {
    background-color: rgba(225, 225, 225, 0);
    border: none !important;
    color: black;
    font-size: 16px;
    /* font-weight: bold; */
    /* font-family: "Lucida Calligraphy", cursive, serif, sans-serif; */
}

/deep/ .el-dialog__headerbtn .el-dialog__close {
    font-size: 22px;
}
/deep/ .el-dialog__header {
    border: none !important;
}

/deep/.el-textarea__inner {
    /* background-color: rgba(225, 225, 225, 0); */
    background-color: #fff !important;
    border: none !important;
    color: black;
    font-size: 20px;
    /* font-weight: bold; */
}
/deep/.el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}
/deep/ .el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}

/deep/ .el-table tr {
    background-color: transparent !important;
    border: none;
}
/deep/ .el-table--enable-row-transition .el-table__body td,
.el-table .cell {
    background-color: transparent !important;
}
/deep/ .el-table th.el-table__cell {
    background-color: transparent !important;
    color: #fff;
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}
/deep/ .el-dialog__title {
    color: #fff !important;
}

/deep/ .el-table tbody tr {
    pointer-events: none;
}
</style>
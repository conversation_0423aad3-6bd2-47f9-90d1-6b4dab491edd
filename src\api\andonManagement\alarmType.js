import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_ANDON'
// 告警类型

//获取告警类型列表
export function getAlarmTypeTreetList(data) {
    const api = '/andon/AlarmType/GetTreeList'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取一级告警类型列表
export function getAlarmTypeRootList(data) {
    const api = '/andon/AlarmType/GetRootList'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取一级告警类型ID获取二级告警类型列表
export function GetListByAlarmId(data) {
    const api = '/andon/AlarmType/GetListByAlarmId'
    return getRequestResources(baseURL, api, 'post', data, true);
}
//新增、编辑告警类型
export function AlarmTypeSaveForm(data) {
    const api = '/andon/AlarmType/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除告警类型
export function DeleteAlarmType(data) {
    const api = '/andon/AlarmType/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//更新告警类型
export function UpdateAlarmTypeStatus(data) {
    const api = '/andon/AlarmType/UpdateStatus'
    return getRequestResources(baseURL, api, 'post', data);
}

//告警设置列表
export function GetAlarmTypePropertyPageList(data) {
    const api = '/andon/AlarmTypePropertyValue/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

//告警设置列表保存
export function AlarmTypePropertyValueSaveForm(data) {
    const api = '/andon/AlarmTypePropertyValue/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}

//告警设置列表删除
export function AlarmTypePropertyValueDelete(data) {
    const api = '/andon/AlarmTypePropertyValue/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

//告警设置列表删除
export function AlarmSolutionDelete(data) {
    const api = '/api/AlarmSolution/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

//告警设置列表删除
export function AlarmSolutionSaveForm(data) {
    const api = '/api/AlarmSolution/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}

//告警设置列表删除
export function AlarmSolutionInsert(data) {
    const api = '/api/AlarmSolution/Insert'
    return getRequestResources(baseURL, api, 'post', data);
}


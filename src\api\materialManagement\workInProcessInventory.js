import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_MATERIAL'
// 仓库建模

//分页获取在制品统计列表
export function GetGroupPageList(data) {
    const api = '/materail/WipReserve/GetGroupPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

//分页获取在制品列表
export function getWorkInProcess(data) {
    const api = '/materail/WipReserve/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 在制品移库
export function moveWarehouse(data) {
    const api = '/materail/WipReserve/MMStockTransfer'
    return getRequestResources(baseURL, api, 'post', data);
}

// 新增  编辑在制品表单保存
export function saveWorkInProcessForm(data) {
    const api = '/materail/WipReserve/SaveForm1'
    return getRequestResources(baseURL, api, 'post', data);
}

// 删除在制品库存
export function deleteWorkInProcess(data) {
    const api = '/materail/WipReserve/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取AGV仓库list
export function getAGVList(data) {
    const api = '/materail/AgvwarehouseManage/GetWarehouseList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 根据MES库获取AGV仓库list
export function getAGVListByMes(data) {
    const api = '/materail/AgvWarehouse/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}


// 获取AGV库区list
export function getAGVAreaList(data) {
    const api = '/materail/AgvwarehouseManage/GetWarehouseareaList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取AGV库区list
export function getAGVSeatList(data) {
    const api = '/materail/AgvwarehouseManage/GetWarehousePostionList'
    return getRequestResources(baseURL, api, 'post', data);
}
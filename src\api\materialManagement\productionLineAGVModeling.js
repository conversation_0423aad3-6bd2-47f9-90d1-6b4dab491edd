import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_MATERIAL'
// 产线工站库位关系建模

//分页获取关系建模列表
export function getAgvLoadingAreaPageList(data) {
    const api =  '/materail/AgvLoadingArea/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑关系建模
export function AgvLoadingAreaSaveForm(data) {
    const api =  '/materail/AgvLoadingArea/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除关系建模
export function DeleteLineAgvLoadingArea(data) {
    const api =  '/materail/AgvLoadingArea/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
// 获取上料区
export function GetAgvLoadingAreaList(data) {
    const api =  '/materail/AgvwarehouseManage/GetAgvLoadingAreaList'
    return getRequestResources(baseURL, api, 'post', data);
}

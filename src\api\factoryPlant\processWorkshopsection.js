import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url

//获取列表数据
export function GetPageList(data) {
    return request({
        url: baseURL + '/api/SegmentProcess/GetPageList',
        method: 'post',
        data
    });
}

//获取不分页列表数据
export function GetList(data) {
    return request({
        url: baseURL + '/api/SegmentProcess/GetList',
        method: 'post',
        data
    });
}
//新增数据
export function SegmentProcessSaveForm(data) {
    return request({
        url: baseURL + '/api/SegmentProcess/SaveForm',
        method: 'post',
        data
    });
}
//删除岗位数据
export function SegmentProcessDelete(data) {
    return request({
        url: baseURL + '/api/SegmentProcess/Delete',
        method: 'post',
        data
    });
}

// 数据导入
export function doImport(data) {
    return request({
        url: baseURL + '/api/SegmentProcess/ImportData',
        method: 'post',
        data
    });
}
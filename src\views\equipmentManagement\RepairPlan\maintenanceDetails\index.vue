<template>
    <Tables :page-options="pageOptions" :loading="loading" :btn-list="btnList" :dictionaryList="dictionaryList"
        tableHeight="calc(50vh - 150px)" table-name="TPM_SBGL_SBWXJL_WXMX" :headers="RepairPlanColum" :desserts="desserts"
        @selectePages="selectePages" @tableClick="tableClick" @itemSelected="SelectedItems"
        @toggleSelectAll="SelectedItems"></Tables>
</template>
<script>
import { DeviceRepairGetPageList, DeviceRepairDelete } from '@/api/equipmentManagement/Repair.js';
import { RepairPlanColum } from '@/columns/equipmentManagement/Repair.js';
const IsCases = [
    {
        lable: 0,
        value: '是'
    },
    {
        lable: 1,
        value: '否'
    }
];
export default {
    props: {
        rowtableItem: {
            type: Object,
            default: () => { }
        },
        equipStatuslist: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            IsCases,
            loading: false,
            //查询条件
            RepairPlanColum,
            desserts: [],
            papamstree: {
                key: null,
                pageIndex: 1,
                pageSize: 20
            },
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            hasChildren: {} // 新增字典详情判断-子节点才能新增
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBWXJL_WXMX_DELETE'
                }
            ];
        },
        dictionaryList() {
            return [
                { arr: this.equipStatuslist, key: 'RepairStatus', val: 'ItemValue', text: 'ItemName' },
                { arr: this.IsCases, key: 'IsCase', val: 'lable', text: 'value' }
            ];
        }
    },
    created() { },
    mounted() {
        // this.RepastInfologGetPage();
    },
    methods: {
        // 维修记录列表查询
        async RepastInfologGetPage(item) {
            let params = {
                woid: item.ID || this.rowtableItem.ID,
                pageIndex: this.papamstree.pageIndex,
                pageSize: this.papamstree.pageSize
            };
            this.loading = true;
            const res = await DeviceRepairGetPageList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'repair':
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'debug':
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await DeviceRepairDelete(params);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.RepastInfologGetPage(this.rowtableItem);
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfologGetPage(this.rowtableItem);
        }
    }
};
</script>
<style lang="scss" scoped></style>
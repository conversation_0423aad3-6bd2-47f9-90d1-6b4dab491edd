// 损耗记录 dictionary: true
export const lossRecordsColumns = [
    {
        text: '序号',
        value: 'Index',
        width: 60
    },
    // { text: '事件ID', value: 'AlarmEventId', width: 140 },
    { text: '订单ID', value: 'WoId', width: 160 },
    // { text: '事件号', value: 'EventNo', width: 100 },
    { text: '原因', value: 'ReasonCode', width: 120, dictionary: true },
    { text: '人工数量', value: 'ManQuantity', width: 120, semicolonFormat: true },
    { text: '设备数量', value: 'DEVICE_Quantity', width: 120, semicolonFormat: true },
    { text: '单位', value: 'Unit', width: 120 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: 200
    }
];

// 报警记录详情 dictionary: true
export const alarmRecordDetails = [
    {
        text: '序号',
        value: 'Index',
        width: 60
    },
    { text: '阶段', value: 'Process', width: 80 },
    { text: '描述', value: 'ActionType', width: 80 },
    { text: '通知对象', value: 'Receiver', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '开始时差', value: 'StartTimeDiff', width: 100 },
    // { text: '上一步时差', value: 'PreTimeDiff', width: 100 },
    {
        text: '',
        align: 'center',
        value: 'noActions',
        width: 0
    }
];

// 升级路线 dictionary: true
export const upgradeColumns = [
    {
        text: '序号',
        value: 'Index',
        width: 60
    },
    { text: '等级', value: 'EventLevel', width: 80 },
    { text: '目标角色', value: 'Duty', width: 100, dictionary: true },
    // { text: '目标人员', value: 'Receiver', width: 120 },
    { text: '升级时间（min)', value: 'OutTime', width: 160 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    {
        text: '',
        align: 'center',
        value: 'noActions',
        width: 0
    }
];

// 损耗 dictionary: true
export const lossColumns = [
    {
        text: '序号',
        value: 'Index',
        width: 60
    },
    { text: '物料号', value: 'Process', width: 160 },
    { text: '物料名称', value: 'EquipmentCode', width: 160 },
    { text: 'Scada统计量', value: 'Receiver', width: 120 },
    { text: '最终损耗量', value: 'CreateDate', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    {
        text: '',
        align: 'center',
        value: 'noActions',
        width: 0
    }
];
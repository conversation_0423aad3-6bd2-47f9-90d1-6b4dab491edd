import { getRequestResources } from '@/api/fetch';
const DFM = 'baseURL_DFM'

// 查询成品料号列表
export function getMaterialList(data) {
    const api = '/api/Material/GetPageList'
    return getRequestResources(DFM, api, 'post', data)
}
// 表单提交
export function saveForm(data) {
    const api = '/api/Productlineouttag/SaveForm'
    return getRequestResources(DFM, api, 'post', data)
}
// 查询配置列表
export function getCollectionPointConfigList(data) {
    const api = '/api/Productlineouttag/GetPageList'
    return getRequestResources(DFM, api, 'post', data)
}
// 删除配置数据
export function delCollectionPointConfig(data) {
    const api = '/api/Productlineouttag/Delete'
    return getRequestResources(DFM, api, 'post', data)
}
//  导入
export function doImport(data) {
    const api = '/api/Productlineouttag/ImportExcel'
    return getRequestResources(DFM, api, 'post', data)
}
// 批量禁用 启用
export function handleChangeEnable(data) {
    const api = '/api/Productlineouttag/UpdateIsenabled'
    return getRequestResources(DFM, api, 'post', data)
}
// 导出
export function doExport(data) {
    const api = '/api/Productlineouttag/ExportExcelTemplates'
    return getRequestResources(DFM, api, 'post', data)
}
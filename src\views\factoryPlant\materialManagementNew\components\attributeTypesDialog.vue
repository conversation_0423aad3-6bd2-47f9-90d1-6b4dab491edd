<template>
    <v-dialog v-model="sttributeTypesDialog" persistent max-width="980px">
        <!-- 字典分类 查询 -->
        <v-card ref="form" class="sttribute-type">
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                物料属性类型
                <v-icon @click="sttributeTypesDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-container>
                    <div class="sttribute-type-title">
                        <div class="sttribute-type-title-btns">
                            <v-btn icon color="primary">
                                <v-icon>mdi-cached</v-icon>
                            </v-btn>
                            <v-btn-toggle v-model="toggle_exclusive" dense>
                                <v-btn color="primary" large>{{ $t('GLOBAL._XZ') }}</v-btn>
                                <v-btn color="primary" large>{{ $t('GLOBAL._BJ') }}</v-btn>
                                <v-btn color="primary" large>{{ $t('GLOBAL._SC') }}</v-btn>
                            </v-btn-toggle>
                        </div>
                    </div>
                    <v-data-table :headers="headers" :items="originData" :items-per-page="itemsPerPage"
                        hide-default-footer class="elevation-1" item-key="xh"></v-data-table>
                </v-container>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script>
import { getSaveForm, getDelete } from '@/api/factoryPlant/supplier.js';
export default {
    name: 'ClassificationDialog',
    props: {
        editList: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            // 提交表单数据
            valid: true,
            sttributeTypesDialog: false,
            checkbox: false,
            formModel: {
                causeCode: null,
                causeName: null,
                causeType: null
            },
            state: {},
            page: 1,
            pageCount: 1,
            itemsPerPage: 10,
            originData: [
                {
                    xh: '1',
                    num: '11',
                    mc: 'mc',
                    cb: 'cb',
                    ss: 'ss',
                    ms: 'ms',
                    yx: 'leix',
                    sc: 'geshi'
                }
            ],
            dataList: [],
            headers: [
                { text: '序号', value: 'xh' },
                { text: '名称', value: 'mc' },
                { text: '编号', value: 'num' },
                { text: '类型', value: 'ss' },
                { text: '长度', value: 'cb' },
                { text: '是否为空', value: 'ms' },
                { text: '数据字典类型', value: 'yx' },
                { text: '格式检查', value: 'sc' }
            ],
            toggle_exclusive: ''
        };
    },
    computed: {
        editformModel() {
            return {
                causeCode: this.editList.causeCode,
                causeName: this.editList.causeName,
                causeType: this.editList.causeType
            };
        }
    },
    created() {
        // this.initData();
    },
    methods: {
        // 随机设置条数
        initData() {
            const num = 1 + Math.floor(Math.random() * Math.random() * 20);
            for (let index = 1; index < num + 1; index++) {
                const obj = {
                    xh: index,
                    num: '11' + index,
                    mc: 'mc' + index,
                    cb: 'cb' + index,
                    ss: 'ss' + index,
                    ms: 'ms' + index,
                    yx: index % 2 === 0 ? '是' : '否',
                    sc: index % 3 === 0 ? '是' : '否'
                };
                this.dataList.push(obj);
            }
        },
        // 查询
        searchForm() {
            console.log(1111);
        },
        //新增
        addClick() { },
        async addSubmit() {
            let params = {
                isOutsourcing: this.isOutsourcing,
                supplierCode: this.supplierCode,
                supplierName: this.supplierName,
                tel: this.tel,
                address: this.address,
                remark: this.remark,
                id: '',
                createUserId: 'text'
            };
            let res = await getSaveForm(params);
            let { status, success, msg } = res;
            alert(msg);
            this.dialog = false;
            this.$parent.supplierList();
        },
        //编辑
        async editSubmit() {
            let params = {
                isOutsourcing: this.editList.IsOutsourcing,
                supplierCode: this.editList.SupplierCode,
                supplierName: this.editList.SupplierName,
                tel: this.editList.Tel,
                address: this.editList.Address,
                remark: this.editList.Remark,
                id: this.editList.ID,
                createUserId: 'text',
                updateTimeStamp: this.editList.UpdateTimeStamp,
                modifyUserId: this.editList.ModifyUserId
            };
            let res = await getSaveForm(params);
            let { status, success, msg } = res;
            this.dialog = false;
            this.$parent.supplierList();
        },
        // 删除
        async delSubmit() {
            await getDelete([this.deleteList.ID]);
            this.dialog = false;
            this.$parent.supplierList();
        }
    }
};
</script>
<style lang="scss" scoped>
.sttribute-type {
    height: 380px !important;
}

.sttribute-type-title {
    width: 100%;
    height: 58px !important;
    display: flex;
    justify-content: flex-end;
    justify-items: center;

    .sttribute-type-title-btns {
        display: flex;
        justify-content: space-between;

        .v-btn {
            height: 39px !important;
        }
    }
}

.v-text-field {
    margin-right: 16px;
}

.col-12 {
    padding: 0;
}

.v-sheet.v-card {
    border-radius: 10px;
}
</style>

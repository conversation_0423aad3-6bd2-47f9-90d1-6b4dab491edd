<template>
    <div class="disassemble-defective-products">
        <SearchForm class="mt-1" :show-from="showForm" :searchinput="searchInputs" @searchForm="searchForm" />
        <div class="form-btn-list">
            <v-btn icon class="float-left mx-4" @click="showForm = !showForm">
                <v-icon>{{ 'mdi-table-search' }}</v-icon>
                {{ $t('GLOBAL._SSL') }}
            </v-btn>
            <v-btn icon color="primary" @click="getdata">
                <v-icon>mdi-cached</v-icon>
            </v-btn>
            <v-btn color="primary" v-has="'SBTJJL_TJMX'" :disabled="!selectList.length" @click="addDetail()">{{
                $t('DFM_SBTJJL._TJMX') }}</v-btn>
            <!-- <v-btn color="primary">{{ $t('GLOBAL._PLSC') }}</v-btn> -->
        </div>
        <!-- tableHeight="320" -->
        <div class="pb-2 pl-2">{{ $t('DFM_SBTJJL._CJJL') }}</div>
        <Tables ref="table" tableHeight="320" table-name="TPM_SBTJJL" :page-options="pageOptions" :clickFun="getDetailData"
            :loading="loading" :headers="headers" :currentSelectId="currentSelectId" :desserts="tableList"
            @selectePages="selectePages" @itemSelected="SelectedItems" @toggleSelectAll="SelectedItems"></Tables>
        <div class="pt-2 pl-2">{{ $t('DFM_SBTJJL._TZJL') }}</div>
        <Tables class="mt-3" ref="detailTable" tableHeight="320" table-name="TPM_SBTJJL_XQ"
            :page-options="detailPageOptions" :loading="detailLoading" :headers="detailHeaders" :desserts="detailTableList"
            @selectePages="detailSelectePages" @tableClick="detailTableClick">
            <template #actions="{ item }">
                <v-btn v-for="(list, index) in btnList" :key="index" :disabled="!item.ID" text small class="ma-0 pa-0 ml-1"
                    :color="list.type" @click.stop="detailTableClick(item, list.code)">
                    {{ list.text }}
                </v-btn>
            </template>
        </Tables>

        <v-dialog scrollable persistent v-model="isShowEditPopup" width="55%">
            <EditPopup v-if="isShowEditPopup" @getDetailData="getDetailData" :type="type" @closePopup="closePopup"
                :selectList="selectList" :editItemObj="editItemObj" />
        </v-dialog>
    </div>
</template>

<script>
import { getRecordList, getReasonList, getRecordDetailList, deleteStopDetail } from './service';
import { stopRecordColumns, stopDetailRecordColumns } from '@/columns/equipmentManagement/equipmentShutdownRecord.js';
import EditPopup from './components/editPopup.vue';
import dayjs from 'dayjs';
import Util from '@/util';
export default {
    components: {
        EditPopup
    },
    data() {
        return {
            type: '',
            selectList: [],
            currentSelectId: null,
            reasonList: [],
            shiftList: [{ name: '白班' }, { name: '夜班' }],
            lineList: [],
            segmentList: [],
            workstationList: [],
            isShowEditPopup: false,
            editItemObj: {},
            loading: false,
            detailLoading: false,
            showForm: false,
            paramsObj: {
                Date: dayjs().format('YYYY-MM-DD')
            },
            tableList: [],
            detailTableList: [],
            headers: stopRecordColumns,
            detailHeaders: stopDetailRecordColumns,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100]
            },
            detailPageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100]
            }
        };
    },
    computed: {
        searchInputs() {
            let list = [
                {
                    value: dayjs().format('YYYY-MM-DD'),
                    icon: 'mdi-account-check',
                    label: this.$t('DFM_SBTJJL._RQ'),
                    type: 'date',
                    placeholder: '',
                    key: 'Date'
                },
                {
                    value: '',
                    icon: 'mdi-account-check',
                    label: this.$t('DFM_SBTJJL._CX'),
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.lineList, 'EquipmentCode', 'EquipmentName'),
                    placeholder: '',
                    key: 'Line'
                },
                {
                    value: '',
                    icon: 'mdi-account-check',
                    label: this.$t('DFM_SBTJJL._GD'),
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.segmentList, 'Remark', 'Remark'),
                    placeholder: '',
                    key: 'Segment'
                },
                {
                    value: '',
                    icon: 'mdi-account-check',
                    label: this.$t('DFM_SBTJJL._GZ'),
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.workstationList, 'EquipmentName', 'EquipmentName'),
                    placeholder: '',
                    key: 'Unit'
                },
                {
                    value: '',
                    icon: 'mdi-account-check',
                    label: this.$t('DFM_SBTJJL._BZ'),
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.shiftList, 'name', 'name'),
                    placeholder: '',
                    key: 'Shift'
                },
                {
                    value: '',
                    icon: 'mdi-account-check',
                    label: this.$t('DFM_SBTJJL._TJYY'),
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.reasonList, 'ReasontreeName', 'ReasontreeName'),
                    placeholder: '',
                    key: 'Reason'
                }
            ];
            return list;
        },
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: ''
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: ''
                }
            ];
        }
    },
    created() {
        this.getReason();
        this.getLineData();
        this.getSegmentData();
        this.getWorkStationData();
        this.getdata();
    },
    methods: {
        closePopup() {
            this.isShowEditPopup = false;
        },
        SelectedItems(item) {
            this.selectList = item;
        },
        addDetail() {
            this.editItemObj = {};
            this.type = 'add';
            this.isShowEditPopup = true;
        },
        delData(ids) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let resp = await deleteStopDetail(ids);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });

                    this.getDetailData();
                })
                .catch(() => { });
        },
        async getDetailData(data) {
            let obj = {};
            if (data) {
                this.currentSelectId = data.ID;
                obj = data;
            } else {
                obj = this.tableList.find(item => item.ID == this.currentSelectId);
            }
            const { ID } = obj;
            let params = {
                DataId: ID,
                pageIndex: this.detailPageOptions.page,
                pageSize: this.detailPageOptions.pageSize
            };
            this.detailLoading = true;
            try {
                let resp = await getRecordDetailList({ ...params });
                this.detailLoading = false;
                this.detailTableList = resp.response.data;
                this.detailPageOptions.pageCount = resp.response.pageCount;
                this.detailPageOptions.total = resp.response.dataCount;
            } catch {
                this.detailLoading = false;
            }
        },
        // 获取原因列表
        async getReason() {
            let resp = await getReasonList();
            this.reasonList = resp.response.filter(item => item.ReasontreeType == 'OEE');
        },
        // 获取产线列表
        async getLineData() {
            this.lineList = await Util.GetEquipmenByLevel('Area');
        },
        // 获取工段列表
        async getSegmentData() {
            this.segmentList = await Util.GetEquipmenByLevel('Line');
        },
        // 获取工站列表
        async getWorkStationData() {
            this.workstationList = await Util.GetEquipmenByLevel('Segment');
        },
        async getdata() {
            this.loading = true;
            try {
                let resp = await getRecordList({ ...this.paramsObj, pageIndex: this.pageOptions.page, pageSize: this.pageOptions.pageSize });
                this.loading = false;
                this.tableList = resp.response.data;
                this.detailTableList = [];
                this.currentSelectId = null;
                this.detailPageOptions.page = 1;
                this.pageOptions.pageCount = resp.response.pageCount;
                this.pageOptions.total = resp.response.dataCount;
            } catch {
                this.loading = false;
            }
        },
        searchForm(params) {
            this.paramsObj = params;
            this.pageOptions.page = 1;
            this.getdata();
        },
        // 分页操作
        selectePages(data) {
            this.pageOptions.page = data.pageCount;
            this.pageOptions.pageSize = data.pageSize;
            this.getdata();
        },
        // 分页操作
        detailSelectePages(data) {
            this.detailPageOptions.page = data.pageCount;
            this.detailPageOptions.pageSize = data.pageSize;
            // this.getdata();
        },
        detailTableClick(item, type) {
            switch (type) {
                case 'edit':
                    this.editItemObj = item;
                    this.type = type;
                    this.isShowEditPopup = true;
                    break;
                case 'delete':
                    this.delData([item.ID]);
                    break;
            }
        }
    }
};
</script>

<style></style>
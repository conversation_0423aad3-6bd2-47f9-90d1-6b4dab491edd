<template>
    <v-dialog v-model="dialog" persistent max-width="980px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ this.$t('ANDON_BJJL.alarmList') }}
                <v-icon @click="closePopup">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="mt-2">
                <Tables
                    ref="tablePath"
                    :dictionaryList="dictionaryList"
                    :showSelect="false"
                    table-height="calc(100vh - 260px)"
                    :loading="loading"
                    :headers="alarmRecordDetails"
                    :desserts="desserts"
                    :footer="false"
                    :page-options="pageData"
                    table-name="ANDON_BJJL"
                ></Tables>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script>
import { alarmRecordDetails } from '@/columns/andonManagement/alarmRecord.js';
import { GetListByEventNo } from '@/api/andonManagement/alarmRecord.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        productLineList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            dialog: '',
            loading: false,
            alarmRecordDetails,
            currentSelectId: '',
            currentSelect: {},
            desserts: [],
            pageData: {
                pageSize: 1000,
                page: 1,
                total: 0,
                pageCount: 0,
                pageSizeitems: [10, 20, 30, 40]
            },
            typeChildList: []
        };
    },
    computed: {
        dictionaryList() {
            return [{ arr: this.typeChildList, key: 'SubAlarmType', val: 'AlarmCode', text: 'AlarmName' }];
        }
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal) {
                    this.getData();
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        async getData() {
            const res = await GetListByEventNo({ eventNo: this.operaObj.EventNo });
            const { success, response } = res;
            if (success) {
                this.desserts = response || [];
            }
        },
        closePopup() {
            this.dialog = false;
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .v-sheet.v-card:not(.v-sheet--outlined) {
    box-shadow: none;
}
</style>
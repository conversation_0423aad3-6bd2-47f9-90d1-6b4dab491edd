<template>
  <el-dialog
    :style="backgroundVar"
    title="事故列表"
    :append-to-body="!fullscreen"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="value"
    :before-close="handleClose2"
    lock-scroll
    :fullscreen="true"
  >
    <div class="styleTable">
      <!-- <v-card>
        <v-card-title
          class="headline primary lighten-2"
          primary-title
        >
          {{ this.editItemObj.ID ? '编辑事故' : '新增事故' }}
        </v-card-title>
        <v-card-text style="padding: 0 10px;">
         
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions style="justify-content: flex-end;">
          <v-btn
            color="primary"
            @click="submitForm"
          >{{ $t('GLOBAL._QD') }}</v-btn>
          <v-btn @click="cancel">{{ $t('GLOBAL._QX') }}</v-btn>
        </v-card-actions>
      </v-card> -->
      <v-form
        ref="form"
        v-model="valid"
      >
        <v-row class="mt-5">
          <v-col
            :cols="12"
            :lg="6"
            class="pt-0 pb-0"
          >
            <v-menu
              :close-on-content-click="true"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              max-width="290px"
              min-width="290px"
            >
              <template #activator="{ on, attrs }">
                <v-text-field
                  v-model="form.PresentDate"
                  :clearable="true"
                  outlined
                  dense
                  label="发起日期"
                  readonly
                  v-bind="attrs"
                  v-on="on"
                >
                </v-text-field>
              </template>
              <v-date-picker
                v-model="form.PresentDate"
                placeholder="发起日期"
                :locale="locale"
              >
              </v-date-picker>
            </v-menu>
          </v-col>
          <v-col
            :cols="12"
            :lg="6"
            class="pt-0 pb-0  mb-5 "
          >
            <Treeselect
              :disabled="true"
              @select="getTeamList"
              disableBranchNodes
              v-model="form.PresentProdProcessId"
              placeholder="所属部门"
              noChildrenText="暂无数据"
              noOptionsText="暂无数据"
              :default-expand-level="4"
              :normalizer="normalizer"
              :options="EquipmentProductLineTree1"
              :rules="rules"
            />
          </v-col>
          <v-col
            :cols="12"
            :lg="6"
            class="pt-0 pb-0"
          >
            <!-- <Treeselect v-model="form.TeamId" placeholder="发现班组" noChildrenText="暂无数据"
                              noOptionsText="暂无数据" :default-expand-level="4" :normalizer="normalizer"
                              :options="EquipmentTeamTree" @select="handleChangeSelectTree" :rules="rules" /> -->
            <v-select
              v-model="form.TeamId"
              @select="changeTeam"
              :items="teamList"
              label="发现班组"
              item-text="Name"
              item-value="BoxTeamCode"
              required
              dense
              outlined
            ></v-select>
          </v-col>
          <v-col
            :cols="12"
            :lg="6"
            class="pt-0 pb-0 "
          >
            <!-- <Treeselect
                              v-model="form.PresentUserId"
                              placeholder="提出人"
                              noChildrenText="暂无数据"
                              noOptionsText="暂无数据"
                              :default-expand-level="4"
                              :normalizer="normalizer"
                              :options="companyTree"
                              @select="handleChangeSelectTree"
                              :rules="rules"
                          />  -->
            <!-- <v-select 
                              v-model="form.PresentUserId" 
                              :items="StaffList" 
                              label="提出人"
                              item-text="Name" 
                              item-value="Code"
                              required dense outlined
                              ></v-select> -->
            <!-- <v-autocomplete
              v-model="form.PresentUserId"
              :items="StaffList"
              item-text="Name"
              item-value="Code"
              clearable
              dense
              outlined
              label="提出人"
              placeholder="提出人"
            >
            </v-autocomplete> -->
            <v-text-field
              v-model="form.PresentUserId"
              outlined
              dense
              label="提出人"
              disabled
            >
            </v-text-field>
          </v-col>
          <v-col
            :lg="6"
            class="pt-0 pb-0 "
          >
            <v-select
              v-model="form.ClassfyCode"
              :items="ClassfyCodeList"
              label="事故类型"
              item-text="ItemName"
              item-value="ItemValue"
              required
              dense
              outlined
            ></v-select>
          </v-col>
          <v-col
            :cols="12"
            :lg="6"
            class="pt-0 pb-0 "
          >
            <!-- <Treeselect
              v-model="form.ResponsibleDepartmentId"
              disableBranchNodes
              placeholder="责任部门"
              noChildrenText="暂无数据"
              noOptionsText="暂无数据"
              :default-expand-level="4"
              :normalizer="normalizer"
              :options="EquipmentProductLineTree"
              @select="handleChangeSelectTree"
              :rules="rules"
            /> -->
            <v-select
              v-model="form.ResponsibleDepartmentId"
              @change="handleChangeDepart"
              :items="LineList"
              label="责任部门"
              item-text="DepartMentName"
              item-value="DepartMentCode"
              required
              dense
              outlined
            ></v-select>
            <!-- <v-select v-model="form.ResponsibleDepartmentId" :items="DepartmentList" label="责任部门"
                              item-text="Fullname" item-value="ID" required dense outlined></v-select> -->
          </v-col>
          <v-col
            :cols="12"
            :lg="6"
            class="pt-0 pb-0 "
          >
            <!-- <Treeselect
                              v-model="form.ResponsibleUserId"
                              placeholder="负责人"
                              noChildrenText="暂无数据"
                              noOptionsText="暂无数据"
                              :default-expand-level="4"
                              :normalizer="normalizer"
                              :options="companyTree"
                              @select="handleChangeSelectTree"
                              :rules="rules"
                          />  -->
            <!-- <v-select 
                              v-model="form.ResponsibleUserId" 
                              :items="StaffList" 
                              label="负责人"
                              item-text="Name" 
                              item-value="Code"
                              required dense outlined
                              ></v-select> -->
            <!-- <v-autocomplete
              v-model="form.ResponsibleUserId"
              :items="StaffList"
              item-text="Name"
              item-value="Code"
              clearable
              dense
              outlined
              label="负责人"
              placeholder="负责人"
            >
            </v-autocomplete> -->
            <v-autocomplete
              v-model="form.ResponsibleUserId"
              :items="staffListByDepart"
              item-text="UserName"
              item-value="LoginName"
              clearable
              dense
              outlined
              label="责任人"
              placeholder="责任人"
            >
            </v-autocomplete>
          </v-col>

          <v-col
            :cols="12"
            :lg="6"
            class="pt-0 pb-0  "
          >
            <v-menu
              :close-on-content-click="true"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              max-width="290px"
              min-width="290px"
            >
              <template #activator="{ on, attrs }">
                <v-text-field
                  v-model="form.PlanFinishDate"
                  :clearable="true"
                  outlined
                  dense
                  label="计划完成日期"
                  readonly
                  v-bind="attrs"
                  v-on="on"
                >
                </v-text-field>
              </template>
              <v-date-picker
                v-model="form.PlanFinishDate"
                placeholder="计划完成日期"
                :locale="locale"
              >
              </v-date-picker>
            </v-menu>
          </v-col>
          <v-col
            v-if="editItemObj.ID"
            :cols="12"
            :lg="6"
            class="pt-0 pb-0   "
          >
            <v-menu
              :close-on-content-click="true"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              max-width="290px"
              min-width="290px"
            >
              <template #activator="{ on, attrs }">
                <v-text-field
                  v-model="form.ActualFinishDate"
                  :clearable="true"
                  outlined
                  dense
                  label="实际完成日期"
                  readonly
                  v-bind="attrs"
                  v-on="on"
                >
                </v-text-field>
              </template>
              <v-date-picker
                v-model="form.ActualFinishDate"
                placeholder="实际完成日期"
                :locale="locale"
              >
              </v-date-picker>
            </v-menu>
          </v-col>
          <v-col
            :cols="12"
            :lg="6"
            class="pt-0 pb-0  "
          >
            <v-menu
              :close-on-content-click="true"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              max-width="290px"
              min-width="290px"
            >
              <template #activator="{ on, attrs }">
                <v-text-field
                  v-model="form.ActualFinishDate"
                  :clearable="true"
                  outlined
                  dense
                  label="事故关闭日期"
                  readonly
                  v-bind="attrs"
                  v-on="on"
                >
                </v-text-field>
              </template>
              <v-date-picker
                v-model="form.ActualFinishDate"
                placeholder="事故关闭日期"
                :locale="locale"
              >
              </v-date-picker>
            </v-menu>
          </v-col>
          <v-col
            :cols="12"
            :lg="12"
            class="pt-0 pb-0"
          >
            <v-textarea
              v-model="form.AccidentDesc"
              clearable
              label="问题描述"
              outlined
              dense
              required
            ></v-textarea>
          </v-col>
          <v-col
            :cols="12"
            :lg="12"
            class="pt-0 pb-0"
          >
            <v-textarea
              v-model="form.MeasureDesc"
              clearable
              label="采取的措施"
              outlined
              dense
              required
            ></v-textarea>
          </v-col>

        </v-row>
      </v-form>
      <el-button
        style="float: right;margin-top: 20px;"
        type="primary"
        size="mini"
        @click="submitForm"
      >保 存</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { mapGetters } from 'vuex';
import dayjs from 'dayjs'
import { AddSafeAccident, getTeam, getStaffByDepartId } from '@/views/simManagement/sim1/service.js';

export default {
  props: {
    // 是否显示弹出框
    value: {
      type: Boolean,
      default: false
    },
    curTeamTreeObj: {
      type: Object,
      default: () => { }
    },
    searchFormObj: {
      type: Object,
      default: () => { }
    },
    editItemObj: {
      type: Object,
      default: () => { }
    },
    backgroundImg: {
      type: String,
      default: ''
    },
    fullscreen: {
      type: Boolean,
      default: false
    },
    currentYearMonth1: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      EquipmentProductLineTree1: [],
      teamList: [],
      staffListByDepart: [],
      valid: false,//校验
      // isChecked: true,//确定并关闭
      form: {
        ReceiveDepartmentId: '',
        ResponsiblePlantId: '',
        PresentPlantId: '',
        PresentProdProcessId: null, // 事故所在产线
        ClassfyCode: '',
        ID: undefined,
        PresentDate: this.currentYearMonth1,// 提起时间
        // PresentDepartmentId:  undefined,//提起部门
        PresentUserId: this.$store.getters.getUserinfolist[0].LoginName || undefined,//提出人
        ResponsibleDepartmentId: '',// 责任部门ID
        ResponsibleUserId: '',// 责任人ID
        TeamId: '',//发现班组ID
        PlanFinishDate: '',//计划完成日期
        ActualFinishDate: dayjs().format('YYYY-MM-DD'),//实际完成日期
        AccidentDesc: '',//事故描述
        MeasureDesc: '',//采取的措施
      },
      rules: [v => !!v || this.$t('GLOBAL._MANDATORY')],

      departmentData: [],
      normalizer(node) {
        return {
          id: node.id,
          label: node.name,
          children: node.children
        };
      },
    }
  },
  computed: {
    ...mapGetters(['getUserinfolist']),
    ...mapGetters(['DefaultDepartmentPermission']),
    ...mapGetters(['DepartmentPermission']),
    curAuth() {
      return this.$store.getters.getUserinfolist[0]
    },
    curAuth1() {
      return this.$store.getters.DefaultDepartmentPermission
    },
    curAuth2() {
      return this.$store.getters.DepartmentPermission
    },
    OriEquipmentTeamTree() {
      return this.$store.state.sim.OriEquipmentTeamTree
    },
    LineList() {
      return this.$store.state.sim.LineList
    },
    backgroundVar() {
      return {
        '--background': this.backgroundImg
      }
    },
    locale() {
      return this.$store.state.app.locale || 'zh';
    },
    companyTree() {
      return this.$store.state.sim.companyTree
    },
    //工序
    EquipmenList() {
      return this.$store.state.sim.EquipmenList
    },
    EquipmentTeamTree() {
      return this.$store.state.sim.EquipmentTeamTree
    },
    //产线树
    EquipmentProductLineTree() {
      return this.$store.getters.EquipmentProductLineTree
    },
    StaffList() {
      return this.$store.state.sim.StaffList
    },
    DepartmentList() {
      return this.$store.state.sim.DepartmentList
    },
    // teamList() {
    //   return this.$store.state.sim.TeamList
    // },
    ClassfyCodeList() {
      return this.$store.state.sim.ClassfyCodeList.filter(item => { return item.ItemName != '无事故天数' })
    }
  },
  created() {
    console.log(this.$store.state.sim.LineList, 'this.$store.state.simthis.$store.state.simthis.$store.state.simthis.$store.state.simthis.$store.state.simthis.$store.state.simthis.$store.state.simthis.$store.state.simthis.$store.state.sim');

    this.$store.dispatch('getClassfyCodeList');
    console.log(this.editItemObj, 'this.editItemObjthis.editItemObjthis.editItemObjthis.editItemObj');

    // this.$store.dispatch('getEquipmentTeamTree', "Team");
    if (this.editItemObj && this.editItemObj.ID) {
      this.echo()
      // this.form.ResponsibleDepartmentId = this.editItemObj.ResponsibleDepartment
      this.form.PresentProdProcessId = this.editItemObj.PresentProdProcessId
      this.form.TeamId = this.curTeamTreeObj?.TeamCode,//发现班组ID
        this.form.ResponsiblePlantId = this.curTeamTreeObj?.FactoryCode
      this.form.PresentPlantId = this.curTeamTreeObj?.FactoryCode
      this.getTeamList({ modelid: this.form.PresentProdProcessId, "key": "" })
      // let resp = await getTeam({ modelid: this.curTeamTreeObj?.ProductionLineCode, "key": "" })
      // this.teamList = resp.response
    } else {
      // this.form.PresentProdProcessId = this.getUserinfolist[0].Departmentid;
      this.form.PresentProdProcessId = this.DefaultDepartmentPermission;
      this.getTeamList({ modelid: this.form.PresentProdProcessId, "key": "" })
      // let resp = await getTeam({ modelid: this.form.PresentProdProcessId, "key": "" })
      // this.teamList = resp.response
      this.form.TeamId = this.curTeamTreeObj.TeamCode;
    }
    if (this.form.ResponsibleDepartmentId) {
      this.handleChangeDepart(this.form.ResponsibleDepartmentId, 'init')
    }

    this.EquipmentProductLineTree1 = []
    console.log(this.EquipmentProductLineTree, 'this.EquipmentProductLineTreethis.EquipmentProductLineTreethis.EquipmentProductLineTree');

    this.EquipmentProductLineTree.map(item => {
      item.children.map(child => {
        if (this.DepartmentPermission.split(',').includes(child.id)) {
          this.EquipmentProductLineTree1.push(child);
        }
      });
    });
  },
  methods: {
    async handleChangeDepart(val, type) {
      if (!val) return false
      if (!type) this.form.ResponsibleUserId = ''
      let departId = val
      let resp = await getStaffByDepartId({ Departmentid: departId })
      if (resp && resp.response) {
        this.staffListByDepart = resp.response
      } else {
        this.staffListByDepart = []
      }
    },
    async getTeamList(val) {
      this.form.ResponsiblePlantId = val.parentId
      this.form.PresentPlantId = val.parentId
      // let { id } = val
      let resp = await getTeam({ modelid: val.modelid, "key": "" })
      this.teamList = resp.response
    },
    async changeTeam(val) {

    },
    // 编辑回显
    echo() {
      if (this.editItemObj && this.editItemObj.ID) {
        this.form.TeamId = this.editItemObj.TeamId//发现班组ID
        for (const key in this.form) {
          if (this.editItemObj[key]) {
            this.form[key] = this.editItemObj[key];
          }
        }
        if (this.form.PresentProdProcessId) {
          this.getTeamList({ modelid: this.form.PresentProdProcessId, "key": "" })
        }
      }
    },
    cancel() {
      this.$emit('closePopup');
    },
    async submitForm() {
      const h = this.$createElement;
      if (this.form.TeamId == '' || this.form.TeamId == null || this.form.TeamId == undefined) {
        this.$notify({
          title: '提示',
          message: h('i', { style: 'color: #000000' }, '发现班组不能为空')
        });
        return
      }
      if (this.form.ClassfyCode == '' || this.form.ClassfyCode == null || this.form.ClassfyCode == undefined) {
        this.$notify({
          title: '提示',
          message: h('i', { style: 'color: #000000' }, '事故类型不能为空')
        });
        return
      }
      if (this.form.PlanFinishDate == '' || this.form.PlanFinishDate == null || this.form.PlanFinishDate == undefined) {
        this.$notify({
          title: '提示',
          message: h('i', { style: 'color: #000000' }, '计划完成日期不能为空')
        });
        return
      }
      // let params = 
      let res = await AddSafeAccident(this.form)
      let msg = res.msg
      if (res.success) {
        // this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
        this.$notify({
          title: '提示',
          message: h('i', { style: 'color: #000000' }, msg)
        });
        this.$emit('addDown');
      }
    },
    handleChangeSelectTree(val) {

    },
    handleClose2() {
      this.$emit('closePopup1')
    },
  }
}
</script>
<style scoped>
/deep/ .el-dialog {
    background: var(--background) no-repeat 0 0;
    background-size: 100% 100% !important;
    overflow: hidden;
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}
/deep/ .el-dialog__title {
    color: #fff !important;
}
/deep/ .theme--light.v-text-field--outlined:not(.v-input--is-focused):not(.v-input--has-state) > .v-input__control > .v-input__slot fieldset {
    color: #fff !important;
}
/deep/ .v-text-field--outlined.v-input--dense .v-label--active {
    transform: translateY(-16px) scale(0.75);
    color: #fff !important;
}
/deep/ .theme--light.v-input input,
.theme--light.v-input textarea {
    color: #fff !important;
}
/deep/ .theme--light.v-input input,
.theme--light.v-input textarea {
    color: #fff !important;
}
/deep/ .theme--light.v-label {
    color: #fff !important;
}
/deep/ .theme--light.v-icon {
    color: #fff !important;
}
/deep/ .theme--light.v-select .v-select__selections {
    color: #fff !important;
}
/deep/ .theme--light.v-input input,
.theme--light.v-input textarea {
    color: #fff !important;
}
/deep/ .theme--light.v-input textarea {
    color: #fff !important;
}
/deep/ .theme--light.v-select .v-select__selection--disabled {
    color: #fff !important;
}
</style>

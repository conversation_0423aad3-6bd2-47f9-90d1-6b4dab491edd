import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_MATERIAL'
const baseURL2 = 'baseURL_Inventory'

//Source
export function getSource() {
    const api = '/api/Equipment/GetListByLevel?key=Storage'
    return getRequestResources(baseURL, api, 'post');
}
//Sourcebin
export function getSourcebin() {
    const api = '/api/EquipmentRequirement/GetList'
    return getRequestResources(baseURL, api, 'post');
}
export function GetPrinit(data) {
    const api = '/api/MaterialInventory/GetSelectPrinit'
    return getRequestResources(baseURL2, api, 'post', data, true);
}
////库存打印机
export function GetPrinit2(data) {
    const api = '/api/MaterialInventory/GetSelectPrinit_Bag'
    return getRequestResources(baseURL2, api, 'post', data, true);
}
//原料加工厂备料标签模板
export function GetPrinit3(data) {
    const api = '/api/MaterialInventory/GetSelectPrinit_Process'
    return getRequestResources(baseURL2, api, 'post', data, true);
}
//原料加工厂备料标签模板
export function GetPrinitByEquipmentID(data) {
    const api = '/api/MaterialInventory/GetSelectPrinit_ProcessFour'
  //  return getRequestResources(baseURL2, api, 'post', data, true);
    return getRequestResources(baseURL2, api, 'post', data);
}
////拼锅大标签 
export function GetPrinit4(data) {
    const api = '/api/MaterialInventory/GetSelectPrinit_Pallet'
    return getRequestResources(baseURL2, api, 'post', data, true);
}

//转移打印机
export function GetPrinit5(data) {
    const api = '/api/MaterialInventory/GetSelectPrinit_Move'
    return getRequestResources(baseURL2, api, 'post', data, true);
}
////原料加工厂鼓油
export function GetPrinit6(data) {
    const api = '/api/MaterialInventory/SelectPrinit_KC'
    return getRequestResources(baseURL2, api, 'post', data, true);
}
////拼锅
export function GetPrinit7(data) {
    const api = '/api/MaterialInventory/SelectPrinit_PG'
    return getRequestResources(baseURL2, api, 'post', data, true);
}
////称量备料
export function GetPrinit8(data) {
    const api = '/api/MaterialInventory/GSelectPrinit_CLBL'
    return getRequestResources(baseURL2, api, 'post', data, true);
}
////原料加工厂鼓油
export function GetPrinit10(data) {
    const api = '/api/MaterialInventory/GetSelectPrinit_CY'
    return getRequestResources(baseURL2, api, 'post', data, true);
}
///四厂
export function GetPrinit11(data) {
    const api = '/api/MaterialInventory/GetSelectPrinit_SC'
    return getRequestResources(baseURL2, api, 'post', data, true);
}
//打印小标签
export function PrintMinLabel(data) {
    const api = '/api/MaterialInventory/PrintMinLabel';
    return getRequestResources(baseURL2, api, 'post', data, true);
}
//打印可用库存
export function PrintPreparaLabel(data) {
    const api = '/api/MaterialInventory/PrintPreparaLabel';
    return getRequestResources(baseURL2, api, 'post', data);
}
export function PrintPreparaLabelKY(data) {
    const api = '/api/MaterialInventory/PrintPreparaLabelKY';
    return getRequestResources(baseURL2, api, 'post', data);
}

// //Source
// export function getSource() {
//     const api = '/api/Equipment/GetListByLevel?key=Storage'
//     return getRequestResources(baseURL, api, 'get');
// }
// //Source
// export function getSource() {
//     const api = '/api/Equipment/GetListByLevel?key=Storage'
//     return getRequestResources(baseURL, api, 'get');
// }
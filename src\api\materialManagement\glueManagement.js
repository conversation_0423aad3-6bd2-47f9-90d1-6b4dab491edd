import { getRequestResources } from '@/api/fetch';
const dmfURL = 'baseURL_DFM'
const baseURL = 'baseURL_MATERIAL'
// 胶水管理


// 获取物料列表
export function GetMaterialList(data) {
    const api =  '/api/Material/GetList'
    return getRequestResources(dmfURL, api, 'post', data);
}
//获取楼层tree
export function GetFloorTree(data) {
    const api =  '/api/FactorySite/GetTree'
    return getRequestResources(dmfURL, api, 'post', data);
}

//获取某天胶水需求列表
export function GlueRecordgetList(data) {
    const api =  '/materail/GlueRecord/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//分页获取胶棒列表
export function getGlueRecordPageList(data) {
    const api =  '/materail/GlueRecord/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑胶棒
export function GenerateGlueList(data) {
    const api =  '/materail/GlueRecord/GenerateGlueList'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除胶棒
export function DeleteGlueRecord(data) {
    const api =  '/materail/GlueRecord/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//注胶
export function GlueRecordSaveForm(data) {
    const api =  '/materail/GlueRecord/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}

//根据批次获取物料信息
export function GetSapGlueBy(data) {
    const api =  '/materail/SapGlue/GetSapGlueBy'
    return getRequestResources(baseURL, api, 'post', data, true);
}
//获取物料号
export function GlueRecordGetMaterialSN(data) {
    const api =  '/materail/GlueRecord/GetMaterialSN'
    return getRequestResources(baseURL, api, 'post', data, true);
}
//获取批次号中保质期限
export function GlueRecordGetShelfLifeDate(data) {
    const api =  '/materail/GlueRecord/GetShelfLifeDate'
    return getRequestResources(baseURL, api, 'post', data, true);
}
//分页获取胶水列表
export function getSapGluePageList(data) {
    const api =  '/materail/SapGlue/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取胶水列表
export function SapGlueGetList(data) {
    const api =  '/materail/SapGlue/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增胶水
export function SapGlueSaveForm(data) {
    const api =  '/materail/SapGlue/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除胶棒
export function DeleteSapGlue(data) {
    const api =  '/materail/SapGlue/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

// 导出表格
export function GlueRecordByList(data) {
    const api =  '/materail/GlueRecord/ByList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 新增当天胶水需求
export function SaveDemandGlueFromHis(data) {
    const api =  '/materail/GlueRecord/SaveDemandGlueFromHis'
    return getRequestResources(baseURL, api, 'post', data);
}
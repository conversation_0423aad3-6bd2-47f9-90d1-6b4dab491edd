<template>
    <div>
        <v-card>
            <v-card-title class="headline primary lighten-2" primary-title>
                事故列表
            </v-card-title>
            <v-card-text style="padding: 0 10px;">
                <v-row class="mt-5">
                    <v-col :cols="6" class="pt-0 pb-0">
                        <vxe-button size="small" content="新增" @click="add()"></vxe-button>
                        <vxe-button size="small" content="修改" @click="edit()"></vxe-button>
                        <vxe-button size="small" content="升级" @click="update()"></vxe-button>
                        <!-- <vxe-button size="small" content="回复" @click="reply()"></vxe-button> -->
                        <vxe-button size="small" content="删除" @click="del()"></vxe-button>
                    </v-col>
                    <v-col :cols="3" class="pt-0 pb-0">
                        <v-menu :close-on-content-click="true" :nudge-right="40" transition="scale-transition" offset-y
                            max-width="290px" min-width="290px">
                            <template #activator="{ on, attrs }">
                                <v-text-field v-model="searchForm.start" :clearable="true" outlined dense label="开始时间"
                                    readonly v-bind="attrs" v-on="on">
                                </v-text-field>
                            </template>
                            <v-date-picker v-model="searchForm.start" placeholder="开始时间" :locale="locale" no-title
                                @input="getList">
                            </v-date-picker>
                        </v-menu>
                    </v-col>
                    <v-col :cols="3" class="pt-0 pb-0">
                        <v-menu :close-on-content-click="true" :nudge-right="40" transition="scale-transition" offset-y
                            max-width="290px" min-width="290px">
                            <template #activator="{ on, attrs }">
                                <v-text-field v-model="searchForm.end" :clearable="true" outlined dense label="结束时间"
                                    readonly v-bind="attrs" v-on="on">
                                </v-text-field>
                            </template>
                            <v-date-picker v-model="searchForm.end" placeholder="结束时间" :locale="locale" no-title
                                @input="getList">

                            </v-date-picker>
                        </v-menu>
                    </v-col>
                </v-row>
                <!-- <vxe-toolbar ref="xToolbar">
                    <template #buttons>
                        <vxe-button size="mini" content="新增" @click="add()"></vxe-button>
                        <vxe-button size="mini" content="修改" @click="edit()"></vxe-button>
                        <vxe-button size="mini" content="删除" @click="del()"></vxe-button>
                    </template>
                </vxe-toolbar> -->
                <div class="table-box">
                    <vxe-table :data="tableData" :show-footer="false" :loading="false" ref="vxeTable"
                        class="mytable-scrollbar" :row-config="{ isHover: true }" :column-config="{ resizable: true }"
                        height="400" border size="mini">
                        <vxe-column type="checkbox" width="40"></vxe-column>
                        <vxe-column show-overflow="title" field="PresentDate" title="发起日期">
                            <!-- <template #default="{ row }">
                                {{ getEquipmentTeamNameByID(row.TeamId) }}
                            </template> -->
                        </vxe-column>
                        <vxe-column show-overflow="title" title="班组">
                            <template #default="{ row }">
                                {{ getEquipmentTeamNameByID(row.TeamId) }}
                            </template>
                        </vxe-column>
                        <vxe-column show-overflow="title" title="提出人">
                            <template #default="{ row }">
                                {{ getStaffName(row['PresentUserId']) }}
                            </template>
                        </vxe-column>
                        <vxe-column show-overflow="title" title="责任部门">
                            <template #default="{ row }">
                                {{ getDepartmentName(row['ResponsibleDepartmentId']) }}
                            </template>
                        </vxe-column>
                        <vxe-column show-overflow="title" field="ChargeUserId" title="负责人">
                            <template #default="{ row }">
                                {{ getStaffName(row.ResponsibleUserId) }}
                            </template>
                        </vxe-column>
                        <vxe-column show-overflow="title" field="PlanFinishDate" title="计划完成日期"></vxe-column>
                        <vxe-column show-overflow="title" field="ActualFinishDate" title="实际完成日期"></vxe-column>

                        <vxe-column show-overflow="title" field="ClassfyCode" title="事故类型">
                            <template #default="{ row }">
                                {{ getClassfyCodeName(row.ClassfyCode) }}
                            </template>
                        </vxe-column>
                        <vxe-column show-overflow="title" field="AccidentDesc" title="事故描述"></vxe-column>
                        <vxe-column show-overflow="title" field="PresentProdProcessId" title="事故所在产线">
                            <template #default="{ row }">
                                {{ getLineText(row.PresentProdProcessId) }}
                            </template>
                        </vxe-column>
                    </vxe-table>
                </div>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions style="justify-content: flex-end;">
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <!-- 问题列表弹窗 -->
        <v-dialog v-model="addDialogVisable" scrollable persistent width="55%">
            <AddDialog v-if="addDialogVisable" :editItemObj="editItemObj" :searchFormObj="searchFormObj"
                :curTeamTreeObj="curTeamTreeObj" @addDown="addDown" @closePopup="closeDialog">
            </AddDialog>
        </v-dialog>
    </div>
</template>

<script>
import dayjs from 'dayjs'
import AddDialog from "./addDialog.vue"
import { GetSafePageList, DeleteSafeAccident, UpdateSafeAccident } from '@/views/simManagement/sim1/service.js';
import { EquipmentGetEquipmentTeamTree } from '@/api/common.js';

export default {
    name: "CalendarDialog",
    components: {
        AddDialog
    },
    props: {
        curTeamTreeObj: {
            type: Object,
            default: () => { }
        },
        searchFormObj: {
            type: Object,
            default: () => { }
        },
        curDay: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            departNameData: {},
            nameData: {},
            searchForm: {
                start: '',
                end: ''
            },
            tableData: [],
            addDialogVisable: false,
            editItemObj: {}
        }
    },
    computed: {
        com() {
            return this.$store.getters.flatCompany
        },
        EquipmentProductLineTree() {
            return this.$store.getters.EquipmentProductLineTree
        },
        //员工列表
        staffList() {
            return this.$store.state.sim.StaffList
        },
        DepartmentList() {
            return this.$store.state.sim.DepartmentList
        },
        dateParam() {
            let before = dayjs(this.searchFormObj.date).format('YYYY-MM')
            let date = before + '-' + this.curDay.day
            return date
        },
        ClassfyCodeList() {
            return this.$store.state.sim.ClassfyCodeList
        },
        locale() {
            return this.$store.state.app.locale || 'zh';
        }
    },
    watch: {
        dateParam: {
            handler(nv, ov) {
                this.searchForm.start = nv
                this.searchForm.end = nv
                this.getList()
            },
            immediate: true
        }
        // 'searchFormObj': {
        //     handler(nv, ov) {
        //         this.getList()
        //     },
        //     deep: true,
        //     immediate: true
        // }
    },
    methods: {
        recursion(list, id) {
            for (var i = 0; i < list.length; i++) {
                if (id == list[i].id) {
                    this.nameData[id] = list[i]
                    break;
                } else if (list[i].children && list[i].children.length) {
                    this.recursion(list[i].children, id)
                }
            }
        },
        getLineText(id) {
            this.recursion(this.EquipmentProductLineTree, id)
            return this.nameData[id] ? this.nameData[id].name : ''
        },
        async getList() {
            let params = {
                // "TeamId": this.searchFormObj.PresentDepartmentId,
                "QueryStartDate": this.searchForm.start,
                "QueryEndDate": this.searchForm.end,
                "pageIndex": 1,
                "pageSize": 10,
            }
            if (this.$route.name === 'SIM1') {
                params.TeamId = this.searchFormObj.PresentDepartmentId
            } else if (this.$route.name === 'SIM2') {
                params.DepartmentId = this.searchFormObj.PresentDepartmentId
            } else {
                params.PlantId = this.searchFormObj.PresentDepartmentId
            }
            let { response } = await GetSafePageList(params)
            // let { response } = await GetMonthStatistics(params)
            response.data.forEach(item => {
                item.PlanFinishDate = dayjs(item.PlanFinishDate).format('YYYY-MM-DD');
                item.PresentDate = dayjs(item.PresentDate).format('YYYY-MM-DD');
                item.CreateDate = dayjs(item.CreateDate).format('YYYY-MM-DD');
                item.ModifyDate = dayjs(item.ModifyDate).format('YYYY-MM-DD');
                if (item.ActualFinishDate) {
                    item.ActualFinishDate = dayjs(item.ActualFinishDate).format('YYYY-MM-DD');
                }
            });
            this.tableData = response.data
        },
        submitForm() {
            this.$emit('closePopup');
        },
        //根据ID获取组织建模单位 名字
        getCompanyNameByID(id) {
            let target = {}
            for (let i = 0; i < this.$store.getters.flatCompany.length; i++) {
                const element = this.$store.getters.flatCompany[i];
                if (element.id === id) {
                    target = element
                    break
                }
            }
            return target.name
        },
        //根据员工Code匹配名字
        getStaffName(staffId) {
            let target = this.staffList.find(item => item.Code == staffId)
            if (target) {
                return target.Name
            }
            return staffId
        },
        //根据事故类型id匹配名字
        getClassfyCodeName(id) {
            let target = this.ClassfyCodeList.find(item => item.ItemValue == id)
            if (target) {
                return target.ItemName
            }
            return id
        },
        recursionDepart(list, id) {
            for (var i = 0; i < list.length; i++) {
                if (id == list[i].id) {
                    this.departNameData[id] = list[i]
                    break;
                } else if (list[i].children && list[i].children.length) {
                    this.recursionDepart(list[i].children, id)
                }
            }
        },
        //根据部门 id匹配名字
        getDepartmentName(id) {
            this.recursionDepart(this.EquipmentProductLineTree, id)
            return this.departNameData[id] ? this.departNameData[id].name : id
        },
        //根据ID获取物理建模单位 名字
        getEquipmentTeamNameByID(id) {
            let target = {}
            for (let i = 0; i < this.$store.getters.flatEquipmentTeam.length; i++) {
                const element = this.$store.getters.flatEquipmentTeam[i];
                if (element.id === id) {
                    target = element
                    break
                }
            }
            return target.name
        },
        add() {
            this.addDialogVisable = true
        },
        edit() {
            let selectRecords = this.$refs.vxeTable.getCheckboxRecords()
            if (selectRecords.length !== 1) {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
                return false;
            }
            this.editItemObj = selectRecords[0]
            this.addDialogVisable = true
        },
        update() {
            let selectRecords = this.$refs.vxeTable.getCheckboxRecords()
            if (selectRecords.length == 0) {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
                return false;
            }
            this.$confirms({
                message: this.$t('请确认是否升级'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            }).then(async () => {
                let params = {
                    id: selectRecords[0].ID
                }
                let res = await UpdateSafeAccident(params)
                this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
                this.getList()
            })
        },
        //删除
        del() {
            let selectRecords = this.$refs.vxeTable.getCheckboxRecords()
            if (selectRecords.length == 0) {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
                return false;
            }
            this.$confirms({
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            }).then(async () => {
                let params = selectRecords.map(item => item.ID)
                let res = await DeleteSafeAccident(params)
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
                this.getList()
            })
        },
        addDown() {
            this.addDialogVisable = false
            this.getList()
        },
        //关闭新增弹窗
        closeDialog() {
            this.addDialogVisable = false
            this.editItemObj = {}
        }
    },
    created() {
        this.getList()
    },
    mounted() {
    },
    beforeDestroy() {
        this.nameData = {}
        this.departNameData = {}
    },
}
</script>

<style lang="less" scoped>
.table-box {
    height: calc(100% - 40px);
}
</style>
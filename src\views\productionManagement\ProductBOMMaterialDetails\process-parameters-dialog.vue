<template>
  <el-dialog 
    :title="dialogTitle" 
    :visible.sync="dialogVisible" 
    width="600px"
    :close-on-click-modal="false" 
    :modal-append-to-body="true" 
    append-to-body 
    :close-on-press-escape="false"
    @close="handleClose"
    class="parameter-dialog"
  >
    <el-form 
      :model="form" 
      :rules="rules" 
      ref="form" 
      label-width="120px" 
      size="small"
      class="parameter-form"
    >

      <el-form-item label="设备选择" prop="EquipmentId">
        <el-select 
          v-model="form.EquipmentId" 
          placeholder="请选择设备" 
          class="full-width"
          filterable
          clearable
        >
          <el-option
            v-for="item in equipmentList"
            :key="item.ID"
            :label="item.EquipmentCode"
            :value="item.ID"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="参数名称" prop="ParameterName">
        <el-input v-model="form.ParameterName" placeholder="请输入参数名称" class="full-width"></el-input>
      </el-form-item>
      <el-form-item label="参数值" prop="ParameterValue">
        <el-input v-model="form.ParameterValue" placeholder="请输入参数值" class="full-width"></el-input>
      </el-form-item>
            <el-form-item label="数据块" prop="DataBlock">
        <el-input v-model="form.DataBlock" placeholder="请输入数据块,样例RTD0450" class="full-width"></el-input>
      </el-form-item>
            <el-form-item label="数据块点位" prop="DataBlockItem">
        <el-input v-model="form.DataBlockItem" placeholder="请输入数据块点位,样例RD01[1,1]" class="full-width"></el-input>
      </el-form-item>
      <el-form-item label="描述" prop="Remark">
        <el-input 
          type="textarea" 
          v-model="form.Remark" 
          placeholder="请输入描述" 
          :rows="3"
          class="full-width"
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button 
        size="small"
        @click="dialogVisible = false"
      >{{ $t('GLOBAL._QX') }}</el-button>
      <el-button 
        size="small"
        type="primary" 
        :loading="loading"
        @click="submitForm"
      >{{ $t('GLOBAL._QD') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { saveProcessParameter, getUnitList } from '@/api/productionManagement/productBOMMaterialDetails'

export default {
  name: 'ProcessParametersDialog',
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '',
      isEdit: false,
      loading: false,
      equipmentList: [],
      form: {
        ID: '',
        ParameterCode: '',
        ParameterName: '',
        ParameterValue: '',
        Remark: '',
        SapSegmentMaterialId: '',
        EquipmentId: ''
      },
      rules: {
        EquipmentId: [
          { required: true, message: '请选择设备', trigger: 'change' }
        ],
        ParameterValue: [
          { required: true, message: '请输入下发值', trigger: 'blur' }
        ],
        ParameterName: [
          { required: true, message: '请输入参数名称', trigger: 'blur' }
        ],
        DataBlock: [
          { required: true, message: '请输入数据块', trigger: 'blur' }
        ],
        DataBlockItem: [
          { required: true, message: '请输入数据块点位', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    show(row) {
        // 获取设备列表
      this.getUnitList();
      this.resetForm()
      if (row && row.ID) {
        this.isEdit = true
        this.dialogTitle = '编辑工艺关键参数'
        this.form = { ...row }
      } else {
        this.isEdit = false
        this.dialogTitle = '添加工艺关键参数'
        // 如果是新增，需要设置sapSegmentMaterialId
        if (row && row.sapSegmentMaterialId) {
          this.form.SapSegmentMaterialId = row.sapSegmentMaterialId
        }
      }
    
      this.dialogVisible = true
    },
    handleClose() {
      this.resetForm()
    },
    resetForm() {
      this.form = {
        ID: '',
        ParameterCode: '',
        ParameterName: '',
        ParameterValue: '',
        Remark: '',
        SapSegmentMaterialId: '',
        EquipmentId: ''
      }
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    },
    async getUnitList() {
      try {
        //const { data } = await getUnitList();
        //this.equipmentList = data.response;

         getUnitList().then(res => {
        this.equipmentList.push(...res.response)
        // this.dialogForm = Object.assign({},data)
      })

      } catch (error) {
        console.error('获取设备列表失败:', error);
      }
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          saveProcessParameter(this.form).then(res => {
            if (res.success) {
              this.$message.success(this.isEdit ? '编辑成功' : '添加成功')
              this.dialogVisible = false
              this.$emit('saveForm')
            } else {
              this.$message.error(res.message || (this.isEdit ? '编辑失败' : '添加失败'))
            }
            this.loading = false
          }).catch(error => {
            console.error('保存工艺关键参数失败:', error)
            this.$message.error(this.isEdit ? '编辑失败，请稍后重试' : '添加失败，请稍后重试')
            this.loading = false
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.parameter-dialog {
  ::v-deep .el-dialog {
    border-radius: 8px;
    overflow: hidden;
    
    .el-dialog__header {
      background-color: #f7f8fa;
      padding: 15px 20px;
      border-bottom: 1px solid #e8e8e8;
      
      .el-dialog__title {
        font-size: 18px;
        font-weight: 500;
        color: #303133;
      }
    }
    
    .el-dialog__body {
      padding: 20px 25px;
    }
    
    .el-dialog__footer {
      border-top: 1px solid #e8e8e8;
      padding: 12px 20px;
    }
  }
  
  .parameter-form {
    .el-form-item {
      margin-bottom: 18px;
      
      ::v-deep .el-form-item__label {
        font-weight: 500;
        color: #606266;
        padding-right: 12px;
      }
      
      ::v-deep .el-form-item__content {
        line-height: 36px;
      }
    }
    
    .full-width {
      width: 100%;
    }
    
    ::v-deep .el-input__inner {
      height: 36px;
      line-height: 36px;
      border-radius: 4px;
      
      &:hover {
        border-color: #c0c4cc;
      }
      
      &:focus {
        border-color: #409EFF;
      }
    }
    
    ::v-deep .el-textarea__inner {
      border-radius: 4px;
      padding: 8px 12px;
      
      &:hover {
        border-color: #c0c4cc;
      }
      
      &:focus {
        border-color: #409EFF;
      }
    }
  }
}

::v-deep .el-button {
  border-radius: 4px;
  padding: 8px 16px;
  
  &--primary {
    background-color: #409EFF;
    border-color: #409EFF;
    
    &:hover {
      background-color: #66b1ff;
      border-color: #66b1ff;
    }
    
    &.is-loading {
      background-color: #409EFF;
      border-color: #409EFF;
      opacity: 0.8;
    }
  }
}

// 响应式布局
@media screen and (max-width: 768px) {
  .parameter-dialog {
    ::v-deep .el-dialog {
      width: 95% !important;
      margin-top: 10vh !important;
    }
    
    .parameter-form {
      ::v-deep .el-form-item__label {
        float: none;
        display: block;
        text-align: left;
        padding: 0 0 8px;
      }
      
      ::v-deep .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
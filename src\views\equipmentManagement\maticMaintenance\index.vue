<template>
    <div class="disassemble-defective-products">
        <v-row class="mt-1 pr-4" v-if="showForm">
            <v-col :cols="12" :lg="3" class="pt-0 pb-0">
                <v-text-field :label="$t('DFM_ZNYWPZ.Factory')" dense outlined v-model="paramsObj.factory"></v-text-field>
            </v-col>
            <v-col :cols="12" :lg="3" class="pt-0 pb-0">
                <v-select :items="seriesList" item-text="ItemName" item-value="ItemValue" clearable dense
                    v-model="paramsObj.productseries" outlined required :label="$t('DFM_ZNYWPZ.ProductSeries')" />
            </v-col>
            <v-col :cols="12" :lg="3" class="pt-0 pb-0">
                <v-select :items="lineList" @click:clear="handleChangeLine" @change="handleChangeLine"
                    item-text="EquipmentName" item-value="EquipmentCode" dense clearable v-model="paramsObj.linecode"
                    outlined :label="$t('DFM_ZNYWPZ.Linename')" />
            </v-col>
            <v-col :cols="12" :lg="3" class="pt-0 pb-0">
                <v-select :items="segmentList" item-text="EquipmentName" item-value="EquipmentCode" dense clearable
                    v-model="paramsObj.segmentcode" outlined :label="$t('DFM_ZNYWPZ.SegmentName')" />
            </v-col>
            <v-col :cols="12" :lg="3" class="pt-0 pb-0">
                <v-select :items="monitorTypeList" item-text="ItemName" item-value="ItemValue" clearable dense
                    v-model="paramsObj.typecode" outlined :label="$t('DFM_ZNYWPZ.TypeName')" />
            </v-col>
            <v-col :cols="12" :lg="3" class="pt-0 pb-0">
                <v-select :items="statusList" item-text="ItemName" item-value="ItemValue" clearable dense
                    v-model="paramsObj.state" outlined :label="$t('DFM_ZNYWPZ.State')" />
            </v-col>
            <v-col :cols="12" :lg="3" class="pt-0 pb-0">
                <v-btn color="primary" @click="getdata()">{{ $t('GLOBAL._CX') }}</v-btn>
                <v-btn class="ml-4" @click="resetData()">{{ $t('GLOBAL._CZ') }}</v-btn>
            </v-col>
        </v-row>
        <!-- <SearchForm class="mt-1" :show-from="showForm" :searchinput="searchInputs" @searchForm="searchForm" /> -->
        <div class="form-btn-list">
            <v-btn icon class="float-left mx-4" @click="showForm = !showForm">
                <v-icon>{{ 'mdi-table-search' }}</v-icon>
                {{ $t('GLOBAL._SSL') }}
            </v-btn>
            <v-btn icon color="primary" @click="getdata">
                <v-icon>mdi-cached</v-icon>
            </v-btn>
            <v-btn color="primary" v-has="'ZNYWPZ_MBXZ'" @click="templateDownload()">{{ $t('GLOBAL._MBXZ') }}</v-btn>
            <v-btn color="primary" v-has="'ZNYWPZ_DR'" @click="handleImport()">{{ $t('GLOBAL._DR') }}</v-btn>
            <v-btn color="primary" v-has="'ZNYWPZ_DC'" @click="dataExport()">{{ $t('GLOBAL._EXPORT') }}</v-btn>
            <v-btn color="primary" v-has="'ZNYWPZ_ADD'" @click="add()">{{ $t('GLOBAL._XZ') }}</v-btn>
            <v-btn color="primary" v-has="'ZNYWPZ_ALLREMOVE'" @click="batchDel()">{{ $t('GLOBAL._PLSC') }}</v-btn>
        </div>
        <Tables ref="table" table-name="DFM_ZNYWPZ"
            :tableHeight="showForm ? ' calc(100vh - 280px)' : ' calc(100vh - 180px)'" :btnList="btnList"
            :dictionaryList="dictionaryList" :page-options="pageOptions" :loading="loading" :headers="headers"
            :desserts="tableList" @selectePages="selectePages" @tableClick="tableClick">
            <template #DeviceType="{ item }">
                {{ item.DeviceType == 1 ? '工单维度' : item.DeviceType == 2 ? '公共设备' : '' }}
            </template>
        </Tables>
        <!-- 添加  编辑弹窗 -->
        <v-dialog scrollable persistent v-model="isShowEditPopup" width="55%">
            <EditPopup :statusList="statusList" :editItemObj="editItemObj" :monitorTypeList="monitorTypeList"
                :seriesList="seriesList" @getdata="getdata" v-if="isShowEditPopup" @closePopup="closePopup" />
        </v-dialog>

        <div class="loading-box" v-if="importLoading">
            <a-spin tip="导入中..." :spinning="importLoading"></a-spin>
        </div>
    </div>
</template>

<script>
import { maticMaintenanceColumns } from '@/columns/factoryPlant/tableHeaders';
import EditPopup from './components/editPopup.vue';
import Util from '@/util';
import { EquipmentGetPageList } from '@/api/common.js';
import { getMaintenanceList, delMaintenance, doImport } from './service';

import { configUrl } from '@/config';
const templateUrl = configUrl[process.env.VUE_APP_SERVE]['baseURL_TPM'] + '/tpm/MaticMaintenanceConfig/ImportExcelTemplates';
const dataExportUrl = configUrl[process.env.VUE_APP_SERVE]['baseURL_TPM'] + '/tpm/MaticMaintenanceConfig/ByList';
export default {
    components: {
        EditPopup
    },
    data() {
        return {
            importLoading: false,
            statusList: [],
            monitorTypeList: [],
            segmentList: [],
            lineList: [],
            seriesList: [], // 产品系列 列表
            isShowEditPopup: false,
            editItemObj: {},
            loading: false,
            showForm: false,
            paramsObj: {
                factory: '',
                productseries: '',
                linecode: '',
                segmentcode: '',
                typecode: '',
                state: ''
            },
            tableList: [],
            headers: maticMaintenanceColumns,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            }
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'ZNYWPZ_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'ZNYWPZ_DELETE'
                }
            ];
        },
        dictionaryList() {
            return [
                { arr: this.seriesList, key: 'ProductSeries', val: 'ItemValue', text: 'ItemName' },
                { arr: this.monitorTypeList, key: 'MonitorType', val: 'ItemValue', text: 'ItemName' },
                { arr: this.statusList, key: 'State', val: 'ItemValue', text: 'ItemName' }
            ];
        },
        searchInputs() {
            let list = [
                {
                    value: '',
                    icon: 'mdi-account-check',
                    label: '厂区',
                    placeholder: '',
                    key: 'factory'
                },
                {
                    value: '',
                    icon: 'mdi-account-check',
                    label: '产品系列',
                    placeholder: '',
                    type: 'select',
                    selectData: this.$changeSelectItems(this.seriesList, 'ItemValue', 'ItemName'),
                    key: 'productseries'
                },
                {
                    value: '',
                    icon: 'mdi-account-check',
                    label: '产线',
                    placeholder: '',
                    type: 'select',
                    selectData: this.$changeSelectItems(this.lineList, 'EquipmentCode', 'EquipmentName'),
                    key: 'linecode'
                },
                {
                    value: '',
                    icon: 'mdi-account-check',
                    label: '工段',
                    placeholder: '',
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.segmentList, 'EquipmentCode', 'EquipmentName'),
                    key: 'segmentcode'
                },
                {
                    value: '',
                    icon: 'mdi-account-check',
                    label: '监控类型',
                    placeholder: '',
                    type: 'select',
                    selectData: this.$changeSelectItems(this.monitorTypeList, 'ItemValue', 'ItemName'),
                    key: 'typecode'
                },
                {
                    value: '',
                    icon: 'mdi-account-check',
                    label: '状态',
                    placeholder: '',
                    type: 'select',
                    selectData: this.$changeSelectItems(this.statusList, 'ItemValue', 'ItemName'),
                    key: 'state'
                }
            ];
            return list;
        }
    },
    created() {
        this.getMonitorType();
        this.getSeries();
        this.getLineData();
        this.getStatus();
        this.getdata();
    },
    methods: {
        handleImport() {
            let _this = this;
            let input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xls,.xlsx';
            input.click();
            input.onchange = async function () {
                let file = input.files[0];
                let formdata = new FormData();
                formdata.append('file', file);

                _this.importLoading = true;
                try {
                    await doImport(formdata);
                    _this.$store.commit('SHOW_SNACKBAR', { text: '导入成功', color: 'success' });
                    _this.GetPageList();
                    _this.importLoading = false;
                } catch {
                    _this.importLoading = false;
                }
            };
        },
        // 数据导出
        dataExport() {
            window.open(dataExportUrl, '_blank');
        },
        // 导入模板下载
        async templateDownload() {
            window.open(templateUrl, '_blank');
        },
        resetData() {
            this.paramsObj = {
                factory: '',
                productseries: '',
                linecode: '',
                segmentcode: '',
                typecode: '',
                state: ''
            };
            this.getdata();
        },
        async handleChangeLine(val) {
            this.segmentList = [];
            this.paramsObj.segmentcode = '';
            let obj = this.lineList.find(item => item.EquipmentCode == val);
            if (!obj) return false;
            const { ID } = obj;
            const res = await EquipmentGetPageList({ DataItemCode: 'EquipmentLevel', ParentId: ID, pageIndex: 1, pageSize: 9999 });
            const { success, response } = res;
            if (success) {
                this.segmentList = response.data;
            }
        },
        async getStatus() {
            this.statusList = await this.$getDataDictionary('maintenanceDeviceStatus');
        },
        async getMonitorType() {
            this.monitorTypeList = await this.$getDataDictionary('ZNYWQD');
        },
        // 获取产线列表
        async getLineData() {
            this.lineList = await Util.GetEquipmenByLevel('Area');
        },
        add() {
            this.editItemObj = {};
            this.isShowEditPopup = true;
        },
        // 批量删除
        batchDel() {
            let selecteds = this.$refs.table.selected;
            if (selecteds.length === 0) {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
                return false;
            }
            let ids = [];
            selecteds.forEach(item => {
                ids.push(item.ID);
            });
            this.delData(ids);
        },
        delData(ids) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let resp = await delMaintenance(ids);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });

                    this.getdata();
                })
                .catch(() => { });
        },
        async getSeries() {
            this.seriesList = await this.$getDataDictionary('ProductSeries');
        },
        closePopup() {
            this.isShowEditPopup = false;
        },
        async getdata() {
            this.loading = true;
            try {
                let resp = await getMaintenanceList({ ...this.paramsObj, pageIndex: this.pageOptions.page, pageSize: this.pageOptions.pageSize });
                this.loading = false;
                this.$refs.table.selected = [];
                this.tableList = resp.response.data;
                this.pageOptions.pageCount = resp.response.pageCount;
                this.pageOptions.total = resp.response.dataCount;
            } catch {
                this.loading = false;
            }
        },
        searchForm(params) {
            this.paramsObj = params;
            this.pageOptions.page = 1;
            this.getdata();
        },
        // 分页操作
        selectePages(data) {
            this.pageOptions.page = data.pageCount;
            this.pageOptions.pageSize = data.pageSize;
            this.getdata();
        },
        tableClick(item, type) {
            switch (type) {
                case 'edit':
                    this.editItemObj = item;
                    this.isShowEditPopup = true;
                    break;
                case 'delete':
                    this.delData([item.ID]);
                    break;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>
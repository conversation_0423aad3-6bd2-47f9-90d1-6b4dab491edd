<template>
    <v-dialog v-model="viewDialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="headline primary lighten-2" primary-title>查看物料</v-card-title>
            <v-card-text>
                <v-row style="margin-top: 20px">
                    <v-col :cols="12" :lg="6">
                        <v-select v-model="form.Factory" :items="items" item-text="state" item-value="abbr" label="所属二级分类" persistent-hint return-object single-line dense outlined></v-select>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <v-text-field v-model="form.RoutingType" label="料号" dense outlined></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="12">
                        <v-textarea label="物料描述" :rows="2" dense outlined v-model="form.RoutingType"></v-textarea>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <v-text-field label="厂别" required dense outlined v-model="form.RoutingType"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <v-text-field label="销售码1" required dense outlined v-model="form.RoutingType"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <v-text-field label="销售码2" required dense outlined v-model="form.RoutingType"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <v-text-field label="销售码3" required dense outlined v-model="form.RoutingType"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <v-text-field label="计划族" required dense outlined v-model="form.RoutingType"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <v-text-field label="材质执行标准" required dense outlined v-model="form.RoutingType"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <v-text-field label="物料分类" required dense outlined v-model="form.RoutingType"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <v-text-field label="器械工单类别" required dense outlined v-model="form.RoutingType"></v-text-field>
                    </v-col>
                </v-row>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="viewDialog = false">确认</v-btn>
                <v-btn color="normal" @click="viewDialog = false">关闭</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
export default {
    data() {
        return {
            viewDialog: false,
            form: {
                Factory: '*********',
                RoutingType: ''
            },
            items: [] //{ state: '所属二级分类', abbr: '' }
        };
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>

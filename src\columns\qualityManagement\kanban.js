// 质量看板安灯日志表头
export const andonColumnsNoAction = [
    {
        title: '一级分类',
        dataIndex: 'mainName',
        key: 'mainName',
        width: 90
    },
    {
        title: '二级分类',
        dataIndex: 'subName',
        key: 'subName',
        width: 100,
        ellipsis: true
    },
    {
        title: '工站',
        dataIndex: 'UnitCode',
        key: 'UnitCode',
        width: 100,
        ellipsis: true
    },
    {
        title: '告警等级',
        dataIndex: 'EventLevel',
        key: 'EventLevel',
        width: 90
    },
    {
        title: '当前状态',
        dataIndex: 'statusText',
        key: 'statusText',
        width: 90
    },
    {
        title: '负责人',
        dataIndex: 'Currentman',
        key: 'Currentman',
        width: 90,
        ellipsis: true
    },
    {
        title: '报警时间',
        dataIndex: 'CreateDate',
        key: 'CreateDate',
        width: 160,
        ellipsis: true
    },
    {
        title: '告警内容',
        dataIndex: 'AlarmContent',
        key: 'AlarmContent',
        ellipsis: true
    }
];
export const andonColumns = [
    {
        title: '一级分类',
        dataIndex: 'mainName',
        key: 'mainName',
        width: 90
    },
    {
        title: '二级分类',
        dataIndex: 'subName',
        key: 'subName',
        ellipsis: true,
        width: 90
    },
    {
        title: '工站',
        dataIndex: 'UnitCode',
        key: 'UnitCode',
        width: 90,
        ellipsis: true
    },
    {
        title: '告警等级',
        dataIndex: 'EventLevel',
        key: 'EventLevel',
        width: 90
    },
    {
        title: '当前状态',
        dataIndex: 'statusText',
        key: 'statusText',
        width: 90
    },
    {
        title: '负责人',
        dataIndex: 'Currentman',
        key: 'Currentman',
        width: 90,
        ellipsis: true
    },
    {
        title: '报警时间',
        dataIndex: 'CreateDate',
        key: 'CreateDate',
        width: 110,
        ellipsis: true
    },
    {
        title: '告警内容',
        dataIndex: 'AlarmContent',
        key: 'AlarmContent',
        ellipsis: true
    },
    {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        width: 80,
        scopedSlots: { customRender: 'operation' },
    }
];
// header = ['设备名称', '设备类型', '工单号', '异常描述', '维修状态', '承修人', '维修时长'];
// 维修日志 表头
export const maintainColumns = [
    {
        title: '设备名称',
        dataIndex: 'DeviceName',
        key: 'DeviceName',
        width: 120,
        ellipsis: true
    },
    // {
    //     title: '设备类型',
    //     dataIndex: 'DeviceCategory',
    //     key: 'DeviceCategory',
    //     width: 90
    // },
    {
        title: '工单号',
        dataIndex: 'WoCode',
        key: 'WoCode',
        width: 160,
        ellipsis: true
    },
    {
        title: '维修状态',
        dataIndex: 'RepairStatus',
        key: 'RepairStatus',
        width: 90
    },
    // {
    //     title: '承修人',
    //     dataIndex: 'RepairUser',
    //     key: 'RepairUser',
    //     width: 90,
    //     ellipsis: true
    // },
    {
        title: '维修时长/h',
        dataIndex: 'RepairHours',
        key: 'RepairHours',
        width: 160,
        ellipsis: true
    },
    {
        title: '异常描述',
        dataIndex: 'RepairProcess',
        key: 'RepairProcess',
        ellipsis: true
    },
]

// header = ['保养项目', '保养状态', '保养时间', '委外', '保养人', '备注'];
// 保养日志表头
export const upKeepColumns = [
    {
        title: '保养项目',
        dataIndex: 'MaintainProject',
        key: 'MaintainProject',
        width: 120,
        ellipsis: true
    },
    {
        title: '保养状态',
        dataIndex: 'MaintainStatus',
        key: 'MaintainStatus',
        width: 100,
        ellipsis: true
    },
    {
        title: '保养时间',
        dataIndex: 'MaintainDate',
        key: 'MaintainDate',
        width: 160,
        ellipsis: true
    },
    {
        title: '委外',
        dataIndex: 'TrustDes',
        key: 'TrustDes',
        width: 120,
        ellipsis: true
    },
    {
        title: '保养人',
        dataIndex: 'MaintainUser1',
        key: 'MaintainUser1',
        width: 120,
        ellipsis: true
    },
    {
        title: '备注',
        dataIndex: 'Remark',
        key: 'Remark',
        width: 200,
        ellipsis: true
    },
]

// 工艺参数表头
export const craftColumns = [
    {
        title: 'TAG点',
        dataIndex: 'TargetPointName',
        key: 'TargetPointName',
        width: 145,
        ellipsis: true
    },
    {
        title: '点位名',
        dataIndex: 'PointName',
        key: 'PointName',
        width: 95,
        ellipsis: true
    },
    {
        title: '点位值',
        dataIndex: 'Value',
        key: 'Value',
        width: 60,
        ellipsis: true
    },
]

//产线异常表头
export const lineAbnormalColumns = [
    {
        title: '订单号',
        dataIndex: 'ue_bill_id',
        key: 'ue_bill_id',
        width: 110,
        ellipsis: true
    },
    {
        title: '项目名称',
        dataIndex: 'zspec',
        key: 'zspec',
        width: 100,
        ellipsis: true
    },
    {
        title: '当前阶段',
        dataIndex: 'name_',
        key: 'name_',
        width: 100,
        ellipsis: true
    },
    {
        title: '人员',
        dataIndex: 'assignee_',
        key: 'assignee_',
        width: 80,
        ellipsis: true
    },
    {
        title: '时间',
        dataIndex: 'create_time_',
        key: 'create_time_',
        ellipsis: true
    },
]
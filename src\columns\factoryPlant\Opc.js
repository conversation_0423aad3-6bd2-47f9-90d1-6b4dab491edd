export const opcColumns = [
    { field: 'Name', title: 'Name', width: 200 },
    { field: 'Description', title: 'Description', width: 350 },
    { field: 'OpcActionClassId', title: 'Action Class', minWidth: 300 },
    { field: 'Timeout', title: 'Timeout(ms)', width: 100 },
    { field: 'Properties', title: 'Properties', width: 120 },
    { field: 'Triggers', title: 'Triggers', width: 120 },
    { field: 'LogTransactions', title: 'Log Transsctions', width: 120 },
    { field: 'BlockRead', title: 'Block Read', width: 120 },
    { field: 'AllowMultipleInstances', title: 'Allow Multiple Instances', width: 110 },
    { field: 'Action', title: 'Action', width: 80, fixed: 'right' },
]
export const instancesColumns = [
    { field: 'text', title: '', width: 50 },
    { field: 'EquipmentId', title: 'Machine' },
    { field: 'InstanceId', title: 'Instance' },
    { field: 'IsEnabled', title: 'Enabled' },
    { field: 'Notifications', title: 'Notifications' },
    { field: 'Note', title: 'Note' },
    { field: 'Action', title: 'Action' },
]
export const propertiesColumns = [
    { field: 'Name', title: 'Name', width: 120 },
    { field: 'Description', title: 'Description', width: 300 },
    { field: 'Type', title: 'Type', width: 90 },
    { field: 'DataType', title: 'Data Type', width: 100 },
    { field: 'InitialValue', title: 'Initial Value', width: 100 },
    { field: 'NullValue', title: 'Null Value', width: 100 },
    { field: 'IsBound', title: 'Bound', width: 100 },
    { field: 'IsSubscribed', title: 'Subscription', width: 100 },
    { field: 'Action', title: 'Action', width: 60, fixed: "right" },
]
export const newPropertiesColumns = [
    { field: 'Name', title: 'Name', width: 120 },
    { field: 'Description', title: 'Description', width: 300 },
    { field: 'Type', title: 'Type', width: 90 },
    { field: 'DataType', title: 'Data Type', width: 100 },
    { field: 'InitialValue', title: 'Initial Value', width: 100 },
    { field: 'NullValue', title: 'Null Value', width: 100 },
    { field: 'IsBound', title: 'Bound', width: 100 },
    { field: 'IsSubscribed', title: 'Subscription' },
    { field: 'OpcTagName', title: 'Tag Name' },
]
export const triggersColumns = [
    { field: 'TriggerType', title: 'Trigger Type', width: 120 },
    { field: 'OpcFunctionPropertyId', title: 'Property', width: 120 },
    { field: 'TriggerCondition', title: 'Trigger Condition', width: 120 },
    { field: 'OffCondition', title: 'Off Condition', width: 120 },
    { field: 'Frequency', title: 'Frequency', width: 120 },
    { field: 'OpcActionClassId', title: 'Trigger Action Class', minWidth: 150 },
    { field: 'Action', title: 'Action', width: 60, fixed: "right" },
]
export const tagAddressColumns = [
    { field: 'Name', title: 'Name' },
    { field: 'Description', title: 'Description' },
    { field: 'DataType', title: 'Property Data Type' },
    { field: 'ArrayLength', title: 'Array Size' },
    { field: 'Factor', title: 'Factor' },
    { field: 'RawPlcTagId', title: 'Raw PLC Tag' },
    { field: 'Action', title: '', width: 60 },
]
export const functionInstancesColumn = [
    { type: 'checkbox', width: 50 },
    { field: 'text', title: '', width: 80 },
    { type: 'expand', title: '', width: 80 },
    { field: 'OpcFunctionId', title: 'Function' },
    { field: 'InstanceId', title: 'Instance' },
    { field: 'EquipmentId', title: 'Machine' },
    { field: 'IsEnabled', title: 'Enabled' },
    { field: 'Notifications', title: 'Notifications' },
    { field: 'Note', title: 'Note' },
    { field: 'Action', title: '' },
]
export const functionInstancesExpandColumn = [
    { field: 'Name', title: 'Name' },
    { field: 'DataType', title: 'Data Type' },
    { field: 'InitialValue', title: 'Initial Value' },
    { field: 'CurrentValue', title: 'Current Value' },
    { field: 'TagName', title: 'Tag Name' },
]
export const editPropertyColumns = [
    { field: 'feature', title: 'Feature' },
    { field: 'Name', title: 'Name' },
    { field: 'Description', title: 'Description' },
    { field: 'Value', title: 'Value' },
    { field: 'Default', title: 'Default' },
    { field: 'Updated', title: 'Updated' },
]

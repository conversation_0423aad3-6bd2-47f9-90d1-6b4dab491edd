<template>
  <div>
    <v-form
      v-model="valid"
      ref="form"
    >
      <v-row class="ma-4">
        <span style="color: red;">*</span>
        <v-col
          class="py-0 px-3"
          cols="12"
          sm="6"
          md="2"
        >
          <v-text-field
            v-model="form.leftWidth"
            outlined
            dense
            :label="$t('$vuetify.dataTable.SIM_CONFIG.leftWidth')"
          >
          </v-text-field>
        </v-col>
        <span style="color: red;">*</span>
        <v-col
          class="py-0 px-3"
          cols="12"
          sm="6"
          md="2"
        >
          <v-text-field
            v-model="form.leftHeight"
            outlined
            dense
            :label="$t('$vuetify.dataTable.SIM_CONFIG.leftHeight')"
          >
          </v-text-field>
        </v-col>
        <span style="color: red;">*</span>
        <v-col
          class="py-0 px-3"
          cols="12"
          sm="6"
          md="2"
        >
          <v-text-field
            :disabled="dialogType == 'edit' && cardNum.length>0"
            v-model="form.RegionNum"
            :rules="rules.RegionNum"
            outlined
            dense
            :label="$t('$vuetify.dataTable.SIM_CONFIG.RegionNum')"
            @input="RegionNumChange"
          ></v-text-field>
        </v-col>
        <v-col
          class="py-0 px-3"
          cols="12"
          sm="6"
          md="4"
        >
          <el-upload
            ref="xiangqtu"
            class="upload-demo"
            accept="image/jpeg,image/gif,image/png"
            :action="'https://sim.fhtdchem.com'+'/simapi/PageConfig/Upload'"
            :before-upload="beforeAvatarUpload11"
            multiple
            :on-preview="handlePreview1"
            :on-remove="handleRemove41"
            :on-success="handleAvatarSuccess41"
            :file-list="deailFileList1"
            list-type="picture-card"
          >
            <i
              slot="default"
              class="el-icon-plus"
            />
            <div
              slot="file"
              slot-scope="{file}"
            >
              <img
                :src="file.url"
                alt=""
                class="el-upload-list__item-thumbnail"
              >
              <span class="el-upload-list__item-actions">
                <!-- <span
                  class="el-upload-list__item-delete"
                  @click="handlePreview(file)"
                >
                  <i class="el-icon-zoom-in" />
                </span> -->
                <span
                  class="el-upload-list__item-delete"
                  @click="handleRemove41(file,deailFileList1)"
                >
                  <i class="el-icon-delete" />
                </span>
              </span>
            </div>
            <!-- <el-button size="small" type="primary">点击上传</el-button> -->
          </el-upload>
        </v-col>
        <v-col
          class="py-0 px-3"
          cols="12"
          sm="6"
          md="1"
        >
          <div class="ExhibitionBox">
            <div class="ExhibitionBox_l">
              <!-- <div
              class="ExhibitionBox_l_n"
              v-for="(item,index) in cardNum"
              :key="index"
            >{{index}}</div> -->
            </div>
            <div class="ExhibitionBox_c"></div>
            <div class="ExhibitionBox_r"></div>
          </div>
        </v-col>
      </v-row>
    </v-form>

    <div
      v-for="(item,index) in cardNum"
      :key="index"
      class="showBox"
    >
      <v-form
        v-model="validlist"
        ref="form"
      >
        <div style="padding-left: 2%;box-sizing: border-box;font-size: 14px;font-weight: bold;">图表{{index+1}}</div>
        <v-row class="ma-4">
          <v-col
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;"
          >
            <span style="color: red;">*</span>
            <v-select
              v-model="item.OlineType"
              :items="dutyList"
              item-text="Fullname"
              item-value="Encode"
              :label="$t('$vuetify.dataTable.SIM_CONFIG.OlineType')"
              return-object
              dense
              outlined
              @change="OlineChange(item.OlineType,index)"
            >
            </v-select>
          </v-col>
          <v-col
            v-if="!item.colFlag"
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;"
          >
            <span style="color: red;">*</span>
            <v-text-field
              v-model="item.ModularName"
              outlined
              dense
              :label="$t('$vuetify.dataTable.SIM_CONFIG.ModularName')"
            >
            </v-text-field>
          </v-col>
          <v-col
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;"
          >
            <span style="color: red;">*</span>
            <v-text-field
              v-model="item.style.width"
              outlined
              dense
              :label="$t('$vuetify.dataTable.SIM_CONFIG.width')"
            >
            </v-text-field>
          </v-col>
          <v-col
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;"
          >
            <span style="color: red;">*</span>
            <v-text-field
              v-model="item.style.height"
              outlined
              dense
              :label="$t('$vuetify.dataTable.SIM_CONFIG.height')"
            >
            </v-text-field>
          </v-col>
          <v-col
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;"
          >
            <span style="color: red;">*</span>
            <v-text-field
              v-model="item.Order"
              outlined
              dense
              :label="$t('$vuetify.dataTable.SIM_CONFIG.Order')"
            >
            </v-text-field>
          </v-col>

          <v-col
            v-if="item.colFlag"
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;"
          >
            <span style="color: red;">*</span>
            <v-text-field
              v-model="item.RegionNumTab"
              :rules="rules.RegionNumTab"
              outlined
              dense
              :label="$t('$vuetify.dataTable.SIM_CONFIG.RegionNumTab')"
              @input="tabNumChange(item.RegionNumTab,index)"
            ></v-text-field>
          </v-col>
          <v-col
            v-if="item.tableHeFlag"
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;margin-top: -12px;"
          >
            <v-col
              v-if="item.tableHeFlag"
              style="display: flex;"
            >
              <span style="color: red;">*</span>
              <v-select
                v-model="item.DataSource"
                :items="dutyListData2x"
                item-text="TagName"
                item-value="TagCode"
                :label="$t('$vuetify.dataTable.SIM_CONFIG.DataSource')"
                return-object
                dense
                outlined
                @change="DimensionChange(item.DataSource,index)"
              >
              </v-select>

              <!-- <v-autocomplete
                v-model="item.DataSource"
                :items="dutyListData2x"
                :label="$t('$vuetify.dataTable.SIM_CONFIG.DataSource')"
                item-text="TagName"
                item-value="TagCode"
                clearable
                dense
                outlined
                @change="DimensionChange(item.DataSource,index)"
              >
              </v-autocomplete> -->
            </v-col>
            <div
              @click="slChange1()"
              style="width: 80px;height: 30px;font-size: 16px;text-align: center;line-height: 30px;background-color: #3dcd58;border-radius: 5px;color: #fff;margin-top: 15px;"
            >获取数据</div>
            <div
              @click="slChange()"
              style="width: 80px;height: 30px;font-size: 16px;text-align: center;line-height: 30px;background-color: #3dcd58;border-radius: 5px;color: #fff;margin-top: 15px;"
            >示例</div>

          </v-col>
          <v-col
            v-if="item.tableHeFlag == true"
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;"
          >
            <span style="color: red;">*</span>
            <v-select
              v-model="item.dataFormat"
              :items="dataFormatList"
              item-text="Fullname"
              item-value="Encode"
              :label="$t('$vuetify.dataTable.SIM_CONFIG.dataFormat')"
              return-object
              dense
              outlined
            >
            </v-select>
          </v-col>
          <v-col
            v-if="item.tableHeFlag"
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;"
          >
            <span style="color: red;">*</span>
            <v-select
              v-model="item.dataSpan"
              :items="dataSpanList"
              item-text="Fullname"
              item-value="Encode"
              :label="$t('$vuetify.dataTable.SIM_CONFIG.dataSpan')"
              return-object
              dense
              outlined
            >
            </v-select>
          </v-col>
          <v-col
            v-if="item.tableHeFlag"
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;"
          >
            <span style="color: red;">*</span>
            <v-select
              v-model="item.Dimension"
              :items="item.DimensionList"
              item-text="TimeDimensionName"
              item-value="TimeDimension"
              :label="$t('$vuetify.dataTable.SIM_CONFIG.Dimension')"
              return-object
              dense
              outlined
              multiple
            >
            </v-select>
          </v-col>
          <!-- <v-col
            v-if="item.tableHeFlag"
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;"
          >
            <span style="color: red;">*</span>
            <el-color-picker v-model="item.TargetValue"></el-color-picker>
          </v-col> -->
          <v-col
            v-if="item.tableHeFlag"
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;"
          >
            <span style="color: red;">*</span>
            <v-text-field
              v-model="item.tableHeight"
              outlined
              dense
              :label="$t('$vuetify.dataTable.SIM_CONFIG.tableHeight')"
            >
            </v-text-field>
          </v-col>
          <v-col
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;"
          >
            <v-select
              v-model="item.routePage"
              :items="resultMenu"
              item-text="TagName"
              item-value="TagCode"
              :label="$t('$vuetify.dataTable.SIM_CONFIG.routePage')"
              return-object
              dense
              outlined
              clearable
            >
            </v-select>
          </v-col>
          <v-col
            v-if="(item.OlineType?.Fullname == '文字描述' || item.OlineType?.Fullname == '列别') || item.tableHeFlag"
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;"
          >
            <span style="color: red;">*</span>
            <v-text-field
              v-model="item.listParams"
              outlined
              dense
              :label="$t('$vuetify.dataTable.SIM_CONFIG.listParams')"
            >
            </v-text-field>
          </v-col>
          <v-col
            v-if="(item.OlineType?.Fullname == '文字描述' || item.OlineType?.Fullname == '列别') || item.tableHeFlag"
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;"
          >
            <span style="color: red;">*</span>
            <v-text-field
              v-model="item.itemCode"
              outlined
              dense
              :label="$t('$vuetify.dataTable.SIM_CONFIG.itemCode')"
            >
            </v-text-field>
          </v-col>
          <v-col
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
            style="display: flex;"
          >
            <span style="color: red;">*</span>
            <v-text-field
              v-model="item.indexSort"
              outlined
              dense
              label="图表序号"
            >
            </v-text-field>
          </v-col>
          <v-col
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="2"
          >
            <v-select
              style="margin-top: 10px;"
              v-model="item.TargetValue"
              :items="openList1"
              item-text="Fullname"
              item-value="Encode"
              label="判断条件"
              return-object
              dense
              outlined
            >
            </v-select>
          </v-col>

        </v-row>
        <div style="font-size: 16px;margin-bottom: 10px;margin-left: 30px;"><span style="color: red;">*</span>上传背景图片：</div>
        <v-row class="ma-4">
          <v-col
            class="py-0 px-3"
            cols="12"
            sm="6"
            md="4"
          >
            <el-upload
              :data-index="index"
              ref="xiangqtu"
              class="upload-demo"
              style="width:800px"
              accept="image/jpeg,image/gif,image/png"
              :action="'https://sim.fhtdchem.com'+'/simapi/PageConfig/Upload'"
              :before-upload="beforeAvatarUpload1"
              multiple
              :on-preview="handlePreview"
              :on-remove="handleRemove4"
              :on-success="(response) => handleAvatarSuccess4(response, item,index)"
              :file-list="item.deailFileList"
              list-type="picture-card"
            >
              <i
                slot="default"
                class="el-icon-plus"
              />
              <div
                slot="file"
                slot-scope="{file}"
              >
                <img
                  :src="file.url"
                  alt=""
                  class="el-upload-list__item-thumbnail"
                >
                <span class="el-upload-list__item-actions">
                  <!-- <span
                  class="el-upload-list__item-delete"
                  @click="handlePreview(file)"
                >
                  <i class="el-icon-zoom-in" />
                </span> -->
                  <span
                    class="el-upload-list__item-delete"
                    @click="handleRemove4(file,deailFileList,item)"
                  >
                    <i class="el-icon-delete" />
                  </span>
                </span>
              </div>
              <!-- <el-button size="small" type="primary">点击上传</el-button> -->
            </el-upload>
          </v-col>
          <v-btn
            v-if="(index+1) == cardNum.length"
            style="color: #fff;margin-right: 5px;margin-left: 10px;"
            color="primary"
            @click="addChange(item,index)"
          >{{ $t('GLOBAL._XZ') }}</v-btn>
          <v-btn
            style="color: #fff;"
            color="red"
            @click="removeChange(item,index)"
          >{{ $t('GLOBAL._SC') }}</v-btn>
        </v-row>
        <div
          v-if="item.colFlag"
          style="padding-left: 2%;box-sizing: border-box;font-size: 14px;font-weight: bold;"
        >tab域图表</div>
        <el-table
          v-if="item.colFlag"
          :data="item.tabCardNum"
          border
          style="width: 97%;margin:0 auto;"
          height="600px"
        >
          <el-table-column
            align="center"
            type="index"
            label="序号"
            width="60"
            fixed="left"
          >
          </el-table-column>
          <el-table-column
            prop="TabTitle"
            label="是否启用"
            width="130"
            fixed="left"
          >
            <template slot-scope="scope">
              <v-select
                style="margin-top: 10px;"
                v-model="scope.row.isOpen"
                :items="openList"
                item-text="Fullname"
                item-value="Encode"
                label="是否启用"
                return-object
                dense
                outlined
              >
              </v-select>
            </template>
          </el-table-column>
          <el-table-column
            prop="TabTitle"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.TabTitle')"
            width="120"
            fixed="left"
          >
            <template slot-scope="scope">
              <v-text-field
                style="margin-top: 10px;"
                v-model="scope.row.TabTitle"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.TabTitle')"
              >
              </v-text-field>
            </template>
          </el-table-column>
          <el-table-column
            prop="OlineType"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.OlineType')"
            width="130"
          >
            <template slot-scope="scope">
              <v-select
                style="margin-top: 10px;"
                v-model="scope.row.OlineType"
                :items="dutyList"
                item-text="Fullname"
                item-value="Encode"
                :label="$t('$vuetify.dataTable.SIM_CONFIG.OlineType')"
                return-object
                dense
                outlined
                @change="OlineChange1(scope.row.OlineType,scope.$index)"
              >
              </v-select>
            </template>
          </el-table-column>
          <el-table-column
            prop="ModularName"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.ModularName')"
            width="180"
          >
            <template slot-scope="scope">
              <v-text-field
                style="margin-top: 10px;"
                v-model="scope.row.ModularName"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.ModularName')"
              >
              </v-text-field>
            </template>
          </el-table-column>
          <el-table-column
            prop="width"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.width')"
            width="90"
          >
            <template slot-scope="scope">
              <v-text-field
                style="margin-top: 10px;"
                v-model="scope.row.style.width"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.width')"
              >
              </v-text-field>
            </template>
          </el-table-column>
          <el-table-column
            prop="height"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.height')"
            width="90"
          >
            <template slot-scope="scope">
              <v-text-field
                style="margin-top: 10px;"
                v-model="scope.row.style.height"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.height')"
              >
              </v-text-field>
            </template>
          </el-table-column>

          <el-table-column
            prop="DataSource"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.DataSource')"
            width="180"
          >
            <template slot-scope="scope">
              <v-select
                :disabled="(scope.row.OlineType.Fullname == '九宫格' || scope.row.OlineType.Fullname == '列别' || scope.row.OlineType.Fullname == '文字描述' || scope.row.OlineType.Fullname == '安全十字' || scope.row.OlineType.Fullname == 'tab切换') && scope.row.tabIndex1 == scope.$index"
                style="margin-top: 10px;"
                v-model="scope.row.DataSource"
                :items="dutyListData2x"
                item-text="TagName"
                item-value="TagCode"
                :label="$t('$vuetify.dataTable.SIM_CONFIG.DataSource')"
                return-object
                dense
                outlined
                @change="DimensionChange1(scope.row,scope.$index)"
              >
              </v-select>
            </template>
          </el-table-column>
          <el-table-column
            prop="dataFormat"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.dataFormat')"
            width="120"
          >
            <template slot-scope="scope">
              <v-select
                :disabled="(scope.row.OlineType.Fullname == '九宫格' || scope.row.OlineType.Fullname == '列别' || scope.row.OlineType.Fullname == '文字描述' || scope.row.OlineType.Fullname == '安全十字' || scope.row.OlineType.Fullname == 'tab切换') && scope.row.tabIndex1 == scope.$index"
                style="margin-top: 10px;"
                v-model="scope.row.dataFormat"
                :items="dataFormatList"
                item-text="Fullname"
                item-value="Encode"
                :label="$t('$vuetify.dataTable.SIM_CONFIG.dataFormat')"
                return-object
                dense
                outlined
              >
              </v-select>
            </template>
          </el-table-column>
          <el-table-column
            prop="dataSpan"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.dataSpan')"
            width="100"
          >
            <template slot-scope="scope">
              <v-select
                :disabled="(scope.row.OlineType.Fullname == '九宫格' || scope.row.OlineType.Fullname == '列别' || scope.row.OlineType.Fullname == '文字描述' || scope.row.OlineType.Fullname == '安全十字' || scope.row.OlineType.Fullname == 'tab切换') && scope.row.tabIndex1 == scope.$index"
                style="margin-top: 10px;"
                v-model="scope.row.dataSpan"
                :items="dataSpanList"
                item-text="Fullname"
                item-value="Encode"
                :label="$t('$vuetify.dataTable.SIM_CONFIG.dataSpan')"
                return-object
                dense
                outlined
              >
              </v-select>
            </template>
          </el-table-column>
          <el-table-column
            prop="Dimension1"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.Dimension')"
            width="140"
          >
            <template slot-scope="scope">
              <v-select
                :disabled="(scope.row.OlineType.Fullname == '九宫格' || scope.row.OlineType.Fullname == '列别' || scope.row.OlineType.Fullname == '文字描述' || scope.row.OlineType.Fullname == '安全十字' || scope.row.OlineType.Fullname == 'tab切换') && scope.row.tabIndex1 == scope.$index"
                style="margin-top: 10px;"
                v-model="scope.row.Dimension1"
                :items="scope.row.DimensionList1"
                item-text="TimeDimensionName"
                item-value="TimeDimension"
                :label="$t('$vuetify.dataTable.SIM_CONFIG.Dimension')"
                return-object
                dense
                outlined
                multiple
              >
              </v-select>
            </template>
          </el-table-column>
          <el-table-column
            prop="tabChartIndex"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.tabChartIndex')"
            width="120"
          >
            <template slot-scope="scope">
              <v-text-field
                style="margin-top: 10px;"
                v-model="scope.row.tabChartIndex"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.tabChartIndex')"
              >
              </v-text-field>
            </template>
          </el-table-column>
          <el-table-column
            prop="tableHeight"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.tableHeight')"
            width="100"
          >
            <template slot-scope="scope">
              <v-text-field
                :disabled="scope.row.OlineType.Fullname != '表格'"
                style="margin-top: 10px;"
                v-model="scope.row.tableHeight"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.tableHeight')"
              >
              </v-text-field>
            </template>
          </el-table-column>
          <el-table-column
            prop="routePage"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.routePage')"
            width="180"
          >
            <template slot-scope="scope">
              <v-select
                style="margin-top: 10px;"
                v-model="scope.row.routePage"
                :items="resultMenu"
                item-text="TagName"
                item-value="TagCode"
                :label="$t('$vuetify.dataTable.SIM_CONFIG.routePage')"
                return-object
                dense
                outlined
                clearable
              >
              </v-select>
            </template>
          </el-table-column>
          <el-table-column
            prop="listParams"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.listParams')"
            width="180"
          >
            <template slot-scope="scope">
              <v-text-field
                style="margin-top: 10px;"
                v-model="scope.row.listParams"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.listParams')"
              >
              </v-text-field>
            </template>
          </el-table-column>
          <el-table-column
            prop="itemCode"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.itemCode')"
            width="110"
          >
            <template slot-scope="scope">
              <v-text-field
                style="margin-top: 10px;"
                v-model="scope.row.itemCode"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.itemCode')"
              >
              </v-text-field>
            </template>
          </el-table-column>

          <el-table-column
            prop="Order"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.Order')"
            width="180"
          >
            <template slot-scope="scope">
              <v-text-field
                style="margin-top: 10px;"
                v-model="scope.row.Order"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.Order')"
              >
              </v-text-field>
            </template>
          </el-table-column>
          <el-table-column
            prop="TargetValue"
            label="判断条件"
            width="130"
            fixed="left"
          >
            <template slot-scope="scope">
              <v-select
                style="margin-top: 10px;"
                v-model="scope.row.TargetValue"
                :items="openList1"
                item-text="Fullname"
                item-value="Encode"
                label="判断条件"
                return-object
                dense
                outlined
              >
              </v-select>
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop="itemCode"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.itemCode')"
            width="280"
          >
            <template slot-scope="scope">
              <el-upload
                ref="xiangqtu"
                class="upload-demo"
                style="width:800px"
                accept="image/jpeg,image/gif,image/png"
                :action="'https://sim.fhtdchem.com'+'/simapi/PageConfig/Upload'"
                :before-upload="beforeAvatarUpload1"
                multiple
                :on-preview="handlePreview"
                :on-remove="handleRemove4"
                :on-success="(response) => handleAvatarSuccess4(response, scope.row,scope.$index)"
                :file-list="scope.row.deailFileList"
                list-type="picture-card"
              >
                <i
                  slot="default"
                  class="el-icon-plus"
                />
                <div
                  slot="file"
                  slot-scope="{file}"
                >
                  <img
                    :src="file.url"
                    alt=""
                    class="el-upload-list__item-thumbnail"
                  >
                  <span class="el-upload-list__item-actions">
                    <span
                      class="el-upload-list__item-delete"
                      @click="handleRemove4(file,deailFileList,item1)"
                    >
                      <i class="el-icon-delete" />
                    </span>
                  </span>
                </div>
              </el-upload>
            </template>
          </el-table-column> -->
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="180"
          >
            <template slot-scope="scope">
              <el-button
                v-if="(scope.$index+1) == item.tabCardNum.length"
                type="success"
                size="mini"
                @click.native="addChangetab(scope.row,scope.$index)"
              >新增</el-button>
              <el-button
                type="danger"
                size="mini"
                @click.native="removeChangetab(scope.row,scope.$index)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </v-form>
    </div>
    <sqlList ref="sqlV" />
  </div>
</template>
<script>
// import { getTagList } from '@/api/simConfig/simconfignewdata.js';
import { getDataSourceList, getEditEntityByCode, getImageUel } from '@/api/simConfig/simconfignew.js';
import { configUrl } from '@/config';

export default {
  components: {
    sqlList: () => import('@/views/simManagement/simNew1/components/sqlList.vue'),
  },
  props: {
    dialogType: {
      type: String,
      default: ''
    },
    OneId: {
      type: String,
      default: ''
    },
    Moudelname: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    selectCode: {
      type: String,
      default: ''
    },
    dutyListData2: {
      type: Array,
      default: () => []
    },
    dutyListData22: {
      type: Array,
      default: () => []
    },
    listData1: {
      type: Object,
      default: () => { }
    },
  },
  data() {
    return {
      openList: [{
        Fullname: '是',
        Encode: '1'
      },
      {
        Fullname: '否',
        Encode: '2'
      },],
      openList1: [{
        Fullname: '大于',
        Encode: '1'
      },
      {
        Fullname: '小于',
        Encode: '2'
      },],
      dutyListData2x: [],
      dutyListData22y: [],
      Pageid: '',
      zlistData: {},
      SIMLevel: '',
      resultMenu: [],
      textValue: `
        {
    "x": [
        "24年02月",
        ......
        "24年09月"
    ],
    "大拉": [
        null,
        null,
        null,
        null,
        353.4,
        3866.2,
        6760.3,
        null
    ],
    "目标值": [
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null
    ],
}
      `,
      dialogImageUrl1: '',
      deailFileList1: [],
      fileUrl1: '',
      baseURL: configUrl[process.env.VUE_APP_SERVE].baseURL_DFM,
      deailFileList: [],
      tbIndex: 0,
      thrId: '',
      RelationTypeList: [
        {
          Fullname: '生产',
          Encode: 'shengchan'
        },
        {
          Fullname: '质量',
          Encode: 'zhiliang'
        },
        {
          Fullname: '设备',
          Encode: 'shebei'
        },
        {
          Fullname: '成本',
          Encode: 'chengben'
        },
      ],
      exhibitionTypeData: [
        {
          Fullname: '图表',
          Encode: '图表'
        },
        {
          Fullname: '表格',
          Encode: '表格'
        },
      ],
      configData: [
        {
          Fullname: '是',
          Encode: '是'
        },
        {
          Fullname: '否',
          Encode: '否'
        },
      ],
      dataSpanList: [
        {
          Fullname: '0',
          Encode: '0'
        },
        {
          Fullname: '1',
          Encode: '1'
        },
        {
          Fullname: '2',
          Encode: '2'
        },
        {
          Fullname: '3',
          Encode: '3'
        },
        {
          Fullname: '4',
          Encode: '4'
        },
        {
          Fullname: '5',
          Encode: '5'
        },
        {
          Fullname: '6',
          Encode: '6'
        },
        {
          Fullname: '7',
          Encode: '7'
        },
        {
          Fullname: '8',
          Encode: '8'
        },
        {
          Fullname: '9',
          Encode: '9'
        },
        {
          Fullname: '10',
          Encode: '10'
        },
        {
          Fullname: '11',
          Encode: '11'
        },
        {
          Fullname: '12',
          Encode: '12'
        },
      ],
      dataFormatList: [
        {
          Fullname: '0',
          Encode: '0'
        },
        {
          Fullname: '0.0',
          Encode: '0.0'
        },
        {
          Fullname: '0.00',
          Encode: '0.00'
        },
        {
          Fullname: '0.000',
          Encode: '0.000'
        },
        {
          Fullname: '0.0000',
          Encode: '0.0000'
        },
      ],
      form: {
        leftWidth: '',
        leftHeight: '',
        img1: '',
      },
      dutyListData: [],
      TargetColor: '',
      dutyList: [

        {
          Fullname: '折线图',
          Encode: '1'
        },
        {
          Fullname: '柱状图',
          Encode: '2'
        },
        {
          Fullname: '横向柱状图',
          Encode: '3'
        },
        // {
        //   Fullname: '柱状图+折线图',
        //   Encode: '4'
        // },
        {
          Fullname: '饼图',
          Encode: '5'
        },
        {
          Fullname: '环形图',
          Encode: '6'
        },
        // {
        //   Fullname: '仪表盘',
        //   Encode: '7'
        // },
        {
          Fullname: '表格',
          Encode: '8'
        },
        {
          Fullname: '列别',
          Encode: '9'
        },
        {
          Fullname: '文字描述',
          Encode: '10'
        },
        {
          Fullname: '九宫格',
          Encode: '11'
        },
        {
          Fullname: 'tab切换',
          Encode: '12'
        },
        {
          Fullname: '安全十字',
          Encode: '13'
        },
        // {
        //   Fullname: 'andon',
        //   Encode: '14'
        // },

      ],
      cardNum: [],
      tabCardNum: [],
      preopleList: [],
      classcheckbox: true,
      // form: {
      //   Olinetit: '',
      //   OlineType: '',
      //   RegionNum: '',
      //   width: '',
      //   height: '',
      //   backgroundImage: '',
      //   DataSource: '',
      //   ConditionType: '',
      //   ModularName: '',
      //   tablineName: '',
      //   tabheight: '',
      //   tabName: '',
      //   tabNum: '',
      //   tabwidth: '',
      //   relationName: '',
      //   RegionNumTab: ''
      // },
      rules: {
        Olinetit: [v => !!v || this.$t('GLOBAL.Olinetit')],
        OlineType: [v => !!v || this.$t('GLOBAL.OlineType')],
        RegionNum: [v => !!v || this.$t('GLOBAL.RegionNum')],
        width: [v => !!v || this.$t('GLOBAL.width')],
        height: [v => !!v || this.$t('GLOBAL.height')],
        // backgroundImage: [v => !!v || this.$t('GLOBAL.backgroundImage')],
        DataSource: [v => !!v || this.$t('GLOBAL.DataSource')],
        RegionNumTab: [v => !!v || this.$t('GLOBAL.RegionNumTab')],
      },
      valid: true,
      validlist: true,
      showDialog: false,
      tabFlag: false,
      xlNum: 0,
      tabIndex: 0,
      tabIndex1: 0,
      tableHeFlag: false,
      dutyListData1: [],
      menuList: [],
    };
  },
  created() {
    // this.querySelectList()
    // this.dutyListData2x = this.dutyListData2
    // this.dutyListData22y = this.dutyListData22
    this.resultMenu = []
    this.menuList = this.getMenu();
    this.menuList.map((item, index) => {
      this.resultMenu.push({
        "TagName": item.title,
        "TagCode": item.path
      })
    })
  },
  mounted() {
    // setTimeout(() => {
    //   this.querySelectList()
    // }, 500)
    // this.dutyListData2x = this.dutyListData2
    // this.dutyListData22y = this.dutyListData22
  },

  methods: {
    slChange1() {
      this.querySelectList()
    },
    getMenu() {
      let menus = []
      let computedMenu = this.filterRouteItem(this.$store.getters.getMenuList);
      function recursion(list) {
        list.forEach(item => {
          let obj = JSON.parse(JSON.stringify(item))
          delete obj.children
          menus.push(obj)
          if (item.children && item.children.length > 0) {
            recursion(item.children)
          }
        });
      }
      recursion(computedMenu)
      return [...new Set(menus)].filter(item => item.MEMU_TYPE == 2)
    },
    filterRouteItem(routes) {
      return routes
        .filter(item => item.Enable == '1' && !item.isHide)
        .filter(item => ['admin', 'user'].includes(item.Code)) //  菜单有效判断
        .map(item => {
          return {
            MEMU_TYPE: item.MEMU_TYPE,
            title: this.$store.getters.getLocale == 'cn' ? item.CnName : (this.$t(item.CnCode) || item.CnName),
            icon: item.Icon,
            path: item.Route,
            children: item.children ? this.filterRouteItem(item.children) : []
          };
        });
    },
    slChange() {
      this.$refs.sqlV.showDialogsql = true
    },
    beforeAvatarUpload11(file) {
      const isLtSize = file.size / 1024 < 3072
      if (!isLtSize) {
        this.$message.error('上传图片大小不能超过3M')
        return false
      }
      return true
    },
    handlePreview1(file) {
      this.dialogImageUrl1 = file.url
      this.dialogVisible1 = true
    },
    handleRemove41(file, deailFileList1) {
      for (let i = 0; i < deailFileList1.length; i++) {
        if (file.name === deailFileList1[i].name) {
          deailFileList1.splice(i, 1)
          this.deailFileList1 = deailFileList1
        }
      }
      this.form.img1 = ''
    },
    handleAvatarSuccess41(file) {
      this.form.img1 = file.response.fileName
      // 图片上传成功
      this.deailFileList1.push({
        name: file.response.fileName,
        url: file.response.fileUrl,
      })
    },


    beforeAvatarUpload1(file) {
      const isLtSize = file.size / 1024 < 3072
      if (!isLtSize) {
        this.$message.error('上传图片大小不能超过3M')
        return false
      }
      return true
    },
    handlePreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleRemove4(file, deailFileList, item) {
      for (let i = 0; i < item.deailFileList.length; i++) {
        if (file.name === item.deailFileList[i].name) {
          item.deailFileList.splice(i, 1)
          item.style.backgroundImage = ''
        }
      }
    },
    handleAvatarSuccess4(response, item, index) {
      // 图片上传成功
      item.deailFileList.push({
        name: response.response.fileName,
        url: response.response.fileUrl,
      })
      item.style.backgroundImage = response.response.fileName
    },
    async queryEditList() {
      this.deailFileList1 = []
      const params = {
        code: this.OneId
      }
      const res = await getEditEntityByCode(params);
      if (res.success) {
        this.zlistData = res.response
        if (JSON.parse(res.response.Leftjosn).listChart.length > 0) {
          this.cardNum = JSON.parse(res.response.Leftjosn).listChart
          this.form.leftWidth = JSON.parse(res.response.Leftjosn).leftWidth
          this.form.leftHeight = JSON.parse(res.response.Leftjosn).leftHeight
          this.form.RegionNum = JSON.parse(res.response.Leftjosn).listChart.length
          this.xlNum = JSON.parse(res.response.Leftjosn).listChart.length
          // this.deailFileList1 = JSON.parse(res.response.Leftjosn).deailFileList || []
          if (JSON.parse(res.response.Leftjosn).img1) {
            this.form.img1 = JSON.parse(res.response.Leftjosn).img1
            const res2 = await getImageUel(JSON.parse(res.response.Leftjosn).img1);
            this.deailFileList1.push({
              name: JSON.parse(res.response.Leftjosn).img1,
              url: res2.response
            })
          }

          this.cardNum.map(async (a, b) => {
            if (a.deailFileList.length > 0) {
              const resh3 = await getImageUel(a.deailFileList[0].name);
              a.deailFileList[0].push({
                name: a.deailFileList[0].name,
                url: resh3.response
              })

            }
            a.tabCardNum.map(async (a1, b1) => {
              if (a1.deailFileList.length > 0) {
                const resh34 = await getImageUel(a1.deailFileList[0].name);
                a1.deailFileList[0].push({
                  name: a1.deailFileList[0].name,
                  url: resh34.response
                })
              }
            })
          })
          this.$emit('savesteupThr', this.cardNum, this.form, this.xlNum, this.thrId, this.deailFileList1, this.zlistData)
          this.thrId = res.response.ID
          if (this.dialogType == 'edit')
            this.Pageid = res.response.Pageid
        }
      }

    },
    async querySelectList() {
      this.dutyListData2x = []
      const params = {
        "SIMLevel": this.simlevel == '' ? this.Moudelname : this.simlevel,
        "KpiType": ''
      }
      const res = await getDataSourceList(params);
      console.log(res, '看看是啥');

      if (res != undefined) {
        this.dutyListData22y = res.response
        res.response.map(el => {
          this.dutyListData2x.push({
            "TagName": el.KpiName,
            "TagCode": el.KpiCode
          })
        })
      }
    },
    // 区域数量
    async RegionNumChange(e) {
      this.querySelectList()
      this.cardNum = []
      let num = parseFloat(e)
      this.xlNum = num
      for (let i = 0; i < num; i++) {
        this.cardNum.push({
          style: {
            width: '',
            height: '',
            backgroundSize: '100%',
            padding: '10px',
            boxSizing: 'border-box',
            marginBottom: '5px',
            backgroundImage: "",
          },
          ModularName: '',
          OlineType: "bar",
          Order: this.Moudelname + '-' + this.selectCode + 'left-' + (i + 1) + '-1',
          listData: [],
          tabCardNum: [],
          DimensionList: [],
          dataFormat: '',
          dataSpan: '',
          // configFlag: '',
          exhibitionType: '',
          TargetValue: '',
          DataSource: '',
          TargetColor: '',
          colFlag: false,
          tableHeight: 0,
          tableHeFlag: true,
          Dimension: '',
          deailFileList: [],
          ajFlag: true,
          routePage: '',
          itemCode: '',
          listParams: '',
          indexSort: i
        })
      }
    },
    OlineChange(e, index) {
      if (e.Fullname == '表格') {
        this.$set(this.cardNum[index], 'tableHeFlag', true);
        this.$set(this.cardNum[index], 'colFlag', false);
        this.$set(this.cardNum[index], 'ajFlag', true);
        this.tabIndex = index
      }
      else if (e.Fullname == 'tab切换') {
        this.$set(this.cardNum[index], 'tableHeFlag', false);
        this.$set(this.cardNum[index], 'colFlag', true);
        this.$set(this.cardNum[index], 'ajFlag', false);
        this.tabIndex = index
      } else if (e.Fullname == '安全十字' || e.Fullname == '九宫格' || e.Fullname == '文字描述' || e.Fullname == '列别') {
        this.$set(this.cardNum[index], 'tableHeFlag', false);
        this.$set(this.cardNum[index], 'colFlag', false);
        this.$set(this.cardNum[index], 'ajFlag', false);
        this.tabIndex = index
      } else {
        this.$set(this.cardNum[index], 'tableHeFlag', true);
        this.$set(this.cardNum[index], 'colFlag', false);
        this.$set(this.cardNum[index], 'ajFlag', true);
        this.tabIndex = -1
      }
    },
    OlineChange1(e, index) {
      this.cardNum[this.tabIndex].tabCardNum
      if (e.Fullname == '表格') {
        this.cardNum[this.tabIndex].tabCardNum[index].tableHeFlag = true
        this.cardNum[this.tabIndex].tabCardNum[index].colFlag = false
        this.cardNum[this.tabIndex].tabCardNum[index].ajFlag = true
        this.cardNum[this.tabIndex].tabCardNum[index].tabIndex1 = index
      }
      else if (e.Fullname == 'tab切换') {
        this.cardNum[this.tabIndex].tabCardNum[index].colFlag = true
        this.cardNum[this.tabIndex].tabCardNum[index].ajFlag = false
        this.cardNum[this.tabIndex].tabCardNum[index].tableHeFlag = false
        this.cardNum[this.tabIndex].tabCardNum[index].tabIndex1 = index
      } else if (e.Fullname == '安全十字' || e.Fullname == '九宫格' || e.Fullname == '文字描述' || e.Fullname == '列别') {
        this.cardNum[this.tabIndex].tabCardNum[index].ajFlag = false
        this.cardNum[this.tabIndex].tabCardNum[index].colFlag = false
        this.cardNum[this.tabIndex].tabCardNum[index].tableHeFlag = false
        this.cardNum[this.tabIndex].tabCardNum[index].tabIndex1 = index
      } else {
        this.cardNum[this.tabIndex].tabCardNum[index].tableHeFlag = true
        this.cardNum[this.tabIndex].tabCardNum[index].colFlag = false
        this.cardNum[this.tabIndex].tabCardNum[index].ajFlag = true
        this.cardNum[this.tabIndex].tabCardNum[index].tabIndex1 = index
      }
    },
    DimensionChange(item, index) {
      this.dutyListData22y.map((el, idnex1) => {
        // item.TagCode
        if (item.TagCode == el.KpiCode) {
          this.cardNum[index].DimensionList = el.kpiAnalyticalTimeDimensionDtos
          this.cardNum[index].DimensionList.map(el1 => {
            if (el1.TimeDimension == '1') {
              el1.TimeDimensionName = '年'
            }
            if (el1.TimeDimension == '2') {
              el1.TimeDimensionName = '季度'
            }
            if (el1.TimeDimension == '3') {
              el1.TimeDimensionName = '月'
            }
            if (el1.TimeDimension == '4') {
              el1.TimeDimensionName = '周'
            }
            if (el1.TimeDimension == '5') {
              el1.TimeDimensionName = '日'
            }
            if (el1.TimeDimension == '6') {
              el1.TimeDimensionName = '小时'
            }
            if (el1.TimeDimension == '7') {
              el1.TimeDimensionName = '分钟'
            }
            if (el1.TimeDimension == '8') {
              el1.TimeDimensionName = '秒'
            }

          })
        }
      })
    },
    DimensionChange1(item, index) {
      this.dutyListData22y.map((el, index1) => {
        // item.DataSource.TagCode
        if (item.DataSource.TagCode == el.KpiCode) {
          this.cardNum[0].tabCardNum[index].DimensionList1 = this.dutyListData22y[index1].kpiAnalyticalTimeDimensionDtos
          this.cardNum[0].tabCardNum[index].DimensionList1.map(el1 => {
            if (el1.TimeDimension == '1') {
              el1.TimeDimensionName = '年'
            }
            if (el1.TimeDimension == '2') {
              el1.TimeDimensionName = '季度'
            }
            if (el1.TimeDimension == '3') {
              el1.TimeDimensionName = '月'
            }
            if (el1.TimeDimension == '4') {
              el1.TimeDimensionName = '周'
            }
            if (el1.TimeDimension == '5') {
              el1.TimeDimensionName = '日'
            }
            if (el1.TimeDimension == '6') {
              el1.TimeDimensionName = '小时'
            }
            if (el1.TimeDimension == '7') {
              el1.TimeDimensionName = '分钟'
            }
            if (el1.TimeDimension == '8') {
              el1.TimeDimensionName = '秒'
            }
          })
        }
      })
    },
    tabNumChange(e, o) {
      this.tbIndex = o
      // this.tabFlag = true
      // if (this.tabFlag) {
      //   this.cardNum.map((item, index) => {
      //     for (let i = 0; i < parseFloat(e); i++) {
      //       item.tabCardNum.push(
      //         {
      //           tabChartIndex: i + 1,
      //           TargetColor: '',
      //           DataSource: '',
      //           TargetValue: '',
      //           exhibitionType: '',
      //           configFlag: '',
      //           dataFormat: '',
      //           dataSpan: '',
      //           ModularName: '',
      //           OlineType: '',
      //           Order: 'this.Moudelname-' + this.selectCode + '-left-' + (i + 1) + '-1-' + (i + 1),
      //           listData: [],
      //           style: {
      //             width: '',
      //             height: '',
      //             backgroundSize: '100%',
      //             padding: '10px',
      //             boxSizing: 'border-box',
      //             marginBottom: '16px',
      //             backgroundImage: "",
      //           },
      //         }
      //       )
      //     }
      //   })
      // } else {
      //   this.cardNum.map((item, index) => {
      //     // this.cardNum[o].Order = index + 1 + '-1'
      //     this.cardNum[0].tabCardNum = []
      //   })
      // }
      this.cardNum.map((item, index) => {
        this.cardNum[o].tabCardNum = []
        for (let i = 0; i < parseFloat(e); i++) {
          this.cardNum[o].tabCardNum.push(
            {
              isOpen: '',
              indexSort: i + 1,
              listParams: '',
              itemCode: '',
              routePage: '',
              colFlag: false,
              tableHeFlag: true,
              ajFlag: true,
              deailFileList: [],
              Dimension: '',
              DimensionList: [],
              tabChartIndex: i + 1,
              TargetColor: '',
              DataSource: '',
              TargetValue: '',
              exhibitionType: '',
              // configFlag: '',
              dataFormat: '',
              dataSpan: '',
              ModularName: '',
              OlineType: '',
              Order: this.Moudelname + '-' + this.selectCode + 'left-' + (this.cardNum[o].Order.split('-')[3]) + '-1-' + (i + 1),
              listData: [],
              style: {
                width: '',
                height: '',
                backgroundSize: '100%',
                padding: '10px',
                boxSizing: 'border-box',
                marginBottom: '5px',
                backgroundImage: "",
                tableHeight: 0
              },
            }
          )
        }
      })

    },
    addChange(item, index) {
      this.cardNum.push({
        style: {
          width: '',
          height: '',
          backgroundSize: '100%',
          padding: '10px',
          boxSizing: 'border-box',
          marginBottom: '5px',
          backgroundImage: "",
        },
        ModularName: '',
        OlineType: "bar",
        Order: this.Moudelname + '-' + this.selectCode + 'left-' + (index + 2) + '-1',
        listData: [],
        dataFormat: '',
        tabCardNum: [],
        DimensionList: [],
        dataSpan: '',
        // configFlag: '',
        exhibitionType: '',
        TargetValue: '',
        DataSource: '',
        TargetColor: '',
        Dimension: '',
        deailFileList: [],
      })
      this.form.RegionNum = this.cardNum.length
    },
    removeChange(item, index) {
      this.cardNum.splice(index, 1)
      this.form.RegionNum = this.cardNum.length
      this.cardNum.map((el, index1) => {
        el.Order = this.Moudelname + '-' + this.selectCode + 'left-' + (index1 + 1) + '-1'
        this.cardNum[el.Order.split('-')[3] - 1].tabCardNum.map((el1, index11) => {
          el1.Order = this.Moudelname + '-' + this.selectCode + 'left-' + (el.Order.split('-')[3]) + '-1-' + (index11 + 1)
        })
      })
    },
    addChangetab(item1, index1) {
      this.cardNum[item1.Order.split('-')[3] - 1].RegionNumTab = this.cardNum[item1.Order.split('-')[3] - 1].tabCardNum.length
      this.cardNum[item1.Order.split('-')[3] - 1].tabCardNum.push(
        {
          deailFileList: [],
          Dimension1: '',
          DimensionList1: [],
          tabChartIndex: index1 + 1,
          TargetColor: '',
          DataSource: '',
          TargetValue: '',
          exhibitionType: '',
          // configFlag: '',
          dataFormat: '',
          dataSpan: '',
          ModularName: '',
          OlineType: '',
          Order: this.Moudelname + '-' + this.selectCode + 'left-' + (item1.Order.split('-')[3]) + '-1-' + (parseFloat(item1.Order.split('-')[3]) + 1),
          listData: [],
          style: {
            width: '',
            height: '',
            backgroundSize: '100%',
            padding: '10px',
            boxSizing: 'border-box',
            marginBottom: '5px',
            backgroundImage: "",
            tableHeight: 0
          },
        }
      )




    },
    removeChangetab(item1, index1) {
      this.cardNum[item1.Order.split('-')[3] - 1].tabCardNum.splice(index1, 1)
      this.cardNum[item1.Order.split('-')[3] - 1].RegionNumTab = this.cardNum[item1.Order.split('-')[3] - 1].tabCardNum.length
      this.cardNum[item1.Order.split('-')[3] - 1].tabCardNum.map((el, index11) => {
        el.Order = this.Moudelname + '-' + this.selectCode + 'left-' + (item1.Order.split('-')[3]) + '-1-' + (index11 + 1)
      })

    },
    async save() {
      // this.querySelectList()
      if (this.dialogType == 'edit') {
        setTimeout(() => {
          this.querySelectList()
        }, 1000)
        // this.queryEditList()
        if (JSON.stringify(this.listData1) !== '{}') {
          this.cardNum.sort((a, b) => a.indexSort - b.indexSort);
          this.cardNum = JSON.parse(this.listData1.Leftjosn).listChart
          this.form.leftWidth = JSON.parse(this.listData1.Leftjosn).leftWidth
          this.form.leftHeight = JSON.parse(this.listData1.Leftjosn).leftHeight
          this.form.RegionNum = JSON.parse(this.listData1.Leftjosn).listChart.length
          this.xlNum = JSON.parse(this.listData1.Leftjosn).listChart.length
          // this.deailFileList1 = JSON.parse(this.listData1.Leftjosn).deailFileList || []
          if (JSON.parse(this.listData1.Leftjosn).img1) {
            this.form.img1 = JSON.parse(this.listData1.Leftjosn).img1
            const res2 = await getImageUel(JSON.parse(this.listData1.Leftjosn).img1);
            this.deailFileList1.push({
              name: JSON.parse(this.listData1.Leftjosn).img1,
              url: res2.response
            })
          }
          this.cardNum.map((item, index) => {
            this.dutyListData22y.map((el, idnex1) => {
              // item.TagCode
              if (item.TagCode == el.KpiCode) {
                this.cardNum[index].DimensionList = el.kpiAnalyticalTimeDimensionDtos
                this.cardNum[index].DimensionList.map(el1 => {
                  if (el1.TimeDimension == '1') {
                    el1.TimeDimensionName = '年'
                  }
                  if (el1.TimeDimension == '2') {
                    el1.TimeDimensionName = '季度'
                  }
                  if (el1.TimeDimension == '3') {
                    el1.TimeDimensionName = '月'
                  }
                  if (el1.TimeDimension == '4') {
                    el1.TimeDimensionName = '周'
                  }
                  if (el1.TimeDimension == '5') {
                    el1.TimeDimensionName = '日'
                  }
                  if (el1.TimeDimension == '6') {
                    el1.TimeDimensionName = '小时'
                  }
                  if (el1.TimeDimension == '7') {
                    el1.TimeDimensionName = '分钟'
                  }
                  if (el1.TimeDimension == '8') {
                    el1.TimeDimensionName = '秒'
                  }
                })
              }
              if (item.DataSource?.TagCode == el.KpiCode) {
                this.cardNum[index].DimensionList = el.kpiAnalyticalTimeDimensionDtos
                this.cardNum[index].DimensionList.map(el1 => {
                  if (el1.TimeDimension == '1') {
                    el1.TimeDimensionName = '年'
                  }
                  if (el1.TimeDimension == '2') {
                    el1.TimeDimensionName = '季度'
                  }
                  if (el1.TimeDimension == '3') {
                    el1.TimeDimensionName = '月'
                  }
                  if (el1.TimeDimension == '4') {
                    el1.TimeDimensionName = '周'
                  }
                  if (el1.TimeDimension == '5') {
                    el1.TimeDimensionName = '日'
                  }
                  if (el1.TimeDimension == '6') {
                    el1.TimeDimensionName = '小时'
                  }
                  if (el1.TimeDimension == '7') {
                    el1.TimeDimensionName = '分钟'
                  }
                  if (el1.TimeDimension == '8') {
                    el1.TimeDimensionName = '秒'
                  }
                })
              }
            })
          })
          this.cardNum[0].tabCardNum.map((item, index) => {
            this.dutyListData22y.map((el, index1) => {
              // (item.DataSource.TagCode
              if (item.DataSource.TagCode == el.KpiCode) {
                this.cardNum[0].tabCardNum[index].DimensionList1 = this.dutyListData22y[index1].kpiAnalyticalTimeDimensionDtos
                this.cardNum[0].tabCardNum[index].DimensionList1.map(el1 => {
                  if (el1.TimeDimension == '1') {
                    el1.TimeDimensionName = '年'
                  }
                  if (el1.TimeDimension == '2') {
                    el1.TimeDimensionName = '季度'
                  }
                  if (el1.TimeDimension == '3') {
                    el1.TimeDimensionName = '月'
                  }
                  if (el1.TimeDimension == '4') {
                    el1.TimeDimensionName = '周'
                  }
                  if (el1.TimeDimension == '5') {
                    el1.TimeDimensionName = '日'
                  }
                  if (el1.TimeDimension == '6') {
                    el1.TimeDimensionName = '小时'
                  }
                  if (el1.TimeDimension == '7') {
                    el1.TimeDimensionName = '分钟'
                  }
                  if (el1.TimeDimension == '8') {
                    el1.TimeDimensionName = '秒'
                  }
                })
              }
            })
          })

          this.cardNum.map(async (a, b) => {

            if (a.deailFileList.length > 0) {
              const resh3 = await getImageUel(a.deailFileList[0].name);
              a.deailFileList[0].push({
                name: a.deailFileList[0].name,
                url: resh3.response
              })

            }
            a.tabCardNum.map(async (a1, b1) => {
              const productionData = a.tabCardNum.filter(item => item.TabTitle === '生产');
              const equipmentData = a.tabCardNum.filter(item => item.TabTitle === '设备');
              const qualityData = a.tabCardNum.filter(item => item.TabTitle === '质量');
              const costData = a.tabCardNum.filter(item => item.TabTitle === '成本');

              a.tabCardNum = [
                ...productionData,
                ...equipmentData,
                ...qualityData,
                ...costData
              ];


              if (a1.deailFileList.length > 0) {
                const resh34 = await getImageUel(a1.deailFileList[0].name);
                a1.deailFileList[0].push({
                  name: a1.deailFileList[0].name,
                  url: resh34.response
                })
              }
            })
          })

          this.thrId = JSON.stringify(this.listData1).ID
          if (this.dialogType == 'edit')
            this.Pageid = JSON.stringify(this.listData1).Pageid
        }
      } else {
        this.cardNum.sort((a, b) => a.indexSort - b.indexSort);
        setTimeout(() => {
          this.querySelectList()
        }, 1000)
      }
      // this.querySelectList()
      this.cardNum.sort((a, b) => a.indexSort - b.indexSort);
      this.$emit('savesteupThr', this.cardNum, this.form, this.xlNum, this.thrId, this.deailFileList1, this.zlistData)
    },
    save1() {
      this.cardNum = []
      this.form.leftWidth = ''
      this.form.leftHeight = ''
      this.form.RegionNum = ''
      this.img1 = ''
      this.thrId = ''
      this.deailFileList1 = []
    },
    save3() {
      this.cardNum.sort((a, b) => a.indexSort - b.indexSort);
      this.$emit('savesteupThr', this.cardNum, this.form, this.xlNum, this.thrId, this.deailFileList1, this.zlistData, this.Pageid)
    }
  }
}
</script>
<style scoped>
.ExhibitionBox {
    width: 200px;
    height: 100px;
    border: 1px solid #ccc;
    display: flex;
    padding: 10px;
    box-sizing: border-box;
    margin-left: 30px;
    margin-top: -10px;
    gap: 10px;
}
.ExhibitionBox_l {
    flex: 1;
    border: 1px solid green;
    border-radius: 5px;
    padding: 3px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
    overflow-y: auto;
    justify-content: space-between;
}
.ExhibitionBox_c,
.ExhibitionBox_r {
    flex: 1;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 3px;
    box-sizing: border-box;
    overflow: hidden;
    overflow-y: auto;
}
.ExhibitionBox_l_n {
    width: 20px;
    height: 20px;
    border: 1px solid green;
    border-radius: 3px;
    text-align: center;
}
.showBox {
    width: 98%;
    margin: 0 auto;
    border-radius: 12px;
    box-shadow: 0px 0px 10px 0px #ccc;
    padding: 10px;
    box-sizing: border-box;
    margin-bottom: 15px;
}
</style>
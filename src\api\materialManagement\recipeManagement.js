import { getRequestResources } from '@/api/fetch';
import request from "@/util/request";
const baseURL = 'baseURL_DFM'
const baseURL1 = 'baseURL_30015'
export function addParameterList(data) {
    const api = '/api/ParameterTemplate/SaveListTemplate'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getParameterTemplate(data) {
    const api = '/api/ParameterTemplate/GetListTemplateList'
    return getRequestResources(baseURL, api, 'post', data);
}

export function SaveScoringTemplate(data) {
    const api = '/api/ParameterTemplate/SaveScoringTemplate'
    return getRequestResources(baseURL, api, 'post', data);
}
//

export function addSaveScoringTemplate(data) {
    const api = '/api/ParameterLimit/SaveParameterLimitData'
    return getRequestResources(baseURL, api, 'post', data);
}
// 数据字典DFMYesNo
export function getPARAMETER(data) {
    return request({
        url: baseURL + '/api/DataItemDetail/GetList',
        method: 'post',
        params: data
    });
}

export function getParameterTemplateD(data) {
    const api = '/api/ParameterTemplateD/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}

export function getScoringTemplateList(data) {
    const api = '/api/ParameterTemplate/GetScoringTemplateList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getParameterLimitList(data) {
    const api = '/api/ParameterLimit/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//查询树
export function GetRecipeTreeList(data) {
    const api = '/api/RecipeCommon/GetRecipeTreeList'
    return getRequestResources(baseURL, api, 'post', data);
}
//添加树
export function addTreeData(data) {
    const api = '/api/Recipe/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取Table
export function GetRecipeInfoList(data) {
    const api = '/api/RecipeCommon/GetRecipeInfoList'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取右侧弹窗Table
export function GetDrawerList(data) {
    const api = '/api/RecipeMapping/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除右侧弹窗Table数据
export function deleteDrawerList(data) {
    const api = '/api/RecipeMapping/delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//按产品组添加
export function addGroup(data) {
    const api = '/api/MaterialGroup/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//按产品添加
export function addMaterialProduct(data) {
    const api = '/api/Material/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//按版本添加
export function addMaterialVersion(data) {
    const api = '/api/MaterialVersion/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//保存产品关联
export function addMaterialSaveForm(data) {
    const api = '/api/RecipeMapping/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//添加树子集e
export function addNewRecipeSection(data) {
    // const api = '/api/RecipeCommon/NewRecipeSection'
    const api = '/api/RecipeCommon/NewRecipeSection'
    return getRequestResources(baseURL, api, 'post', data);
}
//修改树
export function editRecipeSection(data) {
    const api = '/api/RecipeCommon/EditRecipeSection'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除树
export function deleteRecipeSection(data) {
    const api = '/api/RecipeCommon/DeleteRecipeSection'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取SectionTable
export function getParameterGroupList(data) {
    const api = '/api/RecipeCommon/GetParameterGroupList'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取表格中的配方组
export function getRecipeParameterDefinitionList(data) {
    const api = '/api/RecipeCommon/GetRecipeParameterDefinitionList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增ParameterGroup
export function addParameterGroup(data) {
    const api = '/api/ParameterGroup/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//Machines”Tab页面
export function getParameterGroupEquipment(data) {
    const api = '/api/ParameterGroupEquipment/GetGroupEquipmentList'
    return getRequestResources(baseURL, api, 'post', data, true);
}
// 新增Machines”Tab页面
export function AddGroupEquipment(data) {
    const api = '/api/ParameterGroupEquipment/AddGroupEquipment'
    return getRequestResources(baseURL, api, 'post', data);
}
// 删除Machines”Tab页面
export function delGroupEquipment(data) {
    const api = '/api/ParameterGroupEquipment/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
// AddParameter
export function AddParameter(data) {
    const api = '/api/ParameterDefinition/AddParameter'
    return getRequestResources(baseURL, api, 'post', data);
}
// get Param entity
export function geParameterEntity(id) {
    const api = `/api/ParameterDefinition/GetEntity/${id}`
    return getRequestResources(baseURL, api, 'get');
}
// EditParameter
export function EditParameter(data) {
    const api = '/api/ParameterDefinition/EditParameter'
    return getRequestResources(baseURL, api, 'post', data);
}
// DeleteParam
export function DeleteParam(data) {
    const api = '/api/ParameterDefinition/DeleteParameter'
    return getRequestResources(baseURL, api, 'post', data);
}
// getUnitList
export function getUnitList() {
    const api = '/api/Unitmanage/GetList'
    return getRequestResources(baseURL, api, 'post');
}
export function getParamConfigtList(data) {
    const api = '/api/RecipeCommon/GetRecipeParameterConfigList'
    return getRequestResources(baseURL, api, 'post',data);
}
export function saveParamConfig(data) {
    const api = '/api/ParameterConfig/SaveForm'
    return getRequestResources(baseURL, api, 'post',data);
}


// addRecipeSectionCheckOut
export function newRecipeSectionCheckOut(data) {
    const api = '/api/RecipeCommon/RecipeSectionCheckOut'
    return getRequestResources(baseURL, api, 'post', data);
}
// 迁入
export function RecipeSectionCheckIn(data) {
    const api = '/api/RecipeCommon/RecipeSectionCheckIn'
    return getRequestResources(baseURL, api, 'post', data);
}
export function RecipeContextCheckIn(data) {
    const api = '/api/RecipeCommon/RecipeContextCheckIn'
    return getRequestResources(baseURL, api, 'post', data);
}

// 审批
export function RecipeSectionApprove(data) {
    const api = '/api/RecipeCommon/RecipeSectionApprove'
    return getRequestResources(baseURL, api, 'post', data);
}
export function RecipeContextApprove(data) {
    const api = '/api/RecipeCommon/RecipeContextApprove'
    return getRequestResources(baseURL, api, 'post', data);
}

//	Context签出签入审批
export function RecipeContextCheckOut(data) {
    const api = '/api/RecipeCommon/RecipeContextCheckOut'
    return getRequestResources(baseURL, api, 'post', data);
}
//	选择Section查看版本历史
export function getRecipeSectionHisList(data) {
    const api = '/api/RecipeCommon/GetRecipeSectionHisList'
    return getRequestResources(baseURL, api, 'post', data);
}
//	加载配方树，包含 配方，Section 产品组，产品，产品版本，工序
export function getRecipeTreeList(data) {
    const api = '/api/RecipeCommon/GetRecipeTreeList'
    return getRequestResources(baseURL, api, 'post', data);
}
//	选择Context查看版本历史
export function getRecipeContextHis(data) {
    const api = '/api/RecipeCommon/GetRecipeContextHisList'
    return getRequestResources(baseURL, api, 'post', data);
}
//	保存编辑Context版本历史
export function saveRecipeContextHis(data) {
    const api = '/api/RecipeContextVersion/EditRecipeContextVersion'
    return getRequestResources(baseURL, api, 'post', data);
}

//	查看配方详情
export function getRecipeEntity(id) {
    const api = `/api/Recipe/GetEntity/${id}`
    return getRequestResources(baseURL, api, 'get');
}
//	查看section详情
export function getSectionEntity(id) {
    const api = `/api/RecipeSectionVersion/GetEntity/${id}`
    return getRequestResources(baseURL, api, 'get');
}
export function getEquipmentList() {
    const api = `/api/Equipment/GetListByLevel?key=Unit`
    return getRequestResources(baseURL, api, 'post');
}

export function getRecipeCommon(data) {
    const api = `/api/RecipeCommon/GetRecipeTableData`
    return getRequestResources(baseURL, api, 'post',data);
}
//获取点位
export function getInfluxOpcTag(data) {
    const api = `/api/InfluxOpcTag/GetDistinctNameList`
    return getRequestResources(baseURL1, api, 'post',data);
}
//获取工单列表
export function getGetOrderText(data) {
    const api = `/ppm/ProductionOrder/GetOrderText`
    return getRequestResources(baseURL1, api, 'post',data);
}
//应用工单列表
export function setBindProcessMappingData(data) {
    const api = `/api/RecipeCommon/BindProcessMappingData`
    return getRequestResources(baseURL, api, 'post',data);
}
// 绑定长文本
export function BindProcessMappingTextData(data) {
    const api = `/api/RecipeCommon/BindProcessMappingTextData`
    return getRequestResources(baseURL, api, 'post',data);
}

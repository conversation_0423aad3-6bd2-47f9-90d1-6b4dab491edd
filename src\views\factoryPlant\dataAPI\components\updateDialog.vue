<template>
    <v-dialog v-model="updateDialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ operaObj.ID ? $t('GLOBAL._BJ'): $t('GLOBAL._XZ') }}
                <v-icon @click="updateDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" class="mt-8">
                    <v-row>      
                        <!-- 名称 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.ModelName" :rules="rules.ModelName" :label="$t('$vuetify.dataTable.DFM_DATAAPI.ModelName')" dense outlined></v-text-field>
                        </v-col>
                         <!-- 编号 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.ModelCode" :rules="rules.ModelCode" :label="$t('$vuetify.dataTable.DFM_DATAAPI.ModelCode')" dense outlined></v-text-field>
                        </v-col>
                         <!-- Sql -->
                        <v-col :cols="12" :lg="12">
                            <v-textarea v-model="form.Sql" :rules="rules.Sql" rows="4" label="Sql" dense outlined></v-textarea>
                        </v-col>
                         <!-- 备注 -->
                        <v-col :cols="12" :lg="12">
                            <v-textarea v-model="form.Description" rows="3" :label="$t('$vuetify.dataTable.DFM_DATAAPI.Description')" dense outlined></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn color="normal" @click="updateDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { DataApiSaveForm } from '@/api/factoryPlant/dataAPI.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            checkbox: true,
            valid: true,
            updateDialog: false,
            form: {
                ID: '',
                ModelCode: '',
                ModelName: '',
                Sql: '',
                Description: ''
            },
            rules: {
                ModelCode: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                ModelName: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                Sql: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            }
        };
    },
    watch: {
        updateDialog: {
            handler(curVal) {
                if(curVal) for (const key in this.form) {
                    if (Object.hasOwnProperty.call(this.form, key)) {
                        this.form[key] = this.operaObj[key];
                    }
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                const res = await DataApiSaveForm({...this.form });
                const { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    this.updateDialog = !this.checkbox;
                    this.$emit('handlePopup', 'refresh'); 
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>

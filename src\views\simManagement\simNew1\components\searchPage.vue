<template>
  <div style="display: flex;">
    <div
      v-for="(item,index) in abc"
      :key="index"
      style="flex:1;"
    >
      <a-input
        v-if="item.ConditionType.Encode == 'input'"
        v-model="item.ConditionType.Fullname"
        :placeholder="item.ConditionName"
      ></a-input>
      <!-- <a-select
        v-if="itemm.ConditionType.Encode == 'select'"
        style="width: 45%"
        allowClear
        v-model="item.ConditionType.Fullname"
        :placeholder="item.ConditionName"
      >
        <a-select-option
          v-for="item in dutyList"
          :key="item.Encode"
          :value="item.Fullname"
        >{{item.ItemName }}</a-select-option>
      </a-select> -->
      <v-select
        v-if="item.ConditionType.Encode == 'select'"
        v-model="item.ConditionType.Fullname"
        :items="dutyList"
        item-text="Fullname"
        item-value="Encode"
        return-object
        dense
        outlined
        :placeholder="item.ConditionName"
      >
      </v-select>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    Searchconfigs: {
      type: Array,
      default: () => []
    },
  },
  data: () => ({
    abc: [],
    dutyList: [
      {
        Fullname: 'input',
        Encode: 'input'
      },
      {
        Fullname: 'select',
        Encode: 'select'
      },
      {
        Fullname: 'date',
        Encode: 'date'
      },
    ],
    factory: '',
    withdrawTime: [],
    productseries: '',
    seriesList: []
  }),
  mounted() {
    this.abc = this.Searchconfigs
  },
  methods: {
    search() {
      this.$emit("searchClick")
    }
  },
}
</script>
<style scoped>
.searchBtn {
    width: 80px;
    height: 40px;
    line-height: 40px;
    border-radius: 12px;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    color: #fff;
    background-color: #3977e6;
}
/deep/ .v-select__selection {
    color: #fff !important;
}
/deep/ .v-input__slot {
    border: 1px solid #fff;
}
/deep/ .v-icon,
.notranslate,
.mdi,
.mdi-menu-down,
.theme--light {
    color: #fff;
}
</style>
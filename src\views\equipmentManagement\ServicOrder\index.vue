<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <!-- @click="RepastInfoGetPage" -->
                    <v-btn icon color="primary" @click="MyGetRepairServicePageList">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <!-- <v-btn color="primary" :disabled="!deleteList.length" @click="btnClickEvet('')">{{ $t('TPM_SBGL_FWDGL._yjqr') }}</v-btn>
                    <v-btn color="primary" :disabled="!deleteList.length" @click="btnClickEvet('')">{{ $t('TPM_SBGL_FWDGL._bmqr') }}</v-btn>
                    <v-btn color="primary" :disabled="!deleteList.length" @click="btnClickEvet('')">{{ $t('TPM_SBGL_FWDGL._gcqr') }}</v-btn> -->
                    <v-btn color="primary" @click="handleExport()">{{ $t('TPM_SBGL_FWDGL._DC') }}</v-btn>
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    ref="Tables"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_FWDGL"
                    :headers="ServicOrderColum"
                    :desserts="desserts"
                    @selectePages="selectePages"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"
                ></Tables>
                <createRepast ref="createRepast" :dialogType="dialogType" :tableItem="tableItem"></createRepast>
            </v-card>
            <el-drawer size="80%" :title="tableItem.DeviceCode + ' | ' + tableItem.RepairWo + ' | ' + tableItem.ServiceNo" :wrapperClosable="false" :visible.sync="detailShow" direction="rtl">
                <v-card class="ma-1">
                    <jytable ref="jytable" :SupplierServiceId="tableItem.ID"></jytable>
                </v-card>
            </el-drawer>
            <el-dialog :title="addTitle" :visible.sync="addModel" width="30%">
                <div class="addForm">
                    <v-text-field v-model="text" outlined dense :label="addText + '*'"></v-text-field>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="addModel = false">取 消</el-button>
                    <el-button type="primary" @click="Save()">确 定</el-button>
                </span>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';

import { GetRepairServicePageList, GetRepairServiceStatus, GetRepairServiceSaveForm } from '@/api/equipmentManagement/ServicOrder.js';
import { ServicOrderColum } from '@/columns/equipmentManagement/ServicOrder.js';
import { fwcgdownColum } from '@/columns/equipmentManagement/Repair.js';
import jytable from '@/views/equipmentManagement/ServicOrder/components/jytable.vue';
import { configUrl } from '@/config';
import { GetExportData } from '@/api/equipmentManagement/Equip.js';
import { Message, MessageBox } from 'element-ui';
import { GetPersonList } from '@/api/equipmentManagement/Equip.js';
export default {
    name: 'RepastModel',
    components: {
        jytable,
        createRepast: () => import('./components/createRepast.vue')
    },
    data() {
        return {
            // tree 字典数据
            addModel: false,
            addText: '',
            addTitle: '',
            text: '',
            detailShow: false,
            tab: null,
            fwcgdownColum,
            loading2: false,
            desserts2: [],
            loading: false,
            showFrom: false,
            papamstree: {
                RepairWo: '',
                ServiceNo: '',
                RequestBy: '',
                Status: '',
                DeviceCode: '',
                PurchasePoNo: '',
                RequestDateFrom: '',
                RequestDateTo: '',
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            ServicOrderColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            statusList: [],
            sqrList: []
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BH'),
                    code: 'bh',
                    type: 'primary',
                    showList: ['待审批'],
                    showKey: 'Status',
                    icon: '',
                    authCode: 'SBGLZY_FWDGL_BH'
                },
                {
                    text: this.$t('TPM_SBGL_FWDGL._CG'),
                    code: 'cg',
                    type: 'primary',
                    showList: ['待审批'],
                    showKey: 'Status',
                    icon: '',
                    authCode: 'SBGLZY_FWDGL_CG'
                },
                {
                    text: this.$t('TPM_SBGL_FWDGL._CD'),
                    code: 'cd',
                    type: 'primary',
                    showList: ['待出单'],
                    showKey: 'Status',
                    icon: '',
                    authCode: 'SBGLZY_FWDGL_CD'
                },
                {
                    text: this.$t('TPM_SBGL_FWDGL._YS'),
                    code: 'ys',
                    type: 'primary',
                    showList: ['服务中','已驳回'],
                    showKey: 'Status',
                    icon: '',
                    authCode: 'SBGLZY_FWDGL_YS'
                },
                {
                    text: this.$t('TPM_SBGL_FWDGL._yjqr'),
                    code: 'yjqr',
                    type: 'primary',
                    showList: ['待用家确认'],
                    showKey: 'Status',
                    icon: '',
                    authCode: ''
                },
                {
                    text: this.$t('TPM_SBGL_FWDGL._LDYS'),
                    code: 'ldys',
                    type: 'primary',
                    showList: ['待领导审批'],
                    showKey: 'Status',
                    icon: '',
                    authCode: ''
                }
            ];
        },
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'RepairWo',
                    label: this.$t('TPM_SBGL_FWDGL.wxdh'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'ServiceNo',
                    label: this.$t('TPM_SBGL_FWDGL.fwdh'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    byValue: 'ItemValue',
                    key: 'RequestBy',
                    label: this.$t('TPM_SBGL_FWDGL.sqr'),
                    icon: 'mdi-account-check',
                    selectData: this.sqrList,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Status',
                    label: this.$t('TPM_SBGL_FWDGL.zt'),
                    icon: 'mdi-account-check',
                    selectData: this.statusList,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'DeviceCode',
                    label: this.$t('TPM_SBGL_FWDGL.sbh'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'PurchasePoNo',
                    label: this.$t('TPM_SBGL_FWDGL.poh'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: new Date(new Date().getTime() - 6 * 24 * 60 * 60 * 1000).toISOString().substr(0, 10),
                    key: 'RequestDateFrom',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_FWDGL.sqkssj'),
                    placeholder: this.$t('TPM_SBGL_FWDGL.sqkssj')
                },
                {
                    value: new Date(Date.now() - new Date().getTimezoneOffset() * 60000).toISOString().substr(0, 10),
                    key: 'RequestDateTo',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_FWDGL.sqjssj'),
                    placeholder: this.$t('TPM_SBGL_FWDGL.sqjssj')
                }
            ];
        }
    },
    async mounted() {
        let MaintenanceGroup = await GetPersonList('MaintenanceGroup');
        let MaintenanceGroupData = MaintenanceGroup.response[0].ChildNodes;
        MaintenanceGroupData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.sqrList = MaintenanceGroupData;
        this.MyGetRepairServicePageList();
        this.getStatus();
    },
    methods: {
        //  查看BOM详情
        clickFun(data) {
            // this.tableItem = data;
            // this.detailShow = true;
            // setTimeout(() => {
            //     this.$refs.jytable.getData(this.tableItem);
            // }, 10);
        },
        async getStatus() {
            let res = await GetRepairServiceStatus();
            res.response.forEach(item => {
                item.ItemValue = item.value;
                item.ItemName = item.name;
            });
            this.statusList = res.response;
        },
        async handleExport() {
            let params = {
                ...this.papamstree
            };
            const baseUrl3 = configUrl[process.env.VUE_APP_SERVE]['baseURL_EQUIPMENT'] + `/api/RepairService/ExportData`;
            let res = await GetExportData(baseUrl3, params);
            let binaryData = [];
            binaryData.push(res);
            const url = window.URL.createObjectURL(new Blob(binaryData));
            console.log(url);
            const link = document.createElement('a');
            link.href = url;
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            let fileName = `服务单管理${formattedDateTime}.xlsx`;
            document.body.appendChild(link);
            link.setAttribute('download', fileName);
            link.click();
            window.URL.revokeObjectURL(link.href);
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.MyGetRepairServicePageList();
        },
        // 列表查询
        async MyGetRepairServicePageList() {
            let params = {
                ...this.papamstree
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading = true;
            const res = await GetRepairServicePageList(params);
            let { success, response } = res;
            for (let k in response.data) {
                let item = response.data[k];
                item.RequestByName = await this.$getPerson(item.RequestBy);
                this.statusList.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                        item.StatusValue = it.ItemValue;
                    }
                });
            }
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
            }
        },
        async Save() {
            if (this.text == '') {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            if (this.type == 'yjqr') {
                this.tableItem.UserConfirmResult = this.text;
            } else {
                this.tableItem.AcceptanceResult = this.text;
            }
            this.tableItem.Status = this.tableItem.StatusValue;
            let res = await GetRepairServiceSaveForm(this.tableItem);
            this.addModel = false;
            this.MyGetRepairServicePageList();
        },
        // 表单操作
        tableClick(item, type) {
            if (type == 'detail') {
                this.tableItem = item;
                this.detailShow = true;
                setTimeout(() => {
                    this.$refs.jytable.getData(this.tableItem);
                }, 10);
            } else {
                this.dialogType = type;
                this.tableItem = item;
                this.$refs.createRepast.load(item, type);
            }
            // if (type == 'yjqr' || type == 'ldys') {
            //    this.addText = type == 'yjqr' ? this.$t('TPM_SBGL_FWDGL.UserConfirmResult') : this.$t('TPM_SBGL_FWDGL.AcceptanceResult');
            //     this.addTitle = type == 'yjqr' ? this.$t('TPM_SBGL_FWDGL._yjqr') : this.$t('TPM_SBGL_FWDGL._LDYS');
            //     this.text = '';
            //     this.addModel = true;
            // } else {
            // }
            // this.$refs.createRepast.showDialog = true;
            // if(this.dialogType == 'cg'){
            //     this.$refs.createRepast.load(item);
            // }else{
            //     this.$refs.createRepast.clear();
            // }
            // if(this.dialogType == 'ys'){
            //     setTimeout(() => {
            //         this.$refs.jytable.getData(this.tableItem);
            //     }, 100);
            // }
        },
        // 删除列表
        SelectedItems(item) {
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}

.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

let myxdata = []
let xdata = []

export function getarealine(valuedata, mytitle, colorlist) {
    xdata = []
    for (let i = 0; i <= 24; i++) {
        let hour = ""
        if (i < 10) {
            i = `0${i}`
        }
        hour = `${i}:00`
        xdata.push(hour)
        if (i != 24) {
            for (let k = 1; k <= 59; k++) {
                let min = ""
                if (k < 10) {
                    k = `0${k}`
                }
                min = `${i}:${k}`
                xdata.push(min)
            }
        }
    }
    myxdata = xdata
    let option = {
        title: {
            text: mytitle,
            left: "center",
            textStyle: {
                fontSize: 16
            },
        },
        color: colorlist,
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xdata,
            axisTick: {
                show: false
            },
            axisLine: {
                show: false
            },
            axisLabel: {
                interval: 119,
                // color: "#fff"
            },
        },

        tooltip: {
            confine: true,
            trigger: 'axis',
            formatter: function (params) {
                let str = ""
                for (let i = 0; i < params.length; i++) {
                    if (params[i].data.value != null) {
                        if (params[i].seriesName == valuedata[i].name) {
                            str = `${params[i].marker}${params[i].seriesName}</br>`
                            valuedata[i].data.forEach((item, ind) => {
                                if (ind == params[i].data.name) {
                                    str += `<div>开始时间：${item.starttime} 结束时间：${item.overtime}</div>`
                                }
                            })
                        }
                    }
                }
                return str
            }
            // axisPointer: {
            //   type: 'cross',
            //   label: {
            //     backgroundColor: '#6a7985'
            //   }
            // }
        },

        legend: {
            show: true,
            icon: "roundRect",
            right: "15%",
            textStyle: {
                // color: "#fff"
            }
        },
        grid: {
            top: "25%",
            bottom: "25%",
            right: "5%",
            left: "5%"
        },
        yAxis: {
            type: 'value',
            axisTick: {
                show: false
            },
            axisLine: {
                show: false
            },
            axisLabel: {
                show: false
            },
            splitLine: {
                show: false

            },
        },
        series: [],
    };
    valuedata.forEach((item) => {
        let obj = {
            type: 'line',
            symbolSize: 0, //设定实心点的大小
            areaStyle: {
                opacity: 1
            },
            data: [],
            markLine: {},
            lineStyle: {}
        }
        obj.name = item.name
        let timedata = item.data
        for (let i = 0; i < xdata.length; i++) {
            obj.data[i] = {
                name: "",
                value: null
            }
            // obj.data[i] = null
        }
        timedata.forEach((it, ind) => {
            let start = getdateindex(myxdata, it.starttime)
            let end = getdateindex(myxdata, it.overtime)
            for (let i = start; i < end + 1; i++) {
                obj.data[i].value = 100
                obj.data[i].name = ind
            }
        })
        option.series.push(obj)
    })
    return option
}

export function getDateIndex(date) {
    return getdateindex(xdata, date)
}

function getdateindex(a, x) {
    var results = null,
        len = a.length,
        pos = 0;
    while (pos < len) {
        pos = a.indexOf(x, pos);
        if (pos === -1) { //未找到就退出循环完成搜索
            break;
        }
        results = pos; //找到就存储索引
        pos += 1; //并从下个位置开始搜索
    }
    return results;
}
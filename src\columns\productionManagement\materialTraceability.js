export const materialTraceabilityColum = [
    {
        label: '物料批号',
        prop: 'BatchNo'
    },
    {
        label: '工单号',
        prop: 'WoCode'
    },
    {
        label: '物料批次类型',
        width: 110,
        prop: 'Type'
    },
    {
        label: '测试等级',
        width: 90,
        prop: 'TestGrade'
    },
    {
        label: '物料编码',
        width: 110,
        prop: 'MaterialCode'
    },
    {
        label: '物料描述',
        prop: 'MaterialDescription'
    },
    {
        label: '批次数量',
        width: 90,
        prop: 'BatchQuantity',
        semicolonFormat: true
    },
    // {
    //     label: '占用数量',
    //     width: 100,
    //     prop: 'BindQuantity',
    //     semicolonFormat: true
    // },
    {
        label: '单位',
        width: 80,
        prop: 'Unit',
        semicolonFormat: true
    },
    {
        label: '批次创建时间',
        width: 160,
        prop: 'CreateDate'
    },
    {
        label: '上料时间',
        width: 160,
        prop: 'FeedingTime'
    }
];

import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';
let DFM = 'baseURL_DFM';


// 设备分类
export function GetDeviceCategoryTree(data) {
    const api = '/api/DeviceCategory/GetTree';
    return getRequestResources(baseURL, api, 'post', data, true);
}
export function GetSpotCheckItemPageList(data) {
    const api = '/api/SpotCheckItem/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}
export function ImportSpotCheckItemData(data, key) {
    const api = `/api/SpotCheckItem/ImportData?factory=${key}`;
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetSpotCheckItemExportData(data) {
    const api = '/api/SpotCheckItem/ExportData';
    return getRequestResources(baseURL, api, 'get', data);
}
export function GetSpotCheckItemGetFileUrl(data) {
    const api = '/api/SpotCheckItem/GetFileUrl';
    return getRequestResources(baseURL, api, 'get', data);
}
export function GetSpotCheckGetFileUrl(data) {
    const api = '/api/SpotCheckItem/GetFileUrl';
    return getRequestResources(baseURL, api, 'get', data);
}
export function GetSpotCheckItemSaveForm(data) {
    const api = '/api/SpotCheckItem/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetSpotCheckWoItemSaveForm(data) {
    const api = '/api/SpotCheckWoItem/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetSpotCheckWoItemUploadFile(data) {
    const api = '/api/SpotCheckWoItem/UploadFile';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetSpotCheckItemDelete(data) {
    const api = '/api/SpotCheckItem/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
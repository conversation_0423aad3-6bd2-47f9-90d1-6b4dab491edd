<template>
  <div>
    <a-button @click="saveClick" type="primary">Save</a-button>
    <div class="save-form">
      <div class="form">
        <el-form ref="dialogForm" :model="form" label-width="130px">
          <el-form-item label="Material Type" required>
            <el-select style="width: 100%" v-model="form.MaterialType" placeholder="请选择">
              <el-option v-for="(item, index) in materialTypeOptions" :key="index" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="form.MaterialType === '1'" label="SPA Storage Bin" required>
            <el-cascader v-model="equipmentRequirementIds" :options="SPAStorageBinOptions"
              :props="{ value: 'id', label: 'name' }" :show-all-levels="false" @change="changeCascader"></el-cascader>
          </el-form-item>
          <el-form-item label="Default" required>
            <el-switch v-model="form.IsDefault" active-color="#13ce66" inactive-color="#cccccc">
            </el-switch>
          </el-form-item>
          <el-form-item label="Incoming Mode" required>
            <el-select style="width: 100%" v-model="form.IncomingStorageMode" placeholder="请选择">
              <el-option v-for="(item, index) in incomingModeOptions" :key="index" :label="item.ItemName"
                :value="item.ItemValue">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="Outgoing Mode" required>
            <el-select style="width: 100%" v-model="form.OutgoingStorageMode" placeholder="请选择">
              <el-option v-for="(item, index) in outgoingModeOptions" :key="index" :label="item.ItemName"
                :value="item.ItemValue">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { getEquipmentRequirementTree, getEquipmentStorage, getDataDictionary, generalSaveForm } from "../service";
import {getLabelFormatList} from "@/api/systemManagement/labelPrint";
export default {
  name: 'general',
  props: {
    EquipmentId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {},
      // MaterialType: [],
      options: [],
      value: true,
      materialTypeOptions: [
        {
          label: 'SAP',
          value: '1'
        },
        {
          label: 'MES',
          value: '2'
        }
      ],
      SPAStorageBinOptions: [],
      incomingModeOptions: [],
      outgoingModeOptions: [],
      equipmentRequirementIds: []
    }
  },
  mounted() {
    this.form = {}
    this.equipmentRequirementIds = []
    this.getSPAStorageBin()
    // this.getGeneralData()
    this.getInComing()
    this.getOutGoing()
  },
  methods: {

    getSPAStorageBin() {
      getEquipmentRequirementTree(this.searchForm).then(res => {
        this.SPAStorageBinOptions = this.getTreeData(res.response)
        this.getGeneralData()
      })
      // const { response } = await getEquipmentRequirementTree({})
      // this.SPAStorageBinOptions = this.getTreeData(response)
    },
    async getGeneralData() {
      const { response } = await getEquipmentStorage({
        EquipmentId: this.EquipmentId
      })
      this.form = {
        ...response ? response : {},
        EquipmentId: this.EquipmentId,
        IsDefault: response?.IsDefault === '1' ? true : false
      }
      if (!response || response.MaterialType === '2') return
      this.equipmentRequirementIds = this.changeDetSelect(response.EquipmentRequirementId, this.SPAStorageBinOptions)
    },
    async getInComing() {
      const { response } = await getDataDictionary({
        itemCode: 'StorageInComingMode',
        pageIndex: 1,
        pageSize: 1000
      })
      this.incomingModeOptions = response.data
    },
    async getOutGoing() {
      const { response } = await getDataDictionary({
        itemCode: 'StorageOutGoingMode',
        pageIndex: 1,
        pageSize: 1000
      })
      this.outgoingModeOptions = response.data
    },
    // 递归处理子节点children为[]情况
    getTreeData(data) {
      if (!data.length) return
      for (let i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          data[i].children = void(0)
        } else {
          this.getTreeData(data[i].children)
        }
      }
      return data
    },
    // 处理回显
    changeDetSelect(key, treeData) {
      let arr = []
      let returnArr = []
      let depth = 0
      function childrenEach(childrenData, depthN) {
        for (var j = 0; j < childrenData.length; j++) {
          depth = depthN
          arr[depthN] = childrenData[j].id
          if (childrenData[j].id == key) {
            returnArr = arr.slice(0, depthN + 1)
            break
          } else {
            if (childrenData[j].children) {
              depth++
              childrenEach(childrenData[j].children, depth)
            }
          }
        }
        return returnArr;
      }
      return childrenEach(treeData, depth)
    },
    async saveClick() {
      if (this.form.MaterialType === '2' && this.form.EquipmentRequirementId !== void (0)) {
        delete this.form.EquipmentRequirementId
      }
      const { msg } = await generalSaveForm({
        ...this.form,
        IsDefault: this.form.IsDefault ? '1' : '0'
      })
      this.$message.success(msg)
      this.getGeneralData()
    },
    changeCascader() {
      if (!this.equipmentRequirementIds.length) {
        this.form.EquipmentRequirementId = ''
      } else {
        this.form.EquipmentRequirementId = this.equipmentRequirementIds[this.equipmentRequirementIds.length - 1]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.save-form {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;

  .form {
    margin-top: 50px;
    margin-left: 100px;
    width: 450px;

    .flex {
      display: flex;
      align-items: center;
      gap: 20px
    }
  }
}
</style>

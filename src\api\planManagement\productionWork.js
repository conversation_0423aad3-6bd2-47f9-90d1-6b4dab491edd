import { getRequestResources } from '@/api/fetch';
const baseURL3 = 'baseURL_30015'

// 生成大表
export function createPlanTable(data) {
  const api = '/ppm/Formulaschedule/CreatePlanTable'
  return getRequestResources(baseURL3, api, 'post', data);
}

// 初步排产
export function firstPlan(data) {
  const api = '/ppm/Formulaschedule/FirstPlan'
  return getRequestResources(baseURL3, api, 'post', data);
}

// 生成排产表ByDate
export function CreateScheduleByDate(data) {
  const api = '/ppm/Formulaschedule/CreateScheduleByDate'
  return getRequestResources(baseURL3, api, 'post', data);
}
// 生成排产表ByIds
export function CreateScheduleByIds(data) {
  const api = '/ppm/Formulaschedule/CreateScheduleByIds'
  return getRequestResources(baseURL3, api, 'post', data);
}

//获取排产表
export function getSchedulePageList(data) {
  const api = '/ppm/Formulaschedule/GetPageList'
  return getRequestResources(baseURL3, api, 'post', data);
}
//根据排产表生成工单
export function setFormulaschedule(data) {
  const api = '/ppm/Formulaschedule/CreatePlanOrderByID '
  return getRequestResources(baseURL3, api, 'post', data);
}

//获取制造工单列表
export function getProductionPageList(data) {
  const api = '/ppm/Formulaschedule/GetProductionOrderList'
  return getRequestResources(baseURL3, api, 'post', data);
}

//调整排产表
export function formulascheduleChange(data) {
  const api = '/ppm/Formulaschedule/FormulascheduleChange'
  return getRequestResources(baseURL3, api, 'post', data);
}
//根据配方ID生成制造工单
export function setCreatePlanOrderByID(data) {
  const api = '/ppm/Formulaschedule/CreatePlanOrderByID'
  return getRequestResources(baseURL3, api, 'post', data);
}
//修改制造工单
export function productionOrderChange(data) {
  const api = '/ppm/Formulaschedule/ProductionOrderChange'
  return getRequestResources(baseURL3, api, 'post', data);
}

export function generateWorkOrder(data) {
  const api = '/ppm/Formulaschedule/CreatePlanOrder'
  return getRequestResources(baseURL3, api, 'post', data);
}
//发布工单
export function publishWorkOrder(data) {
  const api = '/ppm/Formulaschedule/PublishOrder'
  return getRequestResources(baseURL3, api, 'post', data, false,true,true);
}

//取消工单
export function cancelWorkOrder(data) {
  const api = '/ppm/Formulaschedule/CancleProductionOrder'
  return getRequestResources(baseURL3, api, 'post', data);
}
//获取排序工单列表
export function getSortList(data) {
  const api = '/ppm/Formulaschedule/GetOrderToSortList'
  return getRequestResources(baseURL3, api, 'post', data);
}

//获取排序工单列表
export function saveSortList(data) {
  const api = '/ppm/Formulaschedule/SaveOrderSortList'
  return getRequestResources(baseURL3, api, 'post', data);
}

//获取节拍计算信息
export function getBeatInfo(data) {
  const api = '/ppm/Formulaschedule/GetBeatInfoList'
  return getRequestResources(baseURL3, api, 'post', data);
}

// 查询bom
export function getProductOrderBomByOrderId(data) {
  const api = '/ppm/Formulaschedule/GetProOrderBomByOrderId'
  return getRequestResources(baseURL3, api, 'post', data);
}

//创建生产工单
export function productionOrderAdd(data) {
  const api = '/ppm/Formulaschedule/CreateProductionOrder'
  return getRequestResources(baseURL3, api, 'post', data);
}
//获取工单添加喉头明细
export function getThroatDetail(data) {
  const api = '/ppm/Workorderthroat/GetList'
  return getRequestResources(baseURL3, api, 'post', data);
}
//上传SAP
export function setSendCkOrderToSap(data) {
  const api = '/ppm/Formulaschedule/SendCkOrderToSap'
  return getRequestResources(baseURL3, api, 'post', data);
}
//根据选择上传SAP
export function setSendCkOrderToSapByOrderId(data) {
  const api = '/ppm/Formulaschedule/SendCkOrderToSapByOrderId'
  return getRequestResources(baseURL3, api, 'post', data);
}
//忽略包装工单
export function setIgnorePackOrder(data) {
  const api = '/ppm/Formulaschedule/IgnorePackOrder'
  return getRequestResources(baseURL3, api, 'post', data);
}
//获取CIP列表
export function getProductionOrderCip(data) {
  const api = '/ppm/ProductionOrderCip/GetList'
  return getRequestResources(baseURL3, api, 'post', data);
}
//添加CIP列表
export function addProductionOrderCip(data) {
  const api = '/ppm/ProductionOrderCip/SaveForm'
  return getRequestResources(baseURL3, api, 'post', data);
}
//添加子工单喉头
export function addBatchOrderByParentId(data) {
  const api = '/ppm/Formulaschedule/CreateBatchOrderByParentId'
  return getRequestResources(baseURL3, api, 'post', data);
}
//删除CIP列表
export function delProductionOrderCip(data) {
  const api = '/ppm/ProductionOrderCip/Delete'
  return getRequestResources(baseURL3, api, 'post', data);
}
//CIP详情
export function getProductionOrderCipDetail(id) {
  const api = '/ppm/ProductionOrderCip/GetEntity/' + id
  return getRequestResources(baseURL3, api, 'get');
}
//同步SAP-BOM
export function upDateCKPoBomAndRouting(data) {
  const api = '/ppm/ProductionOrder/GetCKPoBomAndRouting'
  return getRequestResources(baseURL3, api, 'post', data);
}
//同步SAP-BOM
export function upDatePKPoBomAndRouting(data) {
  const api = '/ppm/ProductionOrder/GetPKPoBomAndRouting'
  return getRequestResources(baseURL3, api, 'post', data);
}


import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_TRACE'
// 列表
export function getSapSegmentEquipmentList(data) {
    const api =  '/api/SapSegmentEquipment/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取设备列表 
export function getSapSegmentEquipments(data) {
    const api =  '/api/SapSegmentEquipment/GetPhaseEquipmentList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取工序
export function getSapSegments(data) {
    const api =  '/api/SapSegment/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 新增 
export function saveSapSegmentEquipment(data) {
    const api =  '/api/SapSegmentEquipment/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}

// 删除 
export function deleteSapSegmentEquipment(data) {
    const api =  '/api/SapSegmentEquipment/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

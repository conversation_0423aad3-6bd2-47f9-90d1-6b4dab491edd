# 2级x轴刻度坐标功能更新日志

## 功能概述

实现了当 `GroupField` 包含 `"SpecificationCode"` 规格字段时，柱状图和折线图自动将 `ChartData` 结果转换成2级x轴刻度坐标的功能。

## 更新内容

### 1. 修改的组件

#### ✅ barChart.vue (柱状图组件)
- **文件路径**: `src/views/simManagement/simNew1/components/barChart.vue`
- **主要修改**:
  - 修改 `xAxisOption` 计算属性，添加2级x轴检测逻辑
  - 新增 `getTwoLevelXAxis()` 方法，生成2级x轴配置
  - 新增 `formatDateLabel()` 方法，格式化日期标签
  - 新增 `getSingleLevelXAxis()` 方法，保持原有单级x轴逻辑
  - 新增 `transformDataForTwoLevelAxis()` 方法，转换数据以适配2级x轴
  - 在数据处理部分添加2级x轴支持

#### ✅ lineChart.vue (折线图组件)
- **文件路径**: `src/views/simManagement/simNew1/components/lineChart.vue`
- **主要修改**:
  - 修改 `xAxisOption` 计算属性，添加2级x轴检测逻辑
  - 新增 `getTwoLevelXAxis()` 方法，生成2级x轴配置
  - 新增 `formatDateLabel()` 方法，格式化日期标签
  - 新增 `getSingleLevelXAxis()` 方法，保持原有单级x轴逻辑
  - 新增 `transformDataForTwoLevelAxis()` 方法，转换数据以适配2级x轴
  - 在数据处理部分添加2级x轴支持

#### ✅ barLine.vue (柱状+折线图组件)
- **文件路径**: `src/views/simManagement/simNew1/components/barLine.vue`
- **主要修改**:
  - 修改 `xAxisOption` 计算属性，添加2级x轴检测逻辑
  - 新增 `getTwoLevelXAxis()` 方法，生成2级x轴配置
  - 新增 `formatDateLabel()` 方法，格式化日期标签
  - 新增 `getSingleLevelXAxis()` 方法，保持原有单级x轴逻辑
  - 新增 `transformDataForTwoLevelAxis()` 方法，转换数据以适配2级x轴
  - 在数据处理部分添加2级x轴支持

### 2. 核心实现逻辑

#### 检测机制
```javascript
const hasSpecification = this.curShift?.GroupField && 
  this.curShift.GroupField.includes('SpecificationCode')
```

#### x轴配置转换
```javascript
if (hasSpecification) {
  return this.getTwoLevelXAxis()
} else {
  return this.getSingleLevelXAxis()
}
```

#### 数据转换
```javascript
data: hasSpecification ? 
  this.transformDataForTwoLevelAxis(key, this.curShift.ChartData[key]) : 
  this.curShift.ChartData[key]
```

### 3. 数据结构要求

#### 输入数据格式
```javascript
{
  GroupField: ["SpecificationCode", "Date"],
  x: ["2024-01-01", "2024-01-02", "2024-01-03"],
  "规格A_产量": [100, 120, 110],
  "规格A_质量": [95, 98, 96],
  "规格B_产量": [80, 85, 90],
  "规格B_质量": [92, 94, 93]
}
```

#### 转换后的x轴标签
```
规格A        规格A        规格A        规格B        规格B        规格B
01-01       01-02       01-03       01-01       01-02       01-03
```

### 4. 兼容性

- ✅ 向后兼容：当没有规格字段时，自动回退到原有的单级x轴模式
- ✅ 数据格式兼容：支持现有的数据结构，无需修改API
- ✅ 样式兼容：保持原有的图表样式和主题

### 5. 技术细节

#### 规格提取逻辑
- 从 `ChartData` 的键中提取规格信息
- 假设数据键格式为：`规格_指标名称`
- 自动过滤掉 `x`、`目标值`、`上限`、`下限` 等特殊键

#### 日期格式化
- 支持 `YYYY-MM-DD` 格式的日期
- 根据 `TimeDimension` 自动调整显示格式
- 日、周：显示为 `MM-DD`
- 月、季度：显示为 `YYYY-MM`

#### ECharts配置
- 使用换行符 `\n` 实现2级标签显示
- 调整字体大小和行高以适应2级显示
- 保持原有的颜色主题和样式

## 使用说明

### 1. 启用条件
确保后端返回的数据中 `GroupField` 包含 `"SpecificationCode"`

### 2. 数据准备
按照 `规格_指标名称` 的格式命名数据键

### 3. 自动转换
组件会自动检测并转换为2级x轴显示

## 注意事项

1. **性能考虑**: 大量规格和日期组合可能影响渲染性能
2. **数据完整性**: 确保所有规格的数据长度与日期数组长度一致
3. **样式调整**: 可能需要调整图表容器宽度以避免标签重叠

## 版本信息

- **实现日期**: 2025-07-09
- **影响范围**: 图表组件显示逻辑
- **兼容性**: 完全向后兼容

<!-- eslint-disable vue/valid-v-slot -->
<template>
    <v-card>
        <v-card-title class="headline primary lighten-2" primary-title>{{ $t('DFM_RL._SZRL') }}</v-card-title>
        <v-card-text>
            <v-stepper :value="step">
                <v-stepper-header>
                    <v-stepper-step step="1" :complete="step > 1">{{ $t('DFM_RL._JCXX') }}</v-stepper-step>
                    <v-divider></v-divider>
                    <v-stepper-step step="2" :complete="step > 2">{{ $t('DFM_RL._BCSZ') }}</v-stepper-step>
                    <v-divider></v-divider>
                    <v-stepper-step step="3" :complete="step > 3">{{ $t('DFM_RL._SLBZ') }}</v-stepper-step>
                    <v-divider></v-divider>
                    <v-stepper-step step="4" :complete="step > 4">{{ $t('DFM_RL._XLBZ') }}</v-stepper-step>
                </v-stepper-header>
            </v-stepper>

            <div class="content mt-5 px-5" style="height: 300px">
                <div class="step-1" v-if="step == 1">
                    <v-form v-model="valid" ref="form">
                        <v-row>
                            <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                                <v-text-field :label="currentDeparment.value" disabled dense outlined
                                    :value="currentDeparment.name"></v-text-field>
                            </v-col>

                            <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                                <v-menu ref="menu0" v-model="menu0" :close-on-content-click="false"
                                    transition="scale-transition" offset-y max-width="290px" min-width="290px">
                                    <template #activator="{ on, attrs }">
                                        <v-text-field required :rules="[v => !!v || $t('GLOBAL._MANDATORY')]"
                                            v-model="form.month" :label="$t('DFM_RL._YF')" persistent-hint outlined
                                            dense v-bind="attrs" v-on="on"></v-text-field>
                                    </template>
                                    <v-date-picker type="month" :locale="locale" v-model="form.month" no-title
                                        @input="menu0 = false"></v-date-picker>
                                </v-menu>
                            </v-col>
                            <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                                <v-select :items="modeList" item-text="Name" item-value="ID"
                                    :no-data-text="$t('DFM_RL._ZWSJ')" clearable dense v-model="form.class" outlined
                                    required :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" @change="handleChangeMode"
                                    :label="$t('DFM_RL._XZBZ')" />
                            </v-col>
                            <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                                <v-menu ref="menu1" v-model="menu1" :close-on-content-click="false"
                                    transition="scale-transition" offset-y max-width="290px" min-width="290px">
                                    <template #activator="{ on, attrs }">
                                        <v-text-field v-model="form.Starttime" required
                                            :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" :label="$t('DFM_RL._KSRQ')"
                                            persistent-hint outlined dense readonly v-bind="attrs" v-on="on">
                                        </v-text-field>
                                    </template>
                                    <v-date-picker :locale="locale" v-model="form.Starttime" no-title
                                        @input="menu1 = false"></v-date-picker>
                                </v-menu>
                            </v-col>
                            <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                                <v-menu ref="menu2" v-model="menu2" :close-on-content-click="false"
                                    transition="scale-transition" offset-y max-width="290px" min-width="290px">
                                    <template #activator="{ on, attrs }">
                                        <v-text-field v-model="form.Endtime" required
                                            :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" :label="$t('DFM_RL._JSRQ')"
                                            persistent-hint outlined dense readonly v-bind="attrs" v-on="on">
                                        </v-text-field>
                                    </template>
                                    <v-date-picker :locale="locale" v-model="form.Endtime" no-title
                                        @input="menu2 = false"></v-date-picker>
                                </v-menu>
                            </v-col>
                            <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                                <v-text-field :label="$t('DFM_RL._DBZQ')" required
                                    :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" dense outlined v-model="form.Cycles">
                                </v-text-field>
                            </v-col>
                        </v-row>
                    </v-form>
                </div>

                <div class="step-2" v-if="step == 2">
                    <v-row>
                        <v-col :cols="12" :lg="4" class="pt-0 pb-0">
                            <v-select :items="classList" item-text="Name" item-value="ID"
                                :no-data-text="$t('DFM_RL._ZWSJ')" clearable dense v-model="classes" outlined multiple
                                @change="handleChangeClass" :label="$t('DFM_RL._XZBC')" />
                        </v-col>
                        <v-col :cols="12" :lg="4" class="pt-0 pb-0">
                            <v-text-field :label="$t('DFM_RL._BCS')" disabled dense outlined v-model="classesNum">
                            </v-text-field>
                        </v-col>
                    </v-row>

                    <v-data-table :items-per-page='999' :headers="headers" hide-default-footer dense height="200"
                        :items="selectClassList">
                        <template #item.time="{ item }">
                            <v-row>
                                <v-col :cols="12" :lg="4">
                                    <v-menu :ref="item.ID" v-model="item.menu" :close-on-content-click="false"
                                        :nudge-right="40" :return-value.sync="item.STARTTIME"
                                        transition="scale-transition" offset-y max-width="290px" min-width="290px">
                                        <template #activator="{ on, attrs }">
                                            <v-text-field v-model="item.STARTTIME" :label="$t('DFM_RL._KSSJ')" readonly
                                                v-bind="attrs" v-on="on"></v-text-field>
                                        </template>
                                        <v-time-picker v-if="item.menu" v-model="item.STARTTIME" full-width
                                            @click:minute="$refs[item.ID].save(item.STARTTIME)"></v-time-picker>
                                    </v-menu>
                                </v-col>
                                <v-col :cols="12" :lg="4">
                                    <v-menu :ref="item.ID + '1'" v-model="item.menu1" :close-on-content-click="false"
                                        :nudge-right="40" :return-value.sync="item.ENDTIME"
                                        transition="scale-transition" offset-y max-width="290px" min-width="290px">
                                        <template #activator="{ on, attrs }">
                                            <v-text-field v-model="item.ENDTIME" :label="$t('DFM_RL._JSSJ')" readonly
                                                v-bind="attrs" v-on="on"></v-text-field>
                                        </template>
                                        <v-time-picker v-if="item.menu1" v-model="item.ENDTIME" full-width
                                            @click:minute="$refs[item.ID + '1'].save(item.ENDTIME)"></v-time-picker>
                                    </v-menu>
                                </v-col>
                            </v-row>
                        </template>
                    </v-data-table>
                </div>
                <div class="step-3" v-if="step == 3">
                    <v-row>
                        <v-col :cols="12" :lg="4" class="pt-0 pb-0">
                            <v-select :items="teamList" multiple @change="handleChangeFirstTeam" item-text="Name"
                                item-value="ID" :no-data-text="$t('DFM_RL._ZWSJ')" clearable dense v-model="team"
                                outlined :label="$t('DFM_RL._XZBZU')" />
                        </v-col>
                        <v-col :cols="12" :lg="4" class="pt-0 pb-0">
                            <v-text-field :label="$t('DFM_RL._BZS')" disabled dense outlined v-model="teamNum">
                            </v-text-field>
                        </v-col>
                    </v-row>
                    <v-data-table :items-per-page='999' :headers="teamHeaders" hide-default-footer dense height="200"
                        :items="FirstTeamTable">
                    </v-data-table>
                </div>
                <div class="step-4" v-if="step == 4">
                    <v-row>
                        <v-col :cols="12" :lg="4" class="pt-0 pb-0">
                            <v-select :items="teamList" item-text="Name" item-value="ID"
                                :no-data-text="$t('DFM_RL._ZWSJ')" clearable dense v-model="nextTeam" outlined
                                @change="handleChangeNextTeam" multiple :label="$t('DFM_RL._XZBZU')" />
                        </v-col>
                        <v-col :cols="12" :lg="4" class="pt-0 pb-0">
                            <v-text-field :label="$t('DFM_RL._BZS')" disabled dense outlined v-model="nextTeamNum">
                            </v-text-field>
                        </v-col>
                    </v-row>
                    <v-data-table :items-per-page='999' :headers="teamHeaders" hide-default-footer dense height="200"
                        :items="NextTeamTable">
                    </v-data-table>
                </div>
            </div>
        </v-card-text>

        <v-divider></v-divider>

        <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="normal" @click="lastStep">{{ $t('DFM_RL._SYB') }}</v-btn>
            <v-btn color="normal" @click="nextStep">{{ $t('DFM_RL._XYB') }}</v-btn>
            <v-btn color="primary" @click="submitForm">{{ $t('DFM_RL._WC') }}</v-btn>
            <v-btn class="white--text" color="#9E9E9E" @click="closePopup">{{ $t('DFM_RL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { getModeList, getClassesList, getTeamList, saveCalendar } from '../service';
export default {
    props: {
        currentDeparment: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            menu4: false,
            modeList: [],
            desserts: [],
            menu1: false,
            menu2: false,
            menu0: false,
            form: {},
            step: 1,
            classList: [],
            selectClassList: [],
            classes: '',
            classesNum: 2,
            teamList: [],
            team: '',
            nextTeam: '',
            teamNum: 3,
            FirstTeamTable: [],
            NextTeamTable: [],
            nextTeamNum: 4,
            valid: true
        };
    },
    computed: {
        locale() {
            return this.$store.state.app.locale || 'zh';
        },
        headers() {
            return [
                { text: this.$t('DFM_RL._BC'), value: 'Name', width: 100 },
                { text: this.$t('DFM_RL._BCSJ'), value: 'time', width: 150 }
            ];
        },
        teamHeaders() {
            return [
                { text: this.$t('DFM_RL._MC'), value: 'Name', width: 100 },
                { text: this.$t('DFM_RL._JC'), value: 'Shortname', width: 100 }
                // { text: '工作班组', value: 'theSerialNumber', width: 150 }
            ];
        }
    },
    created() {
        this.getModeList();
        this.getClassList();
        this.getTeamList();
    },
    methods: {
        async submitForm() {
            if (this.step == 1 && !this.$refs.form.validate()) return false;

            let resp = await saveCalendar({
                ...this.form,
                Departmentid: this.currentDeparment.id,
                ShiftList: this.selectClassList,
                FirstTeamList: this.FirstTeamTable,
                NextTeamList: this.NextTeamTable,
                Shiftid: '33131',
                Teamid: '1212'
            });
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
            this.$emit('getdata');
            this.closePopup();
        },
        handleChangeNextTeam(vals) {
            if (vals.length && vals.length > this.nextTeamNum) {
                this.nextTeam = vals.slice(0, this.nextTeamNum);
                return false;
            }
            let slen = this.NextTeamTable.length;
            let vlen = vals.length;
            if (vlen === 0) {
                this.NextTeamTable = [];
            } else if (slen > vlen) {
                this.NextTeamTable = this.NextTeamTable.filter(item => vals.indexOf(item.ID) !== -1);
            } else if (slen < vlen) {
                let data = this.teamList.find(item => item.ID === vals[vlen - 1]);
                this.NextTeamTable.push(data);
            }
        },
        handleChangeFirstTeam(vals) {
            if (vals.length && vals.length > this.teamNum) {
                this.team = vals.slice(0, this.teamNum);
                return false;
            }
            let slen = this.FirstTeamTable.length;
            let vlen = vals.length;
            if (vlen === 0) {
                this.FirstTeamTable = [];
            } else if (slen > vlen) {
                this.FirstTeamTable = this.FirstTeamTable.filter(item => vals.indexOf(item.ID) !== -1);
            } else if (slen < vlen) {
                let data = this.teamList.find(item => item.ID === vals[vlen - 1]);
                this.FirstTeamTable.push(data);
            }
        },
        handleChangeClass(vals) {
            if (vals.length && vals.length > this.classesNum) {
                this.classes = vals.slice(0, this.classesNum);
                return false;
            }
            let slen = this.selectClassList.length;
            let vlen = vals.length;
            if (vlen === 0) {
                this.selectClassList = [];
            } else if (slen > vlen) {
                this.selectClassList = this.selectClassList.filter(item => vals.indexOf(item.ID) !== -1);
            } else if (slen < vlen) {
                let data = this.classList.find(item => item.ID === vals[vlen - 1]);
                this.selectClassList.push(data);
            }
        },
        handleChangeMode(val) {
            let mode = this.modeList.filter(item => item.ID === val);
            this.classesNum = mode[0].Shifts;
            this.teamNum = mode[0].Teams;
            this.nextTeamNum = mode[0].Teams;
        },
        async getClassList() {
            let resp = await getClassesList({ key: '' });
            this.classList = resp.response;
        },
        async getModeList() {
            let resp = await getModeList({ key: '' });
            this.modeList = resp.response;
        },
        async getTeamList() {
            let resp = await getTeamList({ key: '', modelid: this.currentDeparment.id });
            this.teamList = resp.response;
        },
        closePopup() {
            this.$emit('handlePopup', false, 'set');
        },
        lastStep() {
            if (this.step == 1) return false;
            this.step--;
        },
        nextStep() {
            if (!this.valid && this.step == 1) {
                this.$refs.form.validate();
                return false;
            }
            if (this.step == 4) return false;
            this.step++;
        }
    }
};
</script>

<style lang="scss" scoped>
.v-sheet.v-stepper:not(.v-sheet--outlined) {
    box-shadow: none;
}
</style>
<template>
    <el-dialog :title="dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')" :visible.sync="dialogVisible" width="700px"
      :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
      @close="dialogVisible = false">
      <el-form ref="dialogForm" :model="dialogForm" label-width="130px">
       
          <el-col :lg="12">
            <el-form-item label="产线" prop="LindCode">
              <el-select filterable clearable style="width: 100%" v-model="dialogForm.LindCode" placeholder="请选择" @change="setFormLineName">
                    <el-option v-for="item in lineOptions" :key="item.EquipmentCode" :label="item.EquipmentName" :value="item.EquipmentCode">
                    </el-option>
                </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="物料">
              <div>
                <el-button icon="el-icon-plus" type="text" @click="openMaterialTable">{{ $t('GLOBAL._CX') }}</el-button>
              </div>
              <div v-if="dialogForm&&dialogForm.MaterialCode">
                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}
              </div>
            </el-form-item>
          </el-col>          

          <el-col :lg="12">
            <el-form-item label="对应关系" prop="Type">
              <el-select v-model="dialogForm.Type" placeholder="请选择对应关系" clearable style="width:100%">
                <el-option v-for="item in typeOptions" :key="item.ItemValue" :label="item.ItemName" :value="item.ItemValue" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="第一批用时" prop="FirstLotPeriod">
              <el-input v-model="dialogForm.FirstLotPeriod" placeholder="请输入第一批用时" />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="中间批用时" prop="MiddleLotPeriod">
              <el-input v-model="dialogForm.MiddleLotPeriod" placeholder="请输入中间批用时" />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="最后一批用时" prop="LastLotPeriod">
              <el-input v-model="dialogForm.LastLotPeriod" placeholder="请输入最后一批用时" />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="批次量" prop="PlanQuantity">
              <el-input v-model="dialogForm.PlanQuantity" placeholder="请输入批次量" />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="最小成批量" prop="MinLotQuantity">
              <el-input v-model="dialogForm.MinLotQuantity" placeholder="请输入最小成批量" />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="最大成批量" prop="MaxLotQuantity">
              <el-input v-model="dialogForm.MaxLotQuantity" placeholder="请输入最大成批量" />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="备注" prop="Remark">
              <el-input v-model="dialogForm.Remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small"
          @click="submit()">确定
        </el-button>
      </div>
      <material-table :is-id="false" ref="materialTable" @saveForm="setMaterial"></material-table>
    </el-dialog>
  </template>

<script>
  import {
    getStandardPeriodLotDetail,
    saveStandardPeriodLotForm,
    getLineList
  } from "@/api/planManagement/standardPeriodLot";
  import MaterialTable from '@/components/MaterialTable.vue';
  export default {
    components:{
      MaterialTable
    },
    data() {
      return {
        dialogForm: {},
        dialogVisible: false,
        formLoading: false,
        lineOptions: [],
        currentRow: {},
      }
    },
    mounted() {
    },
    created() {
      this.initDictList();
      this.getLineList();
    },
    methods: {
      submit() {
        saveStandardPeriodLotForm(this.dialogForm).then(res=>{
          this.$message.success(res.msg)
          this.$emit('saveForm')
          this.dialogVisible = false
        })
      },
      async initDictList(){
        this.typeOptions = await this.$getDataDictionary('StandardPeriodType')    
      },
      show(data) {
        this.dialogForm = {}
        this.currentRow = data
        this.dialogVisible = true
        this.$nextTick(_ => {
          if(data.ID){
            this.getDialogDetail(data.ID)
          }
        })

      },
      getDialogDetail(id){
        getStandardPeriodLotDetail(id).then(res => {
          this.dialogForm = res.response
        })
      },
      async getLineList() {
        const { response } = await getLineList({
         //areaCode: 'PackingArea'
         areaCode: 'Formulation'
        })
        console.log(response)
        this.lineOptions = response
      },
      setFormLineName(EquipmentCode) {
        //console.log(EquipmentCode);
        //console.log(this.lineOptions.find(e => e.EquipmentCode === EquipmentCode));
        this.dialogForm.LineId = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).ID
        //this.dialogForm.LineName = this.lineOptions.find(e => e.ID === id).EquipmentName
        this.dialogForm.LineCode = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).EquipmentCode
        //console.log(this.dialogForm);
      },
      setMaterial(val){
        // console.log("setMaterial")
        // console.log(val)
        this.dialogForm.MaterialId = val.ID
        this.dialogForm.MaterialCode = val.Code
        this.dialogForm.MaterialName = val.NAME
        this.$forceUpdate()
        // this.matInfo = val        
        // console.log(this.dialogForm.MaterialCode)
        // console.log(this.dialogForm.MaterialName)
      },
      openMaterialTable(){
        this.$refs['materialTable'].show()
      }
    }
  }
  </script>
<template>
    <v-card>
        <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
            {{ operaObj.ID ? $t('DFM_WLBOMMX.editBOMDetail') : $t('DFM_WLBOMMX.addBOMDetail') }}
            <v-icon @click="closePopup">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid" class="mt-8">
                <v-row>
                    <v-col :cols="12" :lg="6">
                        <!-- label="上级物料号" -->
                        <!-- <v-select
                            v-model="form.Parent"
                            :items="parentMaterialList"
                            item-text="NAME"
                            item-value="Code"
                            :label="$t('$vuetify.dataTable.DFM_WLBOMMX.ParentName')"
                            persistent-hint
                            return-object
                            dense
                            outlined
                            @change="handleChange"
                        ></v-select> -->
                        <v-autocomplete
                            v-model="form.Parent"
                            :items="parentMaterialList"
                            item-text="NAME"
                            item-value="Code"
                            :label="$t('$vuetify.dataTable.DFM_WLBOMMX.ParentName')"
                            persistent-hint
                            dense
                            outlined
                            clearable
                            return-object
                            @change="handleChange"
                        ></v-autocomplete>
                    </v-col>
                    <!-- label="子物料号" -->
                    <v-col :cols="12" :lg="6">
                        <v-autocomplete
                            v-model="form.Component"
                            :rules="rules.CompoentCode"
                            :items="formSelectList"
                            item-text="NAME"
                            item-value="Code"
                            :label="$t('$vuetify.dataTable.DFM_WLBOMMX.CompoentName')"
                            required
                            persistent-hint
                            dense
                            outlined
                            @update:search-input="asyncUpdate"
                            clearable
                            return-object
                            @change="v => changeVal(v, 'MaterialGroup')"
                        ></v-autocomplete>
                    </v-col>
                    <!-- label="上级物料数量" -->
                    <v-col :cols="12" :lg="6">
                        <v-text-field v-model="form.Conversionrate" :label="$t('$vuetify.dataTable.DFM_WLBOMMX.Conversionrate')" required dense outlined></v-text-field>
                    </v-col>
                    <!-- label="上级物料数量" -->
                    <v-col :cols="12" :lg="6">
                        <v-text-field v-model="form.ParentQuantity" :label="$t('$vuetify.dataTable.DFM_WLBOMMX.ParentQuantity')" required dense outlined></v-text-field>
                    </v-col>
                    <!-- label="子物料数量" -->
                    <v-col :cols="12" :lg="6">
                        <v-text-field v-model="form.CompoentQuantity" :label="$t('$vuetify.dataTable.DFM_WLBOMMX.CompoentQuantity')" required dense outlined></v-text-field>
                    </v-col>
                    <!-- label="父级单位" -->
                    <v-col :cols="12" :lg="6">
                        <v-select
                            v-model="form.ParentUom"
                            :items="unitList"
                            item-text="Name"
                            item-value="Name"
                            no-data-text="暂无数据"
                            clearable
                            dense
                            required
                            outlined
                            :label="$t('$vuetify.dataTable.DFM_WLBOMMX.ParentUom')"
                        />
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <!-- label="子级单位" -->
                        <v-select
                            v-model="form.CompoentUom"
                            :items="unitList"
                            item-text="Name"
                            item-value="Name"
                            no-data-text="暂无数据"
                            clearable
                            dense
                            required
                            outlined
                            :label="$t('$vuetify.dataTable.DFM_WLBOMMX.CompoentUom')"
                        />
                    </v-col>
                    <v-col v-if="form.ID" :cols="12" :lg="6">
                        <v-text-field v-model="form.BomLevel" :label="$t('$vuetify.dataTable.DFM_WLBOMMX.Level')" required dense outlined></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <!-- label="物料组" -->
                        <v-text-field v-model="form.MaterialGroupName" disabled :label="$t('$vuetify.dataTable.DFM_WLBOMMX.MaterialGroup')" dense outlined></v-text-field>
                    </v-col>

                    <v-col :cols="12" :lg="12">
                        <!-- label="备注" -->
                        <v-textarea v-model="form.Remark" :label="$t('$vuetify.dataTable.DFM_WLBOMMX.Remark')" :rows="2" dense outlined></v-textarea>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>

        <v-divider></v-divider>

        <v-card-actions>
            <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._QX') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { saveDetailForm, getMaterialTopN, getParentMaterial } from '@/api/factoryPlant/materialBOM.js';
import { finishedProductNo, childMaterialNo } from '@/util/publicConstants.js';
export default {
    props: {
        unitList: {
            type: Array,
            default: () => []
        },
        operaObj: {
            type: Object,
            default: () => {}
        },
        currentSelectId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            checkbox: true,
            dialog: false,
            form: {
                ID: '',
                Parent: '',
                Component: '',
                ParentCode: '',
                ParentName: '',
                CompoentCode: '',
                CompoentName: '',
                Quantity: '',
                CompoentUom: '',
                ParentUom: '',
                ParentId: '',
                Remark: '',
                ParentQuantity: '',
                CompoentQuantity: '',
                BomLevel: '',
                MaterialGroup: '',
                MaterialGroupName: '',
                Conversionrate: ''
            },
            rules: {
                CompoentCode: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            },
            startTimeMenu: false,
            endTimeMenu: false,
            valid: true,
            finishedProductNo,
            childMaterialNo,
            timer: null,
            formSelectList: [],
            parentMaterialList: [],
            dataList: []
        };
    },
    async created() {
        await this.getParentMaterial();
        if (this.operaObj && this.operaObj.ID) {
            for (const key in this.form) {
                if (Object.hasOwnProperty.call(this.form, key)) {
                    if (this.operaObj[key]) {
                        this.form[key] = this.operaObj[key];
                    }
                }
            }
            await this.getMaterialList(this.form.CompoentCode);
            this.form.Parent = this.parentMaterialList.find(item => item.Code == this.form.ParentCode);
            const { ParentId } = this.form.Parent || {};
            this.form.ParentId = ParentId;
            this.form.Component = this.formSelectList.find(item => item.Code == this.form.CompoentCode);
            this.form.MaterialGroupName = this.form.Component.CategoryName;
        }
    },
    methods: {
        async getParentMaterial() {
            let resp = await getParentMaterial({ materialBomId: this.currentSelectId });
            this.parentMaterialList = resp.response;
        },
        asyncUpdate(val) {
            if (this.timer) clearTimeout(this.timer);
            this.timer = setTimeout(() => {
                this.getMaterialList(val);
            }, 500);
        },
        async getMaterialList(val = '') {
            let resp = await getMaterialTopN({
                key: val,
                Count: 100
            });
            const { response } = resp || {}
            response && response.forEach(e => {
                if(e.Code != this.form.ParentCode){
                    this.dataList.push(e)
                    this.formSelectList.push(e)
                }
            });
        },
        // 表单提交
        async submitForm() {
            this.form.MaterialBomId = this.currentSelectId;
            if (this.$refs.form.validate()) {
                const form = JSON.parse(JSON.stringify(this.form));
                delete form.Parent;
                delete form.Component;
                delete form.MaterialGroupName;
                const res = await saveDetailForm(form);
                const { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    if (this.operaObj.ID || this.checkbox) {
                        this.$emit('handlePopup', 'refresh');
                    } else {
                        this.$refs.form.reset();
                    }
                }
            }
        },
        closePopup() {
            this.$emit('handlePopup', 'close');
        },
        handleChange(o) {
            const { Code, NAME, Unit, ParentId} = o || {};
            this.form = { ...this.form, ParentId: ParentId || 0, ParentCode: Code, ParentName: NAME, ParentUom: Unit, Component: '' }
            this.formSelectList = []
            this.dataList.forEach(e => {
                if(e.Code != this.form.ParentCode) this.formSelectList.push(e)
            });
        },
        changeVal(o, t) {
            if (!o) return false;
            const { Categorycode, CategoryName, Unit, NAME, Code } = o;
            this.form.CompoentCode = Code;
            this.form.CompoentName = NAME;
            this.form[t] = Categorycode;
            this.form.MaterialGroupName = CategoryName;
            this.form.CompoentUom = Unit;
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>

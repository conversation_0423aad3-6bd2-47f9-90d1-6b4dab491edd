export const personalColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },

    {
        text: '员工号',
        value: 'StaffCode',
        width: 100,
        sortable: true,
        align: 'right',
    },
    {
        text: '姓名',
        value: 'StaffName',
        width: 100,
        sortable: true
    },
    {
        text: '上级领导',
        value: 'LeaderName',
        width: 100,
        sortable: true
    },
    {
        text: '车间',
        value: 'Line',
        width: 140,
        sortable: true
    },
    {
        text: '产线',
        value: 'Segment',
        width: 140,
        sortable: true
    },
    {
        text: '工序',
        value: 'Process',
        width: 100,
        sortable: true
    },
    // {
    //     text: '物料号',
    //     value: 'MaterialCode',
    //     width: 100,
    //     sortable: true
    // },
    //{
    //   text: '计件岗位',
    //    value: 'PricePost',
    //    width: 100,
    //    sortable: true
    //},
    {
        text: '安灯岗位',
        value: 'AndonPost',
        width: 100,
        sortable: true
    },
    // {
    //     text: '班制',
    //     value: 'WokrHours',
    //     width: 100,
    //     sortable: true
    // },
    // {
    //     text: '员工分类',
    //     value: 'Type',
    //     width: 100,
    //     sortable: true
    // },
    // {
    //     text: '类型',
    //     value: 'Type2',
    //     width: 100,
    //     sortable: true
    // },
    // {
    //     text: '类别',
    //     value: 'Type3',
    //     width: 100,
    //     sortable: true
    // },
    {
        text: '是否转班',
        value: 'ChangeShifts',
        width: 100,
        sortable: true
    },
    { text: '操作', width: 150, align: 'center', value: 'actions', sortable: true }
];

export const personaslColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '员工号',
        value: 'StaffCode',
        width: 100,
        sortable: true,
        align: 'right',
    },
    {
        text: '姓名',
        value: 'StaffName',
        width: 100,
        sortable: true
    },

    {
        text: '产线',
        value: 'Line',
        width: 140,
        sortable: true
    },
    {
        text: '工段',
        value: 'Segment',
        width: 140,
        sortable: true
    },
    {
        text: '工序',
        value: 'Process',
        width: 100,
        sortable: true
    },
    {
        text: '物料号',
        value: 'MaterialCode',
        width: 100,
        sortable: true
    },
    {
        text: '计件岗位',
        value: 'PricePost',
        width: 100,
        sortable: true
    },
    {
        text: '安灯岗位',
        value: 'AndonPost',
        width: 100,
        sortable: true
    },
    {
        text: '班制',
        value: 'WokrHours',
        width: 100,
        sortable: true
    },
    {
        text: '类型',
        value: 'Type',
        width: 100,
        sortable: true
    }
];
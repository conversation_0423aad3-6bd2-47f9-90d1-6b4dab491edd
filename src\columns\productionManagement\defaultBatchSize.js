export const defaultBatchSizeColum = [
    {
        text: '产品线',
        width: 140,
        value: 'FullLineName',
        sortable: true
    },
    {
        text: '工段',
        width: 140,
        value: 'CompanyName',
        sortable: true
    },
    {
        text: '物料编码',
        width: 100,
        value: 'MaterialCode',
        sortable: true
    },
    {
        text: '物料描述',
        width: 240,
        value: 'MaterialDescription',
        sortable: true
    },
    {
        text: '最大成批数(PCS)',
        width: 150,
        value: 'DefaultBatchQty',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: '最小成批数(PCS)',
        width: 150,
        value: 'MinBatchQty',
        sortable: false,
        semicolonFormat: true,
    },
    {
        text: '自动启停工单',
        width: 130,
        value: 'AutoStartStopWo',
        sortable: false,
        dictionary: true
    },
    {
        text: '手动生成批次类型',
        width: 160,
        value: 'AllowCreateBatchType',
        sortable: false,
        dictionary: true
    },
    {
        text: '允许修改尾批数量',
        width: 150,
        value: 'AllowUpdateLastBatchQty',
        sortable: false,
        dictionary: true
    },
    {
        text: '允许修改尾批数量超最大成批数量',
        width: 160,
        value: 'AllowLastBatchQtyOverMax',
        sortable: false,
        dictionary: true
    },
    
    {
        text: '打印工段别名',
        width: 160,
        value: 'PrintSectionName',
        sortable: false,
        dictionary: true
    },
   
    { text: '操作', width: 120, align: 'center', value: 'actions', sortable: true }
];

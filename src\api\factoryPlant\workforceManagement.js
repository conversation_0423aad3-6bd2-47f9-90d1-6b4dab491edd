import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url
//获取楼层tree
export function FactorySiteGetTree(data) {
    return request({
        url: baseURL + '/api/FactorySite/GetTree',
        method: 'post',
        data
    });
}
//点击楼层 查询列表人员
export function GetStaffPageList(data) {
    return request({
        url: baseURL + '/api/FactorySite/GetPageList',
        method: 'post',
        data
    });
}

// 关联人员与楼层关系
export function StaffSiteSaveListData(data) {
    return request({
        url: baseURL + '/api/StaffSite/SaveListData',
        method: 'post',
        data
    });
}
// 厂房楼层新增
export function FactorySiteSaveForm(data) {
    return request({
        url: baseURL + '/api/FactorySite/SaveForm',
        method: 'post',
        data
    });
}
// 厂房楼层删除
export function FactorySiteDelete(data) {
    return request({
        url: baseURL + '/api/FactorySite/Delete',
        method: 'post',
        data
    });
}


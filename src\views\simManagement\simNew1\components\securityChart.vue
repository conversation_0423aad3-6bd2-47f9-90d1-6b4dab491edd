<template>
  <div class="anqszBox">
    <div class="anqszBox_l">
      <div class="anqszBox_l_t">安全天数</div>
      <div class="anqszBox_l_b">66天</div>
    </div>
    <div class="calendar-box">
      <div class="line line-1">
        <div
          v-for="item in list.slice(0, 3)"
          :key="item.day"
          class="day-item"
          @click="open(item)"
        >
          <template v-if="item.situation == 0">
            <img
              :src="happyImg"
              alt=""
              class="happy"
            >
          </template>
          <template v-else-if="item.situation > 0">
            <img
              :src="sadImg"
              alt=""
              class="happy"
            >
          </template>
          <template v-else>
            <div class="num">{{ item.day }}</div>
          </template>
        </div>
      </div>
      <div class="line line-1">
        <div
          v-for="item in list.slice(3, 6)"
          :key="item.day"
          class="day-item"
          @click="open(item)"
        >
          <template v-if="item.situation == 0">
            <img
              :src="happyImg"
              alt=""
              class="happy"
            >
          </template>
          <template v-else-if="item.situation > 0">
            <img
              :src="sadImg"
              alt=""
              class="happy"
            >
          </template>
          <template v-else>
            <div class="num">{{ item.day }}</div>
          </template>
        </div>
      </div>
      <div class="line line-2">
        <div
          v-for="item in list.slice(6, 13)"
          :key="item.day"
          class="day-item"
          @click="open(item)"
        >
          <template v-if="item.situation == 0">
            <img
              :src="happyImg"
              alt=""
              class="happy"
            >
          </template>
          <template v-else-if="item.situation > 0">
            <img
              :src="sadImg"
              alt=""
              class="happy"
            >
          </template>
          <template v-else>
            <div class="num">{{ item.day }}</div>
          </template>
        </div>
      </div>
      <div class="line line-2">
        <div
          v-for="item in list.slice(13, 20)"
          :key="item.day"
          class="day-item"
          @click="open(item)"
        >
          <template v-if="item.situation == 0">
            <img
              :src="happyImg"
              alt=""
              class="happy"
            >
          </template>
          <template v-else-if="item.situation > 0">
            <img
              :src="sadImg"
              alt=""
              class="happy"
            >
          </template>
          <template v-else>
            <div class="num">{{ item.day }}</div>
          </template>
        </div>
      </div>
      <div class="line line-2">
        <div
          v-for="item in list.slice(20, 27)"
          :key="item.day"
          class="day-item"
          @click="open(item)"
        >
          <template v-if="item.situation == 0">
            <img
              :src="happyImg"
              alt=""
              class="happy"
            >
          </template>
          <template v-else-if="item.situation > 0">
            <img
              :src="sadImg"
              alt=""
              class="happy"
            >
          </template>
          <template v-else>
            <div class="num">{{ item.day }}</div>
          </template>
        </div>
      </div>
      <div class="line line-1">
        <div
          v-for="item in list.slice(27, 30)"
          :key="item.day"
          class="day-item"
          @click="open(item)"
        >
          <template v-if="item.situation == 0">
            <img
              :src="happyImg"
              alt=""
              class="happy"
            >
          </template>
          <template v-else-if="item.situation > 0">
            <img
              :src="sadImg"
              alt=""
              class="happy"
            >
          </template>
          <template v-else>
            <div class="num">{{ item.day }}</div>
          </template>
        </div>
      </div>
      <div class="line line-1">
        <div
          v-for="item in list.slice(30, 33)"
          :key="item.day"
          class="day-item"
          @click="open(item)"
        >
          <template v-if="item.situation == 0">
            <img
              :src="happyImg"
              alt=""
              class="happy"
            >
          </template>
          <template v-else-if="item.situation > 0">
            <img
              :src="sadImg"
              alt=""
              class="happy"
            >
          </template>
          <template v-else>
            <div class="num">{{ item.day }}</div>
          </template>
        </div>
      </div>
      <!-- 问题列表弹窗 -->
      <!-- <v-dialog v-model="showInfoDialog" scrollable persistent width="55%">
          <CalendarDialog v-if="showInfoDialog" :curTeamTreeObj="curTeamTreeObj" :searchFormObj="searchFormObj"
              :curDay="curDay" @closePopup="closeDialog">
          </CalendarDialog>
      </v-dialog> -->
    </div>
  </div>
</template>

<script>
// import CalendarDialog from "./calendarDialog.vue"
import happyImgSrc from "@/assets/imgs/happy.png"
import sadImgSrc from "@/assets/imgs/sad.png"
import { GetMonthStatistics, GetSafeTypeStatistics } from '@/views/simManagement/sim1/service.js';
export default {
  components: {
    // CalendarDialog
  },
  props: {
    // curTeamTreeObj: {
    //   type: Object,
    //   default: () => { }
    // },
    // days: {
    //   type: Array,
    //   default: () => []
    // },
    searchFormObj: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      happyImg: happyImgSrc,
      sadImg: sadImgSrc,
      showInfoDialog: false,
      curDay: '',
      days: [],
      safeList: [],

    };
  },
  computed: {
    list() {
      let curLen = this.days.length
      let needPushLen = 33 - curLen
      let cloneDays = JSON.parse(JSON.stringify(this.days))
      let temp = {
        day: undefined,
        type: undefined
      }
      //补充缺少的数量,保持十字形状
      for (let i = 0; i < needPushLen; i++) {
        cloneDays.push(temp)
      }
      return cloneDays
    }
  },
  watch: {
    'searchFormObj': {
      handler(nv, ov) {
        this.GetMonthStatisticsFn()
        this.GetSafeTypeStatisticsFn()
      },
      deep: true,
      immediate: true
    }
  },
  created() { },
  mounted() {
    // this.GetMonthStatisticsFn()
  },
  methods: {
    async GetMonthStatisticsFn() {
      if (!this.searchFormObj.PresentDepartmentId) {
        return
      }
      let params = {
        "simLevel": this.searchFormObj.simLevel,
        "date": this.searchFormObj.date,
        teamId: this.searchFormObj.PresentDepartmentId,
        departmentId: this.searchFormObj.PresentDepartmentId,
        plantId: this.searchFormObj.PresentDepartmentId
      }
      // if (this.searchFormObj.simLevel === 'SIM1') {
      //     params.teamId = this.searchFormObj.PresentDepartmentId
      // } else if (this.searchFormObj.simLevel === 'SIM2') {
      //     params.departmentId = this.searchFormObj.PresentDepartmentId
      // } else if (this.searchFormObj.simLevel === 'SIM5') {
      //     params.plantId = this.searchFormObj.PresentDepartmentId
      // }
      let res = await GetMonthStatistics(params)
      if (!res || !res.response) return
      let arr = JSON.parse(res.response)
      if (!arr.length) return
      this.days = arr
    },
    async GetSafeTypeStatisticsFn() {
      if (!this.searchFormObj.PresentDepartmentId) {
        return
      }
      let list = [
        {
          "AccidentName": "LTA损失工时事故",
          "AccidentTypeCode": "1",
          "Count": 0
        },
        {
          "AccidentName": "MT医疗事故",
          "AccidentTypeCode": "2",
          "Count": 0
        },
        {
          "AccidentName": "FA急救事故事故",
          "AccidentTypeCode": "3",
          "Count": 0
        },
        {
          "AccidentName": "NearMiss险兆事故",
          "AccidentTypeCode": "4",
          "Count": 0
        },
        {
          "AccidentName": "SO安全隐患事故",
          "AccidentTypeCode": "5",
          "Count": 0
        },
        {
          "AccidentName": "无事故",
          "AccidentTypeCode": "6",
          "Count": 0
        }
      ]
      let params = {
        "simLevel": this.searchFormObj.simLevel,
        "date": this.searchFormObj.date
      }
      if (this.searchFormObj.simLevel === 'SIM1') {
        params.teamId = this.searchFormObj.PresentDepartmentId
      } else if (this.searchFormObj.simLevel === 'SIM2') {
        params.departmentId = this.searchFormObj.PresentDepartmentId
      } else if (this.searchFormObj.simLevel === 'SIM5') {
        params.plantId = this.searchFormObj.PresentDepartmentId
      }
      let res = await GetSafeTypeStatistics(params).catch((err) => {
      })
      if (res && res.response && res.response != '[]') {
        list = JSON.parse(res.response)
      }
      this.safeList = list
      // this.initFunnel()
    },
    open(item) {
      if (item.situation < 0) return
      this.curDay = item
      this.showInfoDialog = true
    },
    //关闭新增弹窗
    closeDialog() {
      this.showInfoDialog = false
      // this.addDialogVisable = false
      // this.editItemObj = {}
      this.$emit('change');

    }
  }
};
</script>
<style lang="less" scoped>
.anqszBox {
    width: 100%;
    display: flex;
}
.anqszBox_l {
    width: 18%;
    padding-top: 3%;
    box-sizing: border-box;
}
.anqszBox_l_t,
.anqszBox_l_b {
    font-size: 18px;
    color: #fff;
    font-weight: bold;
    text-align: center;
    margin-top: 5%;
}
.calendar-box {
    width: 60%;
    height: 90%;
    display: flex;
    flex-direction: column;
    justify-content: left;
}

.line {
    display: flex;
    height: 10%;

    &.line-1 {
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: center;
    }

    &.line-2 {
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: center;
    }

    .day-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 10%;
        height: 100%;
        flex-shrink: 0;
        cursor: pointer;

        // text-align: center;
        .happy {
            width: 85%;

            // height: 85%;
            &:hover {
                // width: 100%;
                // height: 100%;
                transform: scale(1.3);
            }
        }

        .num {
            width: 100%;
            height: 100%;
            background: none !important;
            color: #fff;
            text-align: center;
            line-height: 35px;
            border: 1px solid #fff;

            &:hover {
                background: #888;
            }
        }
    }
}
</style>
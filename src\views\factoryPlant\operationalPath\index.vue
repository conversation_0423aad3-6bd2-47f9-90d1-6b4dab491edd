<template>
    <div class="operational-path">
        <SearchForm :show-from="showForm" :searchinput="searchInputs" @searchForm="searchForm"></SearchForm>

        <v-card outlined>
            <div class="form-btn-list">
                <v-btn icon class="float-left mx-4" @click="showForm = !showForm">
                    <v-icon>{{ showForm ? 'mdi-menu-open' : 'mdi-menu' }}</v-icon>
                    {{ $t('GLOBAL._SSL') }}
                </v-btn>
                <v-btn icon color="primary" @click="getdata">
                    <v-icon>mdi-cached</v-icon>
                </v-btn>
                <v-btn color="primary" @click="addOperationalPath">{{ $t('GLOBAL._XZ') }}</v-btn>
                <v-btn @click="batchDeletePath">{{ $t('GLOBAL._PLSC') }}</v-btn>
            </div>
            <Tables ref="tablePath" table-name="DFM_GYLX" table-height="320" :loading="loading"
                :headers="operationalPath" :current-select-id="currentSelectId" :click-fun="selectedPath"
                :desserts="tableData" :page-options="pageData" @selectePages="selectePages" @tableClick="tableClick">
            </Tables>
        </v-card>
        <v-card class="mt-5">
            <div class="tab-item">
                <v-row class="mb-5">
                    <v-col :cols="12" :lg="6" class="tl">
                        <v-tabs v-model="activeTab" @change="changeTab">
                            <v-tab>{{ $t('DFM_GYLX._LXDYCP') }}</v-tab>
                            <v-tab>{{ $t('DFM_GYLX._GYLXMX') }}</v-tab>
                        </v-tabs>
                    </v-col>
                    <v-col class="btn-box" :cols="12" :lg="4">
                        <v-btn icon color="primary">
                            <v-icon>mdi-cached</v-icon>
                        </v-btn>
                        <v-btn color="primary" @click="addProductOrDetail">{{ $t('GLOBAL._XZ') }}</v-btn>
                        <v-btn color="danger" @click="batchDeleteProductOrDetail">{{ $t('GLOBAL._PLSC') }}</v-btn>
                    </v-col>
                </v-row>
                <Product v-if="activeTab === 0" ref="product" :current-select-id="currentSelectId"
                    @handlePopup="handlePopup" />
                <Detail v-else ref="detail" :current-select-id="currentSelectId" @handlePopup="handlePopup" />
            </div>
        </v-card>
        <!-- 添加工艺路线信息弹窗 -->
        <v-dialog v-model="isShowOperationalPathPopup" scrollable width="55%">
            <OperationalPathPopup v-if="isShowOperationalPathPopup" :select-path-obj="selectPathObj" @getdata="getdata"
                @handlePopup="handlePopup" />
        </v-dialog>

        <!-- 添加路线对应产品 -->
        <v-dialog v-model="isShowProductPopup" scrollable width="55%">
            <ProductPopup v-if="isShowProductPopup" :select-product-obj="selectProductObj"
                :current-select-id="currentSelectId" @handlePopup="handlePopup" @getProductTableList="selectedPath" />
        </v-dialog>

        <!-- 添加工艺路线明细 -->
        <v-dialog v-model="isShowDetailPopup" scrollable width="55%">
            <DetailPopup v-if="isShowDetailPopup" :select-detail-obj="selectDetailObj"
                :current-select-id="currentSelectId" @getDetailTableList="selectedPath" @handlePopup="handlePopup" />
        </v-dialog>
    </div>
</template>

<script>
import Product from './components/product.vue';
import Detail from './components/detail.vue';
import OperationalPathPopup from './components/operationalPathPopup.vue';
import ProductPopup from './components/productPopup.vue';
import DetailPopup from './components/detailPopup.vue';
import { operationalPath } from '@/columns/factoryPlant/tableHeaders';
import { getPathList, getDetailList, deleteOperationalPath } from './service';
const searchInputs = [
    {
        value: '',
        icon: 'mdi-account-check',
        label: '工厂',
        placeholder: '请输入工厂名称',
        key: 'Factory'
    },
    {
        value: '',
        icon: 'mdi-account-check',
        label: '路线代码',
        placeholder: '请输入路线代码',
        key: 'RoutingCode'
    },
    {
        value: '',
        icon: 'mdi-account-check',
        label: '路线名称',
        placeholder: '请输入路线名称',
        key: 'RoutingName'
    },
    {
        value: '',
        icon: 'mdi-account-check',
        label: '类型',
        placeholder: '请输入类型',
        key: 'RoutingType',
        selectData: [
            { text: '11212', value: '1' },
            { text: '233131', value: '2' }
        ],
        isSelect: true
    }
];
export default {
    components: {
        Product,
        Detail,
        OperationalPathPopup,
        ProductPopup,
        DetailPopup
    },
    data() {
        return {
            loading: false,
            showForm: false,
            pageData: {
                pageSize: 20,
                page: 1,
                total: 0,
                pageCount: 0,
                pageSizeitems: [10, 20, 30, 40]
            },
            activeTab: 0,
            searchInputs,
            operationalPath,
            tableData: [],
            productData: [],
            detailData: [],
            isShowOperationalPathPopup: false,
            isShowProductPopup: false,
            isShowDetailPopup: false,
            currentSelectId: '',
            selectPathObj: {},
            selectProductObj: {},
            selectDetailObj: {},
            paramObj: {}
        };
    },
    created() {
        this.getdata();
    },
    methods: {
        // 批量删除路线对应产品或明细
        batchDeleteProductOrDetail() {
            switch (this.activeTab) {
                case 0:
                    this.$refs.product.batchDelete();
                    break;
                case 1:
                    this.$refs.detail.batchDelete();
                    break;
            }
        },
        // 批量删除路线
        batchDeletePath() {
            let selecteds = this.$refs.tablePath.selected;
            if (selecteds.length === 0) {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
                return false;
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let ids = [];
                    selecteds.forEach(item => {
                        ids.push(item.ID);
                    });
                    let resp = await deleteOperationalPath(ids);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
                    this.getdata();
                })
                .catch(() => { });
        },
        // 删除路线
        deletePath(data) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let resp = await deleteOperationalPath([data.ID]);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
                    this.getdata();
                })
                .catch(() => { });
        },
        tableClick(item, type) {
            switch (type) {
                case 'edit':
                    this.editPath(item);
                    break;
                case 'delete':
                    this.deletePath(item);
                    break;
            }
        },
        // 编辑路线
        editPath(item) {
            this.selectPathObj = item;
            this.isShowOperationalPathPopup = true;
        },
        selectePages(data) {
            this.pageData.page = data.pageCount;
            this.pageData.pageSize = data.pageSize;
            this.getdata();
        },
        // 获取路线明细列表数据
        async getDetailTableList() {
            let resp = await getDetailList({ routingId: this.currentSelectId });
            this.detailData = resp.response.data;
        },
        selectedPath(data) {
            if (data) {
                this.currentSelectId = data.ID;
            }
            switch (this.activeTab) {
                case 0:
                    this.$nextTick(() => {
                        this.$refs.product.getdata();
                    });
                    break;
                case 1:
                    this.$nextTick(() => {
                        this.$refs.detail.getdata();
                    });
                    break;
            }
        },
        addProductOrDetail() {
            if (this.activeTab == 0) this.isShowProductPopup = true;
            else this.isShowDetailPopup = true;
        },
        handlePopup(boolean, type, data) {
            switch (type) {
                case 'path':
                    this.isShowOperationalPathPopup = boolean;
                    break;
                case 'product':
                    this.selectProductObj = {};
                    if (data) this.selectProductObj = data;
                    this.isShowProductPopup = boolean;
                    break;
                case 'detail':
                    this.selectDetailObj = {};
                    if (data) this.selectDetailObj = data;
                    this.isShowDetailPopup = boolean;
            }
        },
        async getdata() {
            this.loading = true;
            try {
                let resp = await getPathList({ ...this.paramObj, page: this.pageData.page, intPageSize: this.pageData.pageSize });
                this.loading = false;
                this.$refs.tablePath.selected = [];
                this.tableData = resp.response.data;
                this.pageData.total = resp.response.dataCount;
                this.pageData.pageCount = resp.response.pageCount;

                // 默认选中第一条数据
                this.selectedPath(this.tableData[0]);
            } catch {
                this.loading = false;
            }
        },
        addOperationalPath() {
            this.selectPathObj = {};
            this.isShowOperationalPathPopup = true;
        },
        searchForm(params) {
            this.paramObj = params;
            this.getdata();
        },
        changeTab() {
            // this.selectedPath()
            console.log(this.activeTab);
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-4.col-12.btn-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-left: auto;
    text-align: right;
    padding-right: 30px;

    .v-btn:nth-child(n + 1) {
        margin-left: 15px;
    }
}
</style>

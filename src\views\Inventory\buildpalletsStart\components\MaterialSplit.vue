<template>
    <div class="usemystyle MaterialSplit">
        <div class="tabinputbox">
            <div class="tabinputsinglebox">
                <el-input 
                    size="mini" 
                    @change="getRowBySSCC" 
                    ref="autoFocus" 
                    :placeholder="$t('Consume.SSCC')" 
                    v-model="sscc"
                    :disabled="!splitVisible">
                    <template slot="append"><i class="el-icon-full-screen"></i></template>
                </el-input>
            </div>
            <div class="tabinputsinglebox">
                <el-input 
                    size="mini" 
                    :placeholder="$t('MaterialPreparationBuild.SplitQuantity')" 
                    v-model="splitQuantity"
                    :disabled="!splitVisible || !ssccFlag">
                    <template slot="append">{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}</template>
                </el-input>
            </div>
            <div class="tabinputsinglebox">
                <div class="tabbtnsinglebox">
                    <el-button 
                        style="margin-left: 5px" 
                        :disabled="!canSplit" 
                        size="small" 
                        icon="el-icon-scissors" 
                        class="tablebtn" 
                        @click="performSplit()">
                        {{ this.$t('MaterialPreparationBuild.Split') }}
                    </el-button>
                    <el-button 
                        style="margin-left: 5px" 
                        size="small" 
                        icon="el-icon-view" 
                        @click="toggleSplitVisibility()">
                        {{ splitVisible ? $t('MaterialPreparationBuild.HideSplit') : $t('MaterialPreparationBuild.ShowSplit') }}
                    </el-button>
                </div>
            </div>
        </div>
        
        <!-- 分包信息显示 -->
        <div v-if="splitVisible && selectedMaterial" class="split-info-box">
            <div class="info-item">
                <span class="info-label">{{ $t('MaterialPreparationBuild.SelectedMaterial') }}:</span>
                <span class="info-value">{{ selectedMaterial.MaterialCode }} - {{ selectedMaterial.MaterialName }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">{{ $t('MaterialPreparationBuild.AvailableQuantity') }}:</span>
                <span class="info-value">{{ selectedMaterial.InQuantity }}{{ selectedMaterial.MaterialUnit1 }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">{{ $t('MaterialPreparationBuild.BatchNumber') }}:</span>
                <span class="info-value">{{ selectedMaterial.LBatch }}</span>
            </div>
        </div>
    </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';
import { splitMaterial } from '@/api/materialManagement/lineLibraryManagement.js';
import { Message } from 'element-ui';

export default {
    data() {
        return {
            sscc: '',
            splitQuantity: '',
            ssccFlag: false,
            splitVisible: false,
            selectedMaterial: null,
            detailobj: {}
        };
    },
    computed: {
        canSplit() {
            return this.splitVisible && 
                   this.ssccFlag && 
                   this.sscc !== '' && 
                   this.splitQuantity !== '' && 
                   Number(this.splitQuantity) > 0 &&
                   this.selectedMaterial &&
                   Number(this.splitQuantity) < Number(this.selectedMaterial.InQuantity);
        }
    },
    mounted() {
        this.detailobj = JSON.parse(this.$route.query.query);
    },
    methods: {
        toggleSplitVisibility() {
            this.splitVisible = !this.splitVisible;
            if (!this.splitVisible) {
                this.resetSplitForm();
            }
        },
        
        resetSplitForm() {
            this.sscc = '';
            this.splitQuantity = '';
            this.ssccFlag = false;
            this.selectedMaterial = null;
        },
        
        getRowBySSCC() {
            this.$emit('getRowBySscc', this.sscc);
        },
        
        // 从父组件接收选中的物料信息
        setSelectedMaterial(material) {
            this.selectedMaterial = material;
            this.ssccFlag = true;
        },
        
        async performSplit() {
            if (!this.canSplit) {
                Message({
                    message: this.$t('MaterialPreparationBuild.SplitConditionNotMet'),
                    type: 'warning'
                });
                return;
            }
            
            try {
                const splitData = {
                    materialId: this.selectedMaterial.MaterialId,
                    sscc: this.sscc,
                    originalQuantity: this.selectedMaterial.InQuantity,
                    splitQuantity: Number(this.splitQuantity),
                    batchNumber: this.selectedMaterial.LBatch,
                    equipmentId: window.sessionStorage.getItem('room'),
                    unitId: this.selectedMaterial.UnitId
                };
                
                const result = await splitMaterial(splitData);
                
                Message({
                    message: result.msg || this.$t('MaterialPreparationBuild.SplitSuccess'),
                    type: 'success'
                });
                
                // 重置表单
                this.resetSplitForm();
                
                // 通知父组件刷新数据
                this.$emit('getRefresh');
                
            } catch (error) {
                Message({
                    message: error.message || this.$t('MaterialPreparationBuild.SplitFailed'),
                    type: 'error'
                });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.MaterialSplit {
    .split-info-box {
        margin-top: 10px;
        padding: 10px;
        background: #f5f7fa;
        border-radius: 4px;
        border: 1px solid #e4e7ed;
        
        .info-item {
            display: flex;
            margin-bottom: 5px;
            
            .info-label {
                font-weight: 600;
                color: #606266;
                min-width: 120px;
            }
            
            .info-value {
                color: #303133;
            }
        }
    }
}
</style>

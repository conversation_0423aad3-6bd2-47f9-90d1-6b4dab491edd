import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';

//维修经验列表
export function GetExperiencePageList(data) {
    const api = '/api/RepairExperience/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}
//维修经验列表
export function GetExperienceList(data) {
    const api = '/api/RepairExperience/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
//维修经验保存
export function ExperienceSaveForm(data) {
    const api = '/api/RepairExperience/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}

//维修经验删除
export function ExperienceDelete(data) {
    const api = '/api/RepairExperience/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
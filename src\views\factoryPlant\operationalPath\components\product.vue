<template>
    <div class="product-module">
        <Tables table-name="DFM_GYLX" ref="table" table-height="320" :loading="loading" @selectePages="selectePages"
            :headers="operationalPathProduct" @tableClick="tableClick" :desserts="productData"
            :pageOptions="pageData" />
    </div>
</template>

<script>
import { operationalPathProduct } from '@/columns/factoryPlant/tableHeaders';
import { getProductList, deleteProduct } from '../service';
export default {
    props: {
        currentSelectId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            operationalPathProduct,
            pageData: {
                pageSize: 20,
                page: 1,
                total: 0,
                pageCount: 0,
                pageSizeitems: ['10', '20', '30', '40']
            },
            loading: false,
            productData: []
        };
    },
    created() {
        this.getdata();
    },
    methods: {
        batchDelete() {
            let selecteds = this.$refs.table.selected;
            if (selecteds.length === 0) {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
                return false;
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let ids = [];
                    selecteds.forEach(item => {
                        ids.push(item.ID);
                    });
                    let resp = await deleteProduct(ids);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
                    this.getdata();
                })
                .catch(() => { });
        },
        tableClick(item, type) {
            switch (type) {
                case 'edit':
                    this.editProduct(item);
                    break;
                case 'delete':
                    this.deleteProduct(item);
                    break;
            }
        },
        deleteProduct(data) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let resp = await deleteProduct([data.ID]);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
                    this.getdata();
                })
                .catch(() => { });
        },
        editProduct(item) {
            this.$emit('handlePopup', true, 'product', item);
        },
        selectePages(data) {
            this.pageData.page = data.pageCount;
            this.pageData.pageSize = data.pageSize;
            this.getdata();
        },
        async getdata() {
            if (!this.currentSelectId) return;
            this.loading = true;
            try {
                let resp = await getProductList({ routingId: this.currentSelectId, page: this.pageData.page, intPageSize: this.pageData.pageSize });
                this.loading = false;
                this.$refs.table.selected = [];
                this.productData = resp.response.data;
                this.pageData.total = resp.response.dataCount;
                this.pageData.pageCount = resp.response.pageCount;
            } catch {
                this.loading = false;
            }
        }
    }
};
</script>

<style>

</style>
import request from '@/util/request';
import { configUrl } from '@/config';
import store from '@/store';

const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url

//获取数据字典Tree数据
export function getClassifyTree(data) {
    // 检查缓存是否存在且未过期（1小时）
    const cachedData = store.getters['dictionary/getDictionaryData'];
    const lastUpdate = store.getters['dictionary/getLastUpdateTime'];
    const oneHour = 24 * 60 * 60 * 1000;
    
    if (cachedData && lastUpdate && (Date.now() - lastUpdate < oneHour)) {
        return Promise.resolve({ response: cachedData });
    }
    
    // 缓存不存在或已过期，从API获取
    return request({
        url: baseURL + '/api/DataItem/GetClassifyTree',
        method: 'post',
        params: data
    });
}
// 点击数据字典查询列表 分页
export function GetPageList(data) {
    return request({
        url: baseURL + '/api/DataItemDetail/GetPageList',
        method: 'post',
        data
    });
}

// 数据字典DFMYesNo
export function GetDFMYesNo(data) {
    return request({
        url: baseURL + '/api/DataItemDetail/GetList',
        method: 'post',
        params: data
    });
}
// 数据字典LogSheetFrequency
export function getLogSheetFrequency(data) {
    return request({
        url: baseURL + '/api/DataItemDetail/GetList',
        method: 'post',
        params: data
    });
}

// 点击数据字典查询列表
export function GetNoPageList(data) {
    return request({
        url: baseURL + '/api/DataItemDetail/GetList',
        method: 'post',
        params: data
    });
}

//获取字典分类树列表
export function GetTreeList(data) {
    return request({
        url: baseURL + '/api/DataItem/GetTreeList',
        method: 'post',
        params: data
    });
}
// 获取字典分类列表
export function GetRootClassifyList(data) {
    return request({
        url: baseURL + '/api/DataItem/GetRootClassifyList',
        method: 'post',
        params: data
    });
}
// 删除字典分类
export function DeleteClass(data) {
    return request({
        url: baseURL + '/api/DataItem/Delete',
        method: 'post',
        data
    }).then(resp => {
        // 删除成功后刷新缓存
        if (store && store.dispatch) {
            store.dispatch('dictionary/refreshDictionary');
        }
        return resp;
    });
}

//保存分类数据
export function saveForm(data) {
    return request({
        url: baseURL + '/api/DataItem/SaveForm',
        method: 'post',
        data
    }).then(resp => {
        // 保存成功后刷新缓存
        if (store && store.dispatch) {
            store.dispatch('dictionary/refreshDictionary');
        }
        return resp;
    });
}

// 字典列表增删改查
// api/DataItemDetail/GetList?itemCode=11&lang=cn
//保存分类数据
export function getList(data) {
    return request({
        url: baseURL + '/api/DataItemDetail/GetList',
        method: 'post',
        params: data
    });
}
//字典详情 保存修改
export function saveFormDetail(data) {
    return request({
        url: baseURL + '/api/DataItemDetail/SaveForm',
        method: 'post',
        data
    }).then(resp => {
        // 保存成功后刷新缓存
        if (store && store.dispatch) {
            store.dispatch('dictionary/refreshDictionary');
        }
        return resp;
    });
}

//字典详情删除
export function deleteDetail(data) {
    return request({
        url: baseURL + '/api/DataItemDetail/Delete',
        method: 'post',
        data
    }).then(resp => {
        // 删除成功后刷新缓存
        if (store && store.dispatch) {
            store.dispatch('dictionary/refreshDictionary');
        }
        return resp;
    });
}
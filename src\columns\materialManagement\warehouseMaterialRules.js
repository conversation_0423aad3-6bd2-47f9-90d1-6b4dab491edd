// dictionary: true, isEditCell: true
export const warehouseMaterialRulesColum = [
    { text: '序号', value: 'Index', width: '80px' },
    // { text: '仓库编号', value: 'WarehouseCode', width: '140px' },
    { text: '产线', value: 'Linename', width: '160px' },
    { text: '物料名称', value: 'MaterialName', width: '180px' },
    { text: '供料仓库', value: 'WarehouseId', width: '160px', dictionary: true },
    // { text: '仓库类型', value: 'WarehouseType', width: '140px', dictionary: true },
    // { text: '物料编码', value: 'MaterialCode', width: '140px' },
    { text: '计量单位', value: 'Unit', width: '100px', dictionary: true },
    { text: '安全库存', value: 'Safevalue', width: '120px', semicolonFormat: true },
    { text: '预警仓库', value: 'Warningvalue', width: '120px', semicolonFormat: true },
    { text: '最高库存', value: 'Maxvalue', width: '120px', semicolonFormat: true },
    // { text: '楼层', value: 'Floor', width: '120px' },
    // { text: '产线', value: 'Linename', width: '140px' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '180px'
    }
];
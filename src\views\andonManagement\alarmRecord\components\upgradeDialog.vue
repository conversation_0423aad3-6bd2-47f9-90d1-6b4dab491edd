<template>
    <v-dialog v-model="dialog" persistent max-width="980px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ this.$t('ANDON_BJJL.upgradeList') }}
                <v-icon @click="closePopup">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="mt-2">
                <Tables
                    ref="tablePath"
                    :current-select-id="currentSelectId"
                    itemKey="EventLevel"
                    :dictionaryList="dictionaryList"
                    :showSelect="false"
                    table-height="calc(100vh - 260px)"
                    :loading="loading"
                    :headers="upgradeColumns"
                    :desserts="desserts"
                    :footer="false"
                    :page-options="pageData"
                    table-name="ANDON_BJJL"
                ></Tables>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script>
import { upgradeColumns } from '@/columns/andonManagement/alarmRecord.js';
import { UpgradeRuleGetList } from '@/api/andonManagement/alarmRecord.js';
import { getDepartmentList } from '@/api/andonManagement/upgradeRule.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        productLineList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            dialog: '',
            loading: false,
            upgradeColumns,
            currentSelectId: '',
            desserts: [],
            pageData: {
                pageSize: 1000,
                page: 1,
                total: 0,
                pageCount: 0,
                pageSizeitems: [10, 20, 30, 40]
            },
            typeChildList: [],
            dutyList: []
        };
    },
    computed: {
        dictionaryList() {
            return [{arr: this.dutyList, key: 'Duty', val: 'ID', text: 'Fullname'}];
        }
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal) {
                    console.log(this.operaObj);
                    this.currentSelectId = this.operaObj?.EventLevel
                    this.getData();
                }
            },
            deep: true,
            immediate: true
        }
    },
    created(){
        this.getDutyList()
    },
    methods: {
        // 获取职位列表
        async getDutyList() {
            const res = await getDepartmentList({});
            const { success, response } = res || {};
            if(response && success) this.dutyList = response
            else this.dutyList = []
        },
        async getData() {
            const { MainAlarmType, SubAlarmType } = this.operaObj
            const res = await UpgradeRuleGetList({ mainAlarmType: MainAlarmType, subAlarmType: SubAlarmType });
            const { success, response } = res;
            if (success) {
                this.desserts = response || [];
            }
        },
        closePopup() {
            this.dialog = false;
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .v-sheet.v-card:not(.v-sheet--outlined) {
    box-shadow: none;
}
</style>
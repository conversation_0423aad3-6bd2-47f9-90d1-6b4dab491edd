import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';
let DFM = 'baseURL_DFM';


// 服务单列表
export function GetRepairServicePageList(data) {
    const api = '/api/RepairService/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}
// 服务单状态
export function GetRepairServiceStatus(data) {
    const api = '/api/DataItemDetail/GetDetailTree?itemCode=RepairServiceStatus';
    return getRequestResources(DFM, api, 'post', data);
}
// 服务单采购
export function GetRepairServiceRequest(data) {
    const api = '/api/RepairService/Approve';
    return getRequestResources(baseURL, api, 'post', data);
}
// 服务单驳回
export function GetRepairServiceReject(data) {
    const api = '/api/RepairService/Reject';
    return getRequestResources(baseURL, api, 'post', data);
}
// 服务单出单
export function GetRepairServicePurchase(data) {
    const api = '/api/RepairService/Purchase';
    return getRequestResources(baseURL, api, 'post', data);
}
// 服务单验收
export function GetRepairServiceAccept(data) {
    const api = '/api/RepairService/Accept';
    return getRequestResources(baseURL, api, 'post', data);
}
// 服务单验收
export function GetUserConfirm(data) {
    const api = '/api/RepairService/UserConfirm';
    return getRequestResources(baseURL, api, 'post', data);
}
// 服务单验收
export function GetManagerConfirm(data) {
    const api = '/api/RepairService/ManagerConfirm';
    return getRequestResources(baseURL, api, 'post', data);
}
//服务单修改
export function GetRepairServiceSaveForm(data) {
    const api = '/api/RepairService/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
//服务单导出
export function GetRepairAcceptanceItemExportData(data) {
    const api = '/api/RepairService/ExportData';
    return getRequestResources(baseURL, api, 'get', data);
}
//检验项目
export function GetRepairAcceptanceItemGetPageList(data) {
    const api = '/api/RepairAcceptanceItem/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
//检验项目新增
export function GetRepairAcceptanceItemSaveForm(data) {
    const api = '/api/RepairAcceptanceItem/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}

//检验项目删除
export function GetRepairAcceptanceItemDelete(data) {
    const api = '/api/RepairAcceptanceItem/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
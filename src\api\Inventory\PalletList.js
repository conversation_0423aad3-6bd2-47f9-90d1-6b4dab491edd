import { getRequestResources } from '@/api/fetch';
import store from '@/store';
const baseURL = 'baseURL_Inventory'
const baseURL2 = 'baseURL_MATERIAL'

export function GetDesination(data) {
    const api = '/api/PalletlistView/GetDesination'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetListByLevel(data) {
    const api = '/api/Department/GetListByLevel?key=Line'
    return getRequestResources(baseURL2, api, 'post', data);
}

export function GetList(data) {
    // 检查缓存是否存在且未过期（1小时）
    const cachedData = store.getters['dictionary/getDictionaryByCode']('SAPStatus');
    const lastUpdate = store.getters['dictionary/getLastUpdateTime'];
    const oneHour = 24 * 60 * 60 * 1000;
    
    if (cachedData && lastUpdate && (Date.now() - lastUpdate < oneHour)) {
        return Promise.resolve({ response: cachedData });
    }
    
    // 缓存不存在或已过期，从API获取
    const api = '/api/DataItemDetail/GetList?itemCode=SAPStatus';
    return getRequestResources(baseURL2, api, 'post', data).then(resp => {
        // 更新缓存
        store.dispatch('dictionary/saveDictionary', {
            code: 'SAPStatus',
            data: resp.response
        });
        return resp;
    });
}
export function GetPageList(data) {
    const api = '/api/PalletlistView/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

export function BlockInventory(data) {
    const api = '/api/MaterialInventory/BlockInventory'
    return getRequestResources(baseURL, api, 'post', data, true);
}
export function UBlockInventory(data) {
    const api = '/api/MaterialInventory/UBlockInventory'
    return getRequestResources(baseURL, api, 'post', data, true);
}
export function Reverse(data) {
    const api = '/api/PalletlistView/Reverse'
    return getRequestResources(baseURL, api, 'post', data);
}

export function Verify(data) {
    const api = '/api/PalletlistView/Verify'
    return getRequestResources(baseURL, api, 'post', data, true);
}
export function GetQTY(data) {
    const api = '/api/PalletlistView/GetQTY'
    return getRequestResources(baseURL, api, 'post', data);
}
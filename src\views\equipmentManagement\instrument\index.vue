<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <!-- @click="RepastInfoGetPage" -->
                    <v-btn icon color="primary" @click="getTableData">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'SBGLZY_JLYQTZ_ADD'" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="primary" v-has="'SBGLZY_JLYQTZ_ALLREMOVE'" :disabled="!deleteList.length" @click="btnClickEvet('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn>
                    <v-btn color="primary" v-has="'SBGLZY_JLYQTZ_CJJYRW'" @click="btnClickEvet('jyrw')">{{ $t('TPM_SBGL_JLQJGL.jyrw') }}</v-btn>
                    <v-btn color="primary" v-has="'SBGLZY_JLYQTZ_DC'" @click="handleExport">{{ $t('GLOBAL._DC') }}</v-btn>
                    <v-btn color="primary" v-has="'SBGLZY_JLYQTZ_DR'" @click="handleImport()">{{ $t('GLOBAL._DR') }}</v-btn>
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    :clickFun="clickFun"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_JLQJGL"
                    :headers="instrumentColum"
                    :desserts="desserts"
                    @selectePages="selectePages"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"
                ></Tables>
                <createRepast
                    @load="getTableData"
                    ref="createRepast"
                    :VerifyCategory="VerifyCategory"
                    :VerifyMethod="VerifyMethod"
                    :MeasureType="MeasureType"
                    :dialogType="dialogType"
                    :tableItem="tableItem"
                ></createRepast>
            </v-card>
            <createRepastAdd
                ref="createRepastAdd"
                @loadData="getTableData"
                :VerifyCategory="VerifyCategory"
                :VerifyMethod="VerifyMethod"
                :MeasureType="MeasureType"
                :dialogType="dialogType"
                :tableItem="tableItem"
            ></createRepastAdd>
            <createRepastAdd2 @loadData="RepastInfoLogGetPage" ref="createRepastAdd2" :rowtableItem="rowtableItem" :dialogType="dialogType" :tableItem="tableItem2"></createRepastAdd2>
            <el-drawer size="80%" :title="rowtableItem.Name + ' | ' + rowtableItem.MeasureNo" :wrapperClosable="false" :visible.sync="detailShow" direction="rtl">
                <v-card class="ma-1">
                    <div class="form-btn-list">
                        <v-btn color="primary" v-has="'SBGLZY_JLYQTZ_JLQJMX_ADD'" :disabled="!rowtableItem.ID" @click="btnClickEvet('add2')">{{ $t('GLOBAL._XZ') }}</v-btn>
                    </div>
                    <Tables
                        :page-options="pageOptions2"
                        :footer="false"
                        :showSelect="false"
                        :loading="loading2"
                        :btn-list="btnList2"
                        tableHeight="calc(100vh - 220px)"
                        table-name="TPM_SBGL_JLQJGL"
                        :headers="instrumentColum2"
                        :desserts="desserts2"
                        @tableClick="tableClick2"
                    ></Tables>
                </v-card>
            </el-drawer>
        </div>
        <el-dialog :title="$t('GLOBAL._CheckFile')" :visible.sync="CheckFile">
            <div style="display: flex; justify-content: space-evenly">
                <div v-for="(item, index) in Myfilelist" :key="index" style="display: flex; flex-direction: column; align-items: center">
                    <div style="margin-bottom: 10px">{{ item }}</div>
                    <el-button @click="MycheckFile(index)" style="background: #67c23a; color: #fff" class="tablebtn">{{ $t('GLOBAL._CK') }}</el-button>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="CheckFile = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
        <el-dialog :title="$t('GLOBAL._IMGINPORT')" :visible.sync="imgModel">
            <div><img :src="ImgPath" /></div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="imgModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
        <div class="loading-box" v-if="importLoading">
            <a-spin tip="导入中..." :spinning="importLoading"></a-spin>
        </div>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';

import {
    GetMeasureAccountPageList,
    GetMeasureAccountDelete,
    GetMeasureAccountImportData,
    GetMeasureCalibrateItemGetList,
    GetMeasureCalibrateItemDelete,
    GetMeasureCalibrateItemGetFileUrl
} from '@/api/equipmentManagement/instrument.js';
import { GetExportData, GetPersonList } from '@/api/equipmentManagement/Equip.js';
import { instrumentColum, instrumentColum2 } from '@/columns/equipmentManagement/instrument.js';
import { configUrl } from '@/config';
import { Message } from 'element-ui';

export default {
    name: 'RepastModel',
    components: {
        createRepast: () => import('./components/createRepast.vue'),
        createRepastAdd: () => import('./components/createRepastAdd.vue'),
        createRepastAdd2: () => import('./components/createRepastAdd2.vue')
    },
    data() {
        return {
            detailShow: false,
            ImgPath: '',
            CheckFile: false,
            Myfilelist: [],
            imgModel: false,
            importLoading: false,
            // tree 字典数据
            tab: null,
            loading: false,
            showFrom: false,
            papamstree: {
                MeasureNo: '',
                AccountAssetNo:'',
                Name: '',
                Status: '',
                AdjustmentStatus: '',
                VerifyMethod: '',
                CalibrateDateFrom: '',
                CalibrateDateTo: '',
                ExpirationDateFrom: '',
                ExpirationDateTo: '',
                pageIndex: 1,
                pageSize: 20
            },
            Status: [],
            AdjustmentStatus: [],
            VerifyMethod: [],
            VerifyCategory: [],
            MeasureType: [],
            instrumentColum,
            instrumentColum2,
            //查询条件
            desserts: [],
            loading2: false,
            desserts2: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            pageOptions2: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            rowtableItem: {},
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem2: {},
            MeasureAccountByData: [],
            CalibrationByData: [],
            tableItem: {}, // 选择操作数据
            deleteList: [] //批量选中
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_JLYQTZ_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBGLZY_JLYQTZ_DELETE'
                }
            ];
        },
        btnList2() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit2',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_JLYQTZ_JLQJMX_EDIT'
                },
                {
                    text: this.$t('GLOBAL._CheckFile'),
                    code: 'load',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_JLYQTZ_JLQJMX_CKWJ'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete2',
                    type: 'red',
                    icon: '',
                    authCode: 'SBGLZY_JLYQTZ_JLQJMX_DELETE'
                }
            ];
        },
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'MeasureNo',
                    label: this.$t('TPM_SBGL_JLRWGL.MeasureNo'),
                    icon: 'mdi-account-check',
                    type: 'input',
                    placeholder: ''
                },
                             {
                    value: '',
                    key: 'AccountAssetNo',
                    label: this.$t('TPM_SBGL_JLRWGL.AccountAssetNo'),
                    icon: 'mdi-account-check',
                    type: 'input',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Name',
                    label: this.$t('TPM_SBGL_JLQJGL.mc'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Status',
                    label: this.$t('TPM_SBGL_JLQJGL.zt'),
                    icon: 'mdi-account-check',
                    selectData: this.Status,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'AdjustmentStatus',
                    label: this.$t('TPM_SBGL_JLQJGL.jyzt'),
                    icon: 'mdi-account-check',
                    selectData: this.AdjustmentStatus,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'VerifyMethod',
                    label: this.$t('TPM_SBGL_JLQJGL.jdff'),
                    icon: 'mdi-account-check',
                    selectData: this.VerifyMethod,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'AccoundType',
                    label: this.$t('TPM_SBGL_JLQJGL.abcfl'),
                    icon: 'mdi-account-check',
                    selectData: this.MeasureType,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'CalibrateDateFrom',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_JLQJGL.jyrqks'),
                    placeholder: this.$t('TPM_SBGL_JLQJGL.jyrqks')
                },
                {
                    value: '',
                    key: 'CalibrateDateTo',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_JLQJGL.jyrqjs'),
                    placeholder: this.$t('TPM_SBGL_JLQJGL.jyrqjs')
                },
                {
                    value: '',
                    key: 'ExpirationDateFrom',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_JLQJGL.yxqks'),
                    placeholder: this.$t('TPM_SBGL_JLQJGL.yxqks')
                },
                {
                    value: '',
                    key: 'ExpirationDateTo',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_JLQJGL.yxqjs'),
                    placeholder: this.$t('TPM_SBGL_JLQJGL.yxqjs')
                }
            ];
        }
    },
    async mounted() {
        let MeasureAccountBy = await GetPersonList('MeasureAccountBy');
        this.MeasureAccountByData = MeasureAccountBy.response[0].ChildNodes;
        this.MeasureAccountByData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        let CalibrationBy = await GetPersonList('CalibrationBy');
        this.CalibrationByData = CalibrationBy.response[0].ChildNodes;
        this.CalibrationByData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.Status = await this.$getNewDataDictionary('MeasureAccountStatus');
        this.AdjustmentStatus = await this.$getNewDataDictionary('MeasureAccountAdjustmentStatus');
        this.VerifyMethod = await this.$getNewDataDictionary('MeasureAccountVerifyMethod');
        this.MeasureType = await this.$getNewDataDictionary('MeasureAccoundType');
        this.VerifyCategory = await this.$getNewDataDictionary('MeasureAccountType');
        console.log(this.VerifyCategory);
        console.log(this.MeasureType);
        this.$refs.createRepast.jyrwList.forEach(item => {
            switch (item.id) {
                case 'VerifyMethod':
                    item.options = this.VerifyMethod;
                    break;
                case 'Type':
                    item.options = this.MeasureType;
                    break;
                case 'Category':
                    item.options = this.VerifyCategory;
                    break;
                case 'VerifyBy':
                    item.options = this.CalibrationByData;
                    break;
            }
        });
        this.$refs.createRepastAdd.AddList.forEach(item => {
            switch (item.id) {
                case 'Type':
                    item.options = this.MeasureType;
                    break;
                case 'VerifyMethod':
                    item.options = this.VerifyMethod;
                    break;
                case 'Category':
                    item.options = this.VerifyCategory;
                    break;
                case 'AdjustmentStatus':
                    item.options = this.AdjustmentStatus;
                    break;
                case 'Status':
                    item.options = this.Status;
                    break;
                case 'Manager':
                    item.options = this.MeasureAccountByData;
                    break;
            }
        });
        this.getTableData();
    },
    methods: {
        async handleExport() {
            let params = {
                ...this.papamstree
            };
            const baseUrl3 = configUrl[process.env.VUE_APP_SERVE]['baseURL_EQUIPMENT'] + `/api/MeasureAccount/ExportData`;
            let res = await GetExportData(baseUrl3, params);
            let binaryData = [];
            binaryData.push(res);
            const url = window.URL.createObjectURL(new Blob(binaryData));
            console.log(url);
            const link = document.createElement('a');
            link.href = url;
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            let fileName = `计量器具台账${formattedDateTime}.xlsx`;
            document.body.appendChild(link);
            link.setAttribute('download', fileName);
            link.click();
            window.URL.revokeObjectURL(link.href);
        },
        handleImport() {
            let _this = this;
            let input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xls,.xlsx';
            input.click();
            let Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            input.onchange = async function () {
                let file = input.files[0];
                let formdata = new FormData();
                formdata.append('file', file);

                _this.importLoading = true;
                try {
                    let res = await GetMeasureAccountImportData(formdata, Factory);
                    _this.$store.commit('SHOW_SNACKBAR', { text: res.response });
                    _this.getTableData();
                    _this.importLoading = false;
                } catch {
                    _this.importLoading = false;
                }
            };
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.getTableData();
        },
        async getTableData() {
            let params = {
                ...this.papamstree
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading = true;
            const res = await GetMeasureAccountPageList(params);
            let { success, response } = res;
            response.data.forEach(item => {
                this.MeasureAccountByData.forEach(it => {
                    if (item.Manager == it.ItemValue) {
                        item.Manager = it.ItemName;
                        item.ManagerValue = it.ItemValue;
                    }
                });
                this.MeasureType.forEach(it => {
                    if (item.Type == it.ItemValue) {
                        item.Type = it.ItemName;
                        item.TypeValue = it.ItemValue;
                    }
                });
                this.AdjustmentStatus.forEach(it => {
                    if (item.AdjustmentStatus == it.ItemValue) {
                        item.AdjustmentStatus = it.ItemName;
                        item.AdjustmentStatusValue = it.ItemValue;
                    }
                });
            });
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        async RepastInfoLogGetPage() {
            if (!this.rowtableItem.ID) {
                return false;
            }
            let params = {
                MeasureId: this.rowtableItem.ID
            };
            this.loading2 = true;
            const res = await GetMeasureCalibrateItemGetList(params);
            let { success, response } = res;
            response.forEach(item => {
                this.CalibrationByData.forEach(it => {
                    if (item.CalibrateBy == it.ItemValue) {
                        item.CalibrateBy = it.ItemName;
                        item.CalibrateValue = it.ItemValue;
                    }
                });
            });
            if (success) {
                this.loading2 = false;
                this.desserts2 = response || [];
            }
        }, // 按钮操作
        //  查看BOM详情
        clickFun(data) {
            this.tableItem = data;
            this.rowtableItem = data || {};
            this.detailShow = true;
            this.RepastInfoLogGetPage();
        },
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.createRepastAdd.AddList.forEach(item => {
                        item.value = '';
                    });
                    this.$refs.createRepastAdd.showDialog = true;
                    return;
                case 'add2':
                    this.dialogType = val;
                    this.$refs.createRepastAdd2.AddList.forEach(item => {
                        item.value = '';
                    });
                    this.$refs.createRepastAdd2.AddList[1].options = this.CalibrationByData;
                    this.$refs.createRepastAdd2.FilePath = [];
                    this.$refs.createRepastAdd2.FileName = [];
                    this.$refs.createRepastAdd2.showDialog = true;
                    setTimeout(() => {
                        this.$refs.createRepastAdd2.clearFiles();
                    }, 200);
                    return;
                case 'jyrw':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.$refs.createRepastAdd.AddList.forEach(item => {
                        for (let k in this.tableItem) {
                            if (item.id == k) {
                                if (item.id == 'AdjustmentStatus') {
                                    item.value = this.tableItem.AdjustmentStatusValue;
                                } else if (item.id == 'Manager') {
                                    item.value = this.tableItem.ManagerValue;
                                } else if (item.id == 'Type') {
                                    item.value = this.tableItem.TypeValue;
                                } else {
                                    item.value = this.tableItem[k];
                                }
                            }
                        }
                    });

                    this.$refs.createRepastAdd.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        tableClick2(item, type) {
            this.dialogType = type;
            this.tableItem2 = item;
            switch (type) {
                case 'load':
                    if (item.FilePath == '' || item.FilePath == null) {
                        Message({
                            message: `${this.$t('GLOBAL.NoFile')}`,
                            type: 'warning'
                        });
                        return false;
                    }
                    this.getImg();
                    return;
                case 'edit2':
                    this.$refs.createRepastAdd2.AddList[1].options = this.CalibrationByData;
                    this.$refs.createRepastAdd2.AddList.forEach(item => {
                        for (let k in this.tableItem2) {
                            if (item.id == k) {
                                if (item.id == 'CalibrateBy') {
                                    item.value = this.tableItem2.CalibrateValue;
                                } else {
                                    item.value = this.tableItem2[k];
                                }
                            }
                        }
                    });
                    this.$refs.createRepastAdd2.FileList = [];
                    // this.$refs.createRepastAdd2.FilePath = this.tableItem2.FilePath;
                    // this.$refs.createRepastAdd2.FileName = this.tableItem2.Filename;
                    if (this.tableItem2.Filename != '' && this.tableItem2.Filename != null) {
                        let filelist = this.tableItem2.Filename.split('|');
                        filelist.forEach(item => {
                            let obj = {
                                name: item
                            };
                            this.$refs.createRepastAdd2.FileList.push(obj);
                        });
                        this.$refs.createRepastAdd2.FileName = this.tableItem2.Filename.split('|');
                        this.$refs.createRepastAdd2.FilePath = this.tableItem2.FilePath.split('|');
                    }
                    this.$refs.createRepastAdd2.showDialog = true;
                    console.log(12);
                    return;
                case 'delete2':
                    this.deltable2();
                    return;
            }
        },
        async MycheckFile(index) {
            let FilePath = this.tableItem2.FilePath.split('|');
            let params = {
                fileName: FilePath[index]
            };
            let res = await GetMeasureCalibrateItemGetFileUrl(params);
            window.open(res.response);
        },
        async getImg() {
            this.Myfilelist = this.tableItem2.Filename.split('|');
            this.CheckFile = true;

            // let params = {
            //     fileName: this.tableItem2.FilePath
            // };
            // let res = await GetMeasureCalibrateItemGetFileUrl(params);
            // window.open(res.response);
        },
        deltable2() {
            let params = [this.tableItem2.ID];
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await GetMeasureCalibrateItemDelete(params);
                    if (res.success) {
                        this.tableItem2 = {};
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.RepastInfoLogGetPage();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await GetMeasureAccountDelete(params);
                    if (res.success) {
                        this.tableItem = {};
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.getTableData();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.getTableData();
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}

.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

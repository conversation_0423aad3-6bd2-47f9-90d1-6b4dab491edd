<template>
    <div class="device-type">
        <SearchForm :show-from="showForm" :searchinput="searchInputs" @searchForm="searchForm"></SearchForm>
        <v-card outlined>
            <div class="form-btn-list">
                <v-btn icon class="float-left mx-4" @click="showForm = !showForm">
                    <v-icon>{{ 'mdi-table-search' }}</v-icon>
                    {{ $t('GLOBAL._SSL') }}
                </v-btn>
                <v-btn icon color="primary" @click="getdata">
                    <v-icon>mdi-cached</v-icon>
                </v-btn>
                <v-btn color="primary" @click="addDeviceType">{{ $t('GLOBAL._XZ') }}</v-btn>
                <v-btn @click="batchDeleteDeviceType">{{ $t('GLOBAL._PLSC') }}</v-btn>
            </div>

            <!-- 表格 -->
            <Tables
                ref="table"
                table-name="DFM_SBLX"
                :loading="loading"
                :tableHeight="showForm ? 'calc(100vh - 225px)' : 'calc(100vh - 175px)'"
                :headers="deviceType"
                :desserts="tableList"
                :page-options="pageData"
                item-key="CATEGORY_ID"
                @selectePages="selectePages"
                @tableClick="tableClick"
            />

            <!-- 编辑弹窗 -->
            <v-dialog v-model="isShowPopup" scrollable width="55%">
                <Popup v-if="isShowPopup" :device-type-obj="deviceTypeObj" @getdata="getdata" @handlePopup="handlePopup" />
            </v-dialog>
        </v-card>
    </div>
</template>

<script>
import { deviceType } from '@/columns/factoryPlant/tableHeaders';
import { getDeviceTypeList, deleteDeviceType } from './service';
import Popup from './components/popup.vue';
const searchInputs = [
    {
        value: '',
        icon: 'mdi-account-check',
        label: '设备类型',
        placeholder: '请输入设备类型',
        key: 'key'
    }
];
export default {
    components: {
        Popup
    },
    data() {
        return {
            isShowPopup: false,
            deviceType,
            searchInputs,
            showForm: false,
            pageData: {
                pageSize: 20,
                page: 1,
                total: 0,
                pageCount: 0,
                pageSizeitems: [10, 20, 30, 40]
            },
            tableList: [],
            selecteds: [],
            deviceTypeObj: {},
            paramObj: {},
            loading: false
        };
    },
    created() {
        this.getdata();
    },
    methods: {
        selectePages(data) {
            this.pageData.page = data.pageCount;
            this.pageData.pageSize = data.pageSize;
            this.getdata();
        },
        // 批量删除路线
        batchDeleteDeviceType() {
            let selecteds = this.$refs.table.selected;
            if (selecteds.length === 0) {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
                return false;
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let ids = [];
                    selecteds.forEach(item => {
                        ids.push(item.CATEGORY_ID);
                    });
                    let resp = await deleteDeviceType(ids);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
                    this.getdata();
                })
                .catch(() => {});
        },
        // 删除路线
        deleteDeviceType(data) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let resp = await deleteDeviceType([data.CATEGORY_ID]);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
                    this.getdata();
                })
                .catch(() => {});
        },
        addDeviceType() {
            this.deviceTypeObj = {};
            this.isShowPopup = true;
        },
        tableClick(item, type) {
            switch (type) {
                case 'edit':
                    this.editDeviceType(item);
                    break;
                case 'delete':
                    this.deleteDeviceType(item);
                    break;
            }
        },
        editDeviceType(item) {
            this.deviceTypeObj = item;
            this.isShowPopup = true;
        },
        handlePopup(boolean) {
            this.isShowPopup = boolean;
        },
        searchForm(params) {
            this.paramObj = params;
            this.getdata();
        },
        async getdata() {
            this.loading = true;
            try {
                let resp = await getDeviceTypeList({ ...this.paramObj, page: this.pageData.page, intPageSize: this.pageData.pageSize });
                this.loading = false;
                this.$refs.table.selected = [];
                this.tableList = resp.response.data;
                this.pageData.total = resp.response.dataCount;
                this.pageData.pageCount = resp.response.pageCount;
            } catch {
                this.loading = false;
            }
        }
    }
};
</script>

<style></style>

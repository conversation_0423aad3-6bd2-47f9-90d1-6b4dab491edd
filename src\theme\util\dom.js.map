{"version": 3, "sources": ["../../src/util/dom.ts"], "names": [], "mappings": ";;;;;;;AAAA;;;;AAIG;AACG,SAAU,YAAV,CAAwB,IAAxB,EAAkC;AACtC;AACA,MAAI,OAAO,IAAI,CAAC,WAAZ,KAA4B,UAAhC,EAA4C;AAC1C;AACA,WAAO,IAAI,CAAC,UAAZ;AAAwB,MAAA,IAAI,GAAG,IAAI,CAAC,UAAZ;AAAxB,KAF0C,CAI1C;;;AACA,QAAI,IAAI,KAAK,QAAb,EAAuB,OAAO,IAAP;AAEvB,WAAO,QAAP;AACD;;AAED,MAAM,IAAI,GAAG,IAAI,CAAC,WAAL,EAAb,CAZsC,CActC;;AACA,MAAI,IAAI,KAAK,QAAT,IAAqB,IAAI,CAAC,WAAL,CAAiB;AAAE,IAAA,QAAQ,EAAE;AAAZ,GAAjB,MAAyC,QAAlE,EAA4E,OAAO,IAAP;AAE5E,SAAO,IAAP;AACD", "sourcesContent": ["/**\n * Returns:\n *  - 'null' if the node is not attached to the DOM\n *  - the root node (HTMLDocument | ShadowRoot) otherwise\n */\nexport function attachedRoot (node: Node): null | HTMLDocument | ShadowRoot {\n  /* istanbul ignore next */\n  if (typeof node.getRootNode !== 'function') {\n    // Shadow DOM not supported (IE11), lets find the root of this node\n    while (node.parentNode) node = node.parentNode\n\n    // The root parent is the document if the node is attached to the DOM\n    if (node !== document) return null\n\n    return document\n  }\n\n  const root = node.getRootNode()\n\n  // The composed root node is the document if the node is attached to the DOM\n  if (root !== document && root.getRootNode({ composed: true }) !== document) return null\n\n  return root as HTMLDocument | ShadowRoot\n}\n"], "sourceRoot": "", "file": "dom.js"}
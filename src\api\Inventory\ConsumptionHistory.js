import { getRequestResources } from '@/api/fetch';
import { data } from 'jquery';
import store from '@/store';
const baseURL = 'baseURL_Inventory'
const baseURL2 = 'baseURL_MATERIAL'
const baseURL3 = 'baseURL_DFM'

export function GetPageList(data) {
    const api = '/api/HistoryView/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMachineGBZ(data) {
    const api = '/api/HistoryView/GetConsumMachineGBZ'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMachineZZ(data) {
    const api = '/api/HistoryView/GetConsumMachineZZ'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetConsumMachineSourceGBZ(data) {
    const api = '/api/HistoryView/GetConsumMachineSourceGBZ'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetConsumMachineSourceZZ(data) {
    const api = '/api/HistoryView/GetConsumMachineSourceZZ'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetListByLevel(data) {
    const api = '/api/Equipment/GetListByLevel?key=Storage'
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetList(data) {
    const api = '/api/Equipment/GetList'
    return getRequestResources(baseURL2, api, 'post', data);
}
export function Recoil(data) {
    const api = '/api/HistoryView/Recoil'
    return getRequestResources(baseURL, api, 'post', data, true);
}
export function RepeatData(data) {
    const api = `/api/HistoryView/RepeatPlan`
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetEntityByQTY(data) {
    const api = '/api/HistoryView/GetEntityByQTY'
    return getRequestResources(baseURL, api, 'post', data, true);
}
export function ConsumptionReportSwitchSaveForm(data) {
    const api = '/api/DataItemDetail/SaveForm'
    return getRequestResources(baseURL3, api, 'post', data).then(resp => {
        // 保存成功后清除缓存，确保下次获取最新数据
        store.dispatch('dictionary/clearDictionary', 'ConsumeReportSwitch');
        return resp;
    });
}
export function GetConsumptionReportSwitch(data) {
    // 检查缓存是否存在且未过期（1小时）
    const cachedData = store.getters['dictionary/getDictionaryByCode']('ConsumeReportSwitch');
    const lastUpdate = store.getters['dictionary/getLastUpdateTime'];
    const oneHour = 24 * 60 * 60 * 1000;
    
    if (cachedData && lastUpdate && (Date.now() - lastUpdate < oneHour)) {
        return Promise.resolve({ response: cachedData });
    }
    
    // 缓存不存在或已过期，从API获取
    const api = '/api/DataItemDetail/GetList?itemCode=ConsumeReportSwitch&lang=cn'
    return getRequestResources(baseURL3, api, 'post', data).then(resp => {
        // 更新缓存
        store.dispatch('dictionary/saveDictionary', {
            code: 'ConsumeReportSwitch',
            data: resp.response
        });
        return resp;
    });
}
export function GetGetConsumedQTY(data) {
    const api = `/api/HistoryView/GetGetConsumedQTY`
    return getRequestResources(baseURL, api, 'post', data);
}
import request from '@/util/request';
export function ExportData(url, data) {
    return request({
        url: url,
        method: 'post',
        data,
        responseType: 'blob'
    });    
}
export function GetConsumeSumList(data) {
    const api = `/api/HistoryView/GetConsumeSumList`
    return getRequestResources(baseURL, api, 'post', data);
}
<template>
    <v-dialog v-model="showDialog" max-width="680px">
        <v-card class="" v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Code" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Code')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Name" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Name')"></v-text-field>
                        </v-col>
                           <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="form.SType" :items="equipmentSpareType" item-text="ItemName" item-value="ItemName" :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Type')" clearable dense outlined></v-select>
                        </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Spec" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Spec')"></v-text-field>
                        </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Min" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Min')"></v-text-field>
                        </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Max" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Max')"></v-text-field>
                        </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Useyears" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Useyears')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Procycle" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Procycle')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                                <v-select :items="unitList" item-text="Unit1" item-value="ID"
                                    :no-data-text="$t('DFM_RL._ZWSJ')" clearable dense v-model="form.Unit" outlined
                                    required :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Unit')" />
                            </v-col>
                            <!-- <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Unit" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Unit')"></v-text-field>
                        </v-col> -->
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="form.Remark" row="1" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Remark')"></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'edit'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XG') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="editedItem.Code" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Code')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="editedItem.Name" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Name')"></v-text-field>
                        </v-col>
                           <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="editedItem.SType" :items="equipmentSpareType" item-text="ItemName" item-value="ItemName" :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Type')" clearable dense outlined></v-select>
                        </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="editedItem.Spec" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Spec')"></v-text-field>
                        </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="editedItem.Min" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Min')"></v-text-field>
                        </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="editedItem.Max" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Max')"></v-text-field>
                        </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="editedItem.Useyears" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Useyears')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="editedItem.Procycle" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Procycle')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                                <v-select :items="unitList" item-text="Unit1" item-value="ID"
                                    :no-data-text="$t('DFM_RL._ZWSJ')" clearable dense v-model="form.Unit" outlined
                                    required :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Unit')" />
                            </v-col>
                            <!-- <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Unit" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Unit')"></v-text-field>
                        </v-col> -->
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="editedItem.Remark" row="1" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Remark')"></v-textarea>
                        </v-col>
                    </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { WarehouseManageSaveForm } from '@/api/equipmentManagement/sparePart.js';

export default {
    props: {
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            unitList:[],
            QRcode: '',
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            equipmentSpareType: [],
            form: {
                Code: '',
                Name: '',
                // ParentId: '',
                Remark: ''
            }
        };
    },
    computed: {
        editedItem() {
            const { Code, Name, ParentId, Remark } = this.tableItem;
            return {
                Code,
                Name,
                // ParentId,
                Remark
            };
        }
    },
    created() {
                this.GetequipmentSpareType();
                this.getUnitData()
    },
    methods: {
            // 获取备件分类
        async GetequipmentSpareType() {
            const res = await this.$getDataDictionary('SpareType');
            this.equipmentSpareType = res || [];
        },
                // 单位下拉框数据
        async getUnitData() {
            let resp = await this.$GetEquipmenByLevel('Unit');
            this.unitList = resp.response;
        },
        // 扫码录入
        closeEquip() {
            this.showDialog = false;
            this.$refs.form.reset();
        },
        async addSave(type) {
            const paramsKey = Object.keys(this.form);
            const paramsObj = type == 'add' ? this.form : this.editedItem;
            let params = {};
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            if (type == 'edit') {
                params.ID = this.tableItem.ID;
            }
            const res = await WarehouseManageSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.RepastInfoGetPage();
                this.showDialog = this.classcheckbox ? false : true;
            }
        },
    }
};
</script>

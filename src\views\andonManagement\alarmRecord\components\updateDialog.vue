<template>
    <!-- 告警升级规则 -->
    <v-dialog v-model="dialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ operaObj.ID ? '编辑告警升级规则' : '新增告警升级规则' }}
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" class="mt-8 mb-2">
                    <v-row>
                        <!-- 一级分类 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.MainAlarmType"
                                :rules="rules.MainAlarmType"
                                :items="typeRootList"
                                item-text="AlarmName"
                                item-value="ID"
                                label="一级分类"
                                return-object
                                dense
                                outlined
                                @change="changeV"
                            ></v-select>
                        </v-col>
                        <!-- 二级分类 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.SubAlarmType"
                                :rules="rules.SubAlarmType"
                                :items="typeChildList"
                                item-text="AlarmName"
                                item-value="ID"
                                label="二级分类"
                                return-object
                                dense
                                outlined
                            ></v-select>
                        </v-col>
                        <!-- 事件等级 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.EventLevel" :rules="rules.EventLevel" label="事件等级" required dense outlined />
                        </v-col>
                        <!-- 超时时长 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.OutTime" :rules="rules.OutTime" label="超时时长" required dense outlined />
                        </v-col>
                        <!-- 负责人职位 -->
                        <v-col :cols="12" :lg="6">
                            <Treeselect
                                noChildrenText="暂无数据"
                                noOptionsText="暂无数据"
                                :normalizer="normalizer"
                                :options="departmentData"
                                placeholder="负责人职位"
                                @select="handleChangeSelectTree"
                                v-model="form.Duty"
                                :rules="rules.Duty"
                            />
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn color="normal" @click="closeForm">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { UpgradeRuleSaveForm, getDepartment } from '@/api/andonManagement/upgradeRule.js';
import { getAlarmTypeRootList, GetListByAlarmId } from '@/api/andonManagement/alarmType.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        warehouseList: {
            type: Array,
            default: () => []
        },
        floorList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            checkbox: true,
            valid: true,
            dialog: false,
            form: {
                ID: '',
                Duty: null,
                MainAlarmType: '',
                SubAlarmType: '',
                EventLevel: '',
                OutTime: ''
            },
            rules: {
                Duty: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                MainAlarmType: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                SubAlarmType: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                OutTime: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                EventLevel: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            },
            departmentData: [],
            normalizer(node) {
                return {
                    id: node.id,
                    label: node.name,
                    children: node.children
                };
            },
            typeRootList: [],
            typeChildList: []
        };
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal) {
                    const { MainAlarmType } = this.operaObj
                    if(MainAlarmType){
                        const o = this.typeRootList.find(i=>i.ID == MainAlarmType)
                        if(o) this.changeV(o)
                    }
                    for (const key in this.form) {
                        if (Object.hasOwnProperty.call(this.form, key)) {
                            this.form[key] = this.operaObj[key];
                        }
                    }
                }
            },
            deep: true,
            immediate: true
        }
    },
    created(){
        this.getDepartmentData()
        this.getTypeRootList()
    },
    methods: {
        // 获取大类列表
        async getTypeRootList() {
            const res = await getAlarmTypeRootList({});
            const { success, response } = res || {};
            if (success) {
                this.typeRootList = response;
            } else {
                this.typeRootList = [];
            }
        },
        // 选择一级告警时获取子级
        async changeV(o) {
            this.typeChildList = []
            this.form.SubAlarmType = ''
            const res = await GetListByAlarmId({alarmId: o.ID});
            const { success, response } = res || {};
            if (success) {
                this.typeChildList = response;
            }
        },
        handleChangeSelectTree(val) {
            
        },
        async getDepartmentData() {
            let resp = await getDepartment();
            this.departmentData = resp.response;
        },
        //
        closeForm() {
            this.$emit('handlePopup', 'refresh');
            this.dialog = false;
        },
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                let infos = {}
                const { MainAlarmType, SubAlarmType } = this.form;
                if (MainAlarmType?.ID) {
                    infos = { MainAlarmType: MainAlarmType.ID };
                }
                if (SubAlarmType?.ID) {
                    infos = { ...infos, SubAlarmType: SubAlarmType.ID };
                }
                const res = await UpgradeRuleSaveForm({ ...this.form, ...infos });
                const { success, msg } = res;
                if(success){
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    this.$refs.form.reset();
                    this.$emit('handlePopup', 'refresh');
                    if (this.operaObj.ID || this.checkbox) {
                        this.closeForm()
                    }
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>
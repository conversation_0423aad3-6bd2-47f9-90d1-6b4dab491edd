<template>
  <span>
    <el-form-item class="mb-2" v-if="option.DownLoadUrl">
      <el-button type="success" icon="el-icon-download" @click="getInfluxOpcTagExport()">{{
          $t('GLOBAL._MBXZ')
        }}
      </el-button>
    </el-form-item>
    <el-form-item class="mb-2" v-if="option.uploadUrl">
      <el-upload
          :disabled="uploadDisable"
          class="upload-demo"
          :action="apiHost"
          :headers="uploadHeaders"
          :show-file-list="false"
          name="File"
          :on-success="handleSuccess"
          :on-error="handleSuccess"
          :on-progress="handleProgress"
      >
        <el-button size="small" type="primary" icon="el-icon-upload2">{{ $t('GLOBAL._DR') }}</el-button>
      </el-upload>
      <!--        <el-button type="success" icon="el-icon-search" @click="getSearchBtn()">{{ $t('GLOBAL._DR') }}</el-button>-->
    </el-form-item>
    <el-form-item class="mb-2" v-if="option.exportUrl">
      <el-button type="success" icon="el-icon-download" @click="getInfluxOpcTagExportFile()">{{
          $t('GLOBAL._DC')
        }}
      </el-button>
    </el-form-item>
  </span>
</template>
<script>
import {configUrl} from "@/config";
import {GetExportFile, GetExportFilePost} from "@/views/factoryPlant/physicalModelNew/service";
export default {
  props: {
    option: {
      type: Object,
      default() {
        return {}
      }
    },
    searchForm:{
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      uploadDisable: false,
      apiHost: configUrl[process.env.VUE_APP_SERVE][this.option.serveIp] + this.option.uploadUrl,
      uploadHeaders:{
        'authorization':'Bearer ' + JSON.parse(sessionStorage.getItem('vma')).auth.access_token
      }
    }
  },
  mounted() {
    console.log(this.uploadHeaders,'access_token')
  },
  methods: {
    //文件上传
    handleSuccess(res) {
      const statusValue=res.success?'success':'error'
      this.$message[statusValue](res.msg)
      this.uploadDisable = false
    },
    handleProgress() {
      this.uploadDisable = true
    },
    async getInfluxOpcTagExport() {
      let params = {};
      const baseUrl3 = configUrl[process.env.VUE_APP_SERVE][this.option.serveIp] + this.option.DownLoadUrl;
      let res = await GetExportFile(baseUrl3, params);
      let binaryData = [];
      binaryData.push(res);
      const url = window.URL.createObjectURL(new Blob(binaryData));
      console.log(url);
      const link = document.createElement('a');
      link.href = url;
      const now = new Date();
      const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
      let fileName = `${this.option.name}${formattedDateTime}.xlsx`;
      document.body.appendChild(link);
      link.setAttribute('download', fileName);
      link.click();
      window.URL.revokeObjectURL(link.href);
    },
    async getInfluxOpcTagExportFile() {
      let params = this.searchForm;
      const baseUrl3 = configUrl[process.env.VUE_APP_SERVE][this.option.serveIp] + this.option.exportUrl;
      let res = await GetExportFilePost(baseUrl3, params);
      let binaryData = [];
      binaryData.push(res);
      const url = window.URL.createObjectURL(new Blob(binaryData));
      console.log(url);
      const link = document.createElement('a');
      link.href = url;
      const now = new Date();
      const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
      let fileName = `${this.option.name}${formattedDateTime}.xlsx`;
      document.body.appendChild(link);
      link.setAttribute('download', fileName);
      link.click();
      window.URL.revokeObjectURL(link.href);
    },
  }
}
</script>

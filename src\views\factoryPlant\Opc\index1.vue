<template>
    <div class="opc-page">
        <v-row class="tool-row mt-1">
            <v-col :cols="12" :lg="3">
                <a-input-search v-model="keywords" enter-button placeholder="Quick Search" @search="onSearch" />
            </v-col>
            <v-col :cols="12" :lg="6" class="pl-0">
                <v-btn>
                    <v-icon left>mdi-cached</v-icon>
                    Refresh</v-btn>
            </v-col>
        </v-row>
        <div class="table-box mt-3" style="height:calc(100vh - 160px)">
            <vxe-table height="auto" class="mytable-scrollbar" :loading="loading" size="mini" border resizable
                ref="table" :data="tableList">
                <vxe-column v-for="(column, index) in opcColumns" :key="index" :width="column.width"
                    :field="column.field" :title="column.title">
                    <template #default="{ row }">
                        <div style="display:inline-block;width:85px"
                            v-if="['LogTransactions', 'BlockRead', 'AllowMultipleInstances'].indexOf(column.field) !== -1">
                            <span class="enabled" v-if="row[column.field]"> <v-icon size="16">mdi-check</v-icon>
                                &nbsp;Enabled</span>
                            <span class="disabled" v-if="!row[column.field]"><v-icon size="15">mdi-cancel</v-icon>
                                &nbsp;Disabled</span>
                        </div>
                        <div v-else-if="column.field == 'Name'"><v-icon @click="openBomDownload(row)"
                                style="cursor: pointer;" size="18" color="#3dcd58">mdi-file-document-outline</v-icon>
                            {{
                row[column.field] }}name</div>
                        <span v-else>{{ row[column.field] }}</span>
                    </template>
                </vxe-column>
            </vxe-table>
            <vxe-pager border size="medium" :loading="loading2" :page-sizes="pageOptions.pageSizeitems"
                :current-page="pageOptions.pageIndex" :page-size="pageOptions.pageSize" :total="pageOptions.total"
                :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
                @page-change="handlePageChange">
            </vxe-pager>
        </div>
        <!-- <v-dialog v-model="isShowBomDownload" persistent scrollable width="55%">
            <bomDownload v-if="isShowBomDownload" :currentObj="currentObj" />
        </v-dialog> -->
        <!-- <a-drawer placement="right" :closable="false" v-model:visible="isShowBomDownload">
            <bomDownload v-if="isShowBomDownload" :currentObj="currentObj" />
        </a-drawer> -->
        <a-drawer placement="right" width="50%" :closable="false" :visible="isShowBomDownload"
            :after-visible-change="afterVisibleChange">
            <bomDownload @closeDrawer="closeDrawer" v-if="isShowBomDownload" :currentObj="currentObj" />
        </a-drawer>
    </div>
</template>

<script>
import { opcColumns } from '@/columns/factoryPlant/Opc.js'
import { getOpcFunc } from './service'
import bomDownload from './components/bomDownload.vue'
export default {
    components: {
        bomDownload
    },
    data() {
        return {
            currentObj: {},
            isShowBomDownload: false,
            loading2: false,
            opcColumns,
            keywords: '',
            loading: false,
            tableList: [],
            pageOptions: {
                total: 200,
                pageIndex: 1,
                pageSize: 20,
                pageSizeitems: [20, 50, 100, 500]
            }
        }
    },
    created() {
        this.getdata()
    },
    methods: {
        closeDrawer() {
            this.isShowBomDownload = false
        },
        afterVisibleChange(val) {
            console.log("val=====", val)
        },
        openBomDownload(data) {
            this.currentObj = data
            this.isShowBomDownload = true
        },
        handlePageChange({ currentPage, pageSize }) {
            this.pageOptions.pageIndex = currentPage
            if (this.pageOptions.pageSize != pageSize) this.pageOptions.pageIndex = 1
            this.pageOptions.pageSize = pageSize
            this.getdata()
        },
        onSearch() {
            this.pageOptions.pageIndex = 1
            this.getdata()
        },
        async getdata() {
            this.loading = true
            try {
                let resp = await getOpcFunc({
                    key: this.keywords,
                    pageIndex: this.pageOptions.pageIndex,
                    pageSize: this.pageOptions.pageSize
                })
                this.pageOptions.total = resp.response.dataCount
                this.tableList = resp.response.data
                this.loading = false
                this.tableList = [{
                    Name: 'AssignDestination',
                    Description: 'Standard interface for verifying pallet information.',
                    OpcActionClassId: 'DMOOPC AssignDestination',
                    Timeout: '30000',
                    Properties: '6',
                    Triggers: '1',
                    LogTransactions: true,
                    BlockRead: false,
                    AllowMultipleInstances: false
                }]
            } catch {
                this.loading = false
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.ant-input-search {
    ::v-deep [type=button] {
        background: #3dcd58;
        border-color: #3dcd58;
    }
}

.enabled,
.disabled {
    color: #fff;
    display: flex;
    align-items: center;
    border-radius: 4px;
    padding: 0 8px;

    .v-icon {
        color: #fff;
    }
}

.enabled {
    background: #008000;
}

.disabled {
    background: #ff0000;
}

::v-deep .ant-drawer-body {
    padding: 12px 10px 24px;
}
</style>
<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <!-- @click="RepastInfoGetPage" -->
                    <v-btn icon color="primary" @click="getTableData">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'SBGLZY_JYRWGL_CJJYRW'" @click="btnClickEvet('jyrw')">{{ $t('TPM_SBGL_JLQJGL.jyrw') }}</v-btn>
                    <!-- <v-btn color="primary" v-has="'SBGLZY_JYRWGL_START'" :disabled="!deleteList.length" @click="ChangeStatus('ks')">{{ $t('GLOBAL._KS') }}</v-btn> -->
                    <v-btn color="primary" v-has="'SBGLZY_JYRWGL_COMPLETE'" :disabled="!deleteList.length" @click="ChangeStatus('wc')">{{ $t('GLOBAL._WC') }}</v-btn>
                    <v-btn color="primary" v-has="'SBGLZY_JYRWGL_QR'" :disabled="!deleteList.length" @click="ChangeStatus('qr')">{{ $t('GLOBAL._QR') }}</v-btn>
                    <!-- <v-btn color="primary" @click="handleExport">{{ $t('GLOBAL._DC') }}</v-btn> -->
                    <!-- <v-btn color="primary" @click="handleImport">{{ $t('GLOBAL._DR') }}</v-btn> -->
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    :clickFun="clickFun"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_JLRWGL"
                    :headers="InspectiontaskColum"
                    :desserts="desserts"
                    @selectePages="selectePages"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"
                ></Tables>
                <createRepast
                    ref="createRepast"
                    :VerifyCategory="VerifyCategory"
                    :VerifyMethod="VerifyMethod"
                    :MeasureType="MeasureType"
                    :dialogType="dialogType"
                    :tableItem="tableItem"
                ></createRepast>
                <createRepastEdit
                    ref="createRepastEdit"
                    :VerifyCategory="VerifyCategory"
                    :VerifyMethod="VerifyMethod"
                    :MeasureType="MeasureType"
                    :dialogType="dialogType"
                    :tableItem="tableItem"
                ></createRepastEdit>
                <createRepastEditStatus
                    ref="createRepastEditStatus"
                    :VerifyCategory="VerifyCategory"
                    :VerifyMethod="VerifyMethod"
                    :MeasureType="MeasureType"
                    :dialogType="dialogType"
                    :tableItem="tableItem"
                ></createRepastEditStatus>
                <createRepastEdit2 ref="createRepastEdit2" :rowtableItem="rowtableItem" :dialogType="dialogType" :tableItem="tableItem2"></createRepastEdit2>
            </v-card>
        </div>
        <el-drawer size="80%" :title="rowtableItem.Wo" :wrapperClosable="false" :visible.sync="detailShow" direction="rtl">
            <v-card class="ma-1">
                <div class="myheight">
                    <Tables
                        :page-options="pageOptions2"
                        :footer="false"
                        :showSelect="false"
                        :loading="loading2"
                        :btn-list="btnList2"
                        tableHeight="calc(100vh - 75px)"
                        table-name="TPM_SBGL_JLRWGL"
                        :headers="InspectiontaskColum2"
                        :desserts="desserts2"
                        @tableClick="tableClick2"
                    ></Tables>
                </div>
            </v-card>
        </el-drawer>
        <el-dialog :title="$t('TPM_SBGL_JLRWGL.bhgqd')" :visible.sync="tablemodel">
            <Tables :footer="false" :showSelect="false" :btn-list="[]" tableHeight="calc(55vh - 135px)" table-name="TPM_SBGL_JLRWGL" :headers="InspectiontaskColum3" :desserts="desserts3"></Tables>
            <span slot="footer" class="dialog-footer">
                <el-button @click="tablemodel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
        <el-dialog :title="$t('GLOBAL._CheckFile')" :visible.sync="CheckFile">
            <div style="display: flex; justify-content: space-evenly">
                <div v-for="(item, index) in Myfilelist" :key="index" style="display: flex; flex-direction: column; align-items: center">
                    <div style="margin-bottom: 10px">{{ item }}</div>
                    <el-button @click="MycheckFile(index)" style="background: #67c23a; color: #fff" class="tablebtn">{{ $t('GLOBAL._CK') }}</el-button>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="CheckFile = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
        <el-dialog :title="$t('GLOBAL._IMGINPORT')" :visible.sync="imgModel">
            <div><img :src="ImgPath" /></div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="imgModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
        <div class="loading-box" v-if="importLoading">
            <a-spin tip="导入中..." :spinning="importLoading"></a-spin>
        </div>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';
import {
    GetMeasureCalibrateWoPageList,
    GetMeasureCalibrateWoImportData,
    GetMeasureCalibrateWoStart,
    GetMeasureCalibrateWoFinish,
    GetMeasureCalibrateWoConfirm,
    GetMeasureCalibrateWoCancel,
    GetMeasureCalibrateItemNGList,
    GetMeasureCalibrateItemList,
    GetMeasureCalibrateItemDelete,
    GetMeasureCalibrateItemGetFileUrl
} from '@/api/equipmentManagement/Inspectiontask.js';
import { configUrl } from '@/config';
import { Message } from 'element-ui';
import { GetExportData, GetPersonList } from '@/api/equipmentManagement/Equip.js';

import { InspectiontaskColum, InspectiontaskColum2, InspectiontaskColum3 } from '@/columns/equipmentManagement/Inspectiontask.js';
export default {
    name: 'RepastModel',
    components: {
        createRepast: () => import('./components/createRepast.vue'),
        createRepastEditStatus: () => import('./components/createRepastEditStatus.vue'),
        createRepastEdit: () => import('./components/createRepastEdit.vue'),
        createRepastEdit2: () => import('./components/createRepastEdit2.vue')
    },
    data() {
        return {
            detailShow: false,
            ImgPath: '',
            CheckFile: false,
            imgModel: false,
            // tree 字典数据
            tablemodel: false,
            importLoading: false,
            desserts3: [],
            tab: null,
            loading: false,
            showFrom: false,
            papamstree: {
                VerifyMethod: '',
                Type: '',
                ExpirationDateStart: '',
                ExpirationDateEnd: '',
                pageIndex: 1,
                pageSize: 20
            },
            InspectiontaskColum,
            InspectiontaskColum2,
            InspectiontaskColum3,
            //查询条件
            desserts: [],
            loading2: false,
            desserts2: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            pageOptions2: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            VerifyMethod: [],
            MeasureType: [],
            VerifyCategory: [],
            tableItem2: {},
            rowtableItem: {},
            CalibrationByData: [],
            MeasureCalibrateWoStatus: [],
            Myfilelist: [],
            IsNG: [],
            deleteList: [] //批量选中
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._KS'),
                    code: 'ks',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_JYRWGL_EDIT'
                },
                {
                    text: this.$t('GLOBAL._WC'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_JYRWGL_EDIT'
                },
                {
                    text: this.$t('GLOBAL._QX'),
                    code: 'qx',
                    type: 'red',
                    icon: '',
                    authCode: 'SBGLZY_JYRWGL_QX'
                },
                {
                    text: this.$t('TPM_SBGL_JLRWGL.bhgqd'),
                    code: 'bhgqd',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_JYRWGL_BHGQD'
                },
                {
                    text: this.$t('GLOBAL._DR'),
                    code: 'dr',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_JYRWGL_DR'
                },
                {
                    text: this.$t('GLOBAL._DC'),
                    code: 'dc',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_JYRWGL_DC'
                }
            ];
        },
        btnList2() {
            return [
                {
                    text: this.$t('GLOBAL._XG'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_JYRWGL_RWMX_DEIT'
                },
                {
                    text: this.$t('GLOBAL._CheckFile'),
                    code: 'load',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_JYRWGL_RWMX_CKWJ'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBGLZY_JYRWGL_RWMX_DELETE'
                }
            ];
        },
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'VerifyMethod',
                    label: this.$t('TPM_SBGL_JLRWGL.jdff'),
                    icon: 'mdi-account-check',
                    selectData: this.VerifyMethod,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Type',
                    label: this.$t('TPM_SBGL_JLRWGL.abcfl'),
                    icon: 'mdi-account-check',
                    selectData: this.MeasureType,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'ExpirationDateStart',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_JLQJGL.jyrqks'),
                    placeholder: this.$t('TPM_SBGL_JLQJGL.jyrqks')
                },
                {
                    value: '',
                    key: 'ExpirationDateEnd',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_JLQJGL.jyrqjs'),
                    placeholder: this.$t('TPM_SBGL_JLQJGL.jyrqjs')
                },
                {
                    multiple: true,
                    value: '',
                    byValue: 'ItemValue',
                    selectData: this.CalibrationByData,
                    key: 'VerifyByValue',
                    type: 'select',
                    max: 5,
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_JLQJGL.zrr'),
                    placeholder: this.$t('TPM_SBGL_JLQJGL.zrr')
                }
            ];
        }
    },
    async mounted() {
        this.MeasureCalibrateWoStatus = await this.$getNewDataDictionary('MeasureCalibrateWoStatus');
        this.$refs.createRepastEditStatus.AddList[0].options = this.MeasureCalibrateWoStatus;
        let CalibrationBy = await GetPersonList('CalibrationBy');
        this.CalibrationByData = CalibrationBy.response[0].ChildNodes;
        this.CalibrationByData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.VerifyCategory = await this.$getNewDataDictionary('MeasureAccountType');
        this.MeasureType = await this.$getNewDataDictionary('MeasureAccoundType');
        this.VerifyMethod = await this.$getNewDataDictionary('MeasureAccountVerifyMethod');
        this.$refs.createRepast.jyrwList.forEach(item => {
            switch (item.id) {
                case 'VerifyMethod':
                    item.options = this.VerifyMethod;
                    break;
                case 'Type':
                    item.options = this.MeasureType;
                    break;
                case 'Category':
                    item.options = this.VerifyCategory;
                    break;
                case 'VerifyBy':
                    item.options = this.CalibrationByData;
                    break;
            }
        });
        this.$refs.createRepastEdit.AddList.forEach(item => {
            switch (item.id) {
                case 'VerifyMethod':
                    item.options = this.VerifyMethod;
                    break;
                case 'Type':
                    item.options = this.MeasureType;
                    break;
                case 'Category':
                    item.options = this.VerifyCategory;
                    break;
                case 'VerifyBy':
                    item.options = this.CalibrationByData;
                    break;
            }
        });
        this.IsNG = await this.$getNewDataDictionary('IsNG');
        this.$refs.createRepastEdit2.AddList[3].options = this.IsNG;
        this.$refs.createRepastEdit2.AddList[4].options = this.MeasureCalibrateWoStatus;
        this.getTableData();
    },
    methods: {
        async handleExport(item) {
            let params = {
                ...this.papamstree
            };
            params.WoId = item.ID;
            const baseUrl3 = configUrl[process.env.VUE_APP_SERVE]['baseURL_EQUIPMENT'] + `/api/MeasureCalibrateItem/ExportData`;
            let res = await GetExportData(baseUrl3, params);
            let binaryData = [];
            binaryData.push(res);
            const url = window.URL.createObjectURL(new Blob(binaryData));
            console.log(url);
            const link = document.createElement('a');
            link.href = url;
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            let fileName = `校准任务管理${formattedDateTime}.xlsx`;
            document.body.appendChild(link);
            link.setAttribute('download', fileName);
            link.click();
            window.URL.revokeObjectURL(link.href);
        },
        handleImport(item) {
            let _this = this;
            let input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xls,.xlsx';
            input.click();
            let Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            input.onchange = async function () {
                let file = input.files[0];
                let formdata = new FormData();
                formdata.append('file', file);
                _this.importLoading = true;
                try {
                    let res = await GetMeasureCalibrateWoImportData(formdata, Factory);
                    _this.$store.commit('SHOW_SNACKBAR', { text: res.response });
                    _this.getTableData();
                    _this.importLoading = false;
                } catch {
                    _this.importLoading = false;
                }
            };
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.VerifyBy = this.papamstree.VerifyByValue.join('|');
            this.papamstree.pageIndex = 1;
            this.getTableData();
        },
        async getTableData() {
            let params = {
                ...this.papamstree
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading = true;
            const res = await GetMeasureCalibrateWoPageList(params);
            let { success, response } = res;
            for (let k in response.data) {
                let item = response.data[k];
                item.CreateUserName = await this.$getPerson(item.CreateUserId);
                this.MeasureCalibrateWoStatus.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                        item.StatusValue = it.ItemValue;
                    }
                });
                this.MeasureType.forEach(it => {
                    if (item.Type == it.ItemValue) {
                        item.Type = it.ItemName;
                        item.TypeValue = it.ItemValue;
                    }
                });
                let myVerifyBy = [];
                let myVerifyByValue = [];
                this.CalibrationByData.forEach(it => {
                    let VerifyByList = item.VerifyBy.split('|');
                    VerifyByList.forEach(it2 => {
                        if (it2 == it.ItemValue) {
                            myVerifyBy.push(it.ItemName);
                            myVerifyByValue.push(it.ItemValue);
                        }
                    });
                    item.myVerifyByValue = myVerifyByValue;
                    item.VerifyByName = myVerifyBy.join('|');
                });
            }
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        async RepastInfoLogGetPage() {
            if (!this.rowtableItem.ID) {
                return false;
            }
            let params = {
                WoId: this.rowtableItem.ID
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading2 = true;
            const res = await GetMeasureCalibrateItemList(params);
            let { success, response } = res;
            response.forEach(item => {
                this.MeasureCalibrateWoStatus.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                        item.StatusValue = it.ItemValue;
                    }
                });
                this.IsNG.forEach(it => {
                    if (item.Result == it.ItemValue) {
                        item.Result = it.ItemName;
                        item.ResultValue = it.ItemValue;
                    }
                });
            });
            if (success) {
                this.loading2 = false;
                this.desserts2 = response || [];
            }
        }, // 按钮操作
        //  查看BOM详情
        clickFun(data) {
            this.tableItem = data;
            this.rowtableItem = data || {};
            this.detailShow = true;
            this.RepastInfoLogGetPage();
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'jyrw':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
            }
        },
        async ChangeStatus(type) {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            let res;
            switch (type) {
                case 'ks':
                    res = await GetMeasureCalibrateWoStart(params);
                    break;
                case 'wc':
                    res = await GetMeasureCalibrateWoFinish(params);
                    break;
                case 'qr':
                    res = await GetMeasureCalibrateWoConfirm(params);
                    break;
                case 'qx':
                    res = await GetMeasureCalibrateWoCancel(params);
                    break;
            }
            if (res.success) {
                this.tableItem = {};
                this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
                this.getTableData();
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.$refs.createRepastEdit.AddList.forEach(item => {
                        for (let k in this.tableItem) {
                            if (item.id == k) {
                                if (item.id == 'VerifyBy') {
                                    item.value = this.tableItem.myVerifyByValue;
                                } else if (item.id == 'Type') {
                                    item.value = this.tableItem.TypeValue;
                                } else {
                                    item.value = this.tableItem[k];
                                }
                            }
                        }
                    });
                    this.$refs.createRepastEdit.showDialog = true;
                    return;
                case 'ks':
                    console.log(this.$refs.createRepastEditStatus);
                    this.$refs.createRepastEditStatus.AddList[0].value = this.tableItem.StatusValue;
                    this.$refs.createRepastEditStatus.showDialog = true;
                    return;

                case 'qx':
                    this.ChangeStatus('qx');
                    return;
                case 'bhgqd':
                    this.Getbhgqd();
                    return;
                case 'dr':
                    this.handleImport(item);
                    return;
                case 'dc':
                    this.handleExport(item);
                    return;
            }
        },
        async Getbhgqd() {
            if (!this.tableItem.ID) {
                return false;
            }
            let params = {
                WoId: this.tableItem.ID
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            const res = await GetMeasureCalibrateItemNGList(params);
            let { success, response } = res;
            if (success) {
                response.forEach(item => {
                    this.MeasureCalibrateWoStatus.forEach(it => {
                        if (item.Status == it.ItemValue) {
                            item.Status = it.ItemName;
                            item.StatusValue = it.ItemValue;
                        }
                    });
                    this.IsNG.forEach(it => {
                        if (item.Result == it.ItemValue) {
                            item.Result = it.ItemName;
                            item.ResultValue = it.ItemValue;
                        }
                    });
                });
                this.desserts3 = response || [];
            }
            this.tablemodel = true;
        },
        tableClick2(item, type) {
            this.dialogType = type;
            this.tableItem2 = item;
            switch (type) {
                case 'edit':
                    this.$refs.createRepastEdit2.FileList = [];
                    if (this.tableItem2.Filename != '' && this.tableItem2.Filename != null) {
                        let filelist = this.tableItem2.Filename.split('|');
                        filelist.forEach(item => {
                            let obj = {
                                name: item
                            };
                            this.$refs.createRepastEdit2.FileList.push(obj);
                        });
                        this.$refs.createRepastEdit2.FileName = this.tableItem2.Filename.split('|');
                        this.$refs.createRepastEdit2.FilePath = this.tableItem2.FilePath.split('|');
                    }
                    this.$refs.createRepastEdit2.AddList.forEach(item => {
                        for (let k in this.tableItem2) {
                            if (item.id == k) {
                                if (item.id == 'Status') {
                                    item.value = this.tableItem2.StatusValue;
                                } else if (item.id == 'Result') {
                                    item.value = this.tableItem2.ResultValue;
                                } else {
                                    item.value = this.tableItem2[k];
                                }
                            }
                        }
                    });
                    this.$refs.createRepastEdit2.showDialog = true;
                    return;
                case 'delete':
                    this.deltable2();
                    return;
                case 'load':
                    if (item.Filename == '' || item.Filename == null) {
                        Message({
                            message: `${this.$t('GLOBAL.NoFile')}`,
                            type: 'warning'
                        });
                        return false;
                    }
                    this.getImg();
                    return;
            }
        },
        async MycheckFile(index) {
            let FilePath = this.tableItem2.FilePath.split('|');
            let params = {
                fileName: FilePath[index]
            };
            let res = await GetMeasureCalibrateItemGetFileUrl(params);
            window.open(res.response);
        },
        async getImg() {
            this.Myfilelist = this.tableItem2.Filename.split('|');
            this.CheckFile = true;
        },
        deltable2() {
            let params = [this.tableItem2.ID];
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await GetMeasureCalibrateItemDelete(params);
                    if (res.success) {
                        this.tableItem2 = {};
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.RepastInfoLogGetPage();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
        }
    }
};
</script>
<style lang="scss">
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
    .el-drawer__body {
        .myheight {
            .v-data-table__wrapper {
                height: calc(-75px + 100vh) !important;
            }
        }
    }
}

.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

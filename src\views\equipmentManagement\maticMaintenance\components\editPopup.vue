<template>
    <v-card>
        <v-card-title v-drag class="headline primary lighten-2" primary-title>{{ editItemObj.ID ? $t('GLOBAL._BJ') :
            $t('GLOBAL._XZ') }}</v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid">
                <v-row class="mt-5">
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_ZNYWPZ.Factory')" :rules="[v => !!v || $t('GLOBAL._MANDATORY')]"
                            required dense outlined v-model="form.Factory"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select :items="seriesList" item-text="ItemName" item-value="ItemValue" clearable dense
                            v-model="form.ProductSeries" outlined required :label="$t('DFM_ZNYWPZ.ProductSeries')" />
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select :items="monitorTypeList" item-text="ItemName" item-value="ItemValue" clearable dense
                            v-model="form.TypeCode" outlined required :label="$t('DFM_ZNYWPZ.TypeName')" />
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_ZNYWPZ.Building')" required dense outlined
                            v-model="form.Building"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_ZNYWPZ.Floor')" required dense outlined
                            v-model="form.Floor"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select :items="lineList" @click:clear="handleChangeLine" @change="handleChangeLine"
                            item-text="EquipmentName" item-value="EquipmentCode" dense clearable v-model="form.Linecode"
                            outlined required :label="$t('DFM_ZNYWPZ.Linename')" />
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select :items="segmentList" @change="handleChangeSegment" item-text="EquipmentName"
                            item-value="EquipmentCode" dense clearable @click:clear="handleChangeSegment"
                            v-model="form.SegmentCode" outlined required :label="$t('DFM_ZNYWPZ.SegmentName')" />
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select :items="workstationList" @change="handleChangeWorkstation"
                            @click:clear="handleChangeWorkstation" item-text="EquipmentName" item-value="EquipmentCode"
                            dense clearable v-model="form.StationLinecode" outlined required
                            :label="$t('DFM_ZNYWPZ.StationLinename')" />
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select :items="deviceList" item-text="EquipmentName" item-value="EquipmentCode" clearable dense
                            v-model="form.Device_Code" outlined required :label="$t('DFM_ZNYWPZ.Device_Name')" />
                    </v-col>
                    <!-- <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field label="标签名称" required dense outlined v-model="form.MonitorType"></v-text-field>
                    </v-col> -->
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_ZNYWPZ.DataTag')" required dense outlined
                            v-model="form.DataTag"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_ZNYWPZ.DataFrom')" required dense outlined
                            v-model="form.DataFrom"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_ZNYWPZ.Position')" required dense outlined
                            v-model="form.Position"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_ZNYWPZ.Duty_Person')" required dense outlined
                            v-model="form.Duty_Person"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_ZNYWPZ.Recipient')" required dense outlined
                            v-model="form.Recipient"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_ZNYWPZ.GroupRecipient')" required dense outlined
                            v-model="form.GroupRecipient"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select :items="statusList" item-text="ItemName" item-value="ItemValue" clearable dense
                            v-model="form.State" outlined required :label="$t('DFM_ZNYWPZ.State')" />
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select :items="typeList" item-text="name" item-value="value" clearable dense
                            v-model="form.DeviceType" outlined required :label="$t('DFM_ZNYWPZ.DeviceType')" />
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
            <!-- <v-spacer></v-spacer> -->
            <v-checkbox class="mr-auto" v-model="isChecked" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { EquipmentGetPageList } from '@/api/common.js';
import { saveConfigForm } from '../service';
import Util from '@/util';
export default {
    props: {
        editItemObj: {
            type: Object,
            default: () => { }
        },
        seriesList: {
            type: Array,
            default: () => []
        },
        statusList: {
            type: Array,
            default: () => []
        },
        monitorTypeList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            typeList: [
                { name: '工单维度', value: '1' },
                { name: '公共设备', value: '2' }
            ],
            isChecked: true,
            valid: false,
            form: {
                GroupRecipient: '',
                Recipient: '',
                Duty_Person: '',
                Factory: '',
                ProductSeries: '',
                Linecode: '',
                SegmentCode: '',
                StationLinecode: '',
                Device_Code: '',
                Building: '',
                Floor: '',
                Position: '',
                // MonitorType: '',
                DataTag: '',
                DataFrom: '',
                State: '',
                TypeCode: '',
                Scadatag: '',
                DeviceType: ''
            },
            lineList: [],
            segmentList: [],
            workstationList: [],
            deviceList: []
        };
    },
    async created() {
        await this.getLineData();
        if (this.editItemObj && this.editItemObj.ID) {
            for (const key in this.form) {
                this.form[key] = this.editItemObj[key];
            }
            this.form.ID = this.editItemObj.ID;
            if (this.form.Linecode) {
                await this.handleChangeLine(this.form.Linecode, 'init');
            }
            if (this.form.SegmentCode) {
                await this.handleChangeSegment(this.form.SegmentCode, 'init');
            }
            if (this.form.StationLinecode) {
                this.handleChangeWorkstation(this.form.StationLinecode, 'init');
            }
        }
    },
    methods: {
        // 获取产线列表
        async getLineData() {
            this.lineList = await Util.GetEquipmenByLevel('Area');
        },

        async handleChangeWorkstation(val, type) {
            if (!type) {
                this.deviceList = [];
                this.form.Scadatag = '';
            }
            if (!val) return false;
            let obj = this.workstationList.find(item => item.EquipmentCode == val);
            const { ID } = obj;
            const res = await EquipmentGetPageList({ DataItemCode: 'EquipmentLevel', ParentId: ID, pageIndex: 1, pageSize: 9999 });
            const { success, response } = res;
            if (success) {
                this.deviceList = response.data;
            }
        },
        async handleChangeSegment(val, type) {
            if (!type) {
                this.form.workstation = '';
                this.workstationList = [];
                this.deviceList = [];
                this.form.Scadatag = '';
            }
            if (!val) return false;
            let obj = this.segmentList.find(item => item.EquipmentCode == val);
            const { ID } = obj;
            const res = await EquipmentGetPageList({ DataItemCode: 'EquipmentLevel', ParentId: ID, pageIndex: 1, pageSize: 9999 });
            const { success, response } = res;
            if (success) {
                this.workstationList = response.data;
            }
        },
        async handleChangeLine(val, type) {
            if (!type) {
                this.form.SegmentCode = '';
                this.segmentList = [];
                this.workstationList = [];
                this.deviceList = [];
                this.form.workstation = '';
                this.form.Scadatag = '';
            }
            let obj = this.lineList.find(item => item.EquipmentCode == val);
            if (!obj) return false;
            const { ID } = obj;
            const res = await EquipmentGetPageList({ DataItemCode: 'EquipmentLevel', ParentId: ID, pageIndex: 1, pageSize: 9999 });
            const { success, response } = res;
            if (success) {
                this.segmentList = response.data;
            }
        },
        resetForm() {
            this.$refs.form.reset();
        },
        async submitForm() {
            if (!this.$refs.form.validate()) return false;
            let params = JSON.parse(JSON.stringify(this.form));
            params.Linename = this.lineList.find(item => item.EquipmentCode == params.Linecode)?.EquipmentName;
            params.SegmentName = this.segmentList.find(item => item.EquipmentCode == params.SegmentCode)?.EquipmentName;
            params.StationLinename = this.workstationList.find(item => item.EquipmentCode == params.StationLinecode)?.EquipmentName;
            params.Device_Name = this.deviceList.find(item => item.EquipmentCode == params.Device_Code)?.EquipmentName;
            params.TypeName = this.monitorTypeList.find(item => item.ItemValue == params.TypeCode)?.ItemName;
            let resp = await saveConfigForm({ ...params });
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
            this.resetForm();
            this.$emit('getdata');
            if (this.isChecked) {
                this.$emit('closePopup');
            }
        },
        closePopup() {
            this.$emit('closePopup');
        }
    }
};
</script>

<style></style>
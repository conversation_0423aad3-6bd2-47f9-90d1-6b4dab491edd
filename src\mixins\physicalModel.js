// 搜索栏公用组件多级联动
export default {
    data() {
        return {
            firstLevel: [],
            secondLevel: [],
            thirthLevel: [],
            fourthLevel: [],
            fivthLevel: [],
            sixthLevel: [],
            linkageList: ['firstLevel', 'secondLevel', 'thirthLevel', 'fourthLevel', 'fivthLevel']
        }
    },
    methods: {
        filterList(a, id) {
            const arr = a || [];
            const result = arr.filter(i => i.ParentId == id);
            return result || [];
        },
        selectChange(item, data) {
            const { linkageKey, value, childrenLinkageArray } = item;
            const boo = value && childrenLinkageArray;
            let id = '';
            const flag = this.linkageList.indexOf(linkageKey) > -1 && childrenLinkageArray?.length > 0;
            if (flag) {
                switch (linkageKey) {
                    case 'firstLevel':
                        this.secondLevel = [];
                        this.thirthLevel = [];
                        this.fourthLevel = [];
                        if (boo) {
                            id = value.ID;
                            this.secondLevel = this.filterList(childrenLinkageArray, id);
                        }
                        break;

                    case 'secondLevel':
                        this.thirthLevel = [];
                        this.fourthLevel = [];
                        if (boo) {
                            id = value.ID;
                            this.thirthLevel = this.filterList(childrenLinkageArray, id);
                        }
                        break;

                    case 'thirthLevel':
                        this.fourthLevel = [];
                        if (boo) {
                            id = value.ID;
                            this.fourthLevel = this.filterList(childrenLinkageArray, id);
                        }
                        break;

                    case 'fivthLevel':
                        this.sixthLevel = [];
                        if (boo) {
                            id = value.ID;
                            this.sixthLevel = this.filterList(childrenLinkageArray, id);
                        }
                        break;

                    default:
                        break;
                }
                for (const k in data) {
                    this.searchinput.map(item => {
                        if (item.key == k) {
                            item.value = data[k];
                        }
                    });
                }
            }
        }
    },
}
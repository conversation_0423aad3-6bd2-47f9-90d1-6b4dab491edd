export const FeedingManagementColum = [
    // {
    //     text: '序号',
    //     value: 'Index',
    //     sortable: true
    // },
    {
        text: '工单',
        value: 'WoCode',
        width: '160px',
        sortable: true
    },
    { text: '成品物料', value: 'SapWoProductionCode', width: 190, dictionary: true },
    {
        text: '物料号',
        value: 'MaterialCode',
        width: '160px',
        sortable: true
    },
    {
        text: '物料描述',
        value: 'MaterialDescription',
        width: '160px',
        sortable: true
    },
    {
        text: '产品线',
        value: 'FullLineName',
        width: '160px',
        sortable: true
    },
    {
        text: '工段',
        value: 'CompanyName',
        width: '160px',
        sortable: true
    },
    {
        text: '班组',
        value: 'TeamName',
        width: '100px',
        sortable: true
    },
    {
        text: '班次',
        value: 'ShiftName',
        width: '100px',
        sortable: true
    },
    {
        text: '工站',
        value: 'ProcName',
        width: '160px',
        sortable: true
    },
    {
        text: '生产批号',
        value: 'MaterialProductBatchNo',
        width: '160px',
        sortable: true
    },
    {
        text: '追溯批号',
        value: 'MaterialBatchNo',
        width: '160px',
        sortable: true
    },
    {
        text: '上料数量',
        value: 'BatchQuantity',
        width: '100px',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: '单位',
        value: 'FeedUnit',
        width: '100px',
        sortable: true
    },
    {
        text: '操作员',
        value: 'ModifyUserId',
        width: '120px',
        sortable: true
    },
    {
        text: '上料时间',
        value: 'CreateDate',
        width: '160px',
        sortable: true
    },
    {
        text: '操作',
        width: '120px',
        value: 'actions'
    }
];
export const FeedingManagementColumNoAction = [
    // {
    //     text: '序号',
    //     value: 'Index',
    //     sortable: true
    // },
    {
        text: '工单',
        value: 'WoCode',
        width: '160px',
        sortable: true
    },
    {
        text: '产品线',
        value: 'FullLineName',
        width: '160px',
        sortable: true
    },
    {
        text: '工段',
        value: 'CompanyName',
        width: '160px',
        sortable: true
    },
    {
        text: '班组',
        value: 'TeamName',
        width: '100px',
        sortable: true
    },
    {
        text: '班次',
        value: 'ShiftName',
        width: '100px',
        sortable: true
    },
    {
        text: '工站',
        value: 'ProcName',
        width: '160px',
        sortable: true
    },
    {
        text: '物料号',
        value: 'MaterialCode',
        width: '160px',
        sortable: true
    },
    {
        text: '物料描述',
        value: 'MaterialDescription',
        width: '160px',
        sortable: true
    },
    {
        text: '生产批号',
        value: 'MaterialProductBatchNo',
        width: '160px',
        sortable: true
    },
    {
        text: '追溯批号',
        value: 'MaterialBatchNo',
        width: '160px',
        sortable: true
    },
    {
        text: '上料数量',
        value: 'BatchQuantity',
        width: '100px',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: '单位',
        value: 'FeedUnit',
        width: '100px',
        sortable: true
    },
    {
        text: '操作员',
        value: 'ModifyUserId',
        width: '120px',
        sortable: true
    },
    {
        text: '上料时间',
        value: 'CreateDate',
        width: '160px',
        sortable: true
    },
    {
        text: '操作',
        width: '0',
        value: 'noActions'
    }
];

export const mpmColum = [
    {
        text: '序号',
        value: 'Index',
        width: '80px'
    },
    {
        text: '员工号',
        value: 'PersonnelCode',
        width: '100px'
    },
    // {
    //     text: '员工名字',
    //     value: 'PersonnelName',
    //     width: '120px'
    // },
    {
        text: '上岗时间',
        value: 'Logindt',
        width: '200px'
    },
    {
        text: '离岗时间',
        value: 'Logoutdt',
        width: '200px'
    },
    {
        text: '产线',
        value: 'ProductlineName',
        width: '120px',
        sortable: true
    },
    {
        text: '产线编码',
        value: 'ProductlineCode',
        width: '160px',
        sortable: true
    },
    {
        text: '工站',
        value: 'SegmentName',
        width: '180px',
        sortable: true
    },
    {
        text: '工站编码',
        value: 'SegmentCode',
        width: '100px',
        sortable: true
    },
    {
        text: '设备',
        value: 'UnitName',
        width: '160px',
        sortable: true
    },
    {
        text: '设备号',
        value: 'UnitCode',
        width: '160px',
        sortable: true
    },
    {
        text: '',
        width: '120px',
        value: 'noActions'
    }
];
<!-- eslint-disable vue/valid-v-slot -->
<template>
    <v-card>
        <v-card-title class="headline primary lighten-2" primary-title>{{ $t('DFM_RL._BJRL') }}</v-card-title>
        <v-card-text>
            <v-row class="mt-4">
                <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                    <v-text-field :label="$t('DFM_RL._BM')" disabled dense outlined :value="currentDeparment.name">
                    </v-text-field>
                </v-col>
                <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                    <v-text-field :label="$t('DFM_RL._RQ')" disabled dense outlined :value="editDay"></v-text-field>
                </v-col>
                <v-col :cols="12" :lg="4" class="pt-0 pb-0">
                    <v-select :items="classList" item-text="Name" item-value="ID" :no-data-text="$t('DFM_RL._ZWSJ')"
                        clearable dense v-model="classes" outlined multiple @change="handleChangeClass"
                        :label="$t('DFM_RL._XZBC')" />
                </v-col>
            </v-row>
            <v-data-table :items-per-page='999' :headers="headers" hide-default-footer dense height="200"
                :items="selectClassList">
                <template #item.Team="{ item }">
                    <v-row class="mt-2">
                        <v-col :cols="12" :lg="8" class="pt-0 pb-0">
                            <v-select :items="teamList" item-text="Name" item-value="ID"
                                :no-data-text="$t('DFM_RL._ZWSJ')" clearable dense v-model="item.Teamid" outlined
                                :label="$t('DFM_RL._XZBZ')" />
                        </v-col>
                    </v-row>
                </template>
                <template #item.time="{ item }">
                    <v-row>
                        <v-col :cols="12" :lg="4">
                            <v-menu :ref="item.ID" v-model="item.menu" :close-on-content-click="false" :nudge-right="40"
                                :return-value.sync="item.STARTTIME" transition="scale-transition" offset-y
                                max-width="290px" min-width="290px">
                                <template #activator="{ on, attrs }">
                                    <v-text-field :value="item.STARTTIME" v-model="item.STARTTIME"
                                        :label="$t('DFM_RL._KSSJ')" readonly v-bind="attrs" v-on="on"></v-text-field>
                                </template>
                                <v-time-picker v-if="item.menu" v-model="item.STARTTIME" full-width
                                    @click:minute="$refs[item.ID].save(item.STARTTIME)"></v-time-picker>
                            </v-menu>
                        </v-col>
                        <v-col :cols="12" :lg="4">
                            <v-menu :ref="item.ID + '1'" v-model="item.menu1" :close-on-content-click="false"
                                :nudge-right="40" :return-value.sync="item.ENDTIME" transition="scale-transition"
                                offset-y max-width="290px" min-width="290px">
                                <template #activator="{ on, attrs }">
                                    <v-text-field :value="item.ENDTIME" v-model="item.ENDTIME"
                                        :label="$t('DFM_RL._JSSJ')" readonly v-bind="attrs" v-on="on"></v-text-field>
                                </template>
                                <v-time-picker v-if="item.menu1" v-model="item.ENDTIME" full-width
                                    @click:minute="$refs[item.ID + '1'].save(item.ENDTIME)"></v-time-picker>
                            </v-menu>
                        </v-col>
                    </v-row>
                </template>
            </v-data-table>
        </v-card-text>

        <v-divider></v-divider>

        <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="submitForm">{{ $t('DFM_RL._WC') }}</v-btn>
            <v-btn class="white--text" color="#9E9E9E" @click="closePopup">{{ $t('DFM_RL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { getClassesList, getTeamList, saveCalendar } from '../service';
export default {
    props: {
        currentDeparment: {
            type: Object,
            default: () => { }
        },
        editDay: {
            type: String,
            default: ''
        },
        editDayData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            teamList: [],
            selectClassList: [],
            teamTable: [],
            classList: [],
            classes: [],
            team: []
        };
    },
    computed: {
        headers() {
            return [
                { text: this.$t('DFM_RL._BC'), value: 'Name', width: 100 },
                { text: this.$t('DFM_RL._BZU'), value: 'Team', width: 200 },
                { text: this.$t('DFM_RL._BCSJ'), value: 'time', width: 150 }
            ];
        }
    },
    async created() {
        await this.getClassList();
        await this.getTeamList();
        this.init();
    },
    methods: {
        init() {
            this.editDayData.forEach(item => {
                this.classes.push(item.Shiftid);
                this.team.push(item.Teamid);
                this.selectClassList.push({
                    Teamid: item.Teamid,
                    ...this.classList.find(itm => itm.ID === item.Shiftid),
                    STARTTIME: item.Starttime.substr(11, 5),
                    ENDTIME: item.Endtime.substr(11, 5)
                });
            });
        },
        async getTeamList() {
            let resp = await getTeamList({ key: '', modelid: this.currentDeparment.id });
            this.teamList = resp.response;
        },
        async getClassList() {
            let resp = await getClassesList({ key: '' });
            this.classList = resp.response;
        },
        handleChangeClass(vals) {
            let slen = this.selectClassList.length;
            let vlen = vals.length;
            if (vlen === 0) {
                this.selectClassList = [];
            } else if (slen > vlen) {
                this.selectClassList = this.selectClassList.filter(item => vals.indexOf(item.ID) !== -1);
            } else if (slen < vlen) {
                let data = this.classList.find(item => item.ID === vals[vlen - 1]);
                this.selectClassList.push(data);
            }
        },
        async submitForm() {
            this.selectClassList.forEach(item => {
                this.teamTable.push(this.teamList.find(itm => itm.ID === item.Teamid));
            });

            let resp = await saveCalendar({
                Starttime: this.editDay,
                Endtime: this.editDay,
                Departmentid: this.currentDeparment.id,
                ShiftList: this.selectClassList,
                FirstTeamList: this.teamTable,
                Shiftid: '33131',
                Teamid: '1212',
                Cycles: 1
            });
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
            this.$emit('getdata');
            this.closePopup();
        },
        closePopup() {
            this.$emit('closePopup');
        }
    }
};
</script>

<style>

</style>
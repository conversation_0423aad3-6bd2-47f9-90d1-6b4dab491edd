<template>
  <el-dialog :title="$t('TITLE._Device')"
             :visible.sync="MaterialTableVisible"
             width="1000px"
             append-to-body
             :close-on-click-modal="false"
             :modal-append-to-body="false"
             :close-on-press-escape="false"
             @close="MaterialTableVisible=false">
    <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
      <el-form-item :label="$t('GLOBAL._SSL')">
        <el-input clearable v-model="searchForm.req.MeterCode_Name" placeholder="仪表名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input clearable v-model="searchForm.req.MeterCode" placeholder="仪表编号"></el-input>
      </el-form-item>
      <el-form-item>
        <!--        <el-input clearable v-model="searchForm.MeterType" placeholder="仪表类型"></el-input>-->
        <el-select style="width: 100%" v-model="searchForm.req.MeterType" placeholder="仪表类型">
          <el-option label="水" value="水"></el-option>
          <el-option label="电" value="电"></el-option>
          <el-option label="蒸汽" value="蒸汽"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input clearable v-model="searchForm.req.device_id" placeholder="设备ID"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="getSearchBtn()">{{ $t('GLOBAL._CX') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table class=""
              ref="multipleTable"
              :data="tableData"
              @selection-change="handleSelectionChange"
              style="width: 100%">
      <el-table-column
          type="selection"
          width="55" v-if="isMultiple">
      </el-table-column>
      <!--      <el-table-column prop="name" show-overflow-tooltip label="名称">-->
      <!--        <template slot-scope="scope">-->
      <!--          {{ scope.row.NAME }}-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column prop="MeterCode_Name" label="Name"></el-table-column>
      <el-table-column prop="MeterTypeName" label="TypeName"></el-table-column>
      <el-table-column prop="MeterCode" label="仪表编码"></el-table-column>
      <el-table-column prop="MeterType" label="Type"></el-table-column>
      <el-table-column prop="operation" width="80" :label="$t('GLOBAL._ACTIONS')" align="center" v-if="!isMultiple">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="addRow(scope.row)">{{ $t('GLOBAL._QD') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="width: 100%;height: 10px"></div>
    <el-pagination class="mt-8"
                   background
                   :current-page="searchForm.pageIndex"
                   :page-size="searchForm.pageSize"
                   layout="->, total, prev, pager, next"
                   :total="total"
                   @current-change="handleCurrentChange"/>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="MaterialTableVisible=false">{{ $t('GLOBAL._QX') }}</el-button>
      <el-button v-loading="loading"
                 v-if="isMultiple"
                 :disabled="loading"
                 element-loading-spinner="el-icon-loading"
                 size="small"
                 @click="submit()">{{ $t('GLOBAL.QD') }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import {getEnergyInstrumentPageList} from '@/api/productionManagement/Formula';

export default {
  name: 'add',
  props: {
    isMultiple: {
      type: Boolean,
      default: false
    },
    isId: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      MaterialTableVisible: false,
      tableData: [],
      loading: false,
      searchForm: {
        req:{},
        pageIndex: 1,
        pageSize: 10,
      },
      total: 0,
      multipleSelection: [],
      MaterialObj: {},
      dataObj:{"status":200,"success":true,"msg":"获取成功","msgDev":null,"response":{"page":1,"pageCount":6,"dataCount":60,"pageSize":10,"data":[{"device_id":"a7c5339e56b745a89a742ce27fd3a036","MeterCode":"O-P3-L3-3-02","MeterCode_Name":"小包车间（原接醋厂）","MeterType":"水","MeterTypeName":"潍微远程水表"},{"device_id":"a7c5339e56b745a89a742ce27fd3a045","MeterCode":"O-P3-L3-4-02","MeterCode_Name":"三厂小包间","MeterType":"水","MeterTypeName":"潍微远程水表"},{"device_id":"578781f9a0b35888b23f5b91706edd21","MeterCode":"W-P3-001","MeterCode_Name":"三层生产总用水","MeterType":"水","MeterTypeName":"潍微LXS"},{"device_id":"5bc222d24107b5b21200304bb5d41fd5","MeterCode":"W-P3-002","MeterCode_Name":"三层一般总用水","MeterType":"水","MeterTypeName":"潍微LXS"},{"device_id":"dbafef53c5090ef97e62c94a1731cbec","MeterCode":"W-P3-003","MeterCode_Name":"孖装车间用水","MeterType":"水","MeterTypeName":"潍微LXS"},{"device_id":"1447038b447c426475642b7a8f2058c6","MeterCode":"W-P3-004","MeterCode_Name":"洗桶房用水","MeterType":"水","MeterTypeName":"潍微LXS"},{"device_id":"8fdf55d08cc2ecf80351dc056ed84538","MeterCode":"W-P3-005","MeterCode_Name":"R&D用水","MeterType":"水","MeterTypeName":"潍微LXS"},{"device_id":"24d0ff618ccce2a38ae7d91c24928ed7","MeterCode":"W-P3-006","MeterCode_Name":"L19A线水笼用水","MeterType":"水","MeterTypeName":"潍微LXS"},{"device_id":"5a98060fe053f7d7645ef43c99fe9071","MeterCode":"W-P3-007","MeterCode_Name":"CIP1用水","MeterType":"水","MeterTypeName":"潍微LXS"},{"device_id":"b90ef213cac8eda3f5c175de7deea729","MeterCode":"W-P3-008","MeterCode_Name":"CIP2用水","MeterType":"水","MeterTypeName":"潍微LXS"}]}}
    }
  },
  mounted() {
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    submit() {
      if (!this.multipleSelection.length && !this.MaterialObj) {
        this.$message.error('至少选择一条记录！')
      }
      let matValue = undefined
      if (!this.isMultiple && this.isId) {
        matValue = this.MaterialObj.ID
      }
      if (this.isMultiple && this.isId) {
        matValue = this.multipleSelection.map(e => e.ID)
      }
      if (!this.isMultiple && !this.isId) {
        matValue = this.MaterialObj
      }
      if (this.isMultiple && !this.isId) {
        matValue = this.multipleSelection
      }
      this.$emit('saveForm', matValue)
      this.MaterialTableVisible = false
    },
    show() {
      this.searchForm = {
        req:{},
        pageIndex: 1,
        pageSize: 10
      }
      this.MaterialTableVisible = true
      this.multipleSelection = []
      this.MaterialObj = {}
      this.$nextTick(_ => {
        this.getTableData()
      })
    },
    addRow(row) {
      this.MaterialObj = row
      this.submit()
    },
    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },
    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },
    getTableData() {
      getEnergyInstrumentPageList(this.searchForm).then(res => {
        this.tableData = res.response.data
        this.total = res.response.dataCount
        // this.tableData = this.dataObj.response.data
      })

    }
  }
}
</script>

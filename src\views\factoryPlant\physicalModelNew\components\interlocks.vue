<template>
    <div class="interlocks">
        <v-row class="tool-row mt-1">
            <v-col :cols="12" :lg="3">
                <a-input-search v-model="keywords" enter-button placeholder="Quick Search" @search="onSearch" />
            </v-col>
            <v-col :cols="12" :lg="6" class="pl-0">
                <a-button @click="getList()" type="normal">
                    <v-icon left>mdi-cached</v-icon>
                    Refresh</a-button>
                <!-- <a-button @click="handleStatus(1)" :disabled="interlockList.length == 0" style="margin-left:15px"
                    type="primary">
                    Enabled</a-button>
                <a-button @click="handleStatus(0)" :disabled="interlockList.length == 0" style="margin-left:15px"
                    type="normal">
                    Disabled</a-button> -->
            </v-col>
        </v-row>
        <vxe-table ref="vxeTable" style="margin-top:5px" height="500px" size="mini" border resizable :data="interlockList">
            <vxe-column :width="column.width" :field="column.field" :title="column.title"
                v-for="(column, index) in interlockColumn" :key="index" :type="column.type">
                <template #default="{ row }">
                    <span v-if="column.field == 'ActualStatus'">
                        <vxe-switch @change="changeStatus(row)" v-model="row[column.field]" size="small"
                            class="my-switch1"></vxe-switch>
                    </span>
                    <!-- <span v-if="column.field == 'ActualStatus'">{{ row[column.field] == 1 ? 'Enabled' : 'Disabled' }}</span> -->
                    <span v-else>{{ row[column.field] }}</span>
                </template>
            </vxe-column>
        </vxe-table>
    </div>
</template>

<script>
import { interlockColumn } from '@/columns/factoryPlant/physicalModelNew.js'
import { getActionInterlockList, saveActionInterlock } from '../service'
export default {
    props: {
        actionObj: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            keywords: '',
            interlockColumn,
            interlockList: []
        }
    },
    created() {
        // this.getList()
    },
    methods: {
        async changeStatus(data) {
            console.log("data-----", data)
            let params = JSON.parse(JSON.stringify(data))
            params.ActualStatus = params.ActualStatus ? '1' : '0'
            try {
                let resp = await saveActionInterlock(params)
                this.$store.commit('SHOW_SNACKBAR', { text: '修改成功', color: 'success' });
                this.getList()
            } catch {
                this.getList()
            }
        },
        async handleStatus(val) {
            let list = this.$refs.vxeTable.getCheckboxRecords().map(item => item.InterLockId)
            let data = JSON.parse(JSON.stringify(this.interlockList))
            data.map(item => {
                if (list.indexOf(item.InterLockId) !== -1) {
                    item.ActualStatus = val
                }
                return item
            })
            try {
                let resp = await saveActionInterlock(data)
                this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });
                this.getList()
            } catch {
                this.getList()
            }
        },
        onSearch() {
            this.getList()
        },
        async getList() {
            let resp = await getActionInterlockList({
                EquipmentId: this.actionObj.EquipmentId,
                EquipmentActionId: this.actionObj.EquipmentActionId,
                ActionId: this.actionObj.ActionId,
                FunctionId: this.actionObj.FunctionId,
                InterLockName: this.keywords
            })
            this.interlockList = resp.response.map(item => {
                item.ActualStatus = item.ActualStatus == 1 ? true : false
                return item
            })
            this.$refs.vxeTable.removeCheckboxRow()
        },
    }
}
</script>

<style lang="less" scoped>
::v-deep .vxe-switch.is--on .vxe-switch--button {
    background-color: #13ce66;
}
</style>
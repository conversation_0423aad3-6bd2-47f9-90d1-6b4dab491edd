<template>
  <div class="sop-tree-select">
    <el-popover
      ref="popover"
      v-model="visible"
      placement="bottom-start"
      trigger="click">
      <el-scrollbar>
        <el-tree
          ref="tree"
          :data="data"
          :props="props"
          :node-key="nodeKey"
          :default-expanded-keys="defaultExpandedKeys"
          :current-node-key="value"
          highlight-current
          :expand-on-click-node="false"
          @current-change="handleCurrentChange">
        </el-tree>
      </el-scrollbar>
    </el-popover>
    <el-input
      v-model="selectedLabel"
      :placeholder="placeholder"
      readonly
      v-popover:popover>
      <i slot="suffix" :class="['el-select__caret', 'el-input__icon', 'el-icon-arrow-up', { 'is-reverse': visible }]"></i>
    </el-input>
  </div>
</template>

<script>
export default {
  name: 'TreeSelect',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    data: {
      type: Array,
      default: () => []
    },
    props: {
      type: Object,
      default: () => ({
        children: 'children',
        label: 'label',
        value: 'value'
      })
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    nodeKey: {
      type: String,
      default: 'id'
    },
    defaultExpandedKeys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      selectedLabel: '',
      currentNode: null
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.updateSelected()
      }
    },
    data: {
      immediate: true,
      handler() {
        this.$nextTick(() => {
          this.updateSelected()
        })
      }
    }
  },
  methods: {
    updateSelected() {
      if (!this.data || !this.data.length) return
      const findNode = (data, id) => {
        for (let item of data) {
          if (item[this.nodeKey] === id) {
            return item
          }
          if (item[this.props.children]) {
            const result = findNode(item[this.props.children], id)
            if (result) return result
          }
        }
        return null
      }
      const node = findNode(this.data, this.value)
      this.currentNode = node
      this.selectedLabel = node ? node[this.props.label] : ''
    },
    handleCurrentChange(data, node) {
      if (!data) return
      this.selectedLabel = data[this.props.label]
      this.visible = false
      this.$emit('input', data[this.nodeKey])
      this.$emit('change', data)
      this.$emit('node-click', data, node)
    }
  }
}
</script>

<style lang="scss" scoped>
.sop-tree-select {
  display: inline-block;
  width: 100%;

  .el-scrollbar {
    max-height: 300px;
    min-width: 180px;
  }

  .el-select__caret {
    transition: transform .3s;
    transform: rotate(180deg);
    
    &.is-reverse {
      transform: rotate(0);
    }
  }
}
</style>

import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_MATERIAL'
// 原料入库

//分页获取入库单列表
export function getReceivedOrderList(data) {
    const api =  '/materail/ReceivedOrder/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑入库单
export function ReceivedOrderSaveForm(data) {
    const api =  '/materail/ReceivedOrder/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除入库单
export function DeleteReceivedOrder(data) {
    const api =  '/materail/ReceivedOrder/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

//获取入库单物料明细列表
export function getReceivedMaterialList(data) {
    const api =  '/materail/ReceivedMaterial/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑入库单物料明细
export function ReceivedMaterialSaveForm(data) {
    const api =  '/materail/ReceivedMaterial/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除入库单物料明细
export function DeleteReceivedMaterial(data) {
    const api =  '/materail/ReceivedMaterial/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

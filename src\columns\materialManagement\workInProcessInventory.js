export const workInProcessColumn = [
    { text: '序号', value: 'Index', width: 60 },
    { text: '产品线', value: 'AreaCode', width: 120 },
    { text: 'MES虚拟库', value: 'WarehouseName', width: 220 },
    { text: 'AGV仓库', value: 'AgvwarehouseName', width: 200 },
    { text: 'AGV库区', value: 'AgvareawarehouseName', width: 200 },
    { text: 'AGV库位', value: 'AgvplacewarehouseName', width: 200 },
    { text: '物料编码', value: 'Materialcode', width: 100 },
    { text: '物料名称', value: 'Materialname', width: 240 },
    { text: '产出时间', value: 'Outputdate', width: 160 },
    { text: '批次数量', value: 'Batchnum', width: 130, semicolonFormat: true },
    { text: '当前数量(A)', value: 'Anum', width: 130, semicolonFormat: true },
    { text: '缺陷数量(B)', value: 'Bnum', width: 130, semicolonFormat: true },
    { text: '废品数量(C)', value: 'Cnum', width: 130, semicolonFormat: true },
    { text: '状态', value: 'Status', width: 100, dictionary: true },
    { text: '批次号', value: 'Batchcode', width: 120 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', value: 'noActions', width: 0 }
]

export const moveColum = [
    { text: '批次号', value: 'Batchcode', width: 120 },
    { text: '物料编码', value: 'Materialcode', width: 100 },
    { text: '物料名称', value: 'Materialname', width: 240 },
    { text: 'MES虚拟库', value: 'WarehouseName', width: 220 },
    { text: 'AGV仓库', value: 'AgvwarehouseName', width: 200 },
    { text: '产出时间', value: 'Outputdate', width: 160 },
    { text: '批次数量', value: 'Batchnum', width: 130, semicolonFormat: true },
    { text: '当前数量(A)', value: 'Anum', width: 130, semicolonFormat: true },
    { text: '', value: 'noActions', width: 0 }
]
export const inventoryStatisticsColumn = [
    { text: '序号', value: 'Index', width: 60 },
    { text: '产品线', value: 'AreaCode', width: 120 },
    { text: 'MES虚拟库', value: 'WarehouseName', width: 220 },
    { text: '物料编码', value: 'Materialcode', width: 100 },
    { text: '物料名称', value: 'Materialname', width: 240 },
    { text: '当前数量(A)', value: 'Anum', width: 120, semicolonFormat: true },
    { text: '缺陷数量(B)', value: 'Bnum', width: 120, semicolonFormat: true },
    { text: '废品数量(C)', value: 'Cnum', width: 120, semicolonFormat: true },
    { text: '', value: 'noActions', width: 0 }
]
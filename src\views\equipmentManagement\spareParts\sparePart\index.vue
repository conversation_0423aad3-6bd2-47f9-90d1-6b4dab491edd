<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <!-- <v-btn color="primary" v-has="'SBBJGL_BJKCX_MBXZ'" @click="templateDownload()">{{
                        $t('TPM_SBGL_SBBJGL._MBXZ') }}</v-btn> -->
                         <v-btn color="primary" v-has="'SBBJGL_BJKCX_DR'">{{
                        $t('TPM_SBGL_SBBJGL.TBKC')
                    }}</v-btn>
                    <v-btn color="primary" v-has="'SBBJGL_BJKCX_DR'" :loading="importLoading" @click="handleImport()">{{
                        $t('TPM_SBGL_SBBJGL._DR')
                    }}</v-btn>
                    <v-btn color="primary" v-has="'SBBJGL_BJKCX_DC'" :loading="importLoading" @click="handleExport()">{{
                        $t('TPM_SBGL_SBBJGL._DC')
                    }}</v-btn>
                    <!-- <v-btn color="primary" v-has="'SBBJGL_BJKCX_PLDY'" :disabled="!deleteList.length"
                        @click="btnClickEvet('printAll')">{{
                            $t('TPM_SBGL_SBBJGL._PLDY') }}</v-btn>
                    <v-btn color="primary" v-has="'SBBJGL_BJKCX_ADD'" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ')
                    }}</v-btn> -->
                    <v-btn color="primary" v-has="'SBBJGL_BJKCX_ALLREMOVE'" :disabled="!deleteList.length"
                        @click="btnClickEvet('delete')">{{
                            $t('GLOBAL._PLSC') }}</v-btn>
                </div>
                <Tables :page-options="pageOptions" :loading="loading" :btn-list="btnList"
                     :tableHeight="showFrom ? 'calc(100vh - 285px)' : 'calc(100vh - 210px)'" table-name="TPM_SBGL_SBBJGL"
                    :headers="sparePartColum" :desserts="desserts" :clickFun="clickFun" @selectePages="selectePages"
                    @tableClick="tableClick" @itemSelected="SelectedItems" @toggleSelectAll="SelectedItems">
                    <template #CurrentStock="{ item }">
                        <span :style="`${item.CurrentStock < item.MinStock ? 'color:red;font-weight:600' : ''}`">{{
                            item.CurrentStock }}</span>
                    </template>
                </Tables>
                <createRepast ref="createRepast" :equipmentSpareType="equipmentSpareType" :dialogType="dialogType"
                    :tableItem="tableItem"></createRepast>
            </v-card>
            <!-- <v-card class="ma-1">
                <div class="pa-2">{{ $t('TPM_SBGL_SBBJGL._MXJL') }}</div>
                <Tables :page-options="pageOptions2" :loading="loading2" :btn-list="btnList2"
                    :tableHeight="showFrom ? 'calc(35vh - 180px)' : 'calc(35vh - 164px)'" table-name="TPM_SBGL_SBBJGL_XQ"
                    :headers="sparePartlogColum" :desserts="desserts2" @selectePages="selectePages" @tableClick="tableClick"
                    @itemSelected="SelectedItems" @toggleSelectAll="SelectedItems">
                    <template #Buynum="{ item }">
                        <div>
                            {{ item.Buynum ? '入库' : '出库' }}
                        </div>
                    </template>
                    <template #Usednum="{ item }">
                        <div>
                            {{ item.Usednum ? item.Usednum : item.Buynum }}
                        </div>
                    </template>
                </Tables>
            </v-card> -->
        </div>
    </div>
</template>
<script>
import { mixins } from '@/util/mixins.js';
import { SparepartGetPageList, SparepartDelete, SparepartuselogGetPageList, SparepartuselogDelete, doImport } from '@/api/equipmentManagement/sparePart.js';
import { sparePartColum, sparePartlogColum } from '@/columns/equipmentManagement/sparePart.js';
import { PrintTplGetPageList } from '@/api/systemManagement/printTemplate.js';
import { configUrl } from '@/config';
import dayjs from 'dayjs';

const baseUrl = configUrl[process.env.VUE_APP_SERVE]['baseURL_TPM'] + '/tpm/SparePartsStock/ImportExcelTemplates';
const exportUrl = configUrl[process.env.VUE_APP_SERVE]['baseURL_TPM'] + '/tpm/SparePartsStock/DataExport';
export default {
    name: 'RepastModel',
    components: {
        createRepast: () => import('./components/createRepast.vue')
    },
    mixins: [mixins],
    data() {
        return {
            equipmentSpareType: [],
            importLoading: false,
            // tree 字典数据
            loading: true,
            loading2: false,
            showFrom: false,
            papamstree: {
                key: null,
                type: '',
                isselector: '',
                sparepartscode: '',
                shelfcode: '',
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            sparePartColum,
            sparePartlogColum,
            desserts: [],
            desserts2: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            pageOptions2: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            rowtableItem: {},
            deleteList: [], //批量选中
            hasChildren: {} // 新增字典详情判断-子节点才能新增
        };
    },
    computed: {
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'sparepartscode',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.SparePartsCode'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.SparePartsCode')
                },
                // {
                //     value: '',
                //     key: 'shelfcode',
                //     icon: 'mdi-account-check',
                //     label: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.Remark'),
                //     placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.Remark')
                // },
                {
                    value: '',
                    key: 'name',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.SparePartsName'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.SparePartsName')
                },
                {
                    value: '',
                    key: 'type',
                    icon: 'mdi-account-check',
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.equipmentSpareType, 'ItemName', 'ItemName'),
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.SType'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.SType')
                },
                {
                    value: '',
                    key: 'min',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL._KCDY'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL._KCDY')
                },
                {
                    value: '',
                    key: 'max',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL._KCXY'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL._KCXY')
                },
                // {
                //     value: '',
                //     key: 'Introduce',
                //     icon: 'mdi-account-check',
                //     label: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.Introduce'),
                //     placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.Introduce')
                // },
                
                // {
                //     value: '',
                //     key: 'isselector',
                //     type: 'checkbox',
                //     icon: 'mdi-account-check',
                //     label: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.isselector'),
                //     placeholder: ''
                // }
            ];
        },
        btnList() {
            return [
                {
                    text: this.$t('TPM_SBGL_SBBJGL._DYBJBQ'),
                    code: 'printCode',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBBJGL_BJKCX_DYBJBQ'
                },
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: "SBBJGL_BJKCX_EDIT"
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBBJGL_BJKCX_DELETE'
                }
            ];
        },
        btnList2() {
            return [
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete2',
                    type: 'red',
                    icon: '',
                    authCode: 'SBBJGL_BJMX_DELETE'
                }
            ];
        }
    },
    mounted() {
        this.GetequipmentSpareType()
        this.RepastInfoGetPage();
        this.GetPrintTpl();
    },
    activated() {
        this.GetPrintTpl();
    },
    methods: {
        // 获取备件分类
        async GetequipmentSpareType() {
            const res = await this.$getDataDictionary('SpareType');
            this.equipmentSpareType = res || [];
        },
        handleExport() {
            window.open(exportUrl, '_blank');
        },
        // 导入模板下载
        async templateDownload() {
            window.open(baseUrl, '_blank');
        },
        handleImport() {
            let _this = this;
            let input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xls,.xlsx';
            input.click();
            input.onchange = async function () {
                let file = input.files[0];
                let formdata = new FormData();
                formdata.append('file', file);

                _this.importLoading = true;
                try {
                    await doImport(formdata);
                    _this.$store.commit('SHOW_SNACKBAR', { text: '导入成功', color: 'success' });
                    _this.getdata();
                    _this.importLoading = false;
                } catch {
                    _this.importLoading = false;
                }
            };
        },
        // 查询数据
        searchForm(value) {
            this.papamstree.pageIndex = 1;
            this.papamstree.sparepartscode = value.sparepartscode;
            this.papamstree.shelfcode = value.shelfcode;
            this.papamstree.name = value.name;
            this.papamstree.type = value.type;
            this.papamstree.Introduce = value.Introduce;
            this.papamstree.isselector = value.isselector;

            this.RepastInfoGetPage();
        },
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                sparepartscode: this.papamstree.sparepartscode,
                shelfcode: this.papamstree.shelfcode,
                name: this.papamstree.name,
                type: this.papamstree.type,
                Introduce: this.papamstree.Introduce,
                pageIndex: this.papamstree.pageIndex,
                pageSize: this.papamstree.pageSize
            };
            if (this.papamstree.isselector) {
                params.isselector = this.papamstree.isselector;
            }
            this.loading = true;
            const res = await SparepartGetPageList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.desserts.map(item => {
                    item.ExtendTime && (item.ExtendTime = dayjs(item.ExtendTime).format('YYYY-MM-DD'))
                    return item;
                });
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
                this.rowtableItem = this.desserts[0] || {};
                this.RepastInfoLogGetPage();
            }
        },
        clickFun(data) {
            console.log(data);
            this.rowtableItem = data || {};
            this.RepastInfoLogGetPage();
        },
        // 获取log列表
        async RepastInfoLogGetPage() {
            let params = {
                sparepartid: this.rowtableItem.ID,
                pageIndex: this.papamstree.pageIndex,
                pageSize: this.papamstree.pageSize
            };
            this.loading2 = true;
            const res = await SparepartuselogGetPageList(params);
            let { success, response } = res;
            if (success) {
                this.loading2 = false;
                this.desserts2 = (response || {}).data || [];
                this.pageOptions2.total = response.dataCount;
                this.pageOptions2.page = response.page;
                this.pageOptions2.pageCount = response.pageCount;
                this.pageOptions2.pageSize = response.pageSize;
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'printAll':
                    var printdata = {
                        table: this.deleteList
                    };
                    this.$nextTick(this.PrintTemplateFn(printdata));
                    return;
                case 'add':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable('delete');
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'printCode':
                    var printdata = {
                        table: [item]
                    };
                    this.$nextTick(this.PrintTemplateFn(printdata));
                    return;
                case 'delete':
                    this.deltable(type);
                    return;
                case 'delete2':
                    this.deltable(type);
                    return;
            }
        },
        // 删除
        deltable(type) {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    if (type == 'delete') {
                        let res = await SparepartDelete(params);
                        if (res.success) {
                            this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                            this.RepastInfoGetPage();
                        }
                    } else if (type == 'delete2') {
                        let res = await SparepartuselogDelete(params);
                        if (res.success) {
                            this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                            this.RepastInfoLogGetPage();
                        }
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        },
        async GetPrintTpl() {
            let params = {
                type: 'sparepartCode',
                pageIndex: 1,
                pageSize: 10
            };
            const res = await PrintTplGetPageList(params);
            let { success, response } = res;
            if (success) {
                // 处理有效模板
                let data = response?.data.filter(item => item.Status == 1) ?? [];
                let datas = data[0]?.TplJson;
                this.dataTemplate = JSON.parse(datas);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}
</style>

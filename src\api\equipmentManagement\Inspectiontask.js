import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';

export function GetMeasureCalibrateWoPageList(data) {
    const api = '/api/MeasureCalibrateWo/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureCalibrateWoSaveForm(data) {
    const api = '/api/MeasureCalibrateWo/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureCalibrateWoImportData(data) {
    const api = '/api/MeasureCalibrateItem/ImportData';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureCalibrateWoCreateMeasureCalibrateWo(data) {
    const api = '/api/MeasureCalibrateWo/CreateMeasureCalibrateWo';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureCalibrateWoStart(data) {
    const api = '/api/MeasureCalibrateWo/Start';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureCalibrateWoFinish(data) {
    const api = '/api/MeasureCalibrateWo/Finish';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureCalibrateWoConfirm(data) {
    const api = '/api/MeasureCalibrateWo/Confirm';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureCalibrateWoCancel(data) {
    const api = '/api/MeasureCalibrateWo/Cancel';
    return getRequestResources(baseURL, api, 'post', data);
}
//不合格清单
export function GetMeasureCalibrateItemNGList(data) {
    const api = '/api/MeasureCalibrateItem/GetNGList';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureCalibrateItemList(data) {
    const api = '/api/MeasureCalibrateItem/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureCalibrateItemDelete(data) {
    const api = '/api/MeasureCalibrateItem/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureCalibrateItemSaveForm(data) {
    const api = '/api/MeasureCalibrateItem/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureCalibrateItemUploadFile(data) {
    const api = '/api/MeasureCalibrateItem/UploadFiles';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMeasureCalibrateItemGetFileUrl(data) {
    const api = '/api/MeasureCalibrateItem/GetFileUrl';
    return getRequestResources(baseURL, api, 'get', data);
}
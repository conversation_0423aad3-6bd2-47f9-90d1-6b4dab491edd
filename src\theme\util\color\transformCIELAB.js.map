{"version": 3, "sources": ["../../../src/util/color/transformCIELAB.ts"], "names": [], "mappings": ";;;;;;;AAEA,IAAM,KAAK,GAAG,mBAAd,C,CAAkC;;AAElC,IAAM,sBAAsB,GAAG,SAAzB,sBAAyB,CAAC,CAAD;AAAA,SAC7B,CAAC,YAAG,KAAH,EAAY,CAAZ,CAAD,GACI,IAAI,CAAC,IAAL,CAAU,CAAV,CADJ,GAEK,CAAC,IAAI,aAAI,KAAJ,EAAa,CAAb,CAAJ,CAAF,GAAyB,IAAI,EAHJ;AAAA,CAA/B;;AAMA,IAAM,sBAAsB,GAAG,SAAzB,sBAAyB,CAAC,CAAD;AAAA,SAC7B,CAAC,GAAG,KAAJ,YACI,CADJ,EACS,CADT,IAEK,aAAI,KAAJ,EAAa,CAAb,CAAD,IAAoB,CAAC,GAAG,IAAI,EAA5B,CAHyB;AAAA,CAA/B;;AAMM,SAAU,OAAV,CAAmB,GAAnB,EAA2B;AAC/B,MAAM,SAAS,GAAG,sBAAlB;AACA,MAAM,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,CAAD,CAAJ,CAA9B;AAEA,SAAO,CACL,MAAM,YAAN,GAAqB,EADhB,EAEL,OAAO,SAAS,CAAC,GAAG,CAAC,CAAD,CAAH,GAAS,OAAV,CAAT,GAA8B,YAArC,CAFK,EAGL,OAAO,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,CAAD,CAAH,GAAS,OAAV,CAA/B,CAHK,CAAP;AAKD;;AAEK,SAAU,KAAV,CAAiB,GAAjB,EAAyB;AAC7B,MAAM,SAAS,GAAG,sBAAlB;AACA,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAD,CAAH,GAAS,EAAV,IAAgB,GAA3B;AACA,SAAO,CACL,SAAS,CAAC,EAAE,GAAG,GAAG,CAAC,CAAD,CAAH,GAAS,GAAf,CAAT,GAA+B,OAD1B,EAEL,SAAS,CAAC,EAAD,CAFJ,EAGL,SAAS,CAAC,EAAE,GAAG,GAAG,CAAC,CAAD,CAAH,GAAS,GAAf,CAAT,GAA+B,OAH1B,CAAP;AAKD", "sourcesContent": ["import { XYZ, LAB } from '../colorUtils'\n\nconst delta = 0.20689655172413793 // 6÷29\n\nconst cielabForwardTransform = (t: number): number => (\n  t > delta ** 3\n    ? Math.cbrt(t)\n    : (t / (3 * delta ** 2)) + 4 / 29\n)\n\nconst cielabReverseTransform = (t: number): number => (\n  t > delta\n    ? t ** 3\n    : (3 * delta ** 2) * (t - 4 / 29)\n)\n\nexport function fromXYZ (xyz: XYZ): LAB {\n  const transform = cielabForwardTransform\n  const transformedY = transform(xyz[1])\n\n  return [\n    116 * transformedY - 16,\n    500 * (transform(xyz[0] / 0.95047) - transformedY),\n    200 * (transformedY - transform(xyz[2] / 1.08883)),\n  ]\n}\n\nexport function toXYZ (lab: LAB): XYZ {\n  const transform = cielabReverseTransform\n  const Ln = (lab[0] + 16) / 116\n  return [\n    transform(Ln + lab[1] / 500) * 0.95047,\n    transform(Ln),\n    transform(Ln - lab[2] / 200) * 1.08883,\n  ]\n}\n"], "sourceRoot": "", "file": "transformCIELAB.js"}
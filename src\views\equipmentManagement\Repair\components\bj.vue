<template>
    <div>
        <Tables
            ref="recordTable"
            :page-options="pageOptions"
            :footer="false"
            :showSelect="false"
            :loading="loading"
            :btn-list="btnList"
            tableHeight="calc(100vh - 220px)"
            table-name="TPM_SBGL_SBBYJH_XQ"
            :headers="keepListBjBColum2"
            :desserts="desserts"
            @tableClick="tableClick"
        ></Tables>
    </div>
</template>

<script>
import { keepListBjBColum2 } from '@/columns/equipmentManagement/upkeep.js';
import { GetPartsHistoryDetailPageList } from '@/api/equipmentManagement/NewRepair.js';
export default {
    components: {},
    data() {
        return {
            keepListBjBColum2,
            loading: false,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            PartOutstockStatus:[]
        };
    },
    computed: {
        btnList() {
            return [];
        }
    },
    async created() {
        this.PartOutstockStatus = await this.$getNewDataDictionary('PartOutstockStatus');
    },
    methods: {
        async MyGetPartsHistoryDetailPageList(row) {
            let params = {
                ReferNo: row.RepairWo,
                Factory: this.$route.query.Factory ? this.$route.query.Factory : '2010'
            };
            this.loading = true;
            let res = await GetPartsHistoryDetailPageList(params);
            let { response } = res;
            response.forEach(item => {
                this.PartOutstockStatus.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
            });
            this.desserts = res.response;
            this.loading = false;
        }
    }
};
</script>

<style></style>

<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                </div>
                <Tables :page-options="pageOptions" :loading="loading" :btn-list="btnList" tableHeight="calc(50vh - 150px)"
                    table-name="TPM_SBGL_SBWXGD" ref="Tables" :clickFun="clickFun" :dictionaryList="dictionaryList"
                    :headers="RepairColum" :desserts="desserts" @selectePages="selectePages" @tableClick="tableClick"
                    @itemSelected="SelectedItems" @toggleSelectAll="SelectedItems">
                    <template #RepairStatus="{ item }">
                        <div :style="{ color: stateBc[item.RepairStatus] }">
                            {{ states[item.RepairStatus] }}
                        </div>
                    </template>
                    <template #actions="{ item }">
                        <v-btn v-has="list.authCode" v-for="(list, index) in btnList" :key="index"
                            :disabled="item.RepairStatus == '1' && list.text == '维修'" text small class="ma-0 pa-0 ml-1"
                            :color="list.type" @click.stop="tableClick(item, list.code)">
                            {{ list.text }}
                        </v-btn>
                    </template>
                </Tables>
                <createRepast ref="createRepast" :PartsList="PartsList" :peopleitems="staffList"
                    :equipStatuslist="equipStatuslist" :ressionList="ressionList" :dialogType="dialogType"
                    :tableItem="tableItem"></createRepast>
            </v-card>
            <v-card class="ma-1">
                <v-tabs v-model="tab" background-color="transparent">
                    <v-tab @click="changeTab(0)" key="0">{{ $t('TPM_SBGL_SBWXJL._WXMX') }}</v-tab>
                    <v-tab @click="changeTab(1)" key="1">{{ $t('TPM_SBGL_SBWXJL._TSMX') }}</v-tab>
                    <v-tab @click="changeTab(2)" key="2">{{ $t('TPM_SBGL_SBWXJL._WXCB') }}</v-tab>
                </v-tabs>
                <v-tabs-items v-model="tab">
                    <v-tab-item>
                        <maintenanceDetails ref="maintenanceDetails" :rowtableItem="rowtableItem"
                            :equipStatuslist="equipStatuslist"></maintenanceDetails>
                    </v-tab-item>
                    <v-tab-item>
                        <debuggingDetails ref="debuggingDetails" :staffList="staffList" :rowtableItem="rowtableItem">
                        </debuggingDetails>
                    </v-tab-item>
                    <v-tab-item>
                        <servicecost ref="servicecost" :rowtableItem="rowtableItem"></servicecost>
                    </v-tab-item>
                </v-tabs-items>
            </v-card>
        </div>
    </div>
</template>
<script>
import { GetReasontree } from '@/api/factoryPlant/reasonDetail.js';
import { DeviceRepairWoGetPageList, DeviceRepairWoDelete } from '@/api/equipmentManagement/Repair.js';
import { RepairColum } from '@/columns/equipmentManagement/Repair.js';
import maintenanceDetails from './maintenanceDetails/index.vue';
import debuggingDetails from './debuggingDetails/index.vue';
import servicecost from './servicecost/index.vue';
import { StaffSiteGetList } from '@/api/peopleManagement/basicdata.js';
import { SparepartGetList } from '@/api/equipmentManagement/sparePart.js';
import Util from '@/util'
import dayjs from 'dayjs';
const stateBc = {
    3: `var(--v-secondary-base)`, //已领件
    2: `var(--v-warning-base)`, //维修中
    1: `var(--v-anchor-base)`, //已维修
    0: `var(--v-error-base)` //未维修
};
const states = {
    3: '已领件', //已领件
    2: '维修中', //维修中
    1: '已维修', //已维修
    0: '未维修' //未维修
};
export default {
    name: 'RepastModel',
    components: {
        maintenanceDetails, //维修明细
        debuggingDetails, //调式明细
        servicecost, // 维修费用
        createRepast: () => import('./components/createRepast.vue')
    },
    data() {
        return {
            stateBc,
            states,
            // tree 字典数据
            tab: null,
            loading: true,
            showFrom: false,
            papamstree: {
                evenno: '',
                wocode: '',
                createsdate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
                createedate: dayjs().format('YYYY-MM-DD'),
                key: null,
                inputtype: '',
                repairstatus: '',
                planworkuser: '',

                reasons: '', //原因ID
                stock: '', //备件 code
                startwork: null,
                endwork: null,
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            RepairColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            rowtableItem: {},
            deleteList: [], //批量选中
            hasChildren: {}, // 新增字典详情判断-子节点才能新增

            // 字典配置
            equipStatuslist: [],
            inputtypelist: [], // 查询录入类型
            repairstatuslist: [], // 查询维修状态
            Allday: [
                { k: 1, v: '是' },
                { k: 0, v: '否' }
            ],
            ressionList: [], // 原因数
            staffList: [],
            PartsList: []
        };
    },
    computed: {
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'evenno',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBWXJL._SJH'),
                    placeholder: this.$t('TPM_SBGL_SBWXJL._SJH')
                },
                {
                    value: '',
                    key: 'wocode',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBWXJL._WXGDH'),
                    placeholder: this.$t('TPM_SBGL_SBWXJL._WXGDH')
                },
                {
                    value: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
                    key: 'createsdate',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBWXJL._KSSJ'),
                    placeholder: this.$t('TPM_SBGL_SBWXJL._KSSJ')
                },
                {
                    value: dayjs().format('YYYY-MM-DD'),
                    key: 'createedate',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBWXJL._JSSJ'),
                    placeholder: this.$t('TPM_SBGL_SBWXJL._JSSJ')
                },
                {
                    value: '',
                    key: 'key',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBWXJL._JQBMMC'),
                    placeholder: this.$t('TPM_SBGL_SBWXJL._JQBMMC')
                },
                {
                    value: '',
                    key: 'inputtype',
                    selectData: this.inputtypelist,
                    icon: 'mdi-account-check',
                    type: 'select',
                    label: this.$t('TPM_SBGL_SBWXJL._LRLX'),
                    placeholder: this.$t('TPM_SBGL_SBWXJL._LRLX')
                },
                {
                    value: '',
                    key: 'repairstatus',
                    selectData: this.repairstatuslist,
                    icon: 'mdi-account-check',
                    type: 'select',
                    label: this.$t('TPM_SBGL_SBWXJL._WXZT'),
                    placeholder: this.$t('TPM_SBGL_SBWXJL._WXZT')
                },
                {
                    value: '',
                    key: 'planworkuser',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBWXJL._ZXRY'),
                    placeholder: this.$t('TPM_SBGL_SBWXJL._ZXRY')
                },
                {
                    value: '',
                    key: 'stock',
                    icon: 'mdi-account-check',
                    isShowCustom: true,
                    type: 'select',
                    selectData: Util.changeSelectItems(this.PartsList, 'SparePartsCode', 'SparePartsName'),
                    label: this.$t('TPM_SBGL_SBWXJL._BJBM'),
                    placeholder: this.$t('TPM_SBGL_SBWXJL._BJBM')
                },
                {
                    value: '',
                    key: 'reasons',
                    selectData: this.ressionList,
                    icon: 'mdi-account-check',
                    type: 'select',
                    label: this.$t('TPM_SBGL_SBWXJL._YYFX'),
                    placeholder: this.$t('TPM_SBGL_SBWXJL._YYFX')
                },
                {
                    value: '',
                    key: 'startwork',
                    type: 'number',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBWXJL._KSGS'),
                    placeholder: this.$t('TPM_SBGL_SBWXJL._KSGS')
                },
                {
                    value: '',
                    key: 'endwork',
                    type: 'number',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBWXJL._JSGS'),
                    placeholder: this.$t('TPM_SBGL_SBWXJL._JSGS')
                }
            ];
        },
        btnList() {
            return [
                {
                    text: this.$t('TPM_SBGL_SBWXJL._WX'),
                    code: 'repair',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBWXJL_WX'
                },
                {
                    text: this.$t('TPM_SBGL_SBWXJL._TS'),
                    code: 'debug',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBWXJL_TS'
                }
            ];
        },
        dictionaryList() {
            return [
                { arr: this.equipStatuslist, key: 'RepairStatus', val: 'ItemValue', text: 'ItemName' },
                { arr: this.inputtypelist, key: 'InputType', val: 'ItemValue', text: 'ItemName' },
                { arr: this.Allday, key: 'Allday', val: 'k', text: 'v' }
            ];
        }
    },
    async created() {
        await this.RepastInfoGetPage();
    },
    mounted() {
        this.getSparepartGetList()
        this.getReasonTreeList();
        this.getEquipStatuslist();
        this.getInputType();
        this.getStaffList()
    },
    methods: {
        // 获取配件
        async getSparepartGetList() {
            let params = {
                key: '',
                type: '',
                sparepartscode: ''
            };
            const res = await SparepartGetList(params);
            let { success, response } = res;
            if (success) {
                this.PartsList = response;
                this.PartsList.map(item => {
                    item.labelText = `${item.SparePartsCode} | ${item.SparePartsName}`
                    return item
                })
            }
        },
        // 查询人员列表
        async getStaffList() {
            let resp = await StaffSiteGetList({ key: '' });
            this.staffList = resp.response;
        },
        // 获取原因树型
        async getReasonTreeList() {
            this.ressionList = [];
            const res = await GetReasontree();
            const { success, response } = res;
            if (success) {
                const data = response;
                const datalist = data.find(item => item.name == '停机原因');
                datalist.children.forEach(item => {
                    this.ressionList.push({ label: item.name, value: item.id });
                });
            }
        },
        // 查询数据
        searchForm(value) {
            if (value) {
                for (const key in value) {
                    this.papamstree[key] = value[key]
                }
            }
            this.RepastInfoGetPage();
        },
        changeTab(v) {
            switch (v) {
                case 0:
                    setTimeout(() => {
                        this.$refs.maintenanceDetails.RepastInfologGetPage(this.rowtableItem);
                    }, 10);
                    break;
                case 1:
                    setTimeout(() => {
                        this.$refs.debuggingDetails.CommissioningRecords(this.rowtableItem);
                    }, 10);
                    break;
                case 2:
                    setTimeout(() => {
                        this.$refs.servicecost.GetPricePageList(this.rowtableItem);
                    }, 10);
                    break;
            }
        },
        clickFun(data) {
            console.log(data);
            this.$refs.Tables.selected = [data];
            this.rowtableItem = data || {};
            this.tableItem = data || {};
            switch (this.tab) {
                case 0:
                    this.$refs.maintenanceDetails.RepastInfologGetPage(this.rowtableItem);
                    break;
                case 1:
                    this.$refs.debuggingDetails.CommissioningRecords(this.rowtableItem);
                    break;
                case 2:
                    this.$refs.servicecost.GetPricePageList(this.rowtableItem);
                    break;
            }
        },
        // 工单列表查询
        async RepastInfoGetPage() {

            this.loading = true;
            const res = await DeviceRepairWoGetPageList({ ...this.papamstree, createsdate: this.papamstree.createsdate + ' 00:00:00', createedate: this.papamstree.createedate + ' 23:59:59' });
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
                this.rowtableItem = this.desserts[0] || {};
                this.clickFun(this.rowtableItem)
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'repair':
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'debug':
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await DeviceRepairWoDelete(params);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.RepastInfoGetPage();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        },
        // 获取设备组字典
        async getEquipStatuslist() {
            const res = await this.$getDataDictionary('equipStatus');
            this.equipStatuslist = res || [];
            this.repairstatuslist = [];
            this.equipStatuslist.forEach(item => {
                this.repairstatuslist.push({ label: item.ItemName, value: item.ItemValue });
            });
        },
        // 获取设备录入类型字典
        async getInputType() {
            const res = await this.$getDataDictionary('InputType');
            const datalist = res || [];
            this.inputtypelist = [];
            datalist.forEach(item => {
                this.inputtypelist.push({ label: item.ItemName, value: item.ItemValue });
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}
</style>

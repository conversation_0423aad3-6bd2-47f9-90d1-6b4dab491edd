import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TRACE; // 配置服务url
const DFM = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url
//设备是否有产线
export function GetFeedingProcDevice(data) {
    return request({
        url: baseURL + '/trace/Feeding/GetFeedingProcDevice',
        method: 'post',
        data
    });
}

//上料表信息保存
export function FeedingSaveForm(data) {
    return request({
        url: baseURL + '/trace/Feeding/FeedingSaveForm',
        method: 'post',
        data
    });
}

// 获取上料列表
export function traceGetPageList(data) {
    return request({
        url: baseURL + '/trace/Feeding/GetPageList',
        method: 'post',
        data
    });
}
export function traceGetPageNewList(data) {
    return request({
        url: baseURL + '/trace/Feeding/GetPageList_New',
        method: 'post',
        data
    });
}
// 删除
export function traceFeedingDelete(data) {
    return request({
        url: baseURL + '/trace/Feeding/Delete',
        method: 'post',
        data
    });
}
// 编辑
export function traceFeedingSaveForm(data) {
    return request({
        url: baseURL + '/trace/Feeding/SaveForm',
        method: 'post',
        data
    });
}

// 
export function ProductionEquipmentSaveForm(data) {
    return request({
        url: baseURL + '/trace/ProductionEquipment/SaveForm',
        method: 'post',
        data
    });
}

// 
export function ProductionEquipmentGetPageList(data) {
    return request({
        url: baseURL + '/trace/ProductionEquipment/GetPageList',
        method: 'post',
        data
    });
}

// 查询成品料号列表
export function getMaterialList(data) {
    return request({
        url: DFM + '/api/Material/GetPageList',
        method: 'post',
        data
    });
}
// 上料
export function doLoadMaterial(data) {
    return request({
        url: baseURL + '/trace/Feeding/InputFeedingMaterial',
        method: 'post',
        data
    })
}
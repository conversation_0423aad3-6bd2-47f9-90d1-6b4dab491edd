<template>
    <div class="equipment-fault-definition">
        <v-card class="tree-box">
            <span class="text--primary left tree-title">故障分类
                <v-btn @click="addFault()" class="ml-auto py-0" text color="primary">新增</v-btn>
            </span>
            <v-spacer dark></v-spacer>
            <v-treeview hoverable dense activatable item-key="id" v-model="tree" :items="items" :active="active"
                @update:active="handleChangeTree">
                <template v-slot:append="{ item }">
                    <a-popover placement="bottom">
                        <template slot="content">
                            <p @click="addFault(item)">{{ $t('DFM_BZZY._TJZL') }}</p>
                            <p @click="del(item)">{{ $t('GLOBAL._SC') }}</p>
                        </template>
                        <span style="font-size: 18px" class="iconfont icon-point"></span>
                    </a-popover>
                </template>
            </v-treeview>
        </v-card>
        <div class="form-box px-5">
            <span class="text--primary tree-title">故障定义</span>
            <v-spacer dark></v-spacer>
            <v-form :disabled="!selectTreeNode" ref="form">
                <v-row class="mt-4">
                    <v-col class="pt-0 mt-0" :cols="12" :lg="6">
                        <v-autocomplete multiple label="产线" :items="lineList" item-text="EquipmentName"
                            item-value="EquipmentCode" outlined dense v-model="form.LineCode">
                            <template v-slot:selection="{ item, index }">
                                <span v-if="index === 0">{{ item.EquipmentCode }}</span>
                                &nbsp;
                                <span v-if="index === 1" class="grey--text caption">(+{{ form.LineCode.length - 1 }}
                                    )</span>
                            </template>
                        </v-autocomplete>
                    </v-col>
                    <v-col class="pt-0 mt-0" :cols="12" :lg="6">
                        <v-autocomplete multiple :items="equipList" item-text="Name" item-value="Code" label="设备" outlined
                            dense v-model="form.DeviceCode">
                            <template v-slot:selection="{ item, index }">
                                <span v-if="index === 0">{{ item }}</span>
                                &nbsp;
                                <span v-if="index === 1" class="grey--text caption">(+{{ form.DeviceCode.length - 1 }}
                                    )</span>
                            </template>
                        </v-autocomplete>
                    </v-col>
                    <v-col class="pt-0 mt-0" :cols="12" :lg="6">
                        <!-- <v-text-field label="组件" outlined dense v-model="form.BomCode"></v-text-field> -->
                        <Treeselect :disabled="!selectTreeNode" :noChildrenText="$t('GLOBAL.noData')"
                            :noOptionsText="$t('GLOBAL.noData')" :normalizer="normalizer" :options="bomTreeList"
                            placeholder="组件" v-model="form.BomCode" />
                    </v-col>
                    <v-col class="pt-0 mt-0" :cols="12" :lg="6">
                        <v-text-field :rules="[v => !!v || this.$t('GLOBAL._MANDATORY')]" label="故障名称" outlined dense
                            v-model="form.BreakdownName"></v-text-field>
                    </v-col>
                    <v-col class="pt-0 mt-0" :cols="12" :lg="12">
                        <v-textarea :rules="[v => !!v || this.$t('GLOBAL._MANDATORY')]" label="故障说明" outlined height="90"
                            v-model="form.Remark"></v-textarea>
                    </v-col>
                    <v-col class="pt-0 mt-0 text-right" :cols="12" :lg="12">
                        <v-btn :disabled="!selectTreeNode" color="primary" @click="saveForm">保存</v-btn>
                    </v-col>
                </v-row>
            </v-form>
        </div>

        <v-dialog scrollable persistent v-model="isShowPopup" width="30%">
            <v-card>
                <v-card-title>添加故障分类</v-card-title>
                <v-card-text class="pb-0">
                    <v-text-field class="mt-5" dense label="故障分类名称" v-model="faultName"></v-text-field>
                </v-card-text>
                <v-card-actions class="pa-5 lighten-3">
                    <v-spacer></v-spacer>
                    <v-btn text color="primary" @click="submitFaultName()">{{ $t('GLOBAL._QD') }}</v-btn>
                    <v-btn text @click="isShowPopup = false">{{ $t('GLOBAL._GB') }}</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import Util from '@/util'
import { getEquipByLine, getBomTree, saveFaultForm, getFaultTree, delFault } from './service'
export default {
    data() {
        return {
            tree: [],
            bomTreeList: [],
            normalizer(node) {
                const { id, name, children } = node;
                return {
                    id,
                    label: name,
                    children
                };
            },
            selectTreeNode: null,
            active: ['02305291-7433-2624-4c77-9ea500000000'],
            parentId: '',
            faultName: '',
            isShowPopup: false,
            equipList: [],
            lineList: [],
            form: {
                LineCode: [],
                DeviceCode: [],
                BomCode: undefined,
                BreakdownName: '',
                Remark: ''
            },
            items: []
        }
    },
    watch: {
        'form.LineCode': {
            handler(nv, ov) {
                if (nv) {
                    this.getEquipList()
                }
            }
        },
        'form.DeviceCode': {
            handler(nv, ov) {
                if (nv) {
                    this.getBomList()
                }
            }
        }
    },
    created() {
        this.getLine()
        this.getTreeData()
    },
    methods: {
        async saveForm() {
            if (!this.$refs.form.validate()) return false;
            let param = JSON.parse(JSON.stringify(this.form))
            console.log(param)
            param.LineCode = param.LineCode.join(',')
            param.DeviceCode = param.DeviceCode.join(',')
            let resp = await saveFaultForm({ ...param, ID: this.selectTreeNode.ID })
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });

            this.selectTreeNode.extendField = param.LineCode
            this.selectTreeNode.icon = param.DeviceCode
            this.selectTreeNode.title = param.BomCode
            this.selectTreeNode.name = param.BreakdownName
            this.selectTreeNode.remark = param.Remark
        },
        getTreeNodeData(id, list) {
            for (let i = 0; i < list.length; i++) {
                if (list[i].id == id) {
                    this.selectTreeNode = list[i]
                    break
                }
                if (list[i].children && list[i].children.length) {
                    this.getTreeNodeData(id, list[i].children)
                }
            }
        },
        handleChangeTree(items) {
            if (items && items.length) {
                this.getTreeNodeData(items[0], this.items)

                this.selectTreeNode.extendField && (this.form.LineCode = this.selectTreeNode.extendField.split(','))
                this.selectTreeNode.icon && (this.form.DeviceCode = this.selectTreeNode.icon.split(','))
                this.form.BomCode = this.selectTreeNode.title
                this.form.BreakdownName = this.selectTreeNode.name
                this.form.Remark = this.selectTreeNode.remark
            } else {
                this.form = {
                    LineCode: '',
                    DeviceCode: '',
                    BomCode: '',
                    BreakdownName: '',
                    Remark: ''
                }
                this.selectTreeNode = null
            }
        },
        del(item) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let resp = await delFault([item.id]);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });

                    this.getTreeData();
                })
                .catch(() => { });
        },
        async getTreeData() {
            let resp = await getFaultTree()
            this.items = resp.response
        },
        async submitFaultName() {
            if (!this.faultName) {
                this.$store.commit('SHOW_SNACKBAR', { text: '故障分类名称不能为空', color: 'error' });
                return false
            }
            let resp = await saveFaultForm({ BreakdownName: this.faultName, ParentId: this.parentId })
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
            this.isShowPopup = false
        },
        addFault(data) {
            console.log(data)
            data ? this.parentId = data.id : this.parentId = 0

            this.faultName = ''
            this.isShowPopup = true
        },
        // 查询设备
        async getEquipList() {
            let resp = await getEquipByLine({ linecode: this.form.LineCode.join(',') })
            this.equipList = resp.response
        },
        async getBomList() {
            let arr = this.equipList.filter(item => this.form.DeviceCode.includes(item.Code)).map(item => item.ID)
            let resp = await getBomTree({ eqid: arr.join(',') })
            this.bomTreeList = resp.response
        },
        async getLine() {
            this.lineList = await Util.GetEquipmenByLevel('Area');
        }
    }
}
</script>

<style lang="scss" scoped>
.equipment-fault-definition {
    display: flex;
    width: 100%;
    height: 100%;

    .tree-box {
        width: 300px;
        height: calc(100vh - 64px);
    }

    .form-box {
        width: calc(100% - 300px);
        height: calc(100vh - 64px);
        overflow: auto;
    }
}

.tree-title {
    display: block;
    width: 100%;
    height: 38px;
    font-weight: 500;
    padding: 8px;
    border-bottom: 1px solid #aaa;

    .v-label {
        cursor: pointer !important;
    }
}

.text--primary.tree-title.left {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.ant-popover-inner-content {

    p {
        cursor: pointer;
    }
}

::v-deep .vue-treeselect--disabled {

    .vue-treeselect__control {
        border: 1px solid #bdbdbd !important
    }
}

::v-deep .v-select__selections {
    input {
        position: absolute;
    }
}
</style>
<template>
  <div>
    <el-drawer class="drawer" :visible.sync="drawer" :direction="'rtl'" :before-close="handleClose"
      :append-to-body="false" size="80%">
      <div slot="title" class="title-box">
        <span>{{ `${currentRow.WeekScheduleOrderNo} | ${currentRow.LineCode} | ${currentRow.SegmentCode} | ${currentRow.MaterialCode}-${currentRow.MaterialName}` }}</span>
      </div>      
      <div class="table-box">
        <el-table v-loading="loading" :data="tableDataConsume" element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading" style="width: 100%" height="40vh">
          <el-table-column v-for="(item, index) in tableHeadDrawerConsume" :key="index" :prop="item.field" :label="item.label" :width="item.width" :align="item.alignType">
            <template slot-scope="scope">
              <span v-if="['PercentQuantity', 'AdjustPercentQuantity'].includes(item.field)">
                {{ scope.row[item.field] ? `${scope.row[item.field]}` : '-' }}
              </span>
              <span v-else-if="item.field === 'PoStatus'"> {{ status[scope.row[item.field]] }} </span>
              <span v-else> {{ scope.row[item.field] }} </span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="table-box">
        <el-table v-loading="loading" :data="tableDataProduce" element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading" style="width: 100%" height="40vh">
          <el-table-column v-for="(item, index) in tableHeadDrawerProduce" :key="index" :prop="item.field" :label="item.label" :width="item.width" :align="item.alignType">
            <template slot-scope="scope">
              <span v-if="['PercentQuantity', 'AdjustPercentQuantity'].includes(item.field)">
                {{ scope.row[item.field] ? `${scope.row[item.field]}` : '-' }}
              </span>
              <span v-else-if="item.field === 'PoStatus'"> {{ status[scope.row[item.field]] }} </span>
              <span v-else> {{ scope.row[item.field] }} </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>
    
  </div>
</template>

<script>
import {
  getProductionOrderConsumeList,
  getProductionOrderProduceList
} from '@/api/planManagement/weekSchedule'
import {getTableHead} from "@/util/dataDictionary.js";
export default {
  name: 'POListDetail',
  components: {
    
  },
  data() {
    return {
      searchForm: {},
      drawer: false,
      tableDataConsume: [],
      tableDataProduce: [],
      tableHeadDrawerConsume: [],
      tableHeadDrawerProduce: [],
      hansObjDrawerConsume: this.$t('WeekFormulationReport.ConsumeList'),
      hansObjDrawerProduce: this.$t('WeekFormulationReport.ProduceList'),
      tableOptionDrawerConsume:[        
        {code: 'MaterialCode', width: 150, align: 'left'},
        {code: 'MaterialName', width: 200, align: 'left'},
        {code: 'Quantity', width: 150, align: 'left'},
        {code: 'Unit', width: 100, align: 'left'},
        {code: 'Lot', width: 180, align: 'left'},
        {code: 'SubLot', width: 180, align: 'left'},
        {code: 'Operator', width: 100, align: 'left'},
        {code: 'OperateDate', width: 150, align: 'left'},
      ],
      tableOptionDrawerProduce:[        
        {code: 'MaterialCode', width: 150, align: 'left'},
        {code: 'MaterialName', width: 200, align: 'left'},
        {code: 'Quantity', width: 150, align: 'left'},
        {code: 'Unit', width: 100, align: 'left'},
        {code: 'Lot', width: 180, align: 'left'},
        {code: 'SubLot', width: 180, align: 'left'},
        {code: 'Operator', width: 100, align: 'left'},
        {code: 'OperateDate', width: 150, align: 'left'},
      ],
      loading: false,
      currentRow: {},
      status: {
        '1': 'Pending Release',
        '2': 'Released',
        '3': 'Complete',
        '4': 'Cancel',
        '5' : 'Stop',
        '6' : 'Running'
      }
    }
  },
  mounted() {
    this.initTableHead()
  },
  methods: {
    show(data) {
      console.log(data)
      this.currentRow = data
      this.drawer = true
      this.getTableDataConsume(data)
      this.getTableDataProduce(data)
    },
    handleClose() {
      this.drawer = false
    },
    async getTableDataConsume(data) {
      const { response } = await getProductionOrderConsumeList({
        ID: this.currentRow.ID,
        ...this.searchForm
      })
      this.tableDataConsume = response
    },
    async getTableDataProduce(data) {
      const { response } = await getProductionOrderProduceList({
        ID: this.currentRow.ID,
        ...this.searchForm
      })
      this.tableDataProduce = response
    },
    initTableHead() {
      this.tableHeadDrawerConsume = []
      this.tableHeadDrawerConsume = getTableHead(this.hansObjDrawerConsume, this.tableOptionDrawerConsume)
      console.log(this.tableHeadDrawerConsume);

      this.tableHeadDrawerProduce = []
      this.tableHeadDrawerProduce = getTableHead(this.hansObjDrawerProduce, this.tableOptionDrawerProduce)
      console.log(this.tableHeadDrawerProduce);
      this.$forceUpdate()      
    },
  }
}
</script>

<style lang="scss" scoped>
.drawer {
  :deep(.el-drawer__body) {
    padding-top: 10px;
    background-color: #FFFFFF;
    overflow-y: hidden
  }

  :deep(.el-form--inline) {
    height: 32px;
  }

  .title-box {
    font-size: 18px;
    color: #909399;
  }

  .pd5 {
    padding: 5px;
  }

  .table-box {
    padding: 0 10px;

    :deep(.el-button.is-disabled) {
      background-color: transparent !important;
      border: 0 !important;
    }

    i {
      margin-right: 5px;
      font-size: 15px !important;
      color: #67c23a;
    }
  }
}
</style>
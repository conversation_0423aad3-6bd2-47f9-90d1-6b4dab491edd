<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'SBBJGL_ADD'" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <!-- <v-btn color="primary" v-has="'SBTZGL_ADD'" @click="btnClickTB">{{ $t('GLOBAL._TB') }}</v-btn> -->
                    <v-btn color="primary" v-has="'SBBJGL_DC'" @click="handleExport()" >{{ $t('GLOBAL._EXPORT') }}</v-btn>
                    <v-btn color="primary" v-has="'SBBJGL_DR'" @click="handleImport()">{{ $t('GLOBAL._DR') }}</v-btn>
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_BJKZGL"
                    :headers="sparePartInventoryColum"
                    :desserts="desserts"
                    @selectePages="selectePages"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"
                ></Tables>
                <createRepast ref="createRepast" @loadData="RepastInfoGetPage" :dialogType="dialogType" :tableItem="tableItem"></createRepast>
            </v-card>
        </div>
        <div class="loading-box" v-if="importLoading">
            <a-spin tip="导入中..." :spinning="importLoading"></a-spin>
        </div>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';
import { mixins } from '@/util/mixins.js';
import { GetPartsInventoryPageList, GetPartsInventoryDelete, GetPartType, GetPartsInventoryImportData } from '@/api/equipmentManagement/Parts.js';
import { sparePartInventoryColum } from '@/columns/equipmentManagement/sparePart.js';
import { configUrl } from '@/config';
import { GetExportData } from '@/api/equipmentManagement/Equip.js';
import { getUnit } from '@/views/factoryPlant/unitManagement/service.js';

export default {
    name: 'RepastModel',
    components: {
        createRepast: () => import('./components/createRepast.vue')
    },
    mixins: [mixins],
    data() {
        return {
            importLoading: false,

            // tree 字典数据
            loading: true,
            showFrom: false,
            papamstree: {
                Name: '',
                Code: '',
                Type: '',
                Max: '',
                Min: '',
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            sparePartInventoryColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            rowtableItem: {},
            deleteList: [], //批量选中
            hasChildren: {}, // 新增字典详情判断-子节点才能新增,
            typecodelist: []
        };
    },
    computed: {
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'Code',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_BJFFGL._BJBM'),
                    placeholder: this.$t('TPM_SBGL_BJFFGL._BJBM')
                },
                {
                    value: '',
                    key: 'Name',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_BJFFGL._BJMC'),
                    placeholder: this.$t('TPM_SBGL_BJFFGL._BJMC')
                },
                {
                    value: '',
                    selectData: this.typecodelist,
                    type: 'select',
                    key: 'Type',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Jigtype'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Jigtype')
                },
                {
                    value: '',
                    key: 'Max',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL._KCDY'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL._KCDY')
                },
                {
                    value: '',
                    key: 'Min',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL._KCXY'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL._KCXY')
                }
            ];
        },
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBBJGL_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBBJGL_DELET'
                }
            ];
        }
    },
    async mounted() {
        this.RepastInfoGetPage();
        let PartUnit = await this.$getNewDataDictionary('PartUnit');
        this.$refs.createRepast.SbxxList[7].options = PartUnit;
        this.typecodelist = await this.$getNewDataDictionary('PartType');
    },
    methods: {
        async handleImport() {
            let _this = this;
            let input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xls,.xlsx';
            input.click();
            let Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            input.onchange = async function () {
                let file = input.files[0];
                let formdata = new FormData();
                formdata.append('file', file);

                _this.importLoading = true;
                try {
                    let res = await GetPartsInventoryImportData(formdata, Factory);
                    _this.$store.commit('SHOW_SNACKBAR', { text: res.response });
                    _this.RepastInfoGetPage();
                    _this.importLoading = false;
                } catch {
                    _this.importLoading = false;
                }
            };
        },
        btnClickTB() {},
        async handleExport() {
            let params = {
                ...this.papamstree
            };
            const baseUrl3 = configUrl[process.env.VUE_APP_SERVE]['baseURL_EQUIPMENT'] + `/api/PartsInventory/ExportData`;
            let res = await GetExportData(baseUrl3, params);
            let binaryData = [];
            binaryData.push(res);
            const url = window.URL.createObjectURL(new Blob(binaryData));
            console.log(url);
            const link = document.createElement('a');
            link.href = url;
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            let fileName = `备件库存${formattedDateTime}.xlsx`;
            document.body.appendChild(link);
            link.setAttribute('download', fileName);
            link.click();
            window.URL.revokeObjectURL(link.href);
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.RepastInfoGetPage();
        },
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                ...this.papamstree
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading = true;
            const res = await GetPartsInventoryPageList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
                this.rowtableItem = this.desserts[0] || {};
            }
        },

        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'QRcodes':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'add':
                    this.$refs.createRepast.getPartList();
                    this.$refs.createRepast.SbxxList[1].options = this.typecodelist;
                    this.$refs.createRepast.SbxxList.forEach(item => {
                        item.value = '';
                    });
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            console.log(item);
            switch (type) {
                case 'edit':
                    for (let k in this.tableItem) {
                        this.$refs.createRepast.SbxxList.forEach(item => {
                            if (k == item.id) {
                                item.value = this.tableItem[k];
                            }
                        });
                    }
                    this.$refs.createRepast.getPartList();
                    this.$refs.createRepast.SbxxList[1].options = this.typecodelist;
                    this.$refs.createRepast.SbxxList[0].value = this.tableItem.PartsId + '|' + this.tableItem.Name + '|' + this.tableItem.Code + '|' + this.tableItem.Model;
                    this.$refs.createRepast.showDialog = true;
                    return;
                // case 'etc':
                case 'printCode':
                    item.QRCode = JSON.stringify({
                        Remark: item.Remark,
                        SparePartsCode: item.SparePartsCode
                    });
                    this.tableItem = item;
                    this.$nextTick(this.PrintTemplateFn(item));

                    // this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable(type);
                    return;
                case 'delete2':
                    this.deltable(type);
                    return;
            }
        },
        // 删除
        deltable(type) {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    if (type == 'delete') {
                        let res = await GetPartsInventoryDelete(params);
                        if (res.success) {
                            this.tableItem = {};
                            this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                            this.RepastInfoGetPage();
                        }
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}
</style>

<template>
    <v-dialog v-model="showDialog" max-width="980px">
        <!-- 待办 -->
        <v-card class="" v-if="dialogType == 'repair'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XQ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="editedItem.AbnormalDesc" rows="3" disabled outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.ExceptionDesc')"></v-textarea>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="form.RepairProcess" rows="3" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairProcess')"></v-textarea>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete
                                v-model="form.Reasons1"
                                @change="getReason"
                                :items="ressionList"
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.Reasons1')"
                                item-value="id"
                                item-text="name"
                                return-object
                                outlined
                                dense
                            ></v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="form.RepairStatus" :items="equipStatuslist" item-value="ItemValue" item-text="ItemName" outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairStatus')"></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete
                                v-model="form.RepairUser"
                                :loading="loading"
                                :items="peopleitems"
                                item-value="Code"
                                item-text="Name"
                                :search-input="form.RepairUser"
                                flat
                                outlined
                                dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairUser')"
                            >
                                <template #item="data">
                                    <template v-if="typeof data.item !== 'object'">
                                        <v-list-item-content v-text="data.item"></v-list-item-content>
                                    </template>
                                    <template v-else>
                                        <v-list-item-content>
                                            <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                            <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                        </v-list-item-content>
                                    </template>
                                </template>
                            </v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="form.CurrentSituation" outlined rows="3" dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.CurrentSituation')"></v-textarea>
                        </v-col>
                        <v-col class="py-0" cols="12" sm="4" md="4">
                            <v-autocomplete
                                v-model="form.PartsCode1"
                                :loading="loading"
                                :items="PartsList"
                                item-value="SparePartsCode"
                                item-text="SparePartsName"
                                :search-input="form.PartsCode1"
                                flat
                                outlined
                                dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.Parts1')"
                            ></v-autocomplete>
                        </v-col>
                        <v-col class="py-0" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Parts1Num" outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.Parts1Num')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Starttime" type="datetime-local" outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.StartTime')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Endtime" type="datetime-local" outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.EndTime')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.RepairHours" outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairHours')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.RepairPrice" type="number" outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairPrice')"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-checkbox v-model="form.IsCase" :label="$t('TPM_SBGL_SBWXJL._SFCCWZSK')"></v-checkbox>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions pa-4 class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('2')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <!-- 进行中 -->
        <v-card class="" v-if="dialogType == 'working'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XQ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="editedItem.AbnormalDesc" rows="3" disabled outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.ExceptionDesc')"></v-textarea>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="detaileobj.RepairProcess" rows="3" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairProcess')"></v-textarea>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete
                                v-model="detaileobj.Reasons1"
                                @change="getReason"
                                :items="ressionList"
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.Reasons1')"
                                item-value="id"
                                item-text="name"
                                return-object
                                outlined
                                dense
                            ></v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="detaileobj.RepairStatus" :items="equipStatuslist" item-value="ItemValue" item-text="ItemName" outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairStatus')"></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete
                                v-model="detaileobj.RepairUser"
                                :loading="loading"
                                :items="peopleitems"
                                item-value="Code"
                                item-text="Name"
                                :search-input="detaileobj.RepairUser"
                                flat
                                outlined
                                dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairUser')"
                            >
                                <template #item="data">
                                    <template v-if="typeof data.item !== 'object'">
                                        <v-list-item-content v-text="data.item"></v-list-item-content>
                                    </template>
                                    <template v-else>
                                        <v-list-item-content>
                                            <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                            <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                        </v-list-item-content>
                                    </template>
                                </template>
                            </v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="detaileobj.CurrentSituation" outlined rows="3" dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.CurrentSituation')"></v-textarea>
                        </v-col>
                        <v-col class="py-0" cols="12" sm="4" md="4">
                            <v-autocomplete
                                v-model="detaileobj.PartsCode1"
                                :loading="loading"
                                :items="PartsList"
                                item-value="SparePartsCode"
                                item-text="SparePartsName"
                                :search-input="detaileobj.PartsCode1"
                                flat
                                outlined
                                dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.Parts1')"
                            ></v-autocomplete>
                        </v-col>
                        <v-col class="py-0" cols="12" sm="4" md="4">
                            <v-text-field v-model="detaileobj.Parts1Num" outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.Parts1Num')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="detaileobj.Starttime" type="datetime-local" outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.StartTime')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="detaileobj.Endtime" type="datetime-local" outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.EndTime')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="detaileobj.RepairHours" outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairHours')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="detaileobj.RepairPrice" type="number" outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairPrice')"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-checkbox v-model="detaileobj.IsCase" :label="$t('TPM_SBGL_SBWXJL._SFCCWZSK')"></v-checkbox>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions pa-4 class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('1')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <!-- 已办 -->
        <v-card class="" v-if="dialogType == 'over'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XQ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="editedItem.AbnormalDesc" rows="3" disabled outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.ExceptionDesc')"></v-textarea>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="detaileobj.RepairProcess" rows="3" disabled outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairProcess')"></v-textarea>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete
                                v-model="detaileobj.Reasons1"
                                @change="getReason"
                                :items="ressionList"
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.Reasons1')"
                                item-value="id"
                                item-text="name"
                                disabled
                                return-object
                                outlined
                                dense
                            ></v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="detaileobj.RepairStatus" disabled :items="equipStatuslist" item-value="ItemValue" item-text="ItemName" outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairStatus')"></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete
                                v-model="detaileobj.RepairUser"
                                :loading="loading"
                                :items="peopleitems"
                                item-value="Code"
                                item-text="Name"
                                :search-input="detaileobj.RepairUser"
                                flat
                                disabled
                                outlined
                                dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairUser')"
                            >
                                <template #item="data">
                                    <template v-if="typeof data.item !== 'object'">
                                        <v-list-item-content v-text="data.item"></v-list-item-content>
                                    </template>
                                    <template v-else>
                                        <v-list-item-content>
                                            <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                            <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                        </v-list-item-content>
                                    </template>
                                </template>
                            </v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="detaileobj.CurrentSituation" disabled outlined rows="3" dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.CurrentSituation')"></v-textarea>
                        </v-col>
                        <v-col class="py-0" cols="12" sm="4" md="4">
                            <v-autocomplete
                                v-model="detaileobj.PartsCode1"
                                :loading="loading"
                                :items="PartsList"
                                item-value="SparePartsCode"
                                item-text="SparePartsName"
                                :search-input="detaileobj.PartsCode1"
                                flat
                                disabled
                                outlined
                                dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.Parts1')"
                            ></v-autocomplete>
                        </v-col>
                        <v-col class="py-0" cols="12" sm="4" md="4">
                            <v-text-field v-model="detaileobj.Parts1Num" disabled outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.Parts1Num')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="detaileobj.Starttime" type="datetime-local" disabled outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.StartTime')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="detaileobj.Endtime" type="datetime-local" disabled outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.EndTime')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="detaileobj.RepairHours" disabled outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairHours')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="detaileobj.RepairPrice" type="number" disabled outlined dense  :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairPrice')"></v-text-field>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-4 lighten-3">
                <!-- <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox> -->
                <v-spacer></v-spacer>
                <!-- <v-btn color="primary" @click="addSave">{{ $t('GLOBAL._QD') }}</v-btn> -->
                <v-btn color="primary" @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'debug'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                调试
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form1" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="debugForm.Debugtype" outlined dense label="调试类型"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete
                                v-model="debugForm.Maintenanceperson"
                                :loading="loading"
                                :items="peopleitems"
                                item-value="Code"
                                item-text="Name"
                                :search-input="debugForm.Maintenanceperson"
                                flat
                                outlined
                                dense
                                label="处理人姓名"
                            >
                                <template #item="data">
                                    <template v-if="typeof data.item !== 'object'">
                                        <v-list-item-content v-text="data.item"></v-list-item-content>
                                    </template>
                                    <template v-else>
                                        <v-list-item-content>
                                            <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                            <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                        </v-list-item-content>
                                    </template>
                                </template>
                            </v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="debugForm.Status" :items="equipStatuslist" item-value="ItemValue" item-text="ItemName" outlined dense label="设备状态"></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="debugForm.Failuredescription" outlined rows="3" dense label="调式描述"></v-textarea>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="debugForm.Dealtime" type="datetime-local" outlined dense label="开始处理时间"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="debugForm.Fixedtime" type="datetime-local" outlined dense label="调试结束时间 "></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="debugForm.Remark" outlined rows="2" dense label="备注"></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="adddebug">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <QRcode ref="QRcode" @getQRcodesRes="getQRcodesRes"></QRcode>
    </v-dialog>
</template>
<script>
import { GetReasontree } from '@/api/factoryPlant/reasonDetail.js';
import { CommissioningRecordsSaveForm, DeviceRepairSaveForm, DeviceRepairGetPageList, UpdateStatusForm } from '@/api/equipmentManagement/Repair.js';
import { StaffSiteGetList } from '@/api/peopleManagement/basicdata.js';
import { SparepartGetList } from '@/api/equipmentManagement/sparePart.js';
import { DeviceRepairProjectGetList } from '@/api/equipmentManagement/equipmentReasonTree.js';
export default {
    props: {
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        },
        equipStatuslist: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            valid: true,
            showDialog: false,
            classcheckbox: true,
            peopleitems: [],
            ressionList: [],
            PartsList: [], //备件列表
            strbatchNo: '',
            detaileobj: {}, //维修展示详情
            form: {
                ExceptionDesc: '',
                RepairProcess: '',
                CurrentSituation: '',
                Reasons1: '',
                ReasonsId1: '',
                Parts1: '',
                Parts1Name: '',
                Parts1Code: '',
                Parts1Num: '',
                RepairStatus: '',
                RepairNature: '',
                RepairUser: '',
                Starttime: '',
                Endtime: '',
                RepairHours: '',
                RepairPrice: '',
                IsCase: false
            },
            debugForm: {
                Debugtype: '',
                Debugcode: '',
                Failuredescription: '',
                Maintenanceperson: '',
                Dealtime: '',
                Fixedtime: '',
                Status: ''
            }
        };
    },
    computed: {
        editedItem() {
            const {
                AbnormalDesc,
                ExceptionDesc,
                RepairProcess,
                CurrentSituation,
                ReasonsId1,
                Reasons1,
                Parts1,
                PartsCode1,
                Parts1Num,
                RepairStatus,
                RepairNature,
                RepairUser,
                Starttime,
                Endtime,
                RepairHours,
                RepairPrice
            } = this.tableItem;
            return {
                AbnormalDesc,
                ExceptionDesc,
                RepairProcess,
                CurrentSituation,
                Reasons1,
                ReasonsId1,
                Parts1,
                PartsCode1,
                Parts1Num,
                RepairStatus,
                RepairNature,
                RepairUser,
                Starttime,
                Endtime,
                RepairHours,
                RepairPrice,
                IsCase: this.tableItem.IsCase == '1' ? true : false
            };
        }
    },
    watch: {
        showDialog: {
            handler(newa, olda) {
                if (newa) {
                    switch (this.dialogType) {
                        case 'repair':
                            this.form.RepairStatus = '2';
                            return;
                        case 'working':
                            this.form.RepairStatus = '2';
                            return;
                        case 'over':
                            this.form.RepairStatus = '1';
                            return;
                    }
                }
                if (olda) {
                    this.$refs.form.reset();
                }
            },
            deep: true,
            immediate: true
        }
    },
    created() {
        this.queryPeoplelist();
        this.getReasonTreeList();
        this.getSparepartGetList();
    },
    methods: {
        getQRcodes() {
            this.$refs.QRcode.getQRcode();
        },
        // 获取查询结果
        getQRcodesRes(value) {
            let val = value.text;
            this.form.PartsCode1 = val;
        },
        // 获取原因树型
        async getReasonTreeList() {
            const res = await GetReasontree();
            const { success, response } = res;
            if (success) {
                const data = response;
                const datalist = data.find(item => item.name == '停机原因');
                this.ressionList = datalist.children;
            }
        },
        // 获取原因分析
        async getReason(v) {
            if (v) this.form.CurrentSituation = '';
            let params = {
                personcode: v.id,
                pageIndex: 1,
                pageSize: 9999
            };
            const res = await DeviceRepairProjectGetList(params);
            let { success, response } = res;
            if (success) {
                response.forEach(item => {
                    this.form.CurrentSituation += item.Methods;
                });
                // console.log(response)
                // this.form.CurrentSituation = response
            }
        },
        closeEquip() {
            this.showDialog = false;
            this.$refs.form.reset();
        },
        // 获取人员
        async queryPeoplelist() {
            this.loading = true;
            const res = await StaffSiteGetList({ key: '' });
            let { success, response } = res;
            if (success) {
                this.peopleitems = response;
                this.loading = false;
            }
        },
        // 维修
        async addSave(type) {
            const paramsKey = Object.keys(this.form);
            const paramsObj = type === '2' ? this.form : this.detaileobj;
            let params = {};
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            const { PartsCode1, Reasons1 } = paramsObj;
            let obj = {};
            if (PartsCode1.SparePartsCode) {
                const { SparePartsCode, SparePartsName } = PartsCode1;
                obj = { PartsCode1: SparePartsCode, Parts1: SparePartsName };
            }
            if (Reasons1.id) {
                const { id, name } = Reasons1;
                obj = { ...obj, Reasons1: name, ReasonsId1: id };
            }
            Object.assign(params, obj);
            params.WoId = this.tableItem.ID;
            params.WoCode = this.tableItem.RepairCode;
            params.DeviceName = this.tableItem.DeviceName;
            params.DeviceCode = this.tableItem.DeviceCode;
            // params.Reasons1 = this.jionReson(paramsObj.Reasons1, 'name');
            // params.ReasonsId1 = this.jionReson(paramsObj.Reasons1, 'id');
            params.RepairStatus = type;
            params.IsCase = paramsObj.IsCase ? '1' : '0';
            const res = await DeviceRepairSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.showDialog = this.classcheckbox ? false : true;
                this.$parent.$parent.RepastInfoGetPage(type == '2' ? '0' : '2');
                await this.getUpdateStatusForm(params);
            }
        },
        // 调试
        async adddebug() {
            const paramsKey = Object.keys(this.debugForm);
            const paramsObj = this.debugForm;
            let params = {};
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            params.Equipid = this.tableItem.ID;
            const res = await CommissioningRecordsSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.showDialog = this.classcheckbox ? false : true;
                this.$parent.$parent.$refs.debuggingDetails.CommissioningRecords(this.tableItem);
            }
        },
        // 多原因；字符串拼接
        jionReson(Arr, str) {
            let strJoin = [];
            Arr.forEach(item => {
                strJoin.push(item[str]);
            });
            return strJoin.join();
        },
        // 获取配件
        async getSparepartGetList() {
            let params = {
                key: '',
                type: '',
                sparepartscode: '',
                pageIndex: 1,
                pageSize: 999
            };
            const res = await SparepartGetList(params);
            let { success, response } = res;
            if (success) {
                this.PartsList = response;
            }
        },
        // 获取维修详情
        async RepastInfologGetPage(item) {
            let params = {
                woid: item.ID || this.tableItem.ID,
                pageIndex: 1,
                pageSize: 10
            };
            this.loading = true;
            const res = await DeviceRepairGetPageList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.detaileobj = (response || {}).data[0] || {};
            }
        },
        // 更细维修单状态
        async getUpdateStatusForm(item) {
            let params = {
                ID: item.WoId,
                RepairStatus: item.RepairStatus
            };
            const res = await UpdateStatusForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '更新成功', color: 'success' });
            }
        }
    }
};
</script>

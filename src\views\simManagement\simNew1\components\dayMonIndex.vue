<template>
  <div style="display: flex;width: 100%;justify-content: flex-end">
    <div
      class="titName"
      v-for="(item,index) in Dimension"
      :key="index"
      :class="{'active':Particle == item.TimeDimensionName}"
      @click="tabClick(item,index)"
    >{{item.TimeDimensionName}}</div>
    <div
      style="width:30px;height:30px;cursor: pointer;"
      @click="screenChnange()"
    >
      <img
        style="width:100%;height:100%;"
        src="../image/qpimg.png"
      />
    </div>
    <el-popover
      placement="right"
      trigger="click"
      :append-to-body="false"
      style="cursor: pointer;"
    >
      <div style="width: auto;margin-bottom: 10px;">
        <!-- <v-file-input
          placeholder="导入数据"
          accept=".xml,.xlsx"
          style="font-size: 12px;height: 50px;cursor: pointer;"
          @click="beforeUpload()"
        ></v-file-input> -->
        <div style="display: flex;">
          <v-btn
            style="width: 93px;"
            color="primary"
            @click="beforeUpload"
            class="ml-3"
          >{{ $t('DFM_MXLR._DR') }}</v-btn>
          <el-button
            style="margin-top: 0px;margin-left: 5%;width: 93px;"
            type="primary"
            size="mini"
            @click="deriveExcel()"
          >导出数据</el-button>
          <el-button
            v-if="legendData.length>0"
            style="margin-top: 0px;margin-left: 5%;width: 93px;"
            type="primary"
            size="mini"
            @click="save()"
          >确 定</el-button>
          <el-button
            v-if="legendData.length>0"
            style="margin-top: 0px;margin-left: 5%;width: 93px;"
            type="primary"
            size="mini"
            @click="allChange()"
          >全选</el-button>
          <el-button
            v-if="legendData.length>0"
            style="margin-top: 0px;margin-left: 5%;width: 93px;"
            type="primary"
            size="mini"
            @click="cancel()"
          >取消选择</el-button>
        </div>
      </div>
      <span
        v-if="legendData.length>0"
        style="font-weight: bold;font-size: 16px;"
      >{{title}}:&nbsp;请勾选图表需要显示的内容:</span>
      <el-checkbox-group
        v-if="legendData.length>0"
        v-model="checkedCities"
        style="width:400px;margin-top: 5px;height:400px;overflow-y: auto;"
      >
        <el-checkbox
          v-for="city in legendData"
          :label="city"
          :key="city"
          :disabled="city == 'x'"
          @change="handleCheckboxChange(city)"
        >
          <div style="width:300px;">{{city}}</div>
        </el-checkbox>
      </el-checkbox-group>
      <div
        style="width:22px;height:22px;margin-top:3px;"
        slot="reference"
      >
        <img
          style="width:100%;height:100%;cursor: pointer;"
          src="../image/simcd.png"
        />
      </div>

    </el-popover>

    <screen
      :Particle="Particle"
      v-if="showLookData1"
      v-model="LookDataShow1"
      :id1="id1"
      :position="position"
      :simlevel="simlevel"
      :BaseTime="BaseTime"
      :title="title"
      :backgroundImg="backgroundImg"
      @showCheck1="handleLookData1"
      @showCheckch="handleLookData11"
      :legendData1="checkedCities"
      ref="screenref"
      :colorList="colorList"
      :cxColor="cxColor"
      :cxStatus="cxStatus"
    />

    <screenLine
      v-if="showLookData2"
      :Particle="Particle"
      v-model="LookDataShow2"
      :id1="id1"
      :position="position"
      :simlevel="simlevel"
      :BaseTime="BaseTime"
      :titleline="titleline"
      :backgroundImg="backgroundImg"
      @showCheck2="handleLookData2"
      ref="screenrefline"
      :legendData1="checkedCities"
      :colorList="colorList"
    />

    <screenBarLine
      v-if="showLookData3"
      Particle="Particle"
      v-model="LookDataShow3"
      :id1="id1"
      :position="position"
      :simlevel="simlevel"
      :BaseTime="BaseTime"
      :titlebarline="titlebarline"
      :backgroundImg="backgroundImg"
      @showCheck3="handleLookData3"
      ref="screenBarLineref"
    />

    <screenBarTran
      v-if="showLookData4"
      v-model="LookDataShow4"
      :id1="id1"
      :position="position"
      :simlevel="simlevel"
      :BaseTime="BaseTime"
      :titlebartran="titlebartran"
      :backgroundImg="backgroundImg"
      @showCheck4="handleLookData4"
      ref="screenBarTranref"
    />

    <screenPie
      v-if="showLookData5"
      v-model="LookDataShow5"
      :id1="id1"
      :position="position"
      :simlevel="simlevel"
      :BaseTime="BaseTime"
      :titlepie="titlepie"
      :backgroundImg="backgroundImg"
      @showCheck5="handleLookData5"
      ref="screenPieref"
    />

    <screenCircular
      v-if="showLookData6"
      v-model="LookDataShow6"
      :id1="id1"
      :position="position"
      :simlevel="simlevel"
      :BaseTime="BaseTime"
      :titlecir="titlecir"
      :backgroundImg="backgroundImg"
      @showCheck6="handleLookData6"
      ref="screenCircularref"
    />

    <screenTable
      v-if="showLookData7"
      v-model="LookDataShow7"
      :id1="id1"
      :position="position"
      :simlevel="simlevel"
      :BaseTime="BaseTime"
      :titletbale="titletbale"
      :backgroundImg="backgroundImg"
      @showCheck7="handleLookData7"
      ref="screenTableref"
    />
    <screenMeter
      v-if="showLookData8"
      v-model="LookDataShow8"
      :id1="id1"
      :position="position"
      :simlevel="simlevel"
      :BaseTime="BaseTime"
      :titletbale="titletbale"
      :backgroundImg="backgroundImg"
      @showCheck8="handleLookData8"
      ref="screenMeterref"
    />
  </div>
</template>
<script>
import { ImportExcel, deriveExcel } from '@/api/simConfig/simconfignew.js';
import { configUrl } from '@/config';

export default {
  components: {
    screen: () => import('@/views/simManagement/simNew1/components/screen.vue'),
    screenLine: () => import('@/views/simManagement/simNew1/components/screenLine.vue'),
    screenBarLine: () => import('@/views/simManagement/simNew1/components/screenBarLine.vue'),
    screenBarTran: () => import('@/views/simManagement/simNew1/components/screenBarTran.vue'),
    screenPie: () => import('@/views/simManagement/simNew1/components/screenPie.vue'),
    screenCircular: () => import('@/views/simManagement/simNew1/components/screenCircular.vue'),
    screenTable: () => import('@/views/simManagement/simNew1/components/screenTable.vue'),
    screenMeter: () => import('@/views/simManagement/simNew1/components/screenMeter.vue'),
  },
  props: {
    position: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    Dimension: {
      type: Array,
      default: () => []
    },
    Particle: {
      type: String,
      default: ''
    },
    id1: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    echarstType: {
      type: Number,
      default: 0
    },
    title: {
      type: String,
      default: ''
    },
    titlebarline: {
      type: String,
      default: ''
    },
    titlebartran: {
      type: String,
      default: ''
    },
    titlecir: {
      type: String,
      default: ''
    },
    titlepie: {
      type: String,
      default: ''
    },
    titleline: {
      type: String,
      default: ''
    },
    titletbale: {
      type: String,
      default: ''
    },
    titlemete: {
      type: String,
      default: ''
    },
    backgroundImg: {
      type: String,
      default: ''
    },
    legendData: {
      type: Array,
      default: () => []
    },
    resultArray1: {
      type: Array,
      default: () => []
    },
    colorList: {
      type: Array,
      default: () => []
    },
    cxColor: {
      type: String,
      default: ''
    },
    cxStatus: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      screenlinekey: 1,
      screenkey: 1,
      legendDatanew: [],
      checkedCities: [],
      imporValue: '导入数据',
      showLookData1: false,
      LookDataShow1: false,

      showLookData2: false,
      LookDataShow2: false,

      showLookData3: false,
      LookDataShow3: false,

      showLookData4: false,
      LookDataShow4: false,

      showLookData5: false,
      LookDataShow5: false,

      showLookData6: false,
      LookDataShow6: false,

      showLookData7: false,
      LookDataShow7: false,

      showLookData8: false,
      LookDataShow8: false,

      btnloding: false,
      fileInfo: null,
      checkedItems: {},
      legendDatac: this.legendData,
      uniqueDataObject: {}
    }
  },
  mounted() {
    this.checkedCities = this.resultArray1;
  },
  methods: {
    allChange() {
      this.checkedCities = this.legendData
    },
    cancel() {
      this.checkedCities = []
      this.legendDatanew.splice(0);
    },
    handleCheckboxChange(value) {
      // console.log(this.checkedCities, 'checkedCitiescheckedCities');


      const index = this.legendDatanew.indexOf(value);
      if (index > -1) {
        this.legendDatanew.splice(index, 1);
      } else {
        this.legendDatanew.push(value);
      }
      this.checkedCities = this.checkedCities.filter(item => {
        return !item.includes('目标值') && !item.includes('上限') && !item.includes('下限');
      });
      console.log(this.checkedCities, 'this.checkedCities');

    },
    save() {
      if (localStorage.getItem('list')) {
        let listData = JSON.parse(localStorage.getItem('list'))
        Object.keys(listData).map(el => {
          if (el == this.position) {
            if (this.checkedCities.length > 0) {
              this.checkedCities.map(item => {
                if (Object.keys(listData).includes(el)) {
                  listData[el] = this.checkedCities
                }
              })
            }
            localStorage.setItem('list', JSON.stringify(listData));
          } else {
            this.$store.dispatch('list/saveSelectedData', {
              componentId: this.position,
              data: this.checkedCities
            });
            localStorage.setItem('list', JSON.stringify(this.$store.getters['list/getSelectedDataByComponentId']));
          }
        })
      } else {
        this.$store.dispatch('list/saveSelectedData', {
          componentId: this.position,
          data: this.checkedCities
        });
        localStorage.setItem('list', JSON.stringify(this.$store.getters['list/getSelectedDataByComponentId']));
      }
      // Object.keys(this.$store.getters['list/getSelectedDataByComponentId']).forEach(key => {
      //   this.uniqueDataObject[key] = this.$store.getters['list/getSelectedDataByComponentId'][key].reduce((acc, item) => {
      //     if (!acc.includes(item)) {
      //       acc.push(item);
      //     }
      //     return acc;
      //   }, []);
      // });

      console.log(this.checkedCities, 'this.checkedCities');

      this.$emit('showChack1', this.checkedCities)
    },
    handleLookData1(data) {
      this.checkedCities = data
      this.$emit('showChack1', this.checkedCities)
      this.showLookData1 = false
    },
    handleLookData11(data) {
      // this.checkedCities = data
      // this.showLookData1 = true
      this.$emit('showChack1', data)
      this.showLookData1 = false

    },
    handleLookData2() {
      this.showLookData2 = false
    },
    handleLookData3() {
      this.showLookData3 = false
    },
    handleLookData4() {
      this.showLookData4 = false
    },
    handleLookData5() {
      this.showLookData5 = false
    },
    handleLookData6() {
      this.showLookData6 = false
    },
    handleLookData7() {
      this.showLookData7 = false
    },
    handleLookData8() {
      this.showLookData8 = false
    },
    screenChnange() {
      if (this.echarstType == '1') {
        // this.screenlinekey++
        this.showLookData2 = true
        this.LookDataShow2 = true
      }
      if (this.echarstType == '2') {
        // this.screenkey++
        this.showLookData1 = true
        this.LookDataShow1 = true
      }
      if (this.echarstType == '3') {
        this.showLookData4 = true
        this.LookDataShow4 = true
      }
      if (this.echarstType == '4') {
        this.showLookData3 = true
        this.LookDataShow3 = true
      }
      if (this.echarstType == '5') {
        this.showLookData5 = true
        this.LookDataShow5 = true
      }
      if (this.echarstType == '6') {
        this.showLookData6 = true
        this.LookDataShow6 = true
      }
      if (this.echarstType == '8') {
        this.showLookData7 = true
        this.LookDataShow7 = true
      }
      if (this.echarstType == '7') {
        this.showLookData8 = true
        this.LookDataShow8 = true
      }

    },
    tabClick(item, index) {
      this.$emit('showChack', item.TimeDimensionName)
    },
    async beforeUpload() {
      // var formData = new window.FormData();
      // formData.append('file', this.fileInfo);
      // if (formData) {
      //   let res = await ImportExcel(this.simlevel, this.position, formData)
      //   if (res.success) {
      //     this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });
      //   }
      //   this.$emit('showChack')
      // }

      let _this = this;
      let input = document.createElement('input');
      input.type = 'file';
      input.accept = '.xls,.xlsx';
      input.click();
      input.onchange = async function () {
        let file = input.files[0];
        let formdata = new FormData();
        formdata.append('file', file);

        _this.importLoading = true;
        try {
          let res = await ImportExcel(_this.simlevel, _this.position, formdata)
          if (res.success) {
            _this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });

            _this.$emit('showChack', _this.Dimension[0].TimeDimensionName)
          }
        } catch {
          console.log();

        }
      };

    },
    async deriveExcel() {
      let simlevel = this.position.split('-')[0]
      let position = this.position
      let chktime = this.BaseTime
      let TimeDimension = this.Particle
      let TeamCode = this.simlevel
      let Mark = ''
      let Shift = ''
      const params = {
        simlevel: simlevel,
        position: position,
        chktime: chktime,
        TimeDimension: TimeDimension,
        TeamCode: TeamCode,
        Mark: '',
        Shift: '',
        responseType: 'blob'
      }
      let resp = await deriveExcel(params);
      // let blob = new Blob([resp], {
      //   type: 'application/octet-stream'
      // });
      // //const now = new Date();
      // //const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
      // var link = document.createElement('a');
      // link.href = window.URL.createObjectURL(blob);
      // link.download = `福华指标数据.xls`; // 修正模板字符串语法
      // link.click();
      // //释放内存
      // window.URL.revokeObjectURL(link.href);
      let blob = new Blob([resp], {
        type: "application/octet-stream",
      });
      var link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.download = "指标数据.xls";
      link.click();
      //释放内存
      window.URL.revokeObjectURL(link.href);

    },
  },
}
</script>
<style  scoped>
.titName {
    color: #fff;
    font-size: 14px;
    margin-right: 10px;
    font-weight: 600;
    line-height: 30px;
    cursor: pointer;
}
.active {
    width: 40px;
    height: 30px;
    color: #fff;
    font-weight: 800;
    background-color: #409eff;
    border-radius: 5px;
    font-size: 16px;
    line-height: 30px;
    text-align: center;
}
/deep/ .v-text-field > .v-input__control > .v-input__slot:before {
    border-style: none !important;
}
</style>
<template>
    <!-- 处理告警 -->
    <v-dialog v-model="dialog" persistent max-width="800px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                <span>{{ $t('ANDON_BJJL.close') }}--{{ operaObj.AreaName }}{{ operaObj.ProductLineName?' , ' + operaObj.ProductLineName: ''}} {{ operaObj.UnitName?' , ' + operaObj.UnitName: '' }}
                    {{ operaObj.problemLevel?'(' + operaObj.problemLevel + ')': '' }}</span>
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="mt-6 alarm-home-pdt">
                <v-form ref="form" v-model="valid">
                <v-row>
                    <!-- 一级分类 -->
                    <v-col :cols="6" class="activ-style activ-txt alarm-bdmr" :lg="6"> 
                        {{ $t('$vuetify.dataTable.ANDON_BJJL.MainAlarm') }}
                    </v-col>
                    <!-- 二级分类 -->
                    <v-col :cols="6" class="activ-style activ-txt" :lg="6">
                        {{ $t('$vuetify.dataTable.ANDON_BJJL.SubAlarm') }}
                    </v-col>
                    <!-- 一级分类 -->
                    <v-col :cols="6" :lg="6" class="activ-style activ-height activ-background alarm-bdmr">
                        <v-text-field class="white-bk" v-model="form.MainAlarm" disabled dense/>
                    </v-col>
                    <!-- 二级分类 -->
                    <v-col :cols="6" :lg="6" class="activ-style activ-height activ-background">
                        <v-text-field class="white-bk" v-model="form.SubAlarm" disabled dense/>
                    </v-col>
                    <!--事件等级 -->
                    <v-col :cols="6" class="activ-style activ-txt alarm-bdmr" :lg="6">
                        {{ $t('$vuetify.dataTable.ANDON_BJJL.EventLevel') }}
                    </v-col>
                    <!-- 负责人 -->
                    <v-col :cols="6" class="activ-style activ-txt" :lg="6">
                        {{ $t('$vuetify.dataTable.ANDON_BJJL.Currentduty') }}
                    </v-col>
                    <!-- 事件等级 -->
                    <v-col :cols="6" :lg="6" class="activ-style activ-height alarm-bdmr activ-background">
                        <v-text-field class="white-bk" v-model="form.EventLevel" disabled dense/>
                    </v-col>
                    <!-- 负责人 -->
                    <v-col :cols="6" class="activ-style activ-height activ-background" :lg="6">
                        <v-text-field class="white-bk" v-model="form.Currentduty" disabled dense/>
                    </v-col>
                    <!-- 已发生次数 -->
                    <v-col :cols="6" class="activ-style alarm-bdmr activ-txt" :lg="6">
                        {{ $t('$vuetify.dataTable.ANDON_BJJL.Total') }}
                    </v-col>
                    <!-- 持续时间 -->
                    <v-col :cols="6" class="activ-style activ-txt" :lg="6">
                        {{ $t('$vuetify.dataTable.ANDON_BJJL.con') }}
                    </v-col>
                    <!-- 已发生次数 -->
                    <v-col :cols="6" :lg="6" class="activ-style alarm-bdmr activ-height activ-background">
                        <v-text-field class="white-bk" v-model="form.Total" disabled dense/>
                    </v-col>
                    <!-- 持续时间 -->
                    <v-col :cols="6" :lg="6" class="activ-style activ-height activ-background">
                        <v-text-field class="white-bk" v-model="form.con" disabled dense/>
                    </v-col>
                    <!-- 告警内容   -->
                    <v-col :cols="12" :lg="12" class="activ-style activ-txt">
                        {{ $t('$vuetify.dataTable.ANDON_BJJL.AlarmContent') }}
                    </v-col>
                    <!-- 告警内容 -->
                    <v-col :cols="12" :lg="12" class="activ-style activ-background" style="height: 80px">
                        <v-textarea disabled class="white-bk" v-model="form.AlarmContent" rows="2" dense/>
                    </v-col>
                    <!-- 关警内容   -->
                    <v-col :cols="12" :lg="12" class="activ-style activ-txt">
                        {{ $t('$vuetify.dataTable.ANDON_BJJL.Comment') }}
                    </v-col>
                    <!-- 关警内容 -->
                    <v-col :cols="7" :lg="7" class="activ-style opear-message alarm-bdmr activ-background">
                        <v-textarea class="white-bk" v-model="Comment" rows="4" dense outlined/>
                    </v-col>
                    <!-- 操作 -->
                    <v-col :cols="5" :lg="5" class="activ-style opear-message opear-btns activ-background">
                        <div class="white-bk" @click="closeForm">
                            <span class="iconfont icon-cuowu"></span>
                        </div>
                        <div class="agree-btn" @click="submitForm">
                            <span class="iconfont icon-zhengque"></span>
                        </div>
                    </v-col>
                </v-row>
                </v-form>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script>
import { AlarmRecordClose } from '@/api/andonManagement/alarmHome.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            valid: true,
            dialog: false,
            form: {
                MainAlarm: '',
                ProductLineName: '',
                SubAlarm: '',
                AlarmContent: '',
                UnitName: '',
                AreaName: '',
                Currentduty: '',
                EventLevel: '',
                Total: '',
                con: ''
            },
            Comment: '',
            rules: {
                // ManQuantity: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            }
        };
    },
    watch: {
        dialog: {
            async handler(curVal) {
                if (curVal) {
                    for (const key in this.form) {
                        if (Object.hasOwnProperty.call(this.form, key)) {
                            this.form[key] = this.operaObj[key];
                        }
                    }
                }
            },
            deep: true,
            immediate: true
        }
    },
    async created(){},
    methods: {
        //
        closeForm() {
            this.$refs.form.resetValidation()
            this.$refs.form.reset() 
            this.dialog = false;
        },
        // 提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                const res = await AlarmRecordClose({id: this.operaObj.ID, Comment: this.Comment});
                const { success, msg } = res;
                if(success){
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    this.$emit('handlePopup', 'refresh');
                    this.$refs.form.reset()
                    this.closeForm()
                }else{
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'error' });
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.activ-txt{
    line-height: 6px;
}
.activ-style{
    border: 1px solid #bdbdbd;
    border-bottom: none;
}
.activ-height{
    height: 60px;
}
.alarm-message{
    height: 80px;
}
.opear-message{
    height: 130px;
    border-bottom: 1px solid #bdbdbd;
}
.opear-btns{
    display: flex;
    justify-content: space-around;
    align-items: center;
    div{
        cursor: pointer;
        border: 1px solid gainsboro;
        height: 100%;
        width: 40%;
        display: flex;
        justify-content: space-around;
        align-items: center;
    }
    .agree-btn{
        background: #f2c85d;
    }
}
.white-bk{
    background: #fff;
}
.alarm-bdmr{
    border-right: none;
}
// .activ-style:last-child{
//     border-bottom: 1px solid;
// }
.col-lg-6.col-12,
.col-lg-6.col-6,
.col-6,
.col-12
.col-lg-6,
.col-lg-12 {
    padding: 6x;
}
</style>
<style lang="scss">
.alarm-home-pdt{
    .v-text-field.v-text-field--enclosed .v-text-field__details{
        margin-bottom: 0;
    }
    .v-text-field__details{
        display: none;
    }
    .activ-background{
        background: #f5f5f5
    }
    .iconfont{
        font-size: 80px;
    }
    .v-input--dense > .v-input__control > .v-input__slot{
        margin-bottom: 1px;
    }
    legend{
        display: none;
    }
    .v-text-field--outlined fieldset{
        top: -1px;
    }
}
</style>
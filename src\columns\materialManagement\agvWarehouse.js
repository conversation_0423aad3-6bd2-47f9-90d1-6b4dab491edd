// , semicolonFormat: true
export const agvWarehouseColumn = [
    // {
    //     label: '序号',
    //     prop: 'Index',
    //     width: '60px'
    // },
    { label: '楼层', prop: 'Floor', width: '160px' },
    // { label: '类型', prop: 'WarehouseType', width: '100px' },
    { label: '仓库编号', prop: 'WarehouseCode', width: '230px' },
    { label: '仓库名称', prop: 'WarehouseName', width: '200px' },
    { label: '库区编号', prop: 'BelongAreaCode', width: '160px' },
    { label: '库区名称', prop: 'BelongAreaName', width: '160px' },
    { label: '库位编号', prop: 'PositionCode', width: '160px' },
    { label: '库位名称', prop: 'PositionName', width: '160px' },
    { label: '属性', prop: 'WarehouseLevel', width: '140px' },
    { label: '描述', prop: 'Description', width: '200px' },
    { label: '', prop: 'noctions', width: '0px' },
]

export const agvWarehouseColumnList = [
    { text: '序号', value: 'Index', width: 60 },
    { text: '仓库编号', value: 'WarehouseCode', width: 230 },
    { text: '仓库名称', value: 'WarehouseName', width: 180 },
    { text: '库区编号', value: 'BelongAreaCode', width: 249 },
    { text: '库区名称', value: 'BelongAreaName', width: 190 },
    { text: '库区类型', value: 'MaterialAreaTypeName', width: 140 },
    // { text: '库位编号', value: 'PositionCode', width: 249 },
    // { text: '库位名称', value: 'PositionName', width: 220 },
    // { text: '库位类型', value: 'PositionType', width: 140 },
    // { text: '属性', value: 'WarehouseLevel', width: 140 },
    // { text: '描述', value: 'Description', width: 200 },
    { text: '', value: 'noActions', width: 0 }
]
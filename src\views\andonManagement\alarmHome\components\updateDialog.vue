<template>
    <!-- 新增报警 -->
    <v-dialog v-model="dialog" persistent="persistent" max-width="720px">
        <v-card class="alarm-home-upd">
            <v-card-title
                class="d-flex text-h6 justify-space-between primary lighten-2"
                primary-title="primary-title">
                {{ $t('ANDON_BJZY.xzbj') }}--{{ operaObj.AlarmName }}
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" class="mt-8 mb-2">
                    <v-row>
                        <!-- 二级分类 -->
                        <v-col :cols="6" class="activ-style activ-txt alarm-bdmr" :lg="6">{{ $t('ANDON_BJZY.SubAlarmType') }}</v-col>
                        <!-- 问题等级 -->
                        <v-col :cols="6" class="activ-style activ-txt" :lg="6">{{ $t('ANDON_BJZY.ProblemLevel') }}</v-col>
                        <!-- 二级分类 -->
                        <v-col
                            :cols="6"
                            class="activ-style activ-height activ-background alarm-bdmr"
                            :lg="6">
                            <v-select
                                v-model="form.SubAlarmType"
                                class="white-bk"
                                :rules="rules.SubAlarmType"
                                @change="changeS"
                                :items="typeChildList"
                                item-text="AlarmName"
                                item-value="AlarmCode"
                                dense="dense"
                                outlined="outlined"
                                single-line="single-line"></v-select>
                        </v-col>
                        <!-- 问题等级 -->
                        <v-col :cols="6" class="activ-style activ-height activ-background" :lg="6">
                            <v-select
                                v-model="form.ProblemLevel"
                                disabled="disabled"
                                class="white-bk"
                                :items="problemLevelList"
                                item-text="ItemName"
                                item-value="ItemValue"
                                dense="dense"
                                outlined="outlined"
                                single-line="single-line"></v-select>
                        </v-col>
                        <!-- 20240418, wzh, add the deal type -->
                        <!-- 处置类型 -->
                        <v-col :cols="6" class="activ-style activ-txt" :lg="6">{{ $t('ANDON_BJZY.DealTypeName') }}</v-col>
                        <!-- 产线 -->
                        <v-col :cols="6" class="activ-style activ-txt" :lg="6">{{ $t('ANDON_BJZY.areaid') }}</v-col>
                        <!-- 处置类型 -->
                        <v-col :cols="6" class="activ-style activ-height activ-background" :lg="6">
                            <v-select
                                v-model="form.DealType"
                                disabled="disabled"
                                class="white-bk"
                                :items="dealTypeList"
                                item-text="ItemName"
                                item-value="ItemValue"
                                dense="dense"
                                outlined="outlined"
                                single-line="single-line"></v-select>
                        </v-col>
                        <!-- 产线 -->
                        <v-col
                            :cols="6"
                            class="activ-style activ-height activ-background alarm-bdmr"
                            :lg="6">
                            <!-- 20240325, wzh, change area code to name -->
                            <v-combobox
                                v-model="form.AreaCode"
                                class="white-bk"
                                @change="changeA"
                                :search-input.sync="search1"
                                :items="AreaList"
                                item-text="EquipmentName"
                                item-value="ID"
                                dense="dense"
                                outlined="outlined"
                                clearable="clearable"></v-combobox>
                        </v-col>
                        <!-- 工段 -->
                        <v-col :cols="6" class="activ-style activ-txt alarm-bdmr" :lg="6">{{ $t('ANDON_BJZY.ProductLine') }}</v-col>
                        <!-- 设备 -->
                        <v-col :cols="6" class="activ-style activ-txt" :lg="6">{{ $t('ANDON_BJZY.EquipmentCode') }}</v-col>
                        <!-- 工段 -->
                        <v-col
                            :cols="6"
                            class="activ-style activ-height alarm-bdmr activ-background"
                            :lg="6">
                            <v-combobox
                                v-model="form.ProductLine"
                                :items="EquipmentList"
                                class="white-bk"
                                :search-input.sync="search"
                                item-text="EquipmentName"
                                item-value="ID"
                                @change="changeV"
                                persistent-hint="persistent-hint"
                                return-object="return-object"
                                clearable="clearable"
                                dense="dense"
                                outlined="outlined">
                                <template #no-data>
                                    <v-list-item>
                                        <v-list-item-content>no data</v-list-item-content>
                                    </v-list-item>
                                </template>
                            </v-combobox>
                        </v-col>
                        <!-- 设备 -->
                        <v-col :cols="6" :lg="6" class="activ-style activ-height activ-background">
                            <!-- <v-select v-model="form.EquipmentCode" :rules="rules.EquipmentCode"
                            :items="equipmentList" item-text="EquipmentName" item-value="ID" dense
                            single-line outlined></v-select> -->
                            <Treeselect
                                noChildrenText="no data"
                                noOptionsText="no data"
                                v-model="form.EquipmentCode"
                                :rules="rules.EquipmentCode"
                                :normalizer="normalizer"
                                :options="returnedItem"/>
                        </v-col>
                        <!-- 通知人员 -->
                        <v-col :cols="12" class="activ-style activ-txt alarm-bdmr" :lg="6">{{ $t('ANDON_BJZY.PostUser') }}</v-col>
                        <v-col
                            :cols="12"
                            :lg="12"
                            class="activ-style activ-height alarm-bdmr activ-background">
                            <v-combobox
                                v-model="form.Currentman"
                                class="white-bk"
                                :search-input.sync="search2"
                                :items="UserList"
                                item-text="UserName"
                                item-value="LoginName"
                                @change="changeUser"
                                dense="dense"
                                outlined="outlined"
                                clearable="clearable"></v-combobox>
                        </v-col>
                        <!-- 告警照片 -->

                        <v-col :cols="12" class="activ-style activ-txt alarm-bdmr" :lg="6">
                            <v-btn color='primary' @click="showPreviewDialog">{{ $t('ANDON_BJZY.AlarmPic') }}</v-btn>
                        </v-col>

                        <!-- 内容 -->
                        <v-col :cols="12" :lg="12" class="activ-style activ-txt">{{ $t('ANDON_BJZY.AlarmContent') }}</v-col>
                        <!-- 告警内容 -->
                        <v-col
                            :cols="6"
                            :lg="6"
                            class="activ-style opear-message alarm-bdmr activ-background">
                            <v-textarea
                                v-model="form.AlarmContent"
                                :rules="rules.AlarmContent"
                                class="white-bk"
                                rows="4"
                                dense="dense"
                                outlined="outlined"
                                single-line="single-line"/>
                        </v-col>
                        <!-- 操作 -->
                        <v-col
                            :cols="6"
                            :lg="6"
                            class="activ-style opear-message opear-btns activ-background">
                            <div class="white-bk" @click="closeForm">
                                <span class="iconfont icon-cuowu"></span>
                            </div>
                            <div class="agree-btn" @click="submitForm">
                                <span class="iconfont icon-zhengque"></span>
                            </div>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
        </v-card>
        <!-- 文件预览对话框 -->
        <v-dialog v-model="dialogVisible" max-width="60%">
            <v-card>
                <v-toolbar color="primary" dark="dark">
                    <v-toolbar-title>文件预览</v-toolbar-title>
                    <v-spacer></v-spacer>
                    <v-btn icon="icon" @click="dialogVisible = false">
                        <v-icon>mdi-close</v-icon>
                    </v-btn>
                </v-toolbar>
                <v-card-text>
                    <v-row>
                        <v-col cols="12">
                            <v-btn color='primary' style="margin-top: 20px;" @click="triggerFileInput">{{ $t('ANDON_BJZY.AlarmPic') }}</v-btn>
                            <input
                                ref="fileInput"
                                type="file"
                                accept="image/*"
                                multiple="multiple"
                                style="display: none"
                                @change="handleFileChange"/>
                        </v-col>
                    </v-row>
                    <v-row :gutter="10">
                        <v-col
                            v-for="(file, index) in previewFiles"
                            :key="index"
                            cols="12"
                            sm="6"
                            md="4">
                            <v-card @click="showFullImagePreview(file.fileUrl)">
                                <v-img :src="file.fileUrl" aspect-ratio="1.75"></v-img>
                                <v-card-actions>
                                    <v-btn color="red" @click="removeFile(index)">删除</v-btn>
                                </v-card-actions>
                            </v-card>
                        </v-col>
                    </v-row>
                </v-card-text>
            </v-card>
        </v-dialog>
        <!-- 全屏图片预览对话框 -->
        <v-dialog
            v-model="fullImageDialogVisible"
            fullscreen="fullscreen"
            hide-overlay="hide-overlay"
            transition="dialog-bottom-transition">
            <v-card flat="flat">
                <v-toolbar color="primary" dark="dark">
                    <v-toolbar-title>全屏预览</v-toolbar-title>
                    <v-spacer></v-spacer>
                    <v-btn icon="icon" @click="fullImageDialogVisible = false">
                        <v-icon>mdi-close</v-icon>
                    </v-btn>
                </v-toolbar>
                <v-card-text>
                    <v-img :src="fullImageUrl" contain="contain" height="100%"/>
                </v-card-text>
            </v-card>
        </v-dialog>
    </v-dialog>
</template>

<script>
    import {AlarmRecordSaveForm, Upload} from '@/api/andonManagement/alarmHome.js';
    import {getRoleList, getAllUserList} from '../../../systemManagement/userManagement/service.js';
    import {GetListByAlarmId} from '@/api/andonManagement/alarmType.js';
    import {configUrl} from '@/config';
    export default {
        // components: {     UploadComponent: () => import('./updateDialog.vue'), },
        props: {
            operaObj: {
                type: Object,
                default: () => {}
            },
            problemLevelList: {
                type: Array,
                default: () => []
            },
            // 20240418, wzh, add the deal type
            dealTypeList: {
                type: Array,
                default: () => []
            },
            AreaList: {
                type: Array,
                default: () => []
            },
            productLineList: {
                type: Array,
                default: () => []
            },
            equipmentTree: {
                type: Array,
                default: () => []
            },
            postInfoList: {
                type: Array,
                default: () => []
            },
            departmentData: {
                type: Array,
                default: () => []
            }
        },
        data() {
            return {
                importLoading: false,
                search: null,
                search1: null,
                search2: null,
                EquipmentList: [],
                typeChildList: [],
                returnedItem: [],
                valid: true,
                dialog: false,
                postSelectInfo: [],
                baseURL: 'baseURL_ANDON',
                api: '/andon/AlarmRecord/Upload',
                uploadPic: [],
                form: {
                    AreaCode: '',
                    MainAlarmType: '',
                    SubAlarmType: '',
                    ProductLine: '',
                    AlarmContent: '',
                    EquipmentCode: null,
                    ProblemLevel: '',
                    DealType: '' // 20240418, wzh, add the deal type
                },
                rules: {
                    SubAlarmType: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                    AlarmContent: [v => !!v || this.$t('GLOBAL._MANDATORY')]
                },
                normalizer(node) {
                    const {value, name, children} = node;
                    return {id: value, label: name, children};
                },
                RoleList: [],
                UserList: [],
                previewFiles: [],
                fullImageUrl: '',
                fullImageDialogVisible: false,
                dialogVisible: false
            };
        },
        watch: {
            dialog: {
                handler(curVal) {
                    if (curVal) {
                        this.getTypeChildList(this.operaObj.ID);
                    }
                    this.form.EquipmentCode = null;
                },
                deep: true,
                immediate: true
            }
        },
        async created() {
            // await this.getRoleList()
            this.previewFiles = [];
            await this.getUserList();
        },
        methods: {
            changeS(v) {
                const obj = this
                    .typeChildList
                    .find(i => i.AlarmCode == v);
                if (obj) {
                    this.form.ProblemLevel = obj.ProblemLevel;
                    this.form.DealType = obj.DealType; // 20240418, wzh, add the deal type
                }
            },
            changeUser(v) {
                this.form.Currentman = v.UserName;
                this.form.EmpLark = v.LoginName;
            },
            OnFileChange(files) {
                this.form.AlarmPic = files;
            },
            changeV(v) {
                this.form.EquipmentCode = null;
                this.returnedItem = [];
                this.findEqu(
                    this.equipmentTree,
                    v
                        ?.ID
                );
            },
            changeA(v) {
                this.form.ProductLine = null;
                this.EquipmentList = [];
                this.form.EquipmentCode = null;
                this.returnedItem = [];
                this.EquipmentList = this
                    .productLineList
                    .filter(
                        i => i.ParentId == v
                            ?.ID
                    );
            },
            // 根据工段获取设备
            findEqu(arr, id) {
                //循环遍历
                arr.forEach(item => {
                    //判断递归结束条件
                    if (item.id == id) {
                        this
                            .returnedItem
                            .push(item);
                    }
                    if (item.children != null) {
                        //递归调用
                        this.findEqu(item.children, id);
                    }
                });
            },
            //
            closeForm() {
                this
                    .$refs
                    .form
                    .reset();
                this.dialog = false;
            },
            // 获取子级
            async getTypeChildList(alarmId) {
                this.typeChildList = [];
                const res = await GetListByAlarmId({alarmId});
                const {success, response} = res || {};
                if (success) {
                    this.typeChildList = response;
                }
            },
            triggerFileInput() {
                this
                    .$refs
                    .fileInput
                    .click();
            },
            showPreviewDialog() {
                this.dialogVisible = true;
            },
            async handleFileChange(event) {
                const files = event.target.files;
                for (let file of files) {
                    const formData = new FormData();
                    formData.append('file', file);
                    const response = await Upload(formData);
                    if (response.success) {
                        const data = response.response;
                        this
                            .previewFiles
                            .push(data);
                    }

                }
                this.updateUploadedFiles();
            },
            removeFile(index) {
                this
                    .previewFiles
                    .splice(index, 1);
                this.updateUploadedFiles();
            },
            updateUploadedFiles() {
                this.form.AlarmPic = this.previewFiles.map(file => file.fileName).join(',');
            },
            // 表单提交
            async submitForm() {
                if (this.$refs.form.validate()) {
                    const {ProductLine, AreaCode} = this.form;
                    const {EquipmentCode} = ProductLine || {};
                    let obj = {
                        ...this.form
                    };
                    if (EquipmentCode) {
                        obj = {
                            ...obj,
                            ProductLine: EquipmentCode
                        };
                    }
                    if (
                        AreaCode
                            ?.EquipmentCode
                    ) {
                        obj = {
                            ...obj,
                            ProductLine: EquipmentCode,
                            AreaCode: AreaCode.EquipmentCode
                        };
                    }
                    const res = await AlarmRecordSaveForm({
                        ...obj,
                        MainAlarmType: this.operaObj.AlarmCode
                    });
                    const {success, msg} = res;
                    if (success) {
                        this
                            .$store
                            .commit('SHOW_SNACKBAR', {
                                text: msg,
                                color: 'success'
                            });
                        this
                            .$refs
                            .form
                            .reset();
                        this.$emit('handlePopup', 'refresh');
                        this.closeForm();
                    } else {
                        this
                            .$store
                            .commit('SHOW_SNACKBAR', {
                                text: msg,
                                color: 'error'
                            });
                    }
                }
            },
            async getRoleList() {
                const res = await getRoleList({});
                const {success, response} = res || {};
                if (response && success) 
                    this.RoleList = response;
                else 
                    this.RoleList = [];
                }
            ,
            showFullImagePreview(file) {
                this.fullImageUrl = file;
                this.fullImageDialogVisible = true;
            },
            async getUserList() {
                const res = await getAllUserList({});
                const {success, response} = res || {};
                if (response && success) 
                    this.UserList = response;
                else 
                    this.UserList = [];
                }
            }
    };
</script>

<style lang="scss" scoped="scoped">
    .activ-txt {
        line-height: 6px;
    }

    .activ-style {
        border: 1px solid #bdbdbd;
        border-bottom: none;
    }

    .activ-height {
        height: 60px;
    }

    .alarm-message {
        height: 80px;
    }

    .opear-message {
        height: 130px;
        border-bottom: 1px solid #bdbdbd;
    }

    .opear-btns {
        display: flex;
        justify-content: space-around;
        align-items: center;

        div {
            cursor: pointer;
            border: 1px solid gainsboro;
            height: 100%;
            width: 40%;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .agree-btn {
            background: #f2c85d;
        }
    }

    .white-bk {
        background: #fff;
    }

    .alarm-bdmr {
        border-right: none;
    }

    // .activ-style:last-child{
    //     border-bottom: 1px solid;
    // }
    .col-12 .col-lg-6,
    .col-6,
    .col-lg-12,
    .col-lg-6.col-12,
    .col-lg-6.col-6 {
        padding: 6x;
    }

    .v-card--fullscreen .v-toolbar {
        background-color: rgba(0, 0, 0, 0.5);
        /* 半透明背景 */
    }

    .v-card--fullscreen .v-toolbar__title {
        color: white;
        /* 文字颜色 */
    }

    .v-card--fullscreen .v-card__text {
        padding: 0;
        /* 移除内边距 */
    }

    .v-card--fullscreen .v-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
</style>
<style lang="scss">
    .alarm-home-upd {
        .v-text-field.v-text-field--enclosed .v-text-field__details {
            margin-bottom: 0;
        }

        .v-text-field__details {
            display: none;
        }

        .activ-background {
            background: #f5f5f5;
        }

        .iconfont {
            font-size: 80px;
        }

        legend {
            display: none;
        }

        .v-text-field--outlined fieldset {
            top: -1px;
        }
    }
</style>
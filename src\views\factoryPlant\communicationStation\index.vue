<template>
  <div class="usemystyle MaterialPreparation">
    <div class="InventorySearchBox">
      <div class="searchbox pd-left">
        <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
          <el-form-item :label="$t('GLOBAL._SSL')">
            <el-input clearable v-model="searchForm.Search" placeholder="快速搜索"></el-input>
          </el-form-item>
          <el-form-item :label="$t('CommunicationStation.title.type')">
            <el-select v-model="searchForm.Type" placeholder="请选择采集类型" clearable>
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('CommunicationStation.title.code')">
            <el-input clearable v-model="searchForm.Code" placeholder="站点编码搜索"></el-input>
          </el-form-item>
          <el-form-item :label="$t('CommunicationStation.title.name')">
            <el-input clearable v-model="searchForm.Name" placeholder="站点名称搜索"></el-input>
          </el-form-item>
          <el-form-item :label="$t('CommunicationStation.title.url')">
            <el-input clearable v-model="searchForm.Url" placeholder="URL搜索"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" @click="getSearchBtn">{{ $t('GLOBAL._CX') }}</el-button>
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="success" icon="el-icon-circle-plus-outline" @click="showDialog({})">{{
              $t('GLOBAL._XZ') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="tablebox height">
      <el-table v-loading="loading" :data="tableData" element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading" style="width: 100%" height="100%">
        <el-table-column v-for="(item, index) in tableName" :key="index" :prop="item.field" :label="item.label">
          <template slot-scope="scope">
            <span v-if="item.field === 'Type'">{{ scope.row[item.field] ? 'OPCUA' : 'OPCDA' }}</span>
            <span v-else>{{ scope.row[item.field] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="operation" width="160" :label="$t('GLOBAL._ACTIONS')" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="showDialog(scope.row)">{{ $t('GLOBAL._BJ') }}</el-button>
            <el-button size="mini" type="text" @click="delRow(scope.row)">{{ $t('GLOBAL._SC') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination class="mt-8p" background :current-page="searchForm.pageIndex" :page-size="searchForm.pageSize"
      layout="->, total, prev, pager, next" :total="total" @current-change="handleCurrentChange" />
    <FormDialog ref="dialog" @SaveForm="getSearchBtn" />
  </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';
import FormDialog from './components/form-dialog'
import { getInfluxOpcServerPageList, influxOpcServerDelete } from "@/api/factoryPlant/communicationStation.js";
export default {
  name: 'CommunicationStation',
  components: {
    FormDialog
  },
  data() {
    return {
      searchForm: {
        pageIndex: 1,
        pageSize: 10,
      },
      total: 0,
      tableData: [],
      hansObj: this.$t('CommunicationStation.Station_Table'),
      tableName: [],
      loading: false,
      typeOptions: [{
        label: 'OPCDA',
        value: 0
      }
      // , {
      //   label: 'OPCUA',
      //   value: 1
      // }
    ]
    }
  },
  mounted() {
    this.getZHHans()
    this.getTableData()
  },
  methods: {
    getZHHans() {
      for (let key in this.hansObj) {
        this.tableName.push({ field: key, label: this.hansObj[key] })
      }
    },
    showDialog(row) {
      this.$refs.dialog.show(row)
    },
    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },
    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },
    delRow(row) {
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then( async () => {
        const { msg } = await influxOpcServerDelete([row.ID])
        this.$message.success(msg)
        this.getTableData()
      }).catch(err => {
        console.log(err);
      });
    },
    async getTableData() {
      this.loading = true
      const { response } = await getInfluxOpcServerPageList(this.searchForm)
      this.loading = false
      this.tableData = response.data
      this.total = response.dataCount
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}

.mt-8p {
  margin-top: 8px;
}

.height {
  height: 83vh;
}

.pd-left {
  padding-left: 5px
}
</style>

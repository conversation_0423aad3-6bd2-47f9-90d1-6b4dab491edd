<template>
  <div class="left-top">
    <div class="chart-box">
      <Calendar
        style="width:40%;height:100%"
        :days="days"
        :curTeamTreeObj="curTeamTreeObj"
        :searchFormObj="searchFormObj"
        :backgroundImg="backgroundImg"
        :fullscreen="fullscreen"
        :bcode="bcode"
        @change="requestFn"
      >
      </Calendar>
      <div
        id="funnel"
        style="width:60%;height:100%"
      ></div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import myImg from "@/assets/imgs/happy.png"
import Calendar from "./calendar/index.vue"
import { GetMonthStatistics, GetSafeTypeStatistics } from '@/views/simManagement/sim1/service.js';
import { mapGetters } from 'vuex';

export default {
  components: {
    Calendar
  },
  props: {
    curTeamTreeObj: {
      type: Object,
      default: () => { }
    },
    searchFormObj: {
      type: Object,
      default: () => { }
    },
    simlevel: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    Order: {
      type: String,
      default: ''
    },
    backgroundImg: {
      type: String,
      default: ''
    },
    fullscreen: {
      type: Boolean,
      default: false
    },
    bcode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [{ name: '损工事件', color: '#ff0000' },
      { name: '医疗事件数', color: '#fe9800' },
      { name: '急救事件数', color: '#f9d307' },
      { name: '未遂事件数', color: '#85ded0' },
      { name: '安全生产天数', color: '#a9d7a9' }
      ],
      days: [],
      safeList: [],
      myCharts1: null,
      myCharts2: null,
    }
  },
  computed: {
    // mySearchFormObj(){
    //     return this.props.searchFormObj
    // }
    ...mapGetters(['getUserinfolist']),
    curAuth() {
      return this.$store.getters.getUserinfolist[0]
    },
  },
  watch: {
    'searchFormObj': {
      handler(nv, ov) {
        this.GetMonthStatisticsFn()
        this.GetSafeTypeStatisticsFn()
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    console.log(this.Order, 'OrderOrderOrder');

  },
  mounted() {

  },
  activated() {
    this.myCharts2.resize()
  },
  methods: {
    //日历图
    initCalendar() {
      function getVirtualData(year) {
        const date = new Date(year + '-01-01').getTime();
        const end = new Date(year * 1 + 1 + '-01-01').getTime();
        const dayTime = 3600 * 24 * 1000;
        const data = [];
        for (let time = date; time < end; time += dayTime) {
          data.push([
            dayjs(time).format('YYYY-MM-DD'),
            Math.round(Math.random())
          ]);
        }
        return data;
      }
      const myData = getVirtualData('2023')

      this.myCharts1 = this.$echarts.init(document.getElementById('calendar'));
      let option = {
        tooltip: {
          position: 'top'
        },
        visualMap: [
          {
            min: 0,
            max: 1,
            calculable: true,
            orient: 'horizontal',
            left: '5%',
            bottom: 20,
            show: false,
            inRange: {
              color: ['transparent', '#cfd8dc',],
              // color: ['red', 'green'],'#4391f4'
              // opacity: 0.3
            }
          },

        ],
        calendar: [
          {
            orient: 'vertical',
            yearLabel: {
              show: false,
              margin: 40,
            },
            monthLabel: {
              show: false,
              nameMap: 'cn',
              margin: 20,
            },
            dayLabel: {
              show: false,
              firstDay: 1,
              nameMap: 'cn',
            },
            left: 5,
            top: 0,
            bottom: 0,
            right: 5,
            // cellSize: 'auto',
            // cellSize: [30, 30],
            itemStyle: {
              borderWidth: 1,
              borderColor: '#fff'
            },
            splitLine: {
              show: false
            },
            range: '2023-10'
          }
        ],
        series: [
          {
            type: 'scatter',
            coordinateSystem: 'calendar',
            data: myData,
            symbol: (params) => {
              if (params[1] > 0) {
                return "none"
              } else {
                return 'image://' + myImg
              }
            },
            symbolSize: 20
          },
          {
            type: 'heatmap',
            coordinateSystem: 'calendar',
            data: myData,
            label: {
              show: true,
              formatter: (params) => {
                if (params.value[1] > 0) {
                  return params.value[0].split('-')[2]
                } else {
                  return ''
                }
              }
            }
          }
        ]
      };
      this.myCharts1.setOption(option)
    },
    //梯形图
    initFunnel() {
      this.myCharts2 = this.$echarts.init(document.getElementById('funnel'));
      let option = {
        // title: {
        //     text: 'Funnel'
        // },
        tooltip: {
          show: false,
          trigger: 'item',
          formatter: '{a} <br/>{b} : @myValue'
        },
        // grid:{
        //     bottom: 50
        // },
        // toolbox: {
        //     feature: {
        //         dataView: { readOnly: false },
        //         restore: {},
        //         saveAsImage: {}
        //     }
        // },
        // legend: {
        //     data: ['Show', 'Click', 'Visit', 'Inquiry', 'Order']
        // },
        series: [
          {
            name: 'Funnel',
            type: 'funnel',
            funnelAlign: 'right',
            left: '0%',
            top: 15,
            bottom: 20,
            width: '50%',
            // min: 0,
            // max: 100,
            minSize: '20%',
            maxSize: '100%',
            sort: 'ascending',
            gap: 0,
            label: {
              show: true,
              fontSize: 12,
              // formatter: '{a|{@myValue}}{b|{b}}',
              formatter: function (params) {
                return `{a|${params.data.myValue}} {b|${params.data.name}}`
              },
              rich: {
                a: {
                  color: '#fff',
                  lineHeight: 10,
                  width: 30,
                  align: 'right',
                  padding: [0, 0, 0, -50]
                },
                b: {
                  // width: 100,
                  color: '#fff',
                  // align: 'left',
                  // padding: [0, 100, 0, 0]
                }
              }
              // position: 'inside'
            },
            labelLine: {
              show: false,
              length: 15,
              lineStyle: {
                width: 1,
                type: 'solid'
              }
            },
            itemStyle: {
              color: '#4391f4',
              borderColor: '#4391f4'
            },
            emphasis: {
              label: {
                fontSize: 20
              }
            },
            data: [
              { value: 20, name: 'LTA损失工时事故', myValue: 0 },
              { value: 40, name: 'Inquiry', myValue: 0 },
              { value: 60, name: 'Order', myValue: 0 },
              { value: 80, name: 'Click', myValue: 0 },
              { value: 100, name: 'Sshoaw2', myValue: 0 },
              { value: 120, name: 'Show', myValue: 10 },
            ]
          }
        ]
      };
      option.series[0].data = this.safeList.map((item, index) => {
        return {
          value: 20 * index + 20,//假值，用来保持梯形形状
          name: item.AccidentName,   //右侧文字
          myValue: item.Count//真值，
        }
      })
      this.myCharts2.setOption(option)
      window.addEventListener('resize', () => {
        this.myCharts2.resize()
      })
    },
    //按天统计数据
    async GetMonthStatisticsFn() {
      if (!this.searchFormObj.PresentDepartmentId) {
        return
      }
      let params = {
        "simLevel": this.Order.split('-')[0],
        "date": this.BaseTime,
        // teamId: this.simlevel,
        // departmentId: this.getUserinfolist[0].Departmentid,
        position: this.Order,
        // departmentId: this.curTeamTreeObj.ProductionLineCode,
        departmentId: this.bcode
        // plantId: this.searchFormObj.PresentDepartmentId
      }
      // if (this.searchFormObj.simLevel === 'SIM1') {
      //     params.teamId = this.searchFormObj.PresentDepartmentId
      // } else if (this.searchFormObj.simLevel === 'SIM2') {
      //     params.departmentId = this.searchFormObj.PresentDepartmentId
      // } else if (this.searchFormObj.simLevel === 'SIM5') {
      //     params.plantId = this.searchFormObj.PresentDepartmentId
      // }
      let res = await GetMonthStatistics(params)
      if (!res || !res.response) return
      let arr = JSON.parse(res.response)
      if (!arr.length) return
      this.days = arr
    },
    //安全类型列表
    // async getSafeList(){
    //     let res = await this.$getDataDictionary('safeType');
    //     this.safeList = res
    // },
    //安全类型统计数据
    async GetSafeTypeStatisticsFn() {
      if (!this.searchFormObj.PresentDepartmentId) {
        return
      }
      let list = [
        {
          "AccidentName": "LTA损失工时事故",
          "AccidentTypeCode": "1",
          "Count": 0
        },
        {
          "AccidentName": "MT医疗事故",
          "AccidentTypeCode": "2",
          "Count": 0
        },
        {
          "AccidentName": "FA急救事故事故",
          "AccidentTypeCode": "3",
          "Count": 0
        },
        {
          "AccidentName": "NearMiss险兆事故",
          "AccidentTypeCode": "4",
          "Count": 0
        },
        {
          "AccidentName": "SO安全隐患事故",
          "AccidentTypeCode": "5",
          "Count": 0
        },
        {
          "AccidentName": "无事故",
          "AccidentTypeCode": "6",
          "Count": 0
        }
      ]
      let params = {
        "simLevel": this.Order.split('-')[0],
        "date": this.searchFormObj.date,
        departmentId: this.bcode
      }
      // if (this.searchFormObj.simLevel === 'SIM1') {
      //   params.departmentId = this.curTeamTreeObj.ProductionLineCode
      // } else if (this.searchFormObj.simLevel === 'SIM2') {
      //   params.departmentId = this.curTeamTreeObj.ProductionLineCode
      // } else if (this.searchFormObj.simLevel === 'SIM5') {
      //   params.plantId = this.curTeamTreeObj.FactoryCode
      // }
      let res = await GetSafeTypeStatistics(params).catch((err) => {
      })
      if (res && res.response && res.response != '[]') {
        list = JSON.parse(res.response)
      }
      this.safeList = list
      this.initFunnel()
    },
    requestFn() {
      this.GetMonthStatisticsFn()
      this.GetSafeTypeStatisticsFn()
    }
  }
}
</script>

<style lang="less" scoped>
.left-top {
    width: 100%;
    height: 100%;
    // height: 30%;

    .lenged {
        display: flex;
        flex-wrap: wrap;
        padding: 0 5px;

        .item {
            width: 33%;
            font-size: 14px;

            span {
                margin-right: 5px;
            }
        }
    }

    .chart-box {
        display: flex;
        width: 100%;
        height: 85%;
    }
}
</style>
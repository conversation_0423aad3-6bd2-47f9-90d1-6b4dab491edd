import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Inventory'

//table数据
export function CheckWeightView(data) {
    const api = '/api/CheckWeightView/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function ResultListDown(data) {
    const api = '/api/CheckWeightView/ResultListDown'
    return getRequestResources(baseURL, api, 'post', data);
}
export function ConfirmCheck(data) {
    const api = '/api/CheckWeightView/ConfirmCheck'
    return getRequestResources(baseURL, api, 'post', data);
}

// //room下拉数据
// export function CheckWeightRoomSelectList(data) {
//     const api = '/api/MaterialPreparationView/GetRoomSelectList'
//     return getRequestResources(baseURL, api, 'post', data);
// }

export function CheckWeightRoomSelectList(data) {
    const api = '/api/MaterialPreparationView/GetRoomSelectListByPG'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetInventData(data) {
    const api = '/api/CheckWeightView/GetCheckWeight'
    return getRequestResources(baseURL, api, 'post', data);
}
//扫码
export function SugarPrePut(data) {
    const api = '/api/MaterialInventory/SugarPrePut'
    return getRequestResources(baseURL, api, 'post', data);
}
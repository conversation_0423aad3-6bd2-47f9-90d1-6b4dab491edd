export const configurationColum = [
    {
        text: '序号',
        value: 'Index',
        width: '80px'
    },
    { text: '项目名称', value: 'ProjectName', width: '160px' },
    { text: '解析项编码', value: 'ParseItemCode', width: '200px' },
    { text: '解析项名称', value: 'ParseItemName', width: '200px' },
    { text: '开始位', value: 'StartNumber', width: '200px' },
    { text: '结束位', value: 'EndNumber', width: '160px' },
    { text: '解析项默认值', value: 'DefaultValue', width: '160px' },
    // { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    // { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '详情',
        width: '120',
        align: 'center',
        value: 'actions'
    }
];
export const detailsColum = [
    {
        text: '序号',
        value: 'Index',
        width: '80px'
    },
    { text: '项目名称', value: 'ProjectName', width: '160px' },
    { text: '解析项编码', value: 'ParseItemCode', width: '160px' },
    { text: '截取值', value: 'CutValue', width: '120px' },
    { text: '对应值', value: 'MappingValue', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '',
        width: '0',
        align: 'center',
        value: 'noActions'
    }
];
<!--甘特图时间头的一块块-->
<template>
    <div class="gantt-time-block">
        <div class="top">{{ targetTime }}</div>
        <div class="bottom">
            <!-- 24小时 -->
            <div class="day-item" v-for="index in 24" :key="index">
                <div class="text">{{ index === 1 ? '' : index - 1 }}</div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'GanttTimeBlock',
    props: {
        targetTime: {
            type: String,
            default: ''
        }
    }
};
</script>

<style scoped lang="scss">
@import '../../common/css/global-property';
.gantt-time-block {
    box-sizing: border-box;
    //border: 1px solid #999999;
    font-size: 12px;
    .top {
        box-sizing: border-box;
        border-bottom: 1px solid $tableBorderColor;
        border-right: 1px solid $tableBorderColor;
        text-align: center;
        // 60 ÷ 2 = 30
        height: $tableHeadHeight/2;
        line-height: $tableHeadHeight/2;
    }
    .bottom {
        display: flex;
        .day-item {
            //180 ÷ 3 = 60
            line-height: $tableHeadHeight/2;
            //为了保持1px 等于1分钟 所以取值60px
            width: 60px;
            height: $tableHeadHeight/2;
            .text {
                margin-left: -6px;
            }
            &:last-child {
                box-sizing: border-box;
                border-right: 1px solid $tableBorderColor;
            }
        }
    }
}
</style>

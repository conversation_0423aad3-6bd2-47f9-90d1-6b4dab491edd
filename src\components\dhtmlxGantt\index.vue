<template>
    <div ref="ganttContainer" style="height: calc(100vh - 170px)"></div>
</template>

<script>
import { gantt } from 'dhtmlx-gantt';
export default {
    props: {
        tasks: {
            type: Object,
            default() {
                return { data: [], links: [] };
            }
        }
    },
    dtaa() {
        return {};
    },
    created() { },
    mounted() {
        gantt.config.columns = [
            { name: 'SapWoCode', label: this.$t('DFM_SCGDGTT._SAPDDH'), align: 'center', min_width: 180, tree: true },
            { name: 'WoCode', label: this.$t('DFM_SCGDGTT._GDH'), align: 'center', min_width: 120 },
            { name: 'ProductionCode', label: this.$t('DFM_SCGDGTT.ProductionCode'), align: 'center', min_width: 100 },
            { name: 'ProductionLineCode', label: this.$t('DFM_SCGDGTT.ProductionLineCode'), align: 'center', min_width: 100 },
            { name: 'Shift', label: this.$t('DFM_SCGDGTT._BC'), align: 'center', min_width: 50 },
            { name: 'start_date', label: this.$t('DFM_SCGDGTT.PlanDate'), align: 'center', min_width: 120 }
            // { name: "duration", label: "Duration", align: "center" },
        ];
        gantt.config.date_format = '%Y-%m-%d %H:%i';
        gantt.config.min_column_width = 20;
        gantt.config.duration_unit = 'minute';
        gantt.config.duration_step = 60;
        gantt.config.scale_height = 50;

        gantt.config.scales = [
            { unit: 'hour', step: 1, format: '%G' },
            { unit: 'day', step: 1, format: '%j %F, %l' }
            // { unit: "minute", step: 15, format: "%i" }
        ];
        gantt.plugins({
            tooltip: true
        });
        gantt.templates.task_text = function (start, end, task) {
            return '已完成：' + (task.progress * 100).toFixed(0) + '%';
        };

        gantt.templates.tooltip_text = function (start, end, task) {
            let htmlStr = `<b>计划数量：${task.PlanQty || 0}</b><br/><b>已完成数量：${task.QuantityQty || 0}</b>`;
            return htmlStr;
        };
        gantt.attachEvent('onBeforeTaskDrag', function (id, mode, e) {
            var modes = gantt.config.drag_mode;
            switch (mode) {
                case modes.move:
                    break;
                case modes.resize:
                    break;
                case modes.progress:
                    break;
            }
        });
        // gantt.config.date_format = "%Y-%m-%d";
        gantt.config.autoscroll = true;
        gantt.config.details_on_dblclick = false;
        gantt.init(this.$refs.ganttContainer);
        gantt.parse(this.$props.tasks);
    },
    methods: {
        initGantt() {
            this.$nextTick(() => {
                gantt.clearAll();
                gantt.parse(this.$props.tasks);
            });
        }
    }
};
</script>
<style>
@import '~dhtmlx-gantt/codebase/dhtmlxgantt.css';

.gantt_task_line {
    background: #3dcd58;
    border: #3dcd58;
}

.gantt_task_progress {
    background: #3dcd58;
}
</style>
<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    ref="Tables"
                    :clickFun="clickFun"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_SBWXGD"
                    :headers="RepairColum"
                    :desserts="desserts"
                    @tableClick="tableClick"
                    @selectePages="selectePages"
                ></Tables>
            </v-card>
            <el-drawer size="80%" :title="rowtableItem.RepairWo" :wrapperClosable="false" :visible.sync="detailShow" direction="rtl">
                <v-card class="ma-1">
                    <v-tabs v-model="tab" background-color="transparent">
                        <v-tab @click="changeTab(0)" key="0">{{ $t('TPM_SBGL_SBWXGD._WXJL') }}</v-tab>
                        <v-tab @click="changeTab(1)" key="1">{{ $t('TPM_SBGL_SBWXGD._BJ') }}</v-tab>
                        <v-tab @click="changeTab(2)" key="2">{{ $t('TPM_SBGL_SBWXGD._FWCG') }}</v-tab>
                    </v-tabs>
                    <v-tabs-items v-model="tab">
                        <v-tab-item>
                            <wxjl ref="wxjl" :rowtableItem="rowtableItem"></wxjl>
                        </v-tab-item>
                        <v-tab-item><bj ref="bj" :rowtableItem="rowtableItem"></bj></v-tab-item>
                        <v-tab-item><fwcg ref="fwcg" :rowtableItem="rowtableItem"></fwcg></v-tab-item>
                    </v-tabs-items>
                </v-card>
            </el-drawer>
            <el-dialog :title="addTitle" :visible.sync="addModel" width="30%">
                <div class="addForm" v-if="clickType != 'pd'">
                    <v-text-field
                        v-model="user"
                        outlined
                        dense
                        :label="clickType != 'pd' ? $t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.Creator') : $t('$vuetify.dataTable.ANDON_BJJL.repair_man') + '*'"
                    ></v-text-field>
                </div>
                <div class="addForm" v-if="clickType == 'pd'">
                    <v-autocomplete
                        clearable
                        v-model="user"
                        :items="gprList"
                        item-text="ItemName"
                        item-value="ItemValue"
                        :label="$t('$vuetify.dataTable.ANDON_BJJL.repair_man') + '*'"
                        clear
                        dense
                        outlined
                    ></v-autocomplete>
                </div>
                <div class="addForm" v-if="clickType != 'pd'">
                    <v-text-field v-model="userDate" :clearable="true" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.CreateDate') + '*'" readonly></v-text-field>
                    <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="userDate" type="datetime" :placeholder="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.CreateDate')"></el-date-picker>
                </div>
                <div class="addForm" v-if="clickType != 'pd'">
                    <v-text-field v-model="reason" outlined dense :label="$t('GLOBAL._REASON') + '*'"></v-text-field>
                </div>

                <div class="addForm" v-if="clickType == 'pd'">
                    <v-text-field v-model="startDate" :clearable="true" outlined dense :label="$t('DFM_GDGL.PlanStartTime') + '*'" readonly></v-text-field>
                    <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="startDate" type="datetime"></el-date-picker>
                </div>
                <div class="addForm" v-if="clickType == 'pd'">
                    <v-text-field v-model="endDate" :clearable="true" outlined dense :label="$t('DFM_GDGL.PlanEndTime')" readonly></v-text-field>
                    <el-date-picker
                        :picker-options="pickerOptionsEnd"
                        default-time="23:59:59"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        v-model="endDate"
                        type="datetime"
                        :placeholder="$t('GLOBAL.EndTime')"
                    ></el-date-picker>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="addModel = false">取 消</el-button>
                    <el-button type="primary" @click="Save()">确 定</el-button>
                </span>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';

import { RepairColum } from '@/columns/equipmentManagement/Repair.js';
import { GetRepairOrderPageList, GetRepairOrderAssign, GetRepairOrderCancel, GetRepairOrderStatus, GetRepairOrderType, GetRepairOrderSource } from '@/api/equipmentManagement/NewRepair.js';
import wxjl from './components/wxjl.vue';
import bj from './components/bj.vue';
import fwcg from './components/fwcg.vue';
import moment from 'moment';
import { Message, MessageBox } from 'element-ui';
import { GetExportData, GetPersonList } from '@/api/equipmentManagement/Equip.js';

export default {
    name: 'RepastModel',
    components: {
        wxjl,
        bj,
        fwcg
    },
    data() {
        return {
            detailShow: false,
            RepairColum,
            tab: 0,

            papamstree: {
                RepairWo:"",
                Source: '',
                Type: '',
                Status: '',
                ReportDateFrom: '',
                ReportDateTo: '',
                DeviceCode: '',
                DeviceName: '',
                RepairManager: this.$store.getters.getUserinfolist[0].LoginName,
                pageIndex: 1,
                pageSize: 20,
                orderByFileds: 'CreateDate desc'
            },
            gprList: [],
            StatusList: [],
            TypeList: [],
            SourceList: [],
            active: 0,
            loading: true,
            showFrom: false,
            rowtableItem: {},
            //查询条件
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            tableItem: {}, // 选择操作数据
            addTitle: '',
            reason: '',
            startDate: null,
            endDate: null,
            user: this.$store.getters.getUserinfolist[0].LoginName,
            userDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
            addModel: false,
            clickType: '',
            ShifMngData: [],
            MaintenanceGroupData: [],
            RepairManager: [],
            RequestStartDate: null,
            RequestFinishDate: null,
            pickerOptionsEnd: {
                disabledDate: time => {
                    if (this.startDate) {
                        return time.getTime() < new Date(this.startDate).getTime() - 24 * 3600000;
                    }
                }
            },
            pickerOptionsEnd2: {
                disabledDate: time => {
                    if (this.RequestStartDate) {
                        return time.getTime() < new Date(this.RequestStartDate).getTime() - 24 * 3600000;
                    }
                }
            }
        };
    },
    computed: {
        searchinputs() {
            return [
                    {
                    value: '',
                    key: 'RepairWo',
                    label: this.$t('TPM_SBGL_WDBX.GDH'),
                    icon: 'mdi-account-check',
                    type: 'input',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Source',
                    label: this.$t('TPM_SBGL_WDBX._GDLY'),
                    icon: 'mdi-account-check',
                    selectData: this.SourceList,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Status',
                    label: this.$t('TPM_SBGL_WDBX._ZT'),
                    icon: 'mdi-account-check',
                    selectData: this.StatusList,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Type',
                    label: this.$t('TPM_SBGL_WDBX._GDLX'),
                    icon: 'mdi-account-check',
                    selectData: this.TypeList,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'DeviceName',
                    label: this.$t('TPM_SBGL_WDBX._SBMC'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'DeviceCode',
                    label: this.$t('TPM_SBGL_WDBX._SBBH'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'ReportDateFrom',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_WDBX._BXKSRQ'),
                    placeholder: this.$t('TPM_SBGL_WDBX._BXKSRQ')
                },
                {
                    value: '',
                    key: 'ReportDateTo',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_WDBX._BXJSRQ'),
                    placeholder: this.$t('TPM_SBGL_WDBX._BXJSRQ')
                },
                {
                    value: this.$store.getters.getUserinfolist[0].LoginName,
                    key: 'RepairManager',
                    type: 'select',
                    selectData: this.RepairManager,
                    byValue: 'ItemValue',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBWXGD._ZPR'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBWXGD._ZPR')
                }
            ];
        },
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._PD'),
                    code: 'pd',
                    showList: ['待派单', '已派单', '进行中', '待备件', '已取消', '已驳回'],
                    showKey: 'Status',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBWXGD_PD'
                },
                {
                    text: this.$t('GLOBAL._QX'),
                    code: 'qx',
                    type: 'primary',
                    showList: ['待派单', '已派单', '进行中', '待备件'],
                    showKey: 'Status',
                    icon: '',
                    authCode: 'SBWXGD_QX'
                }
            ];
        }
    },
    async mounted() {
        let ShiftMng = await GetPersonList('ShiftMng');
        this.ShifMngData = ShiftMng.response[0].ChildNodes;
        this.ShifMngData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        let MyRepairManagerData = await GetPersonList('RepairMng');
        let RepairManagerData = MyRepairManagerData.response[0].ChildNodes;
        console.log(RepairManagerData, 123123);
        RepairManagerData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.RepairManager = RepairManagerData;
        let MaintenanceGroup = await GetPersonList('MaintenanceGroup');
        this.MaintenanceGroupData = MaintenanceGroup.response[0].ChildNodes;
        this.MaintenanceGroupData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.gprList = this.MaintenanceGroupData;
        this.SourceList = await this.$getNewDataDictionary('RepairSource');
        this.StatusList = await this.$getNewDataDictionary('RepairOrderStatus');
        this.TypeList = await this.$getNewDataDictionary('RepairOrderType');
        // this.MyGetRepairOrderStatus();
        // this.MyGetRepairOrderType();
        // this.MyGetRepairOrderSource();
        this.RepastInfoGetPage();
    },
    methods: {
        async Save() {
            let params = this.tableItem;
            let res;
            switch (this.clickType) {
                case 'qx':
                    if (this.user == '' || this.userDate == null || this.reason == '') {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    params.CancelBy = this.user;
                    params.CancelDate = this.userDate;
                    params.CancelReason = this.reason;
                    res = await GetRepairOrderCancel(params);
                    break;
                case 'pd':
                    if (this.user == '' || this.startDate == '') {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    params.ReceiveBy = this.user;
                    // params.RequestStartDate = this.RequestStartDate;
                    // params.RequestFinishDate = this.RequestFinishDate;
                    params.StartDate = this.startDate;
                    params.FinishDate = this.endDate;
                    res = await GetRepairOrderAssign(params);
                    break;
            }
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.RepastInfoGetPage();
                this.addModel = false;
            }
        },
        tableClick(item, type) {
            this.deleteList = [];
            this.userDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.startDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.dialogType = type;
            this.clickType = type;
            this.tableItem = item;
            this.user = this.$store.getters.getUserinfolist[0].LoginName;
            this.reason = '';
            this.deleteList.push(this.tableItem);
            switch (type) {
                case 'qx':
                    this.addTitle = this.$t('GLOBAL._QX');
                    this.addModel = true;
                    return;
                case 'pd':
                    this.RequestStartDate = null;
                    this.RequestFinishDate = null;
                    this.endDate = null;
                    this.addTitle = this.$t('GLOBAL._PD');
                    this.addModel = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // async MyGetRepairOrderStatus() {
        //     let res = await GetRepairOrderStatus();
        //     this.StatusList = res.response;
        // },
        // async MyGetRepairOrderType() {
        //     let res = await GetRepairOrderType();
        //     this.TypeList = res.response;
        // },
        // async MyGetRepairOrderSource() {
        //     let res = await GetRepairOrderSource();
        //     this.SourceList = res.response;
        // },
        changeTab(v) {
            switch (v) {
                case 0:
                    setTimeout(() => {
                        this.$refs.wxjl.MyGetRepairOrderGetListByWoId(this.rowtableItem, this.StatusList, this.MaintenanceGroupData);
                    }, 10);
                    break;
                case 1:
                    setTimeout(() => {
                        this.$refs.bj.MyGetPartsHistoryDetailPageList(this.rowtableItem);
                    }, 10);
                    break;
                case 2:
                    setTimeout(() => {
                        this.$refs.fwcg.MyGetRepairServiceGetList(this.rowtableItem);
                    }, 10);
                    break;
            }
        },
        //  查看BOM详情
        clickFun(data) {
            this.tableItem = data;
            this.rowtableItem = data || {};
            this.detailShow = true;
            switch (this.tab) {
                case 0:
                    setTimeout(() => {
                        this.$refs.wxjl.MyGetRepairOrderGetListByWoId(this.rowtableItem, this.StatusList, this.MaintenanceGroupData);
                    }, 10);
                    break;
                case 1:
                    setTimeout(() => {
                        this.$refs.bj.MyGetPartsHistoryDetailPageList(this.rowtableItem);
                    }, 10);
                    break;
                case 2:
                    setTimeout(() => {
                        this.$refs.fwcg.MyGetRepairServiceGetList(this.rowtableItem);
                    }, 10);
                    break;
            }
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.RepastInfoGetPage();
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        },
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                ...this.papamstree
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading = true;
            const res = await GetRepairOrderPageList(params);
            let { success, response } = res;
            for (let k in response.data) {
                let item = response.data[k];
                item.ReportByName = await this.$getPerson(item.ReportBy);
                item.ReceiveByName = await this.$getPerson(item.ReceiveBy);
                item.ConfirmByName = await this.$getPerson(item.ConfirmBy);
                this.StatusList.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                let str = '';
                this.ShifMngData.forEach(it => {
                    if (item.DutyManager.indexOf('|') != -1) {
                        let arr = item.DutyManager.split('|');
                        arr.forEach(k => {
                            if (k == it.ItemValue) {
                                console.log(it.ItemName, 3);
                                str += it.ItemName + ' ';
                            }
                        });
                        item.DutyManagerName = str;
                        item.DutyManagerValue = arr.join('|');
                    } else {
                        if (item.DutyManager == it.ItemValue) {
                            item.DutyManagerName = it.ItemName;
                            item.DutyManagerValue = it.ItemValue;
                        }
                    }
                });
                this.RepairManager.forEach(it => {
                    if (item.RepairManager == it.ItemValue) {
                        item.RepairManager = it.ItemName;
                        item.RepairManagerValue = it.ItemValue;
                    }
                });
            }
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}
.addForm {
    position: relative;
    .el-date-editor {
        width: 100%;
        position: absolute;
        opacity: 0;
        top: 0;
        .el-input__inner {
            font-size: 16px;
            height: 32.5px;
            color: rgba(0, 0, 0, 0.87);
            border: 1px solid #9e9e9e;
        }
        .el-input__icon {
            line-height: 32.5px;
        }
    }
}
::v-deep .v-select__selections {
    input {
        position: absolute;
    }
}
</style>

<template>
    <v-card>
        <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
            {{ operaObj.ID ? $t('DFM_XBHJW.editRacking') : $t('DFM_XBHJW.addRacking') }}
            <v-icon @click="closePopup">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid" class="mt-8">
                <v-row>
                    <v-col :cols="12" :lg="6">
                        <!-- 工厂 -->
                        <v-text-field v-model="form.Factory" :label="$t('DFM_WLBOMGL.Factory')" disabled required dense outlined></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                         <!-- label="货架代码" -->
                        <v-text-field v-model="form.RackingCode" :rules="rules.RackingCode" :label="$t('$vuetify.dataTable.DFM_XBHJW.RackingCode')" required dense outlined></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                         <!-- label="货架名称" -->
                        <v-text-field v-model="form.RackingName" :rules="rules.RackingName" :label="$t('$vuetify.dataTable.DFM_XBHJW.RackingName')" dense outlined></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                        <!-- <v-select
                            v-model="form.Status"
                            :rules="rules.Status"
                            :items="statusItems"
                            item-text="txt"
                            item-value="val"
                            label="有效标志"
                            persistent-hint
                            return-object
                            single-line
                            dense
                            outlined
                            @change="changeSelect"
                        ></v-select> -->
                         <!-- label="有效标志" -->
                        <v-select v-model="form.Status" :rules="rules.Status" :label="$t('$vuetify.dataTable.DFM_XBHJW.Status')" :items="statusItems" required dense outlined></v-select>
                    </v-col>
                    <v-col :cols="12" :lg="12">
                         <!-- label="货架位置描述" -->
                        <v-textarea v-model="form.Description" :label="$t('$vuetify.dataTable.DFM_XBHJW.Description')" rows="2" required dense outlined></v-textarea>
                    </v-col>
                    <v-col :cols="12" :lg="12">
                         <!-- label="备注" -->
                        <v-textarea v-model="form.Remark" :label="$t('$vuetify.dataTable.DFM_XBHJW.Remark')" rows="2" dense outlined></v-textarea>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>

        <v-divider></v-divider>

        <v-card-actions>
            <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { saveForm } from '@/api/factoryPlant/sideLine.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            checkbox: true,
            valid: true,
            form: {
                ID: '',
                Factory: '40012008',
                RackingCode: '',
                RackingName: '',
                Remark: '',
                Description: '',
                Status: ''
            },
            rules: {
                RackingCode: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                Status: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                RackingName: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            },
            statusItems: ['Y', 'N']
        };
    },
    watch: {
        operaObj: {
            handler(curVal) {
                for (const key in this.form) {
                    if (Object.hasOwnProperty.call(this.form, key)) {
                        this.form[key] = curVal[key] || this.form[key];
                    }
                }
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                const res = await saveForm(this.form);
                const { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    if (this.operaObj.ID || this.checkbox) {
                        this.$emit('handlePopup', 'refresh');
                    }else {
                        this.$refs.form.reset();
                    }
                }
                return false;
            }
        },
        closePopup() {
            this.$emit('handlePopup', 'close');
        },
        changeSelect(o) {
            const { val } = o;
            this.form.Status = val;
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>

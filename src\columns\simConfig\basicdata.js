// 首页信息
export const simColums = [
  {
    text: '序号',
    value: 'Index',
    width: 70,
    sortable: true
  },
  {
    text: 'SIM层级',
    value: 'Moudelname',
    width: 100,
    sortable: true
  },
  {
    text: '看板名称',
    value: 'Olinetit',
    width: 150,
    sortable: true
  },
  {
    text: '部门编码',
    value: 'Simlevel',
    width: 100,
    sortable: true
  },
  {
    text: '部门名称',
    value: 'SimlevelName',
    width: 200,
    sortable: true
  },
  {
    text: '创建人',
    value: 'CreateUserId',
    width: 100,
    sortable: true
  },
  {
    text: '创建时间',
    value: 'CreateDate',
    width: 100,
    sortable: true
  },
  { text: '操作', width: 180, align: 'center', value: 'actions', sortable: true }
];
// 新增信息
export const simAddColums = [
  {
    text: '序号',
    value: 'Index',
    width: 70,
    sortable: true
  },
  {
    text: '看板标题',
    value: 'Olinetit',
    width: 100,
    sortable: true
  },
  {
    text: '图表名称',
    value: 'ModularName',
    width: 100,
    sortable: true
  },
  {
    text: '区域数量',
    value: 'RegionNum',
    width: 100,
    sortable: true
  },
  {
    text: '图表类型',
    value: 'OlineType',
    width: 130,
    sortable: true
  },
  {
    text: '宽度',
    value: 'Width',
    width: 120,
    sortable: true
  },
  {
    text: '高度',
    value: 'Height',
    width: 120,
    sortable: true
  },
  {
    text: '顺序',
    value: 'Order',
    width: 140,
    sortable: true
  },
  {
    text: '边框图片',
    value: 'BorderImg',
    width: 120,
    sortable: true
  },
  {
    text: '数据源',
    value: 'DataSource',
    width: 120,
    sortable: true
  },
  {
    text: '背景图片',
    value: 'backgroundImage',
    width: 140,
    sortable: true
  },
  {
    text: '条件名称',
    value: 'ConditionName',
    width: 140,
    sortable: true
  },
  {
    text: '条件类型',
    value: 'ConditionType',
    width: 140,
    sortable: true
  },
  {
    text: '数据源',
    value: 'ConditionData',
    width: 140,
    sortable: true
  },
  {
    text: 'SIM层级',
    value: 'Moudelname',
    width: 140,
    sortable: true
  },
];

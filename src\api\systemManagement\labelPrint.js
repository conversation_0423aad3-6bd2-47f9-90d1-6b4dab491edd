import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM'
const baseURL1 = 'baseURL_30015'

//Country
export function addLabelCountry(data) {
    const api = '/api/LabelCountry/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
export function delLabelCountry(data) {
    const api = '/api/LabelCountry/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelCountryList(data) {
    const api = '/api/LabelCountry/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelCountryAllList(data) {
    const api = '/api/LabelCountry/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelCountryDetail(id) {
    const api = '/api/LabelCountry/GetEntity/' + id
    return getRequestResources(baseURL, api, 'get');
}
//Size
export function addLabelPrinterSize(data) {
    const api = '/api/LabelPrinterSize/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
export function delLabelPrinterSize(data) {
    const api = '/api/LabelPrinterSize/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelPrinterSizeList(data) {
    const api = '/api/LabelPrinterSize/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelPrinterSizeAllList(data) {
    const api = '/api/LabelPrinterSize/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelPrinterSizeDetail(id) {
    const api = '/api/LabelPrinterSize/GetEntity/' + id
    return getRequestResources(baseURL, api, 'get');
}
//LabelPrinterClass
export function getLabelPrinterClassAllList(data) {
    const api = '/api/LabelPrinterClass/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//Material
export function getMaterialAllList(data) {
    const api = '/api/Material/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//MaterialGroup
export function getMaterialGroupAll(data) {
    const api = '/api/MaterialGroup/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
// //MaterialClass
// export function getMaterialGroupAll(data) {
//     const api = '/api/MaterialGroup/GetList'
//     return getRequestResources(baseURL, api, 'post', data);
// }
//LabelTempleteClass
export function getLabelTempleteClassAllList(data) {
    const api = '/api/LabelTempleteClass/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//LabelFormat
export function getLabelFormatAllList(data) {
    const api = '/api/LabelFormat/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function addLabelFormat(data) {
    const api = '/api/LabelFormat/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
export function delLabelFormat(data) {
    const api = '/api/LabelFormat/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelFormatList(data) {
    const api = '/api/LabelFormat/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelFormatDetail(id) {
    const api = '/api/LabelFormat/GetEntity/' + id
    return getRequestResources(baseURL, api, 'get');
}
//LabelTemplete
export function getLabelTempleteAllList(data) {
    const api = '/api/LabelTemplete/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function addLabelTemplete(data) {
    const api = '/api/LabelTemplete/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
export function delLabelTemplete(data) {
    const api = '/api/LabelTemplete/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelTempleteList(data) {
    const api = '/api/LabelTemplete/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelTempleteDetail(id) {
    const api = '/api/LabelTemplete/GetEntity/' + id
    return getRequestResources(baseURL, api, 'get');
}
//LabelPrinter
export function getLabelPrinterAllList(data) {
    const api = '/api/LabelPrinter/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function addLabelPrinter(data) {
    const api = '/api/LabelPrinter/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
export function delLabelPrinter(data) {
    const api = '/api/LabelPrinter/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelPrinterList(data) {
    const api = '/api/LabelPrinter/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelPrinterDetail(id) {
    const api = '/api/LabelPrinter/GetEntity/' + id
    return getRequestResources(baseURL, api, 'get');
}
export function addPrinterClassProp(data) {
    const api = '/api/LabelPrinterClassProp/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//LabelPrintHistory
export function getLLabelPrintHistoryAllList(data) {
    const api = '/api/LabelPrintHistory/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function addLabelPrintHistory(data) {
    const api = '/api/LabelPrintHistory/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
export function delLabelPrintHistory(data) {
    const api = '/api/LabelPrintHistory/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelPrintHistoryList(data) {
    const api = '/api/LabelPrintHistory/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelPrintHistoryDetail(id) {
    const api = '/api/LabelPrintHistory/GetEntity/' + id
    return getRequestResources(baseURL, api, 'get');
}
//Equipment
export function getEquipmentAllList(data) {
    const api = '/api/Equipment/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//Equipmen分页
export function getEquipmentAllPageList(data) {
    const api = '/api/Equipment/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//LabelEquipmentPrinter
export function getLabelEquipmentPrinterAllList(data) {
    const api = '/api/LabelEquipmentPrinter/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function addLabelEquipmentPrinter(data) {
    const api = '/api/LabelEquipmentPrinter/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
export function delLabelEquipmentPrinter(data) {
    const api = '/api/LabelEquipmentPrinter/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelEquipmentPrinterList(data) {
    const api = '/api/LabelEquipmentPrinter/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLabelEquipmentPrinterDetail(id) {
    const api = '/api/LabelEquipmentPrinter/GetEntity/' + id
    return getRequestResources(baseURL, api, 'get');
}
//Equipment
export function getEquipmentTree(data) {
    const api = '/api/Equipment/GetEquipmentTreeForEnergy'
    return getRequestResources(baseURL, api, 'post', data);
}
//EnergyConfiguration
export function getEnergyMapping(data) {
    const api = '/api/EnergyMapping/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function addEnergyMapping(data) {
    const api = '/api/EnergyMapping/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
export function delEnergyMapping(data) {
    const api = '/api/EnergyMapping/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getEnergyMappingList(data) {
    const api = '/api/EnergyMapping/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getEnergyMappingDetail(id) {
    const api = '/api/EnergyMapping/GetEntity/' + id
    return getRequestResources(baseURL, api, 'get');
}
//Unit
export function getUnitList(data) {
    const api = '/api/Unitmanage/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//EquipmentGroup
export function getEquipmentGroup(data) {
    const api = '/api/EquipmentGroup/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function addEquipmentGroup(data) {
    const api = '/api/EquipmentGroup/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
export function delEquipmentGroup(data) {
    const api = '/api/EquipmentGroup/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getEquipmentGroupList(data) {
    const api = '/api/EquipmentGroup/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getEquipmentGroupDetail(id) {
    const api = '/api/EquipmentGroup/GetEntity/' + id
    return getRequestResources(baseURL, api, 'get');
}
//EquipmentGroupEquip
export function getEquipmentGroupEquip(data) {
    const api = '/api/EquipmentGroupEquip/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function addEquipmentGroupEquip(data) {
    const api = '/api/EquipmentGroupEquip/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
export function delEquipmentGroupEquip(data) {
    const api = '/api/EquipmentGroupEquip/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getEquipmentGroupEquipList(data) {
    const api = '/api/EquipmentGroupEquip/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function saveEquipmentGroupEquipSort(data) {
    const api = '/api/EquipmentGroupEquip/saveEquipmentGroupEquipSort'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getEquipmentGroupEquipDetail(id) {
    const api = '/api/EquipmentGroupEquip/GetEntity/' + id
    return getRequestResources(baseURL, api, 'get');
}
//EquipmentLine
export function getEquipmentLineList(data) {
    const api = '/api/Equipment/GetListByLevel?key=Line'
    return getRequestResources(baseURL, api, 'post', data);
}
//EquipmentAndLineList
export function getGetEquipmentAndLineList(data) {
    const api = '/api/Equipment/GetEquipmentAndLineList'
    return getRequestResources(baseURL, api, 'post', data);
}

export function getRoleEquipmentGroup(data) {
    const api = '/api/RoleEquipmentGroup/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function setRoleEquipmentGroup(data) {
    const api = '/api/RoleEquipmentGroup/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getPropertyList(id) {
    const api = '/api/Property/GetList?classid=' + id
    return getRequestResources(baseURL, api, 'post');
}
// 打印机属性列表
export function getListForProperty(data) {
    const api = '/api/LabelPrinterClassProp/GetListForProperty'
    return getRequestResources(baseURL, api, 'post', data);
}
// 保存打印机属性
export function addProperty(data) {
    const api = '/api/Property/SaveFormList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getEnergyInstrumentList(data) {
    const api = '/ppm/PoProducedExecution/GetEnergyInstrumentList'
    return getRequestResources(baseURL, api, 'post', data);
}
//备注
export function addBatchEditOrderRemark(data) {
    const api = '/ppm/ProductionOrder/BatchEditOrderRemark'
    return getRequestResources(baseURL1, api, 'post', data);
}
//获取工单BOM物料信息
export function getOrderBomMatList(data) {
    const api = '/ppm/PoMaterialRemark/GetOrderBomMatList'
    return getRequestResources(baseURL1, api, 'post', data);
}
//保存工单BOM物料备注信息
export function SaveRemarkList(data) {
    const api = '/ppm/PoMaterialRemark/SaveRemarkList'
    return getRequestResources(baseURL1, api, 'post', data);
}
//获取批次工单
export function getBatchOrderByParentId(data) {
    const api = '/ppm/Formulaschedule/GetBatchOrderByParentId'
    return getRequestResources(baseURL1, api, 'post', data);
}
//获取批次工单
export function getCookOrderLtexts(data) {
    const api = '/ppm/PoProducedExecution/GetCookOrderLtexts'
    return getRequestResources(baseURL1, api, 'post', data);
}



<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <!-- @click="RepastInfoGetPage" -->
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'SBGLZY_SBDXRW_DC'" @click="handleExport">{{ $t('GLOBAL._DC') }}</v-btn>
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    :clickFun="clickFun"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_SBDXJH"
                    :headers="OverhaulTaskColum"
                    :desserts="desserts"
                    @selectePages="selectePages"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"
                ></Tables>
                <createRepast ref="createRepast" :dialogType="dialogType" :rowtableItem="rowtableItem"></createRepast>
            </v-card>
            <el-drawer size="80%" :title="rowtableItem.Year + ' | ' + rowtableItem.Month + ' | ' + rowtableItem.Line" :wrapperClosable="false" :visible.sync="detailShow" direction="rtl">
                <v-card class="ma-1">
                    <div class="form-btn-list">
                        <v-btn color="primary" v-has="'SBGLZY_SBDXRW_CJWXGD'" @click="btnClickEvet('cjwxgd')">{{ $t('TPM_SBGL_SBDXJH.cjwxgd') }}</v-btn>
                    </div>
                    <Tables
                        :page-options="pageOptions"
                        :footer="false"
                        :showSelect="false"
                        :loading="loading2"
                        :btn-list="[]"
                        tableHeight="calc(100vh - 220px)"
                        table-name="TPM_SBGL_SBDXJH"
                        :headers="OverhaulOrderColum"
                        :desserts="desserts2"
                    ></Tables>
                </v-card>
            </el-drawer>

            <el-dialog :title="$t('GLOBAL._PJ')" :visible.sync="addModel" width="30%">
                <div class="addForm">
                    <v-autocomplete clearable v-model="pj" :items="pjList" item-text="ItemName" item-value="ItemValue" :label="$t('GLOBAL._PJ') + '*'" clear dense outlined></v-autocomplete>
                </div>
                <div class="addForm">
                    <v-text-field v-model="userDate" :clearable="true" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.CreateDate') + '*'" readonly></v-text-field>
                    <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="userDate" type="datetime" :placeholder="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.CreateDate')"></el-date-picker>
                </div>
                <div class="addForm">
                    <v-text-field v-model="Remark" outlined dense :label="$t('TPM_SBGL_WDGD.bz')"></v-text-field>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="addModel = false">取 消</el-button>
                    <el-button type="primary" @click="PjSave()">确 定</el-button>
                </span>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';
import { GetDevicePageList } from '@/api/equipmentManagement/MyRepair.js';
import {
    GetOverhaulWoPageList,
    GetRepairOrderList,
    GetOverhaulWoDelete,
    GetOverhaulWoStart,
    GetOverhaulWoCancel,
    GetOverhaulWoFinish,
    GetOverhaulWoEvaluate
} from '@/api/equipmentManagement/Overhaultask.js';
import { configUrl } from '@/config';
import moment from 'moment';
import { GetListByLevel } from '@/api/common.js';
import { Message, MessageBox } from 'element-ui';
import { GetExportData, GetPersonList } from '@/api/equipmentManagement/Equip.js';
import { OverhaulTaskColum, OverhaulOrderColum } from '@/columns/equipmentManagement/Overhaultask.js';
export default {
    name: 'RepastModel',
    components: {
        createRepast: () => import('./components/createRepast.vue')
    },
    data() {
        return {
            addModel: false,
            detailShow: false,
            Remark: '',
            // tree 字典数据
            tab: null,
            loading: false,
            showFrom: false,
            papamstree: {
                Line: '',
                Year: '',
                Month: '',
                Status: '',
                pageIndex: 1,
                pageSize: 20
            },
            rowtableItem: {},
            OverhaulTaskColum,
            //查询条件
            OverhaulOrderColum,
            desserts: [],
            loading2: false,
            desserts2: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            hasChildren: {}, // 新增字典详情判断-子节点才能新增

            //
            addTitle: '',
            pj: '',
            pjList: [],
            userDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
            OverhaulLine: [],
            OverhaulYear: [],
            OverhaulMonth: [],
            RepairMngData: [],
            ShifMngData: [],
            StatusList: [],
            OverhaulWoStatus: [],
            MaintenanceGroupData: []
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._KS'),
                    code: 'ks',
                    showList: ['未开始'],
                    showKey: 'Status',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_SBDXRW_START'
                },
                {
                    text: this.$t('GLOBAL._WC'),
                    code: 'wc',
                    type: 'primary',
                    showList: ['进行中'],
                    showKey: 'Status',
                    icon: '',
                    authCode: 'SBGLZY_SBDXRW_COMPLETE'
                },
                {
                    text: this.$t('TPM_SBGL_BJFFGL._TH'),
                    code: 'qx',
                    type: 'red',
                    showList: ['未开始', '进行中'],
                    showKey: 'Status',
                    icon: '',
                    authCode: 'SBGLZY_SBDXRW_QX'
                },
                {
                    text: this.$t('GLOBAL._PJ'),
                    code: 'pj',
                    showList: ['已维修'],
                    showKey: 'Status',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_SBDXRW_PJ'
                }
            ];
        },
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'Line',
                    label: this.$t('TPM_SBGL_SBDXJH._SCX'),
                    icon: 'mdi-account-check',
                    selectData: this.OverhaulLine,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Year',
                    label: this.$t('TPM_SBGL_SBDXJH._ND'),
                    icon: 'mdi-account-check',
                    selectData: this.OverhaulYear,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Month',
                    label: this.$t('TPM_SBGL_SBDXJH._YF'),
                    icon: 'mdi-account-check',
                    selectData: this.OverhaulMonth,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Status',
                    label: this.$t('TPM_SBGL_SBDXJH._ZT'),
                    icon: 'mdi-account-check',
                    selectData: this.OverhaulWoStatus,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'TaskBy',
                    label: this.$t('TPM_SBGL_JLRWGL.zrr'),
                    icon: 'mdi-account-check',
                    byValue:"ItemValue",
                    selectData: this.MaintenanceGroupData,
                    type: 'select',
                    placeholder: ''
                }
            ];
        }
    },
    async mounted() {
        let MaintenanceGroup = await GetPersonList('MaintenanceGroup');
        this.MaintenanceGroupData = MaintenanceGroup.response[0].ChildNodes;
        this.MaintenanceGroupData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.MyGetDevicePageList();
        let RepairMng = await GetPersonList('RepairMng');
        this.RepairMngData = RepairMng.response[0].ChildNodes;
        this.RepairMngData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        let ShiftMng = await GetPersonList('ShiftMng');
        this.ShifMngData = ShiftMng.response[0].ChildNodes;
        this.ShifMngData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.StatusList = await this.$getNewDataDictionary('RepairOrderStatus');
        this.TypeList = await this.$getNewDataDictionary('RepairOrderType');
        let RepairPhenomenon = await this.$getNewDataDictionary('RepairPhenomenon');
        let RepairUrgency = await this.$getNewDataDictionary('RepairUrgency');
        this.$refs.createRepast.SbxxList.forEach(item => {
            switch (item.id) {
                case 'Type':
                    item.option = this.TypeList;
                    break;
                case 'Phenomenon':
                    item.option = RepairPhenomenon;
                    break;
                case 'Urgency':
                    item.option = RepairUrgency;
                    break;
                case 'DutyManager':
                    item.option = this.RepairMngData;
                    break;
                case 'RepairManager':
                    item.option = this.ShifMngData;
                    break;
            }
        });
        this.OverhaulYear = this.getTenYearsRange();
        this.OverhaulMonth = this.getMonth();
        this.OverhaulWoStatus = await this.$getNewDataDictionary('OverhaulPlanStatus');
        this.pjList = await this.$getNewDataDictionary('Score');
        this.RepastInfoGetPage();
        this.getLine();
    },
    methods: {
        async MyGetDevicePageList() {
            let res = await GetDevicePageList();
            this.DevList = res.response;
            this.DevList.forEach(item => {
                item.ItemName = item.LineCode + ':' + item.Name + ':' + item.AssetsNo;
                item.ItemValue = item.ID + '|' + item.Code + '|' + item.LineCode;
            });
            this.$refs.createRepast.SbxxList[0].option = this.DevList;
        },
        async getLine() {
            let params = {
                key: 'Line'
            };
            let res = await GetListByLevel(params);
            res.response.forEach(item => {
                item.ItemName = item.EquipmentName;
                item.ItemValue = item.EquipmentName;
            });
            this.OverhaulLine = res.response;
        },
        getMonth() {
            let arr = [];
            for (let i = 1; i <= 12; i++) {
                let obj = {
                    ItemValue: i,
                    ItemName: i
                };
                arr.push(obj);
            }
            return arr;
        },
        getTenYearsRange() {
            let year = new Date().getFullYear(); // 如果没有提供年份，则使用当前年份
            let startYear = year - 5;
            let endYear = year + 5;
            let years = [];
            for (let i = startYear; i <= endYear; i++) {
                let obj = {
                    ItemValue: i,
                    ItemName: i
                };
                years.push(obj);
            }
            return years;
        },
        async handleExport() {
            let params = {
                ...this.papamstree
            };
            const baseUrl3 = configUrl[process.env.VUE_APP_SERVE]['baseURL_EQUIPMENT'] + `/api/OverhaulWo/ExportData`;
            let res = await GetExportData(baseUrl3, params);
            let binaryData = [];
            binaryData.push(res);
            const url = window.URL.createObjectURL(new Blob(binaryData));
            console.log(url);
            const link = document.createElement('a');
            link.href = url;
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            let fileName = `设备大修任务${formattedDateTime}.xlsx`;
            document.body.appendChild(link);
            link.setAttribute('download', fileName);
            link.click();
            window.URL.revokeObjectURL(link.href);
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.RepastInfoGetPage();
        },
        async RepastInfoGetPage() {
            let params = {
                ...this.papamstree
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading = true;
            const res = await GetOverhaulWoPageList(params);
            let { success, response } = res;
            response.data.forEach(item => {
                this.OverhaulWoStatus.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                this.MaintenanceGroupData.forEach(it => {
                    if (item.TaskBy == it.ItemValue) {
                        item.TaskBy = it.ItemName;
                        item.TaskByValue = it.ItemValue;
                    }
                });
            });
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        //  查看BOM详情
        clickFun(data) {
            this.tableItem = data;
            this.rowtableItem = data || {};
            this.detailShow = true;
            this.RepastInfoLogGetPage();
        },
        async RepastInfoLogGetPage() {
            if (!this.rowtableItem.ID) {
                return false;
            }
            let params = {
                ReferOrderNo: this.rowtableItem.OverhaulWo
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading2 = true;
            const res = await GetRepairOrderList(params);
            let { success, response } = res;
            response.forEach(item => {
                this.StatusList.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                this.ShifMngData.forEach(it => {
                    if (item.RepairManager == it.ItemValue) {
                        item.RepairManager = it.ItemName;
                    }
                });
                this.RepairMngData.forEach(it => {
                    if (item.DutyManager == it.ItemValue) {
                        item.DutyManager = it.ItemName;
                    }
                });
            });
            if (success) {
                this.loading2 = false;
                this.desserts2 = response || {} || [];
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'cjwxgd':
                    this.dialogType = val;
                    this.$refs.createRepast.SbxxList.forEach(item => {
                        item.value = '';
                        item.disabled = false;
                        if (item.id == 'Source') {
                            item.value = '大修';
                            item.disabled = true;
                        }
                    });
                    this.$refs.createRepast.FilePath = '';
                    this.$refs.createRepast.FileName = '';
                    this.$refs.createRepast.showDialog = true;
                    this.$refs.createRepast.clearFiles();
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'pj':
                    this.pj = '';
                    this.Remark = '';
                    this.userDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
                    this.addModel = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
                default:
                    this.PjSave();
                    return;
            }
        },
        async PjSave() {
            let params = this.tableItem;
            let res;
            switch (this.dialogType) {
                case 'pj':
                    if (this.pj == '' || this.userDate == null) {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    params.Evaluation = this.pj;
                    params.EvaluateDate = this.userDate;
                    params.EvaluateRemark = this.Remark;
                    res = await GetOverhaulWoEvaluate(params);
                    break;
                case 'wc':
                    res = await GetOverhaulWoFinish(params);
                    break;
                case 'qx':
                    res = await GetOverhaulWoCancel(params);
                    break;
                case 'ks':
                    if (params.Status != '未开始') {
                        this.$store.commit('SHOW_SNACKBAR', { text: '该状态无法开始', color: 'error' });
                        return;
                    }
                    res = await GetOverhaulWoStart(params);
                    break;
            }
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.RepastInfoGetPage();
                this.addModel = false;
            }
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await GetOverhaulWoDelete(params);
                    if (res.success) {
                        this.tableItem = {};
                        this.RepastInfoGetPage();
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}

.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

<template>
    <v-card>
        <v-card-title class="headline primary lighten-2" primary-title>{{ editItemObj.ID ? $t('GLOBAL._BJ') :
            $t('GLOBAL._XZ') }} </v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid">
                <v-row class="mt-5">
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-autocomplete :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" :items="lineList"
                            :label="$t('DFM_GDSPZCJDPZ.ProductlineName')" @change="handleChangeLine"
                            item-text="EquipmentName" item-value="EquipmentCode" required dense outlined
                            v-model="form.ProductlineId"></v-autocomplete>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-autocomplete :label="$t('DFM_GDSPZCJDPZ.SegmentName')"
                            :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" :items="segmentList" item-text="EquipmentName"
                            item-value="EquipmentCode" required dense outlined v-model="form.SegmentCode"></v-autocomplete>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-autocomplete :items="materialList" :label="$t('DFM_GDSPZCJDPZ.Materialname')" item-text="NAME"
                            item-value="Code" required dense outlined v-model="form.Materialcode"></v-autocomplete>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GDSPZCJDPZ.Intag')" required dense outlined
                            v-model="form.Intag"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GDSPZCJDPZ.Oktag')" required dense outlined
                            v-model="form.Oktag"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select :items="stateList" :label="$t('DFM_GDSPZCJDPZ.Islastsegment')" item-text="text"
                            item-value="value" required dense outlined v-model="form.Islastsegment"></v-select>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GDSPZCJDPZ.PlcOktag')" required dense outlined
                            v-model="form.PlcOktag"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select :items="stateList" :label="$t('DFM_GDSPZCJDPZ.Enable')" item-text="text"
                            item-value="value" required dense outlined v-model="form.Enable"></v-select>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
        <v-divider></v-divider>

        <v-card-actions>
            <!-- <v-spacer></v-spacer> -->
            <v-checkbox class="mr-auto" v-model="isChecked" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { saveForm } from '../service'
import { EquipmentGetPageList } from '@/api/common.js';
const stateList = [{ value: 0, text: '否' }, { value: 1, text: '是' }]
export default {
    props: {
        materialList: {
            type: Array,
            default: () => []
        },
        editItemObj: {
            type: Object,
            default: () => { }
        },
        lineList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            stateList,
            segmentList: [],
            valid: false,
            isChecked: true,
            form: {
                ProductlineId: '',
                SegmentCode: '',
                Intag: '',
                Oktag: '',
                Islastsegment: '',
                Materialcode: '',
                PlcOktag: '',
                Enable: ''
            }
        }
    },
    async created() {
        if (this.editItemObj && this.editItemObj.ID) {
            for (const key in this.form) {
                this.form[key] = this.editItemObj[key];
            }
            this.form.ID = this.editItemObj.ID;
            if (this.form.ProductlineId) {
                this.handleChangeLine(this.form.ProductlineId, 'init');
            }
        }
    },
    methods: {
        async handleChangeLine(val, type) {
            if (!type) {
                this.form.SegmentCode = '';
            }
            let obj = this.lineList.find(item => item.EquipmentCode == val);
            const { ID } = obj;
            this.segmentList = [];
            const res = await EquipmentGetPageList({ DataItemCode: 'EquipmentLevel', ParentId: ID, pageIndex: 1, pageSize: 9999 });
            const { success, response } = res;
            if (success) {
                this.segmentList = response.data;
            }
        },
        resetForm() {
            this.form = {
                ProductlineId: '',
                SegmentCode: '',
                Intag: '',
                Oktag: '',
                Islastsegment: '',
                Materialcode: '',
                PlcOktag: '',
                Enable: ''
            }
        },
        async submitForm() {
            if (!this.$refs.form.validate()) return false;
            let params = JSON.parse(JSON.stringify(this.form))
            params.ProductlineName = this.lineList.find(item => item.EquipmentCode == this.form.ProductlineId)?.EquipmentName || ''
            // 需求要求SegmentName SegmentId 取Remark字段
            params.SegmentId = this.segmentList.find(item => item.EquipmentCode == this.form.SegmentCode)?.Remark || ''  // 后端要求segmentId字段传name
            params.SegmentName = params.SegmentId
            params.Materialname = this.materialList.find(item => item.Code == this.form.Materialcode)?.NAME || ''
            let resp = await saveForm({ ...params });
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
            this.resetForm();
            this.$emit('getdata');
            if (this.isChecked) {
                this.$emit('closePopup');
            }
        },
        closePopup() {
            this.$emit('closePopup')
        }
    }
}
</script>

<style></style>
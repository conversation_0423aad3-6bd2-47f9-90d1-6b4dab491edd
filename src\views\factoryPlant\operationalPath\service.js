import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM'

//获取工艺路线表格信息
export function getPathList(data) {
  const api = '/api/RoutingHead/GetPageList'
  return getRequestResources(baseURL, api, 'post', data)
}

// 根据工艺路线获取路线产品列表
export function getProductList(data) {
  const api = '/api/RoutingProduct/GetPageList?routingId=' + data.routingId
  return getRequestResources(baseURL, api, 'post', data)
}

// 根据工艺路线获取路线明细列表
export function getDetailList(data) {
  const api = '/api/RoutingDetail/GetPageList'
  return getRequestResources(baseURL, api, 'post', data)
}

// 新建工艺路线信息
export function saveOperationalPath(data) {
  const api = '/api/RoutingHead/SaveForm'
  return getRequestResources(baseURL, api, 'post', data)
}

// 新建工艺路线明细
export function savePathDetail(data) {
  const api = '/api/RoutingDetail/SaveForm'
  return getRequestResources(baseURL, api, 'post', data)
}

// 新建工艺路线产品
export function savePathProduct(data) {
  const api = '/api/RoutingProduct/SaveForm'
  return getRequestResources(baseURL, api, 'post', data)
}

//获取数据字典明细 根据分类编号
export function getDataDictionary(data) {
  const api = '/api/DataItemDetail/GetList?lang=cn&&itemCode=' + data.itemCode
  return getRequestResources(baseURL, api, 'post', data)
}

//获取数据字典明细 根据分类编号
export function getMaterialList(data) {
  const api = '/api/Material/GetPageList'
  return getRequestResources(baseURL, api, 'post', data)
}

//  删除路线
export function deleteOperationalPath(data) {
  const api = '/api/RoutingHead/Delete'
  return getRequestResources(baseURL, api, 'post', data)
}

// 删除路线对应产品
export function deleteProduct(data) {
  const api = '/api/RoutingProduct/Delete'
  return getRequestResources(baseURL, api, 'post', data)
}

// 删除路线对应明细
export function deleteDetail(data) {
  const api = '/api/RoutingDetail/Delete'
  return getRequestResources(baseURL, api, 'post', data)
}
<template>
  <el-dialog
    :style="backgroundVar"
    :title="title1"
    :append-to-body="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="value"
    :before-close="handleClose1"
    lock-scroll
    :fullscreen="true"
  >
    <el-popover
      placement="left"
      trigger="click"
      :append-to-body="false"
      style="cursor: pointer;"
    >
      <el-button
        v-if="legendData.length>0"
        style="margin-top: 0px;margin-left: 5%;width: 93px;"
        type="primary"
        size="mini"
        @click="cancel()"
      >取消选择</el-button>
      <div>
        <span style="font-weight: bold;font-size: 16px;">请勾选图表需要显示的内容:</span>
      </div>
      <el-checkbox-group
        v-model="checkedCities"
        style="width:400px;margin-top: 5px;height:400px;overflow-y: auto;"
      >
        <el-checkbox
          v-for="city in legendData"
          :label="city"
          :key="city"
          :disabled="city == 'x'"
          @change="handleCheckboxChange(city)"
        >
          <div style="width:300px;">{{city}}</div>
        </el-checkbox>
      </el-checkbox-group>
      <div style="width: 100px;margin-top: 10px;float: right;">
        <div style="display: flex;">
          <el-button
            style="width: 93px;"
            type="primary"
            size="mini"
            @click="save()"
          >确 定</el-button>
        </div>
      </div>
      <div
        style="width:33px;height:30px;margin-top:-20px;"
        slot="reference"
      >
        <img
          style="width:100%;height:100%;cursor: pointer;"
          src="../image/simcd.png"
        />
      </div>

    </el-popover>
    <div
      :id="id1 + '-123'"
      style="width: 100%;height: 400px;"
    >
    </div>
    <div
      v-if="tableData.length>0"
      style="font-size: 18px;color:#fff;font-weight: bold;"
    >{{ barTitle }}:</div>
    <el-table
      v-if="tableData.length>0"
      :data="tableData"
      border
      style="width: 100%;margin-top: 20px;color:#fff; font-size: 24px;font-weight: bold;overflow-y: auto;overflow-x: auto;"
      :header-cell-style="{background:'#fafafa',textAlign: 'center',fontSize:'18px',color:'#409eff'}"
      :row-style="{height: '35px'}"
      show-overflow-tooltip
      :tooltip-effect="'dark'"
      :height="tableHeight"
    >
      <el-table-column
        v-if="checkedCitiesflag"
        label="日期"
        prop="date"
        align="center"
        min-width="120px"
      >
      </el-table-column>
      <el-table-column
        v-for="header in tableHeaders"
        :key="header"
        :prop="header"
        :label="header"
        align="center"
        min-width="120px"
      >
      </el-table-column>
    </el-table>
  </el-dialog>
</template>
<script>
import { getChartStructure, getTableList, getColorList } from '@/api/simConfig/simconfignew.js';
import { title } from 'echarts/lib/theme/dark';

export default {
  props: {
    // 是否显示弹出框
    value: {
      type: Boolean,
      default: false
    },
    position: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    id1: {
      type: String,
      default: ''
    },
    titleline: {
      type: String,
      default: ''
    },
    backgroundImg: {
      type: String,
      default: ''
    },
    Particle: {
      type: String,
      default: ''
    },
    legendData1: {
      type: Array,
      default: () => []
    },
    // colorList: {
    //   type: Array,
    //   default: () => []
    // }
  },
  data() {
    return {
      labelShow: false,
      legendData11: [],
      colorList: [],
      checkedCitiesflag: false,
      chartData: [],
      tableHeaders: [],
      tableData: [],
      legendDatanew: [],
      checkedCities: [],
      legendData: [],
      title1: '',
      barTitle: '',
      tableHeight: 0,
      lineLegend: ['实际值', '目标值'],
      //当前时间颗粒度
      curShift: {
        KpiValues: []
      },
      myShiftList: [],
      chartBartc: null,
      yAxisOption: {
        type: 'value',
        // show: false
        axisLine: {
          show: false
        },
        axisLabel: {
          show: true
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      },
    }
  },
  watch: {
    colorList(n, o) {
      console.log(n, o, 9999);
      if (n.length > 0) {
        this.colorList = n
      }
    }
  },
  computed: {
    backgroundVar() {
      return {
        '--background': this.backgroundImg
      }
    },
    //x轴配置
    xAxisOption() {
      let list = ['7', '6', '5', '4', '3', '2', '1']
      if (this.curShift.ChartData && this.curShift.ChartData.x) {
        list = this.curShift.ChartData.x.map(item => {
          // let month = dayjs(item).$M+1
          // let day = dayjs(item).$D
          if (['日', '周'].includes(this.curShift.TimeDimension)) {
            let month = item.split('-')[1]
            let day = item.split('-')[2]
            // 这里需要匹配颗粒度,来输出不同的x轴数据.
            // return `${month}-${day}`
            return `${month}-${day}`
          } else if (['月', '季度'].includes(this.curShift.TimeDimension)) {
            let label = item.split('年')[1]
            return label
          } else {
            return item
          }
        })
        // list.reverse()
        // list = this.curShift.ChartData.x
      }
      return {
        type: 'category',
        // boundaryGap: false,
        axisLine: {
          onZero: false,
          lineStyle: {
            color: '#fff'
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff'
        },
        data: list
      }
    },
    lineSeries() {
      if (this.curShift.ChartData.x.length <= 0) {
        this.labelShow = false // eslint-disable-line
        this.$nextTick(() => {
          const dom = document.getElementById(this.id1 + '-123');
          dom.innerHTML = '<div class="noDataBox">暂无数据</div>';
          dom.removeAttribute('_echarts_instance_');
          return
        })
      }
      //区分横竖
      let axisMarkLine = this.curConfig.ChartType === '4'
        ? [{ xAxis: this.curShift.TargetValue || '' }]
        : [{ yAxis: this.curShift.TargetValue || '' }]
      let list = []
      let legendData = []
      if (this.legendData1.length <= 0) {
        this.labelShow = false // eslint-disable-line
        if (Object.keys(this.curShift.ChartData).length == 2) {
          this.labelShow = true // eslint-disable-line
        } else {
          this.labelShow = false // eslint-disable-line
        }
        var colors = this.colorList
        Object.keys(this.curShift.ChartData).forEach((key, index) => {
          legendData.push(key)
          const sorted = legendData.sort((a, b) => {
            const aParts = a.split(';');
            const bParts = b.split(';');
            const mainPartA = aParts.slice(0, -1).join(';');
            const mainPartB = bParts.slice(0, -1).join(';');
            return mainPartA.localeCompare(mainPartB);
          });
          this.legendData = sorted
          this.legendData = this.legendData.filter(item => { // eslint-disable-line
            return !item.includes('目标值') && !item.includes('上限') && !item.includes('下限');
          });
          this.legendData11 = sorted
          if (['x', 'x', '目标值'].includes(key)) {
            return
          }
          let obj = {
            // name: `${this.curShift.KpiName}实际值`,
            // name: key.split(':')[1],
            name: `${key}实际值`,
            type: ['2', '3'].includes(this.curConfig.ChartType) ? 'bar' : 'line',
            symbol: 'circle',
            symbolSize: 14,
            // barWidth: 10,
            itemStyle: {
              normal: {
                color: colors[index % colors.length],
                // color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                //   offset: 0,
                //   color: "#4391F4" // 0% 处的颜色
                // }, {
                //   offset: 1,
                //   color: "#6B74E4" // 100% 处的颜色
                // }], false),
                // color: function (index) {
                //   return colors[index.dataIndex % colors.length];
                // },
                barBorderRadius: [4, 4, 4, 4],
              }
            },
            // data: this.curShift.KpiValues.map(item=>item.DataValue),
            data: this.curShift.ChartData[key],
            label: {
              show: this.labelShow,
              position: 'top',
              textStyle: {
                color: '#fff'
              }
            },
            // markLine: {//目标值线条
            //   silent: true,
            //   lineStyle: {
            //     color: this.curShift.TargetColor || 'gray'
            //     // color: 'red'
            //   },
            //   data: axisMarkLine
            //   // data: [{xAxis: 20 }]
            // }
          }
          list.push(obj)
        })
        list.map(el => {
          if (el.name.includes('目标值')) {
            el.type = 'line'
          }
        })
        return list
      } else {
        this.labelShow = true // eslint-disable-line
        Object.keys(this.curShift.ChartData).forEach((key, index) => {
          legendData.push(key)
          const sorted = legendData.sort((a, b) => {
            const aParts = a.split(';');
            const bParts = b.split(';');
            const mainPartA = aParts.slice(0, -1).join(';');
            const mainPartB = bParts.slice(0, -1).join(';');
            return mainPartA.localeCompare(mainPartB);
          });
          this.legendData = sorted
          this.legendData = this.legendData.filter(item => { // eslint-disable-line
            return !item.includes('目标值') && !item.includes('上限') && !item.includes('下限');
          });
          this.legendData11 = sorted
        })
        let result = [];
        this.legendData11.map(item => {
          this.legendData1.map(el => {
            if (item.includes(el)) {
              result.push(item);
            }
          })
        });
        const result2 = [];
        result.map(item => {
          if (result2.indexOf(item) === -1) {
            result2.push(item);
          }
        });
        this.legendData1 = result2 // eslint-disable-line

        var colors1 = this.colorList
        this.legendData1.map((key, index) => {
          // if (['x', 'x', '目标值'].includes(key)) {
          //   return
          // }
          let obj = {
            // name: `${this.curShift.KpiName}实际值`,
            // name: key.split(':')[1],
            name: `${key}实际值`,
            type: ['2', '3'].includes(this.curConfig.ChartType) ? 'bar' : 'line',
            symbol: 'circle',
            symbolSize: 14,
            itemStyle: {
              normal: {
                color: colors1[index % colors1.length],
                // color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                //   offset: 0,
                //   color: "#4391F4" // 0% 处的颜色
                // }, {
                //   offset: 1,
                //   color: "#6B74E4" // 100% 处的颜色
                // }], false),
                // color: function (index) {
                //   return colors1[index.dataIndex % colors1.length];
                // },
                barBorderRadius: [4, 4, 4, 4],
              }
            },

            // data: this.curShift.KpiValues.map(item=>item.DataValue),
            data: this.curShift.ChartData[key],
            label: {
              show: this.labelShow,
              position: 'top',
              textStyle: {
                color: '#fff'
              }
            },
            // markLine: {//目标值线条
            //   silent: true,
            //   lineStyle: {
            //     color: this.curShift.TargetColor || 'gray'
            //     // color: 'red'
            //   },
            //   data: axisMarkLine
            //   // data: [{xAxis: 20 }]
            // }
          }
          list.push(obj)
        })
      }

      list.map(el => {
        if (el.name.includes('目标值') || el.name.includes('上限') || el.name.includes('下限')) {
          el.type = 'line'
        }
      })
      return list

    },
  },
  created() {
    this.getColor()
    // this.getBarList()
    if (this.legendData1.length > 0) {
      this.checkedCitiesflag = true
    }
  },
  mounted() {
    this.$nextTick(function () {
      this.tableHeight = window.innerHeight - 574;
      let self = this;
      window.onresize = function () {
        self.tableHeight = window.innerHeight - 574;
      }
      this.checkedCities = this.legendData1;
    })
  },
  methods: {
    async getColor() {
      this.colorList = []
      let params = {
        "lang": "cn",
        "key": "",
        "RootId": "02312312-2261-6900-163e-0370f6000000",
        "itemCode": "EchartColorList",
        "pageIndex": 1,
        "pageSize": 100
      }
      let res = await getColorList(params)
      if (res.success) {
        res.response.data.map(el => {
          this.colorList.push(el.ItemValue)
        })
        if (this.colorList.length > 0) {
          this.getBarList()
        }
      }
    },
    cancel() {
      this.checkedCities = []
      this.legendDatanew.splice(0);
    },
    handleCheckboxChange(value) {
      const index = this.legendDatanew.indexOf(value);
      if (index > -1) {
        this.legendDatanew.splice(index, 1);
      } else {
        this.legendDatanew.push(value);
      }
    },
    save() {
      if (localStorage.getItem('list')) {
        let listData = JSON.parse(localStorage.getItem('list'))
        Object.keys(listData).map(el => {
          if (el == this.position) {
            if (this.checkedCities.length > 0) {
              this.checkedCities.map(item => {
                if (Object.keys(listData).includes(el)) {
                  listData[el] = this.checkedCities
                }
              })
            }
            localStorage.setItem('list', JSON.stringify(listData));
          } else {
            this.$store.dispatch('list/saveSelectedData', {
              componentId: this.position,
              data: this.checkedCities
            });
            localStorage.setItem('list', JSON.stringify(this.$store.getters['list/getSelectedDataByComponentId']));
          }
        })
      } else {
        this.$store.dispatch('list/saveSelectedData', {
          componentId: this.position,
          data: this.checkedCities
        });
        localStorage.setItem('list', JSON.stringify(this.$store.getters['list/getSelectedDataByComponentId']));
      }
      this.checkedCitiesflag = true
      // this.$emit('showCheckch', this.checkedCities)
      this.getBarList()
      this.getBarList1()
      // let listData1 = JSON.parse(localStorage.getItem('list'))
      // this.newArray = []; // eslint-disable-line
      // Object.keys(listData1).map(el => {
      //   if (el == this.position) {
      //     console.log(el, 'el');
      //     console.log(JSON.parse(localStorage.getItem('list'))[el]);

      //     const data = JSON.parse(localStorage.getItem('list'))[el];
      //     // for (const key in data) {
      //     //   for (const searchKey of this.curShift?.ChartData) {
      //     //     if (key.includes(searchKey)) {
      //     //       this.newArray.push(data[key]); // eslint-disable-line
      //     //       break; // 找到匹配的就不用继续用当前searchKey去判断了
      //     //     }
      //     //   }
      //     // }
      //     data.map(el2 => {
      //       for (const searchKey of this.curShift?.ChartData) {
      //         if (el2.includes(searchKey)) {
      //           this.newArray.push(data[el2]); // eslint-disable-line
      //           break; // 找到匹配的就不用继续用当前searchKey去判断了
      //         }
      //       }
      //     })
      //     console.log(this.newArray, 'newArraynewArraynewArray');
      //   }
      // })
    },
    async getBarList() {
      let params = {
        "Position": this.position,
        "BaseTime": this.BaseTime,
        "TeamCode": this.simlevel,
        // "ProductionLineCode": this.ProductionLineCode,
        // "FactoryCode": this.FactoryCode
      }
      let { response } = await getChartStructure(params)
      this.curConfig = response

      if (this.curConfig.IsSql == '1') {
        this.getBarList1()
      } else {
        if (this.curConfig?.ChartConfigs != null) {
          let listData1 = JSON.parse(localStorage.getItem('list'))
          if (listData1 != null) {
            let listData1 = JSON.parse(localStorage.getItem('list'))
            this.newArray = []; // eslint-disable-line
            Object.keys(listData1).map(el => {
              if (el == this.position) {
                this.legendData1 = JSON.parse(localStorage.getItem('list'))[el]; // eslint-disable-line
              }
            })
          }
          if (this.curConfig.Unit == undefined || this.curConfig.Unit == '' || this.curConfig.Unit == null) {
            this.title1 = this.titleline
          } else {
            // this.title1 = this.titleline + '(' + this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit + ')'
            this.title1 = this.titleline + '(' + this.curConfig.Unit + ')'
          }
          // this.id = this.curConfig.ID;
          // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
          this.curConfig.ChartConfigs.map(item => {
            item.KpiName = this.curConfig.ChartConfigs.KpiName
            if (item.KpiValues[0]) {
              item.KpiCode = item.KpiValues[0].KpiCode
              item.TargetValue = item.KpiValues[0].TargetValue || 0
            }
          })
          //图表配置整体赋值
          // this.curConfig = response
          //时间颗粒度列表
          this.myShiftList = this.curConfig.ChartConfigs.filter(item => {
            return item.TargetVisible === 1
          })
          //默认激活第一个时间颗粒度
          this.myShiftList.map((el, index) => {
            if (this.Particle == el.TimeDimension) {
              this.curShift = this.myShiftList[index]
              this.query1(true)
            }
          })
          // this.curShift = this.myShiftList[0]
          // this.query1()
          this.tableDataChange(this.curConfig.ChartConfigs[0]?.ChartData)
        } else {
          this.title1 = this.title
        }
      }

    },
    async getBarList1() {
      let params = {
        "simLevel": this.position.split('-')[0],
        "position": [
          this.position
        ],
        "paramList": [
          this.simlevel,
          this.BaseTime
        ]
      }
      let { response } = await getTableList(params)
      this.curConfig = response
      if (this.curConfig?.ChartConfigs != null) {
        if (this.curConfig.Unit == undefined || this.curConfig.Unit == '' || this.curConfig.Unit == null) {
          this.title1 = this.title
        } else {
          // this.title1 = this.title + '(' + this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit + ')'
          this.title1 = this.title + '(' + this.curConfig.Unit + ')'
        }
        // this.id = this.curConfig.ID;
        // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
        this.curConfig.ChartConfigs.map(item => {
          item.KpiName = this.curConfig.ChartConfigs.KpiName
          if (item.KpiValues[0]) {
            item.KpiCode = item.KpiValues[0].KpiCode
            item.TargetValue = item.KpiValues[0].TargetValue || 0
          }
        })
        //图表配置整体赋值
        // this.curConfig = response
        //时间颗粒度列表
        this.myShiftList = this.curConfig.ChartConfigs.filter(item => {
          return item.TargetVisible === 1
        })
        //默认激活第一个时间颗粒度
        this.myShiftList.map((el, index) => {
          if (this.Particle == el.TimeDimension) {
            this.curShift = this.myShiftList[index]
            this.query1(true)
          }
        })
        this.tableDataChange(this.curConfig.ChartConfigs[0]?.ChartData)
      } else {
        this.title1 = this.title
      }
    },
    tableDataChange(data) {
      const keys = Object.keys(this.curShift.ChartData);
      let formattedData1 = keys.filter(key => key !== 'x');
      // this.tableHeaders = formattedData1.map(item => item.replace(/≥/g, '大于等于'));

      const dateValues = this.curShift.ChartData.x;
      let dataArray = keys.filter(key => key !== 'x').map(key => {
        return {
          key,
          values: this.curShift.ChartData[key]
        };
      });
      // const formattedData = dataArray.map(item => {
      //   return {
      //     ...item,
      //     key: item.key.replace(/≥/g, '大于等于')
      //   };
      // });
      // dataArray = formattedData
      this.tableData = dateValues.map((dateValue, index) => {
        const row = { date: dateValue };
        dataArray.map(item => {
          row[item.key] = item.values[index];
        });
        return row;
      });
      const allKeys = new Set();
      this.tableData.map(item => {
        Object.keys(item).map(key => allKeys.add(key));
      });
      this.tableHeaders = Array.from(this.checkedCities.length <= 0 ? allKeys : this.checkedCities)

      this.tableData = this.tableData.map(item => {
        const newItem = { date: item.date };
        this.tableHeaders.map(header => {
          if (header !== 'date') {
            newItem[header] = item[header] || 0;
          }
        });
        return newItem;
      });
    },
    query1() {
      // if (this.chartBartc) {
      //   this.chartBartc.clear()
      //   return
      // }
      this.chartBartc = this.$echarts.init(document.getElementById(this.id1 + '-123'));
      var option
      var that1 = this
      option = {
        symbol: 'circle',
        tooltip: {
          axisPointer: {
            type: 'shadow',
          },
          confine: true,
          extraCssText: 'max - width: none; overflow: visible;',
          formatter: function (params) {
            if (that1.Particle == '日') {
              let abc = []
              let abc1 = []
              let name1 = ''
              if (that1.curConfig.listShift.length > 0) {
                that1.curConfig.listShift.map(el => {
                  if ((el.WorkDate.split(' ')[0].split('-')[1] + '-' + el.WorkDate.split(' ')[0].split('-')[2]) == params.name) {
                    abc1.push(el)
                  }
                })
              }
              let msg = ''
              Object.keys(that1.curConfig.ChartConfigs[0].ChartData).map((el, index) => {
                if (el.includes(params.seriesName + '_')) {
                  abc.push(el + '：' + that1.curConfig.ChartConfigs[0].ChartData[el][params.dataIndex])
                } else if (el == params.seriesName) {
                  abc.push(el + '：' + that1.curConfig.ChartConfigs[0].ChartData[el][params.dataIndex])
                }
              })
              abc1.map(el => {
                if (params.seriesName.includes(el.WorkShift)) {
                  name1 = params.seriesName + '-' + el.WorkNum
                }
                // else {
                //   name1 = params.seriesName
                // }
              })
              msg += (name1 == '' ? params.seriesName : name1) + '：' + params.value + "<br>";
              // msg += name1 + "<br>";
              // abc.map((el, index) => {
              //   msg += el + "<br>";
              // })
              return msg
            } else {
              let msg = ''
              let arr = []
              let abc = []
              that1.curConfig.ChartConfigs.map((el, index) => {
                if (el.TimeDimension == that1.Particle) {
                  arr.push(el)
                }
              })
              arr.map((el, index) => {
                Object.keys(el.ChartData).map((el1, index1) => {
                  if (el1.includes(params.seriesName + '_')) {
                    abc.push(el1 + '：' + arr[0].ChartData[el1][params.dataIndex])
                  }
                })
              })
              msg += (params.seriesName + params.value) + "<br>";
              // msg += params.name + "<br>";
              // msg += params.seriesName + '：' + params.value + "<br>";
              // abc.map((el, index) => {
              //   msg += el + "<br>";
              // })
              return msg
            }
          }
        },
        legend: {
          data: this.lineSeries.map(item => item.name),
          textStyle: {
            color: '#fff'
          }
        },
        grid: {
          left: '5%',
          right: '6%',
          bottom: '3%',
          top: '20%',
          containLabel: true
        },

        // visualMap: this.lineVisualMap,
        xAxis: this.xAxisOption,
        yAxis: {
          type: 'value',
          // show: false
          axisLine: {
            show: true,
            lineStyle: {
              color: '#fff'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff" //X轴文字颜色
            },
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        series: this.lineSeries
      }
      this.chartBartc.setOption(option, true);
      window.addEventListener("resize", () => {
        this.chartBartc.resize()
      }, false);
    },
    handleClose1() {
      this.$emit('showCheck2')
    }
  }
}
</script>
<style scoped>
/deep/ .el-dialog__title {
    color: #fff !important;
}
/deep/ .el-dialog {
    background: var(--background) no-repeat 0 0;
    background-size: 100% 100% !important;
    overflow: hidden;
}
/deep/ .el-textarea__inner {
    font-size: 16px !important;
}
/deep/ .el-dialog__headerbtn .el-dialog__close {
    font-size: 22px;
}
/deep/ .el-dialog__header {
    border: none !important;
}
/deep/.el-textarea__inner {
    background-color: rgba(225, 225, 225, 0);
    border: none !important;
    color: black;
    font-size: 16px;
    /* font-weight: bold; */
    /* font-family: "Lucida Calligraphy", cursive, serif, sans-serif; */
}

/deep/ .el-dialog__headerbtn .el-dialog__close {
    font-size: 22px;
}
/deep/ .el-dialog__header {
    border: none !important;
}

/deep/.el-textarea__inner {
    /* background-color: rgba(225, 225, 225, 0); */
    background-color: #fff !important;
    border: none !important;
    color: black;
    font-size: 20px;
    /* font-weight: bold; */
}
/deep/.el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}
/deep/ .el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}

/deep/ .el-table tr {
    background-color: transparent !important;
    border: none;
}
/deep/ .el-table--enable-row-transition .el-table__body td,
.el-table .cell {
    background-color: transparent !important;
}
/deep/ .el-table th.el-table__cell {
    background-color: transparent !important;
    color: #fff;
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}
/deep/ .el-dialog__title {
    color: #fff !important;
}

/deep/ .el-table tbody tr {
    pointer-events: none;
}
::v-deep .noDataBox {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
}
/* /deep/ .el-table__header-wrapper {
    background-color: #4391f4 !important;
} */
</style>
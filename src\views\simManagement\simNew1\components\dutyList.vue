<template>
  <el-dialog
    :style="backgroundVar"
    :append-to-body="!fullscreen"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="value"
    :before-close="handleClosetable"
    lock-scroll
    title="弹窗配置"
    width="50%"
  >
    <div class="styleTable">
      <el-radio
        style="color: #fff;"
        v-model="radio"
        label="1"
      >是</el-radio>
      <el-radio
        style="color: #fff;"
        v-model="radio"
        label="0"
      >否</el-radio>
      <el-select
        v-model="value1"
        filterable
        clearable
        placeholder="请选择KPI"
      >
        <el-option
          v-for="item in dutyListData"
          :key="item.KpiCode"
          :label="item.KpiName"
          :value="item.KpiCode"
          @click.native="dutyListChange(item)"
        >
        </el-option>
      </el-select>

      <el-button
        style="float: right;margin-top: 20px;"
        type="primary"
        size="mini"
        @click="submitForm"
      >保 存</el-button>
    </div>

  </el-dialog>
</template>
<script>
import { GetKpiList, SaveConfig, GetChartStructure } from '@/views/simManagement/components/chartsConfig/service.js';
import { getDataSourceList, getEditEntityByCode } from '@/api/simConfig/simconfignew.js';

export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    dutIndex: {
      type: String,
      default: ''
    },
    fullscreen: {
      type: Boolean,
      default: false
    },
    backgroundImg: {
      type: String,
      default: ''
    },
    Position: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      klpiName: '',
      radio: '0',
      value1: '',
      dutyListData: []
    }
  },
  computed: {
    backgroundVar() {
      return {
        '--background': this.backgroundImg
      }
    },
  },
  created() {
    this.query()
    console.log(this.fullscreen, 'fullscreenfullscreenkkkkk');

  },

  methods: {
    dutyListChange(item) {
      this.klpiName = item.KpiName
    },
    handleClosetable() {
      this.$emit('checkdut')
    },
    async query() {
      const params = {
        "SimLevel": this.Position.split('-')[0],
        "KpiType": "",
        "isSql": true
      }
      const res = await getDataSourceList(params);
      this.dutyListData = res.response
    },
    //确定
    async submitForm() {
      this.$emit('checkdut', this.radio, this.value1, this.dutIndex, this.klpiName)
    },

  }

}
</script>
<style scoped>
.titName {
    width: 160px;
    height: 10%;
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    text-align: center;
    cursor: pointer;
    margin-right: 20px;
}
.active {
    color: #409eff;
    font-weight: bold;
    border-right: 2px solid #409eff;
    box-shadow: 0px 0px 10px #fff;
    border-radius: 5px;
    font-size: 22px;
}
/deep/ .el-dialog {
    background: var(--background) no-repeat 0 0;
    background-size: 100% 100% !important;
    overflow: hidden;
}
.styleTable /deep/.el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}
.styleTable /deep/ .el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}

.styleTable /deep/ .el-table tr {
    background-color: transparent !important;
    border: none;
}
.styleTable /deep/ .el-table--enable-row-transition .el-table__body td,
.el-table .cell {
    background-color: transparent !important;
}
.styleTable /deep/ .el-table th.el-table__cell {
    background-color: transparent !important;
    color: #fff;
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}
/deep/ .el-dialog__title {
    color: #fff !important;
}
/deep/ .v-select__selection,
.v-select__selection--comma {
    color: #fff !important;
}
/deep/ .v-label,
.v-label--active,
.theme--light {
    color: #fff !important;
}
/deep/ .theme--light.v-text-field > .v-input__control > .v-input__slot:before {
    border-color: #fff !important;
}
</style>
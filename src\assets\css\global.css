

.el-button--success {
    background-color: #3dcd58 !important;
    border-color: #3dcd58 !important;
}

.el-switch.is-checked .el-switch__core {
    background-color: #3dcd58 !important;
    border-color: #3dcd58 !important;
}

.el-button.is-disabled {
    color: #C0C4CC !important;
}

.el-tag.el-tag--success {
    /*color: #3dcd58 !important;*/
}

.el-icon-edit-outline {
    color: inherit;
}

.el-button--primary {
    color: #FFFFFF;
    background-color: #3dcd58;
    border-color: #3dcd58;
}

.el-button--text {
    color: #3dcd58 !important;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: #3dcd58 !important;
}

.el-table th.el-table__cell > .cell {

}

/*.el-tag.el-tag--success{*/
/*    color: #3dcd58 !important;*/
/*}*/
.el-button--text:hover, .el-button--text:focus {
    border: 0 !important;
}

.el-input.is-disabled .el-input__inner {
    color: #999 !important;
}

.descriptions-box {
    width: 15vw;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.el-descriptions__body .el-descriptions__table {
    white-space: nowrap;
}

.el-descriptions-item__content {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 8px;
}

.v-application .primary.lighten-2 {
    background-color: transparent !important;
    border-color: transparent !important;
}

.lighten-2 {
    background-color: transparent !important;
    border-color: transparent !important;
}
/*::-webkit-scrollbar {*/
/*    height: 18px !important;*/
/*}*/
.font-05 {
    font-size: 0.5rem;
}

.font-06 {
    font-size: 0.6rem;
}

.font-07 {
    font-size: 0.7rem;
}

.font-08 {
    font-size: 0.8rem;
}

.font-09 {
    font-size: 0.9rem;
}

.font-10 {
    font-size: 1rem;
}

.font-12 {
    font-size: 1.2rem;
}

.font-14 {
    font-size: 1.4rem;
}

.font-16 {
    font-size: 1.6rem;
}

.font-20 {
    font-size: 2rem;
}

.font-30 {
    font-size: 3rem;
}

.font-50 {
    font-size: 5rem;
}
.font-M05 {
    font-size: 0.5vw
}

.font-M06 {
    font-size: 0.6vw;
}

.font-M07 {
    font-size: 0.7vw;
}

.font-M08 {
    font-size: 0.8vw;
}

.font-M09 {
    font-size: 0.9vw;
}

.font-M10 {
    font-size: 1vw;
}

.font-M12 {
    font-size: 1.2vw;
}

.font-M14 {
    font-size: 1.4vw;
}

.font-M16 {
    font-size: 1.6vw;
}

.font-M20 {
    font-size: 2vw;
}

.font-M30 {
    font-size: 3VW;
}

.font-M50 {
    font-size: 5vw;
}

.color-white {
    color: white;
}

.color-red {
    color: red;
}

.color-yellow {
    color: yellow;
}

.color-green {
    color: #3dcd58;
}

.color-blue {
    color: #4aa2e4;
}

.m-l-5px {
    margin-left: 5px;
}

.m-l-10px {
    margin-left: 10px;
}

.m-l-15px {
    margin-left: 15px;
}

.m-r-5px {
    margin-right: 5px;
}

.m-t-12h {
    margin-top: 12vh !important;
}

.background-color-red {
    background-color: #9c1d30;
}

.background-color-red1 {
    background-color: red;
}

.background-color-yellow {
    background-color: yellow;
}

.background-blue {
    background-color: #072d59 ;
}

.background-blue1 {
    background-color: #3b7ee5;
}

.background-green {
    background-color: #25c092;
}

.background-gray {
    background-color: #999;
}

.background-yellow {
    background-color: #ffe75f;
}

.background-purple {
    background-color: #c744fe;
}

.background-red {
    background-color: #fa0000;
}

.dashboard-Material-preparation-legend-blue {
    background-color: #072d59;
}

.dashboard-Material-preparation-legend-blue1 {
    background-color: #3b7ee5;
}

.dashboard-Material-preparation-legend-green {
    background-color: #25c092;
}

.dashboard-Material-preparation-legend-gray {
    background-color: #999;
}

.dashboard-Material-preparation-legend-yellow {
    background-color: #ffe75f;
}

.dashboard-Material-preparation-legend-purple {
    background-color: #c744fe;
}

.dashboard-Material-preparation-legend-red {
    background-color: #fa0000;
}
.sim-line-status-gray {
    background-image: radial-gradient(circle, rgba(200, 200, 200, 1), rgba(50, 50, 50, 1))
}
.sim-line-status-green {
    background-image: radial-gradient(circle, rgba(40, 247, 159, 1), rgba(1, 147, 85, 1))
}
.sim-line-status-blue {
    background-image: radial-gradient(circle, rgba(59, 171, 255, 1), rgba(36, 91, 132, 1))
}
.sim-line-status-red {
    background-image: radial-gradient(circle, rgba(255, 62, 62, 1), rgba(132, 36, 36, 1))
}
.sim-line-status-yellow {
    background-image: radial-gradient(circle, rgba(255, 189, 62, 1), rgba(169, 127, 47, 1))
}

.main-box {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: hidden;
    top: 0;
    left: 0;
    z-index: 99;
}
.dashboard-main {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: auto;
    top: 0;
    left: 0;
}

.position-absolute {
    overflow: hidden;
    position: absolute;
    top: 0;
}

.dashboard-content-background {
    background-image: url("../imgs/coldtorage_bg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.dashboard-content-background-one {
    background-image: url("../imgs/backgroundOne.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.dashboard-content-background-two {
    background-image: url("../imgs/background-tow.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.dashboard-head-content {
    width: 100%;
    height: 10vh;
    background-image: url("../imgs/dashboard-head.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: absolute;
    top: 0;
    left: 0;
}

.dashboard-head-content-one {
    width: 100%;
    height: 9vh;
    background-image: url("../imgs/dashboard-head-one.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: absolute;
    top: 0;
    left: 0;
}

.dashboard-head-content-two {
    width: 100%;
    height: 8vh;
    background-image: url("../imgs/bz_title_bg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: absolute;
    top: 0;
    left: 0;
}

.dashboard-head-title {
    width: 100%;
    height: 9vh;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    letter-spacing: 2px;
}

.dashboard-head-left {
    width: 16vw;
    height: 5vh;
    position: absolute;
    top: 4.5vh;
    left: 9vw;
    background-image: url("../imgs/head-left.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
}

.dashboard-head-right {
    width: 16vw;
    height: 5vh;
    position: absolute;
    top: 4.5vh;
    right: 9vw;
    background-image: url("../imgs/head-right.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;

}

.dashboard-head-right-one {
    width: 16vw;
    height: 5vh;
    position: absolute;
    top: 1.5vh;
    right: 19vw;
    background-image: url("../imgs/head-right-one.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;

}

.dashboard-head-left-Icon {
    width: 1.2vw;
    height: 1.2vw;
    background-image: url("../imgs/ljgzstb.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: inline-block;
    vertical-align: middle;
}

.dashboard-head-right-Icon {
    background-image: url("../imgs/ljqrtsstb.png");
}

.dashboard-table-title {
    width: 100%;
    height: 6vh;
    background-image: url("../imgs/table-title.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
}

.dashboard-table-title-text {
    position: absolute;
    top: 0;
    left: 2vw;
}

.dashboard-table-title-text-right {
    position: absolute;
    top: -4rem;
    right: 19vw;
    font-size: 5rem;
}

.dashboard-content-item {
    width: 96%;
    height: 40vh;
    margin: 2vh auto 0 auto;
    position: relative;
}

.dashboard-table-header {
    width: 100%;
    height: 6vh;
    display: flex;
    background: linear-gradient(to bottom, rgba(10, 100, 154, 0.2), rgba(10, 100, 154, 0.9));
    border-bottom: 1px solid #17a3ff;
}

.dashboard-table-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}

.dashboard-table-box {
    width: 100%;
    height: 30vh;
    overflow-y: auto;
}

.dashboard-table-content {
    width: 100%;
    height: 6vh;
    display: flex;
}

.dashboard-table-content-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    background-color: rgba(10, 100, 154, 0.5);
    margin-left: 0.2vw;
    margin-top: 1vh;
}

.dashboard-table-td-border-left {
    border-left: 1px solid #17a3ff;
}

.dashboard-table-td-border-right {
    border-right: 1px solid #17a3ff;
}

.dashboard-table-time-background {
    border-radius: 5%;
    padding: 0.3vh 1vw;
}

.dashboard-rawMaterial-table-left {
    width: calc(50% - 0.5vw);
    height: 36vh;
    position: absolute;
    top: 7vh;
    left: 0;
    overflow-y: auto;
}

.dashboard-rawMaterial-table-right {
    width: calc(50% - 0.5vw);
    height: 36vh;
    position: absolute;
    top: 7vh;
    right: 0;
    overflow-y: auto;
}

.dashboard-rawMaterial-table {
    width: 100%;
    color: white;
}

.dashboard-rawMaterial-table th {
    text-align: center;
    border: 1px solid #95d4ff;
    background-color: rgba(149, 212, 255, 0.2);
}

.dashboard-rawMaterial-table tr {
    height: 5.6vh;
}

.dashboard-rawMaterial-table tr:nth-child(even) {
    background-color: rgba(149, 212, 255, 0.2);
}

.dashboard-rawMaterial-table td {
    text-align: center;
    border: 1px solid #95d4ff;
}

.dashboard-rawMaterial-load-box {
    width: 30vw;
    border: 1px solid #26a6ff;
    height: 3vh;
    display: inline-block;
    border-radius: 1vh;
    margin-bottom: -1vh;
    margin-left: 1vw;
    overflow: hidden;
    position: relative;
}

.dashboard-rawMaterial-load-box-load {
    width: 50%;
    height: 100%;
    background: linear-gradient(to right, rgba(8, 209, 113, 0.2), rgba(8, 209, 113, 0.9));
}

.dashboard-rawMaterial-load-box-load-left {
    line-height: 3vh;
    position: absolute;
    left: 0.5vw;
    top: 0;
    z-index: 9;
}

.dashboard-rawMaterial-load-box-load-right {
    line-height: 3vh;
    position: absolute;
    right: 0.5vw;
    top: 0;
    z-index: 9;
}

.dashboard-Material-preparation-legend {
    width: 0.7vw;
    height: 0.7vw;
    display: inline-block;
    margin-left: 0.5vw;
    margin-right: 0.1vw;
    border: 1px solid white;
}

.dashboard-Material-preparation-legend-one {
    width: 0.7vw;
    height: 0.7vw;
    display: inline-block;
    margin-left: 0.5vw;
    margin-right: 0.1vw;
    border: 1px solid white;
    border-radius: 50%;
    overflow: hidden;
    vertical-align: middle;
}

.dashboard-Material-preparation-head {
    position: absolute;
    top: 12vh;
    left: 1vw;
    width: calc(100% - 2vw);
    height: 6vh;
    background-image: url("../imgs/workorder-Table-head.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    /*background: linear-gradient(to top, rgba(10,100,154,0.9), rgba(10,100,154,0.1));*/
}

.dashboard-Material-preparation-table {
    position: absolute;
    top: 17.8vh;
    left: 1vw;
    width: calc(100% - 2vw);
}

.dashboard-Material-preparation-tr {
    width: 100%;
    min-height: 6vh;
    display: flex;
}

.dashboard-Material-preparation-td {
    border: solid 1px #2eb1e0;
    flex: 1;
    padding: 1vh 5px 1vh 5px;
    overflow: hidden;
}

.dashboard-Material-preparation-Formula-code {
    height: 4vh;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #1e5d89;
}

.dashboard-Material-preparation-work-order {
    width: 5vw;
    height: 8vh;
    background-image: url("../imgs/workorder-cont.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
}

.dashboard-Material-pot-left {
    width: 75%;
    float: left;
}

.dashboard-Material-pot-box {
    width: 100%;
    height: 4vh;
    border: 1px solid #2486be;
    overflow: hidden;
    border-radius: 1vw;
}

.dashboard-Material-pot-box-one {
    width: 100%;
    height: 3vh;
    border: 1px solid #2486be;
}

.dashboard-Material-pot-box-item-one {
    float: left;
    width: auto;
    height: 100%;
    line-height: 3vh;
}

.dashboard-Material-pot-box-item {
    float: left;
    width: auto;
    height: 100%;
    line-height: 4vh;
}

.dashboard-Material-pot-right {
    margin-left: 77%;
}

.dashboard-Material-total-pot {
    width: 5vw;
    height: 4vh;
    background-image: url("../imgs/totalPot.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
}

.el-select-dropdown.el-popper.detailDialog_select-popper {
    z-index: 10001 !important;
}

.Cooking-ingredients-box {
    width: 98vw;
    height: 88vh;
    position: absolute;
    top: 10vh;
    left: 1vw;
    background-image: url("../imgs/Cooking-ingredients.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.dashboard-Cooking-table {
    width: 100%;
    color: white;
}

.dashboard-Cooking-table th {
    text-align: center;
    font-size: 0.9rem;
    border: 1px solid #95d4ff;
    background-color: rgba(149, 212, 255, 0.2);
}

.dashboard-Cooking-table tr {
    height: 4.5vh;
}

.dashboard-Cooking-table tr:nth-child(even) {
    background-color: rgba(149, 212, 255, 0.2);
}

.dashboard-Cooking-table td {
    text-align: center;
    font-size: 0.8rem;
    border: 1px solid #95d4ff;
}

.dashboard-Cooking-preparation-table {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.dashboard-Cooking-preparation-th {
    background-color: rgba(149, 212, 255, 0.15);
}

.dashboard-Cooking-preparation-tr {
    width: 100%;
    min-height: 6vh;
    display: flex;
}

.dashboard-Cooking-preparation-td {
    border: solid 1px #2eb1e0;
    flex: 1;
    padding: 1vh 5px 1vh 5px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.dashboard-Cooking-produce {
    width: 60vw;
    height: 60vh;
    position: absolute;
    left: 1vw;
    top: 11vh;
    border: 1px solid #2eb1e0;
}

.dashboard-Cooking-PAC {
    width: 37vw;
    height: 32vh;
    position: absolute;
    left: 62vw;
    top: 11vh;
    border: 1px solid #2eb1e0;
}

.dashboard-Cooking-stop {
    width: 37vw;
    height: 26vh;
    position: absolute;
    left: 62vw;
    top: 45vh;
    border: 1px solid #2eb1e0;
}

.dashboard-Cooking-this {
    width: 45vw;
    height: 26vh;
    position: absolute;
    left: 54vw;
    bottom: 18vh;
    border: 1px solid #2eb1e0;
}

.dashboard-Cooking-next {
    width: 52vw;
    height: 26vh;
    position: absolute;
    left: 1vw;
    bottom: 18vh;
    border: 1px solid #2eb1e0;
}

.dashboard-Cooking-title {
    width: 100%;
    height: 4vh;
    line-height: 4vh;
    background: linear-gradient(to right, rgba(4, 100, 166, 1), rgba(4, 100, 166, 0.1))
}

.Cooking-title-right {
    position: absolute;
    right: 1vw;
    top: 0;
    height: 4vh;
}

.dashboard-produce-table-head {
    width: 99%;
    margin: 1vh auto;
    height: 6vh;
    background-image: url("../imgs/table-head.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border-bottom: 1px solid #2eb1e0;
    display: flex;
}

.table-head-item {
    flex: 1;
    line-height: 6vh;
    text-align: center;
}

.dashboard-produce-table-box {
    width: 99%;
    margin: auto;
    height: 16vh;
    border-bottom: 1px solid #eee;
    border-top: 1px solid #eee;
    display: flex;
}

.table-box-item {
    flex: 1;
    overflow-y: auto;
}

.dashboard-package-table {
    width: 100%;
}

.dashboard-package-td {
    border: solid 1px #2eb1e0;
    flex: 1;
    padding: 1vh 5px 1vh 5px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.dashboard-package-tr {
    width: 100%;
    display: flex;
}

.report-root {
    width: 100%;
    height: auto;
    overflow: auto;
}

.el-table th {
    height: 32px !important;
    line-height: 32px !important;
    background: #f5f7fa !important;
}

.el-table th {
    padding: 8px 0 !important;
}


.dashboard-Feeding-line {
    width: 36vw;
    height: 20vh;
    position: absolute;
    left: 1vw;
    top: 11vh;
}

.dashboard-Feeding-formula {
    width: 36vw;
    height: 20vh;
    position: absolute;
    left: 38vw;
    top: 11vh;
}

.dashboard-Feeding-canning {
    width: 24vw;
    height: 20vh;
    position: absolute;
    left: 75vw;
    top: 11vh;
}

.dashboard-Feeding-info {
    width: 98vw;
    height: 30vh;
    position: absolute;
    left: 1vw;
    top: 31vh;
    display: flex;
}

.dashboard-Feeding-schedule {
    width: 98vw;
    height: 38vh;
    position: absolute;
    right: 1vw;
    top: 61vh;
    display: flex;
}

.dashboard-Feeding-title {
    width: 100%;
    height: 6vh;
    line-height: 6vh;
    background-image: url("../imgs/Feeding-title.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.dashboard-Feeding-title-one {
    background-image: url("../imgs/Feeding-title-one.png");
}

.Feeding-schedule-text {
    position: relative;
    width: 100%;
}

.Feeding-schedule-box {
    position: relative;
    width: 100%;
    border: 1px solid #066c92;
    background-color: rgba(255, 255, 255, 0.2);
    height: 5vh;
    overflow: hidden;
    border-radius: 5px;
}

.right-Feeding-title {
    position: absolute;
    right: 0.5vw;
    top: 0
}

.Feeding-line-item {
    width: auto;
    height: 5vh;
    line-height: 5vh;
    float: left;
}

.Feeding-canning-content {
    width: 80%;
    height: 10vh;
    margin-left: auto;
    margin-right: auto;
    background-image: url("../imgs/Feeding-canning.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
}

.Feeding-item {
    flex: 1;
}

.Feeding-item-info-title {
    width: 100%;
    height: 5vh;
    line-height: 5vh;
    background-image: url("../imgs/Feeding-item.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.Feeding-item-info-title-one {
    width: 100%;
    height: 6vh;
    background-image: url("../imgs/Feeding-item-info-title-one.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.Feeding-item-info-title-icon {
    width: 1.5vw;
    height: 1.5vw;
    background-image: url("../imgs/Feeding-icon-one.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    float: left;
}

.Feeding-item-info-title-icon1 {
    width: 1.5vw;
    height: 1.5vw;
    background-image: url("../imgs/Feeding-icon.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    float: left;
}
.Feeding-item-info-title-icon1-stop {
    width: 1.5vw;
    height: 1.5vw;
    background-image: url("../imgs/Feeding-icon-stop.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    float: left;
}

.Feeding-item-info-title-text {
    float: left;
    min-width: 2vw;
}

.Feeding-item-info-title-icon2 {
    width: 1.5vw;
    height: 1.5vw;
    display: inline-block;
    border-radius: 50%;
    overflow: hidden;
    border: 1px solid #0cb99a;
}

.Feeding-item-info-box {
    width: 100%;
    height: 5vh;
    background-image: url("../imgs/Feeding-item-info-box.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
    margin-top: 1vh;
}

.Feeding-item-info-title-two {
    width: 100%;
    height: 6vh;
    background-image: url("../imgs/Feeding-item-info-title-two.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
}

.Feeding-item-info-status {
    width: 5vw;
    height: 3vh;
    line-height: 3vh;
    display: inline-block;
    overflow: hidden;
}

.Feeding-item-info-title-tree {
    width: 100%;
    height: 4vh;
    line-height: 4vh;
    background-color: rgba(28, 116, 184, 0.2);
    border-left: 1px solid #0cb2e0;
    position: relative;
    margin-top: 1vh;
}

.Feeding-item-info-title-four {
    width: 100%;
    height: 4vh;
    line-height: 4vh;
    position: relative;
    margin-top: 1vh;
}

.status-box {
    display: inline-block;
    padding: 0.2vh 0.5vw;
    border-radius: 0.2vw;
    overflow: hidden;
    background-color: #3dcd58;
    white-space: nowrap;
}

.root {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.root-head {
    width: calc(100% - 18px);
    height: auto;
    overflow: hidden;
    /*overflow-y: auto;*/
    margin-top: 18px;
}

.root-main {
    width: calc(100% - 18px);
    overflow: auto;
}

.root-main-new {
    width: calc(100% - 18px);
    height: calc(100% - 116px);
    position: absolute;
    top: 55px;
    overflow: auto;
}

.root-main-two {
    width: calc(100% - 18px);
    height: calc(100% - 116px);
    position: absolute;
    top: 90px;
    overflow: auto;
}

.el-table__body-wrapper::-webkit-scrollbar {
    width: 12px !important;
    height: 12px !important;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: #dde;
    border-radius: 3px;
}

.el-table__body-wrapper::-webkit-scrollbar-track {
    background: white;
    border-radius: 2px;
}

.el-table th.el-table__cell > .cell {
    white-space: nowrap !important;
    overflow: initial;
}

.el-table__fixed-right {
    right: 6px !important;
    bottom: 6px !important;
}

.root-footer {
    width: calc(100% - 18px);
    height: 58px;
    overflow: hidden;
    position: absolute;
    bottom: 0;
    background-color: white;
}

.logo-head-img {
    width: 4vw;
    height: 6vh;
    background-image: url("../imgs/logo.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.package-row-img {
    width: 4vw;
    height: 4vw;
    background-image: url("../imgs/L1.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.justify-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.package-status-box {
    display: inline-block;
    width: 3vw;
    height: 3vw;
    background-image: url("../imgs/lampBox.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    vertical-align: middle;
}

.package-status-icon {
    width: 1vw;
    height: 3vh;
    background-image: url("../imgs/redlamp.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.package-status-red {
    background-image: url("../imgs/redlamp.png");
}

.package-status-yellow {
    background-image: url("../imgs/yellowlamp.png");
}

.package-status-green {
    background-image: url("../imgs/greenlamp.png");
}

.package-status-grey {
    background-image: url("../imgs/greylamp.png");
}

.package-status-blue {
    background-image: url("../imgs/bluelamp.png");
}


.package-status-text {
    width: 49%;
    background-image: url("../imgs/Feeding-item-info-box.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    float: left;
    margin-left: 1%;
    margin-bottom: 0.5vh;
    padding: 0.6vh 0;
}

.package-status-OEE {
    width: 90%;
    height: 43%;
    margin-left: auto;
    margin-right: auto;
    background-image: url("../imgs/packagestatusOEE.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    vertical-align: middle;
    position: relative;
    margin-top: 0.5vh;
}

.package-oee-text {
    padding-top: 0.2vh;
    padding-left: 0.5vw;
}

.package-oee-values {
    position: absolute;
    top: 1vh;
    left: 4vw;
}

.package-oee-sort {
    position: absolute;
    bottom: 1vh;
    right: 0.5vw
}

.package-work-box {
    width: 90%;
    height: 70%;
    margin-left: auto;
    margin-right: auto;
    background-image: url("../imgs/packagOrder.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    vertical-align: middle;
    position: relative;
    margin-top: 0.5vh;
}

.sim-left-line {
    position: fixed;
    left: 0;
    top: 8vh;
    width: 5vw;
    height: 92vh;
    overflow-y: auto;
}

.sim-left-line-background {
    background-color: #01173e;
    box-shadow: 0px 0px 0.5vw 0px rgba(255, 255, 255, 0.2);
}

.sim-line-text {

}

.sim-title-bc {
    width: 100%;
    line-height: 4vh;
    margin-left: 3vw;
    position: relative;
}

.sim-title-right {
    position: absolute;
    top: 0;
    right: 4vw;
}

.sim-plan-box {
    width: 100%;
    height: 19vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.sim-plan-content {
    width: 80%;
    height: 10vh;
    background-image: url("../imgs/jhzxxsk.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
}

.sim-line-item {
    width: 90%;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    margin-top: 1vh;
    margin-bottom: 1vh;
}

.sim-line-item-icon {
    width: 1.5vw;
    height: 1.5vw;
    border-radius: 50%;
    overflow: hidden;
}

.sim-line-item-text {
    position: absolute;
    left: 2.1vw;
    top: 0.5vh;
}
.sim-line-item-time {
    position: absolute;
    right: 0.5vw;
    top: 0.5vh;
}

.sim-plan-info {
    position: fixed;
    left: 6vw;
    top: 10vh;
    width: 28vw;
    height: 22vh;
    background-image: url("../imgs/jhzxxx.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.sim-productivity {
    position: fixed;
    left: 35vw;
    top: 10vh;
    width: 35.5vw;
    height: 22vh;
}

.sim-Plan-Execution {
    position: fixed;
    left: 6vw;
    top: 10vh;
    width: 28vw;
    height: 48vh;
    background-image: url("../imgs/PlanExecution.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
.sim4-Plan-Execution {
    position: fixed;
    left: 6vw;
    top: 10vh;
    width: 28vw;
    height: 16vh;
    border: 1px solid rgba(16, 96, 139, 0.8);
}

.sim-Material-preparation {
    position: fixed;
    left: 6vw;
    top: 60vh;
    width: 28vw;
    height: 38vh;
}

.sim-productive-power {
    position: fixed;
    left: 35vw;
    top: 34vh;
    width: 35.5vw;
    height: 32vh;
}

.sim-YOY {
    position: fixed;
    left: 35vw;
    top: 68vh;
    width: 35.5vw;
    height: 32vh;
}

.sim-Quality-Safe {
    position: fixed;
    left: 71.5vw;
    top: 10vh;
    width: 28vw;
    height: 16vh;
}

.sim-week-line {
    position: fixed;
    left: 6vw;
    top: 34vh;
    width: 28vw;
    height: 32vh;
}

.sim-equipment-management {
    position: fixed;
    left: 6vw;
    top: 68vh;
    width: 28vw;
    height: 30vh;
}

.sim-Unit-consumption {
    position: fixed;
    left: 71.5vw;
    top: 56vh;
    width: 28vw;
    height: 20vh;
}

.sim-andon {
    position: fixed;
    left: 71.5vw;
    top: 78vh;
    width: 28vw;
    height: 20vh;
    border: 1px solid rgba(16, 96, 139, 0.8);
}

.sim-Material-organization {
    position: fixed;
    left: 71.5vw;
    top: 28vh;
    width: 28vw;
    height: 26vh;
    border: 1px solid rgba(16, 96, 139, 0.8);
}
.sim-production-costs {
    position: fixed;
    left: 71.5vw;
    top: 28vh;
    width: 28vw;
    height: 26vh;
}

.sim-background-01 {
    background-image: url("../imgs/zdcxsyl.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.sim-background-02 {
    background-image: url("../imgs/jhzxxx.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.sim-equipment-item {
    width: 100%;
    height: auto;
    display: flex;
    margin-top: 3vh;
    justify-content: center;
    align-items: center;
}

.sim-equipment-box {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.sim-equipment-icon {
    width: 5vw;
    height: 5vw;
    background-image: url("../imgs/itemBOX.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
}

.sim-title {
    width: 100%;
    line-height: 4vh;
    background-image: url("../imgs/sim-tITLE.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
}

.sim-title-text {
    margin-left: 3vw;
}

.sim-title-icon {
    position: absolute;
    right: 1vw;
    top: -3.5vh;
}

.sim-productivity-item {
    width: 100%;
    height: auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

.sim-productivity-box {
    flex: 1;
    border: 1px solid #54c3ff;
    height: 16vh;
}

.sim-productivity-text {
    border: 1px solid #54c3ff;
    width: 80%;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 2vh;
    box-shadow: 0px 0px 1vw 0px rgba(255, 255, 255, 0.3);
}

.sim-Quality-img {
    height: 12vh;
    flex: 1;
    background-image: url("../imgs/qrtsk.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding: 1vh 0.5vw;
}

.sim-Quality-relative {
    height: 5vh;
    position: relative;
}

.sim-table-head {
    background-color: rgba(8, 48, 104, 0.7);
    display: flex;
}

.sim-table-item {
    flex: 1;
    text-align: center;
    padding: 1vh 0;
}

.sim-table-tbody {
    width: 100%;
    height: 17vh;
    overflow-y: auto;
}

.sim-tbody-box {
    display: flex;
}

.sim-tbody-item {
    flex: 1;
    text-align: center;
    padding: 0.5vh 0;
}

.sim-tbody-status {
    width: auto;
    background-color: rgba(221, 65, 65, 0.7);
    border-radius: 10px;
    padding: 0 1vw;
}

.sim-Unit-img {
    background-image: url("../imgs/water.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.sim-Unit-img1 {
    background-image: url("../imgs/electricity.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.sim-Unit-img2 {
    background-image: url("../imgs/gas.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.sim-Unit-flex {
    width: 85%;
    margin-left: auto;
    margin-right: auto;
    display: flex;
}

.sim-Unit-item {
    flex: 1;
    height: 16vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.sim-andon-content {
    width: 90%;
    margin-left: auto;
    margin-right: auto;
}

.sim-andon-login {
    height: 4vh;
    width: 100%;
    border: 1px solid rgba(90, 233, 251, 0.6);
    border-radius: 5px;
    overflow: hidden;
    position: relative;
    display: flex;
}
.sim-andon-bar-new {
    flex:1;
    min-width: 50px;
    height: 100%;
    background: linear-gradient(to right, rgba(189, 255, 227, 0.8), rgba(8, 205, 90, 1));
    display: flex;
    align-items: center;
    justify-content: center;
}
.sim-andon-bar-new-feeding {
    flex:1;
    min-width: 110px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.sim-andon-bar {
    width: auto;
    height: calc(100% - 4px);
    background: linear-gradient(to right, rgba(189, 255, 227, 0.8), rgba(8, 205, 90, 1));
    margin: 2px;
    display: flex;
    align-items: center;
    border-radius: 5px;
}

.background-blue-gradient {
    background: linear-gradient(to right, rgba(22, 132, 161, 1), rgba(32, 182, 233, 1));
}

.background-blue-gradient1 {
    background: linear-gradient(to right, rgba(90, 233, 251, 0.6), rgba(17, 99, 146, 0.6));
}

.background-blue-gradient2 {
    background: linear-gradient(to right, rgba(177, 172, 255, 1), rgba(62, 20, 229, 1));
}

.sim-andon-item {
    background-color: rgba(9, 42, 102, 0.4);
    padding: 0.5vh 0;
}

.sim-Plan-flex {
    width: 100%;
    height: auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

.sim-Plan-item {
    flex: 1;
}

.sim-plan-img {
    height: 9vw;
    width: 9vw;
    background-image: url("../imgs/panHxtbj.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-right: auto;
    margin-left: auto;
}

.sim-Work-order {
    width: 93%;
    margin-left: auto;
    margin-right: auto;
}

.sim-Work-order-progress {
    width: 93%;
    margin-left: auto;
    margin-right: auto;
    height: 15.5vh;
    display: flex;
    flex-direction: column;
}

.sim-progress-item {
    flex: 1;
    display: flex;
    align-items: center;
    padding-left: 1vw;
}

.sim-progress-img {
    background-image: url("../imgs/sim-progress.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.sim-stock-preparation-img {
    background-image: url("../imgs/backgound01.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.sim-Material-box {
    width: 96%;
    height: auto;
    margin-right: auto;
    margin-left: auto;
}
.sim3-production-costs{
    background-image: url("../imgs/backgound01.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
.sim-production-costs-text{
    width: 5vw;
    height: 9vh;
    background-color: rgba(35,132,211,0.15);
}
.sim3-UNIT-COST{
    width: 60%;
    margin-left: auto;
    margin-right: auto;
    border-bottom: 1px solid #ccc;
    text-align: center;
    padding-top: 1vh;
}
.sim3-UNIT-COST-num{
    width: 80%;
    margin-left: auto;
    margin-right: auto;
    background-color: rgba(9,106,195,1);
    margin-top: 1vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.5vh 0;
}
.sim4-plan{
    width: 90%;
    height: 32vh;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    flex-direction: column;
}
.sim4-plan-item{
    margin-top: 1vh;
    margin-bottom: 1vh;
    flex: 1;
    position: relative;
    line-height: 6vh;
}
.sim4-plan-img{
    background-image: url("../imgs/background2.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

@media screen and (min-width: 3800px) {
    .font-05 {
        font-size: 0.5vw;
    }

    .font-06 {
        font-size: 0.6vw;
    }

    .font-07 {
        font-size: 0.7vw;
    }

    .font-08 {
        font-size: 0.8vw;
    }

    .font-09 {
        font-size: 0.9vw;
    }

    .font-10 {
        font-size: 1vw;
    }

    .font-12 {
        font-size: 1.2vw;
    }

    .font-14 {
        font-size: 1.4vw;
    }

    .font-16 {
        font-size: 1.6vw;
    }

    .font-20 {
        font-size: 2vw;
    }

    .font-30 {
        font-size: 3vw;
    }

    .font-50 {
        font-size: 5vw;
    }
}
@media screen and (max-width: 2000px) {
    .font-M05 {
        font-size: 0.5rem;
    }

    .font-M06 {
        font-size: 0.6rem;
    }

    .font-M07 {
        font-size: 0.7rem;
    }

    .font-M08 {
        font-size: 0.8rem;
    }

    .font-M09 {
        font-size: 0.9rem;
    }

    .font-M10 {
        font-size: 1rem;
    }

    .font-M12 {
        font-size: 1.2rem;
    }

    .font-M14 {
        font-size: 1.4rem;
    }

    .font-M16 {
        font-size: 1.6rem;
    }

    .font-M20 {
        font-size: 2rem;
    }

    .font-M30 {
        font-size: 3rem;
    }

    .font-M50 {
        font-size: 5rem;
    }
}
.scrollbar-warp {
    height: 40px;
    line-height: 40px;
    width: 100%;
    margin: 0 auto;
    overflow: hidden;
}
.el-radio-button__orig-radio:checked + .el-radio-button__inner{
    background-color: #3dcd58!important;
    border-color: #3dcd58!important;
    box-shadow: -1px 0 0 0 #3dcd58!important;
}

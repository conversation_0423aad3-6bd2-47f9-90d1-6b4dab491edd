export const MyWorkorderColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
}, {
    text: '维修单号',
    value: 'RepairWo',
    Namevalue: "wxdh",
    width: 150,
    sortable: true
}, {
    text: '产线名称',
    value: 'LineCode',
    Namevalue: "LineCode",
    width: 150,
    sortable: true
},{
    text: '设备号',
    value: 'DeviceCode',
    Namevalue: "sbh",
    width: 150,
    sortable: true
}, {
    text: '设备名称',
    value: 'DeviceName',
    Namevalue: "sbmc",
    width: 150,
    sortable: true
},{
    text: '报修内容',
    value: 'Description',
    Namevalue: "bxlr",
    width: 150,
    sortable: true
},{
    text: '故障现象',
    value: 'Phenomenon',
    Namevalue: "gzxx",
    width: 150,
    sortable: true
}, {
    text: '状态',
    value: 'Status',
    Namevalue: "zt",
    width: 100,
    sortable: true
},  {
    text: '状态',
    value: 'RecordStatus',
    Namevalue: "RecodeStatus",
    width: 150,
    sortable: true
}, {
    text: '紧急度',
    value: 'Urgency',
    Namevalue: "jjd",
    width: 100,
    sortable: true
}, {
    text: '接单人',
    value: 'ReceiveByName',
    Namevalue: "jdr",
    width: 100,
    sortable: true
}, {
    text: '计划开始时间',
    value: 'PlanStartDate',
    Namevalue: "jhkssj",
    width: 150,
    sortable: true
}, {
    text: '计划完成时间',
    value: 'PlanFinishDate',
    Namevalue: "jhwcsj",
    width: 150,
    sortable: true
},{
    text: '是否停机',
    value: 'IsStop',
    Namevalue: "sftj",
    width: 100,
    sortable: true
}, {
    text: '工单类型',
    value: 'Type',
    Namevalue: "gdlx",
    width: 100,
    sortable: true
}, {
    text: '工单来源',
    value: 'Source',
    Namevalue: "gdly",
    width: 100,
    sortable: true
}, {
    text: '关联单号',
    value: 'ReferOrderNo',
    Namevalue: "gldh",
    width: 100,
    sortable: true
},
//  {
//     text: '指定完成日期',
//     value: 'FinishDate',
//     Namevalue: "zdwcrq",
//     width: 100,
//     sortable: true
// }, 
{
    text: '当值主管',
    value: 'DutyManagerName',
    Namevalue: "dbzg",
    width: 100,
    sortable: true
}, {
    text: '报修人',
    value: 'ReportByName',
    Namevalue: "bxr",
    width: 100,
    sortable: true
}, {
    text: '报修时间',
    value: 'ReportDate',
    Namevalue: "bxsj",
    width: 150,
    sortable: true
}, {
    text: '指派人',
    value: 'RepairManager',
    Namevalue: "zpr",
    width: 100,
    sortable: true
}, {
    text: '开始处理时间',
    value: 'StartDate',
    Namevalue: "kscl",
    width: 100,
    sortable: true
}, {
    text: '结束处理时间',
    value: 'FinishDate',
    Namevalue: "ksjs",
    width: 100,
    sortable: true
}, {
    text: '维修时长（h）',
    value: 'RepairDuration',
    Namevalue: "wxsc",
    width: 100,
    sortable: true
}, 
// {
//     text: '系统分类',
//     value: 'FaultCategory',
//     Namevalue: "xtfl",
//     width: 100,
//     sortable: true
// }, {
//     text: '原因分类',
//     value: 'ReasonCategory',
//     Namevalue: "yyfl",
//     width: 100,
//     sortable: true
// }, 

{
    text: '维修过程描述',
    value: 'RepairRecordDesc',
    Namevalue: "wxgcms",
    width: 100,
    sortable: true
}, {
    text: '原因分析',
    value: 'Reason',
    Namevalue: "yyfx",
    width: 100,
    sortable: true
}, {
    text: '原因分析结论',
    value: 'ReasonResult',
    Namevalue: "yyfxjl",
    width: 100,
    sortable: true
}, {
    text: '维修性质',
    value: 'RepairNature',
    Namevalue: "wxxz",
    width: 100,
    sortable: true
}, {
    text: '故障性质',
    value: 'FaultNature',
    Namevalue: "gzxz",
    width: 100,
    sortable: true
}, {
    text: '故障部位',
    value: 'FaultCategory',
    Namevalue: "gzbw",
    width: 100,
    sortable: true
}, {
    text: '确认人',
    value: 'ConfirmByName',
    Namevalue: "qrr",
    width: 100,
    sortable: true
}, {
    text: '操作',
    value: 'actions',
    Namevalue: "action",
    width: 250,
    sortable: true
}, ]
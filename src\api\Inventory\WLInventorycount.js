import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Inventory'
const baseURL2 = 'baseURL_MATERIAL'

export function GetPageList(data) {
    const api = '/api/VerifiyDetail/GetPageList_WL'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetList(data) {
    const api = '/api/VerifiyDetail/GetWL_DetailByID'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetReturnList(data) {
    const api = '/api/VerifiyDetail/GetReturnWL_DetailByID'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetTotalist(data) {
    const api = '/api/VerifiyDetail/GetReturn_DetailTotal'
    return getRequestResources(baseURL, api, 'post', data);
}
export function AddDetail_YL(data) {
    const api = '/api/VerifiyDetail/AddDetail_WL'
    return getRequestResources(baseURL, api, 'post', data);
}
export function ModifyDetail_YL(data) {
    const api = '/api/VerifiyDetail/ModifyDetail_WL'
    return getRequestResources(baseURL, api, 'post', data);
}
export function UpInVentAll(data) {
    const api = '/api/VerifiyDetail/UpInVentAll'
    return getRequestResources(baseURL, api, 'post', data);
}
export function SapReportWork(data) {
    const api = '/api/VerifiyDetail/SapReportWork'
    return getRequestResources(baseURL, api, 'post', data);
}
export function Add_YL(data) {
    const api = '/api/VerifiyDetail/Add_WL'
    return getRequestResources(baseURL, api, 'post', data);
}
export function Delete_YL(data) {
    const api = '/api/VerifiyDetail/Delete_WL'
    return getRequestResources(baseURL, api, 'post', data);
}
export function ModifyDetailQS_YL(data) {
    const api = '/api/VerifiyDetail/ModifyDetailQS_WL'
    return getRequestResources(baseURL, api, 'post', data);
}

export function NewAddDetail_YL(data) {
    const api = '/api/VerifiyDetail/NewAddDetail_WL'
    return getRequestResources(baseURL, api, 'post', data);
}

export function CherkReturnData(data) {
    const api = '/api/Container/CherkReturnDataByVeriyDetail';
    return getRequestResources(baseURL, api, 'post', data);
}
export function ReturnInventoryALL(data) {
    const api = '/api/Container/ReturnInventoryALLVeriyDetail';
    return getRequestResources(baseURL, api, 'post', data);
}
export function ReInspection(data) {
    const api = '/api/VerifiyDetail/ReInspection'
    return getRequestResources(baseURL, api, 'post', data);
}
export function TK_ChangeState(data) {
    const api = '/api/VerifiyDetail/TK_ChangeState'
    return getRequestResources(baseURL, api, 'post', data);
}
export function ChangeRemark(data) {
    const api = '/api/VerifiyDetail/ChangeRemark'
    return getRequestResources(baseURL, api, 'post', data, true);
}
export function ChangeRead(data) {
    const api = '/api/VerifiyDetail/ChangeRead'
    return getRequestResources(baseURL, api, 'post', data, true);
}
// export function Reverse(data) {
//     const api = '/api/ProductionSummaryDetailsView/Reverse'
//     return getRequestResources(baseURL, api, 'post', data);
// }
// export function GetReasonCode(data) {
//     const api = '/api/Dataitemdetail/GetReasonCode'
//     return getRequestResources(baseURL, api, 'post', data);
// }
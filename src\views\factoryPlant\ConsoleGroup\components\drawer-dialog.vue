<template>
  <el-dialog :title="dialogForm.ID ? $t('GLOBAL._BJ'): $t('GLOBAL._XZ')" :visible.sync="dialogVisible" width="600px"
    :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
    @close="dialogVisible = false">
    <el-form  ref="dialogForm" :model="dialogForm" label-width="110px">
    <el-form-item label="Equipment">
      <el-select style="width: 100%" filterable v-model="dialogForm.EquipmentRowId" placeholder="">
        <el-option v-for="(item, index) in EquipmentList" :key="index" :label="item.EquipmentName" :value="item.EquipmentId">
        </el-option>
      </el-select>
    </el-form-item>
      <el-form-item label="Sort">
        <el-input-number style="width: 100%;"  v-model="dialogForm.SortOrder" :min="0" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogVisible = false">{{$t('GLOBAL._QX')}}</el-button>
      <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small"
        @click="submit()">{{ $t('GLOBAL._QD') }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  addEquipmentGroup,
  addEquipmentGroupEquip,
  addLabelCountry,
  addLabelPrinterSize,
  getEquipmentAllList,
  getEquipmentGroupDetail,
  getEquipmentGroupEquipDetail,
  getEquipmentLineList,
  getGetEquipmentAndLineList,
  getLabelPrinterSizeDetail
} from "@/api/systemManagement/labelPrint";
import {GetDFMYesNo} from "@/api/systemManagement/dataDictionary";

export default {
  name: 'Add',
  data() {
    return {
      dialogForm: {},
      dialogVisible: false,
      formLoading: false,
      TypeList:[],
      EquipmentList:[],
    }
  },
  mounted() {
  },
  methods: {
    submit() {
      addEquipmentGroupEquip(this.dialogForm).then(res => {
      const statusValue=res.success?'success':'error'
      this.$message[statusValue](res.msg)
        this.$emit('saveForm')
        this.dialogVisible = false
      })
    },
    show(data) {
      this.dialogVisible = true
      this.$nextTick(_ => {
        console.log(data)
        this.dialogForm = {
          EquipmentGroupRowId:data.ID ||''
        }
        // if(data.ID)this.getDialogDetail(data.ID)
      })
      this.getEquipmentAllList()
    },
    getDialogDetail(id){
      getEquipmentGroupEquipDetail(id).then(res => {
        this.dialogForm = res.response
      })
    },
    getTypeList() {
      GetDFMYesNo({ItemCode: 'EquipmentGroupType'}).then(res => {
        this.TypeList = res.response
      })
    },
    getEquipmentAllList() {
      getGetEquipmentAndLineList().then(res => {
        this.EquipmentList = res.response
      })
    },
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-input-number__increase{
    top: 4px;
    width: 40px;
    height: 30px;
    line-height: 30px;
  }
::v-deep .el-input-number__decrease{
  top: 4px;
  width: 40px;
  height: 30px;
  line-height: 30px;
}
</style>

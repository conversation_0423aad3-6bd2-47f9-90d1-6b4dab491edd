<template>
    <el-dialog :title="dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')" :visible.sync="dialogVisible" width="800px"
      :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
      @close="dialogVisible = false">
      <el-form ref="dialogForm" :model="dialogForm" label-width="130px">

          <el-col :lg="12">
            <el-form-item label="工厂" prop="factory">
              <el-select v-model="dialogForm.Factory" placeholder="请选择工厂" clearable style="width:100%">
                <el-option v-for="item in factoryOptions" :key="item.ItemValue" :label="item.ItemName" :value="item.ItemValue" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="车间" prop="Workshop">
              <el-select v-model="dialogForm.Workshop" placeholder="请选择车间" clearable style="width:100%">
                <el-option v-for="item in workshopOptions" :key="item.ItemValue" :label="item.ItemName" :value="item.ItemValue" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="产线代码" prop="LineCode">
              <el-select filterable clearable style="width: 100%" v-model="dialogForm.LindCode" placeholder="请选择产线" @change="setFormLineName">
                    <el-option v-for="(item, index) in lineOptions" :key="index" :label="item.EquipmentName" :value="item.EquipmentCode">
                    </el-option>
                </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="计划分类" prop="Category">
              <el-select filterable clearable style="width: 100%" v-model="dialogForm.Category" placeholder="请选择计划分类" >
                <el-option v-for="item in categoryOptions" :key="item.ItemValue" :label="item.ItemName" :value="item.ItemValue" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="物料代码" prop="MaterialCode">
              <div>
                <el-button icon="el-icon-plus" type="text" @click="openMaterialTable">{{ $t('GLOBAL._CX') }}</el-button>
              </div>
              <div>
                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}
              </div>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="包装规格" prop="PackSize">
              <el-input v-model="dialogForm.PackSize" placeholder="请输入包装规格" />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="设计代码" prop="DesignCode">
              <el-input v-model="dialogForm.DesignCode" placeholder="请输入设计代码" />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="单位" prop="Unit">
              <el-input v-model="dialogForm.Unit" placeholder="请输入单位" />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="输出" prop="Output">
              <el-input v-model="dialogForm.Output" placeholder="请输入输出" />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="开始工作日" prop="StartWorkday">
              <el-date-picker v-model="dialogForm.StartWorkday" type="datetime" placeholder="选择日期时间"></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="开始班次" prop="StartShift">
              <el-select filterable clearable style="width: 100%" v-model="dialogForm.StartShift" placeholder="请选择开始班次">
                <el-option v-for="item in shiftOptions" :key="item.ItemValue" :label="item.ItemName" :value="item.ItemValue" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :lg="12">
            <el-form-item label="结束工作日" prop="FinishWorkday">
              <el-date-picker v-model="dialogForm.FinishWorkday" type="datetime" placeholder="选择日期时间"></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="结束班次" prop="FinishShift">
              <el-select filterable clearable style="width: 100%" v-model="dialogForm.FinishShift" placeholder="请选择结束班次">
                <el-option v-for="item in shiftOptions" :key="item.ItemValue" :label="item.ItemName" :value="item.ItemValue" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="计划数量" prop="PlanQuantity">
              <el-input v-model="dialogForm.PlanQuantity" placeholder="请输入计划数量" />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="计划编号" prop="OrderNo">
              <el-input v-model="dialogForm.OrderNo" placeholder="请输入计划编号" />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="计划类型" prop="Type">
              <el-select filterable clearable style="width: 100%" v-model="dialogForm.Type" placeholder="请选择计划类型">
                <el-option v-for="item in typeOptions" :key="item.ItemValue" :label="item.ItemName" :value="item.ItemValue" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="dialogForm.Remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small" @click="submit()">确定</el-button>
      </div>
      <material-table :is-id="false" ref="materialTable" @saveForm="setMaterial"></material-table>
    </el-dialog>
  </template>
  

<script>
  import {
    getWeekScheduleDetail,
    saveWeekScheduleForm,
    getLineList
  } from "@/api/planManagement/weekSchedule";
  import MaterialTable from '@/components/MaterialTable.vue';
  export default {
    components:{
      MaterialTable
    },
    data() {
      return {
        dialogForm: {},
        dialogVisible: false,
        formLoading: false,
        factoryOptions: [],
        workshopOptions: [],
        lineOptions: [],        
        categoryOptions: [],
        shiftOptions: [],
        typeOptions: [],
        currentRow: {},
        matInfo:{}
      }
    },
    created() {
      this.initDictList();
      this.getLineList();
    },
    mounted() {
    },
    methods: {
      async initDictList(){
        this.factoryOptions = await this.$getDataDictionary('WeekScheduleFactory');
        this.workshopOptions = await this.$getDataDictionary('WeekScheduleWorkshop');
        this.categoryOptions = await this.$getDataDictionary('WeekScheduleCategory');
        this.shiftOptions = await this.$getDataDictionary('WeekScheduleShift');
        this.typeOptions = await this.$getDataDictionary('WeekScheduleType');
      },
      async getLineList() {
        const { response } = await getLineList({
         //areaCode: 'PackingArea'
         areaCode: 'Formulation'
        })
        console.log(response)
        this.lineOptions = response
      },
      submit() {
        saveWeekScheduleForm(this.dialogForm).then(res=>{
          this.$message.success(res.msg)
          this.$emit('saveForm')
          this.dialogVisible = false
        })
      },
      show(data) {
        this.dialogForm = {}
        this.currentRow = data
        this.dialogVisible = true
        this.$nextTick(_ => {
          if(data.ID){
            this.getDialogDetail(data.ID)
          }
        })
      },
      getDialogDetail(id){
        getWeekScheduleDetail(id).then(res => {
          this.dialogForm = res.response
        })
      },
      setFormLineName(EquipmentCode) {
        console.log(EquipmentCode);
        console.log(this.lineOptions.find(e => e.EquipmentCode === EquipmentCode));
        this.dialogForm.LineId = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).ID
        //this.dialogForm.LineName = this.lineOptions.find(e => e.ID === id).EquipmentName
        this.dialogForm.LineCode = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).EquipmentCode
        console.log(this.dialogForm);
      },
      setMaterial(val){
        // console.log("setMaterial")
        // console.log(val)        
        this.dialogForm.MaterialId = val.ID
        this.dialogForm.MaterialCode = val.Code
        this.dialogForm.MaterialName = val.NAME
        this.$forceUpdate()
        // this.matInfo = val        
        // console.log(this.dialogForm.MaterialCode)
        // console.log(this.dialogForm.MaterialName)
      },
      openMaterialTable(){
        this.$refs['materialTable'].show()
      }
    }
  }
  </script>
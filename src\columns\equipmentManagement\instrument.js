export const instrumentColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
}, {
    text: '编号',
    value: 'MeasureNo',
    Namevalue: "MeasureNo",
    width: 150,
    sortable: true
}, {
    text: '资产码',
    value: 'AccountAssetNo',
    Namevalue: "AccountAssetNo",
    width: 150,
    sortable: true
},{
    text: '设备名称',
    value: 'Name',
    Namevalue: "sbmc",
    width: 150,
    sortable: true
}, {
    text: '型号',
    value: 'Model',
    Namevalue: "xh",
    width: 100,
    sortable: true
}, {
    text: '规格',
    value: 'Spec',
    Namevalue: "gg",
    width: 100,
    sortable: true
}, {
    text: '状态',
    value: 'Status',
    Namevalue: "zt",
    width: 120,
    sortable: true
}, {
    text: '检验状态',
    value: 'AdjustmentStatus',
    Namevalue: "jyzt",
    width: 120,
    sortable: true
}, {
    text: 'ABC分类',
    value: 'Type',
    Namevalue: "abcfl",
    width: 150,
    sortable: true
}, {
    text: '类别',
    value: 'Category',
    Namevalue: "lb",
    width: 120,
    sortable: true
}, {
    text: '有效期',
    value: 'ExpirationDate',
    Namevalue: "yxq",
    width: 150,
    sortable: true
}, {
    text: '测量范围',
    value: 'MeasureRange',
    Namevalue: "clfw",
    width: 150,
    sortable: true
}, {
    text: '等级',
    value: 'Grade',
    Namevalue: "dj",
    width: 120,
    sortable: true
}, {
    text: '精度',
    value: 'Precision',
    Namevalue: "jd",
    width: 120,
    sortable: true
}, {
    text: 'd实际分度值',
    value: 'DActualScaleInterval',
    Namevalue: "dsjfdz",
    width: 150,
    sortable: true
}, {
    text: 'e检定分度值',
    value: 'EVerifyScaleInterval',
    Namevalue: "ejdfdz",
    width: 150,
    sortable: true
}, {
    text: 'K系数',
    value: 'KCoefficient',
    Namevalue: "kxs",
    width: 120,
    sortable: true
}, {
    text: '允差',
    value: 'Tolerance',
    Namevalue: "yc",
    width: 100,
    sortable: true
}, {
    text: '制造厂家名称',
    value: 'Manufacturer',
    Namevalue: "zzcjmc",
    width: 150,
    sortable: true
}, {
    text: '出厂日期',
    value: 'ManufactureDate',
    Namevalue: "ccrq",
    width: 120,
    sortable: true
}, {
    text: '出厂编号',
    value: 'FactoryNo',
    Namevalue: "ccbh",
    width: 120,
    sortable: true
}, {
    text: '旧编号',
    value: 'OldMeasureNo',
    Namevalue: "jbh",
    width: 150,
    sortable: true
}, {
    text: '申请检点日期',
    value: 'RequestVerifyDate',
    Namevalue: "sqjdrq",
    width: 150,
    sortable: true
}, {
    text: '检验周期',
    value: 'VerifyCycle',
    Namevalue: "jyzq",
    width: 120,
    sortable: true
}, {
    text: '检定方法',
    value: 'VerifyMethod',
    Namevalue: "jdff",
    width: 120,
    sortable: true
}, {
    text: '检验日期',
    value: 'CalibrateDate',
    Namevalue: "jyrq",
    width: 150,
    sortable: true
}, {
    text: '检定部门',
    value: 'VerifyDepartment',
    Namevalue: "jdbm",
    width: 150,
    sortable: true
}, {
    text: '使用部门',
    value: 'UsingDepartment',
    Namevalue: "sybm",
    width: 150,
    sortable: true
}, {
    text: '分部门',
    value: 'SubDepartment',
    Namevalue: "fbm",
    width: 150,
    sortable: true
}, {
    text: '放置地点',
    value: 'StorageLocation',
    Namevalue: "fzdd",
    width: 150,
    sortable: true
}, {
    text: '器具管理人员',
    value: 'Manager',
    Namevalue: "qjglry",
    width: 150,
    sortable: true
}, {
    text: '有账资产码',
    value: 'AccountAssetNo',
    Namevalue: "yzzcm",
    width: 150,
    sortable: true
}, {
    text: '无账资产码',
    value: 'NonAccountAssetNo',
    Namevalue: "wzzcm",
    width: 150,
    sortable: true
}, {
    text: '磁吸码',
    value: 'MagneticCode',
    Namevalue: "cxm",
    width: 100,
    sortable: true
}, {
    text: '备注',
    value: 'Remark',
    Namevalue: "bc",
    width: 150,
    sortable: true
}, {
    text: '操作',
    value: 'actions',
    Namevalue: "actions",
    width: 150,
    sortable: true
}];

export const instrumentColum2 = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
}, {
    text: '检验日期',
    value: 'CalibrateDate',
    Namevalue: "jyrq",
    width: 150,
    sortable: true
}, {
    text: '检验人',
    value: 'CalibrateBy',
    Namevalue: "jyr",
    width: 150,
    sortable: true
}, {
    text: '有效期',
    value: 'NewExpirationDate',
    Namevalue: "yxq",
    width: 150,
    sortable: true
}, {
    text: '合格证号',
    value: 'CertificateNo',
    Namevalue: "hgzh",
    width: 150,
    sortable: true
}, {
    text: '文件',
    value: 'Filename',
    Namevalue: "file",
    width: 200,
    sortable: true
}, {
    text: '操作',
    value: 'actions',
    Namevalue: "actions",
    width: 150,
    sortable: true
}]
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_ORDER'


/**
 * 标准时间批量分页查询
 * @param {查询条件} data
 */
export function getStandardPeriodLotList(data) {
    const api = '/ppm/StandardPeriodLot/GetPageList'
    return getRequestResources(baseURL, api, 'post', data)
}


/**
 * 保存标准时间批量
 * @param data
 */
export function saveStandardPeriodLotForm(data) {
    const api = '/ppm/StandardPeriodLot/SaveForm'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 获取标准时间批量详情
 * @param {Id}
 */
export function getStandardPeriodLotDetail(id) {
    const api = '/ppm/StandardPeriodLot/GetEntity/'+id;
    return getRequestResources(baseURL, api, 'get')
}

/**
 * 删除标准时间批量
 * @param {主键} data
 */
export function delStandardPeriodLot(data) {
    const api = '/ppm/StandardPeriodLot/Delete'
    return getRequestResources(baseURL, api, 'post', data)
}

// 产线
export function getLineList(data) {
    const api = `/ppm/StandardPeriodLot/GetLineList?areaCode=${data.areaCode}`
    return getRequestResources(baseURL, api, 'post', null);
}



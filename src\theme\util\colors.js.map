{"version": 3, "sources": ["../../src/util/colors.ts"], "names": [], "mappings": ";;;;;;AAAA,IAAM,GAAG,GAAG,MAAM,CAAC,MAAP,CAAc;AACxB,EAAA,IAAI,EAAE,SADkB;AAExB,EAAA,QAAQ,EAAE,SAFc;AAGxB,EAAA,QAAQ,EAAE,SAHc;AAIxB,EAAA,QAAQ,EAAE,SAJc;AAKxB,EAAA,QAAQ,EAAE,SALc;AAMxB,EAAA,QAAQ,EAAE,SANc;AAOxB,EAAA,OAAO,EAAE,SAPe;AAQxB,EAAA,OAAO,EAAE,SARe;AASxB,EAAA,OAAO,EAAE,SATe;AAUxB,EAAA,OAAO,EAAE,SAVe;AAWxB,EAAA,OAAO,EAAE,SAXe;AAYxB,EAAA,OAAO,EAAE,SAZe;AAaxB,EAAA,OAAO,EAAE,SAbe;AAcxB,EAAA,OAAO,EAAE;AAde,CAAd,CAAZ;AAiBA,IAAM,IAAI,GAAG,MAAM,CAAC,MAAP,CAAc;AACzB,EAAA,IAAI,EAAE,SADmB;AAEzB,EAAA,QAAQ,EAAE,SAFe;AAGzB,EAAA,QAAQ,EAAE,SAHe;AAIzB,EAAA,QAAQ,EAAE,SAJe;AAKzB,EAAA,QAAQ,EAAE,SALe;AAMzB,EAAA,QAAQ,EAAE,SANe;AAOzB,EAAA,OAAO,EAAE,SAPgB;AAQzB,EAAA,OAAO,EAAE,SARgB;AASzB,EAAA,OAAO,EAAE,SATgB;AAUzB,EAAA,OAAO,EAAE,SAVgB;AAWzB,EAAA,OAAO,EAAE,SAXgB;AAYzB,EAAA,OAAO,EAAE,SAZgB;AAazB,EAAA,OAAO,EAAE,SAbgB;AAczB,EAAA,OAAO,EAAE;AAdgB,CAAd,CAAb;AAiBA,IAAM,MAAM,GAAG,MAAM,CAAC,MAAP,CAAc;AAC3B,EAAA,IAAI,EAAE,SADqB;AAE3B,EAAA,QAAQ,EAAE,SAFiB;AAG3B,EAAA,QAAQ,EAAE,SAHiB;AAI3B,EAAA,QAAQ,EAAE,SAJiB;AAK3B,EAAA,QAAQ,EAAE,SALiB;AAM3B,EAAA,QAAQ,EAAE,SANiB;AAO3B,EAAA,OAAO,EAAE,SAPkB;AAQ3B,EAAA,OAAO,EAAE,SARkB;AAS3B,EAAA,OAAO,EAAE,SATkB;AAU3B,EAAA,OAAO,EAAE,SAVkB;AAW3B,EAAA,OAAO,EAAE,SAXkB;AAY3B,EAAA,OAAO,EAAE,SAZkB;AAa3B,EAAA,OAAO,EAAE,SAbkB;AAc3B,EAAA,OAAO,EAAE;AAdkB,CAAd,CAAf;AAiBA,IAAM,UAAU,GAAG,MAAM,CAAC,MAAP,CAAc;AAC/B,EAAA,IAAI,EAAE,SADyB;AAE/B,EAAA,QAAQ,EAAE,SAFqB;AAG/B,EAAA,QAAQ,EAAE,SAHqB;AAI/B,EAAA,QAAQ,EAAE,SAJqB;AAK/B,EAAA,QAAQ,EAAE,SALqB;AAM/B,EAAA,QAAQ,EAAE,SANqB;AAO/B,EAAA,OAAO,EAAE,SAPsB;AAQ/B,EAAA,OAAO,EAAE,SARsB;AAS/B,EAAA,OAAO,EAAE,SATsB;AAU/B,EAAA,OAAO,EAAE,SAVsB;AAW/B,EAAA,OAAO,EAAE,SAXsB;AAY/B,EAAA,OAAO,EAAE,SAZsB;AAa/B,EAAA,OAAO,EAAE,SAbsB;AAc/B,EAAA,OAAO,EAAE;AAdsB,CAAd,CAAnB;AAiBA,IAAM,MAAM,GAAG,MAAM,CAAC,MAAP,CAAc;AAC3B,EAAA,IAAI,EAAE,SADqB;AAE3B,EAAA,QAAQ,EAAE,SAFiB;AAG3B,EAAA,QAAQ,EAAE,SAHiB;AAI3B,EAAA,QAAQ,EAAE,SAJiB;AAK3B,EAAA,QAAQ,EAAE,SALiB;AAM3B,EAAA,QAAQ,EAAE,SANiB;AAO3B,EAAA,OAAO,EAAE,SAPkB;AAQ3B,EAAA,OAAO,EAAE,SARkB;AAS3B,EAAA,OAAO,EAAE,SATkB;AAU3B,EAAA,OAAO,EAAE,SAVkB;AAW3B,EAAA,OAAO,EAAE,SAXkB;AAY3B,EAAA,OAAO,EAAE,SAZkB;AAa3B,EAAA,OAAO,EAAE,SAbkB;AAc3B,EAAA,OAAO,EAAE;AAdkB,CAAd,CAAf;AAiBA,IAAM,IAAI,GAAG,MAAM,CAAC,MAAP,CAAc;AACzB,EAAA,IAAI,EAAE,SADmB;AAEzB,EAAA,QAAQ,EAAE,SAFe;AAGzB,EAAA,QAAQ,EAAE,SAHe;AAIzB,EAAA,QAAQ,EAAE,SAJe;AAKzB,EAAA,QAAQ,EAAE,SALe;AAMzB,EAAA,QAAQ,EAAE,SANe;AAOzB,EAAA,OAAO,EAAE,SAPgB;AAQzB,EAAA,OAAO,EAAE,SARgB;AASzB,EAAA,OAAO,EAAE,SATgB;AAUzB,EAAA,OAAO,EAAE,SAVgB;AAWzB,EAAA,OAAO,EAAE,SAXgB;AAYzB,EAAA,OAAO,EAAE,SAZgB;AAazB,EAAA,OAAO,EAAE,SAbgB;AAczB,EAAA,OAAO,EAAE;AAdgB,CAAd,CAAb;AAiBA,IAAM,SAAS,GAAG,MAAM,CAAC,MAAP,CAAc;AAC9B,EAAA,IAAI,EAAE,SADwB;AAE9B,EAAA,QAAQ,EAAE,SAFoB;AAG9B,EAAA,QAAQ,EAAE,SAHoB;AAI9B,EAAA,QAAQ,EAAE,SAJoB;AAK9B,EAAA,QAAQ,EAAE,SALoB;AAM9B,EAAA,QAAQ,EAAE,SANoB;AAO9B,EAAA,OAAO,EAAE,SAPqB;AAQ9B,EAAA,OAAO,EAAE,SARqB;AAS9B,EAAA,OAAO,EAAE,SATqB;AAU9B,EAAA,OAAO,EAAE,SAVqB;AAW9B,EAAA,OAAO,EAAE,SAXqB;AAY9B,EAAA,OAAO,EAAE,SAZqB;AAa9B,EAAA,OAAO,EAAE,SAbqB;AAc9B,EAAA,OAAO,EAAE;AAdqB,CAAd,CAAlB;AAiBA,IAAM,IAAI,GAAG,MAAM,CAAC,MAAP,CAAc;AACzB,EAAA,IAAI,EAAE,SADmB;AAEzB,EAAA,QAAQ,EAAE,SAFe;AAGzB,EAAA,QAAQ,EAAE,SAHe;AAIzB,EAAA,QAAQ,EAAE,SAJe;AAKzB,EAAA,QAAQ,EAAE,SALe;AAMzB,EAAA,QAAQ,EAAE,SANe;AAOzB,EAAA,OAAO,EAAE,SAPgB;AAQzB,EAAA,OAAO,EAAE,SARgB;AASzB,EAAA,OAAO,EAAE,SATgB;AAUzB,EAAA,OAAO,EAAE,SAVgB;AAWzB,EAAA,OAAO,EAAE,SAXgB;AAYzB,EAAA,OAAO,EAAE,SAZgB;AAazB,EAAA,OAAO,EAAE,SAbgB;AAczB,EAAA,OAAO,EAAE;AAdgB,CAAd,CAAb;AAiBA,IAAM,IAAI,GAAG,MAAM,CAAC,MAAP,CAAc;AACzB,EAAA,IAAI,EAAE,SADmB;AAEzB,EAAA,QAAQ,EAAE,SAFe;AAGzB,EAAA,QAAQ,EAAE,SAHe;AAIzB,EAAA,QAAQ,EAAE,SAJe;AAKzB,EAAA,QAAQ,EAAE,SALe;AAMzB,EAAA,QAAQ,EAAE,SANe;AAOzB,EAAA,OAAO,EAAE,SAPgB;AAQzB,EAAA,OAAO,EAAE,SARgB;AASzB,EAAA,OAAO,EAAE,SATgB;AAUzB,EAAA,OAAO,EAAE,SAVgB;AAWzB,EAAA,OAAO,EAAE,SAXgB;AAYzB,EAAA,OAAO,EAAE,SAZgB;AAazB,EAAA,OAAO,EAAE,SAbgB;AAczB,EAAA,OAAO,EAAE;AAdgB,CAAd,CAAb;AAiBA,IAAM,KAAK,GAAG,MAAM,CAAC,MAAP,CAAc;AAC1B,EAAA,IAAI,EAAE,SADoB;AAE1B,EAAA,QAAQ,EAAE,SAFgB;AAG1B,EAAA,QAAQ,EAAE,SAHgB;AAI1B,EAAA,QAAQ,EAAE,SAJgB;AAK1B,EAAA,QAAQ,EAAE,SALgB;AAM1B,EAAA,QAAQ,EAAE,SANgB;AAO1B,EAAA,OAAO,EAAE,SAPiB;AAQ1B,EAAA,OAAO,EAAE,SARiB;AAS1B,EAAA,OAAO,EAAE,SATiB;AAU1B,EAAA,OAAO,EAAE,SAViB;AAW1B,EAAA,OAAO,EAAE,SAXiB;AAY1B,EAAA,OAAO,EAAE,SAZiB;AAa1B,EAAA,OAAO,EAAE,SAbiB;AAc1B,EAAA,OAAO,EAAE;AAdiB,CAAd,CAAd;AAiBA,IAAM,UAAU,GAAG,MAAM,CAAC,MAAP,CAAc;AAC/B,EAAA,IAAI,EAAE,SADyB;AAE/B,EAAA,QAAQ,EAAE,SAFqB;AAG/B,EAAA,QAAQ,EAAE,SAHqB;AAI/B,EAAA,QAAQ,EAAE,SAJqB;AAK/B,EAAA,QAAQ,EAAE,SALqB;AAM/B,EAAA,QAAQ,EAAE,SANqB;AAO/B,EAAA,OAAO,EAAE,SAPsB;AAQ/B,EAAA,OAAO,EAAE,SARsB;AAS/B,EAAA,OAAO,EAAE,SATsB;AAU/B,EAAA,OAAO,EAAE,SAVsB;AAW/B,EAAA,OAAO,EAAE,SAXsB;AAY/B,EAAA,OAAO,EAAE,SAZsB;AAa/B,EAAA,OAAO,EAAE,SAbsB;AAc/B,EAAA,OAAO,EAAE;AAdsB,CAAd,CAAnB;AAiBA,IAAM,IAAI,GAAG,MAAM,CAAC,MAAP,CAAc;AACzB,EAAA,IAAI,EAAE,SADmB;AAEzB,EAAA,QAAQ,EAAE,SAFe;AAGzB,EAAA,QAAQ,EAAE,SAHe;AAIzB,EAAA,QAAQ,EAAE,SAJe;AAKzB,EAAA,QAAQ,EAAE,SALe;AAMzB,EAAA,QAAQ,EAAE,SANe;AAOzB,EAAA,OAAO,EAAE,SAPgB;AAQzB,EAAA,OAAO,EAAE,SARgB;AASzB,EAAA,OAAO,EAAE,SATgB;AAUzB,EAAA,OAAO,EAAE,SAVgB;AAWzB,EAAA,OAAO,EAAE,SAXgB;AAYzB,EAAA,OAAO,EAAE,SAZgB;AAazB,EAAA,OAAO,EAAE,SAbgB;AAczB,EAAA,OAAO,EAAE;AAdgB,CAAd,CAAb;AAiBA,IAAM,MAAM,GAAG,MAAM,CAAC,MAAP,CAAc;AAC3B,EAAA,IAAI,EAAE,SADqB;AAE3B,EAAA,QAAQ,EAAE,SAFiB;AAG3B,EAAA,QAAQ,EAAE,SAHiB;AAI3B,EAAA,QAAQ,EAAE,SAJiB;AAK3B,EAAA,QAAQ,EAAE,SALiB;AAM3B,EAAA,QAAQ,EAAE,SANiB;AAO3B,EAAA,OAAO,EAAE,SAPkB;AAQ3B,EAAA,OAAO,EAAE,SARkB;AAS3B,EAAA,OAAO,EAAE,SATkB;AAU3B,EAAA,OAAO,EAAE,SAVkB;AAW3B,EAAA,OAAO,EAAE,SAXkB;AAY3B,EAAA,OAAO,EAAE,SAZkB;AAa3B,EAAA,OAAO,EAAE,SAbkB;AAc3B,EAAA,OAAO,EAAE;AAdkB,CAAd,CAAf;AAiBA,IAAM,KAAK,GAAG,MAAM,CAAC,MAAP,CAAc;AAC1B,EAAA,IAAI,EAAE,SADoB;AAE1B,EAAA,QAAQ,EAAE,SAFgB;AAG1B,EAAA,QAAQ,EAAE,SAHgB;AAI1B,EAAA,QAAQ,EAAE,SAJgB;AAK1B,EAAA,QAAQ,EAAE,SALgB;AAM1B,EAAA,QAAQ,EAAE,SANgB;AAO1B,EAAA,OAAO,EAAE,SAPiB;AAQ1B,EAAA,OAAO,EAAE,SARiB;AAS1B,EAAA,OAAO,EAAE,SATiB;AAU1B,EAAA,OAAO,EAAE,SAViB;AAW1B,EAAA,OAAO,EAAE,SAXiB;AAY1B,EAAA,OAAO,EAAE,SAZiB;AAa1B,EAAA,OAAO,EAAE,SAbiB;AAc1B,EAAA,OAAO,EAAE;AAdiB,CAAd,CAAd;AAiBA,IAAM,MAAM,GAAG,MAAM,CAAC,MAAP,CAAc;AAC3B,EAAA,IAAI,EAAE,SADqB;AAE3B,EAAA,QAAQ,EAAE,SAFiB;AAG3B,EAAA,QAAQ,EAAE,SAHiB;AAI3B,EAAA,QAAQ,EAAE,SAJiB;AAK3B,EAAA,QAAQ,EAAE,SALiB;AAM3B,EAAA,QAAQ,EAAE,SANiB;AAO3B,EAAA,OAAO,EAAE,SAPkB;AAQ3B,EAAA,OAAO,EAAE,SARkB;AAS3B,EAAA,OAAO,EAAE,SATkB;AAU3B,EAAA,OAAO,EAAE,SAVkB;AAW3B,EAAA,OAAO,EAAE,SAXkB;AAY3B,EAAA,OAAO,EAAE,SAZkB;AAa3B,EAAA,OAAO,EAAE,SAbkB;AAc3B,EAAA,OAAO,EAAE;AAdkB,CAAd,CAAf;AAiBA,IAAM,UAAU,GAAG,MAAM,CAAC,MAAP,CAAc;AAC/B,EAAA,IAAI,EAAE,SADyB;AAE/B,EAAA,QAAQ,EAAE,SAFqB;AAG/B,EAAA,QAAQ,EAAE,SAHqB;AAI/B,EAAA,QAAQ,EAAE,SAJqB;AAK/B,EAAA,QAAQ,EAAE,SALqB;AAM/B,EAAA,QAAQ,EAAE,SANqB;AAO/B,EAAA,OAAO,EAAE,SAPsB;AAQ/B,EAAA,OAAO,EAAE,SARsB;AAS/B,EAAA,OAAO,EAAE,SATsB;AAU/B,EAAA,OAAO,EAAE,SAVsB;AAW/B,EAAA,OAAO,EAAE,SAXsB;AAY/B,EAAA,OAAO,EAAE,SAZsB;AAa/B,EAAA,OAAO,EAAE,SAbsB;AAc/B,EAAA,OAAO,EAAE;AAdsB,CAAd,CAAnB;AAiBA,IAAM,KAAK,GAAG,MAAM,CAAC,MAAP,CAAc;AAC1B,EAAA,IAAI,EAAE,SADoB;AAE1B,EAAA,QAAQ,EAAE,SAFgB;AAG1B,EAAA,QAAQ,EAAE,SAHgB;AAI1B,EAAA,QAAQ,EAAE,SAJgB;AAK1B,EAAA,QAAQ,EAAE,SALgB;AAM1B,EAAA,QAAQ,EAAE,SANgB;AAO1B,EAAA,OAAO,EAAE,SAPiB;AAQ1B,EAAA,OAAO,EAAE,SARiB;AAS1B,EAAA,OAAO,EAAE,SATiB;AAU1B,EAAA,OAAO,EAAE;AAViB,CAAd,CAAd;AAaA,IAAM,QAAQ,GAAG,MAAM,CAAC,MAAP,CAAc;AAC7B,EAAA,IAAI,EAAE,SADuB;AAE7B,EAAA,QAAQ,EAAE,SAFmB;AAG7B,EAAA,QAAQ,EAAE,SAHmB;AAI7B,EAAA,QAAQ,EAAE,SAJmB;AAK7B,EAAA,QAAQ,EAAE,SALmB;AAM7B,EAAA,QAAQ,EAAE,SANmB;AAO7B,EAAA,OAAO,EAAE,SAPoB;AAQ7B,EAAA,OAAO,EAAE,SARoB;AAS7B,EAAA,OAAO,EAAE,SAToB;AAU7B,EAAA,OAAO,EAAE;AAVoB,CAAd,CAAjB;AAaA,IAAM,IAAI,GAAG,MAAM,CAAC,MAAP,CAAc;AACzB,EAAA,IAAI,EAAE,SADmB;AAEzB,EAAA,QAAQ,EAAE,SAFe;AAGzB,EAAA,QAAQ,EAAE,SAHe;AAIzB,EAAA,QAAQ,EAAE,SAJe;AAKzB,EAAA,QAAQ,EAAE,SALe;AAMzB,EAAA,QAAQ,EAAE,SANe;AAOzB,EAAA,OAAO,EAAE,SAPgB;AAQzB,EAAA,OAAO,EAAE,SARgB;AASzB,EAAA,OAAO,EAAE,SATgB;AAUzB,EAAA,OAAO,EAAE;AAVgB,CAAd,CAAb;AAaA,IAAM,MAAM,GAAG,MAAM,CAAC,MAAP,CAAc;AAC3B,EAAA,KAAK,EAAE,SADoB;AAE3B,EAAA,KAAK,EAAE,SAFoB;AAG3B,EAAA,WAAW,EAAE;AAHc,CAAd,CAAf;;eAMe,MAAM,CAAC,MAAP,CAAc;AAC3B,EAAA,GAAG,EAAH,GAD2B;AAE3B,EAAA,IAAI,EAAJ,IAF2B;AAG3B,EAAA,MAAM,EAAN,MAH2B;AAI3B,EAAA,UAAU,EAAV,UAJ2B;AAK3B,EAAA,MAAM,EAAN,MAL2B;AAM3B,EAAA,IAAI,EAAJ,IAN2B;AAO3B,EAAA,SAAS,EAAT,SAP2B;AAQ3B,EAAA,IAAI,EAAJ,IAR2B;AAS3B,EAAA,IAAI,EAAJ,IAT2B;AAU3B,EAAA,KAAK,EAAL,KAV2B;AAW3B,EAAA,UAAU,EAAV,UAX2B;AAY3B,EAAA,IAAI,EAAJ,IAZ2B;AAa3B,EAAA,MAAM,EAAN,MAb2B;AAc3B,EAAA,KAAK,EAAL,KAd2B;AAe3B,EAAA,MAAM,EAAN,MAf2B;AAgB3B,EAAA,UAAU,EAAV,UAhB2B;AAiB3B,EAAA,KAAK,EAAL,KAjB2B;AAkB3B,EAAA,QAAQ,EAAR,QAlB2B;AAmB3B,EAAA,IAAI,EAAJ,IAnB2B;AAoB3B,EAAA,MAAM,EAAN;AApB2B,CAAd,C", "sourcesContent": ["const red = Object.freeze({\n  base: '#f44336',\n  lighten5: '#ffebee',\n  lighten4: '#ffcdd2',\n  lighten3: '#ef9a9a',\n  lighten2: '#e57373',\n  lighten1: '#ef5350',\n  darken1: '#e53935',\n  darken2: '#d32f2f',\n  darken3: '#c62828',\n  darken4: '#b71c1c',\n  accent1: '#ff8a80',\n  accent2: '#ff5252',\n  accent3: '#ff1744',\n  accent4: '#d50000',\n})\n\nconst pink = Object.freeze({\n  base: '#e91e63',\n  lighten5: '#fce4ec',\n  lighten4: '#f8bbd0',\n  lighten3: '#f48fb1',\n  lighten2: '#f06292',\n  lighten1: '#ec407a',\n  darken1: '#d81b60',\n  darken2: '#c2185b',\n  darken3: '#ad1457',\n  darken4: '#880e4f',\n  accent1: '#ff80ab',\n  accent2: '#ff4081',\n  accent3: '#f50057',\n  accent4: '#c51162',\n})\n\nconst purple = Object.freeze({\n  base: '#9c27b0',\n  lighten5: '#f3e5f5',\n  lighten4: '#e1bee7',\n  lighten3: '#ce93d8',\n  lighten2: '#ba68c8',\n  lighten1: '#ab47bc',\n  darken1: '#8e24aa',\n  darken2: '#7b1fa2',\n  darken3: '#6a1b9a',\n  darken4: '#4a148c',\n  accent1: '#ea80fc',\n  accent2: '#e040fb',\n  accent3: '#d500f9',\n  accent4: '#aa00ff',\n})\n\nconst deepPurple = Object.freeze({\n  base: '#673ab7',\n  lighten5: '#ede7f6',\n  lighten4: '#d1c4e9',\n  lighten3: '#b39ddb',\n  lighten2: '#9575cd',\n  lighten1: '#7e57c2',\n  darken1: '#5e35b1',\n  darken2: '#512da8',\n  darken3: '#4527a0',\n  darken4: '#311b92',\n  accent1: '#b388ff',\n  accent2: '#7c4dff',\n  accent3: '#651fff',\n  accent4: '#6200ea',\n})\n\nconst indigo = Object.freeze({\n  base: '#3f51b5',\n  lighten5: '#e8eaf6',\n  lighten4: '#c5cae9',\n  lighten3: '#9fa8da',\n  lighten2: '#7986cb',\n  lighten1: '#5c6bc0',\n  darken1: '#3949ab',\n  darken2: '#303f9f',\n  darken3: '#283593',\n  darken4: '#1a237e',\n  accent1: '#8c9eff',\n  accent2: '#536dfe',\n  accent3: '#3d5afe',\n  accent4: '#304ffe',\n})\n\nconst blue = Object.freeze({\n  base: '#2196f3',\n  lighten5: '#e3f2fd',\n  lighten4: '#bbdefb',\n  lighten3: '#90caf9',\n  lighten2: '#64b5f6',\n  lighten1: '#42a5f5',\n  darken1: '#1e88e5',\n  darken2: '#1976d2',\n  darken3: '#1565c0',\n  darken4: '#0d47a1',\n  accent1: '#82b1ff',\n  accent2: '#448aff',\n  accent3: '#2979ff',\n  accent4: '#2962ff',\n})\n\nconst lightBlue = Object.freeze({\n  base: '#03a9f4',\n  lighten5: '#e1f5fe',\n  lighten4: '#b3e5fc',\n  lighten3: '#81d4fa',\n  lighten2: '#4fc3f7',\n  lighten1: '#29b6f6',\n  darken1: '#039be5',\n  darken2: '#0288d1',\n  darken3: '#0277bd',\n  darken4: '#01579b',\n  accent1: '#80d8ff',\n  accent2: '#40c4ff',\n  accent3: '#00b0ff',\n  accent4: '#0091ea',\n})\n\nconst cyan = Object.freeze({\n  base: '#00bcd4',\n  lighten5: '#e0f7fa',\n  lighten4: '#b2ebf2',\n  lighten3: '#80deea',\n  lighten2: '#4dd0e1',\n  lighten1: '#26c6da',\n  darken1: '#00acc1',\n  darken2: '#0097a7',\n  darken3: '#00838f',\n  darken4: '#006064',\n  accent1: '#84ffff',\n  accent2: '#18ffff',\n  accent3: '#00e5ff',\n  accent4: '#00b8d4',\n})\n\nconst teal = Object.freeze({\n  base: '#009688',\n  lighten5: '#e0f2f1',\n  lighten4: '#b2dfdb',\n  lighten3: '#80cbc4',\n  lighten2: '#4db6ac',\n  lighten1: '#26a69a',\n  darken1: '#00897b',\n  darken2: '#00796b',\n  darken3: '#00695c',\n  darken4: '#004d40',\n  accent1: '#a7ffeb',\n  accent2: '#64ffda',\n  accent3: '#1de9b6',\n  accent4: '#00bfa5',\n})\n\nconst green = Object.freeze({\n  base: '#4caf50',\n  lighten5: '#e8f5e9',\n  lighten4: '#c8e6c9',\n  lighten3: '#a5d6a7',\n  lighten2: '#81c784',\n  lighten1: '#66bb6a',\n  darken1: '#43a047',\n  darken2: '#388e3c',\n  darken3: '#2e7d32',\n  darken4: '#1b5e20',\n  accent1: '#b9f6ca',\n  accent2: '#69f0ae',\n  accent3: '#00e676',\n  accent4: '#00c853',\n})\n\nconst lightGreen = Object.freeze({\n  base: '#8bc34a',\n  lighten5: '#f1f8e9',\n  lighten4: '#dcedc8',\n  lighten3: '#c5e1a5',\n  lighten2: '#aed581',\n  lighten1: '#9ccc65',\n  darken1: '#7cb342',\n  darken2: '#689f38',\n  darken3: '#558b2f',\n  darken4: '#33691e',\n  accent1: '#ccff90',\n  accent2: '#b2ff59',\n  accent3: '#76ff03',\n  accent4: '#64dd17',\n})\n\nconst lime = Object.freeze({\n  base: '#cddc39',\n  lighten5: '#f9fbe7',\n  lighten4: '#f0f4c3',\n  lighten3: '#e6ee9c',\n  lighten2: '#dce775',\n  lighten1: '#d4e157',\n  darken1: '#c0ca33',\n  darken2: '#afb42b',\n  darken3: '#9e9d24',\n  darken4: '#827717',\n  accent1: '#f4ff81',\n  accent2: '#eeff41',\n  accent3: '#c6ff00',\n  accent4: '#aeea00',\n})\n\nconst yellow = Object.freeze({\n  base: '#ffeb3b',\n  lighten5: '#fffde7',\n  lighten4: '#fff9c4',\n  lighten3: '#fff59d',\n  lighten2: '#fff176',\n  lighten1: '#ffee58',\n  darken1: '#fdd835',\n  darken2: '#fbc02d',\n  darken3: '#f9a825',\n  darken4: '#f57f17',\n  accent1: '#ffff8d',\n  accent2: '#ffff00',\n  accent3: '#ffea00',\n  accent4: '#ffd600',\n})\n\nconst amber = Object.freeze({\n  base: '#ffc107',\n  lighten5: '#fff8e1',\n  lighten4: '#ffecb3',\n  lighten3: '#ffe082',\n  lighten2: '#ffd54f',\n  lighten1: '#ffca28',\n  darken1: '#ffb300',\n  darken2: '#ffa000',\n  darken3: '#ff8f00',\n  darken4: '#ff6f00',\n  accent1: '#ffe57f',\n  accent2: '#ffd740',\n  accent3: '#ffc400',\n  accent4: '#ffab00',\n})\n\nconst orange = Object.freeze({\n  base: '#ff9800',\n  lighten5: '#fff3e0',\n  lighten4: '#ffe0b2',\n  lighten3: '#ffcc80',\n  lighten2: '#ffb74d',\n  lighten1: '#ffa726',\n  darken1: '#fb8c00',\n  darken2: '#f57c00',\n  darken3: '#ef6c00',\n  darken4: '#e65100',\n  accent1: '#ffd180',\n  accent2: '#ffab40',\n  accent3: '#ff9100',\n  accent4: '#ff6d00',\n})\n\nconst deepOrange = Object.freeze({\n  base: '#ff5722',\n  lighten5: '#fbe9e7',\n  lighten4: '#ffccbc',\n  lighten3: '#ffab91',\n  lighten2: '#ff8a65',\n  lighten1: '#ff7043',\n  darken1: '#f4511e',\n  darken2: '#e64a19',\n  darken3: '#d84315',\n  darken4: '#bf360c',\n  accent1: '#ff9e80',\n  accent2: '#ff6e40',\n  accent3: '#ff3d00',\n  accent4: '#dd2c00',\n})\n\nconst brown = Object.freeze({\n  base: '#795548',\n  lighten5: '#efebe9',\n  lighten4: '#d7ccc8',\n  lighten3: '#bcaaa4',\n  lighten2: '#a1887f',\n  lighten1: '#8d6e63',\n  darken1: '#6d4c41',\n  darken2: '#5d4037',\n  darken3: '#4e342e',\n  darken4: '#3e2723',\n})\n\nconst blueGrey = Object.freeze({\n  base: '#607d8b',\n  lighten5: '#eceff1',\n  lighten4: '#cfd8dc',\n  lighten3: '#b0bec5',\n  lighten2: '#90a4ae',\n  lighten1: '#78909c',\n  darken1: '#546e7a',\n  darken2: '#455a64',\n  darken3: '#37474f',\n  darken4: '#263238',\n})\n\nconst grey = Object.freeze({\n  base: '#9e9e9e',\n  lighten5: '#fafafa',\n  lighten4: '#f5f5f5',\n  lighten3: '#eeeeee',\n  lighten2: '#e0e0e0',\n  lighten1: '#bdbdbd',\n  darken1: '#757575',\n  darken2: '#616161',\n  darken3: '#424242',\n  darken4: '#212121',\n})\n\nconst shades = Object.freeze({\n  black: '#000000',\n  white: '#ffffff',\n  transparent: 'transparent',\n})\n\nexport default Object.freeze({\n  red,\n  pink,\n  purple,\n  deepPurple,\n  indigo,\n  blue,\n  lightBlue,\n  cyan,\n  teal,\n  green,\n  lightGreen,\n  lime,\n  yellow,\n  amber,\n  orange,\n  deepOrange,\n  brown,\n  blueGrey,\n  grey,\n  shades,\n})\n"], "sourceRoot": "", "file": "colors.js"}
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_ANDON'
import request from '@/util/request';
import { configUrl } from '@/config';
const baseAndonURL = configUrl[process.env.VUE_APP_SERVE].baseURL_ANDON;
//通知记录

//获取通知记录列表
export function getNoticeRecordList(data) {
    const api =  '/andon/NoticeRecord/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑通知记录
export function NoticeRecordSaveForm(data) {
    const api =  '/andon/NoticeRecord/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除通知记录
export function DeleteNoticeRecord(data) {
    const api =  '/andon/NoticeRecord/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//excel导出
export function ExportNoticeRecordToExcel(data) {
    return request({
        url: baseAndonURL + '/andon/NoticeRecord/ExportNoticeRecordToExcel',
        method: 'post',
        data,
        responseType: 'blob'
    })
}
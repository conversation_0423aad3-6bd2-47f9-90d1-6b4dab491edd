import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';
let DFM = 'baseURL_DFM';


// 任务列表
export function GetSpotCheckWoPageList(data) {
    const api = '/api/SpotCheckWo/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}
//任务生成
export function GetCreateSpotCheckWoByItems(data, id, PlanStartDate) {
    const api = `/api/SpotCheckWo/CreateSpotCheckWoByItems?deviceId=${id}&PlanCheckDate=${PlanStartDate}`
    return getRequestResources(baseURL, api, 'post', data);
}
//任务删除
export function GetSpotCheckWoDelete(data) {
    const api = '/api/SpotCheckWo/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//点检任务删除
export function GetSpotCheckWoItemDelete(data) {
    const api = '/api/SpotCheckWoItem/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//点检任务
export function GetSpotCheckWoItemPageList(data) {
    const api = '/api/SpotCheckWoItem/GetListByWoId'
    return getRequestResources(baseURL, api, 'post', data);
}
//点检保存
export function GetSpotCheckWoItemSaveForm(data) {
    const api = '/api/SpotCheckWoItem/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//点检图片上传
export function GetSpotCheckWoItemUploadFile(data) {
    const api = '/api/SpotCheckWoItem/UploadFile'
    return getRequestResources(baseURL, api, 'post', data);
}
//点检图片上传
export function GetSpotCheckItemUploadFile(data) {
    const api = '/api/SpotCheckItem/UploadFile'
    return getRequestResources(baseURL, api, 'post', data);
}
//点检图片下载
export function GetSpotCheckWoItemGetFileUrl(data, params) {
    const api = `/api/SpotCheckWoItem/GetFileUrl?fileName=${params}`
    return getRequestResources(baseURL, api, 'get', data);
}
//批量点检
export function GetSpotCheckWoItemSpotCheck(data) {
    const api = '/api/SpotCheckWoItem/SpotCheckes'
    return getRequestResources(baseURL, api, 'post', data);
}
//单个点检
export function GetSpotCheckWoItemSingleSpotCheck(data) {
    const api = '/api/SpotCheckWoItem/SpotCheck'
    return getRequestResources(baseURL, api, 'post', data);
}
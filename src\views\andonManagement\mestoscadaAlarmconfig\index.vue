// 告警升级规则
<template>
    <div class="line-side-view">
        <div class="line-side-main overflow-auto">
            <SearchForm ref="contactTorm" class="mt-2" @selectChange="selectChange" :searchinput="searchinput"
                :show-from="showFrom" @searchForm="searchForm" />
            <v-card outlined>
                <div class="form-btn-list">
                    <!-- 搜索栏 -->
                    <!-- <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn> -->
                    <v-btn icon color="primary">
                        <v-icon @click="getDataList">mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'BJCFGZ_ADD'" @click="operaClick({})">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="error" v-has="'BJCFGZ_ALLREMOVE'" @click="sureItems()"
                        :disabled="selectedList.length == 0">{{ $t('GLOBAL._PLSC') }}</v-btn>
                </div>
                <Tables :headers="headers" :desserts="desserts" :loading="loading" :page-options="pageOptions"
                    :btn-list="btnList" :dictionaryList="dictionaryList" @selectePages="selectePages"
                    table-name="ANDON_BJCFGZ" @itemSelected="selectedItems" @toggleSelectAll="selectedItems"
                    @tableClick="tableClick"></Tables>
            </v-card>
            <update-dialog ref="updateDialog" :productLineList="productLine" :opera-obj="operaObj"
                :equipmentList="equipmentList" @handlePopup="handlePopup"></update-dialog>
        </div>
    </div>
</template>
<script>
import { GetListByLevel, EquipmentGetEquipmentTree } from '@/api/common.js';
import { getAlarmTypeRootList, GetListByAlarmId, getAlarmTypeTreetList } from '@/api/andonManagement/alarmType.js';
import { getMestoscadaAlarmconfigList, DeleteMestoscadaAlarmconfig } from '@/api/andonManagement/mestoscadaAlarmconfig.js';
import { mestoscadaAlarmconfig } from '@/columns/andonManagement/mestoscadaAlarmconfig.js';
export default {
    name: 'UpgradeRule',
    components: {
        UpdateDialog: () => import('./components/updateDialog.vue')
    },
    data() {
        return {
            operaObj: {},
            showFrom: false,
            headers: mestoscadaAlarmconfig,
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            deleteId: [],
            selectedList: [],
            desserts: [],
            searchParams: {},
            alarmTypeList: [],
            alarmTypeRootList: [],
            subAlarmTypeList: [],
            MainAlarmType: '',
            SubAlarmType: '',
            productLine: [],
            equipmentList: []
        }
    },
    computed: {
        //查询条件
        searchinput() {
            return [
                // 告警
                {
                    key: 'Maintype',
                    icon: '',
                    type: 'select',
                    selectData: this.$changeSelectItems(this.alarmTypeRootList, 'AlarmCode', 'AlarmName'),
                    value: this.MainAlarmType,
                    label: this.$t('$vuetify.dataTable.ANDON_BJCFGZ.Maintype')
                },
                // 二级分类
                {
                    key: 'Subtype',
                    icon: '',
                    type: 'select',
                    selectData: this.$changeSelectItems(this.subAlarmTypeList, 'AlarmCode', 'AlarmName'),
                    value: this.SubAlarmType,
                    label: this.$t('$vuetify.dataTable.ANDON_BJCFGZ.Subtype')
                }
            ];
        },
        btnList() {
            return [
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary', authCode: 'BJCFGZ_EDIT' },
                { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'error', authCode: 'BJCFGZ_EDIT' }
            ];
        },
        dictionaryList() {
            return [
                { arr: this.alarmTypeList, key: 'Maintype', val: 'AlarmCode', text: 'AlarmName' },
                { arr: this.productLine, key: 'Productlinecode', val: 'EquipmentCode', text: 'EquipmentName' },
                { arr: this.alarmTypeList, key: 'Subtype', val: 'AlarmCode', text: 'AlarmName' },
                { arr: this.equipmentList, key: 'Equipmentcode', val: 'ItemValue', text: 'ItemName' }
            ]
        }
    },
    async created() {
        this.getalarmTypeList();
        await this.GetFactoryTree()
        await this.getAlarmTypeList();
        await this.GetListByLevel();
        this.getDataList();
    },
    methods: {
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        // 操作栏按钮
        tableClick(item, type) {
            const MainAlarmName = this.$getDictionaryVal(item.MainAlarmType, this.alarmTypeList, 'AlarmCode', 'AlarmName')
            const SubAlarmName = this.$getDictionaryVal(item.SubAlarmType, this.alarmTypeList, 'AlarmCode', 'AlarmName')
            switch (type) {
                // 编辑
                case 'edit':
                    this.operaClick(item);
                    break;
                // 删除
                case 'delete':
                    this.deleteId = item?.ID
                    this.sureDelete()
                    break;
                default:
                    break;
            }
        },
        // 获取设备树形数据
        async GetFactoryTree() {
            const res = await EquipmentGetEquipmentTree();
            let { success, response } = res;
            if (success) {
                this.equipmentList = response || [];
            }
        },
        // 获取工段
        async GetListByLevel() {
            let params = {
                key: 'Line' // 工段
            };
            this.productLine = []
            const res = await GetListByLevel(params);
            let { success, response } = res;
            if (success) {
                this.productLine = response;
            }
        },
        // 获取大类列表
        async getAlarmTypeList() {
            const res = await getAlarmTypeRootList({});
            const { success, response } = res || {};
            if (success) {
                this.alarmTypeRootList = response;
            } else {
                this.alarmTypeRootList = [];
            }
        },
        // 获取子级
        async getTypeChildList(alarmId) {
            this.subAlarmTypeList = [];
            const res = await GetListByAlarmId({ alarmId });
            const { success, response } = res || {};
            if (success) {
                this.subAlarmTypeList = response;
            }
        },
        // 获取告警类型列表
        async getalarmTypeList() {
            const res = await getAlarmTypeTreetList({});
            this.alarmTypeList = []
            const { success, response } = res || {};
            if (response && success) {
                response.forEach(e => {
                    this.alarmTypeList.push(e)
                    const { children } = e
                    if (children && children.length) {
                        children.forEach(i => {
                            this.alarmTypeList.push(i)
                        })
                    }
                })
            }
        },
        // 下拉框操作
        selectChange(v, p) {
            const { key, value } = v || {}
            if (key == "MainAlarmType") {
                const { MainAlarmType, SubAlarmType } = p
                this.MainAlarmType = MainAlarmType;
                this.SubAlarmType = SubAlarmType;
                const obj = this.alarmTypeRootList.find(i => i.AlarmCode == value)
                this.getTypeChildList(obj?.ID)
            }
        },
        // 获取全部表格数据
        async getDataList() {
            this.loading = true;
            let params = {
                ...this.searchParams,
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await getMestoscadaAlarmconfigList(params);
            const { success, response } = res || {};
            const { data, dataCount, page } = response || {};
            this.desserts = []
            if (success && data) {
                const arr = data || [];
                arr.forEach(e => {
                    this.desserts.push({ ...e, actionsBtnShow: 'Status' })
                });
                this.pageOptions.total = dataCount;
                this.pageOptions.page = page;
            } else {
                this.desserts = [];
            }
            this.loading = false;
        },
        // 查询数据
        searchForm(v) {
            this.searchParams = v;
            this.getDataList();
        },
        // 新增
        operaClick(o) {
            this.operaObj = o || {};
            this.$refs.updateDialog.dialog = true;
        },
        // 批量删除
        sureItems() {
            this.deleteId = ''
            this.sureDelete();
        },
        // 删除二次确认
        sureDelete() {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    const params = [];
                    if (this.deleteId) {
                        params.push(this.deleteId);
                    } else {
                        this.selectedList.forEach(e => {
                            params.push(e.ID);
                        });
                    }
                    const res = await DeleteMestoscadaAlarmconfig(params);
                    this.selectedList = [];
                    this.deleteId = '';
                    const { success, msg } = res;
                    if (success) {
                        this.pageOptions.pageCount = 1;
                        this.getDataList();
                        this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 根据子组件返回来值
        handlePopup(type, data) {
            this.getDataList();
            // switch (type) {
            //     case 'refresh':
            //         this.getDataList();
            //         break;
            //     case 'detail':
            //         this.receivedorderid = data?.ID
            //         this.$refs.materailDetailDialog.dialog = true;
            //         break;
            //     default:
            //         break;
            // }
        }
    }
};
</script>
<style lang="scss" scoped>
.line-side-view {
    display: flex;

    .line-side-main {
        flex: 1;
        width: 100%;

        .v-data-table {
            width: 100%;
        }
    }
}
</style>
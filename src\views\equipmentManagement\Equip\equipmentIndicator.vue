<template>
    <div>
        <div class="form-btn-list">
            <v-btn icon color="primary" @click="RepastInfoTARGetPage">
                <v-icon>mdi-cached</v-icon>
            </v-btn>
            <v-btn color="primary" v-has="'SBTZGL_SBZB_ADD'" @click="btnClickEvet('addTarget')">{{ $t('GLOBAL._XZ')
            }}</v-btn>
            <v-btn color="primary" v-has="'SBTZGL_SBZB_ALLREMOVE'" :disabled="!deleteList.length"
                @click="btnClickEvet('delete')">{{ $t('GLOBAL._PLSC')
                }}</v-btn>
        </div>
        <Tables :page-options="pageOptions" :loading="loading" :btn-list="btnList" tableHeight="calc(50vh - 135px)"
            table-name="TPM_SBGL_SBTZGL_SBZB" :headers="EquipTargetColum" :desserts="desserts" @selectePages="selectePages"
            @tableClick="tableClick" @itemSelected="SelectedItems" @toggleSelectAll="SelectedItems"></Tables>
        <createRepast ref="createRepast" :dialogType="dialogType" :tableItem="tableItem" :rowtableItem="rowtableItem">
        </createRepast>
    </div>
</template>
<script>
import { EquipTargetGetPageList, EquipTargetDelete } from '@/api/equipmentManagement/Equip.js';
import { EquipTargetColum } from '@/columns/equipmentManagement/Equip.js';

export default {
    components: {
        createRepast: () => import('./components/createRepast.vue')
    },
    props: {
        rowtableItem: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            loading: false,
            showFrom: false,
            papamstree: {
                key: null,
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            EquipTargetColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            hasChildren: {} // 新增字典详情判断-子节点才能新增
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'editTarget',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBTZGL_SBZB_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBTZGL_SBZB_DELETE'
                }
            ];
        }
    },
    async created() {
        // await this.RepastInfoTARGetPage();
    },
    methods: {
        // 设备target列表查询
        async RepastInfoTARGetPage(itemrow) {
            let params = {
                key: this.papamstree.key,
                eqid: itemrow.ID || this.rowtableItem.ID,
                pageIndex: this.papamstree.pageIndex,
                pageSize: this.papamstree.pageSize
            };
            this.loading = true;
            const res = await EquipTargetGetPageList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'addTarget':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'editTarget':
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await EquipTargetDelete(params);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.RepastInfoTARGetPage(this.rowtableItem);
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + item);
            this.deleteList = item;
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoTARGetPage();
        }
    }
};
</script>
<style lang="scss" scoped></style>
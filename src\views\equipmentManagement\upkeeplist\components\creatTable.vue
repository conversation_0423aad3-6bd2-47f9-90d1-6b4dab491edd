<template>
    <v-card class="creatTable" style="overflow-y: hidden">
        <v-card-title class="text-h6 justify-space-between primary lighten-2">
            {{ $t('TPM_SBGL_SBDJJH._SCDJJH') }}
            <v-icon @click="closePopup">mdi-close</v-icon>
        </v-card-title>
        <div class="allCreatTable" style="height: 625px">
            <div class="allCreatTableLeft">
                <TreeView :items="treeData" :activeKey="' '" :title="$t('DFM_GZRL._WLMX')" @clickClassTree="clickClassTree"></TreeView>
            </div>
            <div class="allCreatTableRight" style="height: 94.5%">
                <div class="allCreatTablesingle">
                    <div class="CreatTableTitle">{{ $t('TPM_SBGL_SBBYJH._SBQD') }}</div>
                    <Tables
                        :page-options="pageOptions"
                        :loading="loading"
                        ref="Tables"
                        :showSelect="false"
                        tableHeight="215px"
                        table-name="TPM_SBGL_SBTZGL"
                        :clickFun="clickFun"
                        :headers="keepListSBColum"
                        :desserts="desserts"
                        @selectePages="selectePages"
                    ></Tables>
                </div>
                <div class="allCreatTablesingle">
                    <div class="CreatTableTitle">{{ $t('TPM_SBGL_SBDJJH._DJXMQD') }}</div>
                    <Tables
                        :page-options="pageOptions2"
                        :loading="loading2"
                        tableHeight="215px"
                        table-name="TPM_SBGL_SBDJXM"
                        :headers="keepPlanNoActionsColum"
                        :desserts="desserts2"
                        @itemSelected="SelectedItems"
                        @toggleSelectAll="SelectedItems"
                        @selectePages="selectePages2"
                    ></Tables>
                </div>
                <div class="allCreatTablesingle" style="display: flex">
                    <div class="CreatTableTitle" style="margin-top: 20px">{{ $t('TPM_SBGL_SBDJJH._DJXMJHKSSJ') }}</div>
                    <div style="margin-top: 20px">
                        <div class="textfieldbox">
                            <v-text-field v-model="PlanStartDate" :clearable="true" outlined dense :label="$t('DFM_GDGL.PlanStartTime')" readonly></v-text-field>
                            <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="PlanStartDate" type="datetime" :placeholder="$t('DFM_GDGL.PlanStartTime')"></el-date-picker>
                        </div>
                    </div>
                </div>
                <!-- 
                <v-card-text class="card-text" style="min-height: auto; height: 50px">
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="PlanStartDate" :clearable="true" outlined dense :label="$t('DFM_GDGL.PlanStartTime')" readonly></v-text-field>
                        <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="PlanStartDate" type="datetime" :placeholder="$t('DFM_GDGL.PlanStartTime')"></el-date-picker>
                    </v-col>
                </v-card-text> -->
            </div>
        </div>

        <v-card-actions>
            <v-btn color="primary" @click="Save()" :disabled="PlanStartDate == ''">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" style="margin-right: 18px" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>
<script>
import { EquipmentGetEquipmentTree } from '@/api/common.js';
import { GetCreateSpotCheckWoByItems } from '@/api/equipmentManagement/SpotCheckWo.js';
import { keepPlanNoActionsColum, keepListSBColum } from '@/columns/equipmentManagement/upkeep.js';
import { GetDevicePageList } from '@/api/equipmentManagement/Equip.js';
import { GetSpotCheckItemPageList } from '@/api/equipmentManagement/SpotCheckItem.js';

export default {
    name: 'creatTable',
    data() {
        return {
            treeData: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            pageOptions2: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            papamstree: {
                LineId: '',
                pageIndex: 1,
                pageSize: 20
            },
            papamstree2: {
                DeviceCategoryId: '',
                pageIndex: 1,
                pageSize: 20
            },
            PlanStartDate: '',
            loading: false,
            loading2: false,
            desserts: [],
            desserts2: [],
            deleteList: [],
            keepListSBColum,
            keepPlanNoActionsColum,
            deviceId: ''
        };
    },
    mounted() {
        this.GetFactorylineTree();
    },
    methods: {
        // 列表查询
        async RepastInfoGetPage2() {
            let params = {
                ...this.papamstree2
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading2 = true;
            const res = await GetSpotCheckItemPageList(params);
            let { success, response } = res;
            if (success) {
                this.loading2 = false;
                this.desserts2 = (response || {}).data || [];
                this.pageOptions2.total = response.dataCount;
                this.pageOptions2.page = response.page;
                this.pageOptions2.pageCount = response.pageCount;
                this.pageOptions2.pageSize = response.pageSize;
            }
        },

        //  查看BOM详情
        clickFun(data) {
            this.deviceId = data.ID;
            this.papamstree2.pageIndex = 1;
            this.papamstree2.DeviceCategoryId = data.DeviceCategoryId;
            this.$refs.Tables.selected = [data];
            this.RepastInfoGetPage2();
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        },
        selectePages2(v) {
            this.papamstree2.pageIndex = v.pageCount;
            this.papamstree2.pageSize = v.pageSize;
            this.RepastInfoGetPage2();
        },
        // 设备列表查询
        async RepastInfoGetPage() {
            let params = {
                ...this.papamstree
            };
            this.loading = true;
            const res = await GetDevicePageList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.desserts.map(item => {
                    item.Status = item.Status == 1 ? true : false;
                    return item;
                });
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
                this.rowtableItem = this.desserts[0] || {};
            }
        },
        async Save() {
            this.deleteList.forEach(item => {
                item.DeviceCategoryId = this.papamstree2.DeviceCategoryId;
                item.PlanStartDate = this.PlanStartDate;
            });
            let res = await GetCreateSpotCheckWoByItems(this.deleteList, this.deviceId, this.PlanStartDate);
            this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
            this.$emit('closePopup');
            this.$emit('loadData');
        },
        // 树状点击获取
        clickClassTree(v) {
            this.papamstree.pageIndex = 1;
            if (v.id == this.papamstree.LineId) {
                this.papamstree.LineId = '';
            } else {
                this.papamstree.LineId = v.id;
            }
            this.desserts2 = [];
            this.deleteList = [];
            this.RepastInfoGetPage();
        },
        closePopup() {
            this.$emit('closePopup');
        },
        // 删除列表
        SelectedItems(item) {
            this.deleteList = [...item];
        },
        // 获取树形数据
        async GetFactorylineTree() {
            const res = await EquipmentGetEquipmentTree();
            let { success, response } = res;
            if (success) {
                this.treeData = response || [];
                this.papamstree.LineId = '';
            }
        }
    }
};
</script>
<style lang="scss" scoped>
</style>

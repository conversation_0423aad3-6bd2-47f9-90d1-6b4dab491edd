// import store from '../store';
import { LayoutAuth, LayoutDefault, RouteWrapper } from '@/components/layouts';

export const initMenu = (router, menu) => {
    if (menu.length === 0) {
        return;
    }
    let menus = formatRoutes(menu);
    // 最后添加
    // let unfound = { path: '*', redirect: '/404', hidden: true };
    // menus.push(unfound);
    let routerItem = {
        path: '/',
        component: LayoutDefault,
        redirect: to => { return '/dashboard' },
        children: menus
    }
    router.addRoute(routerItem)
    // router.addRoutes(menus);
    // store.commit('ADD_ROUTERS', menus);
};

export const formatRoutes = aMenu => {
    if (!aMenu || aMenu.length == 0) {
        return []
    }
    const aRouter = [{
        path: '/dashboard',
        name: 'dashboard',
        meta: {
            title: 'dashboard',
            icon: 'mdi-view-dashboard'
        },
        component: () =>
            import('@/views/Dashboard.vue')
    },];
    aMenu.forEach(oMenu => {
        // const { path, component, name, icon, childrens } = oMenu;
        const { Route, component, name, Icon, children, MEMU_TYPE, IsKeepAlive, isHide } = oMenu;
        let path;
        if (Route.indexOf('?') != -1) {
            path = Route.substring(0, Route.indexOf("?"))
        } else {
            path = Route
        }
        // eslint-disable-next-line no-undef
        const oRouter = {
            path: path,
            component: MEMU_TYPE == 1 ? () =>
                import('../components/layouts/RouteWrapper.vue') : () =>
                import(`../views${path}/index.vue`),
            name: name,
            icon: Icon,
            // eslint-disable-next-line no-undef
            children: formatRoutes(children),
            meta: {
                isKeepAlive: !!IsKeepAlive,
                isHide: isHide,
            }
        };
        aRouter.push(oRouter);
    });
    return aRouter;
};
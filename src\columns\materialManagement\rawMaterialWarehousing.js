// dictionary: true, isEditCell: true, 
export const rawMaterialWarehousingColum = [
    { text: '序号', value: 'Index', width: '80px' },
    { text: '入库单号', value: 'ReceivedOrderid', width: '160px' },
    { text: '订单号', value: 'Orderid', width: '160px' },
    // { text: '仓库编号', value: 'WarehouseCode', width: '140px' },
    { text: '仓库名称', value: 'WarehouseName', width: '140px' },
    { text: '发料仓库', value: 'ReceivedWarehouseName', width: '140px' },
    { text: '需求日期', value: 'DemandDate', width: '160px' },
    { text: '发料日期', value: 'ReceivedDate', width: '146px',},
    { text: '状态', value: 'Status', width: '140px', dictionary: true },
    { text: '联系人', value: 'WarehouseType', width: '140px',},
    { text: '确认人', value: 'Confirmuserid', width: '140px',},
    { text: '确认时间', value: 'Confirmdate', width: '160px' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '150px'
    }
];

export const rawMaterialDetailColum = [
    // { text: '序号', value: 'Index', width: '80px' },
    { text: '物料名称', value: 'Materialname', width: '240px' },
    { text: '数量', value: 'Num', width: '100px', semicolonFormat: true },
    { text: '物料批次', value: 'Batchcode', width: '160px' },
    { text: '追溯批次', value: 'Backbatchcode', width: '160px'},
    { text: '计量单位', value: 'Unit', width: '100px',},
    { text: '生产日期', value: 'Producedate', width: '160px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '100px'
    }
];
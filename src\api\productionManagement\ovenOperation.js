import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TRACE; // 配置服务url
// 获取产线
export function GetFeedingProcDevice(data) {
    return request({
        url: baseURL + '/trace/Feeding/GetEquipmentDevice',
        method: 'post',
        data
    });
}
//烘箱录入
export function hxintoBatchAndBatchRech(data) {
    return request({
        url: baseURL + '/trace/wo/hxintoBatchAndBatchRech',
        method: 'post',
        data
    });
}

//虚拟入库
export function VirtualStorage(data) {
    return request({
        url: baseURL + '/trace/wo/VirtualStorage',
        method: 'post',
        data
    });
}

//烘箱列表
export function BatchGetPageList(data) {
    return request({
        url: baseURL + '/trace/BatchFlowRecord/GetPageList',
        method: 'post',
        data
    });
}
// 取消虚拟入库
export function cancelVirtualStorage(data) {
    return request({
        url: baseURL + '/trace/Wo/CancelVirtualStorage',
        method: 'post',
        data
    })
}


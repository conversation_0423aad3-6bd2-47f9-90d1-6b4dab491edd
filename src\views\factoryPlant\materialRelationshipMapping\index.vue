// 物料关系映射
<template>
    <div class="basic-process-view">
        <div class="basic-process-main overflow-auto">
            <SearchForm class="mt-4" ref="contactTorm" :searchinput="searchinput" :show-from="showFrom"
                @searchForm="searchForm" />
            <v-card outlined>
                <div class="form-btn-list">
                    <!-- 搜索栏 -->
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary">
                        <v-icon @click="getDataList">mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'WLGXYS_ADD'" @click="operaClick({})">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="error" v-has="'WLGXYS_ALLREMOVE'" @click="deleteItems()"
                        :disabled="selectedList.length == 0">{{ $t('GLOBAL._PLSC')
                        }}</v-btn>
                </div>
                <Tables :desserts="desserts" :loading="loading" :page-options="pageOptions" :btn-list="btnList"
                    :headers="headers" :footer="false"
                    :tableHeight="showFrom ? 'calc(100vh - 191px)' : 'calc(100vh - 125px)'" :dictionaryList="dictionaryList"
                    table-name="DFM_WLGXYS" @selectePages="selectePages" @itemSelected="selectedItems"
                    @toggleSelectAll="selectedItems" @tableClick="tableClick"></Tables>
            </v-card>
            <update-dialog ref="updateDialog" :materialGroupList="MaterialGroupList" :materialTypeList="MaterialTypeList"
                :opera-obj="operaObj" @handlePopup="handlePopup"></update-dialog>
        </div>
    </div>
</template>
<script>
import { GetMaterialTypeRelList, DeleteMaterialTypeRel, GetCategoryList } from '@/api/factoryPlant/materialRelationshipMapping.js';
import { materialRelationshipMapping } from '@/columns/factoryPlant/materialRelationshipMapping.js';
export default {
    name: 'MaterialRelationshipMapping',
    components: {
        UpdateDialog: () => import('./components/updateDialog.vue')
    },
    data() {
        return {
            operaObj: {},
            showFrom: false,
            deleteId: '',
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 9999, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            headers: materialRelationshipMapping,
            selectedList: [],
            desserts: [],
            searchParams: {},
            processTypeList: [],
            MaterialTypeList: [],
            MaterialGroupList: []
        };
    },
    computed: {
        //查询条件
        searchinput() {
            return [
                // 物料组
                {
                    key: 'Categorycode',
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.MaterialGroupList, 'Code', 'Name'),
                    value: '',
                    icon: '',
                    label: this.$t('$vuetify.dataTable.DFM_WLGXYS.Type')
                },
                // 物料类别
                {
                    key: 'Type',
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.MaterialTypeList, 'Code', 'Name'),
                    value: '',
                    icon: '',
                    label: this.$t('$vuetify.dataTable.DFM_WLGXYS.Categorycode')
                }
            ];
        },
        btnList() {
            return [
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary', authCode: 'WLGXYS_EDIT' },
                { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'red', authCode: 'WLGXYS_DELETE' }
            ];
        },
        dictionaryList() {
            return [
                { arr: this.MaterialTypeList, key: 'Type', val: 'Code', text: 'Name' },
                { arr: this.MaterialGroupList, key: 'Categorycode', val: 'Code', text: 'Name' }
            ]
        }
    },
    mounted() {
        this.getDataList();
        this.GetCategoryList('MaterialType');
        this.GetCategoryList('MaterialGroup');
    },
    methods: {
        async GetCategoryList(identity) {
            // 获取物料分类
            const res = await GetCategoryList({ identity, pageIndex: 1, pageSize: 9999 });
            const { success, response } = res || {};
            const { data } = response
            if (success) {
                if (identity == 'MaterialType') this.MaterialTypeList = data
                else this.MaterialGroupList = data
            } else {
                this.desserts = [];
            }
        },
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        // 操作栏按钮
        tableClick(item, type) {
            switch (type) {
                // 编辑
                case 'edit':
                    this.operaClick(item);
                    break;
                // 删除
                case 'delete':
                    this.deleteId = item.ID;
                    this.sureDelete();
                    break;
                default:
                    break;
            }
        },
        // 获取全部表格数据
        async getDataList() {
            this.loading = true;
            let params = {
                ...this.searchParams,
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await GetMaterialTypeRelList(params);
            const { success, response } = res || {};
            const { data } = response
            if (success) {
                this.desserts = data;
            } else {
                this.desserts = [];
            }
            this.loading = false;
        },
        // 查询数据
        searchForm(v) {
            this.searchParams = v;
            this.getDataList();
        },
        // 新增/编辑基础工序
        operaClick(o) {
            this.operaObj = o || {};
            this.$refs.updateDialog.updateDialog = true;
        },
        // 批量删除
        async deleteItems() {
            this.deleteId = '';
            this.sureDelete();
        },
        // 确认删除
        sureDelete() {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            }).then(async () => {
                const params = [];
                if (this.deleteId) {
                    params.push(this.deleteId);
                } else {
                    this.selectedList.forEach(e => {
                        params.push(e.ID);
                    });
                }
                const res = await DeleteMaterialTypeRel(params);
                this.selectedList = [];
                this.deleteId = '';
                const { success, msg } = res;
                if (success) {
                    this.pageOptions.pageCount = 1;
                    this.getDataList();
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                }
            });
        },
        // 根据子组件返回来值
        handlePopup() {
            this.getDataList();
        }
    }
};
</script>
<style lang="scss" scoped>
.basic-process-view {
    display: flex;

    .basic-process-main {
        flex: 1;
        width: 100%;
    }
}
</style>

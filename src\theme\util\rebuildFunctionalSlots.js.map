{"version": 3, "sources": ["../../src/util/rebuildFunctionalSlots.ts"], "names": [], "mappings": ";;;;;;;AAEc,SAAU,sBAAV,CAAkC,KAAlC,EAAiF,CAAjF,EAAiG;AAC7G,MAAM,QAAQ,GAAY,EAA1B;;AAEA,OAAK,IAAM,IAAX,IAAmB,KAAnB,EAA0B;AACxB,QAAI,KAAK,CAAC,cAAN,CAAqB,IAArB,CAAJ,EAAgC;AAC9B,MAAA,QAAQ,CAAC,IAAT,CAAc,CAAC,CAAC,UAAD,EAAa;AAAE,QAAA,IAAI,EAAJ;AAAF,OAAb,EAAuB,KAAK,CAAC,IAAD,CAA5B,CAAf;AACD;AACF;;AAED,SAAO,QAAP;AACD", "sourcesContent": ["import { CreateElement, VNode } from 'vue'\n\nexport default function rebuildFunctionalSlots (slots: { [key: string]: VNode[] | undefined }, h: CreateElement) {\n  const children: VNode[] = []\n\n  for (const slot in slots) {\n    if (slots.hasOwnProperty(slot)) {\n      children.push(h('template', { slot }, slots[slot]))\n    }\n  }\n\n  return children\n}\n"], "sourceRoot": "", "file": "rebuildFunctionalSlots.js"}
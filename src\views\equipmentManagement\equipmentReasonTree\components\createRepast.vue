<template>
    <v-dialog v-model="showDialog" max-width="980px">
        <v-card class="" v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form.RepairProject" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_GZZSK.RepairProject')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <!-- multiple -->
                            <v-select v-model="form.PersonCode" :items="ressionList" :label="$t('$vuetify.dataTable.TPM_SBGL_GZZSK.PersonName')" item-value="id" item-text="name" return-object outlined dense></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form.CheckStandard" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_GZZSK.CheckStandard')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form.Tools" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_GZZSK.Tools')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="form.Methods" rows="2" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_GZZSK.Methods')"></v-textarea>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="form.Remark" rows="2" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_GZZSK.Remark')"></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'edit'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._BJ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="editedItem.RepairProject" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_GZZSK.RepairProject')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-select v-model="editedItem.PersonCode" :items="ressionList" :label="$t('$vuetify.dataTable.TPM_SBGL_GZZSK.PersonName')" item-value="id" item-text="name" return-object outlined dense></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="editedItem.CheckStandard" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_GZZSK.CheckStandard')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="editedItem.Tools" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_GZZSK.Tools')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="editedItem.Methods" rows="2" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_GZZSK.Methods')"></v-textarea>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="editedItem.Remark" rows="2" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_GZZSK.Remark')"></v-textarea>
                        </v-col>
                    </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions pa-4 class="lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { DeviceRepairProjectSaveForm } from '@/api/equipmentManagement/equipmentReasonTree.js';
export default {
    props: {
        repastTypelist: {
            type: Array,
            default: () => []
        },
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        },
        ressionList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            form: {
                RepairProject: '',
                CheckStandard: '',
                PersonCode: '',
                PersonName: '',
                Methods: '',
                Tools: '',
                Remark: ''
            }
        };
    },
    computed: {
        editedItem() {
            const { RepairProject, CheckStandard, PersonCode, PersonName, Methods, Tools, Remark } = this.tableItem;
            return {
                RepairProject,
                CheckStandard,
                PersonCode,
                PersonName,
                Methods,
                Tools,
                Remark
            };
        }
    },

    methods: {
        closeEquip() {
            this.showDialog = false;
            this.$refs.form.reset();
        },
        async addSave(type) {
            const paramsKey = Object.keys(this.form);
            const paramsObj = type == 'add' ? this.form : this.editedItem;
            let params = {};
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            const { PersonCode } = paramsObj;
            let obj = {};
            if (PersonCode.id) {
                const { id, name } = PersonCode;
                obj = { PersonCode: id, PersonName: name };
            }
            if (type == 'edit') {
                params.ID = this.tableItem.ID;
            }
            Object.assign(params, obj);
            const res = await DeviceRepairProjectSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.$parent.RepastInfoGetPage();
                this.showDialog = this.classcheckbox ? false : true;
            }
        }
    }
};
</script>

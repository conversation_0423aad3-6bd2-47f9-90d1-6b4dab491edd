<template>
    <div class="dictionary-view">
        <TreeView :items="treeData" :activeKey="' '" :title="$t('TPM_SBGL_SBTZGL._SBFL')" @clickClassTree="clickClassTree"></TreeView>
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'SBGLZY_ADD'" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="primary" v-has="'SBGLZY_ALLREMOVE'" :disabled="!deleteList.length" @click="btnClickEvet('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn>
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_SBFLGL"
                    ref="Tables"
                    :headers="ClassificationColum"
                    :desserts="desserts"
                    @selectePages="selectePages"
                    :clickFun="clickFun"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"
                >
                    <template #Status="{ item }">
                        <v-switch v-model="item.Status" style="height: 30px; transform: scale(0.8); transform-origin: left" class="ma-0 ba-0" label="" @click.stop="updateStatus(item)"></v-switch>
                    </template>
                </Tables>
                <createRepast :ParentId="papamstree.ParentId" ref="createRepast" :dialogType="dialogType" :tableItem="tableItem"></createRepast>
            </v-card>
        </div>
        <el-drawer size="80%" :title="rowtableItem.Name + ' | ' + rowtableItem.Code" :wrapperClosable="false" :visible.sync="detailShow" direction="rtl">
            <v-card class="ma-1">
                <v-tabs v-model="tab" background-color="transparent">
                    <v-tab @click="changeTab(0)" key="0">{{ $t('TPM_SBGL_SBTZGL._BPBJQD') }}</v-tab>
                    <v-tab @click="changeTab(1)" key="1">{{ $t('TPM_SBGL_SBTZGL._SBBOM') }}</v-tab>
                    <v-tab @click="changeTab(2)" key="2">{{ $t('TPM_SBGL_SBTZGL._WJ') }}</v-tab>
                    <v-tab @click="changeTab(3)" key="3">{{ $t('TPM_SBGL_SBTZGL._SXPZ') }}</v-tab>
                </v-tabs>
                <v-tabs-items v-model="tab">
                    <v-tab-item>
                        <sparepartsList ref="sparepartsList" :rowtableItem="rowtableItem"></sparepartsList>
                    </v-tab-item>
                    <v-tab-item>
                        <equipmentBom ref="equipmentBom" :rowtableItem="rowtableItem"></equipmentBom>
                    </v-tab-item>
                    <v-tab-item>
                        <equipmentFile ref="equipmentFile" :rowtableItem="rowtableItem"></equipmentFile>
                    </v-tab-item>
                    <v-tab-item>
                        <DeviceCategoryProp ref="DeviceCategoryProp" :rowtableItem="rowtableItem"></DeviceCategoryProp>
                    </v-tab-item>
                </v-tabs-items>
            </v-card>
        </el-drawer>
        <div class="loading-box" v-if="importLoading">
            <a-spin tip="导入中..." :spinning="importLoading"></a-spin>
        </div>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';
import { mixins } from '@/util/mixins.js';
import { GetDeviceTree, GetDevicePageList, DeviceDelete } from '@/api/equipmentManagement/classification.js';
import { ClassificationColum } from '@/columns/equipmentManagement/classification.js';
import sparepartsList from '../Equip/sparepartslist';
import equipmentBom from '../Equip/equipmentBom.vue';
import equipmentFile from '../Equip/equipmentFile.vue';
import DeviceCategoryProp from './DeviceCategoryProp';
export default {
    name: '',
    components: {
        sparepartsList, //被品备件
        equipmentBom, //设备BOM
        equipmentFile, // 设备文件
        DeviceCategoryProp, //属性配置
        createRepast: () => import('./components/createRepast.vue')
    },
    mixins: [mixins],
    data() {
        return {
            detailShow: false,
            // tree 字典数据
            importLoading: false,
            tab: 0,
            treeData: [],
            loading: false,
            showFrom: false,
            papamstree: {
                ParentId: '',
                Code: '',
                Name: '',
                pageIndex: 1,
                pageSize: 20,
                orderByFileds: 'Code asc'
            },
            //查询条件
            ClassificationColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            rowtableItem: {},
            deleteList: [] //批量选中
        };
    },
    computed: {
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'Code',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBFLGL.classcode'),
                    placeholder: this.$t('TPM_SBGL_SBFLGL.classcode')
                },
                {
                    value: '',
                    key: 'Name',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBFLGL.classname'),
                    placeholder: this.$t('TPM_SBGL_SBFLGL.classname')
                }
            ];
        },
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    authCode: 'SBGLZY_EDIT',
                    icon: ''
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    authCode: 'SBGLZY_DELETE',
                    code: 'delete',
                    type: 'red',
                    icon: ''
                }
            ];
        }
    },
    mounted() {
        this.GetFactorylineTree();
    },
    methods: {
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        },
        // 删除列表
        SelectedItems(item) {
            this.deleteList = item;
        },
        // 树状点击获取
        clickClassTree(v) {
            this.papamstree.pageIndex = 1;
            if (v.id == this.papamstree.ParentId) {
                this.papamstree.ParentId = '';
            } else {
                this.papamstree.ParentId = v.id;
            }
            this.RepastInfoGetPage();
        },
        // 获取树形数据
        async GetFactorylineTree() {
            let params = {};
            console.log(this.$route);
            params.factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            const res = await GetDeviceTree(params);
            let { success, response } = res;
            if (success) {
                this.treeData = response || [];
                this.papamstree.ParentId = '';
            }
            this.RepastInfoGetPage();
        },
        // tree转化为一维数组
        getTreeChild(tree) {
            tree.forEach(e => {
                this.floors.push(e);
                const { children } = e;
                if (children && children[0]) {
                    this.getTreeChild(children);
                }
            });
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.RepastInfoGetPage();
        },
        // 设备列表查询
        async RepastInfoGetPage() {
            let params = {
                ...this.papamstree
            };
            this.loading = true;
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            const res = await GetDevicePageList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await DeviceDelete(params);
                    if (res.success) {
                        this.tableItem = {};
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.RepastInfoGetPage();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        clickFun(data) {
            this.tableItem = data;
            this.rowtableItem = data || {};
            this.detailShow = true;
            switch (this.tab) {
                case 0:
                    setTimeout(() => {
                        this.$refs.sparepartsList.GetSparepartGetPageList(this.rowtableItem);
                    }, 10);
                    break;
                case 1:
                    setTimeout(() => {
                        this.$refs.equipmentBom.RepastBOMlistTARGetPage(this.rowtableItem);
                    }, 10);
                    break;
                case 2:
                    setTimeout(() => {
                        this.$refs.equipmentFile.RepastInfoTARGetPage(this.rowtableItem);
                    }, 10);
                    break;
                case 3:
                    setTimeout(() => {
                        this.$refs.DeviceCategoryProp.RepastInfoPropGetPage(this.rowtableItem);
                    }, 10);
                    break;
            }
        },
        changeTab(v) {
            switch (v) {
                case 0:
                    setTimeout(() => {
                        this.$refs.sparepartsList.GetSparepartGetPageList(this.rowtableItem);
                    }, 10);
                    break;
                case 1:
                    setTimeout(() => {
                        this.$refs.equipmentBom.RepastBOMlistTARGetPage(this.rowtableItem);
                    }, 10);
                    break;
                case 2:
                    setTimeout(() => {
                        this.$refs.equipmentFile.RepastInfoTARGetPage(this.rowtableItem);
                    }, 10);
                    break;
                case 3:
                    setTimeout(() => {
                        this.$refs.DeviceCategoryProp.RepastInfoPropGetPage(this.rowtableItem);
                    }, 10);
                    break;
            }
        },
        // 表单操作
        tableClick(item, type) {
            console.log(item, 123);
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.$refs.createRepast.showDialog = true;
                    this.$refs.createRepast.SbxxList.forEach(item => {
                        for (let k in this.tableItem) {
                            if (item.id == k) {
                                item.value = this.tableItem[k];
                            }
                        }
                    });
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        }
        // 删除列表
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}

.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

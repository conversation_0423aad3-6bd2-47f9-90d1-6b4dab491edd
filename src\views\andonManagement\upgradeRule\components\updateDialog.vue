<template>
    <!-- 告警升级规则 -->
    <v-dialog v-model="dialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ operaObj.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ') }}
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" class="mt-8 mb-2">
                    <v-row>
                        <!-- 一级分类 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.MainAlarmType"
                                :rules="rules.MainAlarmType"
                                :items="typeRootList"
                                item-text="AlarmName"
                                item-value="AlarmCode"
                                :label="$t('$vuetify.dataTable.ANDON_BJSJGZ.MainAlarmType')"
                                return-object
                                dense
                                outlined
                                @change="changeV"
                            ></v-select>
                        </v-col>
                        <!-- 二级分类 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.SubAlarmType"
                                :rules="rules.SubAlarmType"
                                :items="typeChildList"
                                item-text="AlarmName"
                                item-value="AlarmCode"
                                :label="$t('$vuetify.dataTable.ANDON_BJSJGZ.SubAlarmType')"
                                return-object
                                dense
                                outlined
                            ></v-select>
                        </v-col>
                        <!-- 事件等级 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.EventLevel" :rules="rules.EventLevel" :label="$t('$vuetify.dataTable.ANDON_BJSJGZ.EventLevel')" required dense outlined />
                        </v-col>
                        <!-- 升级时间 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model.number="form.OutTime" :rules="rules.OutTime" :label="$t('$vuetify.dataTable.ANDON_BJSJGZ.OutTime')" required dense outlined />
                        </v-col>
                        <!-- :通知方式 -->
                        <v-col :cols="12" :lg="6">
                            <v-combobox
                                v-model="form.NoticeType"
                                :items="noticeTypeList"
                                :rules="rules.NoticeType"
                                multiple
                                :label="$t('$vuetify.dataTable.ANDON_BJSJGZ.NoticeType')"
                                item-text="ItemName"
                                item-value="ItemValue"
                                persistent-hint
                                clearable
                                dense
                                outlined
                            >
                                <template #no-data>
                                    <v-list-item>
                                        <v-list-item-content>no data</v-list-item-content>
                                    </v-list-item>
                                </template>
                                <template v-slot:selection="{ item, index }">
                                    <span v-if="index === 0">{{ item.ItemName }}</span>
                                    <span v-if="index === 1">&nbsp;&nbsp;</span>
                                    <span v-if="index === 1" class="grey--text caption">(+{{ form.NoticeType.length - 1 }} )</span>
                                </template>
                            </v-combobox>
                        </v-col>
                        <!-- 处置方式 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.DealMode"
                                :rules="rules.DealMode"
                                :items="dealModeList"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="$t('$vuetify.dataTable.ANDON_BJSJGZ.DealMode')"
                                dense
                                outlined
                            ></v-select>
                        </v-col>
                        <!-- 负责人类型 -->
                        <v-col :cols="12" :lg="12">
                            <v-select
                                v-model="form.NoterType"
                                :rules="rules.NoterType"
                                @change="NoterTypeChange"
                                :items="NoterTypeList"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="$t('$vuetify.dataTable.ANDON_BJSJGZ.NoterType')"
                                dense
                                outlined
                            ></v-select>
                        </v-col>
                        <!-- 负责人部门 -->
                        <!-- <v-col ref="deptSelect" :cols="12" :lg="6" class="pt-0 pb-0  mb-5 " v-if="userTag">
                            <Treeselect placeholder="负责人部门" noChildrenText="暂无数据" noOptionsText="暂无数据" 
                                :default-expand-level="4" :normalizer="normalizer" :options="EquipmentTeamTree"
                                @select="handleChangeSelectTree" :rules="rules" />
                        </v-col> -->
                        <!-- 负责人 -->
                        <!-- :search-input.sync="search2" -->
                        <v-col ref="userSelect" :cols="12" :lg="12" v-if="userTag">
                            <v-combobox
                                v-model="form.Noter"
                                class="white-bk"
                                :label="$t('$vuetify.dataTable.ANDON_BJSJGZ.Noter')"
                                :items="NoterList"
                                item-text="UserName"
                                item-value="LoginName"
                                @change="NoterChange"
                                dense
                                outlined
                                clearable
                                multiple
                            ></v-combobox>
                        </v-col>
                        <!-- 负责人角色 -->
                        <v-col ref="roleSelect" :cols="12" :lg="12" v-if="roleTag">
                            <v-combobox
                                v-model="form.NoterDepart"
                                :items="NoterDepartList"
                                item-text="Name"
                                item-value="ID"
                                :rules="rules.NoterDepart"
                                :multiple="true"
                                :label="$t('$vuetify.dataTable.ANDON_BJSJGZ.NoterDepart')"
                                :search-input.sync="search"
                                persistent-hint
                                clearable
                                dense
                                outlined
                            >
                                <template #no-data>
                                    <v-list-item>
                                        <v-list-item-content>no data</v-list-item-content>
                                    </v-list-item>
                                </template>
                            </v-combobox>
                        </v-col>
                        <!-- 负责人职位 -->
                        <v-col ref="dutySelect" :cols="12" :lg="12" v-if="dutyTag">
                            <!-- <v-select
                                v-model="form.Dutys"
                                :rules="rules.Dutys"
                                :items="departmentData"
                                :multiple="true"
                                label="负责人职位"
                                dense
                                outlined
                            >
                            </v-select> -->
                            <v-combobox
                                v-model="form.Dutys"
                                :items="departmentData"
                                :rules="rules.Dutys"
                                :multiple="true"
                                :label="$t('$vuetify.dataTable.ANDON_BJSJGZ.Dutydetail')"
                                item-text="Name"
                                item-value="Name"
                                :search-input.sync="search"
                                persistent-hint
                                clearable
                                dense
                                outlined
                            >
                                <template #no-data>
                                    <v-list-item>
                                        <v-list-item-content>no data</v-list-item-content>
                                    </v-list-item>
                                </template>
                            </v-combobox>
                            <!-- <Treeselect
                                noChildrenText="暂无数据"
                                noOptionsText="暂无数据"
                                :multiple="true"
                                :normalizer="normalizer"
                                :options="departmentData"
                                :limit="2"
                                :disable-branch-nodes="true"
                                placeholder="负责人职位"
                                @select="handleChangeSelectTree"
                                v-model="form.Dutys"
                            /> -->
                        </v-col>
                        <!-- 响应等级 -->
                        <!-- <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.ResponseLevel"
                                :rules="rules.ResponseLevel"
                                :items="ResponseLevelSendList"
                                item-text="ItemName"
                                item-value="ItemValue"
                                label="响应等级"
                                dense
                                outlined
                            ></v-select>
                        </v-col> -->
                    </v-row>
                </v-form>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn color="normal" @click="closeForm">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { UpgradeRuleSaveForm, StaffGetAndonPostList } from '@/api/andonManagement/upgradeRule.js';
import { getAlarmTypeRootList, GetListByAlarmId } from '@/api/andonManagement/alarmType.js';
import { GetFilteredUsersList } from '../../../systemManagement/userManagement/service.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        noticeTypeList: {
            type: Array,
            default: () => []
        },
        dealModeList: {
            type: Array,
            default: () => []
        },
        NoterDepartList: {
            type: Array,
            default: () => []
        },
        NoterTypeList: {
            type: Array,
            default: () => []
        },
        dealTypeList: {
            type: Array,
            default: () => []
        },
        dutyList: {
            type: Array,
            default: () => []
        },
        IsDownAllSendList: {
            type: Array,
            default: () => []
        },
        ResponseLevelSendList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            search: null,
            checkbox: true,
            valid: true,
            dialog: false,
            userTag: false,
            roleTag: false,
            dutyTag: false,
            form: {
                ID: '',
                Dutys: [],
                MainAlarmType: '',
                SubAlarmType: '',
                EventLevel: '',
                NoticeType: [],
                NoterList: [],
                DealMode: '',
                DealType: '',
                ResponseLevel: '',
                IsDownAllSend: '',
                OutTime: '',
                NoterType: '',
                Noter: [],
                NoterCode: '',
                NoterName: '',
                NoterDepart: []
            },
            rules: {
                // Dutys: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                MainAlarmType: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                SubAlarmType: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                OutTime: [v => !!v || v === 0 || this.$t('GLOBAL._MANDATORY')],
                NoticeType: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                DealMode: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                ResponseLevel: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                // IsDownAllSend: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                EventLevel: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            },
            departmentData: [],
            typeRootList: [],
            typeChildList: [],
            NoterList: [],
            // normalizer(node) {
            //     return {
            //         id: node.id,
            //         label: node.name,
            //         children: node.children
            //     };
            // }
            normalizer(node) {
                const { children, id, name } = node;
                const o = { id: id, label: name, children };
                return o;
            }
        };
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal) {
                    const { MainAlarmType, Dutys, IsDownAllSend, NoticeType, DealMode, NoterType, NoterCode, NoterName, NoterDepart } = this.operaObj;
                    if (MainAlarmType) {
                        const o = this.typeRootList.find(i => i.AlarmCode == MainAlarmType);
                        if (o) this.changeV(o);
                    }
                    for (const key in this.form) {
                        if (Object.hasOwnProperty.call(this.form, key)) {
                            this.form[key] = this.operaObj[key];
                        }
                    }
                    this.form.NoticeType = [];
                    if (NoticeType.split(',').length > 0) {
                        NoticeType.split(',').forEach(item => {
                            let obj = this.noticeTypeList.find(itm => itm.ItemValue == item);
                            this.form.NoticeType.push(obj);
                        });
                    }
                    this.form.Dutys = [];
                    if (!IsDownAllSend) this.form.IsDownAllSend = 'yes';
                    if (Dutys) {
                        const a = Dutys.split('|');
                        a.forEach(e => {
                            const v = this.departmentData.find(j => j.Name == e);
                            if (v) this.form.Dutys.push(v);
                        });
                    }
                    this.NoterTypeChange(NoterType);
                    if (this.dealModeList.length > 0) {
                        let dealModelValue = this.dealModeList.find(i => i.ItemValue == DealMode);
                        this.form.DealMode = dealModelValue;
                    }

                    // if(this.NoterTypeList.length > 0){
                    //     this.form.NoterType = this.NoterTypeList.find(i => i.ItemValue == NoterType)
                    // }
                    this.form.Noter = [];
                    if (NoterCode && NoterCode.split(',').length > 0) {
                        var noterList = NoterCode.split(',');
                        var noterNameList = NoterName.split(',');
                        for (var i = 0; i < noterList.length; i++) {
                            var item = noterList[i];
                            if (this.NoterList.length > 0) {
                                let obj = this.NoterList.find(a => a.LoginName == item);
                                this.form.Noter.push(obj);
                            } else {
                                var noter = { LoginName: item, UserName: noterNameList[i] };
                                this.form.Noter.push(noter);
                            }
                        }
                    }
                    this.form.NoterDepart = [];
                    if (NoterDepart && NoterDepart.split(',').length > 0) {
                        NoterDepart.split(',').forEach(item => {
                            let obj = this.NoterDepartList.find(a => a.ID == item);
                            this.form.NoterDepart.push(obj);
                        });
                    }
                }
            },
            deep: true,
            immediate: true
        }
    },
    created() {
        // this.$store.dispatch('getEquipmentTeamTree', "Team");
        this.getDepartmentData();
        this.getTypeRootList();
        this.handleChangeSelectTree();
    },
    computed: {
        EquipmentTeamTree() {
            return this.$store.state.sim.EquipmentTeamTree;
        }
    },
    methods: {
        // 获取大类列表
        async getTypeRootList() {
            const res = await getAlarmTypeRootList({});
            const { success, response } = res || {};
            if (success) {
                this.typeRootList = response;
            } else {
                this.typeRootList = [];
            }
        },
        // 选择一级告警时获取子级
        async changeV(o) {
            this.typeChildList = [];
            this.form.SubAlarmType = '';
            const res = await GetListByAlarmId({ alarmId: o.ID });
            const { success, response } = res || {};
            if (success) {
                this.typeChildList = response;
            }
        },
        NoterTypeChange(val) {
            if (val == '1') {
                this.userTag = true;
                this.dutyTag = false;
                this.roleTag = false;
            }
            if (val == '2') {
                this.userTag = false;
                this.dutyTag = true;
                this.roleTag = false;
            }
            if (val == '3') {
                this.userTag = false;
                this.dutyTag = false;
                this.roleTag = true;
            }
        },
        NoterChange(v) {
            this.form.NoterCode = v.map(item => item.LoginName).join(',');
            this.form.NoterName = v.map(item => item.UserName).join(',');
            console.log('NoterCode:', this.form.NoterCode);
            console.log('NoterName:', this.form.NoterName);
        },
        async handleChangeSelectTree(val) {
            const res = await GetFilteredUsersList();
            const { success, response } = res || {};
            if (success) {
                this.NoterList = response;
            }
        },
        findChildren(treeData) {
            for (var i = 0; i < treeData.length; i++) {
                if (treeData[i].children.length) {
                    treeData[i].children = this.findChildren(treeData[i].children);
                } else {
                    delete treeData[i].children;
                }
            }
            return treeData;
        },
        // 数组处理
        handlerData(treeData) {
            for (var i = 0; i < treeData.length; i++) {
                if (treeData[i].children.length) {
                    treeData[i].children = this.findChildren(treeData[i].children);
                } else {
                    delete treeData[i].children;
                }
            }
            return treeData;
        },
        async getDepartmentData() {
            let resp = await StaffGetAndonPostList({ Type: 'Andonwork' });
            const { success, response } = resp || {};
            if (success) {
                this.departmentData = response;
            }
        },
        //关闭
        closeForm() {
            this.$emit('handlePopup', 'refresh');
            this.dialog = false;
        },
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                let infos = {},
                    str = '';
                const { MainAlarmType, SubAlarmType, DealMode, NoterType, NoticeType, Dutys, Noter, NoterDepart } = this.form;
                if (MainAlarmType?.AlarmCode) {
                    infos = { MainAlarmType: MainAlarmType.AlarmCode };
                }
                if (SubAlarmType?.AlarmCode) {
                    infos = { ...infos, SubAlarmType: SubAlarmType.AlarmCode };
                }
                if (DealMode?.ItemValue) {
                    infos = { ...infos, DealMode: DealMode.ItemValue };
                }
                if (NoticeType.length > 0) {
                    infos = { ...infos, NoticeType: NoticeType.map(item => item.ItemValue).join(',') };
                } else {
                    infos = { ...infos, NoticeType: null };
                }
                if (NoterType) {
                    infos = { ...infos, NoterType: NoterType };
                }
                if (NoterDepart && NoterDepart.length > 0) {
                    infos = { ...infos, NoterDepart: NoterDepart.map(item => item.ID).join(',') };
                } else {
                    infos = { ...infos, NoterDepart: null };
                }
                if (this.form.NoterType === '2') {
                    if (Dutys.length) {
                        Dutys.forEach((e, k) => {
                            str += k == Dutys.length - 1 ? e.Name : `${e.Name}|`;
                        });
                    }
                }
                const res = await UpgradeRuleSaveForm({ ...this.form, ...infos, Dutys: str });
                const { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    this.$refs.form.reset();
                    this.$emit('handlePopup', 'refresh');
                    if (this.operaObj.ID || this.checkbox) {
                        this.closeForm();
                    }
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>
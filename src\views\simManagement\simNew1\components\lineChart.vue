<template>
  <div>
    <div style="display: flex;width: 100%;height: 30px;">
      <!-- <div
        style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;cursor: pointer;"
        @click="routeChange()"
      >{{ title1 }}</div> -->
      <div
        style="width: 70%;"
        class="titimgbox"
        @click="routeChange()"
      >
        <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
        <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ title1 }}</div>
      </div>
      <div style="width: 30%;display: flex;">
        <dayMonIndex
          :key="dayMonIndexkey"
          :simlevel='simlevel'
          :position="Order"
          @showChack="getExcelline"
          @showChack1="getExcelbar1"
          :Dimension="Dimension"
          :Particle="Particle"
          :id1="id1"
          :BaseTime="BaseTime"
          :titleline="title"
          :echarstType="1"
          :backgroundImg="backgroundImg"
          :legendData="legendData"
          :resultArray1="resultArray1"
          :colorList="colorList"
        />
      </div>
    </div>
    <div
      @click="openPopup()"
      :id="id1"
      style="width:100%;height:100%;"
    ></div>
    <!-- <div
      v-if="!showData"
      style="font-size: 14px;color: #fff;text-align: center;margin-top: -100px;"
    >暂无数据</div> -->
    <!-- <keyIndicatorslist
      ref="keyIndicatorsref"
      :exhibitionType="exhibitionType"
      :jtitle="jtitle"
    ></keyIndicatorslist> -->

    <keyIndicatorslistnew
      ref="keyIndicatorsrefnew"
      v-if="keyIndicatorslistnewShow"
      v-model="showkeyIndicatorslistnew"
      :exhibitionType="exhibitionType"
      :jtitle="title"
      :simlevel="simlevel"
      :Order="Order"
      :isSql="0"
      :BaseTime="BaseTime1"
      :BaseTime2="BaseTime"
      :barName="barName"
      :backgroundImg="backgroundImg"
      :Particle="Particle1"
      :legendData="legendData"
      @keynew="heandleKeybar"
    ></keyIndicatorslistnew>

  </div>
</template>
<script>
import { getqueryZ, getqueryLcr, getChartStructure, getTableList } from '@/api/simConfig/simconfignew.js';

export default {
  components: {
    dayMonIndex: () => import('@/views/simManagement/simNew1/components/dayMonIndex.vue'),
    // keyIndicatorslist: () => import('@/views/simManagement/simNew1/components/keyIndicatorslist.vue'),
    keyIndicatorslistnew: () => import('@/views/simManagement/simNew1/components/keyIndicatorslistnew.vue'),
  },
  props: {
    id1: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    Order: {
      type: String,
      default: ''
    },
    configFlag: {
      type: String,
      default: ''
    },
    exhibitionType: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    Dimension: {
      type: Array,
      default: () => []
    },
    routeList: {
      type: Array,
      default: () => []
    },
    backgroundImg: {
      type: String,
      default: ''
    },
    colorList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      labelShow: false,
      legendData1: [],
      BaseTime1: '',
      dayMonIndexkey: 0,
      resultArray1: [],
      Particle1: '',
      legendDatanew: [],
      legendData: [],
      title1: '',
      keyIndicatorslistnewShow: false,
      showkeyIndicatorslistnew: false,
      showData: true,
      Particle: '',
      jtitle: '',
      // BaseTime: '2024-08-01',
      // TeamCode: 'A1180240614',
      // ProductionLineCode: 'A11802406',
      // FactoryCode: 'A118024',
      chartLine: null,
      //当前时间颗粒度
      curShift: {
        KpiValues: []
      },
      myShiftList: [],
      id: '',
      yAxisOption: {
        type: 'value',
        // show: false
        axisLine: {
          show: true,
          lineStyle: {
            color: '#fff'
          }
        },
        axisLabel: {
          show: true
        },
        axisTick: {
          show: false
        },
      },
    }
  },
  computed: {
    //x轴配置
    xAxisOption() {
      let list = ['7', '6', '5', '4', '3', '2', '1']
      if (this.curShift.ChartData && this.curShift.ChartData.x) {
        list = this.curShift.ChartData.x.map(item => {
          // let month = dayjs(item).$M+1
          // let day = dayjs(item).$D
          if (['日', '周'].includes(this.curShift.TimeDimension)) {
            let month = item.split('-')[1]
            let day = item.split('-')[2]
            // 这里需要匹配颗粒度,来输出不同的x轴数据.
            // return `${month}-${day}`
            return `${month}-${day}`
          } else if (['月', '季度'].includes(this.curShift.TimeDimension)) {
            let label = item.split('年')[1]
            return label
          } else {
            return item
          }
        })
        // list.reverse()
        // list = this.curShift.ChartData.x
      }
      return {
        type: 'category',
        // boundaryGap: false,
        axisLine: {
          onZero: false,
          lineStyle: {
            color: '#fff'
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff'
        },
        data: list
      }
    },
    //折线图配置
    lineSeries() {
      console.log(this.curShift.ChartData.x.length, '8765');

      if (this.curShift.ChartData.x.length <= 0) {
        this.$nextTick(() => {
          const dom = document.getElementById(this.id1);
          dom.innerHTML = '<div class="noDataBox">暂无数据</div>';
          dom.removeAttribute('_echarts_instance_');
          return
        })
      }
      let dataObject = []
      if (localStorage.getItem('list') != null) {
        dataObject = JSON.parse(localStorage.getItem('list'));
      }
      const resultArray = [];
      if (dataObject[this.Order]) {
        resultArray.push(...dataObject[this.Order]);
        [...new Set(resultArray)]
      }
      this.resultArray1 = [...new Set(resultArray)] // eslint-disable-line
      //区分横竖
      let axisMarkLine = this.curConfig.ChartType === '4'
        ? [{ xAxis: this.curShift?.TargetValue || '' }]
        : [{ yAxis: this.curShift?.TargetValue || '' }]
      let list = []
      let legendData = []
      Object.keys(this.curShift.ChartData).map(el => {
        legendData.push(el)
        const sorted = legendData.sort((a, b) => {
          const aParts = a.split(';');
          const bParts = b.split(';');
          const mainPartA = aParts.slice(0, -1).join(';');
          const mainPartB = bParts.slice(0, -1).join(';');
          return mainPartA.localeCompare(mainPartB);
        });
        this.legendData = [...new Set(sorted)]
        this.legendData1 = [...new Set(sorted)]
      })
      let result = [];
      this.legendData1.map(item => {
        this.resultArray1.map(el => {
          if (item.includes(el)) {
            result.push(item);
          }
        })
      });
      const result2 = [];
      result.map(item => {
        if (result2.indexOf(item) === -1) {
          result2.push(item);
        }
      });
      this.resultArray1 = result2 // eslint-disable-line

      if (this.resultArray1.length > 0) {
        this.labelShow = true // eslint-disable-line
        this.legendData = this.legendData.filter(item => { // eslint-disable-line
          return !item.includes('目标值') && !item.includes('上限') && !item.includes('下限');
        });
        var colors = this.colorList
        this.dayMonIndexkey++ // eslint-disable-line
        this.resultArray1.map((key, index) => {
          // if (['x', 'x', '目标值'].includes(key)) {
          //   return
          // }
          let obj = {
            // name: `${this.curShift.KpiName}实际值`,
            // name: key.split(':')[1],
            name: `${key}`,
            type: ['2', '3'].includes(this.curConfig.ChartType) ? 'bar' : 'line',
            symbol: 'circle',
            symbolSize: 14,
            itemStyle: {
              normal: {
                // color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                //   offset: 0,
                //   color: "#4391F4" // 0% 处的颜色
                // }, {
                //   offset: 1,
                //   color: "#6B74E4" // 100% 处的颜色
                // }], false),
                // color: function (params) {
                //   let r, g, b;
                //   do {
                //     r = Math.floor(Math.random() * 256);
                //     g = Math.floor(Math.random() * 256);
                //     b = Math.floor(Math.random() * 256);
                //   } while ((r > 200 && g < 50 && b < 50) || // 类似红色的范围判断
                //   (r > 150 && g < 100 && b < 100) ||
                //     (r > 100 && g < 150 && b < 150));
                //   return `rgb(${r},${g},${b})`;
                // },
                color: colors[index % colors.length],
                barBorderRadius: [4, 4, 4, 4],
              }
            },

            // data: this.curShift.KpiValues.map(item=>item.DataValue),
            data: this.curShift.ChartData[key],
            label: {
              show: this.labelShow,
              position: 'top',
              textStyle: {
                color: '#fff'
              }
            },
            // markLine: {//目标值线条
            //   silent: true,
            //   lineStyle: {
            //     color: this.curShift.TargetColor || 'gray'
            //     // color: 'red'
            //   },
            //   data: axisMarkLine
            //   // data: [{xAxis: 20 }]
            // }
          }
          list.push(obj)
        })
      }
      if (this.legendDatanew.length <= 0 && this.resultArray1.length <= 0) {
        if (this.legendData.length == 2) {
          this.labelShow = true // eslint-disable-line
        } else {
          this.labelShow = false // eslint-disable-line
        }
        var colors1 = this.colorList
        if (this.curShift?.ChartData) {
          Object.keys(this.curShift?.ChartData).forEach((key, index) => {
            legendData.push(key)
            const sorted = legendData.sort((a, b) => {
              const aParts = a.split(';');
              const bParts = b.split(';');
              const mainPartA = aParts.slice(0, -1).join(';');
              const mainPartB = bParts.slice(0, -1).join(';');
              return mainPartA.localeCompare(mainPartB);
            });
            this.legendData = [...new Set(sorted)]
            this.legendData1 = [...new Set(sorted)]
            this.legendData = this.legendData.filter(item => {
              return !item.includes('目标值') && !item.includes('上限') && !item.includes('下限');
            });
            if (['x', 'x', '目标值'].includes(key)) {
              return
            }
            let obj = {
              // name: `${this.curShift.KpiName}实际值`,
              // name: key.split(':')[1],
              name: `${key}`,
              type: ['2', '3'].includes(this.curConfig.ChartType) ? 'bar' : 'line',
              symbol: 'circle',
              symbolSize: 14,
              itemStyle: {
                normal: {
                  // color: function (params) {
                  //   let r, g, b;
                  //   do {
                  //     r = Math.floor(Math.random() * 256);
                  //     g = Math.floor(Math.random() * 256);
                  //     b = Math.floor(Math.random() * 256);
                  //   } while ((r > 200 && g < 50 && b < 50) || // 类似红色的范围判断
                  //   (r > 150 && g < 100 && b < 100) ||
                  //     (r > 100 && g < 150 && b < 150));
                  //   return `rgb(${r},${g},${b})`;
                  // },
                  // color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  //   offset: 0,
                  //   color: "#4391F4" // 0% 处的颜色
                  // }, {
                  //   offset: 1,
                  //   color: "#6B74E4" // 100% 处的颜色
                  // }], false),
                  color: colors1[index % colors1.length],
                  barBorderRadius: [4, 4, 4, 4],
                }
              },

              // data: this.curShift.KpiValues.map(item=>item.DataValue),
              data: this.curShift.ChartData[key],
              label: {
                show: this.labelShow,
                position: 'top',
                textStyle: {
                  color: '#fff'
                }
              },
              // markLine: {//目标值线条
              //   silent: true,
              //   lineStyle: {
              //     color: this.curShift.TargetColor || 'gray'
              //     // color: 'red'
              //   },
              //   data: axisMarkLine
              //   // data: [{xAxis: 20 }]
              // }
            }
            list.push(obj)
          })
        }
      }
      list.map(el => {
        if (el.name.includes('目标值') || el.name.includes('上限') || el.name.includes('下限')) {
          el.type = 'line'
        }
      })
      return list
    },
  },
  mounted() {
  },
  created() {
    this.$nextTick(() => {
      this.getBarList()
    })
  },
  methods: {
    heandleKeybar() {
      this.keyIndicatorslistnewShow = false
    },
    routeChange() {
      if (this.routeList != '' && this.routeList != null && this.routeList != undefined) {
        this.$router.push({ path: `${this.routeList}` })
      }
      // this.$router.push({ path: 'simNew2', query: { code: item.Simcode } });
    },
    openPopup() {
      if (this.configFlag == '是') {
        this.$refs.keyIndicatorsref.showDialog = true;
      }
    },
    getExcelline(data) {
      this.Particle = data
      this.getBarList()
    },
    getExcelbar1(data) {
      this.legendDatanew = data
      let result = [];
      this.legendData1.map(item => {
        data.map(el => {
          if (item.includes(el)) {
            result.push(item);
          }
        })
      });
      const result2 = [];
      result.map(item => {
        if (result2.indexOf(item) === -1) {
          result2.push(item);
        }
      });
      this.legendDatanew = result2
      this.getBarList()
    },
    async getBarList() {
      let params = {
        "Position": this.Order,
        "BaseTime": this.BaseTime,
        "TeamCode": this.simlevel,
        // "ProductionLineCode": this.ProductionLineCode,
        // "FactoryCode": this.FactoryCode
      }
      let { response } = await getChartStructure(params)
      this.curConfig = response
      if (this.curConfig.IsSql == '1') {
        this.getBarList1()
      } else {
        if (this.curConfig?.ChartConfigs != null) {
          if (this.curConfig.Unit == undefined || this.curConfig.Unit == '' || this.curConfig.Unit == null) {
            this.title1 = this.title
          } else {
            // this.title1 = this.title + '(' + this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit + ')'
            this.title1 = this.title + '(' + this.curConfig.Unit + ')'
          }
          // this.id = this.curConfig.ID;
          // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
          this.curConfig.ChartConfigs.map(item => {
            item.KpiName = this.curConfig.ChartConfigs.KpiName
            if (item.KpiValues[0]) {
              item.KpiCode = item.KpiValues[0].KpiCode
              item.TargetValue = item.KpiValues[0].TargetValue || 0
            }
          })
          //图表配置整体赋值
          // this.curConfig = response
          //时间颗粒度列表
          this.myShiftList = this.curConfig?.ChartConfigs.filter(item => {
            return item.TargetVisible === 1
          })
          //默认激活第一个时间颗粒度
          if (this.Particle != '') {
            this.myShiftList.map((el, index) => {
              if (this.Particle == el.TimeDimension) {
                this.curShift = this.myShiftList[index]
                this.$nextTick(() => {
                  this.query1(true)
                })
              } else {
                this.$nextTick(() => {
                  this.query1(false)
                })
              }
            })
          } else {
            this.curShift = this.myShiftList[0]
            this.Particle = this.curShift?.TimeDimension
            this.$nextTick(() => {
              this.$nextTick(() => {
                this.query1(true)
              })
            })
          }
        } else {
          this.title1 = this.title
        }
      }

    },
    async getBarList1() {
      let params = {
        "simLevel": this.Order.split('-')[0],
        "position": [
          this.Order
        ],
        "paramList": [
          this.simlevel,
          this.BaseTime
        ]
      }
      let { response } = await getTableList(params)
      this.curConfig = response
      if (this.curConfig?.ChartConfigs != null) {
        if (this.curConfig.Unit == undefined || this.curConfig.Unit == '' || this.curConfig.Unit == null) {
          this.title1 = this.title
        } else {
          // this.title1 = this.title + '(' + this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit + ')'
          this.title1 = this.title + '(' + this.curConfig.Unit + ')'
        }
        // this.id = this.curConfig.ID;
        // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
        this.curConfig.ChartConfigs.map(item => {
          item.KpiName = this.curConfig.ChartConfigs.KpiName
          if (item.KpiValues[0]) {
            item.KpiCode = item.KpiValues[0].KpiCode
            item.TargetValue = item.KpiValues[0].TargetValue || 0
          }
        })
        //图表配置整体赋值
        // this.curConfig = response
        //时间颗粒度列表
        this.myShiftList = this.curConfig?.ChartConfigs.filter(item => {
          return item.TargetVisible === 1
        })
        //默认激活第一个时间颗粒度
        if (this.Particle != '') {
          this.myShiftList.map((el, index) => {
            if (this.Particle == el.TimeDimension) {
              this.curShift = this.myShiftList[index]
              this.$nextTick(() => {
                this.query1(true)
              })
            } else {
              this.$nextTick(() => {
                this.query1(false)
              })
            }
          })
        } else {
          this.curShift = this.myShiftList[0]
          this.Particle = this.curShift.TimeDimension
          this.$nextTick(() => {
            this.query1(true)
          })
        }
      } else {
        this.title1 = this.title
      }

    },
    query1(data) {
      // this.showData = data
      // if (this.chartLine) {
      //   this.chartLine.clear()
      //   return
      // }
      this.chartLine = this.$echarts.init(document.getElementById(this.id1));
      var option
      var that1 = this
      option = {
        symbol: 'circle',
        // tooltip: {
        //   trigger: 'axis',
        //   // extraCssText: 'z-index:99999',
        //   axisPointer: {
        //     type: 'shadow',
        //   },
        //   confine: true,
        //   extraCssText: 'max - width: none; overflow: visible;',
        // },
        tooltip: {
          axisPointer: {
            type: 'shadow',
          },
          confine: true,
          extraCssText: 'max - width: none; overflow: visible;',
          formatter: function (params) {
            if (that1.Particle == '日') {
              let abc = []
              let abc1 = []
              let name1 = ''
              if (that1.curConfig.listShift.length > 0) {
                that1.curConfig.listShift.map(el => {
                  if ((el.WorkDate.split(' ')[0].split('-')[1] + '-' + el.WorkDate.split(' ')[0].split('-')[2]) == params.name) {
                    abc1.push(el)
                  }
                })
              }
              let msg = ''
              Object.keys(that1.curConfig.ChartConfigs[0].ChartData).map((el, index) => {
                if (el.includes(params.seriesName + '_')) {
                  abc.push(el + '：' + that1.curConfig.ChartConfigs[0].ChartData[el][params.dataIndex])
                } else if (el == params.seriesName) {
                  abc.push(el + '：' + that1.curConfig.ChartConfigs[0].ChartData[el][params.dataIndex])
                }
              })
              abc1.map(el => {
                if (params.seriesName.includes(el.WorkShift)) {
                  name1 = params.seriesName + '-' + el.WorkNum
                }
                // else {
                //   name1 = params.seriesName
                // }
              })
              msg += (name1 == '' ? params.seriesName : name1) + '：' + params.value + "<br>";
              // msg += name1 + "<br>";
              // abc.map((el, index) => {
              //   msg += el + "<br>";
              // })
              return msg
            } else {
              let msg = ''
              let arr = []
              let abc = []
              that1.curConfig.ChartConfigs.map((el, index) => {
                if (el.TimeDimension == that1.Particle) {
                  arr.push(el)
                }
              })
              arr.map((el, index) => {
                Object.keys(el.ChartData).map((el1, index1) => {
                  if (el1.includes(params.seriesName + '_')) {
                    abc.push(el1 + '：' + arr[0].ChartData[el1][params.dataIndex])
                  }
                })
              })
              msg += (params.seriesName + params.value) + "<br>";
              // msg += params.name + "<br>";
              // msg += params.seriesName + '：' + params.value + "<br>";
              // abc.map((el, index) => {
              //   msg += el + "<br>";
              // })
              return msg
            }
          }
        },
        // legend: {
        //   data: this.lineLegend
        // },
        grid: {
          left: '5%',
          right: '6%',
          bottom: '3%',
          top: '11%',
          containLabel: true
        },

        // visualMap: this.lineVisualMap,
        xAxis: this.xAxisOption,
        yAxis: {
          type: 'value',
          // show: false
          axisLine: {
            show: true,
            lineStyle: {
              color: '#fff'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff" //X轴文字颜色
            },
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        series: this.lineSeries
      }
      var that = this
      that.chartLine.off('click');
      that.chartLine.on('click', function (param) {
        that.BaseTime1 = param.name
        // if (that.configFlag == '是') {
        //   that.barName = param.name
        //   that.$refs.keyIndicatorsref.flagChange()
        //   that.$refs.keyIndicatorsref.showDialog = true;
        // }
        that.barName = param.seriesName.split('-')[0]

        // that.$refs.keyIndicatorsref.flagChange()
        // that.$refs.keyIndicatorsref.showDialog = true;
        that.Particle1 = that.Particle
        that.isSql1 = that.curConfig.IsSql
        that.keyIndicatorslistnewShow = true
        that.showkeyIndicatorslistnew = true
      });
      this.chartLine.setOption(option, true);
      window.addEventListener("resize", () => {
        this.chartLine.resize()
      }, false);
    },
    query() {
      this.chartLine = this.$echarts.init(document.getElementById(this.id1));
      var option
      option = {
        title: {
          text: this.title,
          textStyle: { // 标题样式
            color: '#fff',
          }
        },
        tooltip: {
          trigger: "item"
        },
        legend: {
          right: 0,
          data: [
            {
              name: "目标值",
              textStyle: {
                color: "white"
              }
            },
            {
              name: "实际值",
              textStyle: {
                color: "white"
              }
            }
          ]
        },
        grid: {
          top: '21%',
          bottom: '1%',
          right: '1%',
          left: '1%',
          containLabel: true
        },
        toolbox: {
          show: true,
        },
        calculable: true,
        xAxis: [
          {
            type: 'category',
            axisLine: {
              lineStyle: {
                color: "#ccc"
              }
            },
            splitLine: {
              show: false
            },
            // data: this.nianDataYu.map(item => item.COMMONNAME),
            data: ['10.10', '10.11', '10.13', '10.14'],
            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff" //X轴文字颜色
              },
            },
          }
        ],
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: "#ccc"
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff" //X轴文字颜色
              },
              formatter: "{value}%",
            },
          },
          // {
          //   //右边百分比部分
          //   name: '百分比',
          //   type: "value",
          //   position: "right",
          //   axisLine: {
          //     lineStyle: {
          //       color: "#fff"
          //     }
          //   },
          //   axisTick: {
          //     show: false,
          //   },
          //   axisLabel: {
          //     textStyle: {
          //       color: "#fff",
          //     },
          //     show: true,
          //     interval: "auto",
          //     formatter: "{value}%",
          //   },
          //   show: true,
          //   splitLine: {  //网格线
          //     show: false
          //   }
          // }
        ],
        series: [
          {
            name: '目标值',
            smooth: true,
            emphasis: {//折线图的高亮状态。
              focus: "series",//聚焦当前高亮的数据所在的系列的所有图形。
            },
            tooltip: {
              show: true
            },
            type: 'line',
            barWidth: 6,
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "#4391F4" // 0% 处的颜色
                }, {
                  offset: 0.6,
                  color: "#4391F4" // 60% 处的颜色
                }, {
                  offset: 1,
                  color: "#4391F4" // 100% 处的颜色
                }], false)
              }
            },
            data: [10, 30, 40, 20, 15],
          },

          {
            name: '实际值',
            smooth: true,
            // symbolSize: 10,   //折线点的大小
            // stack: 'Total',
            // areaStyle: {},
            // emphasis: {
            //   focus: 'series'
            // },
            // yAxisIndex: 1,
            // stack: "Search Engine",
            // emphasis: {
            //   focus: "series",
            // },
            type: 'line',
            // barWidth: 10,
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0,
                  color: "#6B74E4" // 0% 处的颜色
                }, {
                  offset: 0.6,
                  color: "#6B74E4" // 60% 处的颜色
                }, {
                  offset: 1,
                  color: "#6B74E4" // 100% 处的颜色
                }], false),

              }
            },
            barGap: 0,
            // data: this.nianDataYu.map(item => item.RATE),
            data: [10, 30, 50, 25],
            label: {
              show: false,
              position: 'top',
              textStyle: {
                color: 'white',
                fontSize: 10
              },
              formatter: '{c}%'
            }
          },
        ]
      };
      this.chartLine.setOption(option, true);
      window.addEventListener("resize", () => {
        this.chartLine.resize()
      }, false);
    }
  },
}
</script>
<style lang="scss" scoped>
::v-deep .noDataBox {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
}
#chartLine {
    width: 100%;
    height: 100%;
}
.titimgbox {
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    width: 50%;
    height: 30px;
    line-height: 30px;
    border-radius: 5px;
    /* background-image: linear-gradient(to right, #056be0 0%, #000b61 100%); */
    display: flex;
    /* border: 1px solid #fff; */
    /* box-shadow: 0px 0px 7px 0px #fff; */
    /* overflow: hidden; */
}
</style>
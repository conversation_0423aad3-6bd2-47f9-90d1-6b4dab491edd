<template>
    <v-card>
        <v-card-title class="headline primary lighten-2" primary-title>{{ type === 'team' ? $t('DFM_RL._TJBZ') :
        $t('DFM_RL._TJBC') }}</v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid">
                <v-row class="mt-5">
                    <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_RL._MC')" :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" required
                            dense outlined v-model="form.Name"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_RL._BM')" disabled dense outlined v-model="deparment.name">
                        </v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_RL._JC')" :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" required
                            dense outlined v-model="form.Shortname"></v-text-field>
                    </v-col>
                    <!-- <v-col :cols="12" :lg="12" class="pt-0 pb-0" v-if="type == 'classes'">
                    <v-select
                        :items="typeList"
                        item-text="ItemName"
                        item-value="ItemValue"
                        no-data-text="暂无数据"
                        clearable
                        dense
                        v-model="form.Type"
                        outlined
                        label="类型"
                        placeholder="请选择类型"
                    />
                </v-col> -->
                    <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_RL._XH')" :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" required
                            dense outlined v-model="form.Sequence"></v-text-field>
                    </v-col>
                    <!-- <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                    <v-checkbox class="mt-0" v-model="form.Enabledmark" label="有效"></v-checkbox>
                </v-col> -->
                    <v-col class="pt-0 pb-0" :cols="12" :lg="12">
                        <v-textarea :label="$t('DFM_RL._BZ')" v-model="form.Description" :value="form.Notes" outlined
                            height="70"></v-textarea>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
        <v-divider></v-divider>

        <v-card-actions>
            <!-- <v-spacer></v-spacer> -->
            <v-checkbox class="mr-auto" v-model="isChecked" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closeEditPopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { saveClassesForm, saveTeamForm } from '../service';
export default {
    inject: ['currentDeparment'],
    props: {
        type: {
            type: String,
            default: 'classes'
        },
        editObj: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            valid: false,
            typeList: [],
            prodLineList: [],
            isChecked: true,
            deparmentList: [],
            form: {
                Name: '',
                Shortname: '',
                // Type: '',
                Sequence: '',
                // Enabledmark: false,
                Description: ''
            }
        };
    },
    computed: {
        deparment() {
            return this.currentDeparment() || {};
        }
    },
    created() {
        if (this.editObj && this.editObj.ID) {
            for (const key in this.form) {
                this.form[key] = this.editObj[key];
            }
            this.form.ID = this.editObj.ID;
            // this.form.Enabledmark = this.form.Enabledmark ? true : false;
        }
    },
    methods: {
        closeEditPopup() {
            this.$emit('closeEditPopup');
        },
        resetForm() {
            this.form = {
                Name: '',
                Shortname: '',
                // Type: '',
                Sequence: '',
                // Enabledmark: false,
                Description: ''
            };
        },
        async submitForm() {
            if (!this.$refs.form.validate()) return false;

            if (this.type === 'classes') {
                await saveClassesForm({ ...this.form, Departmentid: this.deparment.id });
            } else {
                await saveTeamForm({ ...this.form, Departmentid: this.deparment.id });
            }
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
            this.resetForm();
            this.$emit('getdata');
            if (this.isChecked) {
                this.isChecked = !this.isChecked;
                this.$emit('closeEditPopup');
            }
        }
    }
};
</script>

<style>

</style>
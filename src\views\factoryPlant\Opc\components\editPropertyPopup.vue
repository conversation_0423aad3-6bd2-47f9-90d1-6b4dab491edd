<template>
    <div class="edit-property-popup">
        <v-row class="tool-row">
            <v-col :cols="12" :lg="3">
                <a-input-search v-model="keywords" enter-button placeholder="Quick Search" @search="onSearch" />
            </v-col>
            <v-col :cols="12" :lg="9" class="pl-0">
                <a-button @click="getdata">
                    <v-icon left>mdi-cached</v-icon>
                    Refresh</a-button>
                <a-button style="margin-left:10px" type="primary">
                    Save</a-button>
            </v-col>
        </v-row>
        <div class="table" style="margin-top:10px">
            <vxe-table height="auto" class="mytable-scrollbar" :loading="loading" size="mini" border resizable ref="table"
                :data="tableList">
                <vxe-column v-for="(column, index) in editPropertyColumns" :key="index" :field="column.field"
                    :title="column.title" :width="column.width">
                    <template #default="{ row }">
                        <vxe-input v-if="column.field == 'Value'" size="mini" v-model="row[column.field]"></vxe-input>
                        <span v-else>{{ row[column.field] }}</span>
                    </template>
                </vxe-column>
            </vxe-table>
        </div>
    </div>
</template>

<script>

import { editPropertyColumns } from '@/columns/factoryPlant/Opc.js'
export default {
    props: {
        editItemObj: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            keywords: '',
            editPropertyColumns,
            loading: false,
            tableList: []
        }
    },
    created() { },
    methods: {
        getdata() { },
        onSearch() {
            this.getdata()
        }
    }
}
</script>

<style></style>
const state = {
    SearchForm: {}, //  查询对象
    SearchList: [], // 查询条件
    SearchbtnList: [], //查询按钮
    MainAlarmType: '',//一级分类
};
const getters = {
    getSearchForm: state => state.SearchForm,
    getSearchList: state => state.SearchList,
    getSearchbtnList: state => state.SearchbtnList
};

const mutations = {
    SET_SearchForm(state, munes) {
        state.SearchForm = munes;
    },
    SET_SearchList(state, munes) {
        state.SearchList = munes;
    },
    SET_SearchbtnList(state, munes) {
        state.SearchbtnList = munes;
    },
    //设置一级分类
    SET_MainAlarmType(state, payload) {
        state.MainAlarmType = payload
    }
};

export default {
    namespace: true,
    state,
    getters,
    mutations
};

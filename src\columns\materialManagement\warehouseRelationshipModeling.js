// dictionary: true, isEditCell: true
export const warehouseRelationshipModeling = [
    { text: '序号', value: 'Index', width: '80px' },
    // { text: '楼层', value: 'Floor', width: '120px' },
    // { text: '产线', value: 'Linename', width: '140px' },
    { text: 'MES仓库编号', value: 'WarehouseCode', width: '180px' },
    { text: 'MES仓库名称', value: 'WarehouseName', width: '160px' },
    { text: 'AGV仓库编号', value: 'AGVWarehouseCode', width: '200px' },
    { text: 'AGV仓库名称', value: 'AGVWarehouseName', width: '180px' },
    { text: 'AGV仓库库区编号', value: 'BelongAreaCode', width: '220px' },
    { text: 'AGV仓库库区名称', value: 'BelongAreaName', width: '200px' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '120px'
    }
];
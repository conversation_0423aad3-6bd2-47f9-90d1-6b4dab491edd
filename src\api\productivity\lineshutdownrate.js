import { getRequestResources } from '@/api/fetch';
const baseURL_30015 = 'baseURL_30015'
const DFM = 'baseURL_DFM'

//物料模型
export function GetEquipmentListByLevel(data) {
    const api = '/api/Equipment/GetEquipmentTree';
    return getRequestResources(DFM, api, 'post', data);
}
//Line新增
export function GetDowntimetgtSaveForm(data) {
    const api = '/api/Downtimetgt/SaveForm'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Line删除
export function GetDowntimetgtDelete(data) {
    const api = '/api/Downtimetgt/Delete'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Line列表
export function GetDowntimetgtPageList(data) {
    const api = '/api/Downtimetgt/GetPageList'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Line导入
export function GetDowntimetgtImportData(data) {
    const api = '/api/Downtimetgt/ImportData'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
import request from '@/util/request';
export function ExportData(url, data) {
    return request({
        url: url,
        method: 'post',
        data,
        responseType: 'blob'


    });}
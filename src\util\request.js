import axios from 'axios';
import Vue from 'vue';
import store from '@/store';
import { configUrl } from '@/config';
import Util from '@/util';
import { Loading } from 'element-ui';
import _, { has } from 'lodash';
import Router from 'vue-router';
import router from '@/router'


// 获取防抖的按钮
const requestLoadings = document.getElementsByClassName('v-btn primary');
// 设置按钮为不可用
function setDisabled() {
    let footer = document.getElementsByClassName('dialog-footer')
    for (let i = 0; i < footer.length; i++) {
        for (let k = 0; k < footer[i].children.length; k++) {
            footer[i].children[k].setAttribute('disabled', true);
            footer[i].children[k].style.setProperty('background', '#e8e8e8', 'important');
            footer[i].children[k].style.cursor = 'not-allowed';
        }

    }
    for (let index = 0; index < requestLoadings.length; index++) {
        requestLoadings[index].setAttribute('disabled', true);
        requestLoadings[index].style.setProperty('background', '#e8e8e8', 'important');
        requestLoadings[index].style.cursor = 'not-allowed';
    }
}
// 移除按钮的不可用
function removeDisabled() {
    let footer = document.getElementsByClassName('dialog-footer')
    for (let i = 0; i < footer.length; i++) {
        for (let k = 0; k < footer[i].children.length; k++) {
            footer[i].children[k].style.background = '';
            if (footer[i].children[k].getAttribute('class')) {
                if (footer[i].children[k].getAttribute('class').indexOf('is-disabled') == -1) {
                    footer[i].children[k].removeAttribute('disabled');
                    footer[i].children[k].style.cursor = 'pointer';
                }
            }
        }
    }
    for (let index = 0; index < requestLoadings.length; index++) {
        requestLoadings[index].style.background = '';
        if (requestLoadings[index].getAttribute('class')) {
            if (requestLoadings[index].getAttribute('class').indexOf('is-disabled') == -1) {
                requestLoadings[index].removeAttribute('disabled');
                requestLoadings[index].style.cursor = 'pointer';
            }
        }

    }
}

let qmsUrlList = ['/backend-api/workflow/customerComplain/getCustomerComplainStatistics',
    '/backend-api/workflow/customerComplain/getCustomerComplainPhenomenonType',
    '/backend-api/workflow/experimentApplicationAct/getLapsePPMByLocAndStage',
    '/backend-api/workflow/experimentApplicationAct/getLapsePPMByEaclass'
]
let loadingInstance1 = null;
let loadingInstance1Count = 0;

qmsUrlList.forEach((item, index) => {
    qmsUrlList[index] = configUrl[process.env.VUE_APP_SERVE]['baseURL_QMS'] + item
})

/**
 * @description 函数返回唯一的请求key **/
function getRequestKey(config) {
    let { method, url, params, data } = config;
    // axios中取消请求及阻止重复请求的方法
    // 参数相同时阻止重复请求：
    return [method, url, JSON.stringify(params), JSON.stringify(data)].join('&');
    // 请求方法相同，参数不同时阻止重复请求
    // return [method, url].join('&');
}

/**
 * @description 添加请求信息 **/
let pendingRequest = new Map();

function addPendingRequest(config) {
    // console.log(config.url)
    let requestKey = getRequestKey(config);
    config.cancelToken =
        config.cancelToken ||
        new axios.CancelToken(cancel => {
            if (!pendingRequest.has(requestKey)) {
                pendingRequest.set(requestKey, cancel);
            }
        });
}

/**
 * @description 取消重复请求 **/
function removePendingRequest(config) {
    let requestKey = getRequestKey(config);
    if (pendingRequest.has(requestKey)) {
        // 如果是重复的请求，则执行对应的cancel函数
        let cancel = pendingRequest.get(requestKey);
        cancel(requestKey);
        // 将前一次重复的请求移除
        pendingRequest.delete(requestKey);
    }
}
const service = axios.create({
    // baseURL: configUrl[process.env.VUE_APP_SERVE], // api base_url
    timeout: 50000, // timeout,
    headers: { 'Access-Control-Allow-Origin': '*', 'Content-Type': 'application/json' }
});
const err = error => {
    // removeDisabled();
    loadingInstance1Count--;
    loadingInstance1Count = Math.max(loadingInstance1Count, 0); //做个保护
    if (loadingInstance1Count === 0) {
        toHideLoading()
    }
    try {
        let status = error.response.status
        console.log(error)
        switch (status) {
            case 400:
                store.commit('SHOW_SNACKBAR', { text: status + ' ~ bad request', color: 'error' });
                break;
            case 422:
                store.commit('SHOW_SNACKBAR', { text: status + ' ~ Unprocessable Entity', color: 'error' });
                break;
            case 401:
                store.dispatch('logout');
                sessionStorage.clear();
                localStorage.clear();
                router.replace('/auth/login');
                //store.commit('SHOW_SNACKBAR', { text: status + ' ~ unauthorized', color: 'error' });
                store.commit('SHOW_SNACKBAR', { text: '本次登陆已过期，请重新登陆系统!', color: 'error' });
                break;
            case 403:
                store.commit('SHOW_SNACKBAR', { text: status + ' ~ forbidden', color: 'error' });
                break;
            case 404:
                store.commit('SHOW_SNACKBAR', { text: status + ' ~ not found', color: 'error' });
                break;
            case 500:
                store.commit('SHOW_SNACKBAR', { text: status + ' ~ internal sever error', color: 'error' });
                break;
            default:
                break;
        }
        return Promise.reject(error);
    } catch (err) {
        if (error.toString().indexOf('Cancel') == '-1') {
            store.commit('SHOW_SNACKBAR', { text: error.status || '服务异常' + ' ~ service unavailable', color: 'error' });
        }
    }
};
// request interceptor
service.interceptors.request.use(config => {
    let hash = window.location.hash
    let noloadingHashList = [
        "MaterialLabel",
        "MaterialPrePowderPrepRoom"
    ]
    let noloadFlag = false
    noloadFlag = noloadingHashList.some(item=>{
        return hash.includes(item)
    })
    if (!config.noloading && !noloadFlag) {
        if (loadingInstance1Count === 0 && !loadingInstance1) {
            loadingInstance1 = Loading.service({
                lock: true,
                text: 'Loading',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)',
                fullscreen: true
            });
        }
        loadingInstance1Count++;
    }

    if (hash.indexOf("equipmentManagement") != -1) {
        let Factory = hash.substring(hash.indexOf("?") + 1, 9999999)
        let FactoryArr = Factory.split("&")
        let myFactory = ""
        FactoryArr.forEach(item => {
            if (item.indexOf('factory') != -1) {
                myFactory = item.substring(item.indexOf("=") + 1, item.length)
            }
        })
        if (myFactory == "") {
            myFactory = '2010'
        }
        config.params = {
            ...config.params,
            factory: myFactory,
        };
        config.data = config.data || {};
        if (!config.data.Factory) {
            config.data.Factory = myFactory
        }
    }
    if (config.url.indexOf("ReadCallData") == -1) {
        // setDisabled();
    }
    config.headers['Access-Control-Allow-Origin'] = '*';
    config.headers['Content-Type'] = 'application/json';
    // 判断是否是QMS接口，不是QMS接口 请求头加上Token
    if (qmsUrlList.indexOf(config.url) === -1) {
        config.headers['Authorization'] = 'Bearer ' + store.getters.getAccessToken;
    }
    config.headers['lang'] = store.getters.getLocale;
    // const { data } = config;
    // if (data) {
    // Util.removeEmpty(data)
    // }
    // 检查是否存在重复请求，若存在则取消已发的请求
    removePendingRequest(config);
    // 把当前请求信息添加到pendingRequest对象中
    addPendingRequest(config);
    if (Util.getSearchTarget()) {
        const { data } = config;
        config.data = { ...data, pageIndex: 1 }
    }
    return config;
}, err);

// response interceptor

// eslint-disable-next-line no-unused-vars
service.interceptors.response.use(({ data, config }) => {
    return new Promise((resolve, reject) => {
        loadingInstance1Count--;
        loadingInstance1Count = Math.max(loadingInstance1Count, 0); //做个保护
        if (loadingInstance1Count === 0) {
            toHideLoading()
        }
        // removeDisabled();
        Util.removeSearchTarget();
        // htmls.forEach(e => {
        //     e.disabled = true;
        // });
        if (qmsUrlList.indexOf(config.url) !== -1) {
            if (data.code == 200) {
                resolve(data)
                return true
            } else {
                reject(data)
            }
        }
        if (data.success) {
            resolve(data);
            // removePendingRequest(config);
        } else if (data.result) {
            resolve(data);
            // removePendingRequest(config);
        } else {
            if (config.responseType == 'blob') {
                resolve(data);
            } else {
                reject(data);
                removePendingRequest(config || {});
                if (axios.isCancel(data)) {
                    console.log('被取消的重复请求：' + data.message);
                    //return false;
                }
                store.commit('SHOW_SNACKBAR', { text: data.msg || '数据异常', color: 'error' })
                // if (config.url.indexOf('AlarmRecord/DefectSelect/') == -1) store.commit('SHOW_SNACKBAR', { text: data.msg || '数据异常', color: 'error' });
            }
        }
    });
}, err);
//防抖：将 300ms 间隔内的关闭 loading 便合并为一次。防止连续请求时， loading闪烁的问题。
let toHideLoading = _.debounce(() => {
    if (loadingInstance1Count === 0 && loadingInstance1) {
        loadingInstance1.close();
        loadingInstance1 = null;
    }
}, 200);
export default service;

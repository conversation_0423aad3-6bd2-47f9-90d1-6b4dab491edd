export const physicalModelColum = [
    {
        text: '序号',
        value: 'Index',
        width: 80,
        sortable: true
    },
    { text: '编码', value: 'EquipmentCode', width: 170 },
    { text: '名称', value: 'EquipmentName', width: 160 },
    { text: '简称', value: 'Remark', width: 180 },
    { text: '类型', value: 'Level', width: 120 },
    { text: '是否启用', value: 'Enabled', width: 100 },
    { text: '属性', align: 'center', value: 'attribute', width: 150 },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    { text: '操作', align: 'center', value: 'actions', width: 160, sortable: true }
];
export const attributeDialog = [
    { text: '编码', value: 'PropertyCode', width: 140 },
    { text: '值', value: 'PropertyValue', width: 142 },
    // { text: '属性', align: 'center', value: 'Enable', width: 80 },
    { text: '描述', value: 'Remark', width: 180 },
    { text: '操作', align: 'center', value: 'actions', width: 150, sortable: true }
];
export const attributeTableHead = [
    { text: '编码', value: 'PropertyCode', width: 140 },
    { text: '描述', value: 'PropertyName',width: 140 },
    { text: '值', value: 'PropertyValue' },
    { text: '操作', align: 'center', value: 'actions', width: 150, sortable: true }
];

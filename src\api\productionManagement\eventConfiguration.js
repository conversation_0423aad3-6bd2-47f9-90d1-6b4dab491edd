import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_30015'

//树查询
export function getDownTimeReasonTree(data) {
  const api = '/api/DowntimeCategroy/GetDownTimeReasonTree'
  return getRequestResources(baseURL, api, 'post', data);
}
/* Group  Reason**/
// 一级
export function downtimeCategroyList(data) {
  const api = '/api/DowntimeCategroy/GetList'
  return getRequestResources(baseURL, api, 'post', data);
}

// 二级
export function downtimeGroupList(data) {
  const api = '/api/DowntimeGroup/GetListByCategoryId'
  return getRequestResources(baseURL, api, 'post', data);
}

// 三级及以下
export function downtimeReasonList(data) {
  const api = '/api/DowntimeReason/GetListByGroupId'
  return getRequestResources(baseURL, api, 'post', data);
}

// 新增group group 下拉
export function getWithoutParentGroupIdList(data) {
  const api = '/api/DowntimeGroup/GetWithoutParentGroupIdList'
  return getRequestResources(baseURL, api, 'post', data);
}

// 新增group
export function saveGroupForm(data) {
  const api = '/api/DowntimeGroup/SaveForm'
  return getRequestResources(baseURL, api, 'post', data);
}

// 新增reason
export function saveReasonForm(data) {
  const api = '/api/DowntimeReason/SaveForm'
  return getRequestResources(baseURL, api, 'post', data);
}

/* Line Configuration **/
// 产线列表
export function getLineList(data) {
  const api = '/api/LineAndEquipmentView/GetLineList'
  return getRequestResources(baseURL, api, 'post', data);
}

// 获取产线下设备列表
export function getListByLineCode(data) {
  const api = '/api/LineAndEquipmentView/GetListByLineCode'
  return getRequestResources(baseURL, api, 'post', data);
}

// 自动reason列表
export function getReasonEgAssocList(data) {
  const api = '/api/MReasonEqview/Getlist'
  return getRequestResources(baseURL, api, 'post', data);
}

// 自动保存
export function saveReasonEgAssocForm(data) {
  const api = '/api/DowntimeReasonEqAssoc/SaveForm'
  return getRequestResources(baseURL, api, 'post', data);
}

// 自动删除
export function deleteReasonEgAssoc(data) {
  const api = '/api/DowntimeReasonEqAssoc/Delete'
  return getRequestResources(baseURL, api, 'post', data);
}

// 手动
// export function getReasonMappingList(data) {
//   const api = '/api/DowntimeReasonMapping/GetList'
//   return getRequestResources(baseURL, api, 'post', data);
// }

// 手动列表
export function getListByEqMentId(data) {
  const api = '/api/MReasonMappingView/GetList'
  return getRequestResources(baseURL, api, 'post', data);
}

// 手动保存
export function saveFormWithReasonList(data) {
  const api = '/api/DowntimeReasonMapping/SaveFormWithReasonList'
  return getRequestResources(baseURL, api, 'post', data);
}

export function saveReasonMappingForm(data) {
  const api = '/api/DowntimeReasonMapping/SaveForm'
  return getRequestResources(baseURL, api, 'post', data);
}

// 手动删除
export function deleteReasonMapping(data) {
  const api = '/api/DowntimeReasonMapping/delete'
  return getRequestResources(baseURL, api, 'post', data);
}

<template>
    <v-dialog v-model="showDialog" max-width="1080px">
        <v-card v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                <!-- 新增备品备件 -->
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <div class="EquipmentSearch">
                <div class="addForm">
                    <v-text-field v-model="Code" outlined dense :label="$t('TPM_SBGL_BJYJCX._BJBM')"></v-text-field>
                </div>
                <div class="addForm">
                    <v-text-field v-model="Name" outlined dense :label="$t('TPM_SBGL_BJYJCX._BJMC')"></v-text-field>
                </div>
                <div class="addForm">
                    <v-text-field v-model="Model" outlined dense :label="$t('TPM_SBGL_BJYJCX._GGXH')"></v-text-field>
                </div>
                <div class="addForm">
                    <v-btn color="primary" @click="getMat()">{{ $t('TPM_SBGL_BJYJCX.Filtermaterials') }}</v-btn>
                </div>
                <div class="addForm">
                    <v-autocomplete
                        :multiple="true"
                        clearable
                        v-model="material"
                        :items="materialList"
                        item-text="ItemName"
                        item-value="ItemValue"
                        :label="this.$t('TPM_SBGL_BJYJCX.ChoosePart')"
                        clear
                        dense
                        outlined
                    ></v-autocomplete>
                </div>
                <div class="addForm">
                    <v-autocomplete
                        clearable
                        disabled
                        v-model="StorageBin"
                        :items="StorageBinList"
                        item-text="ItemName"
                        item-value="ItemValue"
                        :label="$t('$vuetify.dataTable.TPM_SBGL_BJKZGL.StorageBin')"
                        clear
                        dense
                        outlined
                    ></v-autocomplete>
                </div>
                <div class="addForm">
                    <v-btn color="primary" @click="getInv()">{{ $t('TPM_SBGL_BJYJCX.QueryInventory') }}</v-btn>
                </div>
            </div>
            <Tables
                :footer="false"
                :page-options="pageOptions"
                :showSelect="false"
                :loading="loading"
                ref="BjTables"
                :btn-list="btnList"
                tableHeight="300px"
                :clickFun="clickFun"
                table-name="TPM_SBGL_SBTZGL_BPBJQD"
                :headers="ByEquipListColum"
                :desserts="desserts"
            ></Tables>
            <v-card-text class="card-text" style="min-height: auto">
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="Listform.Qty" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Qty') + ' *'"></v-text-field>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" :disabled="JSON.stringify(CheckItem) == '{}'" @click="addListSave('addList')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { ByEquipListColum } from '@/columns/equipmentManagement/Equip.js';
import { GetPartsInventoryFromSap, GetPartsHistoryDetailSaveForm, GetPartsListByLike, GetPartsListFromSapByPartCode } from '@/api/equipmentManagement/upkeeplistB.js';
import { SparepartSaveForm } from '@/api/equipmentManagement/EquipParts.js';
import { Message, MessageBox } from 'element-ui';

export default {
    props: {
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        },
        rowtableItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            ByEquipListColum,
            loading: false,
            desserts: [],
            StorageBin: '',
            StorageBinList: [],
            Code: '',
            Name: '',
            Model: '',
            material: [],
            materialList: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            showDialog: false,
            classcheckbox: true,
            CheckItem: {},
            Listform: {
                PartsId: '',
                Qty: ''
            },
            equipmentSpareType: []
        };
    },
    computed: {
        btnList() {
            return [];
        }
    },
    async mounted() {
        this.StorageBinList = await this.$getNewDataDictionary('StorageBin');
        this.StorageBin = this.StorageBinList[0].ItemValue;
    },
    methods: {
        async getMat() {
            let params = {
                Factory: this.$route.query.Factory ? this.$route.query.Factory : '2010',
                Code: this.Code,
                Name: this.Name,
                Model: this.Model
            };
            let res = await GetPartsListByLike(params);
            let data = res.response;
            if (data.length > 0) {
                data.forEach(item => {
                    item.ItemValue = item.Code;
                    item.ItemName = item.Name + '-' + item.Code + '-' + item.Model;
                });
            }
            this.materialList = data;
        },
        async getInv() {
            this.loading = true;
            console.log(this.material);
            if (this.material.length == 0) {
                let obj = {
                    StorageBin: this.StorageBin,
                    Code: this.Code,
                    Name: this.Name,
                    Model: this.Model
                };
                let arr = [];
                arr.push(obj);
                let res = await GetPartsListFromSapByPartCode(arr);
                let { success, response } = res;
                if (success) {
                    this.$refs.BjTables.selected = [];
                    this.loading = false;
                    this.desserts = response || {} || [];
                }
            } else {
                let arr = [];
                this.material.forEach(item => {
                    let obj = {
                        StorageBin: this.StorageBin,
                        DeviceId: this.rowtableItem.DeviceId,
                        Code: item
                    };
                    arr.push(obj);
                });
                let res = await GetPartsListFromSapByPartCode(arr);
                let { success, response } = res;
                // response.forEach((item, index) => {
                //     item.ID = index;
                // });
                if (success) {
                    this.$refs.BjTables.selected = [];
                    this.loading = false;
                    this.desserts = response || {} || [];
                }
            }
        },
        async getData() {
            this.loading = true;
            let params = {
                DeviceId: ''
            };
            params.DeviceId = this.rowtableItem.DeviceId;
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            let res = await GetPartsInventoryFromSap(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = response || {} || [];
            }
        },
        clickFun(item) {
            this.CheckItem = item;
            this.$refs.BjTables.selected = [item];
        },
        // 备品备件
        async addListSave(type) {
            if (this.Listform.Qty == '' || this.Listform.Qty == null || this.Listform.Qty == 0) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            if (Number(this.Listform.Qty) > Number(this.CheckItem.Stock)) {
                Message({
                    message: `申请数量不能大于SAP库存数量！`,
                    type: 'error'
                });
                return;
            }
            let params = this.CheckItem;
            params.DeviceId = this.rowtableItem.DeviceId;
            params.ReferNo = this.rowtableItem.RepairWo;
            params.Source = '保养';
            params.Qty = this.Listform.Qty;
            params.PartsCode = this.CheckItem.Code;
            params.PartsName = this.CheckItem.Name;
            params.PartsModel = this.CheckItem.Model;
            params.PartsType = this.CheckItem.Type;
            params.ID = '';
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            const res = await GetPartsHistoryDetailSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$emit('loadData');
                // this.$parent.$parent.$refs.Tables.selected = [];
                this.showDialog = this.classcheckbox ? false : true;
            }
        }
    }
};
</script>

<style lang="scss">
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
}
.mytree {
    .tree-card {
        margin: 5px;
        .tree-title {
            align-items: center !important;
            display: flex;
            flex-wrap: wrap;
            font-size: 1.25rem !important;
            letter-spacing: 0.0125em;
            line-height: 2rem;
            word-break: break-all;
            width: 100%;
            font-weight: 500;
            padding: 8px;
            border-bottom: 1px solid #aaa;
        }
    }
}
.ant-calendar-picker {
    ::v-deep input {
        border-color: #9e9e9e;
    }

    ::v-deep i {
        color: #757575;
    }
}
</style>

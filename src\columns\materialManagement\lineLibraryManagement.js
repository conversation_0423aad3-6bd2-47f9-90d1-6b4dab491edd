export const lineLibraryManagementColumn = [
    { text: '序号', value: 'Index', width: 60 },
    { text: '仓库', value: 'Warehousename', width: 120 },
    { text: 'SAP库位', value: 'SapStore', width: 100 },
    { text: '日期', value: 'CreateDate', width: 160 },
    { text: '物料号', value: 'Materialcode', width: 100 },
    { text: '物料名称', value: 'Materialname', width: 200 },
    { text: '数量', value: 'Num', width: 80, semicolonFormat: true },
    { text: '单位', value: 'Unit', width: 80 },
    { text: '批次', value: 'Batchcode', width: 120 },
    { text: '追溯批次', value: 'Backbatchcode', width: 150 },
    { text: '产线', value: 'Productionline', width: 100 },
    { text: '班组', value: 'PrepareTeam', width: 80 },
    { text: '是否异常', value: 'IsNomarlV', width: 100 },
    { text: '生产日期', value: 'Productiontime', width: 140 },
    { text: '操作', value: 'actions', width: 80 }
]

export const balanceRecordColumn = [
    { text: '序号', value: 'Index', width: 60 },
    { text: '仓库', value: 'Warehousename', width: 150 },
    { text: 'SAP库位', value: 'Sapcode', width: 100 },
    { text: '日期', value: 'ModifyDate', width: 160 },
    { text: '物料号', value: 'Materialcode', width: 120 },
    { text: '物料名称', value: 'Materialname', width: 200 },
    { text: '原有数量', value: 'OldNum', width: 120, semicolonFormat: true },
    { text: '现有数量', value: 'NewNum', width: 120, semicolonFormat: true },
    { text: '单位', value: 'Unit', width: 80 },
    { text: '原因', value: 'Description', width: 180 },
    { text: '', value: 'noActions', width: 0 }
]

export const materialBalanceColumn = [
    { text: '序号', value: 'Index', width: 60 },
    { text: '仓库', value: 'WareHouseName', width: 150 },
    { text: 'SAP库位', value: 'SapHouse', width: 100 },
    { text: '物料号', value: 'Materialcode', width: 100 },
    { text: '物料名称', value: 'Materialname', width: 230 },
    { text: '数量', value: 'Num', width: 80, semicolonFormat: true },
    { text: '单位', value: 'Unit', width: 80 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', value: 'actions', align: 'center', width: 100 }
]

export const intoRecordColumn = [
    { text: '序号', value: 'Index', width: 60 },
    { text: '仓库', value: 'WareHouseName', width: 150 },
    { text: 'SAP库位', value: 'sapcode', width: 100 },
    { text: '日期', value: 'ReceivedDate', width: 140 },
    { text: '物料号', value: 'MaterialCode', width: 140 },
    { text: '物料名称', value: 'MaterialName', width: 230 },
    { text: '数量', value: 'Num', width: 120, semicolonFormat: true },
    { text: '单位', value: 'Unit', width: 80 },
    // { text: '创建时间', value: 'CreateDate', width: 160 },
    // { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '', value: 'noActions', align: 'center', width: 0 }
]

export const lineBalanceColumn = [
    { text: '序号', value: 'Index', width: 60 },
    { text: '产品线', value: 'FullLineName', width: 100 },
    { text: '工段', value: 'CompanyName', width: 130 },
    { text: '工段编码', value: 'CompanyCode', width: 140 },
    { text: '物料号', value: 'MaterialCode', width: 140 },
    { text: '物料描述', value: 'MaterialDescription', width: 200 },
    { text: '数量', value: 'Quantity', width: 120, semicolonFormat: true },
    { text: '单位', value: 'Uom', width: 80 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '', value: 'noActions', align: 'center', width: 0 }
]

export const returnMaterailColumn = [
    { text: '序号', value: 'Index', width: 60 },
    { text: '仓库', value: 'Warehousename', width: 140 },
    { text: '产线', value: 'Productionline', width: 100 },
    { text: 'SAP库位', value: 'SapStore', width: 100 },
    { text: '日期', value: 'CreateDate', width: 160 },
    { text: '物料号', value: 'Materialcode', width: 100 },
    { text: '物料名称', value: 'Materialname', width: 200 },
    { text: '退料数量', value: 'ReturnNum', width: 100, semicolonFormat: true },
    { text: '退料原因', value: 'ReturnReason', width: 150 },
    // { text: '班组', value: 'PrepareTeam', width: 80 },
    // { text: '生产日期', value: 'Productiontime', width: 140 },
    { text: '', value: 'noActions', width: 0 }
]

export const returnMaterailDColumn = [
    { text: '序号', value: 'Index', width: 60 },
    { text: '仓库', value: 'Warehousename', width: 140 },
    { text: '产线', value: 'Productionline', width: 100 },
    { text: 'SAP库位', value: 'SapStore', width: 100 },
    { text: '日期', value: 'CreateDate', width: 160 },
    { text: '物料号', value: 'Materialcode', width: 100 },
    { text: '物料名称', value: 'Materialname', width: 200 },
    { text: '数量', value: 'Num', width: 80, semicolonFormat: true },
    { text: '退料数量', value: 'ReturnNum', width: 100, isEditCell: true },
    { text: '班组', value: 'PrepareTeam', width: 80 },
    { text: '生产日期', value: 'Productiontime', width: 140 },
    { text: '', value: 'noActions', width: 0 }
]
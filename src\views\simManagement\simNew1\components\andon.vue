<template>
  <div class="andon-content">
    <div
      class="andon-item"
      v-for="(item, index) in list"
      :key="index"
      @click="andonClick(item)"
    >
      <div class="item-top">

        <span
          class="iconfont"
          :class="item.icon"
        ></span>

        <!-- <v-icon class="item-icon">{{ item.icon }}</v-icon> -->

        <p class="num-box">{{ item.alarmName }}</p>
      </div>
      <div class="item-btm">
        <span class="alarmCount">{{ item.alarmCount }}</span>
        <span>{{ item.totalCount }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { GetAndonList } from '@/views/simManagement/sim1/service.js';

export default {
  name: "",
  components: {},
  props: {
    searchFormObj: {
      type: Object,
      default: () => { }
    },
  },
  data() {
    return {
      list: [
        {
          icon: 'icon-safe',
          alarmCount: 0,
          totalCount: 0,
          alarmName: '安全'
        },
        {
          icon: 'icon-kucun',
          alarmCount: 0,
          totalCount: 0,
          alarmName: '缺料'
        },
        {
          icon: 'icon-gongyi',
          alarmCount: 0,
          totalCount: 0,
          alarmName: '工艺支持'
        },
        {
          icon: 'icon-agv',
          alarmCount: 0,
          totalCount: 0,
          alarmName: '来料异常'
        },
        {
          icon: 'icon-weixiu',
          alarmCount: 0,
          totalCount: 0,
          alarmName: '设备故障'
        },
        {
          icon: 'icon-quality',
          alarmCount: 0,
          totalCount: 0,
          alarmName: '质量异常'
        },
      ]
    };
  },
  watch: {
    searchFormObj: {
      handler(nv, ov) {
        this.GetAndonList()
      },
      deep: true
    }
  },
  computed: {},
  methods: {
    async GetAndonList() {
      let { response } = await GetAndonList()
      console.log(response, 'Andeng');

      this.list = response

      this.mapFn()
    },
    mapFn() {
      this.list.forEach(item => {
        switch (item.alarmName) {
          case "安全安灯":
            item.icon = 'icon-safe'
            break;
          case "库存安灯":
            item.icon = 'icon-kucun'
            break;
          case "工艺安灯":
            item.icon = 'icon-gongyi'
            break;
          case "生产安灯":
            item.icon = 'icon-agv'
            break;
          case "设备安灯":
            item.icon = 'icon-weixiu'
            break;
          case "质量安灯":
            item.icon = 'icon-quality'
            break;
          default:
            break;
        }
      })
    },
    andonClick(andon) {
      this.$store.commit('SET_MainAlarmType', andon.alarmCode)
      this.$router.push('/andonManagement/alarmRecord');
      // this.$router.push({ path: '/andonManagement/alarmRecord', query: { id: 112121 } });
    }
  },
  created() {
    this.GetAndonList()
  },
  mounted() { }
};
</script>
<style lang="less" scoped>
.andon-content {
    width: 100%;
    height: 100%;
    // border: 1px solid blue;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    // align-content: space-between;
    align-content: space-around;
    .andon-item {
        display: flex;
        width: 32.5%;
        height: 32%;
        // border: 1px solid crimson;
        background: #00c853;
        border-radius: 5px;
        color: #fff;
        cursor: pointer;

        .item-top {
            display: flex;
            height: 100%;
            width: 70%;
            justify-content: space-between;
            flex-direction: column;

            .item-icon {
                width: 70%;
            }

            .iconfont {
                font-size: 30px;
                // width: 65px;
                height: 50%;
                text-align: center;
            }

            .num-box {
                display: flex;
                align-items: flex-end;
                justify-content: center;
                // width: 47%;
                height: 50%;
                text-align: center;
                word-break: keep-all;
                font-size: 14px;
                margin: 0;

                // text-indent: 10px;
                p {
                    margin: 0;
                }
            }
        }

        .item-btm {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            font-size: 20px;
            width: 30%;

            span {
                line-height: 16px;
            }

            .alarmCount {
                color: yellow;
            }
        }
    }
}
</style>

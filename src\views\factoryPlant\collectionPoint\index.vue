<template>
  <div class="usemystyle MaterialPreparation">
    <div class="InventorySearchBox">
      <div class="searchbox pd-left">
        <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
          <el-form-item :label="$t('GLOBAL._SSL')">
            <el-input clearable v-model="filterText" placeholder="快速搜索"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" @click="filterTree">{{ $t('GLOBAL._CX') }}</el-button>
          </el-form-item>
          <!-- <el-form-item>
            <el-button icon="el-icon-download" @click="getInfluxOpcTagExport">{{ $t('GLOBAL._DC') }}</el-button>
          </el-form-item> -->
          <upload-button :option="buttonOption" :searchForm="searchForm" ref="uploadButton"></upload-button>
        </el-form>
      </div>
    </div>
    <div class="tablebox height">
      <div class="left">
        <el-tree ref="tree" :data="treeData" node-key="id" :expand-on-click-node="false" :render-content="renderContent"
          :props="{ label: 'name' }" @node-click="handleNodeClick" :default-checked-keys="defaultCheck"
          :highlight-current="true" :filter-node-method="filterNode">
        </el-tree>
      </div>
      <div class="right">
        <div class="form-box">
          <span class="title">{{ currentTree.name }}</span>
          <el-form size="mini" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
            <el-form-item>
              <el-input clearable v-model="searchForm.Search" placeholder="快速搜索"></el-input>
            </el-form-item>
            <el-form-item :label="$t('CollectionPoint.title.station')">
              <el-select v-model="searchForm.ServerId" placeholder="请选择通讯站点" clearable>
                <el-option v-for="item in stationOptions" :key="item.value" :label="item.Name" :value="item.ID">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('CollectionPoint.title.type')">
              <el-select v-model="searchForm.Type" placeholder="请选择采集类型" clearable>
                <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('CollectionPoint.title.isSave')">
              <el-select v-model="searchForm.IsSave" placeholder="请选择是否储存" clearable>
                <el-option v-for="item in saveOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-search" @click="getSearchBtn">{{ $t('GLOBAL._CX') }}</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="success" icon="el-icon-circle-plus-outline" @click="showDialog({})">{{
                $t('GLOBAL._XZ') }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table class="table" v-loading="loading" border :data="tableData" element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading" style="width: 100%" height="100%">
          <section v-for="(item, index) in tableName" :key="index">
            <el-table-column v-if="item.field === 'IsSave'" :prop="item.field" :label="item.label">
              <template slot-scope="scope">
                <span>{{ scope.row[item.field] ? '是' : '否' }}</span>
              </template>
            </el-table-column>
            <el-table-column v-else-if="item.field === 'Type'" :prop="item.field" :label="item.label">
              <template slot-scope="scope">
                <span>{{ scope.row[item.field] ? '变化时' : '周期性' }}</span>
              </template>
            </el-table-column>
            <el-table-column v-else-if="item.field === 'ServerName'" :prop="item.field" :label="item.label" width="230">
              <template slot-scope="scope">
                <span>{{ scope.row[item.field] }}</span>
              </template>
            </el-table-column>
            <el-table-column v-else-if="item.field === 'Describe'" :prop="item.field" :label="item.label" width="200">
              <template slot-scope="scope">
                <span>{{ scope.row[item.field] }}</span>
              </template>
            </el-table-column>
            <el-table-column v-else :prop="item.field" :label="item.label">
              <template slot-scope="scope">
                <span>{{ scope.row[item.field] }}</span>
              </template>
            </el-table-column>
          </section>
          <el-table-column prop="operation" width="160" :label="$t('GLOBAL._ACTIONS')" align="center">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="showDialog(scope.row)">{{ $t('GLOBAL._BJ') }}</el-button>
              <el-button size="mini" type="text" @click="delRow(scope.row)">{{ $t('GLOBAL._SC') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-pagination class="mt-8p" background :current-page="searchForm.pageIndex" :page-size="searchForm.pageSize"
      layout="->, total, prev, pager, next" :total="total" @current-change="handleCurrentChange" />
    <FormDialog ref="formDialog" @SaveForm="getSearchBtn" />
  </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';
import { getInfluxOpcTaglList, influxOpcTagDelete } from "@/api/factoryPlant/collectionPoint.js";
import { GetEquipmentTree } from "@/api/factoryPlant/physicalModel.js";
import { getInfluxOpcServerList } from "@/api/factoryPlant/communicationStation.js"
import FormDialog from './components/form-dialog'
import {InfluxOpcTagExport} from "@/api/factoryPlant/collectionPoint";
import {configUrl} from "@/config";
import {GetExportData} from "@/api/equipmentManagement/Equip";
import UploadButton from "@/components/UploadButton.vue";

export default {
  name: 'CollectionPoint',
  components: {
    FormDialog,UploadButton
  },
  data() {
    return {
      uploadDisable:false,
      filterText: '',
      searchForm: {
        pageIndex: 1,
        pageSize: 10
      },
      treeData: [],
      defaultCheck: [],
      currentTree: {},
      total: 0,
      tableData: [{}],
      hansObj: this.$t('CollectionPoint.Collection_Point_Table'),
      tableName: [],
      loading: false,
      stationOptions: [],
      typeOptions: [{
        label: '周期性',
        value: 0
      }, {
        label: '变化时',
        value: 1
      }],
      saveOptions: [
        {
          label: '否',
          value: false
        }, {
          label: '是',
          value: true
        }
      ],
      buttonOption:{
        name:'数采点收集',
        serveIp:'baseURL_30015',
        uploadUrl:'/api/InfluxOpcTag/ImportData', //导入
        exportUrl:'/api/InfluxOpcTag/ExportData', //导出
        //DownLoadUrl:'/api/InfluxOpcTag/DownLoadTemplate', //下载模板
      }
    }
  },
  // watch: {
  //   filterText(val) {
  //     this.$refs.tree.filter(val);
  //   }
  // },
  mounted() {
    this.getZHHans()
    this.getTreeData()
    this.getSationData()
  },
  methods: {
    filterTree(){
      this.$refs.tree.filter(this.filterText)
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    async getSationData() {
      const { response } = await getInfluxOpcServerList({})
      this.stationOptions = response
    },
    getZHHans() {
      for (let key in this.hansObj) {
        this.tableName.push({ field: key, label: this.hansObj[key] })
      }
    },
    async getTreeData() {
      const { response } = await GetEquipmentTree({})
      this.treeData = response
      this.currentTree = response.length && response[0]
      this.defaultCheck.push(response.length && response[0].id)
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(response.length && response[0].id)
      });
      this.searchForm.EquipmentId = this.currentTree.id
      this.getTableData()
    },
    handleNodeClick(data) {
      console.log(data);
      this.currentTree = data
      this.searchForm.EquipmentId = this.currentTree.id
      this.getTableData()
    },
    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },
    async getTableData() {
      this.loading = true
      const { response } = await getInfluxOpcTaglList(this.searchForm)
      this.loading = false
      this.tableData = response.data
      this.total = response.dataCount
    },
    delRow(row) {
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        const { msg } = await influxOpcTagDelete([row.ID])
        this.$message.success(msg)
        this.getTableData()
      }).catch(err => {
        console.log(err);
      });
    },
    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },
    showDialog(row) {
      this.$refs.formDialog.show(row)
    },
    async getInfluxOpcTagExport(){
      let params = {
        ...this.searchForm
      };
      const baseUrl3 = configUrl[process.env.VUE_APP_SERVE]['baseURL_Inventory'] + `/api/InfluxOpcTag/ExportData`;
      let res = await GetExportData(baseUrl3, params);
      let binaryData = [];
      binaryData.push(res);
      const url = window.URL.createObjectURL(new Blob(binaryData));
      console.log(url);
      const link = document.createElement('a');
      link.href = url;
      const now = new Date();
      const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
      let fileName = `${formattedDateTime}.xlsx`;
      document.body.appendChild(link);
      link.setAttribute('download', fileName);
      link.click();
      window.URL.revokeObjectURL(link.href);
    },
    //文件上传
    handleSuccess(res){
      this.$message.success(res.msg)
      this.uploadDisable = false
    },
    handleProgress(){
      this.uploadDisable = true
    },
    renderContent(h, { node, data, store }) {
      if (node.level === 1 && !node.expanded) {
        return (
          <span>
            <i class="el-icon-folder"></i>
            <span style='margin-left:5px'>{node.label}</span>
          </span>);
      } else if (node.level === 1 && node.expanded) {
        return (
          <span>
            <i class="el-icon-folder-opened"></i>
            <span style='margin-left:5px'>{node.label}</span>
          </span>);
      } else {
        return (
          <span>
            <i class="el-icon-tickets"></i>
            <span style='margin-left:5px'>{node.label}</span>
          </span>)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}

.mt-8p {
  margin-top: 8px;
}

.height {
  height: 83vh;
  display: flex;
  gap: 10px;

  .left {
    width: 300px;
    height: 100%;
    padding: 10px;
    border: 1px solid #ebeef5;
    overflow-y: auto;

    :deep(.el-icon-caret-right) {
      font-size: 17px;
    }
  }

  .right {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    .form-box {
      padding: 5px 0;

      .title {
        color: #909399;
        font-size: 16px;
        font-weight: 600;
      }

      :deep(.el-form--inline) {
        height: 33px;
      }
    }

    .table {
      flex: 1;
    }
  }
}

.pd-left {
  padding-left: 5px
}
</style>

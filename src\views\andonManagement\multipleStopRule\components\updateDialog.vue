<template>
    <!-- 多次停机规则 -->
    <v-dialog v-model="dialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ operaObj.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ') }}
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" class="mt-8 mb-2">
                    <v-row>
                        <!-- 工段 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.ProductionLineCode"
                                :rules="rules.ProductionLineCode"
                                :items="productLineList"
                                item-text="EquipmentName"
                                item-value="EquipmentCode"
                                :label="$t('$vuetify.dataTable.ANDON_DCTJBJ.ProductionLineCode')"
                                return-objects
                                dense
                                outlined
                            ></v-select>
                        </v-col>
                        <!-- 停机次数 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.Times" :rules="rules.Times" :label="$t('$vuetify.dataTable.ANDON_DCTJBJ.Times')" required dense outlined />
                        </v-col>
                        <!-- 停机累计时长 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.Duration" :rules="rules.Duration" :label="$t('$vuetify.dataTable.ANDON_DCTJBJ.Duration')" required dense outlined />
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn color="normal" @click="closeForm">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { MultistopRuleSaveForm } from '@/api/andonManagement/multipleStopRule.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        productLineList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            checkbox: true,
            valid: true,
            dialog: false,
            form: {
                ID: '',
                ProductionLineId: '',
                ProductionLineCode: '',
                Times: '',
                Duration: ''
            },
            rules: {
                ProductionLineCode: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                Times	: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                Duration: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            }
        };
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal) {
                    for (const key in this.form) {
                        if (Object.hasOwnProperty.call(this.form, key)) {
                            this.form[key] = this.operaObj[key]  
                        }
                    }
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        //
        closeForm() {
            this.$emit('handlePopup', 'refresh');
            this.dialog = false;
        },
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                const { ProductionLineCode } = this.form
                if(ProductionLineCode?.EquipmentCode){
                    const { EquipmentCode, ID } = ProductionLineCode
                    this.form = { ...this.form, ProductionLineId: ID, ProductionLineCode: EquipmentCode }
                }
                const res = await MultistopRuleSaveForm({ ...this.form });
                const { success, msg } = res;
                if(success){
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    this.$refs.form.reset();
                    this.$emit('handlePopup', 'refresh');
                    if (this.operaObj.ID || this.checkbox) {
                        this.closeForm()
                    }
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>
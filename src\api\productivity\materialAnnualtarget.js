import { getRequestResources } from '@/api/fetch';
const baseURL_30015 = 'baseURL_30015'
const DFM = 'baseURL_DFM'


//Losstgt新增
export function GetLosstgtSaveForm(data) {
    const api = '/api/Losstgt/SaveForm'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Losstgt类型
export function GetLosstgtItem(data) {
    const api = '/api/Losstgt/GetItem'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Losstgt删除
export function GetLosstgtDelete(data) {
    const api = '/api/Losstgt/Delete'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Losstgt列表
export function GetLosstgtPageList(data) {
    const api = '/api/Losstgt/GetPageList'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Losstgt导入
export function GetLosstgtImportData(data) {
    const api = '/api/Losstgt/ImportData'
    return getRequestResources(baseURL_30015, api, 'post', data);
}

import request from '@/util/request';
export function ExportData(url, data) {
    return request({
        url: url,
        method: 'post',
        data,
        responseType: 'blob'


    });}
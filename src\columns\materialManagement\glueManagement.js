// dictionary: true, isEditCell: true
export const glueColumns = [
    { text: '序号', value: 'Index', width: '60px' },
    { text: '物料编号', value: 'Materialcode', width: '160px' },
    { text: '胶水型号', value: 'Materialname', width: '160px' },
    { text: '胶管条码', value: 'Serialno', width: '200px' },
    { text: '单管容量', value: 'SingleTubeWeight', width: '160px' },
    { text: '仓库编号', value: 'WarehouseCode', width: '170px' },
    { text: '仓库名称', value: 'WarehouseName', width: '180px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '120px'
    }
];
export const glueSendColumns = [
    { text: '序号', value: 'Index', width: '60px' },
    { text: '物料编号', value: 'Materialcode', width: '160px' },
    { text: '胶水型号', value: 'Materialname', width: '160px' },
    { text: '胶管条码', value: 'Serialno', width: '200px' },
    { text: '单管容量', value: 'SingleTubeWeight', width: '160px' },
    { text: '失效时间', value: 'Expdate', width: '160px' },
    { text: '上下料状态', value: 'IsLoading', width: '150px', dictionary: true },
    { text: '上料时间', value: 'LoadingDate', width: '160px' },
    { text: '下料时间', value: 'UnLoadingDate', width: '160px' },
    { text: '设备编码', value: 'EquipmentCode', width: '160px' },
    { text: '仓库编号', value: 'WarehouseCode', width: '170px' },
    { text: '仓库名称', value: 'WarehouseName', width: '180px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '',
        align: 'center',
        value: 'noActions',
        width: ''
    }
];
export const GlueLoadingUnloadingColumns = [
    { text: '序号', value: 'Index', width: '60px' },
    { text: '设备编码', value: 'EquipmentCode', width: '160px' },
    { text: '物料编号', value: 'Materialcode', width: '160px' },
    { text: '胶管条码', value: 'Serialno', width: '200px' },
    { text: '单管容量', value: 'SingleTubeWeight', width: '160px' },
    // { text: '上下料状态', value: 'IsLoading', width: '150px', dictionary: true },
    { text: '上料时间', value: 'LoadingDate', width: '160px' },
    { text: '注胶时间', value: 'GlueDate', width: '160px' },
    // { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '120px'
    }
];

export const dumpRecordColumns = [
    { text: '序号', value: 'Index', width: '60px' },
    // { text: '物料批次', value: 'Materialbatchno', width: '120px' },
    { text: '物料编号', value: 'Materialcode', width: '150px' },
    { text: '胶水型号', value: 'Materialname', width: '160px' },
    // { text: '来料时间', value: 'Indate', width: '160px' },
    { text: '保质期(天)', value: 'QualityGuaranteePeriod', width: '160px' },
    { text: '大瓶胶水提醒时间(小时)', value: 'QualityConfirmTime', width: '160px' },
    { text: '小瓶胶水预警时间(分钟)', value: 'QualityNoticeTime', width: '160px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '130px'
    }
];

export const glueDetailsColumns = [
    { text: '序号', value: 'Index', width: '80px' },
    { text: '物料编号', value: 'WarehouseCode', width: '180px' },
    { text: '物料名称', value: 'AGVWarehouseName', width: '180px' },
    { text: '胶管编号', value: 'BelongAreaCode', width: '220px' },
    { text: '生成时间', value: 'ModifyDate', width: '160px' },
    {
        text: '',
        align: 'center',
        value: 'noActions',
        width: '0'
    }
];

export const glueDialogColumns = [
    { text: '序号', value: 'Index', width: '80px' },
    { text: '物料编号', value: 'Materialcode', width: '160px' },
    { text: '胶水型号', value: 'Materialname', width: '160px' },
    { text: '单管容量', value: 'SingleTubeWeight', width: '160px', isEditCell: true },
    { text: '仓库编号', value: 'WarehouseCode', width: '170px' },
    { text: '仓库名称', value: 'WarehouseName', width: '180px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '100px'
    }
];
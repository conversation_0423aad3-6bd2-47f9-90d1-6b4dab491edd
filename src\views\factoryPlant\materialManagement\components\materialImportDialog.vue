<template>
    <v-dialog v-model="materialImportDialog" persistent max-width="980px">
        <!-- 物料导入 -->
        <v-card ref="form" class="material-import">
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                物料导入
                <v-icon @click="materialImportDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-file-input chips multiple label="File input w/ chips"></v-file-input>
                <v-container></v-container>
            </v-card-text>
            <v-card-actions class="grey lighten-3">
                <v-checkbox label="确定并关闭窗口"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="materialImportDialog = false">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="materialImportDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { getSaveForm, getDelete } from '@/api/factoryPlant/supplier.js';
export default {
    name: 'MaterialImportDialog',
    data() {
        return {
            // 提交表单数据
            valid: true,
            materialImportDialog: false,
            checkbox: false
        };
    },
    computed: {
        editformModel() {
            return {
                causeCode: this.editList.causeCode,
                causeName: this.editList.causeName,
                causeType: this.editList.causeType
            };
        }
    },
    methods: {
        //新增
        addClick() {},
        async addSubmit() {
            let params = {
                isOutsourcing: this.isOutsourcing,
                supplierCode: this.supplierCode,
                supplierName: this.supplierName,
                tel: this.tel,
                address: this.address,
                remark: this.remark,
                id: '',
                createUserId: 'text'
            };
            let res = await getSaveForm(params);
            let { status, success, msg } = res;
            alert(msg);
            this.dialog = false;
            this.$parent.supplierList();
        },
        //编辑
        async editSubmit() {
            let params = {
                isOutsourcing: this.editList.IsOutsourcing,
                supplierCode: this.editList.SupplierCode,
                supplierName: this.editList.SupplierName,
                tel: this.editList.Tel,
                address: this.editList.Address,
                remark: this.editList.Remark,
                id: this.editList.ID,
                createUserId: 'text',
                updateTimeStamp: this.editList.UpdateTimeStamp,
                modifyUserId: this.editList.ModifyUserId
            };
            let res = await getSaveForm(params);
            let { status, success, msg } = res;
            this.dialog = false;
            this.$parent.supplierList();
        },
        // 删除
        async delSubmit() {
            await getDelete([this.deleteList.ID]);
            this.dialog = false;
            this.$parent.supplierList();
        }
    }
};
</script>
<style lang="scss" scoped>
.material-import-title {
    width: 100%;
    height: 58px !important;
    display: flex;
    justify-content: flex-start;
}
.v-text-field {
    margin-right: 16px;
}
.col-12 {
    padding: 0;
}
.v-sheet.v-card {
    border-radius: 10px;
}
</style>
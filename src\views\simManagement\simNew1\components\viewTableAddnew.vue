<template>
  <el-dialog
    :style="backgroundVar"
    :append-to-body="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="value"
    :before-close="handleClosetable"
    lock-scroll
    :fullscreen="true"
    title="KPI模型选择"
  >
    <div class="styleTable">
      <el-table
        :data="tableList"
        border
        style="width: 100%;margin-top: 20px;color:#fff; font-size: 12px;font-weight: bold;"
        :header-cell-style="{background:'#fafafa',textAlign: 'center'}"
        :row-style="{height: '35px'}"
        ref="vxeTable"
        @selection-change="handleSelectionChange"
        :height="tableHeight"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
        >
        </el-table-column>
        <el-table-column
          align="center"
          prop="KpiName"
          label="KPI模型名称"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          align="center"
          prop="KpiCode"
          label="KPI模型Code"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          align="center"
          prop="Unit"
          label="单位"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          align="center"
          prop="SqlText"
          label="SQL"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          align="center"
          prop="Description"
          label="描述"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="操作"
          fixed="right"
          header-align="center"
          align="center"
          width="160"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-view"
              @click.native="look(scope.row,scope.$index)"
            >
              弹窗配置
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- <div style="font-size: 14px;color: #fff;width: 50px;height: 25px;line-height: 25px;text-align: center;float: right;border-radius: 3px;background-color: red;">保 存</div> -->
      <el-button
        style="float: right;margin-top: 20px;"
        type="primary"
        size="mini"
        @click="submitForm"
      >保 存</el-button>
    </div>
    <dutyList
      v-if="dutyListshow"
      v-model="showdutyList"
      :Position="Position"
      :dutIndex="dutIndex"
      :fullscreen="fullscreen"
      @checkdut="dutHandle"
      :backgroundImg="backgroundImg"
    />
  </el-dialog>
</template>
<script>
import { GetKpiList, SaveConfig, GetChartStructure } from '@/views/simManagement/components/chartsConfig/service.js';
import { getDataSourceList, getEditEntityByCode } from '@/api/simConfig/simconfignew.js';

export default {
  components: {
    dutyList: () => import('@/views/simManagement/simNew1/components/dutyList.vue'),
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    KpiChartId: {
      type: String,
      default: ''
    },
    searchFormObj: {
      type: Object,
      default: () => { }
    },
    curDay: {
      type: Object,
      default: () => { }
    },
    Position: {
      type: String,
      default: ''
    },
    fullscreen: {
      type: Boolean,
      default: false
    },
    backgroundImg: {
      type: String,
      default: ''
    },
    Order: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableHeight: 0,
      dutyListshow: false,
      showdutyList: false,
      radio: '1',
      options: [
        { label: '是', value: '1' },
        { label: '否', value: '0' },
      ],
      tableList: [],
      dutyListData: [],
      multipleSelection: [],
      path1: false,
    }
  },
  computed: {
    backgroundVar() {
      return {
        '--background': this.backgroundImg
      }
    },
  },
  watch: {
    $route(to, from) {
      console.log(to.path, 'to.path');
      if (to.path == '/simManagement/simSpot') {
        this.path1 = true
      } else {
        this.path1 = false
      }
    }
  },
  created() {
    this.GetKpiListFn()
    // this.query()
  },
  mounted() {
    this.$nextTick(function () {
      this.tableHeight = window.innerHeight - 170;
      let self = this;
      window.onresize = function () {
        self.tableHeight = window.innerHeight - 170
      }
    })
  },
  methods: {
    look(row, index) {
      this.dutIndex = index
      this.dutyListshow = true
      this.showdutyList = true
    },
    dutHandle(data1, data2, data3, data4) {
      this.dutyListshow = false
      this.tableList.map((el, index) => {
        if (data3 == index) {
          this.tableList[index].isFlag = data1
          this.tableList[index].configList = data2
          this.tableList[index].configList1 = data4
        }
      })
    },
    handleSelectionChange(val) {
      const h = this.$createElement;
      if (val.length > 1) {
        this.$notify({
          title: '提示',
          message: h('i', { style: 'color: #000000' }, '只能勾选一条数据')
        });
        return
      }
      this.multipleSelection = val;
    },

    handleClosetable() {
      this.$emit('closePopup')
    },
    //获取列表
    async GetKpiListFn() {
      const params = {
        SimLevel: this.Position.split('-')[0],
        "KpiType": "",
        "isSql": true
      }
      let { response } = await GetKpiList(params)
      if (!response) return
      this.tableList = response
      // this.tableList = response.filter(item => {
      //   return item.CalculationType == "1"
      // })
      this.tableList.map(el => {
        el.isFlag = '1'
        el.configList = ''
      })
    },
    async query() {
      const params = {
        "SimLevel": this.$route.path == '/simManagement/simSpot' ? 'SIM2' : this.Order.split('-')[0],
        "KpiType": "",
        "isSql": true
      }
      const res = await getDataSourceList(params);
      res.response.map(el => {
        this.dutyListData.push({
          "text": el.KpiName,
          "value": el.KpiCode
        })
      })
    },
    //确定
    async submitForm() {
      console.log(this.$route.path, 'this.$route.path');

      const h = this.$createElement;
      if (this.multipleSelection.length > 1 || this.multipleSelection.length <= 0) {
        this.$notify({
          title: '提示',
          message: h('i', { style: 'color: #000000' }, '只能勾选一条数据')
        });
        return
      }
      let selectRecords = this.multipleSelection[0]
      if (!selectRecords) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
        return false;
      }
      this.SaveConfigFn(selectRecords)
    },
    //保存
    async SaveConfigFn(selectRecords) {

      const h = this.$createElement;
      let params = {
        ID: this.KpiChartId,
        // SimLevel: this.$route.name,
        SimLevel: this.$route.path == '/simManagement/simSpot' ? 'SIM2' : this.Order.split('-')[0],
        Position: this.Position,
        KpiName: selectRecords.KpiName,
        KpiCode: selectRecords.KpiCode,
        IsPopu: selectRecords.isFlag,
      }
      let { status, msg, success } = await SaveConfig(params)
      let msg1 = msg
      if (success) {
        this.$emit('closePopup');
        this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
        // this.addListFn()
        this.$emit('addListFn');
      } else {
        this.$notify({
          title: '提示',
          message: h('i', { style: 'color: #000000' }, msg1)
        });
      }

      if (selectRecords.configList != '' && selectRecords.configList != null && selectRecords.configList != undefined) {
        let params1 = {
          ID: this.KpiChartId,
          // SimLevel: this.$route.name,
          SimLevel: this.$route.path == '/simManagement/simSpot' ? 'SIM2' : this.Order.split('-')[0],
          Position: this.Position + '-1',
          KpiName: selectRecords.configList1,
          KpiCode: selectRecords.configList,
          // IsSql: selectRecords.isFlag
        }
        const res = await SaveConfig(params1)
        let msg2 = res.msg
        if (res.success) {
          this.$emit('closePopup');
          this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
          // this.addListFn()
          this.$emit('addListFn');
        } else {
          this.$notify({
            title: '提示',
            message: h('i', { style: 'color: #000000' }, msg2)
          });
        }
      }
      this.$emit('closePopup');

    },
  }

}
</script>
<style scoped>
.messageIndex {
    z-index: 300000 !important;
}
.titName {
    width: 160px;
    height: 10%;
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    text-align: center;
    cursor: pointer;
    margin-right: 20px;
}
.active {
    color: #409eff;
    font-weight: bold;
    border-right: 2px solid #409eff;
    box-shadow: 0px 0px 10px #fff;
    border-radius: 5px;
    font-size: 22px;
}
/deep/ .el-dialog {
    background: var(--background) no-repeat 0 0;
    background-size: 100% 100% !important;
    overflow: hidden;
}
.styleTable /deep/.el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}
.styleTable /deep/ .el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}

.styleTable /deep/ .el-table tr {
    background-color: transparent !important;
    border: none;
}
.styleTable /deep/ .el-table--enable-row-transition .el-table__body td,
.el-table .cell {
    background-color: transparent !important;
}
.styleTable /deep/ .el-table th.el-table__cell {
    background-color: transparent !important;
    color: #fff;
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}
/deep/ .el-dialog__title {
    color: #fff !important;
}
/deep/ .v-select__selection,
.v-select__selection--comma {
    color: #fff !important;
}
/deep/ .v-label,
.v-label--active,
.theme--light {
    color: #fff !important;
}
/deep/ .theme--light.v-text-field > .v-input__control > .v-input__slot:before {
    border-color: #fff !important;
}
</style>
// 人员信息
export const personalColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '头像',
        value: 'UserAvatar',
        width: 75,
        sortable: true
    },
    {
        text: '员工号',
        value: 'Code',
        width: 100,
        sortable: true
    },
    {
        text: '姓名',
        value: 'Name',
        width: 80,
        sortable: true
    },
    {
        text: '性别',
        value: 'Gender',
        width: 80,
        sortable: true
    },
    {
        text: '工厂/部门',
        value: 'OrganizationName',
        width: 180,
        sortable: true
    },
    // {
    //     text: '物料号',
    //     value: 'MaterialCode',
    //     width: 140,
    //     sortable: true
    // },
    // {
    //     text: '计件岗位',
    //     value: 'PieceRatePosition',
    //     width: 120,
    //     sortable: true
    // },
    // {
    //     text: '职称',
    //     value: 'JobTitle',
    //     width: 100,
    //     sortable: true
    // },
    // {
    //     text: '基础工段',
    //     value: 'Segment',
    //     width: 100,
    //     sortable: true
    // },

    // {
    //     text: '工序',
    //     value: 'Process',
    //     width: 140,
    //     sortable: true
    // },
    // {
    //     text: '岗位',
    //     value: 'PostNames',
    //     width: 180,
    //     sortable: true
    // },
    // {
    //     text: '职位',
    //     value: 'RankId',
    //     width: 140,
    //     sortable: true
    // },
    {
        text: '星级',
        value: 'StarLevelId',
        width: 100,
        sortable: true
    },

    {
        text: '上级领导',
        value: 'LeaderName',
        width: 100,
        sortable: true
    },
    // {
    //     text: '班制',
    //     value: 'ShiftSystem',
    //     width: 80,
    //     sortable: true
    // },
    // {
    //     text: '工段/班组',
    //     value: 'WorkshopSectionName',
    //     width: 180,
    //     sortable: true
    // },
    // {
    //     text: '产线',
    //     value: 'DutyLineNames',
    //     width: 240,
    //     sortable: true
    // },
    // {
    //     text: '安灯事件岗位',
    //     value: 'AndonPost',
    //     width: 160,
    //     sortable: true
    // },
    {
        text: '是否计件',
        value: 'IsPiecework',
        align: 'center',
        isSwitch: true,
        width: 100,
        sortable: true
    },
    {
        text: '是否全职',
        value: 'IsFulltime',
        align: 'center',
        isSwitch: true,
        width: 100,
        sortable: true
    },
    {
        text: '精力占比',
        value: 'EnergyPercent',
        width: 100,
        sortable: true
    },
    {
        text: '入职时间',
        value: 'EntryDate',
        width: 160,
        sortable: true
    },
    {
        text: '离职时间',
        value: 'DimissionDate',
        width: 140,
        sortable: true
    },
    {
        text: '离职方式',
        value: 'DimissionType',
        width: 100,
        sortable: true
    },
    {
        text: '备注',
        value: 'Remark',
        width: 180,
        sortable: true
    },
    {
        text: '飞书',
        value: 'Feishu',
        width: 140,
        sortable: true
    },
    {
        text: '微信',
        value: 'Wechat',
        width: 140,
        sortable: true
    },
    {
        text: '手机',
        value: 'Phone',
        width: 140,
        sortable: true
    },
    { text: '操作', width: 180, align: 'center', value: 'actions', sortable: true }
];
// 班组
export const staffGroupColums = [
    {
        text: '编号',
        value: 'FactoryId',
        width: 120,
        sortable: true
    },
    {
        text: '班组',
        value: 'DepartementId',
        width: 120,
        sortable: true
    },
    { text: '操作', width: 120, align: 'center', value: 'actions', sortable: true }
];
// 技能
export const SkillColums = [
    {
        text: '技能编号',
        value: 'Code',
        width: 120,
        sortable: true
    },
    {
        text: '技能',
        value: 'Name',
        width: 120,
        sortable: true
    },
    {
        text: '星级',
        value: 'StarLevel',
        width: 120,
        sortable: true
    },
    { text: '操作', width: 120, align: 'center', value: 'actions', sortable: true }
];
// 人员技能映射
export const staffSkillColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '员工号',
        value: 'StaffCode',
        width: 100,
        sortable: true
    },
    {
        text: '员工名称',
        value: 'StaffName',
        width: 100,
        sortable: true
    },
    {
        text: '技能列表',
        value: 'SkillNames',
        width: 500,
        sortable: true
    }
];

// 人员岗位映射
export const posStaffColums = [
    {
        text: '岗位编号',
        value: 'FactoryId',
        width: 120,
        sortable: true
    },
    {
        text: '工段（岗位）',
        value: 'DepartementId',
        width: 120,
        sortable: true
    },
    {
        text: '员工名称',
        value: 'DepartementId',
        width: 120,
        sortable: true
    },
    { text: '操作', width: 120, align: 'center', value: 'actions', sortable: true }
];
// 人员出勤
export const presentColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '员工号',
        value: 'FactoryId',
        width: 120,
        sortable: true
    },
    {
        text: '名称',
        value: 'DepartementId',
        width: 120,
        sortable: true
    },
    {
        text: '上班时间',
        value: 'DepartementId',
        width: 120,
        sortable: true
    },
    {
        text: '下班时间',
        value: 'DepartementId',
        width: 120,
        sortable: true
    },
    {
        text: '状态',
        value: 'DepartementId',
        width: 120,
        sortable: true
    },
    { text: '操作', width: 120, align: 'center', value: 'actions', sortable: true }
];

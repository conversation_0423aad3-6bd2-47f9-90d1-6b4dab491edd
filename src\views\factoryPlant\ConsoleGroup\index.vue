<template>
  <div class="usemystyle MaterialPreparation">
    <div class="InventorySearchBox">
      <div class="searchbox pd-left">
        <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
          <el-form-item :label="$t('GLOBAL._SSL')">
            <el-input clearable v-model="searchForm.Key"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" @click="getSearchBtn()">{{ $t('GLOBAL._CX') }}</el-button>
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="success" icon="el-icon-circle-plus-outline" @click="showDialog({})">{{
                $t('GLOBAL._XZ')
              }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="tablebox height">
      <el-table v-loading="loading" :data="tableData" element-loading-text="拼命加载中"
                element-loading-spinner="el-icon-loading" style="width: 100%" height="100%">
        <el-table-column v-for="(item, index) in tableName" :key="index" :prop="item.field" :label="item.label">
          <template slot-scope="scope">
            <span v-if="item.field === 'ConsoleGroup'">
              <i class="el-icon-document" @click="showDrawer(scope.row)"></i>
              {{ scope.row[item.field] }}
            </span>
            <span v-if="item.field === 'Type'">
              {{ filterField(scope.row[item.field])}}
            </span>
            <el-tag v-if="item.field === 'Status' && !scope.row[item.field]" type="info" effect="dark"
                    size="mini">inactive
            </el-tag>
            <el-tag v-if="item.field === 'Status' && scope.row[item.field] === '0'" type="info" effect="dark"
                    size="mini">inactive
            </el-tag>
            <el-tag v-if="item.field === 'Status' && scope.row[item.field] === '1'" effect="dark" size="mini">active
            </el-tag>
            <span v-if="item.field !== 'Status' && item.field !== 'ConsoleGroup'&& item.field !== 'Type'">{{ scope.row[item.field] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="operation" width="160" :label="$t('GLOBAL._ACTIONS')" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="showDialog(scope.row)">{{ $t('GLOBAL._BJ') }}</el-button>
            <el-button size="mini" type="text" @click="delRow(scope.row)">{{ $t('GLOBAL._SC') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination class="mt-8p" background :current-page="searchForm.pageIndex" :page-size="searchForm.pageSize"
                   layout="->, total, prev, pager, next" :total="total" @current-change="handleCurrentChange"/>
    <FormDialog ref="dialog" @saveForm="getSearchBtn"/>
    <Drawer ref="Drawer" @saveForm="getSearchBtn"></Drawer>
  </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';
import FormDialog from "./components/form-dialog.vue"
import Drawer from "./components/drawer.vue"
import {deleteSapSegmentMaterial} from "@/api/productionManagement/Formula";
import {getDataDictionary} from "@/util/dataDictionary";
import {
  delEquipmentGroup,
  delLabelPrinterSize,
  getEquipmentGroupList,
  getLabelCountryList,
  getLabelPrinterSizeList
} from "@/api/systemManagement/labelPrint";
import {GetDFMYesNo} from "@/api/systemManagement/dataDictionary";

export default {
  name: 'LabelSize',
  components: {
    FormDialog,
    Drawer
  },
  data() {
    return {
      searchForm: {
        Key: '',
        pageIndex: 1,
        pageSize: 10,
      },
      total: 0,
      tableData: [{
        Description: 'Canada',
        Culture: 'en-US'
      }],
      hansObj: this.$t('ConsoleGroup.table'),
      tableName: [],
      loading: false,
      TypeList: []
    }
  },
  mounted() {
    this.getZHHans()
    this.getTypeList()
  },
  methods: {
    getZHHans() {
      for (let key in this.hansObj) {
        this.tableName.push({field: key, label: this.hansObj[key]})
      }
    },
    showDialog(row) {
      this.$refs.dialog.show(row)
    },
    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },
    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },
    delRow(row) {
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(() => {
        delEquipmentGroup([row.ID]).then(res => {
          this.$message.success(res.msg)
          this.getTableData()
        })
      }).catch(err => {
        console.log(err);
      });
    },
    getTableData() {
      this.loading = true
      getEquipmentGroupList(this.searchForm).then(res => {
        this.loading = false
        this.tableData = res.response.data
        this.total = res.response.dataCount
      })
    },
    showDrawer(row) {
      this.$refs['Drawer'].show(row)
    },
    getTypeList() {
      GetDFMYesNo({ItemCode: 'EquipmentGroupType'}).then(res => {
        this.TypeList = res.response
        this.getTableData()
      })
    },
    filterField(val){
      let obj =  this.TypeList.find(e=>{
        return e.ItemValue === val
      })
      if(obj){
        return obj.ItemName
      }
      return '--'
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}

.mt-8p {
  margin-top: 8px;
}

.height {
  height: 78vh;

  i {
    margin-right: 5px;
    font-size: 15px !important;
    color: #67c23a;
  }
}

.pd-left {
  padding-left: 5px
}
</style>

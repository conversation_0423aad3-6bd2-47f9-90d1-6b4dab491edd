<template>
  <div>
    <div style="display: flex;width: 100%;height: 30px;">
      <!-- <div
        style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;cursor: pointer;"
        @click="routeChange()"
      >{{ title1 }}</div> -->
      <div
        style="width: 70%;"
        class="titimgbox"
        @click="routeChange()"
      >
        <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
        <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ title1 }}</div>
      </div>
      <div style="width: 30%;display: flex;">
        <dayMonIndex
          :simlevel='simlevel'
          :position="Order"
          @showChack="getExcelhx"
          :Dimension="Dimension"
          :Particle="Particle"
          :id1="id1"
          :BaseTime="BaseTime"
          :titlebartran="title"
          :echarstType="3"
          :backgroundImg="backgroundImg"
        />
      </div>
    </div>
    <div
      :id="id1"
      style="width:100%;height:100%;"
    ></div>
    <!-- <div
      v-if="!showData"
      style="font-size: 14px;color: #fff;text-align: center;margin-top: -100px;"
    >暂无数据</div> -->
    <!-- <keyIndicatorslist
      ref="keyIndicatorsref"
      :exhibitionType="exhibitionType"
      :jtitle="title"
      :simlevel="simlevel"
      :Order="Order"
      :isSql="0"
      :BaseTime="BaseTime"
      :barName="barName"
    ></keyIndicatorslist> -->

    <keyIndicatorslistnew
      ref="keyIndicatorsrefnew"
      v-if="keyIndicatorslistnewShow"
      v-model="showkeyIndicatorslistnew"
      :exhibitionType="exhibitionType"
      :jtitle="title"
      :simlevel="simlevel"
      :Order="Order"
      :isSql="isSql1"
      :BaseTime="BaseTime1"
      :barName="barName"
      :backgroundImg="backgroundImg"
      :Particle="Particle1"
      :legendData="legendData"
      @keynew="heandleKeybarh"
    ></keyIndicatorslistnew>
  </div>
</template>
<script>
import { getqueryZ, getqueryLcr, getChartStructure, getTableList } from '@/api/simConfig/simconfignew.js';

export default {
  components: {
    dayMonIndex: () => import('@/views/simManagement/simNew1/components/dayMonIndex.vue'),
    // keyIndicatorslist: () => import('@/views/simManagement/simNew1/components/keyIndicatorslist.vue'),
    keyIndicatorslistnew: () => import('@/views/simManagement/simNew1/components/keyIndicatorslistnew.vue'),
  },
  props: {
    id1: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    Order: {
      type: String,
      default: ''
    },
    configFlag: {
      type: String,
      default: ''
    },
    exhibitionType: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    Dimension: {
      type: Array,
      default: () => []
    },
    routeList: {
      type: String,
      default: ''
    },
    backgroundImg: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    BaseTime1: '',
    legendData: [],
    isSql1: '',
    Particle1: '',
    title1: '',
    keyIndicatorslistnewShow: false,
    showkeyIndicatorslistnew: false,
    barName: '',
    showData: true,
    Particle: '',
    jtitle: '',
    curConfig: {},
    // BaseTime: '2024-07-01',
    // TeamCode: 'A1180240614',
    // ProductionLineCode: 'A11802406',
    // FactoryCode: 'A118024',
    barTransverseChart: null,
    scrollTime: null,
    //当前时间颗粒度
    curShift: {
      KpiValues: []
    },
    myShiftList: [],
    id: '',
    yAxisOption: {
      type: 'value',
      // show: false
      axisLine: {
        show: false
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: "#fff", //X轴文字颜色
          fontSize: 13
        },
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      },


    },
    // styless: {
    //   width: '100%',
    //   height: '100%',
    //   backgroundImage: 'url("https://img1.baidu.com/it/u=2756664614,3290369440&fm=253&fmt=auto&app=138&f=JPEG?w=753&h=500")',
    //   backgroundSize: '100%',
    // }
  }),
  computed: {
    //x轴配置
    xAxisOption() {
      let list = ['7', '6', '5', '4', '3', '2', '1']
      if (this.curShift.ChartData && this.curShift.ChartData.x) {
        list = this.curShift.ChartData.x.map(item => {
          // let month = dayjs(item).$M+1
          // let day = dayjs(item).$D
          if (['日', '周'].includes(this.curShift.TimeDimension)) {
            let month = item.split('-')[1]
            let day = item.split('-')[2]
            // 这里需要匹配颗粒度,来输出不同的x轴数据.
            // return `${month}-${day}`
            return `${month}-${day}`
          } else if (['月', '季度'].includes(this.curShift.TimeDimension)) {
            let label = item.split('年')[1]
            return label
          } else {
            return item
          }
        })
        // list.reverse()
        // list = this.curShift.ChartData.x
      }
      return {
        type: 'category',
        // boundaryGap: false,
        // axisLine: {
        //   onZero: false,
        //   lineStyle: {
        //     color: '#888'
        //   },
        // },
        axisLabel: {
          show: true,
          textStyle: {
            color: "#fff" //X轴文字颜色
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            onZero: true,
            color: '#fff' // 设置轴线的颜色为白色
          }
        },
        data: list
      }
    },
    //目标值配置
    lineVisualMap() {
      // let list = []
      let obj = {
        show: false,
        // seriesIndex: 0,
        dimension: this.curConfig.ChartType === '3' ? '0' : '1',
        // dimension: 0,
        pieces: [
          {
            gt: 0,
            // lte: kpi.targetValue,
            lte: this.curShift.TargetValue,
            // color: kpi.noReachColor
            color: this.curShift.BelowTargetColer
            // color: 'red'
          },
          {
            // gt: kpi.targetValue,
            gt: this.curShift.TargetValue,
            // lte: 200,
            // color: kpi.reachColor
            color: this.curShift.AboveTargetColor
            // color: 'yellow'

          },
        ],
        // outOfRange: {
        //   color: '#999'
        // }
      }
      return [obj]

      // return [obj,obj2]
    },
    //折线图 Series  柱状图Series
    lineSeries() {
      if (this.curShift.ChartData.x.length <= 0) {
        this.$nextTick(() => {
          const dom = document.getElementById(this.id1);
          dom.innerHTML = '<div class="noDataBox" style="text-align:center;padding-top:50px;color:#fff;">暂无数据</div>';
          dom.removeAttribute('_echarts_instance_');
          return
        })
      }
      //区分横竖
      let axisMarkLine = this.curConfig.ChartType === '3'
        ? [{ xAxis: this.curShift.TargetValue || '' }]
        : [{ yAxis: this.curShift.TargetValue || '' }]

      // let obj2 = {
      //   name: `${this.curShift.KpiName}实际值`,
      //   type: ['2','3'].includes(this.curConfig.ChartType)?'bar':'line',
      //   symbol: 'circle',
      //   symbolSize: 4,
      //   data: this.curShift.KpiValues.map(item=>item.DataValue),
      //   markLine: {//目标值线条
      //     silent: true,
      //     lineStyle: {
      //       color: this.curShift.TargetColor || 'gray'
      //       // color: 'red'
      //     },
      //     data: axisMarkLine
      //     // data: [{xAxis: 20 }]
      //   }
      // }
      let list = []
      Object.keys(this.curShift.ChartData).forEach(key => {
        if (['x', 'x', '目标值'].includes(key)) {
          return
        }
        let obj = {
          // name: `${this.curShift.KpiName}实际值`,
          // name: key.split(':')[1],
          name: `${key}实际值`,
          type: ['2', '3'].includes(this.curConfig.ChartType) ? 'bar' : 'line',
          symbol: 'circle',
          symbolSize: 4,
          barWidth: 14,
          itemStyle: {
            normal: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                offset: 0,
                color: '#4391F4' // 0% 处的颜色
              }, {
                offset: 1,
                color: '#6B74E4' // 100% 处的颜色
              }], false),
              barBorderRadius: [6, 6, 6, 6]
            }
          },
          // data: this.curShift.KpiValues.map(item=>item.DataValue),
          data: this.curShift.ChartData[key],
          label: {
            show: true,
            position: 'top',
            textStyle: {
              color: '#fff'
            }
          },
          markLine: {//目标值线条
            silent: true,
            lineStyle: {
              color: this.curShift.TargetColor || 'gray'
              // color: 'red'
            },
            data: axisMarkLine
            // data: [{xAxis: 20 }]
          }
        }
        list.push(obj)
      })
      return list
      // return [obj2]
    },
    //折线图配置
    lineOption() {
      return {
        symbol: 'circle',
        tooltip: {
          trigger: 'axis',
          extraCssText: 'z-index:999',
          axisPointer: {
            type: 'shadow',
          }
        },
        // legend: {
        //   data: this.lineLegend
        // },
        grid: {
          left: '5%',
          right: '6%',
          bottom: '3%',
          top: 20,
          containLabel: true
        },
        // visualMap: this.lineVisualMap,
        xAxis: this.xAxisOption,
        yAxis: this.yAxisOption,
        series: this.lineSeries
      }
    },
    bar1Option() {
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          }
        },
        // legend: {
        //   data: this.lineLegend
        // },
        grid: {
          left: '5%',
          right: '6%',
          bottom: '3%',
          top: '11%',
          containLabel: true
        },
        // visualMap: this.lineVisualMap,
        xAxis: this.xAxisOption,
        yAxis: this.yAxisOption,
        series: this.lineSeries
      }
    },
    bar2Option() {
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          }
        },
        // legend: {
        //   data: this.lineLegend
        // },
        grid: {
          left: '5%',
          right: '4%',
          bottom: '3%',
          top: 20,
          containLabel: true
        },
        // visualMap: this.lineVisualMap,
        xAxis: this.yAxisOption,
        yAxis: this.xAxisOption,
        series: this.lineSeries
      }
    },
    //饼图配置
    pieOption() {
      return {
        tooltip: {
          trigger: 'item'
        },
        // legend: {
        //   orient: 'vertical',
        //   left: 'left'
        // },
        series: [
          {
            name: `${this.curShift.KpiName}`,
            type: 'pie',
            radius: '50%',
            // data: this.xAxisOption.data.map((item,index)=>{
            //   return {
            //     value: this.KPIList[0].practicalValueList[index],
            //     name: item
            //   }
            // }),
            data: this.curShift.KpiValues.map(item => {
              return {
                value: item.DataValue,
                name: item.DataTime
              }
            }),
            // [
            //   { value: 1048, name: 'Search Engine' },
            //   { value: 735, name: 'Direct' },
            //   { value: 580, name: 'Email' },
            //   { value: 484, name: 'Union Ads' },
            //   { value: 300, name: 'Video Ads' }
            // ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
    },
    //右侧时间列表
    // myTimeList(){
    //   return this.curConfig.timeTypeList.map(item=>{
    //     let target = this.mapList.find(foo=>foo.value == item)
    //     return target
    //   })
    // },
    //左侧班次列表
    // myShiftList(){
    //   if(this.curTime.name == '班次'){
    //     return this.shiftList1
    //   }else if(['日班','月班'].includes(this.curTime.name) ){
    //     return this.shiftList2
    //   }else{
    //     return []
    //   }
    // }
  },
  created() {
    this.getBarList()
  },
  mounted() {
  },
  methods: {
    heandleKeybarh() {
      this.keyIndicatorslistnewShow = false
    },
    routeChange() {
      if (this.routeList != '' && this.routeList != null && this.routeList != undefined) {
        this.$router.push({ path: `${this.routeList}` })
      }
      // this.$router.push({ path: 'simNew2', query: { code: item.Simcode } });
    },
    // openPopup() {
    //   if (this.configFlag == '是') {
    //     this.$refs.keyIndicatorsref.showDialog = true;
    //   }
    // },
    getExcelhx(data) {
      this.Particle = data
      this.getBarList()
    },
    async getBarList() {
      let params = {
        "Position": this.Order,
        "BaseTime": this.BaseTime,
        "TeamCode": this.simlevel,
        // "ProductionLineCode": this.ProductionLineCode,
        // "FactoryCode": this.FactoryCode
      }
      let { response } = await getChartStructure(params)
      this.curConfig = response
      if (this.curConfig.IsSql == '1') {
        this.getBarList1()
      } else {
        if (this.curConfig?.ChartConfigs != null) {
          if (this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit == undefined) {
            this.title1 = this.title
          } else {
            this.title1 = this.title + '(' + this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit + ')'
          }
          // this.id = this.curConfig.ID;
          // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
          this.curConfig?.ChartConfigs.map(item => {
            item.KpiName = this.curConfig?.ChartConfigs.KpiName
            if (item.KpiValues[0]) {
              item.KpiCode = item.KpiValues[0].KpiCode
              item.TargetValue = item.KpiValues[0].TargetValue || 0
            }
          })
          //图表配置整体赋值
          // this.curConfig = response
          //时间颗粒度列表
          this.myShiftList = this.curConfig?.ChartConfigs.filter(item => {
            return item.TargetVisible === 1
          })
          //默认激活第一个时间颗粒度
          if (this.Particle != '') {
            this.myShiftList.map((el, index) => {
              if (this.Particle == el.TimeDimension) {
                this.curShift = this.myShiftList[index]
                this.query1(true)
              } else {
                this.query1(false)
              }
            })
          } else {
            this.curShift = this.myShiftList[0]
            this.Particle = this.curShift.TimeDimension
            this.query1(true)
          }
        } else {
          this.title1 = this.title
        }
      }

    },
    async getBarList1() {
      let params = {
        "simLevel": this.Order.split('-')[0],
        "position": [
          this.Order
        ],
        "paramList": [
          this.simlevel,
          this.BaseTime
        ]
      }
      let { response } = await getTableList(params)
      this.curConfig = response
      if (this.curConfig?.ChartConfigs != null) {
        if (this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit == undefined) {
          this.title1 = this.title
        } else {
          this.title1 = this.title + '(' + this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit + ')'
        }
        // this.id = this.curConfig.ID;
        // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
        this.curConfig?.ChartConfigs.map(item => {
          item.KpiName = this.curConfig?.ChartConfigs.KpiName
          if (item.KpiValues[0]) {
            item.KpiCode = item.KpiValues[0].KpiCode
            item.TargetValue = item.KpiValues[0].TargetValue || 0
          }
        })
        //图表配置整体赋值
        // this.curConfig = response
        //时间颗粒度列表
        this.myShiftList = this.curConfig?.ChartConfigs.filter(item => {
          return item.TargetVisible === 1
        })
        //默认激活第一个时间颗粒度
        if (this.Particle != '') {
          this.myShiftList.map((el, index) => {
            if (this.Particle == el.TimeDimension) {
              this.curShift = this.myShiftList[index]
              this.$nextTick(() => {
                this.query1(true)
              })
            } else {
              this.$nextTick(() => {
                this.query1(false)
              })
            }
          })
        } else {
          this.curShift = this.myShiftList[0]
          this.Particle = this.curShift.TimeDimension
          this.$nextTick(() => {
            this.query1(true)
          })
        }
      } else {
        this.title1 = this.title
      }
    },
    query1(data) {
      // console.log(data, 'kkkkkk');

      this.showData = data

      this.barTransverseChart = document.getElementById(this.id1);
      var myChart = this.$echarts.init(this.barTransverseChart);
      if (data == false) {
        myChart.clear()
        return
      }
      var option
      // var data = ['输送泵', '给料机', '粉碎机', '引风机', '电磁除铁器', '解包站']
      option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          }
        },
        // legend: {
        //   data: this.lineLegend
        // },
        grid: {
          left: '5%',
          right: '4%',
          bottom: '3%',
          top: 20,
          containLabel: true
        },
        // visualMap: this.lineVisualMap,
        xAxis: this.yAxisOption,
        yAxis: this.xAxisOption,
        series: this.lineSeries
      }

      var that = this
      myChart.off('click');
      myChart.on('click', function (param) {
        that.BaseTime1 = param.name
        // if (that.configFlag == '是') {
        //   that.barName = param.name
        //   that.$refs.keyIndicatorsref.flagChange()
        //   that.$refs.keyIndicatorsref.showDialog = true;
        // }
        that.barName = param.seriesName.split('实际值')[0]

        // that.$refs.keyIndicatorsref.flagChange()
        // that.$refs.keyIndicatorsref.showDialog = true;
        that.Particle1 = that.Particle
        that.isSql1 = that.curConfig.IsSql
        // that.$refs.keyIndicatorsref.flagChange()
        // that.$refs.keyIndicatorsref.showDialog = true;
        that.keyIndicatorslistnewShow = true
        that.showkeyIndicatorslistnew = true
      });
      // if (option && typeof option === 'object') {
      //   // 定时自动滚动
      //   this.scrollTime = setInterval(function () {
      //     console.log(option.dataZoom[0].endValue == data.length);

      //     if (option.dataZoom[0].endValue == data.length) {
      //       option.dataZoom[0].endValue = 4;
      //       option.dataZoom[0].startValue = 0;
      //     } else {
      //       option.dataZoom[0].endValue = option.dataZoom[0].endValue + 1;
      //       option.dataZoom[0].startValue = option.dataZoom[0].startValue + 1;
      //     }
      //     myChart.setOption(option);
      //   }, 2000)
      // }
      myChart.setOption(option, true);
      window.addEventListener("resize", () => {
        myChart.resize()
      }, false);
    },
    query() {
      this.barTransverseChart = document.getElementById(this.id1);
      var myChart = this.$echarts.init(this.barTransverseChart);
      var option
      var data = ['输送泵', '给料机', '粉碎机', '引风机', '电磁除铁器', '解包站']
      option = {
        title: {
          text: '设备管理',
          textStyle: { // 标题样式
            color: '#fff'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          top: 0,
          data: [
            {
              name: '报修数量',
              textStyle: {
                color: 'white'
              }
            }
          ]
        },
        grid: {
          top: '10%',
          left: '3%',
          right: '10%',
          bottom: '7%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01],
          splitLine: { show: false }, //去除网格线
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff" //X轴文字颜色
            },
          },
          axisLine: {
            lineStyle: {
              color: 'white' // 设置轴线的颜色为白色
            }
          }
        },
        yAxis: {
          type: 'category',
          data: data,
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff", //X轴文字颜色
              fontSize: 13
            },
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: { show: false }, //去除网格线
        },
        color: ['#39d6f7'],
        dataZoom: [
          {
            yAxisIndex: 0,// 这里是从X轴的0刻度开始
            show: true, // 是否显示滑动条，不影响使用
            type: "slider", // 这个 dataZoom 组件是 slider 型 dataZoom 组件
            startValue: 0, // 从头开始。
            endValue: 2, // 一次性展示多少个。
          },
        ],
        series: [
          {
            name: '报修数量',
            type: 'bar',
            barWidth: 14,
            data: [32, 45, 67, 12, 66, 34],
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                  offset: 0,
                  color: '#4391F4' // 0% 处的颜色
                }, {
                  offset: 1,
                  color: '#6B74E4' // 100% 处的颜色
                }], false),
                barBorderRadius: [6, 6, 6, 6]
              }
            },
            label: {
              show: true,
              position: 'right',
              textStyle: {
                color: 'white',
                fontSize: 10
              }
            }
          },
        ]
      };
      if (option && typeof option === 'object') {
        // 定时自动滚动
        this.scrollTime = setInterval(function () {
          if (option.dataZoom[0].endValue == data.length) {
            option.dataZoom[0].endValue = 4;
            option.dataZoom[0].startValue = 0;
          } else {
            option.dataZoom[0].endValue = option.dataZoom[0].endValue + 1;
            option.dataZoom[0].startValue = option.dataZoom[0].startValue + 1;
          }
          myChart.setOption(option);
        }, 2000)
      }
      myChart.setOption(option, true);
      window.addEventListener("resize", () => {
        myChart.resize()
      }, false);
    }
  },
  beforeDestroy() {
    clearInterval(this.scrollTime);
  },
}
</script>
<style lang="scss" scoped>
::v-deep .noDataBox {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
}
.titimgbox {
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    width: 50%;
    height: 30px;
    line-height: 30px;
    border-radius: 5px;
    /* background-image: linear-gradient(to right, #056be0 0%, #000b61 100%); */
    display: flex;
    /* border: 1px solid #fff; */
    /* box-shadow: 0px 0px 7px 0px #fff; */
    /* overflow: hidden; */
}
</style>
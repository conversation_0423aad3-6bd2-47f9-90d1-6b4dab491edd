<template>
    <div :class="isFull ? 'device-kanban-full' : 'device-kanban-not'">
        <dv-full-screen-container>
            <div class="sbkb">
                <div class="all">
                    <div class="title">
                        故障分析看板
                        <div class="searchbox">
                            <div class="dashboardinputbox">
                                <div class="dashboardinputboxlabel">工厂:</div>
                                <el-input v-model="factory"></el-input>
                            </div>
                            <div class="dashboardinputbox" style="width: 270px">
                                <el-date-picker v-model="time" value-format="yyyy-MM-dd" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                            </div>
                            <div class="dashboardinputbox"><el-button icon="el-icon-search" size="mini" @click="search()">查询</el-button></div>
                        </div>
                    </div>
                    <div class="tabbox">
                        <div class="tabboxrow">
                            <div class="tabboxbox" :style="{ width: item.width }" v-for="(item, index) in tablelist1" :key="index">
                                <div class="tabboxboxtitle">{{ item.title }}</div>
                                <div class="tabboxboxcenter" :id="item.id" v-loading="item.loading"></div>
                            </div>
                        </div>
                        <div class="tabboxrow">
                            <div class="tabboxbox" :style="{ width: item.width }" v-for="(item, index) in tablelist2" :key="index">
                                <div class="tabboxboxtitle">{{ item.title }}</div>
                                <div class="tabboxboxcenter" :id="item.id" v-loading="item.loading"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </dv-full-screen-container>
    </div>
</template>
<script>
import { jdrwheader, byrwheader, dxrwheader, wxrwheader, jjrwheader } from '@/columns/equipmentdashboard/tableheader';
import { getbarstack } from '@/components/echarts/stackBar.js';
import { DeviceChartStopByLine, DeviceChartStopByDevice, DeviceChartStopByPart } from '@/api/equipmentManagement/FaultAnalysis';
import moment from 'moment';
import '@/views/equipmentdashboard/style.scss';
export default {
    data() {
        return {
            isFull: false,
            myChart1: null,
            myChart2: null,
            myChart3: null,
            factory: '',
            line: '',
            device: '',
            time: [moment().startOf('week').format('YYYY-MM-DD'), moment().endOf('week').format('YYYY-MM-DD')],
            tablelist1: [
                {
                    title: '产线停机/故障时间',
                    width: '100%',
                    id: 'chart1',
                    loading: true
                }
            ],
            tablelist2: [
                {
                    title: '设备停机/故障时间',
                    width: '50%',
                    id: 'chart2',
                    loading: false
                },
                {
                    title: '系统停机/故障时间',
                    width: '50%',
                    id: 'chart3',
                    loading: false
                }
            ],
            SearchParams: {}
        };
    },
    mounted() {
        this.SearchParams.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
        this.SearchParams.startDate = this.time[0];
        this.SearchParams.endDate = this.time[1] + ' 23:59:59';
        setTimeout(() => {
            this.getChart1();
            // this.getChart2();
            // this.getChart3();
            window.addEventListener('resize', this.handleResize);
        }, 500);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.myChart1) {
            this.myChart1.dispose(); // 清理图表实例
            this.myChart2.dispose(); // 清理图表实例
            this.myChart3.dispose(); // 清理图表实例
        }
    },
    methods: {
        search() {
            if (this.time == null) {
                this.time = [];
            }
            this.SearchParams.startDate = this.time[0];
            this.SearchParams.endDate = this.time[1] + ' 23:59:59';
            this.getChart1();
            // this.getChart2();
            // this.getChart3();
        },
        async getChart1() {
            let chartDom = document.getElementById('chart1');
            this.myChart1 = this.$echarts.init(chartDom);
            let res = await DeviceChartStopByLine(this.SearchParams);
            let data = res.response;
            let xdata = data.categorydata;
            let arr = [];
            if (data.series[0]) {
                let series = data.series;
                series.forEach((item, index) => {
                    let obj = {
                        name: '停机',
                        stack: xdata[index],
                        data: item.data[0],
                        color: '#4C98DE'
                    };
                    arr.push(obj);
                    let obj2 = {
                        name: '故障',
                        stack: xdata[index],
                        data: item.data[1],
                        color: '#D4CE47'
                    };
                    arr.push(obj2);
                });
                // let series = data.series[0];
                // series.data = [[series.data[0]], [series.data[1]]];
                // series.data[0].forEach((item, index) => {
                //     let obj = {
                //         name: '停机',
                //         stack: xdata[index],
                //         data: item,
                //         color: '#4C98DE'
                //     };
                //     arr.push(obj);
                // });
                // series.data[1].forEach((item, index) => {
                //     let obj = {
                //         name: '故障',
                //         stack: xdata[index],
                //         data: item,
                //         color: '#D4CE47'
                //     };
                //     arr.push(obj);
                // });
            }
            let data1 = {
                xdata: xdata,
                data: arr
            };
            let option1 = getbarstack(data1, 'min');
            this.myChart1.setOption(option1, true);
            this.myChart1.on('click', params => {
                this.line = params.name;
                this.tablelist2[0].loading = true;
                this.getChart2();
            });
        },
        async getChart2() {
            let chartDom = document.getElementById('chart2');
            this.myChart2 = this.$echarts.init(chartDom);
            let params = {
                line: this.line,
                ...this.SearchParams
            };
            let res = await DeviceChartStopByDevice(params);
            let data = res.response;
            let xdata = data.categorydata;
            let arr = [];
            if (data.series[0]) {
                let series = data.series;
                series.forEach((item, index) => {
                    let obj = {
                        name: '停机',
                        stack: xdata[index],
                        data: item.data[0],
                        color: '#4C98DE'
                    };
                    arr.push(obj);
                    let obj2 = {
                        name: '故障',
                        stack: xdata[index],
                        data: item.data[1],
                        color: '#D4CE47'
                    };
                    arr.push(obj2);
                });
                // let series = data.series[0];
                // series.data = [[series.data[0]], [series.data[1]]];

                // series.data[0].forEach((item, index) => {
                //     let obj = {
                //         name: '停机',
                //         stack: xdata[index],
                //         data: item,
                //         color: '#4C98DE'
                //     };
                //     arr.push(obj);
                // });
                // series.data[1].forEach((item, index) => {
                //     let obj = {
                //         name: '故障',
                //         stack: xdata[index],
                //         data: item,
                //         color: '#D4CE47'
                //     };
                //     arr.push(obj);
                // });
            }
            let data1 = {
                xdata: xdata,
                data: arr
            };
            let option1 = getbarstack(data1, 'min');
            this.myChart2.setOption(option1, true);
            this.tablelist2[0].loading = false;
            this.myChart2.on('click', params => {
                this.device = params.name;
                this.tablelist2[1].loading = true;
                this.getChart3();
            });
        },
        async getChart3() {
            let chartDom = document.getElementById('chart3');
            this.myChart3 = this.$echarts.init(chartDom);
            let params = {
                line: this.line,
                device: this.device,
                ...this.SearchParams
            };
            let res = await DeviceChartStopByPart(params);
            let data = res.response;
            let xdata = data.categorydata;
            let arr = [];
            if (data.series[0]) {
                let series = data.series;
                series.forEach((item, index) => {
                    let obj = {
                        name: '停机',
                        stack: xdata[index],
                        data: item.data[0],
                        color: '#4C98DE'
                    };
                    arr.push(obj);
                    let obj2 = {
                        name: '故障',
                        stack: xdata[index],
                        data: item.data[1],
                        color: '#D4CE47'
                    };
                    arr.push(obj2);
                });
                // let series = data.series[0];
                // series.data = [[series.data[0]], [series.data[1]]];
                // series.data[0].forEach((item, index) => {
                //     let obj = {
                //         name: '停机',
                //         stack: xdata[index],
                //         data: item,
                //         color: '#4C98DE'
                //     };
                //     arr.push(obj);
                // });
                // series.data[1].forEach((item, index) => {
                //     let obj = {
                //         name: '故障',
                //         stack: xdata[index],
                //         data: item,
                //         color: '#D4CE47'
                //     };
                //     arr.push(obj);
                // });
            }
            let data1 = {
                xdata: xdata,
                data: arr
            };
            let option1 = getbarstack(data1, 'min');
            this.myChart3.setOption(option1, true);
            this.tablelist2[1].loading = false;
        },
        handleResize() {
            if (this.myChart1) {
                this.myChart1.resize();
                this.myChart2.resize();
                this.myChart3.resize();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.page_wrapper {
    padding: 0 !important;
}
</style>

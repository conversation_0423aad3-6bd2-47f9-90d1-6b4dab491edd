export const personalColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '班组',
        value: 'Team',
        width: 90,
        sortable: true
    },
    {
        text: '班次',
        value: 'Shift',
        width: 90,
        sortable: true
    },
    {
        text: '产线',
        value: 'Line',
        width: 130,
        sortable: true
    },
    {
        text: '员工号',
        value: 'StaffCode',
        width: 100,
        sortable: true,
        align: 'right',
    },
    {
        text: '姓名',
        value: 'StaffName',
        width: 100,
        sortable: true
    },
    {
        text: '工段',
        value: 'Segment',
        width: 120,
        sortable: true
    },
    {
        text: '工序',
        value: 'Process',
        width: 120,
        sortable: true
    },
    {
        text: '出勤',
        value: 'StateDisplayName',
        width: 100,
        sortable: true
    },
    {
        text: '员工分类',
        value: 'Type',
        width: 100,
        sortable: true
    },
    {
        text: '类型',
        value: 'Type2',
        width: 100,
        sortable: true
    },
    {
        text: '类别',
        value: 'Type3',
        width: 100,
        sortable: true
    },
    {
        text: '来源',
        value: 'Source',
        width: 100,
        sortable: true
    },
    {
        text: '操作',
        width: 160,
        align: 'center',
        value: 'actions',
        sortable: true
    }
];

export const personaslColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '班组',
        value: 'Team',
        width: 90,
        sortable: true
    },
    {
        text: '产线',
        value: 'Line',
        width: 130,
        sortable: true
    },
    {
        text: '员工号',
        value: 'StaffCode',
        width: 100,
        sortable: true,
        align: 'right',
    },
    {
        text: '姓名',
        value: 'StaffName',
        width: 100,
        sortable: true
    },
    {
        text: '工段',
        value: 'Segment',
        width: 120,
        sortable: true
    },
    {
        text: '工序',
        value: 'Process',
        width: 120,
        sortable: true
    },
    {
        text: '出勤',
        value: 'State',
        width: 100,
        sortable: true
    },
    {
        text: '来源',
        value: 'Source',
        width: 100,
        sortable: true
    }
];






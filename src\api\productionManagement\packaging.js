import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TRACE; // 配置服务url
// 列表查询
export function BatchGetPageList(data) {
    return request({
        //url: baseURL + '/trace/Batch/GetPackingBatchPageList',
        url: baseURL + '/trace/Box/GetPageList',
        method: 'post',
        data
    });
}
//扫描录入
export function bzSingleBatchScanData(data) {
    return request({
        url: baseURL + '/trace/wo/bzSingleBatchScanData',
        //url: baseURL + '/trace/Box/InsertBarCodeData',
        method: 'post',
        data
    });
}
// 车载扫码
export function vehicleScanningCode(data) {
    return request({
        url: baseURL + '/trace/Wo/ScanBoxForCz',
        method: 'post',
        data
    })
}

// 车载查询接口
export function vehicleGetDataList(data) {
    return request({
        url: baseURL + '/trace/WoSn/GetCzBoxPageList',
        method: 'post',
        data
    })
}





// 货架对应设备机台
<template>
    <div class="product-module">
        <v-card class="mt-5">
            <Tables :table-height="tableHeight" :headers="machineEquipmentColumns" :desserts="desserts"
                :loading="loading" :page-options="pageOptions" :btn-list="btnList" table-name="DFM_XBHJW"
                @selectePages="selectePages" @itemSelected="selectedItems" @toggleSelectAll="selectedItems"
                @tableClick="tableClick"></Tables>
        </v-card>
        <!-- 添加/编辑 -->
        <v-dialog v-model="isUpdateDetail" scrollable width="55%">
            <updateMachinePopup v-if="isUpdateDetail" :opera-obj="operaObj" :current-select-id="currentSelectId"
                @handlePopup="handlePopup" />
        </v-dialog>
    </div>
</template>

<script>
import { machineEquipmentColumns } from '@/columns/factoryPlant/lineSideShelfPosition.js';
import { GetRackingDevicePageList, DeleteRackingDevice } from '@/api/factoryPlant/sideLine.js';
export default {
    components: {
        updateMachinePopup: () => import('../components/updateMachinePopup.vue'),
        Tables: () => import('@/components/Tables/index.vue')
    },
    props: {
        currentSelectId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            deleteId: '',
            deleteDialog: false,
            isUpdateDetail: false,
            machineEquipmentColumns,
            operaObj: {},
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            selectedList: [],
            desserts: [],
            tableHeight: '320'
        };
    },
    computed: {
        //查询条件
        // searchinput() {
        //     return [
        //         // '工厂'
        //         {
        //             key: 'Factory',
        //             value: '40012008',
        //             icon: '',
        //             label: this.$t('DFM_WLBOMGL.Factory')
        //         },
        //         // 料号'
        //         {
        //             key: 'materialCode',
        //             value: '',
        //             icon: '',
        //             label: this.$t('$vuetify.dataTable.DFM_WLBOMGL.materialCode')
        //         }
        //     ];
        // },
        btnList() {
            return [
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary' },
                { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'red' }
            ];
        }
    },
    // watch: {
    //     currentSelectId: {
    //         handler(v) {
    //             v ? this.getDataList() : (this.desserts = []);
    //         },
    //         deep: true
    //     }
    // },
    created() {
        this.getDataList();
    },
    methods: {
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        // 操作栏按钮
        tableClick(item, type) {
            switch (type) {
                // 编辑
                case 'edit':
                    this.operaClick(item);
                    break;
                // 删除
                case 'delete':
                    // this.deleteDialog = true;
                    this.deleteId = item.ID;
                    this.sureDelete();
                    break;
                default:
                    break;
            }
        },
        // 获取全部表格数据
        async getDataList() {
            if (!this.currentSelectId) return;
            this.loading = true;
            let params = {
                key: this.currentSelectId,
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await GetRackingDevicePageList(params);
            const { success, response } = res || {};
            const { data, dataCount, page } = response || {};
            if (success) {
                this.desserts = data;
                this.pageOptions.total = dataCount;
                this.pageOptions.page = page;
            } else {
                this.desserts = [];
            }
            this.loading = false;
        },
        // 新增/编辑BOM
        operaClick(v) {
            this.isUpdateDetail = true;
            setTimeout(() => {
                this.operaObj = v;
            }, 100);
        },
        // 批量删除
        deleteItems() {
            if (this.selectedList.length > 0) {
                // this.deleteDialog = true;
                this.deleteId = '';
                this.sureDelete();
            } else {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'error' });
            }
        },
        handlePopup(type, data) {
            switch (type) {
                case 'refresh':
                    this.isUpdateDetail = false;
                    this.getDataList();
                    break;
                case 'close':
                    this.isUpdateDetail = false;
                    break;
                default:
                    break;
            }
        },
        // 确认删除
        sureDelete() {
            this.$confirms({
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            }).then(async () => {
                const params = [];
                if (this.deleteId) {
                    params.push(this.deleteId);
                } else {
                    this.selectedList.forEach(e => {
                        params.push(e.ID);
                    });
                }
                const res = await DeleteRackingDevice(params);
                this.selectedList = [];
                this.deleteId = '';
                const { success, msg } = res;
                if (success) {
                    this.pageOptions.pageCount = 1;
                    this.getDataList();
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    this.deleteDialog = false;
                }
            });
        }
    }
};
</script>

<!-- eslint-disable vue/valid-v-slot -->
<template>
    <div class="table-view common-table-components">
        <!-- 表单 -->
        <v-card>
            <v-card-title v-if="isSearch" class="py-2 my-0">
                <v-text-field v-model="search" append-icon="mdi-magnify" :label="$t('GLOBAL._SX')" class="py-0 my-0" single-line hide-details></v-text-field>
            </v-card-title>
            <v-data-table
                ref="table"
                v-model="selected"
                fixed-header
                :height="tableHeight"
                :loading="loadingProgress"
                :search="search"
                :single-select="singleSelect"
                :headers="initHead"
                :items="handlDesserts"
                hide-default-footer
                :show-select="showSelect"
                :item-key="itemKey"
                :items-per-page="footer ? options.pageSize : 9999"
                :item-class="setHighLightRow"
                :custom-sort="customSort"
                :single-expand="singleExpand"
                :expand-icon="expandIcon"
                :show-expand="showExpandOption.showExpand"
                @update:expanded="clickExpanded"
                @toggle-select-all="toggleSelectAll"
                @item-selected="itemSelected"
                @page-count="pageCount = $event"
                @click:row="selectCurrentRow"
            >
                <!-- 自定义需要展示的数据  -->
                <template v-slot:expanded-item="{ headers }">
                    <td class="expanded-item-td" v-if="showSelect"></td>
                    <td class="expanded-item-td" v-if="headers[0]?.Index"></td>
                    <!-- <td class="expanded-item-td"></td> -->
                    <td class="expanded-item-td" :colspan="headers.length - (showSelect ? 1 : 0) - (headers[0]?.Index ? 1 : 0)">
                        <!-- 这里是为了让展开的数据行跟原数据行长度一致  -->
                        <v-data-table v-if="showExpandOption?.type == 'table'" :headers="showExpandOption?.headers" :items="showExpandDesserts" hide-default-footer></v-data-table>
                        <template v-else>
                            {{ showExpand }}
                        </template>
                    </td>
                </template>
                <!-- 复制  -->
                <template v-slot:item.isCopyContent="{ item, header }">
                    {{ item[header.isCopyContent] }}
                    <v-btn v-show="item[header.isCopyContent]" x-small color="primary" class="ml-2 pa-0" @click.stop="copyValue(item[header.isCopyContent])">复制</v-btn>
                </template>
                <template #item.isNoTranslate="{ item, header }">
                    {{ item[header.isNoTranslate] || 0 }}
                </template>
                <template #item.isEditCell="props">
                    <div v-for="(i, k) in editCellList" :key="k">
                        <v-edit-dialog
                            v-if="getFlagNum(k, editCellList.length) == k"
                            class="show-edit-dialog"
                            :return-value.sync="props.item[i]"
                            large
                            @save="saveEditCell(props.item, props.header.text)"
                            save-text="保存"
                            cancel-text="取消"
                        >
                            <span v-if="dictionarySort.indexOf(i) > -1" style="color: blue; border-bottom: 1px solid">
                                {{ props.item | getValue(i, dictionaryList) }}
                            </span>
                            <span style="color: blue; border-bottom: 1px solid" v-else>{{ props.item[i] }}</span>
                            <template #input>
                                <template v-if="dictionarySort.indexOf(i) == -1">
                                    <v-text-field
                                        v-if="props.header.digitalConver"
                                        style="width: 100%; font-size: 14px"
                                        v-model.number="props.item[i]"
                                        label="Edit"
                                        type="number"
                                        min-value="0"
                                        single-line
                                        counter
                                        autofocus
                                    ></v-text-field>
                                    <v-text-field v-else style="width: 100%; font-size: 14px" v-model="props.item[i]" label="Edit" single-line counter autofocus></v-text-field>
                                </template>
                                <v-select
                                    v-else
                                    style="width: 100%; font-size: 14px !important"
                                    v-model="props.item[i]"
                                    label="Edit"
                                    :items="getSelectData(i, 'a')"
                                    :item-text="getSelectData(i, 'n')"
                                    :item-value="getSelectData(i, 'v')"
                                    single-line
                                    autofocus
                                ></v-select>
                            </template>
                        </v-edit-dialog>
                    </div>
                </template>
                <template #item.dictionarySendSapFlag="{ item, header }">
                    <span v-if="header.isColor" :style="item.SendSapFlag == 1 ? 'color: green' : 'color: red'">
                        {{ item | getValue('SendSapFlag', dictionaryList) }}
                    </span>
                    <span v-else>{{ item | getValue('SendSapFlag', dictionaryList) }}</span>
                </template>
                <template #item.DealTime="{ item }">
                    <slot name="DealTime" :item="item"></slot>
                </template>
                <template #item.WoStatus="{ item }">
                    <slot name="WoStatus" :item="item"></slot>
                </template>
                <template #item.NoticeType="{ item }">
                    <slot name="NoticeType" :item="item"></slot>
                </template>
                <template #item.Content="{ item }">
                    <slot name="Content" :item="item">{{ item.Content }}</slot>
                </template>

                <template #item.Shift="{ item }">
                    <slot name="Shift" :item="item">{{ item.Shift }}</slot>
                </template>
                <template #item.CurrentStock="{ item }">
                    <slot name="CurrentStock" :item="item">{{ item.CurrentStock }}</slot>
                </template>
                <template #item.McStatus="{ item }">
                    <slot name="McStatus" :item="item">{{ item.McStatus == '1' ? '已完成' : '未完成' }}</slot>
                </template>
                <template #item.FjStatus="{ item }">
                    <slot name="FjStatus" :item="item">{{ item.FjStatus == '1' ? '已完成' : '未完成' }}</slot>
                </template>
                <template #item.MaintainStatus="{ item }">
                    <slot name="MaintainStatus" :item="item">{{ item.MaintainStatus == '1' ? '已完成' : '未完成' }}</slot>
                </template>
                <template #item.Parameter="{ item }">
                    <slot name="Parameter" :item="item">{{ item.Parameter }}</slot>
                </template>
                <template #item.Serialno="{ item }">
                    <slot name="Serialno" :item="item">{{ item.Serialno }}</slot>
                </template>
                <template #item.EnableMonitoring="{ item }">
                    <slot name="EnableMonitoring" :item="item">{{ item.EnableMonitoring }}</slot>
                </template>
                <template #item.DealStatus="{ item }">
                    <slot name="DealStatus" :item="item">{{ item.DealStatus }}</slot>
                </template>
                <template #item.InspectionSheet="{ item }">
                    <slot name="InspectionSheet" :item="item">{{ item.InspectionSheet }}</slot>
                </template>
                <template #item.BatchNo="{ item }">
                    <slot name="BatchNo" :item="item">{{ item.BatchNo }}</slot>
                </template>
                <template #item.UwbWorkTimes="{ item }">
                    <slot name="UwbWorkTimes" :item="item">{{ item.UwbWorkTimes }}</slot>
                </template>
                <template #item.WorkTimes="{ item }">
                    <slot name="WorkTimes" :item="item">{{ item.WorkTimes }}</slot>
                </template>
                <template #item.IsDuty="{ item }">
                    <slot name="IsDuty" :item="item">
                        <v-icon v-if="item.IsDuty" color="primary">mdi-check</v-icon>
                    </slot>
                </template>
                <template #item.TestItemList="{ item }">
                    <slot name="TestItemList" :item="item">{{ item.TestItemList }}</slot>
                </template>
                <template #item.WoType="{ item }">
                    <slot name="WoType" :item="item">{{ item.WoType }}</slot>
                </template>
                <template #item.Deleted="{ item }">
                    <slot name="Deleted" :item="item">{{ item.Deleted }}</slot>
                </template>
                <template #item.ChangeShifts="{ item }">
                    <slot name="ChangeShifts" :item="item">{{ item.ChangeShifts }}</slot>
                </template>
                <template #item.TestGroupList="{ item }">
                    <slot name="TestGroupList" :item="item">{{ item.TestGroupList }}</slot>
                </template>
                <template #item.Isauto="{ item }">
                    <slot name="Isauto" :item="item">{{ item.Isauto }}</slot>
                </template>
                <template #item.Isspc="{ item }">
                    <slot name="Isspc" :item="item">{{ item.Isspc }}</slot>
                </template>
                <template #item.WoCode="{ item }">
                    <slot name="WoCode" :item="item">{{ item.WoCode }}</slot>
                </template>
                <template #item.LockStatus="{ item }">
                    <slot name="LockStatus" :item="item">{{ item.LockStatus }}</slot>
                </template>
                <template #item.Year="{ item }">
                    <slot name="Year" :item="item">{{ item.Year }}</slot>
                </template>
                <template #item.Month="{ item }">
                    <slot name="Month" :item="item">{{ item.Month }}</slot>
                </template>
                <template #item.PlanDate="{ item }">
                    <slot name="PlanDate" :item="item">{{ formatDate(item.PlanDate) }}</slot>
                </template>
                <!-- <template #item.dictionary="{ item }">
                    {{ item | getValue(dictionarySort, dictionaryList) }}
                </template> -->
                <template #item.YieldRate="{ item }">
                    {{ item.YieldRate | getPercent() }}
                </template>
                <template #item.Automatic="{ item }">
                    {{ item.Automatic == 0 ? '手动计算' : '自动计算' }}
                </template>
                <template #item.EnabledMark="{ item }">
                    {{ item.EnabledMark ? '启用' : '禁用' }}
                </template>
                <template #item.Detail="{ item }">
                    <slot name="Detail" :item="item">
                        <template>
                            <i class="el-icon-document" style="font-size: 18px" @click.stop="tableClick(item, 'detail')"></i>
                        </template>
                    </slot>
                    <!-- <i class="el-icon-document" @click="tableClick(item, 'detail')"></i> -->
                </template>
                <template #item.Enable="{ item }">
                    <slot name="Enable" :item="item">
                        <v-switch
                            v-model="item.Enable"
                            style="height: 30px; transform: scale(0.8); transform-origin: left"
                            class="ma-0 ba-0"
                            label=""
                            @click.stop="tableClick(item, 'Enabled')"
                        ></v-switch>
                    </slot>
                </template>
                <template #item.Enabled="{ item }">
                    <span v-if="item.tdContentType == 'text'">
                        {{ item }}
                    </span>
                    <v-switch
                        v-else
                        v-model="item.Enabled"
                        style="height: 30px; transform: scale(0.8); transform-origin: left"
                        class="ma-0 ba-0"
                        label=""
                        @click.stop="tableClick(item, 'Enabled')"
                    ></v-switch>
                </template>
                <template #item.attribute="{ item, value = $t('DFM_TJPZ._SXKZ') }">
                    <v-btn text color="primary" small class="mx-0 my-0" @click.stop="tableClick(item, 'attribute')">{{ value }}</v-btn>
                </template>
                <!-- 是否需要根据状态来显示按钮 -->

                <template #item.actions="{ item }">
                    <slot name="actions" :item="item">
                        <template v-for="(list, index) in btnList">
                            <v-btn
                                v-if="getBtnAuto(list.authCode, list, item)"
                                :disabled="getBtnShow(list, item)"
                                :key="index"
                                text
                                small
                                class="mx-1 px-0"
                                :class="item[list.itemKey] || item[list.itemKey] > -1 ? 'itemKeyBtn pa-0 ma-0' : ''"
                                :color="item[list.itemKey] || item[list.itemKey] > -1 ? '' : list.type"
                                @click.stop="tableClick(item, list.code)"
                            >
                                {{ item[list.itemKey] || item[list.itemKey] > -1 ? item[list.itemKey] : list.text }}
                            </v-btn>
                        </template>
                    </slot>
                </template>
                <template #item.Status="{ item }">
                    <slot name="Status" :item="item">
                        <span>{{ item.Status }}</span>
                    </slot>
                </template>
                <template #item.Islastsegment="{ item }">
                    <slot name="Islastsegment" :item="item">
                        {{ item.Islastsegment }}
                    </slot>
                </template>
                <template #item.Type="{ item }">
                    <slot name="Type" :item="item">
                        {{ item.Type }}
                    </slot>
                </template>
                <template #item.BatchQuantity="{ item }">
                    <slot name="BatchQuantity" :item="item">
                        {{ item.BatchQuantity }}
                    </slot>
                </template>
                <template #item.Tagert="{ item }">
                    <slot name="Tagert" :item="item">
                        {{ item.Tagert }}
                    </slot>
                </template>
                <!-- 用户头像 -->
                <template #item.UserAvatar="{ item }">
                    <slot name="UserAvatar" :item="item"></slot>
                </template>
                <!-- 性能测试状态 -->
                <template #item.CurrentProcStatus="{ item }">
                    <slot name="CurrentProcStatus" :item="item"></slot>
                </template>
                <!-- 会议名称 -->
                <template #item.Topic="{ item }">
                    <slot name="Topic" :item="item"></slot>
                </template>
                <!-- isSwitch -->
                <template #item.IsPiecework="{ item }">
                    <slot name="IsPiecework" :item="item"></slot>
                </template>
                <template #item.IsFulltime="{ item }">
                    <slot name="IsFulltime" :item="item"></slot>
                </template>
                <template #item.State="{ item }">
                    <slot name="State" :item="item"></slot>
                </template>
                <template #item.StateDisplayName="{ item }">
                    <slot name="StateDisplayName" :item="item">{{ item.StateDisplayName }}</slot>
                </template>
                <template #item.semicolonFormatquantityInStorage="{ item }">
                    <span style="color: red">{{ item.semicolonFormatquantityInStorage }}</span>
                </template>
                <template #item.RepairStatus="{ item }">
                    <slot name="RepairStatus" :item="item"></slot>
                </template>
                <template #item.Buynum="{ item }">
                    <slot name="Buynum" :item="item">{{ item.Buynum }}</slot>
                </template>
                <template #item.Isenable="{ item }">
                    <slot name="Isenable" :item="item">{{ item.Isenable }}</slot>
                </template>
                <template #item.IsClose="{ item }">
                    <slot name="IsClose" :item="item">{{ item.IsClose }}</slot>
                </template>

                <template #item.Usednum="{ item }">
                    <slot name="Usednum" :item="item">{{ item.Usednum }}</slot>
                </template>
                <template #item.DeviceType="{ item }">
                    <slot name="DeviceType" :item="item">{{ item.DeviceType }}</slot>
                </template>
                <template #item.Difftime="{ item }">
                    <slot name="Difftime" :item="item">{{ item.Difftime.toFixed(2) }}</slot>
                </template>
                <template #item.AlarmPic="{ item }">
                    <slot name="AlarmPic" :item="item">{{ item.AlarmPic }}</slot>
                </template>
                <template #item.AlarmVideo="{ item }">
                    <slot name="AlarmVideo" :item="item">{{ item.AlarmVideo }}</slot>
                </template>
            </v-data-table>
            <!-- 分页 -->
            <div v-if="footer" class="page">
                <v-col class="d-flex align-center pa-0 ml-4" cols="auto">
                    <span>{{ $t('GLOBAL._G') }}</span>
                    <span>{{ options.total }}</span>
                    <span>{{ $t('GLOBAL._TSJ') }}</span>
                    <!-- <v-select class="ml-2" :items="options.pageSizeitems" label="10" :value="options.pageSize" dense solo @change="selecteLimitPage"></v-select> -->
                    <select class="select ml-1" v-model="pageSize" @change="selecteLimitPage">
                        <option v-for="(item, index) in options.pageSizeitems" :key="index" :value="item">{{ item }}</option>
                    </select>
                </v-col>
                <slot name="total-work-cost"></slot>
                <v-spacer></v-spacer>
                <v-pagination v-model="options.page" :total-visible="totalVisible" :length="Math.ceil(pageOptions.total / pageOptions.pageSize)" @input="pageSizeClick"></v-pagination>
                <v-spacer></v-spacer>
                <v-col class="d-flex align-center mr-2" cols="auto">
                    <!-- 分页跳转 -->
                    <span>{{ $t('GLOBAL._TZD') }}</span>
                    <input id="pageInput" v-model="pageInput" @change="goPage" />
                    <span>{{ $t('GLOBAL._Y') }}</span>
                </v-col>
            </div>
        </v-card>
    </div>
</template>

<script>
let flagNum = 0;
import Util from '@/util';
export default {
    name: 'Pagination',
    filters: {
        // 处理数据字典
        getValue(o, k, d) {
            let v = o[k];
            try {
                const obj = d.find(i => i.key == k);
                console.log(obj);
                if (obj) {
                    const { arr, key, val, text } = obj;
                    v = o[key];
                    const s = arr.find(i => o[key] == i[val]);
                    if (s) v = s[text];
                }
            } catch (error) {
                console.log(error);
            }
            return v;
        },
        // 加百分号
        getPercent(v) {
            let p = v;
            if ('%'.indexOf(p) == -1) {
                p = (p || 0) + '%';
            }
            return p;
        }
    },
    props: {
        // 自定义的搜索栏
        customizeSearch: { type: String, default: '' },
        // 可以展开对象为列表时
        showExpandDesserts: { type: Array, default: () => [] },
        // 可以展开详情的对象
        showExpandOption: {
            type: Object,
            default: () => {
                return {
                    showExpand: false, // 是否展开
                    type: '', // table为列表，其他为展示数据
                    headers: [] // 展开的表头
                };
            }
        },
        // 展示内容，如果为showExpandDesserts列表中的某个内容，则传相关KEY
        showExpand: { type: String, default: '' },
        // 为true时，只能有一行数据可以展开，其他数据展开项关闭
        singleExpand: { type: Boolean, default: true },
        // 展开按钮的图标设置，默认是$expand,Vuetify的图标，必须加$修饰符，自行下载的图标库可以直接写名字，不加$
        expandIcon: { type: String, default: '$expand' },
        // 是否多选
        singleSelect: { type: Boolean, default: false },
        // 表格Name
        tableName: {
            type: String,
            default: ''
        },
        // 快捷搜索
        isSearch: {
            type: Boolean,
            default: false
        },
        totalVisible: {
            type: Number,
            default: 10
        },
        // 表格高度
        tableHeight: {
            type: String,
            default: 'calc(100vh - 176px)'
        },
        // 表格KSY
        itemKey: {
            type: String,
            default: 'ID'
        },
        currentSelectId: {
            type: String,
            default: ''
        },
        clickFun: {
            type: Function,
            default: () => {}
        },
        loading: {
            type: Boolean,
            default: false
        },
        //数据
        desserts: {
            type: Array,
            default: () => []
        },
        //表头
        headers: {
            type: Array,
            default: () => []
        },
        footer: {
            type: Boolean,
            default: true
        },
        //按钮
        btnList: {
            type: Array,
            default: function () {
                return [
                    {
                        text: this.$t('GLOBAL._BJ'),
                        code: 'edit',
                        type: 'primary',
                        icon: ''
                    },
                    {
                        text: this.$t('GLOBAL._SC'),
                        code: 'delete',
                        type: 'red',
                        icon: ''
                    }
                ];
            }
        },
        // 多选
        showSelect: {
            type: Boolean,
            default: true
        },
        //分页信息
        pageOptions: {
            type: Object,
            default: () => {}
        },
        //数据字典
        dictionaryList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            pageInput: '', // 跳转页码
            selected: [],
            search: '', // 过滤筛选
            dictionarySort: [],
            editCellList: [],
            pageSize: this.pageOptions.pageSize,
            loadingProgress: false,
            timer: null
        };
    },
    computed: {
        initHead() {
            let headList = [];
            headList = this.headers.map(item => {
                const { dictionary, value, Namevalue, isEditCell, semicolonFormat, isDateNum, isCopyContent, isNoTranslate } = item;
                if (semicolonFormat == true) {
                    item.value = 'semicolonFormat' + value;
                    item.semicolonFormat = value;
                    item.align = 'right';
                }
                if (dictionary == true) {
                    item.value = 'dictionary' + value;
                    item.dictionary = value;
                }
                // 是否需要编辑
                if (isEditCell == true) {
                    item.value = 'isEditCell';
                    item.isEditCell = value;
                }
                if (isNoTranslate == true) {
                    item.value = 'isNoTranslate';
                    item.isNoTranslate = value;
                }
                // 是否需要编辑
                if (isCopyContent == true) {
                    item.value = 'isCopyContent';
                    item.isCopyContent = value;
                }
                // 排序去掉
                if (value == 'actions' || value == 'noActions' || value == 'Index' || typeof isEditCell == 'boolean') item.sortable = false;
                if (value == 'actions') item.align = 'center';
                // 是否国际化
                if (this.tableName) {
                    if (value == 'data-table-expand') {
                        item.text = ''; // 展开
                    } else if (value == 'Index') {
                        item.text = this.$t('GLOBAL._INDEX'); // 序号
                    } else if (value == 'actions') {
                        const ga = this.$t(`$vuetify.dataTable.${this.tableName}.actions`);
                        console.log('this.tableName'.indexOf(ga) > -1);
                        item.text = ('this.tableName'.indexOf(ga) > -1 && ga) || this.$t('GLOBAL._ACTIONS'); // 操作
                    } else if (value == 'CreateDate') {
                        item.text = this.$t('GLOBAL.CreateDate'); // 创建时间
                    } else if (value == 'CreateUserId') {
                        item.text = this.$t('GLOBAL.CreateUserId'); // 创建人
                    } else if (value == 'ModifyDate') {
                        item.text = this.$t('GLOBAL.ModifyDate'); // 最近修改时间
                    } else if (value == 'ModifyUserId') {
                        item.text = this.$t('GLOBAL.ModifyUserId'); // 最近修改人
                    } else if (semicolonFormat) {
                        if (!isDateNum) {
                            item.text = this.$t(`$vuetify.dataTable.${this.tableName}.${value.indexOf('semicolonFormat') > -1 ? semicolonFormat : value}`); //千分号
                        }
                    } else if (dictionary && !isEditCell) {
                        item.text = this.$t(`$vuetify.dataTable.${this.tableName}.${value.indexOf('dictionary') > -1 ? dictionary : value}`); //数据字典
                    } else if (isEditCell) {
                        item.text = this.$t(`$vuetify.dataTable.${this.tableName}.${value == 'isEditCell' ? isEditCell : value}`); //编辑
                    } else if (isCopyContent) {
                        item.text = this.$t(`$vuetify.dataTable.${this.tableName}.${value == 'isCopyContent' ? isCopyContent : value}`); //复制
                    } else {
                        if (!isNoTranslate) {
                            item.text = this.$t(`$vuetify.dataTable.${this.tableName}.${Namevalue ? Namevalue : value}`); //  表头对象名称
                        }
                    }
                    if (value == 'noActions') item.text = '';
                }
                if (typeof dictionary == 'boolean' && typeof isEditCell == 'boolean') this.dictionarySort.push(value);
                if (value == 'isEditCell' && dictionary) this.dictionarySort.push(isEditCell);
                return item;
            });
            return headList;
        },
        options() {
            return {
                total: this.pageOptions.total,
                page: this.pageOptions.page, // 当前页码
                pageSize: this.pageOptions.pageSize, // 一页数据
                pageCount: this.pageOptions.pageCount, // 页码分页数
                pageSizeitems: this.pageOptions.pageSizeitems || [20, 30, 50, 100, 500] // 分页
            };
        },
        // 数据处理
        handlDesserts() {
            // 根据标题判断是否需要序号
            const boo = this.headers.some(i => {
                return i.value === 'Index';
            });
            flagNum = 0;
            this.headers.forEach(e => {
                if (e.isEditCell && this.desserts.length) this.editCellList.push(e.isEditCell);
            });
            const newArr = JSON.parse(JSON.stringify(this.dictionaryList));
            this.desserts.forEach((e, index) => {
                e.Index = index + 1;
                for (const k in e) {
                    if (Object.hasOwnProperty.call(e, k)) {
                        if (e[k] == '1900-01-01 00:00:00') {
                            e[k] = '';
                        }
                        if (typeof e[k] == 'string') {
                            if (e[k].indexOf('00:00:00') != -1) {
                                e[k] = e[k].replace('00:00:00', '');
                            }
                        }
                    }
                    const v = e[k];
                    // 数字千分号转换
                    const semicolon = this.headers.find(i => i.semicolonFormat == k);
                    if (semicolon) e[semicolon.value] = Util.formatNum(v);
                    // 数据字典
                    const d = this.headers.find(i => i.dictionary == k);
                    if (d) {
                        const dictionary = this.dictionaryList.find(i => i.key == k);
                        const { arr, val, text } = dictionary || {};
                        const s = arr?.find(i => {
                            // if(index == this.desserts.length - 1 && k == 'Units') console.log(v, i.EquipmentCode, val, k);
                            return v == i[val];
                        });
                        e['dictionary' + k] = s ? s[text] : v;
                    }
                }
                // if (boo) {
                //     //     e.Index = (this.options.page - 1) * this.options.pageSize + index + 1;
                // }
            });
            console.log(this.desserts);
            // console.log(this.footer,123)
            return this.desserts;
        }
    },
    watch: {
        desserts: {
            handler(curVal) {
                this.selected = [];
                this.$nextTick(() => {
                    this.$emit('toggleSelectAll', this.selected);
                });
            },
            deep: true,
            immediate: true
        },
        loading: {
            handler(curVal) {
                this.loadingProgress = curVal;
                if (curVal) {
                    if (this.timer) clearTimeout(this.timer);
                    this.timer = setTimeout(() => {
                        this.loadingProgress = false;
                    }, 20 * 1000);
                }
            },
            deep: true,
            immediate: true
        },
        customizeSearch: {
            handler(curVal) {
                this.search = curVal;
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        // 操作栏按钮权限
        getBtnAuto(v, list, item) {
            if (list.noShowKey) {
                if (item[list.noShowKey] == null || item[list.noShowKey] == '') {
                    return false;
                } else {
                    return true;
                }
            } else {
                const vma = sessionStorage.getItem('vma');
                const o = JSON.parse(vma || JSON.stringify({}));
                const { btnList } = o?.auth;
                const n = btnList.find(i => i.CnCode == v);
                return !v || n ? true : false;
            }
        },
        // 操作栏按钮显示、隐藏 showKey：根据显示的字段，showList：可以显示的列表
        getBtnShow(list, item) {
            if (list.emptyKey) {
                if (item[list.emptyKey] == null) {
                    return false;
                } else {
                    return true;
                }
            } else {
                if (list.showKey) {
                    const boo = list.showList && list.showKey && !list.showList.includes(item[list.showKey]);
                    return boo;
                } else {
                    return false;
                }
            }
        },
        // 复制
        copyValue(v) {
            let copyInput = document.createElement('input'); //创建input元素
            document.body.appendChild(copyInput); //向页面底部追加输入框
            copyInput.setAttribute('value', typeof v == 'object' ? JSON.stringify(v) : v); //添加属性，将url赋值给input元素的value属性
            copyInput.select(); //选择input元素
            document.execCommand('Copy'); //执行复制命令
            this.$store.commit('SHOW_SNACKBAR', { text: '复制成功', color: 'success' });
            //复制之后再删除元素，否则无法成功赋值
            copyInput.remove(); //删除动态创建的节点
        },
        // 日期去除时分秒
        formatDate(date) {
            let year = new Date(date).getFullYear();
            let month = (new Date(date).getMonth() + 1).toString().padStart(2, '0');
            let day = new Date(date).getDate().toString().padStart(2, '0');
            return year + '-' + month + '-' + day;
        },
        // 获取下拉框对应的name，value还有数组
        getSelectData(k, t) {
            const o = this.dictionaryList.find(i => {
                return i.key == k;
            });
            const { arr, val, text } = o || {};
            let s;
            switch (t) {
                case 'n':
                    s = text;
                    break;

                case 'v':
                    s = val;
                    break;

                default:
                    s = arr;
                    break;
            }
            return s;
        },
        //标记以达到编辑行的显示隐藏
        getFlagNum(k, l) {
            if (flagNum == l) flagNum = 0;
            const n = flagNum;
            if (k == l - 1) {
                flagNum++;
            }
            return n;
        },
        // 保存单元格编辑
        saveEditCell(item, props) {
            this.$emit('tableClick', item, 'editCell', props);
        },
        // 设置行高亮
        setHighLightRow(val) {
            if (val.Status == '已点检' || val.Status == '已保养' || val.Status == '已完成') {
                return 'OverRow';
            } else {
                if (this.currentSelectId == val[this.itemKey]) {
                    return 'active-row';
                } else {
                    return '';
                }
            }
            // if (this.tableName == 'TPM_SBGL_SBBJGL' && val.CurrentStock < val.MinStock) {
            //     return 'stock-warn';
            // }
        },
        selectCurrentRow(data) {
            this.clickFun(data);
        },
        // 点击展开时候的操作
        clickExpanded(i) {
            if (i.length > 0) this.$emit('tableClick', i[0], 'showExpand');
        },
        // 一键全选
        toggleSelectAll() {
            this.$nextTick(() => {
                this.$emit('toggleSelectAll', this.selected);
            });
        },
        // 选择账号
        itemSelected() {
            this.$nextTick(() => {
                this.$emit('itemSelected', this.selected);
            });
        },
        //表单事件操作
        // item 列表数据
        // type 按钮类型
        tableClick(item, type) {
            this.$emit('tableClick', item, type);
        },
        // 跳转直指定页码
        goPage() {
            this.options.page = +this.pageInput;
            this.pageSizeClick(this.options.page);
        },
        // 选择一页多少条
        selecteLimitPage(e) {
            const val = e.target.value;
            console.log('------', val);
            this.options.pageSize = +val;
            let pagesOptions = {
                pageSize: this.options.pageSize,
                pageCount: 1
            };
            this.$emit('selectePages', pagesOptions);
        },
        // 分页选择数据
        pageSizeClick(v) {
            console.log(v + '点击分页');
            this.options.pageCount = v;
            let pagesOptions = {
                pageSize: this.options.pageSize,
                pageCount: this.options.pageCount
            };
            this.$emit('selectePages', pagesOptions);
        },
        // 排序处理
        customSort(items, index, isDesc) {
            items.sort((a, b) => {
                if (isDesc != 'false') {
                    return a[index] < b[index] ? -1 : 1;
                } else {
                    return b[index] < a[index] ? -1 : 1;
                }
            });
            items.forEach((e, index) => {
                e.Index = index + 1;
            });
            return items;
        }
    }
};
</script>

<style lang="scss">
.show-edit-dialog {
    .v-list-item {
        min-height: 36px !important;
    }
}

.common-table-components {
    .expanded-item-td,
    .v-data-table__expanded .v-data-table__expanded__content:hover {
        background: #ffffff !important;
        overflow: hidden;
    }

    tbody {
        border-bottom: 1px solid gainsboro;
    }

    .expanded-item-td {
        table {
            width: 80vw;
        }
    }
}
</style>
<style lang="scss" scoped>
::v-deep .v-data-table__wrapper {
    tr {
        td:last-child,
        td:first-child,
        th:last-child,
        th:first-child {
            table-layout: fixed;
            position: sticky;
            right: 0;
            background: #ffffff;
            overflow: hidden;
            // box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
        }
    }

    .v-data-table__selected {
        td:last-child,
        td:first-child,
        th:last-child,
        th:first-child {
            background: #f5f5f5;
        }
    }

    tr:hover > td:first-child,
    tr:hover > td:last-child,
    tr td:last-child:hover {
        background: var(--v-primary-lighten5);
    }
}

::v-deep .active-row {
    background: var(--v-primary-lighten5) !important;

    td:last-child,
    td:first-child {
        table-layout: fixed;
        position: sticky;
        right: 0;
        background: var(--v-primary-lighten5) !important;
        overflow: hidden;
    }
}

.table-view {
    min-width: 100%;
}

.itemKeyBtn {
    background: #3dcd58;
    height: auto !important;
    width: 40px;
    padding: 2px 0 !important;
    color: white !important;
}

.page {
    display: flex;
    align-items: center;

    .select {
        // border-color: var(--v-primary-lighten1);
        min-width: 26px;
        border: 1px solid #000;
        min-height: 26px;
        outline: none;
        padding: 0 4px;
        text-align: center;
        border-radius: 5px;

        option {
            cursor: pointer;
        }

        option:hover {
            background-color: var(--v-primary-lighten3) !important;
        }
    }
}

.v-text-field {
    width: 60px;
    margin-top: 20px;
    margin-right: 10px;
}

span {
    margin: 0 4px;
}

#pageInput {
    max-width: 26px;
    border: 1px solid #000;
    min-height: 26px;
    outline: none;
    padding: 0 4px;
    border-radius: 5px;
}

::v-deep .v-select__selections {
    position: absolute;
}
::v-deep .bordered-table table,
.bordered-table th,
.bordered-table td {
    border: 1px solid #ddd;
}
</style>

import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_ANDON'
//通知记录

//获取角标列表
export function getCornerMark(data) {
    const api = '/andon/AlarmRecord/CornerMark'
    return getRequestResources(baseURL, api, 'get', data);
}
//主动获取角标列表
export function SendCornerMark(data) {
    const api = '/andon/AlarmRecord/SendCornerMark'
    return getRequestResources(baseURL, api, 'get', data);
}
//生成告警记录事件
export function AlarmRecordSaveForm(data) {
    const api = '/andon/AlarmRecord/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取告警列表
export function AlarmRecordGetList(data) {
    const api = '/andon/AlarmRecord/FindMarkRecord'
    return getRequestResources(baseURL, api, 'post', data);
}
//接警处理
export function AlarmRecordRespond(data) {
    const api = '/andon/AlarmRecord/Respond'
    return getRequestResources(baseURL, api, 'post', data);
}
// 报修
export function AlarmRecordCallForRepaired(data) {
    const api = '/andon/AlarmRecord/CallForRepaired'
    return getRequestResources(baseURL, api, 'post', data);
}
// 升级
export function AlarmRecordUpgrade(data) {
    const api = '/andon/AlarmRecord/Upgrade'
    return getRequestResources(baseURL, api, 'post', data);
}
// 到修
export function AlarmRecordToBeRepaired(data) {
    const api = '/andon/AlarmRecord/ToBeRepaired' + '/' + data.id
    return getRequestResources(baseURL, api, 'get');
}
// 关闭
export function AlarmRecordClose(data) {
    const api = '/andon/AlarmRecord/CloseWithComment'
    return getRequestResources(baseURL, api, 'post', data);
}
// 机器调整
export function AlarmRecordConfig(data) {
    const api = '/andon/AlarmRecord/Config' + '/' + data.id
    return getRequestResources(baseURL, api, 'get');
}
// 损耗
export function eventDefectSaveForm(data) {
    const api = '/andon/EventDefect/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
// 编辑查询
export function AlarmRecordDefectSelect(data) {
    const api = '/andon/AlarmRecord/DefectSelect/' + data.id
    return getRequestResources(baseURL, api, 'get');
}
// 安灯未读信息
export function getAndonNotice(data) {
    const api = '/andon/AlarmRecord/PushUserAlarmCount'
    return getRequestResources(baseURL, api, 'post', data);
}
// 已读通知后端
export function readNotice(data) {
    const api = '/andon/AlarmRecord/ReadAlarmBatch'
    return getRequestResources(baseURL, api, 'post', data);
}
// 获取下一级通知人
export function FindUpgradeUserInfo(data) {
    const api = '/andon/AlarmRecord/FindUpgradeUserInfo' + '/' + data.id
    return getRequestResources(baseURL, api, 'get');
}

// 告警图片上传
export function Upload(data) {
    const api = '/andon/AlarmRecord/Upload'
    return getRequestResources(baseURL, api, 'post', data);
}


// 获取经验库
export function GetAlarmExperienceList(data) {
    const api = '/api/AlarmSolution/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}


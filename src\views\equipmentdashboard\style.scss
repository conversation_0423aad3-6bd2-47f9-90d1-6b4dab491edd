.sbkb {
    height: 90%;
    .el-input__suffix{
        height: 115%;
    }
    body {
        height: 100%;
        width: 100%;
    }
    .all {
        background: url(./image/bj.png) 50% 50% no-repeat;
        height: 100%;
        width: 100%;
        overflow: hidden;
        position: relative;
        font-family: system-ui;
    }
    .bkimg {
        height: 100%;
        width: 100%;
        position: absolute;
        z-index: -1;
    }
    .title {
        background: url(./image/title.png) 50% 50% no-repeat;
        height: 85px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        font-size: 30px !important;
        line-height: 0 !important;
        color: #fff;
        .searchbox {
            position: absolute;
            width: 100%;
            height: 40%;
            display: flex;
            align-items: center;
            bottom: 0;
            left: 15px;
            .nowtimebox {
                background: url(./image/time.png) 100% 100% no-repeat;
                position: absolute;
                right: 10%;
                font-size: 1.4vh;
                width: 290px;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .dashboardinputbox {
                margin-right: 1vh;
                display: flex;
                align-items: center;
                font-size: 1.3vh;
                width: 150px;
                .el-button {
                    border: 1px solid rgb(1, 117, 206);
                    border-radius: 0;
                    color: #fff;
                    background: #022555;
                    height: 30px;
                    font-size: 1.3vh;
                }
                .dashboardinputboxlabel {
                    width: 6vh;
                }
                .el-range-editor .el-range-input {
                    color: #fff;
                }
                .el-input__inner {
                    background: #022555;
                    border: 1px solid rgb(1, 117, 206);
                    border-radius: 0;
                    height: 30px;
                    line-height: 30px;
                    color: #fff;
                }
                .el-date-editor .el-range__icon {
                    line-height: 25px;
                }
                .el-date-editor .el-range__close-icon {
                    line-height: 25px;
                }
                .el-date-editor .el-range-separator {
                    color: #fff;
                    line-height: 24px;
                }
            }
        }
    }
    .tabbox {
        width: 100%;
        height: 88%;
        margin-top: 1.5vh;
        display: flex;
        align-items: center;
        flex-direction: column;
    }
    .tabboxrow {
        flex: 1;
        width: 100%;
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding-left: 15px;
        .tabboxbox {
            height: 100%;
            border: 1px solid #04A2C9;
            box-shadow: 0 0 1px #fff, 0 0 2px #fff inset;
            margin-right: 15px;
            .tabboxboxtitle {
                height: 10%;
                width: 100%;
                display: flex;
                align-items: center;
                font-size: 1.8vh;
                padding-left: 3vh;
                color: #fff;
                background-image: linear-gradient(to left, rgba(255, 255, 255, 0), #0561A7);
            }
            .tabboxboxcenter {
                margin-top: 2px;
                width: 100%;
                height: 89%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                .tabboxboxcentertop {
                    padding: 0 10vh;
                    width: 100%;
                    height: 60%;
                    display: flex;
                    .tabboxboxcenterleft {
                        width: 75%;
                        height: 100%;
                    }
                    .tabboxboxcenterright {
                        width: 40%;
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        .tabboxboxcentericon {
                            width: 104%;
                            height: 45%;
                        }
                    }
                }
                .tabboxboxcenterbottom {
                    padding: 0 10vh;
                    width: 100%;
                    height: 32%;
                    display: flex;
                    justify-content: space-around;
                }
                .tabboxboxcentericon {
                    background: url(./image/iconborder.png) 50% 50% no-repeat;
                    width: 33%;
                    height: 100%;
                    display: flex;
                    color: #2EE7FF;
                    align-items: center;
                    justify-content: space-evenly;
                    padding: 0 15px;
                    .valuebox {
                        text-align: center;
                        .valueboxnum {
                            font-size: 1.5rem;
                        }
                        .valueboxtitle {
                            font-size: 1.5vh;
                            color: #fff
                        }
                    }
                }
                .el-table {
                    background-color: transparent;
                }
                .el-table th.el-table__cell>.cell {
                    font-size: 1.3vh;
                }
                .el-table tr {
                    background-color: transparent;
                }
                .el-table::before {
                    width: 0;
                }
                .el-table .cell {
                    color: #fff
                }
                .el-table th.el-table__cell {
                    border-top: 1px solid #04A2C9;
                    border-bottom: 1px solid #04A2C9;
                    background: rgba(5, 97, 167, 0.5)!important;
                    color: #fff
                }
                .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
                    background: rgba(5, 97, 167, 0.3)!important;
                }
                .el-table td.el-table__cell {
                    font-size: 1.2vh;
                    border-bottom: 1px solid #04A2C9;
                    background: transparent;
                }
                .el-table__empty-text {
                    color: #fff
                }
            }
        }
    }
}

.rowtabbox {
    margin: 0 1vh;
    height: 100%;
    cursor: pointer;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.8vh;
    position: relative;
}

.dbimg {
    position: absolute;
    z-index: -1;
}

.center {
    display: flex;
    position: relative;
}

.col {
    height: 100%;
    width: 6%;
}

.col1 {
    margin-left: 5%;
}

.col2 {
    margin-left: 3.5%;
}

.col3 {
    margin-left: 2.5%;
}

.col4 {
    margin-left: .5%;
}

.col5 {
    margin-left: .7%;
}

.col6 {
    margin-left: .5%;
}

.col7 {
    margin-left: 1%;
}

.col8 {
    margin-left: 1%;
}

.col9 {
    margin-left: 2.4%;
}

.col10 {
    margin-left: 3.5%;
}

.col11 {
    margin-left: 4.5%;
}

.col12 {
    margin-left: 1%;
}

.statemsgrow {
    font-size: .1vw;
    display: flex;
    height: 20%;
    margin: .3vh 0;
    text-align: right;
    letter-spacing: .1vh;
}

.statetitlebox {
    color: #00FBFF;
    width: 12vh;
}

.sbmsgrowbox .sbmsgval {
    line-height: 2vh;
}

.statevalbox {
    width: 8vh;
    text-align: left;
}

.hc1 {
    left: 3%;
    top: 25%;
}

.hc2 {
    left: 50%;
    top: 18%;
}

.hc3 {
    left: 57%;
    top: 18%;
}

.hc4 {
    left: 15.5%;
    top: 73%;
}

.hc5 {
    left: 50%;
    top: 56%;
}

.hc6 {
    left: 57%;
    top: 56%;
}

.centertitle {
    position: absolute;
    color: #fff;
    font-size: 2vh;
}

.centertitle1 {
    left: 3%;
    top: 3%;
}

.centertitle2 {
    left: 3%;
    top: 43%;
}

.centertitle3 {
    left: 82.5%;
    top: 62%;
}

.centertitle4 {
    left: 95%;
    top: 19%;
}

.ltkbox {
    position: absolute;
    width: 11.5vh;
    left: 91%;
    top: 58%;
}

.csmain {
    padding: 0 1vh;
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: space-between;
}

.csleft {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-right: 2vh;
}

.cscenter {
    flex: 2;
    display: flex;
    flex-direction: column;
    margin-right: 2vh;
}

.csright {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.leftbox1 {
    width: 100%;
    height: 25%;
    margin-bottom: 2vh;
}

.leftbox2 {
    width: 100%;
    flex: 2;
}

.leftmainbox {
    height: 88%;
    display: flex;
    flex-direction: column;
}

.numbox {
    height: 25%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 1vh 0;
}

.leftnumbox {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 70%;
    height: 100%;
}

.numboximg {
    padding: 1vh 0.5vh;
    margin-left: 1vh;
    width: 32%;
    height: 100%;
}

.bottomchartbox {
    height: 65%;
}

.jhbox {
    height: 80%;
    display: flex;
    flex-direction: column;
}

.jhtitle {
    color: #FFF;
    padding: 0 1vh;
    font-size: 1.7vh;
    height: 2.5vh;
    border-bottom: 1px solid;
    width: fit-content;
    border-image: -webkit-linear-gradient(-270deg, rgba(255, 255, 255, 0.00) 0%, #00BBF2 50%, rgba(255, 255, 255, 0.00) 99%) 2 2 2 2;
    border-image: -moz-linear-gradient(-270deg, rgba(255, 255, 255, 0.00) 0%, #00BBF2 50%, rgba(255, 255, 255, 0.00) 99%) 2 2 2 2;
    border-image: linear-gradient(-270deg, rgba(255, 255, 255, 0.00) 0%, #00BBF2 50%, rgba(255, 255, 255, 0.00) 99%) 2 2 2 2;
}

.jhsinglebox {
    height: 32%;
    margin-top: 1vh;
}

.jhcenterbox {
    height: 86%;
    display: flex;
}

.jhnumbox {
    width: 60%;
    padding: 1.5vh 2vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.jhbottomnum {
    width: 92%;
    padding: .8vh;
    height: 5vh;
    background-color: rgba(0, 102, 255, 0.3);
    color: #fff;
    display: flex;
}

.jhchartbox {
    width: 40%;
}

.jhval {
    color: #00EEF8;
    font-size: 2.2vh;
    width: fit-content;
    display: flex;
    align-items: flex-end;
    margin-left: 1vh;
}

.jhsbox {
    width: 11vh;
    display: flex;
    align-items: flex-end;
}

.jhsimg {
    width: 3vh;
    margin: 0 1vh;
}

.cscenterbox {
    height: 29.2%;
    margin-bottom: 2vh;
}

.csrightbox {
    height: 29.2%;
    margin-bottom: 2vh;
}

.csboxtitle {
    font-weight: 700;
    font-size: 1.7vh;
    height: 3vh;
    width: fit-content;
    padding: 0 2vh;
    line-height: 3.5vh;
    background: linear-gradient(to right, #037BF8 15%, #01EEF9 50%, #037BF8 90%);
    -webkit-background-clip: text;
    color: transparent;
}

.sbtitle {
    height: 2.7vh;
    font-weight: 700;
    font-size: 1.7vh;
    color: #00EEF8;
    text-align: center;
    margin-top: 1vh;
}

.sbmsgbox {
    margin-top: 1.5vh;
    height: 71%;
    display: flex;
    padding: 0 2.5vh;
}

.sbimgbox {
    border: 1px solid #11277D;
    padding: 1vh;
    width: 63%;
    margin-right: 1vh;
}

.sbimgbox img {
    width: 100%;
    height: 100%;
}

.sbmsgrowbox {
    width: 30%;
    display: flex;
    flex-direction: column;
}

.sbmsgimg {
    padding: 1vh 2vh 1vh 2.5vh;
    width: 30%;
    height: 128%;
}

.sbmsgimg img {
    height: 65%;
}

.sbmsgtitleright {
    padding: .5vh 0;
    color: #fff;
    height: 2.6vh;
}

.sbmsgval {
    color: #00EEF8;
    font-size: 1.9vh;
}

.djbox {
    height: 89%;
    width: 50%;
}

.djtitle {
    font-size: 1.8vh;
    color: #00EEF8;
    width: 100%;
    text-align: center;
    height: 2vh;
    margin: 3vh 0 1vh 0;
}

.chartboxdj {
    height: 77%;
}

.centerchartbox {
    display: flex;
    height: 84%;
}

.gjzbbox {
    margin-top: 1vh;
    width: 25%;
    color: #09FCFE;
    font-size: 1.8vh;
    text-align: center;
}

.gjchart {
    height: 85%;
    width: 100%;
}
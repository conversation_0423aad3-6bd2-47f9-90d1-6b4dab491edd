// 线边货架相关接口

import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM'
//新增、编辑线边货架
export function saveForm(data) {
    const api =  '/api/Racking/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
// 获取线边货架列表
export function GetPageList(data) {
    const api =  '/api/Racking/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
// 删除线边货架
export function DeleteRacking(data) {
    const api =  '/api/Racking/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

//新增、编辑线边货架位
export function saveRackingBinForm(data) {
    const api =  '/api/RackingBin/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
// 获取线边货架位列表
export function GetRackingBinPageList(data) {
    const api =  '/api/RackingBin/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
// 删除线边货架位
export function DeleteRackingBin(data) {
    const api =  '/api/RackingBin/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

//新增、编辑货架对应设备机台
export function saveRackingDeviceForm(data) {
    const api =  '/api/RackingDevice/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
// 获取货架对应设备机台列表
export function GetRackingDevicePageList(data) {
    const api =  '/api/RackingDevice/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
// 删除线货架对应设备机台
export function DeleteRackingDevice(data) {
    const api =  '/api/RackingDevice/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

//获取树型结构
export function getRackingTree(data) {
    const api =  '/api/Racking/GetTree'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑线边货架位物料信息
export function saveRackingBinMaterialForm(data) {
    const api =  '/api/RackingBinMaterial/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
// 获取线边货架位物料列表
export function GetRackingBinMaterialPageList(data) {
    const api =  '/api/RackingBinMaterial/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
// 删除线边货架位物料
export function DeleteRackingBinMaterial(data) {
    const api =  '/api/RackingBinMaterial/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

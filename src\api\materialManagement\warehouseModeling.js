import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_MATERIAL'
// 仓库建模

//不分页获取仓库列表
export function getWarehouseList(data) {
    const api =  '/materail/WarehouseManage/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//分页获取仓库列表
export function getWarehouseManageList(data) {
    const api =  '/materail/WarehouseManage/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑仓库
export function WarehouseManageSaveForm(data) {
    const api =  '/materail/WarehouseManage/SaveForm1'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除仓库
export function DeleteWarehouseManage(data) {
    const api =  '/materail/WarehouseManage/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

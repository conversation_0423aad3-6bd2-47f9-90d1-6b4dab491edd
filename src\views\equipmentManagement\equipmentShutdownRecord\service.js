import { getRequestResources } from '@/api/fetch';
const TPM = 'baseURL_TPM', DFM = 'baseURL_DFM'

// 查询停机记录列表
export function getRecordList(data) {
    const api = '/tpm/StopRecord/GetPageList'
    return getRequestResources(TPM, api, 'post', data)
}

// 查询原因列表
export function getReasonList(data) {
    const api = '/api/Reason/GetList'
    return getRequestResources(DFM, api, 'post', data)
}

//  查询停机记录详情列表
export function getRecordDetailList(data) {
    const api = '/tpm/StopRecord/GetDetailPageList'
    return getRequestResources(TPM, api, 'post', data)
}

// 新增停机详情
export function saveDetailForm(data) {
    const api = '/tpm/StopRecord/SaveForm'
    return getRequestResources(TPM, api, 'post', data)
}
// 更新停机详情
export function updateDetailForm(data) {
    const api = '/tpm/StopRecord/UpdateInfo'
    return getRequestResources(TPM, api, 'post', data)
}

// 删除停机详情
export function deleteStopDetail(data) {
    const api = '/tpm/StopRecord/Delete'
    return getRequestResources(TPM, api, 'post', data)
}
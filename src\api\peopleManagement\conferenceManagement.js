import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_SHIFT; // 配置服务url
//  会议列表
export function MeetingGetPageList(data) {
    return request({
        url: baseURL + '/shift/Meeting/GetPageList',
        method: 'post',
        data
    });
}
// 会议列表-新增
export function MeetingtSaveForm(data) {
    return request({
        url: baseURL + '/shift/Meeting/SaveForm',
        method: 'post',
        data
    });
}
// 会议列表 -人员删除
export function MeetingDelete(data) {
    return request({
        url: baseURL + '/shift/Meeting/Delete',
        method: 'post',
        data
    });
}

// 会议详情
export function MeetingStaffGetPageList(data) {
    return request({
        url: baseURL + '/shift/MeetingStaff/GetPageList',
        method: 'post',
        data
    });
}




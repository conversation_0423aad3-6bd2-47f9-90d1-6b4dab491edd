// 工单
export const workorderColum = [

    // {
    //     text: '工单状态',
    //     value: 'WoStatus',
    //     width: 100,
    //     sortable: true
    // },
    {
        text: 'SAP订单',
        value: 'SapWoCode',
        width: 100,
        sortable: true
    },
    {
        text: '工单',
        value: 'WoCode',
        width: 100,
        sortable: true
    },
    {
        text: '班次',
        value: 'ShiftName',
        width: 100,
        sortable: true
    },
    {
        text: '线体',
        value: 'FullLineName',
        width: 120,
        sortable: true
    },
    // {
    //     text: '工段',
    //     value: 'SectionName',
    //     width: 120,
    //     sortable: true
    // },
    {
        text: '工段',
        value: 'CompanyName',
        width: 170,
        sortable: true
    },

    {
        text: '产品料号',
        value: 'MaterialCode',
        width: 150,
        sortable: true
    },
    {
        text: '产品描述',
        value: 'MaterialDescription',
        width: 250,
        sortable: true
    },
    {
        text: '计划量(PCS)',
        width: 120,
        value: 'WoQuantity',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: '成批量(PCS)',
        value: 'WoSumBatchQuantity',
        semicolonFormat: true,
        width: 120,
        sortable: true
    },
    {
        text: '确认量(PCS)',
        value: 'WoConfirmBatchQuantity',
        semicolonFormat: true,
        width: 120,
        sortable: true
    },
    {
        text: '完工量(PCS)',
        value: 'WoCompleteQuantity',
        semicolonFormat: true,
        width: 120,
        sortable: false
    },
    {
        text: '计划开始',
        value: 'PlanStartTime',
        width: 160,
        sortable: true
    },
    {
        text: '计划结束',
        value: 'PlanEndTime',
        width: 160,
        sortable: true
    },
    {
        text: '实际开始',
        value: 'ActualStartTime',
        width: 160,
        sortable: true
    },
    {
        text: '实际结束',
        value: 'ActualEndTime',
        width: 160,
        sortable: true
    },
    {
        text: '关单原因',
        value: 'Reason',
        width: 160,
        sortable: true
    },
    { text: '操作', align: 'center', width: 100, value: 'actions', sortable: true }
];
// 批次

export const batchColum = [
    // {
    //     text: '序号',
    //     value: 'Index',
    //     sortable: true
    // },
    {
        text: '工段',
        value: 'CompanyName',
        width: '160px',
        sortable: true
    },
    {
        text: '批次号',
        value: 'BatchNo',
        width: 100,
        sortable: true
    },
    // {
    //     text: '工单号',
    //     value: 'WoCode',
    //     width: 80,
    //     sortable: true
    // },
    {
        text: '产品料号',
        value: 'MaterialCode',
        width: 80,
        sortable: true
    },
    {
        text: '产品描述',
        value: 'MaterialDescription',
        width: 150,
        sortable: true
    },

    // {
    //     text: '档次',
    //     value: 'TestGrade',
    //     width: 60,
    //     sortable: true
    // },
    {
        text: '数量(PCS)',
        width: 120,
        value: 'BatchQuantity',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: '开始时间',
        value: 'BatchStartTime',
        width: 150,
        sortable: true
    },
    {
        text: '结束时间',
        value: 'BatchEndTime',
        width: 150,
        sortable: true
    },
    // {

    //     text: '状态',
    //     width: 140,
    //     value: 'CurrentProcStatus',
    //     sortable: true
    // }
];
// 物料
export const materialColum = [
    // {
    //     text: '序号',
    //     value: 'Index',
    //     sortable: true
    // },
    // {
    //     text: '产线',
    //     width: 140,
    //     value: 'CompanyName',
    //     sortable: true
    // },
    // {
    //     text: 'SAP订单号',
    //     value: 'SapWoCode',
    //     width: 100,
    //     sortable: true
    // },
    {
        text: '工单号',
        width: 120,
        value: 'WoCode',
        sortable: true
    },
    {
        text: '物料料号',
        width: 120,
        value: 'MaterialCode',
        sortable: true
    },
    {
        text: '物料描述',
        width: 260,
        value: 'MaterialDescription',
        sortable: true
    },

    {
        text: 'SCADA计数量',
        width: 140,
        value: 'ScadaTagQty',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: 'BOM用量',
        width: 140,
        value: 'BomQty',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: 'SCADA消耗量',
        width: 140,
        value: 'ScadaConsumptionQty',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: '确认消耗量',
        width: 140,
        value: 'ConfirmConsumptionQty',
        isEditCell: false,
        sortable: true
    },

    {
        text: '单位',
        value: 'MaterialUom',
        width: 100,
        sortable: true
    }
];
// 人员
export const personnelColum = [
    // {
    //     text: '序号',
    //     value: 'Index',
    //     sortable: true
    // },
    {
        text: 'UWB号牌',
        width: 120,
        value: 'UWB'
    },
    {
        text: '员工工号',
        width: 140,
        value: 'Code'
    },
    {
        text: '员工名称',
        width: 120,
        value: 'Name'
    },
    {
        text: '岗位',
        width: 120,
        value: 'JobContent'
    },
    {
        text: '工时',
        width: 120,
        value: 'Duration'
    },
    // {
    //     text: '班次',
    //     width: 100,
    //     value: 'Shift'
    // },
    {
        text: '班次',
        width: 140,
        value: 'Shift'
    }
];
// 质量
export const qualityColum = [
    // {
    //     text: '序号',
    //     value: 'Index',
    //     sortable: true
    // },
    {
        text: '产线',
        width: 140,
        value: 'CompanyName',
        sortable: true
    },
    {
        text: '工单号',
        width: 120,
        value: 'WoCode',
        sortable: true
    },
    {
        text: '产品料号',
        width: 120,
        value: 'MaterialCode',
        sortable: true
    },
    {
        text: '产品描述',
        width: 260,
        value: 'MaterialDescription',
        sortable: true
    },
    {
        text: '测试批号',
        width: 100,
        value: 'BatchNo',
        sortable: true
    },
    {
        text: '初始数量(PCS)',
        width: 140,
        value: 'BatchQuantity',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: '剩余数量(PCS)',
        width: 140,
        value: 'RemainQuantity',
        isEditCell: true,
        sortable: true
    },
    {
        text: '类型',
        width: 100,
        value: 'TestGrade',
        sortable: true
    },
    {
        text: '状态',
        value: 'CurrentProcStatus',
        width: 100,
        sortable: true
    },
    {
        text: '作业时间',
        width: 180,
        value: 'CreateDate',
        sortable: true
    }
];


// 机台
export const equipmentColum = [
    {
        text: '序号',
        value: 'Index',
        width: 80,
        sortable: true
    },
    {
        text: '设备编码',
        width: 120,
        value: 'EquipmentCode'
    },
    {
        text: '设备名称',
        width: 140,
        value: 'EquipmentName'
    },
    {
        text: '类型',
        width: 120,
        value: 'Level'
    },
    // {
    //     text: '创建时间',
    //     width: 160,
    //     value: 'CreateDate'
    // },
    {
        text: '操作人',
        width: 100,
        value: 'PersonCode'
    },
    { text: '操作', align: 'center', width: 100, value: 'actions' }
];

// 产出信息
export const outputColum = [
    {
        text: '工单',
        value: 'WoCode',
        width: '160px',
        sortable: true
    },
    {
        text: '产品线',
        value: 'FullLineName',
        width: '160px',
        sortable: true
    },
    {
        text: '工段',
        value: 'CompanyName',
        width: '160px',
        sortable: true
    },
    {
        text: '班组',
        value: 'TeamName',
        width: '100px',
        sortable: true
    },
    {
        text: '班次',
        value: 'ShiftName',
        width: '100px',
        sortable: true
    },
    {
        text: '工站',
        value: 'ProcName',
        width: '160px',
        sortable: true
    },
    {
        text: '物料号',
        value: 'MaterialCode',
        width: '160px',
        sortable: true
    },
    {
        text: '物料描述',
        value: 'MaterialDescription',
        width: '160px',
        sortable: true
    },
    {
        text: '生产批号',
        value: 'MaterialProductBatchNo',
        width: '160px',
        sortable: true
    },
    {
        text: '追溯批号',
        value: 'MaterialBatchNo',
        width: '160px',
        sortable: true
    },
    {
        text: '上料数量',
        value: 'BatchQuantity',
        width: '100px',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: '单位',
        value: 'FeedUnit',
        width: '100px',
        sortable: true
    },
    {
        text: '操作员',
        value: 'ModifyUserId',
        width: '120px',
        sortable: true
    },
    {
        text: '上料时间',
        value: 'CreateDate',
        width: '160px',
        sortable: true
    },
    { text: '操作', align: 'center', width: 110, value: 'actions' }
];

// 质量信息
export const qualityInfoColum = [
    {
        text: '检验项目',
        value: 'TestItem',
        width: '160px'
    },
    {
        text: '产品编码',
        value: 'ProductionCode',
        width: '160px',
        sortable: true
    },
    {
        text: ' 产品名称',
        value: 'ProductionName',
        width: '160px',
        sortable: true
    },
    {
        text: '产线',
        value: 'Line',
        width: '100px',
        sortable: true
    },
    {
        text: '工段',
        value: 'Segment',
        width: '100px',
        sortable: true
    },
    {
        text: '工站',
        value: 'EquipmentName',
        width: '160px',
        sortable: true
    },
    {
        text: '最大值',
        value: 'Maxvalue',
        width: '160px',
        sortable: true
    },
    {
        text: '最小值',
        value: 'Minvalue',
        width: '160px',
        sortable: true
    },
    {
        text: '标准值',
        value: 'Standardvalue',
        width: '160px',
        sortable: true
    },
    {
        text: '检测值',
        value: 'MeasuredValue',
        width: '160px',
        sortable: true
    },
    { text: '', align: 'center', width: 0, value: 'noActions' }
];
// 工单SN查询
export const workSnQuery = [
    { text: '产线', value: 'FullLineName' },
    { text: '批次', value: 'BatchNo' },
    { text: '订单', value: 'SapWoCode' },
    { text: '工单', value: 'WoCode' },
    { text: 'Sn', value: 'Sn' },
    { text: '', align: 'center', width: 0, value: 'noActions' }
]
export const productTraceColumns = [
    {
        label: '工单号',
        width: 150,
        prop: 'WoCode'
    },
    {
        label: '物料批号',
        width: 220,
        prop: 'BatchNo',
        template: 'BatchNo',
        type: 'template',
    },

    {
        label: '物料批次类型',
        prop: 'Type'
    },
    {
        label: '测试等级',
        prop: 'TestGrade'
    },
    {
        label: '物料编码',
        width: 110,
        prop: 'MaterialCode'
    },
    {
        label: '物料描述',
        prop: 'MaterialDescription'
    },
    {
        label: '批次数量',
        prop: 'BatchQuantity',
        semicolonFormat: true
    },
    // {
    //     label: '占用数量',
    //     width: 100,
    //     prop: 'BindQuantity',
    //     semicolonFormat: true
    // },
    {
        label: '单位',
        prop: 'Unit',
        semicolonFormat: true
    },
    {
        label: '批次创建时间',
        width: 155,
        prop: 'CreateDate'
    },
    {
        label: '上料时间',
        prop: 'FeedingTime'
    }
];
export const glueTracingColumn = [
    { text: '序号', value: 'Index', width: '60px' },
    { text: '物料编号', value: 'Materialcode', width: '130px' },
    { text: '胶水型号', value: 'Materialname', width: '130px' },
    { text: '胶管条码', value: 'Serialno', width: '180px' },
    { text: '单管容量', value: 'SingleTubeWeight', width: '120px' },
    { text: '失效时间', value: 'Expdate', width: '160px' },
    { text: '上下料状态', value: 'IsLoading', width: '110px', dictionary: true },
    { text: '上料时间', value: 'LoadingDate', width: '160px' },
    { text: '下料时间', value: 'UnLoadingDate', width: '160px' },
    { text: '设备编码', value: 'EquipmentCode', width: '160px' },
    { text: '仓库编号', value: 'WarehouseCode', width: '160px' },
    { text: '仓库名称', value: 'WarehouseName', width: '160px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '',
        align: 'center',
        value: 'noActions',
        width: ''
    }
]
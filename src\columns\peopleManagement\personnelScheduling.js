// 楼层人员主数据
export const personalColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '员工号',
        value: 'StaffCode',
        width: 100,
        sortable: true
    },
    {
        text: '姓名',
        value: 'StaffName',
        width: 100,
        sortable: true
    },
    {
        text: '工段',
        value: 'LineName',
        width: 100,
        sortable: true
    },
    {
        text: '班组',
        value: 'ShiftGroupName',
        width: 100,
        sortable: true
    },
    {
        text: '操作',
        width: 120,
        align: 'center',
        value: 'actions',
        sortable: true
    }
];
// 排班结果
export const personaResulterlColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '工单号',
        value: 'WoCode',
        width: 120,
        sortable: true
    },
    {
        text: '员工号',
        value: 'StaffCode',
        width: 100,
        sortable: true
    },
    {
        text: '姓名',
        value: 'StaffName',
        width: 100,
        sortable: true
    },
    {
        text: '班组',
        value: 'ShiftGroupName',
        width: 120,
        sortable: true
    },
    {
        text: '班次',
        value: 'ShiftName',
        width: 100,
        sortable: true
    },
    {
        text: '工段',
        value: 'LineName',
        width: 160,
        sortable: true
    },
    {
        text: '计划日期',
        value: 'PlanDate',
        width: 140,
        sortable: true
    },
    {
        text: '实际开始时间',
        value: 'ActualStarttime',
        width: 160,
        sortable: true
    },
    {
        text: '实际结束时间',
        value: 'ActualEndtime',
        width: 160,
        sortable: true
    },
    {
        text: '工作时长（h）',
        value: 'Duration',
        width: 140,
        sortable: true
    },
    {
        text: '良品数量',
        value: 'ProductGood',
        width: 140,
        sortable: true
    },
    {
        text: '次品数量',
        value: 'ProductDefective',
        width: 140,
        sortable: true
    },

    {
        text: '操作',
        width: 120,
        align: 'center',
        value: 'actions',
        sortable: true
    }
];

// 工单号结果
export const classResultlColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '工单号',
        value: 'Code',
        width: 120,
        sortable: true
    },
    {
        text: '物理线工段',
        value: 'LineName',
        width: 100,
        sortable: true
    },
    {
        text: '开始时间',
        value: 'PlanStarttime',
        width: 160,
        sortable: true
    },
    {
        text: '结束时间',
        value: 'PlanEndtime',
        width: 160,
        sortable: true
    },
    {
        text: '班组',
        value: 'Team',
        width: 140,
        sortable: true
    }
];
//工作时间线
export const workTimelColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '员工号',
        value: 'StaffCode',
        width: 100,
        sortable: true
    },
    {
        text: '姓名',
        value: 'StaffName',
        width: 100,
        sortable: true
    },
    {
        text: '0:00',
        value: '00-00',
        sortable: true
    },
    {
        text: '1:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '2:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '3:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '4:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '5:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '6:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '7:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '8:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '9:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '10:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '11:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '12:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '13:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '14:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '15:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '16:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '17:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '18:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '19:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '20:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '21:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '22:00',
        value: 'ShiftGroupId',
        sortable: true
    },
    {
        text: '23:00',
        value: 'ShiftGroupId',
        sortable: true
    }
];

// 在岗统计
export const jobsStatisticsColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '员工号',
        value: 'StaffCode',
        width: 100,
        sortable: true
    },
    {
        text: '姓名',
        value: 'StaffName',
        width: 100,
        sortable: true
    },
    {
        text: '工段/岗位',
        value: 'ShiftGroupId',
        width: 140,
        sortable: true
    },
    {
        text: '是否在岗',
        value: 'ShiftGroupId',
        width: 140,
        sortable: true
    }
];


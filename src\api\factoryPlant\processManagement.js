import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM'
// 物料工艺建模相关接口

// 获取工序资源路线树
export function GetClassifyTree() {
    const api = '/api/RoutingProduct/GetClassifyTree'
    return getRequestResources(baseURL, api, 'get');
}
// 添加和编辑工序资源路线树
export function SaveFormForTree(data) {
    const api = '/api/RoutingProduct/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
// 删除工序资源路线树
export function deleteForTree(data) {
    const api = '/api/RoutingProduct/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}



//新增、编辑基础信息
export function ProcBasicinfoSaveForm(data) {
    const api = '/api/ProcBasicinfo/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除基础信息
export function DeleteBasicinfo(data) {
    const api = '/api/ProcBasicinfo/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取基础信息列表数据
export function GetProcBasicinfoPageList(data) {
    const api = '/api/ProcBasicinfo/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}


//批量新增设备
export function deviceSaveList(data) {
    const api = '/api/ProcDevice/SaveList'
    return getRequestResources(baseURL, api, 'post', data);
}
//编辑设备
export function deviceSaveForm(data) {
    const api = '/api/ProcDevice/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除设备
export function DeleteDevice(data) {
    const api = '/api/ProcDevice/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
// 删除设备(新)
export function NewDeleteDevice(data) {
    const api = '/api/ProcEquipmentMapping/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取设备列表数据
export function GetProcDevicePageList(data) {
    const api = '/api/ProcDevice/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取设备列表数据(新)
export function GetNewProcDevicePageList(data) {
    const api = '/api/ProcEquipmentMapping/GetEquipmentList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function getDeviceList(data) {
    const api = '/api/Equipment/GetProcessList'
    return getRequestResources(baseURL, api, 'post', data);
}
// 保存关联设备操作
export function saveRelevancyDevice(data) {
    const api = '/api/ProcEquipmentMapping/SaveForm'
    return getRequestResources(baseURL, api, 'post', data)
}


//新增、编辑工装夹具信息
export function fixturesSaveForm(data) {
    const api = '/api/ProcFixtures/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除工装夹具信息
export function DeleteFixtures(data) {
    const api = '/api/ProcFixtures/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取设备类型列表
export function GetPageDeviceCategoryList() {
    const api = '/api/DeviceCategory/GetList'
    return getRequestResources(baseURL, api, 'post');
}
//获取工装夹具列表数据
export function GetProcFixturesPageList(data) {
    const api = '/api/ProcFixtures/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}


// 根据code获取version列表
export function GetVersionList(data) {
    const api = '/api/MaterialBomDetail/GetVersionList'
    return getRequestResources(baseURL, api, 'post', data, true);
}
// 根据code和version获取bom明细列表
export function getProcMaterialBomDetail(data) {
    const api = '/api/MaterialBomDetail/GetTreeListByMcodeEx'
    return getRequestResources(baseURL, api, 'post', data);
}

//获取物料列表数据
export function getProcMaterialPageList(data) {
    const api = '/api/ProcMaterial/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

//新增物料
export function materialSaveList(data) {
    const api = '/api/ProcMaterial/SaveList'
    return getRequestResources(baseURL, api, 'post', data);
}
//编辑物料
export function materialSaveForm(data) {
    const api = '/api/ProcMaterial/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除物料
export function DeleteMaterial(data) {
    const api = '/api/ProcMaterial/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}


//获取工艺文件主表数据
export function getProcFileList() {
    const api = '/api/ProcFile/GetList'
    return getRequestResources(baseURL, api, 'post');
}
//获取工艺文件列表数据
export function getProcFilePageList(data) {
    const api = '/api/ProcFileDetail/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑工艺文件
export function fileSaveForm(data) {
    const api = '/api/ProcFileDetail/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除工艺文件
export function DeleteFile(data) {
    const api = '/api/ProcFileDetail/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}


//获取工艺参数列表数据
export function getProcParamPageList(data) {
    const api = '/api/ProcParam/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑工艺参数
export function paramSaveForm(data) {
    const api = '/api/ProcParam/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除工艺参数
export function DeleteParam(data) {
    const api = '/api/ProcParam/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}


//获取工艺环境参数列表数据
export function getProcEnvparamPageList(data) {
    const api = '/api/ProcEnvparam/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑工艺环境参数
export function ProcEnvparamSaveForm(data) {
    const api = '/api/ProcEnvparam/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除工艺环境参数
export function DeleteEnvparam(data) {
    const api = '/api/ProcEnvparam/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}


//获取工艺环境参数列表数据
export function getProcStaffingPageList(data) {
    const api = '/api/ProcStaffing/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑工艺环境参数
export function ProcStaffingSaveForm(data) {
    const api = '/api/ProcStaffing/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除工艺环境参数
export function DeleteStaffing(data) {
    const api = '/api/ProcStaffing/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

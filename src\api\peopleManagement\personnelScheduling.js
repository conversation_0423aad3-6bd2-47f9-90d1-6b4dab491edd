import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_SHIFT; // 配置服务url
//  楼层主数据列表
export function LineStaffGetPageList(data) {
    return request({
        url: baseURL + '/shift/StaffLine/GetPageList',
        method: 'post',
        data
    });
}
// 楼层主数据 -人员排班
export function LineStaffSaveList(data) {
    return request({
        url: baseURL + '/shift/StaffLine/SaveList',
        method: 'post',
        data
    });
}
// 楼层主数据 -人员删除
export function StaffLineDelete(data) {
    return request({
        url: baseURL + '/shift/StaffLine/Delete',
        method: 'post',
        data
    });
}

// 人员排班结果
export function ScheduleGetPageList(data) {
    return request({
        url: baseURL + '/shift/Schedule/GetPageList',
        method: 'post',
        data
    });
}
// 人员排班删除
export function ScheduleDelete(data) {
    return request({
        url: baseURL + '/shift/Schedule/Delete',
        method: 'post',
        data
    });
}
// 工单列表
export function OrderGetPageList(data) {
    return request({
        url: baseURL + '/shift/Order/GetPageList',
        method: 'post',
        data
    });
}
// 工单关联
export function ScheduleGenerationByOrder(data) {
    return request({
        url: baseURL + '/shift/Schedule/GenerationByOrder',
        method: 'post',
        data
    });
}


// 获取员工工时
export function GetStaffWorkTime(data) {
    return request({
        url: baseURL + '/shift/ShceduleDetail/GetStaffWorkTime',
        method: 'post',
        data
    });
}

// 获取 uwb 数据分类 类型
export function ShceduleDetaiGetTypes(data) {
    return request({
        url: baseURL + '/shift/ShceduleDetail/GetTypes',
        method: 'post',
        data
    });
}

// 获取实际排版人员结果
export function GetActualAttendanceInfo(data) {
    return request({
        url: baseURL + '/shift/StaffLine/GetActualAttendanceInfo',
        method: 'post',
        data
    });
}

// 人员排班一键同步
export function SyncActualAttendanceStaff(data) {
    return request({
        url: baseURL + '/shift/StaffLine/SyncActualAttendanceStaff',
        method: 'post',
        data
    });
}


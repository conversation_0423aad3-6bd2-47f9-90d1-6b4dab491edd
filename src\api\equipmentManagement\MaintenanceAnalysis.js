import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';

// 维修设备数量
export function DeviceChartGetRepairQty(data) {
    const api = '/api/DeviceChart/GetRepairQty';
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取维修率
export function DeviceChartGetRepairRate(data) {
    const api = '/api/DeviceChart/GetRepairRate';
    return getRequestResources(baseURL, api, 'post', data);
}

// 维修任务
export function DeviceChartGetDeviceRepairWo(data) {
    const api = '/api/DeviceChart/GetDeviceRepairWo';
    return getRequestResources(baseURL, api, 'post', data);
}

// 维修次数
export function DeviceChartGetRepairSum(data) {
    const api = '/api/DeviceChart/GetRepairSum';
    return getRequestResources(baseURL, api, 'post', data);
}

// 维修时长
export function DeviceChartGetDeviceRepair(data) {
    const api = '/api/DeviceChart/GetDeviceRepair';
    return getRequestResources(baseURL, api, 'post', data);
}

// // 作业次数
// export function DeviceChartGetWorkSum(data) {
//     const api = '/api/DeviceChart/GetWorkSum';
//     return getRequestResources(baseURL, api, 'post', data);
// }
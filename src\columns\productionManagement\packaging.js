export const packagigColum = [
    {
        text: '产线',
        width: 140,
        value: 'FullLineName',
        sortable: true
    },
    {
        text: '箱号',
        width: 140,
        value: 'BoxNumber',
        //semicolonFormat: true,
        sortable: true
    },
    {
        text: '产品编码',
        width: 140,
        value: 'MaterialCode',
        //semicolonFormat: true,
        sortable: true
    },
    {
        text: '数量(PCS)',
        width: 140,
        value: 'Quantity',
        //semicolonFormat: true,
        sortable: true
    },
    {
        text: '班次',
        width: 140,
        value: 'ShiftName',
        //semicolonFormat: true,
        sortable: true
    },
    {
        text: '班组',
        width: 140,
        value: 'TeamName',
        //semicolonFormat: true,
        sortable: true
    },
    {
        text: '入库状态',
        width: 140,
        value: 'SendSapFlag',
        dictionary: true,
        isColor: true,
        sortable: true
    },
    {
        text: '扫描时间',
        width: 180,
        value: 'CreateDate',
        sortable: true
    }
];
export const packagigColums = [
    {
        text: '产线',
        width: 140,
        value: 'FullLineName',
        sortable: true
    },
    {
        text: '箱号',
        width: 140,
        value: 'BoxNumber',
        //semicolonFormat: true,
        sortable: true
    },
    {
        text: '产品编码',
        width: 140,
        value: 'MaterialCode',
        //semicolonFormat: true,
        sortable: true
    },
    {
        text: '数量(PCS)',
        width: 140,
        value: 'Quantity',
        //semicolonFormat: true,
        sortable: true
    },
    {
        text: '班次',
        width: 140,
        value: 'ShiftName',
        //semicolonFormat: true,
        sortable: true
    },
    {
        text: '班组',
        width: 140,
        value: 'TeamName',
        //semicolonFormat: true,
        sortable: true
    },
    {
        text: '扫描时间',
        width: 180,
        value: 'CreateDate',
        sortable: true
    }
];
export const vehiclePackagigColum = [
    {
        text: '产线',
        width: 140,
        value: 'FullLineName',
        sortable: true
    },
    {
        text: '箱号',
        width: 140,
        value: 'BoxNo',
        //semicolonFormat: true,
        sortable: true
    },
    {
        text: '产品编码',
        width: 140,
        value: 'MaterialCode',
        //semicolonFormat: true,
        sortable: true
    },
    {
        text: '产品编码',
        width: 140,
        value: 'MaterialDescription',
        //semicolonFormat: true,
        sortable: true
    },
    {
        text: '数量(PCS)',
        width: 140,
        value: 'InQty',
        //semicolonFormat: true,
        sortable: true
    },
    {
        text: '扫描时间',
        width: 180,
        value: 'InStorageTime',
        sortable: true
    }
];
export const packingStorageColumns = [
    {
        text: '产线',
        width: 120,
        value: 'FullLineName',
        sortable: true
    },
    {
        text: '物料编号',
        width: 120,
        value: 'MaterialCode',
        sortable: true
    },
    {
        text: '物料描述',
        width: 140,
        value: 'MaterialDescription',
        sortable: true
    },
    {
        text: '入库单号',
        width: 130,
        value: 'SheetNo',
        sortable: true
    },
    {
        text: '件数',
        width: 100,
        value: 'BoxCount',
        sortable: true
    },
    {
        text: '总数',
        width: 100,
        value: 'SumQuantity',
        sortable: true
    },
    {
        text: '入库日期',
        width: 160,
        value: 'BookDate',
        sortable: true
    },
    {
        text: '创建时间',
        width: 160,
        value: 'CreateDate',
        sortable: true
    },
    {
        text: '操作',
        width: 160,
        value: 'actions',
        sortable: true
    },
]
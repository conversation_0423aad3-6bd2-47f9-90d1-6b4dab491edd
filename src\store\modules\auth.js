import request from '@/util/request';
import colors from 'vuetify/es5/util/colors';
import { configUrl } from '@/config';
import md5 from 'js-md5';
import CryptoJS from 'crypto-js'

const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url

const randomColor = () => {
    const temp = Object.keys(colors);
    const key = temp[Math.floor(Math.random() * temp.length)];
    const color = colors[key].base;
    return color;
};

const state = {
    access_token: null,
    expires_in: 3600,
    token_type: 'bearer',
    username: 'admin',
    avatar: null,
    userColor: '#3dcd58',
    status: 'online',
    menuList: [],
    btnList: [],
    userinfolist: [],
    factoryInfo: []
};
const getters = {
    getAccessToken: state => state.access_token,
    getMenuList: state => state.menuList,
    getBtnList: state => state.btnList,
    getUserinfolist: state => state.userinfolist,
    getAvatar: state => state.avatar,
    getUsername: state => state.username,
    getUserStatus: state => state.status,
    getFactoryInfo: state => state.factoryInfo
};
const actions = {
    login({ commit, dispatch }, params) {
        let key = "M7WNkwb8NRPytd13"
        let iv = "6KcAHww40ydj4jxX"
        key = CryptoJS.enc.Utf8.parse(key)
        iv = CryptoJS.enc.Utf8.parse(iv);
        const encryptedContent = CryptoJS.AES.encrypt(params.password, key, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        })
        //const encStr = encryptedContent.ciphertext.toString()
        //console.log(encryptedContent.toString());
        //console.log(encStr);
        // const decryptedContent = CryptoJS.AES.decrypt(CryptoJS.format.Hex.parse(encStr), key, {
        //     iv: iv,
        //     mode: CryptoJS.mode.CBC,
        //     padding: CryptoJS.pad.Pkcs7
        // })
        // console.log('解密:', CryptoJS.enc.Utf8.stringify(decryptedContent));
        return request({
            url: baseURL + '/api/Login/Login',
            method: 'post',
            data: {
                name: params.username,
                pwd: md5(params.password),
                //txtpwd: params.password, 
                txtpwd: encryptedContent.toString(), 
                SSOToken: params.token,
                isEncryp: 1,
                isDomainUser: params.token ? 1 : 0
            }
        }).then(resp => {
            commit('SET_LOGIN_PROFILE', { username: params.username });
            commit('SET_LOGIN', resp.response);
            debugger;
            // 登录成功后获取权限和数据字典
            dispatch('dictionary/fetchAllDictionary', {}, {root: true});
            // return dispatch('getPermission');
        });
    },
    getPermission({ state, commit }) {
        return request({
            url: baseURL + '/api/Role/GetUserRolePerssionTree',
            method: 'post',
            data: {
                Name: state.username
            }
        }).then(resp => {
            commit('SET_PERMISSION', resp.response[0]);
        });
    },
    register({ commit, dispatch }, data) {
        return request({
            url: '/auth/register',
            method: 'post',
            data: data
        }).then(resp => {
            commit('SET_LOGIN', resp.response);
            return resp;
        });
    },
    logout({ commit, dispatch }) {
        commit('SET_ACCESS_TOKEN', null);
        // 登出时清除数据字典缓存
        commit('dictionary/CLEAR_DICTIONARY', null, {root: true});
    }
};
const mutations = {
    SET_MENUSLIST(state, munes) {
        state.menuList = munes;
    },
    SET_PERMISSION(state, payload) {
        state.menuList = payload.menusList;
        state.btnList = payload.btnList;
        state.userinfolist = payload.userinfolist;
    },
    SET_LOGIN(state, { token, expires_in, token_type, UserEquipments }) {
        state.access_token = token;
        state.expires_in = expires_in;
        state.token_type = token_type;
        state.factoryInfo = UserEquipments;
    },
    SET_ACCESS_TOKEN(state, token) {
        state.access_token = token;
    },
    // 角色
    SET_LOGIN_PROFILE(state, payload) {
        state.username = payload.username;
    },
    UPDATE_SELF_STATUS(state, status) {
        state.status = status;
    }
};

export default {
    namespace: true,
    state,
    getters,
    actions,
    mutations
};
<template>
    <v-row>
        <v-col cols="12">
            <v-autocomplete v-model="friends" dense :disabled="isUpdating" :items="people" outlined color="" :label="lable" item-text="Name" item-value="Code" return-object :multiple="multiple">
                <template #selection="data">
                    <div class="mx-2" v-bind="data.attrs" :input-value="data.selected" close @click="data.select" @click:close="remove(data.item)">
                        {{ data.item.Name }}
                    </div>
                </template>
                <template #item="data">
                    <template v-if="typeof data.item !== 'object'">
                        <v-list-item-content v-text="data.item"></v-list-item-content>
                    </template>
                    <template v-else>
                        <v-list-item-content>
                            <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                            <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                        </v-list-item-content>
                    </template>
                </template>
            </v-autocomplete>
        </v-col>
    </v-row>
</template>
<script>
import { StaffSiteGetList } from '@/api/peopleManagement/basicdata.js';
export default {
    props: {
        lable: {
            type: String,
            default: '员工名称'
        },
        multiple: {
            type: Boolean,
            default: false
        },
        friend: {
            type: null,
            default: null
        }
    },
    data() {
        return {
            autoUpdate: true,
            friends: [],
            isUpdating: false,
            people: []
        };
    },
    watch: {
        isUpdating(val) {
            if (val) {
                setTimeout(() => (this.isUpdating = false), 100);
            }
        },
        friend: {
            handler(a, b) {
                console.log(a, b + ' 人员 展示********');
                if (a && a.Name) {
                    this.friends = a;
                    console.log(this.friends, a, b + ' 人员 展示********');
                } else {
                    this.friends = [];
                }
            },
            deep: true,
            immediate: true
        }
    },
    created() {
        this.GetStaffSiteGetList();
    },
    methods: {
        remove(item) {
            const index = this.friends.indexOf(item.Code);
            if (index >= 0) this.friends.splice(index, 1);
        },
        // 获取人员列表
        async GetStaffSiteGetList() {
            const res = await StaffSiteGetList({ key: '' });
            let { success, response } = res;
            if (success) {
                this.people = response || [];
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.v-chip.v-size--default {
    height: 24px !important;
}
</style>

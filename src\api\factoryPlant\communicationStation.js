import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_30015'

// 列表
export function getInfluxOpcServerPageList(data) {
    const api = '/api/InfluxOpcServer/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

export function getInfluxOpcServerList(data) {
    const api = '/api/InfluxOpcServer/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 保存
export function influxOpcServerSaveForm(data) {
    const api = '/api/InfluxOpcServer/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}

// 删除
export function influxOpcServerDelete(data) {
    const api = '/api/InfluxOpcServer/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}


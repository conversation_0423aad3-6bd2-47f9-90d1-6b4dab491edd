<template>
    <v-dialog v-model="showDialog" max-width="1080px">
        <v-card v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" :sm="item.sm ? item.sm : 3" :md="item.sm ? item.sm : 3" v-for="(item, index) in SbxxList" :key="index">
                            <v-text-field v-if="item.type == 'input'" :id="item.id + 'SbxxList'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                            <v-autocomplete
                                v-if="item.type == 'select'"
                                :id="item.id + 'SbxxList'"
                                clearable
                                v-model="item.value"
                                :items="item.options"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="item.label"
                                clear
                                dense
                                outlined
                            ></v-autocomplete>
                            <v-menu
                                v-if="item.type == 'date' || item.type == 'datetime'"
                                :ref="'menu' + index"
                                v-model="menu[index]"
                                :close-on-content-click="false"
                                :nudge-right="40"
                                transition="scale-transition"
                                offset-y
                                max-width="290px"
                                min-width="290px"
                            >
                                <template #activator="{ on, attrs }">
                                    <v-text-field
                                        v-model="item.value"
                                        :clearable="item.isClearable ? item.isClearable : true"
                                        outlined
                                        dense
                                        :label="item.label"
                                        readonly
                                        v-bind="attrs"
                                        v-on="on"
                                    ></v-text-field>
                                </template>
                                <v-date-picker :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></v-date-picker>
                            </v-menu>
                            <div class="textfieldbox">
                                <v-text-field
                                    v-model="item.value"
                                    :clearable="item.isClearable ? item.isClearable : true"
                                    outlined
                                    dense
                                    v-if="item.type == 'time'"
                                    :label="item.label"
                                    readonly
                                ></v-text-field>
                                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-if="item.type == 'time'" v-model="item.value" type="datetime" :placeholder="item.label"></el-date-picker>
                            </div>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
export default {
    props: {
        dialogType: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            menu: [],
            SbxxList: [
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.bxsb'),
                    value: '',
                    id: 'bxsb',
                    sm: 6,
                    option: [],
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.gdlx'),
                    value: '',
                    id: 'gdlx',
                    sm: 6,
                    option: [],
                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.gdly'),
                    value: '',
                    id: 'gdly',
                    option: [],

                    sm: 6,

                    type: 'select'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.gzxx'),
                    value: '',
                    option: [],
                    sm: 6,

                    type: 'select',
                    id: 'gzxx'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.bxlr'),
                    value: '',
                    sm: 12,
                    id: 'bxlr',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.bxsj'),
                    value: '',
                    sm: 4,
                    id: 'bxsj',
                    type: 'time'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.qwkssj'),
                    value: '',
                    sm: 4,
                    id: 'qwkssj',
                    type: 'time'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.qwwcsj'),
                    value: '',
                    sm: 4,
                    id: 'qwwcsj',
                    type: 'time'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.jjd'),
                    value: '',
                    option: [],
                    sm: 4,
                    type: 'select',
                    id: 'jjd'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.dbzg'),
                    value: '',
                    option: [],
                    sm: 4,
                    type: 'select',
                    id: 'dbzg'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.wxzg'),
                    value: '',
                    option: [],
                    sm: 4,
                    type: 'select',
                    id: 'wxzg'
                }
            ]
        };
    },
    computed: {
        locale() {
            return this.$store.state.app.locale || 'zh';
        }
    },
    watch: {},
    methods: {
        closeEquip() {
            this.showDialog = false;
            // this.$refs.form.reset();
        },
        closeDatePicker(index) {
            this.$set(this.menu, index, false);
        }
    }
};
</script>
<style lang="scss">
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
    .el-date-editor {
        width: 100%;
        position: absolute;
        opacity: 0;
        top: 0;
        .el-input__inner {
            font-size: 16px;
            height: 32.5px;
            color: rgba(0, 0, 0, 0.87);
            border: 1px solid #9e9e9e;
        }
        .el-input__icon {
            line-height: 32.5px;
        }
    }
}
</style>

<style lang="scss" scoped>
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
    .textfieldbox {
        position: relative;
    }
}

.ant-calendar-picker {
    ::v-deep input {
        border-color: #9e9e9e;
    }

    ::v-deep i {
        color: #757575;
    }
}
</style>

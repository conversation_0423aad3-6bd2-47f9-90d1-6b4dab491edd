/* 拖拽时元素辅助线 */
.toplineOfPosition, .bottomlineOfPosition {
  border: 0;
  border-top: 1px dashed  rgb(169, 169, 169);
}
.leftlineOfPosition, .rightlineOfPosition {
  border: 0;
  border-left: 1px dashed  rgb(169, 169, 169);
}

/* 拖拽时的坐标位置 */
.topPosition {
  background: red;
  color: white;
  border-radius: 20rem;
  min-width: 10px;
  padding: 0 2px;
  font-size: 12px;
  line-height: normal;
  z-index: 9;
}
.topPosition-lineMode {
  color: red;
  background: unset;
  line-height: normal;
  z-index: 9;
}
.leftPosition {
  background: red;
  color: white;
  border-radius: 20rem;
  min-width: 10px;
  padding: 0 2px;
  font-size: 12px;
  line-height: normal;
  z-index: 9;
}
.leftPosition-lineMode {
  color: red;
  background: unset;
  line-height: normal;
  z-index: 9;
}

/* 元素始终隐藏 */
.alwaysHide {
  background-color: gray !important;
}

/* 元素宽高 */
.resize-panel .size-box {
  border: 1px solid;
  padding: 0px 4px;
  background: red;
  color: white;
  font-size: 12px;
  border-radius: 6px 6px 0 0;
  white-space: nowrap;
  line-height: normal;
  font-family: unset;
  letter-spacing: normal;
  z-index: 9;
}
.resize-panel .size-box .hide {
  display: none;
}

/* 参数tab */
.prop-tabs {
  background-color: #FFF;
  border-style: none;
  box-shadow: none;
  border-color: #e6e6e6;
}
.prop-tabs .prop-tab-items {
  height: 31px;
  line-height: 31px;
  padding: 2px 0 1px 2px;
  list-style: none;
  outline: 0;
  border: 0;
  text-decoration: none;
  font-size: 100%;
  margin: 0;
  border-bottom: 1px solid #ddd;
  /*box-shadow: 0 1px 3px rgba(26, 26, 26, .1);*/
  box-sizing: content-box;
}
.prop-tabs .prop-tab-items .prop-tab-item {
  background-color: #FFF;
  border-radius: 0;
  padding: 0;
  margin: 0 -1px 0 0;
  display: inline-block;
  cursor: pointer;
  list-style-type: none;
}

.prop-tabs .prop-tab-items .prop-tab-item .tab-title {
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
  font-weight: bold;
  font-size: 14px;
}

.prop-tabs .prop-tab-items li.active {
  border: none;
  border-bottom: 2px solid #2196f3;
  color: #2196f3;
  height: 31px;
  line-height: 30px;
}

.prop-tabs .hiprint-option-items.active {
  display: flex;
}

.prop-tabs .hiprint-option-items {
  display: none;
  margin-top: 1px;
  border: none;
  background-color: #FFF;
  overflow: auto;
  /*height: calc(100vh - 150px);*/
  padding: 0;
}
/* 元素双击编辑 */
.design .editing {
  border: 1px solid red !important;
}

/* hiprint-pagination */
.hiprint-pagination {
    display: inline-block;
    padding-left: 0;
}
    .hiprint-pagination > li {
        border: 1px solid #bdc3c7;
        -moz-border-radius: 2px;
        -webkit-border-radius: 2px;
        display: block;
        float: left;
        padding: 5px;
        text-decoration: none;
        margin-right: 5px;
        margin-bottom: 5px;
        font-family: helvetica;
        font-size: 13px;
        cursor: pointer
    }

        .hiprint-pagination > li > span {
            padding: 0 10px 0 10px;
        }

        .hiprint-pagination > li > a {
            color: #bdc3c7;
            font-weight: bold;
            text-decoration: none;
            font-size: 11px;
            padding: 3px;
        }

            .hiprint-pagination > li > a:hover {
                color: red;
            }



.hiprint-pagination-sm > li > a {
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
}
/*rect-printElement-type hiprint-printElement-type */
.rect-printElement-types .hiprint-printElement-type {
    display: block;
}

.rect-printElement-types .hiprint-printElement-type {
    padding: 0 0 0 0;
    list-style: none;
}

    .rect-printElement-types .hiprint-printElement-type > li > .title {
        display: block;
        padding: 4px 0px;
        clear: both;
    }

    .rect-printElement-types .hiprint-printElement-type > li > ul {
        padding: 0 0 0 0;
        display: block;
        list-style: none;
    }

        .rect-printElement-types .hiprint-printElement-type > li > ul > li {
            display: block;
            width: 50%;
            float: left;
            max-width: 100px;
        }

            .rect-printElement-types .hiprint-printElement-type > li > ul > li > a {
                height: 92px;
                padding: 12px 6px;
                margin-left: -1px;
                line-height: 1.42857143;
                color: #337ab7;
                text-decoration: none;
                background-color: #fff;
                border: 1px solid #ddd;
                margin-right: 5px;
                width: 95%;
                max-width: 100px;
                display: inline-block;
                text-align: center;
                margin-bottom: 7px;
                box-sizing: border-box;
                color: #b9a5a6;
                border: 1px solid rgba(0,0,0,0.2);
                border-radius: 3px;
                box-shadow: 0 1px 0 0 rgba(0,0,0,0.15);
            }


/*small-printElement-type hiprint-printElement-type */
.small-printElement-types .hiprint-printElement-type {
    display: block;
}

.small-printElement-types .hiprint-printElement-type {
    padding: 0 0 0 0;
    list-style: none;
}

    .small-printElement-types .hiprint-printElement-type > li > .title {
        display: block;
        padding: 4px 0px;
        clear: both;
    }

    .small-printElement-types .hiprint-printElement-type > li > ul {
        padding: 0 0 0 0;
        display: block;
        list-style: none;
        width: 100%;
    }

        .small-printElement-types .hiprint-printElement-type > li > ul > li {
            display: block;
            width: 50%;
            float: left;
            padding: 0 4px;
        }

            .small-printElement-types .hiprint-printElement-type > li > ul > li > a {
                height: 22px;
                /* padding: 12px 6px; */
                /* margin-left: -1px; */
                line-height: 20px;
                color: #337ab7;
                text-decoration: none;
                background-color: #fff;
                border: 1px solid #ddd;
                margin-right: 5px;
                width: 100%;
                display: block;
                text-align: center;
                margin-bottom: 7px;
                box-sizing: border-box;
                color: #b9a5a6;
                border: 1px solid rgba(0,0,0,0.2);
                border-radius: 3px;
                box-shadow: 0 1px 0 0 rgba(0,0,0,0.15);
            }


/* hiprint-toolbar*/

.hiprint-toolbar {
}

    .hiprint-toolbar > ul {
        padding: 0px;
        margin-bottom: 5px;
    }

        .hiprint-toolbar > ul > li {
            display: inline-block;
        }

            .hiprint-toolbar > ul > li > a {
                position: relative;
                float: left;
                padding: 3px 10px;
                margin-left: -1px;
                line-height: 1.42857143;
                color: #337ab7;
                text-decoration: none;
                background-color: #fff;
                border: 1px solid #ddd;
                margin-right: 4px;
                cursor: pointer;
            }


.hiprint-printElement-type .glyphicon-class {
    display: block;
    text-align: center;
    word-wrap: break-word;
    /*font-size: 0.65rem;
font-weight: normal;*/
    font-family: Helvetica, sans-serif;
}

.hiprint-printElement-type .glyphicon {
    margin-top: 5px;
    margin-bottom: 10px;
    font-size: 37px;
}


/*


*/

/*option css*/
/*option css*/
.hiprint-option-items {
    font-size: .75rem;
    padding: 10px 5px;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    box-sizing: border-box;
    width: 100%;
}

    .hiprint-option-items .hiprint-option-item {
        box-sizing: border-box;
        float: left;
        width: 50%;
        margin-bottom: 5px;
        padding: 0 5px;
    }

    .hiprint-option-items .hiprint-option-item-row {
        width: 100%;
    }

.hiprint-option-item-label {
    margin: 5px 5px 3px 0;
}

.hiprint-option-items .hiprint-option-item-field input, .hiprint-option-items .hiprint-option-item-field select, .hiprint-option-items .hiprint-option-item-field textarea {
    color: inherit;
    background-color: transparent;
    box-sizing: border-box;
    width: 100%;
    position: relative;
    padding: 3px;
    z-index: 1;
    border: 1px solid rgb(169, 169, 169);
    height: 19pt;
}

.hiprint-option-item-settingBtn {
    height: 19pt;
    line-height: 19pt;
    font-size: 12px;
    padding: 0 24px;
    background: #00c1de;
    border-color: transparent;
    color: #fff;
    display: inline-block;
    margin: 5px;
    font-weight: 400;
    border: 1px solid transparent;
    font-family: PingFangSC, helvetica neue, hiragino sans gb, arial, microsoft yahei ui, microsoft yahei, simsun, "sans-serif";
    vertical-align: middle;
    transition: .3s cubic-bezier(.4, 0, .2, 1);
    transform: translateZ(0);
}

.hiprint-option-item-deleteBtn {
    background: red;
}

.hiprint-option-items .minicolors {
    position: relative;
}

.hiprint-option-items .minicolors-sprite {
    background-image: url(./image/jquery.minicolors.png);
}

.hiprint-option-items .minicolors-swatch {
    position: absolute;
    vertical-align: middle;
    background-position: -80px 0;
    cursor: text;
    padding: 0;
    margin: 0;
    display: inline-block;
}

.hiprint-option-items .minicolors-swatch-color {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.hiprint-option-items .minicolors input[type=hidden] + .minicolors-swatch {
    width: 28px;
    position: static;
    cursor: pointer;
}

.hiprint-option-items .minicolors input[type=hidden][disabled] + .minicolors-swatch {
    cursor: default;
}

/* Panel */
.hiprint-option-items .minicolors-panel {
    position: absolute;
    width: 173px;
    background: white;
    border: solid 1px #CCC;
    box-shadow: 0 0 20px rgba(0, 0, 0, .2);
    z-index: 99999;
    box-sizing: content-box;
    display: none;
}

    .hiprint-option-items .minicolors-panel.minicolors-visible {
        display: block;
    }

/* Panel positioning */
.hiprint-option-items .minicolors-position-top .minicolors-panel {
    top: -154px;
}

.hiprint-option-items .minicolors-position-right .minicolors-panel {
    right: 0;
}

.hiprint-option-items .minicolors-position-bottom .minicolors-panel {
    top: auto;
}

.hiprint-option-items .minicolors-position-left .minicolors-panel {
    left: 0;
}

.hiprint-option-items .minicolors-with-opacity .minicolors-panel {
    width: 194px;
}

.hiprint-option-items .minicolors .minicolors-grid {
    position: relative;
    top: 1px;
    left: 1px; /* LTR */
    width: 150px;
    height: 150px;
    margin-bottom: 2px;
    background-position: -120px 0;
    cursor: crosshair;
}

.hiprint-option-items .minicolors .minicolors-grid-inner {
    position: absolute;
    top: 0;
    left: 0;
    width: 150px;
    height: 150px;
}

.hiprint-option-items .minicolors-slider-saturation .minicolors-grid {
    background-position: -420px 0;
}

.hiprint-option-items .minicolors-slider-saturation .minicolors-grid-inner {
    background-position: -270px 0;
    background-image: inherit;
}

.hiprint-option-items .minicolors-slider-brightness .minicolors-grid {
    background-position: -570px 0;
}

.hiprint-option-items .minicolors-slider-brightness .minicolors-grid-inner {
    background-color: black;
}

.hiprint-option-items .minicolors-slider-wheel .minicolors-grid {
    background-position: -720px 0;
}

.hiprint-option-items .minicolors-slider,
.hiprint-option-items .minicolors-opacity-slider {
    position: absolute;
    top: 1px;
    left: 152px; /* LTR */
    width: 20px;
    height: 150px;
    background-color: white;
    background-position: 0 0;
    cursor: row-resize;
}

.hiprint-option-items .minicolors-slider-saturation .minicolors-slider {
    background-position: -60px 0;
}

.hiprint-option-items .minicolors-slider-brightness .minicolors-slider {
    background-position: -20px 0;
}

.hiprint-option-items .minicolors-slider-wheel .minicolors-slider {
    background-position: -20px 0;
}

.hiprint-option-items .minicolors-opacity-slider {
    left: 173px; /* LTR */
    background-position: -40px 0;
    display: none;
}


.hiprint-option-items .minicolors-with-opacity .minicolors-opacity-slider {
    display: block;
}

/* Pickers */
.hiprint-option-items .minicolors-grid .minicolors-picker {
    position: absolute;
    top: 70px;
    left: 70px;
    width: 12px;
    height: 12px;
    border: solid 1px black;
    border-radius: 10px;
    margin-top: -6px;
    margin-left: -6px;
    background: none;
}

    .hiprint-option-items .minicolors-grid .minicolors-picker > div {
        position: absolute;
        top: 0;
        left: 0;
        width: 8px;
        height: 8px;
        border-radius: 8px;
        border: solid 2px white;
        box-sizing: content-box;
    }

.hiprint-option-items .minicolors-picker {
    position: absolute;
    top: 0;
    left: 0;
    width: 18px;
    height: 2px;
    background: white;
    border: solid 1px black;
    margin-top: -2px;
    box-sizing: content-box;
}

/* Swatches */
.hiprint-option-items .minicolors-swatches,
.hiprint-option-items .minicolors-swatches li {
    margin: 5px 0 3px 5px; /* LTR */
    padding: 0;
    list-style: none;
    overflow: hidden;
}

    .hiprint-option-items .minicolors-swatches .minicolors-swatch {
        position: relative;
        float: left; /* LTR */
        cursor: pointer;
        margin: 0 4px 0 0; /* LTR */
    }


.hiprint-option-items .minicolors-with-opacity .minicolors-swatches .minicolors-swatch {
    margin-right: 7px; /* LTR */
}


.hiprint-option-items .minicolors-swatch.selected {
    border-color: #000;
}

/* Inline controls */
.hiprint-option-items .minicolors-inline {
    display: inline-block;
}

    .hiprint-option-items .minicolors-inline .minicolors-input {
        display: none !important;
    }

    .hiprint-option-items .minicolors-inline .minicolors-panel {
        position: relative;
        top: auto;
        left: auto; /* LTR */
        box-shadow: none;
        z-index: auto;
        display: inline-block;
    }



/* Bootstrap theme */
.hiprint-option-items .minicolors-theme-bootstrap .minicolors-swatch {
    z-index: 2;
    top: 3px;
    left: 3px;
    width: 17px;
    height: 17px;
}

.hiprint-option-items .minicolors-theme-bootstrap .minicolors-swatches .minicolors-swatch {
    margin-bottom: 2px;
    top: 0;
    left: 0; /* LTR */
    width: 20px;
    height: 20px;
}

.hiprint-option-items .minicolors-theme-bootstrap .minicolors-swatch-color {
    border-radius: inherit;
}

.hiprint-option-items .minicolors-theme-bootstrap.minicolors-position-right > .minicolors-swatch {
    left: auto; /* LTR */
    right: 3px; /* LTR */
}

.hiprint-option-items .minicolors-theme-bootstrap .minicolors-input {
    float: none;
    padding-left: 23px; /* LTR */
}

.hiprint-option-items .minicolors-theme-bootstrap.minicolors-position-right .minicolors-input {
    padding-right: 44px; /* LTR */
    padding-left: 12px; /* LTR */
}

.hiprint-option-items .minicolors-theme-bootstrap .minicolors-input.input-lg + .minicolors-swatch {
    top: 4px;
    left: 4px; /* LTR */
    width: 37px;
    height: 37px;
    border-radius: 5px;
}

.hiprint-option-items .minicolors-theme-bootstrap .minicolors-input.input-sm + .minicolors-swatch {
    width: 24px;
    height: 24px;
}

.hiprint-option-items .minicolors-theme-bootstrap .minicolors-input.input-xs + .minicolors-swatch {
    width: 18px;
    height: 18px;
}

.hiprint-option-items .input-group .minicolors-theme-bootstrap:not(:first-child) .minicolors-input {
    border-top-left-radius: 0; /* LTR */
    border-bottom-left-radius: 0; /* LTR */
}



/*hitable reizer*/
.hitable {
}



    .hitable .selected {
        background: #3e66ad;
    }


    /*resizer*/
    .hitable tr.resizerRow,
    .hitable .resizerRow td {
        border: 0pt dashed;
        height: 0pt;
        background: #fff;
    }

        .hitable tr.resizerRow + tr,
        .hitable tr.resizerRow + tr td {
            border-top: 0px !important;
        }

    .hitable td.resizerColumn {
        border: 0pt dashed;
        width: 0.000001px !important;
        background: #fff;
    }


        .hitable td.resizerColumn + td {
            border-left: 0px !important;
        }


/*GRIP*/

.columngrips {
    height: 0px;
    position: absolute;
}

.columngrip {
    margin-left: -5px;
    position: absolute;
    z-index: 5;
    width: 10px;
}

    .columngrip .gripResizer {
        position: absolute;
        filter: alpha(opacity=1);
        opacity: 0;
        width: 10px;
        height: 100%;
        cursor: col-resize;
        top: 0px;
    }

.columngripDraging {
    border-left: 1px dotted black;
}

.rowgrips {
    height: 0px;
    width: 0px;
    position: absolute;
}

.rowgrip {
    margin-top: -5px;
    position: absolute;
    z-index: 5;
    height: 10px;
}

    .rowgrip .gripResizer {
        position: absolute;
        filter: alpha(opacity=1);
        opacity: 0;
        height: 10px;
        width: 100%;
        cursor: row-resize;
        left: 0px;
    }

.rowgripDraging {
    border-top: 1px dotted black;
}

.hitable .hitable-editor-text {
    border: 1px solid;
    width: 95%;
    height: 80%;
}




.hipanel-disable {
    height: 0px;
    display: block !important;
    top: 8500px;
    width: 0px;
    overflow: hidden;
    position: absolute;
}

.hiprint_rul_wrapper {
    position: absolute;
    height: 100%;
    width: 100%;
    overflow: hidden;
    pointer-events: none;
    border: 0;
    border-top: 1px solid rgb(201, 190, 190);
    border-left: 1px solid rgb(201, 190, 190);
    padding-left: 15px;
	padding-top:15px;
    margin: -16px;
	box-sizing: content-box!important;
}

    .hiprint_rul_wrapper .h_img {
        position: absolute;
        top: 0px;
        left: 15px;
        width: 400mm;
        height: 15px;
    }

    .hiprint_rul_wrapper .v_img {
        width: 400mm;
        transform: rotate(90deg);
        transform-origin: 0 100%;
        height: 15px;
        position: absolute;
        top: -2px;
        left: 0px;
    }

/*hiprint-option-table*/

.hiprint-option-table-selected-columns {
    color: inherit;
    background-color: transparent;
    box-sizing: border-box;
    width: 100%;
    position: relative;
    padding: 0px;
    list-style: none;
}

    .hiprint-option-table-selected-columns .hiprint-option-table-selected-item {
        color: inherit;
        background-color: transparent;
        box-sizing: border-box;
        width: 100%;
        padding: 0 3px;
        border: 1px solid rgb(169, 169, 169);
        line-height: 19pt;
        margin: 3px 0;
    }
    .hiprint-option-table-selected-columns .hiprint-option-table-selected-item .column-title {
      display: inline-block;
      min-width: calc(100% - 26px);
    }
/*hi-pretty */
.hi-pretty * {
    box-sizing: border-box;
}

.hi-pretty input:not([type='checkbox']):not([type='radio']) {
    display: none;
}

.hi-pretty {
    position: relative;
    display: inline-block;
    margin-right: 1em;
    white-space: nowrap;
    line-height: 1;
}

    .hi-pretty input {
        position: absolute;
        left: 0;
        top: 0;
        min-width: 1em;
        width: 100%;
        height: 100%;
        z-index: 2;
        opacity: 0;
        margin: 0;
        padding: 0;
        cursor: pointer;
    }

    .hi-pretty .state label {
        position: initial;
        display: inline-block;
        font-weight: normal;
        margin: 0;
        text-indent: 1.5em;
        min-width: calc(1em + 2px);
    }

        .hi-pretty .state label:before,
        .hi-pretty .state label:after {
            content: '';
            width: calc(1em + 2px);
            height: calc(1em + 2px);
            display: block;
            box-sizing: border-box;
            border-radius: 0;
            border: 1px solid transparent;
            z-index: 0;
            position: absolute;
            left: 0;
            top: 0;
            background-color: transparent;
        }

        .hi-pretty .state label:before {
            border-color: #bdc3c7;
        }

    .hi-pretty .state.p-is-hover,
    .hi-pretty .state.p-is-indeterminate {
        display: none;
    }


    .hi-pretty.p-default.p-fill .state label:after {
        -webkit-transform: scale(1);
        -ms-transform: scale(1);
        transform: scale(1);
    }

    .hi-pretty.p-default .state label:after {
        -webkit-transform: scale(0.6);
        -ms-transform: scale(0.6);
        transform: scale(0.6);
    }

    .hi-pretty.p-default input:checked ~ .state label:after {
        background-color: #bdc3c7 !important;
    }

    .hi-pretty.p-default.p-thick .state label:before,
    .hi-pretty.p-default.p-thick .state label:after {
        border-width: calc(1em / 7);
    }

    .hi-pretty.p-default.p-thick .state label:after {
        -webkit-transform: scale(0.4) !important;
        -ms-transform: scale(0.4) !important;
        transform: scale(0.4) !important;
    }

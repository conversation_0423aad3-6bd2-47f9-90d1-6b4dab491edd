import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_30015'
const andonURL = 'baseURL_ANDON'
//产线指示灯
export function getEquipmentStatus(data) {
    const api = '/api/Kpitgt/EquipmentStatus'
    return getRequestResources(baseURL, api, 'post', data);
}
//计划执行信息
export function getProOrderInfo(data) {
    const api = '/api/Kpitgt/GetProOrderInfo'
    return getRequestResources(baseURL, api, 'post', data);
}
//流程等待时间
export function getProcessWaiting(data) {
    const api = '/api/Kpitgt/ProcessWaiting'
    return getRequestResources(baseURL, api, 'post', data);
}
//当日备料、煮料进度对照图
export function getMaterialProgress(data) {
    const api = '/api/Kpitgt/MaterialProgress'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取KPI通用接口
export function getKPIData(data) {
    const api = '/api/Kpitgt/GetKpiData'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取KPIList通用接口
export function getKPIDataList(data) {
    const api = '/api/Kpitgt/GetKPISeries'
    return getRequestResources(baseURL, api, 'post', data);
}
//物料组损耗
export function getMaterialGroupLoss(data) {
    const api = '/api/Kpitgt/MaterialGroupLoss'
    return getRequestResources(baseURL, api, 'post', data);
}
//订单产量状态
export function getRisingSunPictureSerie(data) {
    const api = '/api/Kpitgt/GetRisingSunPictureSeries'
    return getRequestResources(baseURL, api, 'post', data);
}
//sim3包装指示灯
export function getEquipmentStatusHouur(data) {
    const api = '/api/Kpitgt/EquipmentStatusHouur'
    return getRequestResources(baseURL, api, 'post', data);
}
//sim安灯
export function getAlarmStatisticsInfo(data) {
    const api = '/andon/AlarmRecord/GetAlarmStatisticsInfo'
    return getRequestResources(andonURL, api, 'get', data);
}


<template>
    <v-dialog v-model="showDialog" max-width="1080px">
        <v-card v-if="dialogType == 'jyrw'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('TPM_SBGL_JLQJGL.jyrw') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" :sm="item.sm ? item.sm : 3" :md="item.sm ? item.sm : 3" v-for="(item, index) in jyrwList" :key="index">
                            <v-text-field v-if="item.type == 'input'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                            <v-autocomplete
                                v-if="item.type == 'select'"
                                clearable
                                :multiple="item.multiple"
                                v-model="item.value"
                                :items="item.options"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="item.label"
                                clear
                                dense
                                outlined
                            ></v-autocomplete>
                            <v-menu
                                v-if="item.type == 'date' || item.type == 'datetime'"
                                :ref="'menu' + index"
                                v-model="menu[index]"
                                :close-on-content-click="false"
                                :nudge-right="40"
                                transition="scale-transition"
                                offset-y
                                max-width="290px"
                                min-width="290px"
                            >
                                <template #activator="{ on, attrs }">
                                    <v-text-field
                                        v-model="item.value"
                                        :clearable="item.isClearable ? item.isClearable : true"
                                        outlined
                                        dense
                                        :label="item.label"
                                        readonly
                                        v-bind="attrs"
                                        v-on="on"
                                    ></v-text-field>
                                </template>
                                <v-date-picker :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></v-date-picker>
                            </v-menu>
                            <div class="textfieldbox">
                                <v-text-field
                                    v-model="item.value"
                                    :clearable="item.isClearable ? item.isClearable : true"
                                    outlined
                                    dense
                                    v-if="item.type == 'time'"
                                    :label="item.label"
                                    readonly
                                ></v-text-field>
                                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-if="item.type == 'time'" v-model="item.value" type="datetime" :placeholder="item.label"></el-date-picker>
                            </div>
                            <div class="textfieldbox">
                                <v-text-field
                                    v-model="item.value"
                                    :clearable="item.isClearable ? item.isClearable : true"
                                    outlined
                                    dense
                                    v-if="item.type == 'daterange'"
                                    :label="item.label"
                                    readonly
                                ></v-text-field>
                                <el-date-picker
                                    v-model="item.value"
                                    value-format="yyyy-MM-dd"
                                    format="yyyy-MM-dd"
                                    v-if="item.type == 'daterange'"
                                    type="daterange"
                                    range-separator="至"
                                    :start-placeholder="$t('DFM_RL.DFM_RL')"
                                    end-placeholder="$t('DFM_RL._JSRQ')"
                                ></el-date-picker>
                            </div>

                            <el-radio-group v-model="item.value" v-if="item.type == 'radio'">
                                <div class="textlabel">{{ item.label }}:</div>
                                <el-radio :label="it.value" :key="ind" v-for="(it, ind) in item.radiolist">{{ it.label }}</el-radio>
                            </el-radio-group>
                        </v-col>
                        <!-- <v-col class="py-0 px-3" cols="12" sm="3" md="3">
                            <el-upload class="upload-demo" action="" :on-change="FileChange" :on-remove="FileRemove" multiple :limit="1" :file-list="FileList">
                                <v-btn color="primary">{{ $t('$vuetify.dataTable.TPM_SBGL_WDBX.sctp/sp') }}</v-btn>
                            </el-upload>
                        </v-col> -->
                        <!-- <v-col class="py-0 px-3" cols="12" :sm="6" :md="6">
                            <div class="textlabel">{{ $t('$vuetify.dataTable.TPM_SBGL_FWDGL.spjg') }}:</div>
                            <el-radio v-model="spjg" label="1">{{ $t('GLOBAL._TG') }}</el-radio>
                            <el-radio v-model="spjg" label="0">{{ $t('GLOBAL._BH') }}</el-radio>
                        </v-col> -->
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { GetMeasureAccountCreateMeasureCalibrateWo } from '@/api/equipmentManagement/instrument.js';
import { Message, MessageBox } from 'element-ui';
import { fwcgdownColum } from '@/columns/equipmentManagement/Repair.js';

export default {
    components: {},
    props: {
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        },
        VerifyCategory: {
            type: Array,
            default: () => []
        },
        VerifyMethod: {
            type: Array,
            default: () => []
        },
        MeasureType: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            fwcgdownColum,
            loading2: false,
            desserts2: [],
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            spjg: '',
            menu: [],
            FileList: [],
            jyrwList: [
                {
                    require: true,
                    label: this.$t('TPM_SBGL_JLQJGL.jdff'),
                    value: '',
                    sm: 6,
                    id: 'VerifyMethod',
                    type: 'select',
                    options: this.VerifyMethod
                },
                {
                    require: true,
                    label: this.$t('TPM_SBGL_JLQJGL.abcfl'),
                    value: '',
                    sm: 6,
                    id: 'Type',
                    type: 'select',
                    options: this.MeasureType
                },
                {
                    require: true,
                    label: this.$t('TPM_SBGL_JLQJGL.lb'),
                    value: '',
                    sm: 6,
                    id: 'Category',
                    type: 'select',
                    options: this.VerifyCategory
                },
                {
                    require: true,
                    label: this.$t('TPM_SBGL_JLQJGL.yxqfw'),
                    value: [],
                    sm: 6,
                    id: 'MeasureRange',
                    type: 'daterange'
                },
                {
                    require: true,
                    label: this.$t('TPM_SBGL_JLQJGL.zzwcrq'),
                    value: '',
                    sm: 6,
                    id: 'LastFinishDate',
                    type: 'date'
                },
                {
                    require: true,
                    label: this.$t('TPM_SBGL_JLQJGL.zrr'),
                    value: '',
                    sm: 6,
                    multiple: true,
                    id: 'VerifyBy',
                    type: 'select',
                    options: []
                }
            ]
        };
    },
    computed: {
        locale() {
            return this.$store.state.app.locale || 'zh';
        }
    },
    async mounted() {
        this.jyrwList.forEach(item => {
            if (item.require) {
                item.label = item.label + ' *';
            }
        });
    },
    watch: {},
    methods: {
        async addSave() {
            let flag = this.jyrwList.some(item => {
                if (item.require) {
                    return item.value == '' || item.value == null;
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            let params = {};
            this.jyrwList.forEach(item => {
                if (item.multiple) {
                    params[item.id] = item.value.join('|');
                } else {
                    params[item.id] = item.value;
                }
            });
            params.ExpirationDateStart = this.jyrwList[3].value[0];
            params.ExpirationDateEnd = this.jyrwList[3].value[1];
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            let res = await GetMeasureAccountCreateMeasureCalibrateWo(params);
            let { success, msg } = res;
            if (success) {
                this.jyrwList.forEach(item => {
                    item.value = '';
                    if (item.id == '') {
                        item.value = [];
                    }
                });
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$emit('loadData');
                this.showDialog = this.classcheckbox ? false : true;
            }
        },
        formatDate(d, d2) {
            var date = new Date(d);
            var YY = date.getFullYear() + '-';
            var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
            var DD = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
            let str1 = YY + MM + DD;
            var date2 = new Date(d2);
            var YY2 = date2.getFullYear() + '-';
            var MM2 = (date2.getMonth() + 1 < 10 ? '0' + (date2.getMonth() + 1) : date2.getMonth() + 1) + '-';
            var DD2 = date2.getDate() < 10 ? '0' + date2.getDate() : date2.getDate();
            let str2 = YY2 + MM2 + DD2;
            return [str1, str2];
        },
        closeEquip() {
            this.showDialog = false;
            // this.$refs.form.reset();
        },
        closeDatePicker(index) {
            this.$set(this.menu, index, false);
        }
    }
};
</script>
<style lang="scss">
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
    .textlabel {
        display: inline-flex;
        font-size: 16px;
        margin-right: 25px;
    }
    .el-radio-group {
        height: 40px;
        margin-top: 10px;
    }
    .el-radio__input.is-checked + .el-radio__label {
        color: #3dcd58;
    }
    .el-radio__input.is-checked .el-radio__inner {
        border-color: #3dcd58;
        background: #3dcd58;
    }
    .el-radio__label {
        font-size: 16px;
    }
    .el-date-editor {
        width: 100%;
        position: absolute;
        opacity: 0;
        top: 0;
        .el-input__inner {
            font-size: 16px;
            height: 32.5px;
            color: rgba(0, 0, 0, 0.87);
            border: 1px solid #9e9e9e;
        }
        .el-input__icon {
            line-height: 32.5px;
        }
    }
}
</style>

<style lang="scss" scoped>
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
    .textfieldbox {
        position: relative;
    }
}

.ant-calendar-picker {
    ::v-deep input {
        border-color: #9e9e9e;
    }

    ::v-deep i {
        color: #757575;
    }
}
</style>

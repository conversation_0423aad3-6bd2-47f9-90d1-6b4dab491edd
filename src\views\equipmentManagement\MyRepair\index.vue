<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <!-- @click="getTableData" -->
                    <v-btn icon color="primary" @click="getTableData">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'SBGLZY_ADD'" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="primary" v-has="'SBGLZY_DC'" @click="handleExport">{{ $t('TPM_SBGL_WDBX._DC') }}</v-btn>
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_WDBX"
                    :headers="MyRepairColum"
                    :desserts="desserts"
                    @selectePages="selectePages"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"
                ></Tables>
                <createRepast ref="createRepast" :DevList="DevList" :SourceList="SourceList" :dialogType="dialogType" :TypeList="TypeList" :tableItem="tableItem"></createRepast>
            </v-card>
        </div>
        <el-dialog :title="addTitle" :visible.sync="addModel" width="30%">
            <div class="addForm">
                <v-text-field v-model="user" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.Creator') + '*'"></v-text-field>
            </div>
            <div class="addForm">
                <v-text-field v-model="userDate" :clearable="true" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.CreateDate') + '*'" readonly></v-text-field>
                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="userDate" type="datetime" :placeholder="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.CreateDate') + '*'"></el-date-picker>
            </div>
            <div class="addForm" v-if="dialogType == 'qx'">
                <v-text-field v-model="reason" outlined dense :label="$t('GLOBAL._REASON') + '*'"></v-text-field>
            </div>
            <div class="addForm" v-if="dialogType == 'qr'">
                <v-autocomplete
                    clearable
                    v-model="ConfirmResult"
                    :items="pjList"
                    item-text="ItemName"
                    item-value="ItemValue"
                    :label="$t('TPM_SBGL_WDBX.ConfirmResult') + '*'"
                    clear
                    dense
                    outlined
                ></v-autocomplete>
                <!-- <v-text-field v-model="ConfirmResult" type="number" outlined dense :label="$t('TPM_SBGL_WDBX.ConfirmResult') + '*'"></v-text-field> -->
            </div>
            <div class="addForm" v-if="dialogType == 'qr'">
                <v-text-field v-model="ConfirmComment" outlined dense :label="$t('TPM_SBGL_WDBX.ConfirmComment') + '*'"></v-text-field>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addModel = false">取 消</el-button>
                <el-button type="primary" @click="Save()">确 定</el-button>
            </span>
        </el-dialog>
        <div class="loading-box" v-if="importLoading">
            <a-spin tip="导入中..." :spinning="importLoading"></a-spin>
        </div>
        <el-dialog :title="$t('TPM_SBGL_WDGD._WXJL')" id="FlexForm" :visible.sync="RepairRecordModel" width="45%">
            <div class="addForm FlexAddForm" v-for="(item, index) in wxjlList" :key="index">
                <v-text-field
                    disabled
                    v-if="item.type == 'number'"
                    :id="item.id + 'SbxxList'"
                    onkeyup="value=value.replace(/\D/g,'')"
                    type="text"
                    v-model="item.value"
                    outlined
                    dense
                    :label="item.label"
                ></v-text-field>
                <!-- RepairRecordDesc -->
                <v-text-field disabled v-if="item.type == 'RepairRecordDesc'" :id="item.id + 'SbxxList'" v-model="item.value" outlined dense :label="item.label"></v-text-field>

                <v-text-field disabled v-if="item.type == 'input'" :id="item.id + 'SbxxList'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                <v-autocomplete
                    v-if="item.type == 'select'"
                    :id="item.id + 'SbxxList'"
                    clearable
                    v-model="item.value"
                    :items="item.option"
                    item-text="ItemName"
                    item-value="ItemValue"
                    :label="item.label"
                    clear
                    dense
                    disabled
                    outlined
                ></v-autocomplete>
                <v-menu
                    v-if="item.type == 'date' || item.type == 'datetime'"
                    :ref="'menu' + index"
                    v-model="menu[index]"
                    :close-on-content-click="false"
                    :nudge-right="40"
                    transition="scale-transition"
                    offset-y
                    max-width="290px"
                    min-width="290px"
                >
                    <template #activator="{ on, attrs }">
                        <v-text-field
                            disabled
                            v-model="item.value"
                            :clearable="item.isClearable ? item.isClearable : true"
                            outlined
                            dense
                            :label="item.label"
                            readonly
                            v-bind="attrs"
                            v-on="on"
                        ></v-text-field>
                    </template>
                    <el-date-picker :disabled="true" :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></el-date-picker>
                </v-menu>
                <div class="textfieldbox">
                    <v-text-field
                        :disabled="true"
                        v-model="item.value"
                        :clearable="item.isClearable ? item.isClearable : true"
                        outlined
                        dense
                        v-if="item.type == 'time'"
                        :label="item.label"
                        readonly
                    ></v-text-field>
                    <el-date-picker :disabled="true" value-format="yyyy-MM-dd HH:mm:ss" v-if="item.type == 'time'" v-model="item.value" type="datetime" :placeholder="item.label"></el-date-picker>
                </div>
                <el-radio-group :disabled="true" v-model="item.value" v-if="item.type == 'radio'">
                    <div class="textlabel">{{ item.label }}:</div>
                    <el-radio :label="it.value" :key="ind" v-for="(it, ind) in item.radiolist">{{ it.label }}</el-radio>
                </el-radio-group>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="RepairRecordModel = false">取 消</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';
import {
    GetRepairOrderStatus,
    GetRepairOrderType,
    GetRepairOrderSource,
    GetRepairOrderPageList,
    GetRepairOrderExportData,
    GetRepairOrderDelete,
    GetRepairOrderCancel,
    GetRepairOrderConfirm,
    GetRepairRecordListByWoId,
    GetDevicePageList
} from '@/api/equipmentManagement/MyRepair.js';
import { MyRepairColum } from '@/columns/equipmentManagement/MyRepair.js';
import { configUrl } from '@/config';
import moment from 'moment';
import { Message, MessageBox } from 'element-ui';
import { GetExportData, GetPersonList } from '@/api/equipmentManagement/Equip.js';
import equipment from '@/mixins/equipment';
export default {
    name: 'RepastModel',
    components: {
        createRepast: () => import('./components/createRepast.vue')
    },
    mixins: [equipment],
    data() {
        return {
            // tree 字典数据
            loading: false,
            showFrom: false,
            papamstree: {
                Source: '',
                Type: '',
                Status: '',
                ReportDateFrom: '',
                ReportDateTo: '',
                DeviceCode: '',
                DeviceName: '',
                pageIndex: 1,
                pageSize: 20,
                orderByFileds: 'REPORT_DATE DESC'
            },
            //查询条件
            RepairRecordModel: false,
            wxjlList: [
                {
                    label: this.$t('TPM_SBGL_WDGD.kscl') + ' *',
                    type: 'time',
                    require: true,
                    id: 'RecordStartDate',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.ksjs') + ' *',
                    type: 'time',
                    require: true,
                    id: 'RecordFinishDate',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.wxzgss'),
                    type: 'number',
                    id: 'RepairDuration',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.wxgcms') + ' *',
                    type: 'RepairRecordDesc',
                    id: 'RepairRecordDesc',
                    require: true,
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.yyfx'),
                    type: 'input',
                    id: 'Reason',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.yyfxjl'),
                    type: 'input',
                    id: 'ReasonResult',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.wxxz') + ' *',
                    type: 'select',
                    option: [],
                    require: true,
                    id: 'RepairNature',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.gzxz') + ' *',
                    require: true,
                    type: 'select',
                    option: [],
                    id: 'FaultNature',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.gzbw') + ' *',
                    require: true,
                    type: 'select',
                    option: [],
                    id: 'FaultCategory',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.wxsyby') + ' *',
                    require: true,
                    type: 'radio',
                    radiolist: [
                        {
                            label: '是',
                            value: '是'
                        },
                        {
                            label: '否',
                            value: '否'
                        }
                    ],
                    id: 'IsMaintained',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.zwwxjy') + ' *',
                    require: true,
                    type: 'radio',
                    radiolist: [
                        {
                            label: '是',
                            value: true
                        },
                        {
                            label: '否',
                            value: false
                        }
                    ],
                    id: 'IsExperience',
                    value: ''
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_WDGD.gzxx') + ' *',
                    require: true,
                    type: 'input',
                    id: 'Phenomenon',
                    value: ''
                }
            ],
            MyRepairColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            StatusList: [],
            TypeList: [],
            SourceList: [],
            DevList: [],
            user: this.$store.getters.getUserinfolist[0].LoginName,
            userDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
            addModel: false,
            reason: '',
            ConfirmResult: null,
            ConfirmComment: '',
            addTitle: '',
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            hasChildren: {}, // 新增字典详情判断-子节点才能新增
            RepairMngData: [],
            ShifMngData: [],
            pjList: [],
            //
            mcCyclelist: [] // 维修周期
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('TPM_SBGL_WDGD._WXJL'),
                    code: 'wxjl',
                    showList: ['进行中'],
                    showKey: 'Status',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_WDGD_WXJL'
                },
                {
                    text: this.$t('GLOBAL._CheckFile'),
                    code: 'ck',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_CKWJ'
                },
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    showList: ['待派单', '已派单', '已取消', '已驳回'],
                    showKey: 'Status',
                    type: 'red',
                    icon: '',
                    authCode: 'SBGLZY_DELETE'
                },

                {
                    text: this.$t('GLOBAL._QX'),
                    showList: ['待派单', '已派单'],
                    showKey: 'Status',
                    code: 'qx',
                    type: 'red',
                    icon: '',
                    authCode: 'SBGLZY_QX'
                },
                {
                    text: this.$t('GLOBAL._QR'),
                    code: 'qr',
                    showList: ['已维修'],
                    showKey: 'Status',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_QR'
                }
            ];
        },
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'Source',
                    label: this.$t('TPM_SBGL_WDBX._GDLY'),
                    icon: 'mdi-account-check',
                    selectData: this.SourceList,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Status',
                    label: this.$t('TPM_SBGL_WDBX._ZT'),
                    icon: 'mdi-account-check',
                    selectData: this.StatusList,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Type',
                    label: this.$t('TPM_SBGL_WDBX._GDLX'),
                    icon: 'mdi-account-check',
                    selectData: this.TypeList,
                    type: 'select',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'DeviceName',
                    label: this.$t('TPM_SBGL_WDBX._SBMC'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'DeviceCode',
                    label: this.$t('TPM_SBGL_WDBX._SBBH'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'ReportDateFrom',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_WDBX._BXKSRQ'),
                    placeholder: this.$t('TPM_SBGL_WDBX._BXKSRQ')
                },
                {
                    value: '',
                    key: 'ReportDateTo',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_WDBX._BXJSRQ'),
                    placeholder: this.$t('TPM_SBGL_WDBX._BXJSRQ')
                }
            ];
        }
    },
    async mounted() {
        let RepairMng = await GetPersonList('RepairMng');
        this.RepairMngData = RepairMng.response[0].ChildNodes;
        this.RepairMngData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        let ShiftMng = await GetPersonList('ShiftMng');
        this.ShifMngData = ShiftMng.response[0].ChildNodes;
        this.ShifMngData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.SourceList = await this.$getNewDataDictionary('RepairSource');
        this.StatusList = await this.$getNewDataDictionary('RepairOrderStatus');
        this.TypeList = await this.$getNewDataDictionary('RepairOrderType');
        let RepairPhenomenon = await this.$getNewDataDictionary('RepairPhenomenon');
        let RepairUrgency = await this.$getNewDataDictionary('RepairUrgency');
        this.pjList = await this.$getNewDataDictionary('Score');
        this.MyGetDevicePageList();
        this.getTableData();
        let FaultPosition = await this.$getNewDataDictionary('FaultPosition');
        let FaultProperty = await this.$getNewDataDictionary('FaultProperty');
        let RepairProperty = await this.$getNewDataDictionary('Repair Property');
        this.wxjlList.forEach(item => {
            switch (item.id) {
                case 'FaultCategory':
                    item.option = FaultPosition;
                    break;
                case 'FaultNature':
                    item.option = FaultProperty;
                    break;
                case 'RepairNature':
                    item.option = RepairProperty;
                    break;
            }
        });
        this.$refs.createRepast.SbxxList.forEach(item => {
            switch (item.id) {
                case 'Type':
                    item.option = this.TypeList;
                    break;
                case 'Phenomenon':
                    item.option = RepairPhenomenon;
                    break;
                case 'Urgency':
                    item.option = RepairUrgency;
                    break;
                case 'DutyManager':
                    item.option = this.ShifMngData;
                    break;
                case 'RepairManager':
                    item.option = this.RepairMngData;
                    break;
            }
        });
        // console.log(this.$refs.createRepast.SbxxList);
    },
    methods: {
        async MyGetDevicePageList() {
            let res = await GetDevicePageList();
            this.DevList = res.response;
            this.DevList.forEach(item => {
                item.ItemName = item.LineCode + ':' + item.Name + ':' + item.AssetsNo+ ':' + item.Code;
                item.ItemValue = item.ID + '|' + item.Code + '|' + item.LineCode;
            });
            this.$refs.createRepast.SbxxList[0].option = this.DevList;
        },
        async handleExport() {
            let params = {
                ...this.papamstree
            };
            const baseUrl3 = configUrl[process.env.VUE_APP_SERVE]['baseURL_EQUIPMENT'] + `/api/RepairOrder/ExportData`;
            let res = await GetExportData(baseUrl3, params);
            let binaryData = [];
            binaryData.push(res);
            const url = window.URL.createObjectURL(new Blob(binaryData));
            console.log(url);
            const link = document.createElement('a');
            link.href = url;
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            let fileName = `我的报修${formattedDateTime}.xlsx`;
            document.body.appendChild(link);
            link.setAttribute('download', fileName);
            link.click();
            window.URL.revokeObjectURL(link.href);
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.getTableData();
        },
        async getTableData() {
            let params = {
                ...this.papamstree
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading = true;
            const res = await GetRepairOrderPageList(params);
            let { success, response } = res;
            for (let k in response.data) {
                let item = response.data[k];
                item.ReportByName = await this.$getPerson(item.ReportBy);
                this.StatusList.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                let str = '';
                this.ShifMngData.forEach(it => {
                    if (item.DutyManager.indexOf('|') != -1) {
                        let arr = item.DutyManager.split('|');
                        arr.forEach(k => {
                            console.log(k, 1);
                            console.log(it.ItemValue, 2);
                            if (k == it.ItemValue) {
                                console.log(it.ItemName, 3);
                                str += it.ItemName + ' ';
                            }
                        });
                        item.DutyManagerName = str;
                        item.DutyManagerValue = arr.join('|');
                    } else {
                        if (item.DutyManager == it.ItemValue) {
                            item.DutyManagerName = it.ItemName;
                            item.DutyManagerValue = it.ItemValue;
                        }
                    }
                });
                this.RepairMngData.forEach(it => {
                    if (item.RepairManager == it.ItemValue) {
                        item.RepairManager = it.ItemName;
                        item.RepairManagerValue = it.ItemValue;
                    }
                });
            }
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.createRepast.SbxxList.forEach(item => {
                        item.value = '';
                        item.disabled = false;
                        if (item.id == 'Source') {
                            item.value = '报修';
                            item.disabled = true;
                        }
                        if (item.id == 'ReportDate') {
                            item.value = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
                        }
                    });
                    this.$refs.createRepast.FilePath = '';
                    this.$refs.createRepast.FileName = '';
                    this.$refs.createRepast.showDialog = true;
                    this.$refs.createRepast.clearFiles();

                    return;
            }
        },
        async getWXJLDetail() {
            let params = {
                RepairWo: this.tableItem.RepairWo
            };
            let res = await GetRepairRecordListByWoId(params);
            if (res.response.length != 0) {
                let obj = res.response[0];
                for (let k in obj) {
                    this.wxjlList.forEach(item => {
                        if (item.id == k) {
                            if (k == 'IsExperience') {
                                item.value = Boolean(obj[k]);
                            } else [(item.value = obj[k])];
                        }
                    });
                }
                this.RepairRecordModel = true;
            } else {
                Message({
                    message: `${this.$t('GLOBAL.NoFile')}`,
                    type: 'warning'
                });
                return false;
            }
            console.log(res, 123);
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            this.user = this.$store.getters.getUserinfolist[0].LoginName;
            this.userDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.reason = '';
            this.ConfirmComment = '';
            this.ConfirmResult = null;
            switch (type) {
                case 'ck':
                    if (item.FileFullpath == '' || item.FileFullpath == null) {
                        Message({
                            message: `${this.$t('GLOBAL.NoFile')}`,
                            type: 'warning'
                        });
                        return false;
                    }
                    this.$refs.createRepast.GetDetail(this.tableItem);
                    return;
                case 'wxjl':
                    this.getWXJLDetail();
                    return;
                case 'edit':
                    this.$refs.createRepast.SbxxList.forEach(item => {
                        for (let k in this.tableItem) {
                            if (item.id == k) {
                                if (k == 'DutyManager') {
                                    item.value = this.tableItem.DutyManagerValue;
                                } else if (k == 'RepairManager') {
                                    item.value = this.tableItem.RepairManagerValue;
                                } else {
                                    item.value = this.tableItem[k];
                                }
                            }
                        }
                    });
                    // console.log(this.tableItem)
                    if (this.tableItem.Status == '未派单' || this.tableItem.Status == '已派单') {
                        this.$refs.createRepast.SbxxList[3].label = this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.gzxx');
                        this.$refs.createRepast.SbxxList[3].required = false;
                    } else {
                        this.$refs.createRepast.SbxxList[3].label = this.$t('$vuetify.dataTable.TPM_SBGL_WDBX.gzxx') + '*';
                        this.$refs.createRepast.SbxxList[3].required = true;
                    }

                    this.$refs.createRepast.FileFullpath = this.tableItem.FilePath;
                    this.$refs.createRepast.FileName = this.tableItem.FileName;
                    if (this.tableItem.FileName != '') {
                        this.$refs.createRepast.FileList = [
                            {
                                name: this.tableItem.FileName
                            }
                        ];
                    }
                    this.$refs.createRepast.SbxxList[0].value = this.tableItem.DeviceId + '|' + this.tableItem.DeviceCode + '|' + this.tableItem.LineCode;
                    console.log(this.$refs.createRepast.SbxxList);
                    this.$refs.createRepast.showDialog = true;
                    return;

                case 'qx':
                    this.addTitle = this.$t('GLOBAL._QX');
                    this.addModel = true;
                    return;
                case 'qr':
                    this.addTitle = this.$t('GLOBAL._QR');
                    this.addModel = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        async Save() {
            let res;
            switch (this.dialogType) {
                case 'qr':
                    if (this.user == '' || this.userDate == null) {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    this.tableItem.ConfirmBy = this.user;
                    this.tableItem.ConfirmDate = this.userDate;
                    this.tableItem.ConfirmResult = this.ConfirmResult;
                    this.tableItem.ConfirmComment = this.ConfirmComment;
                    res = await GetRepairOrderConfirm(this.tableItem);
                    break;
                case 'qx':
                    if (this.user == '' || this.reason == '' || this.userDate == null) {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    this.tableItem.CancelBy = this.user;
                    this.tableItem.CancelDate = this.userDate;
                    this.tableItem.CancelReason = this.reason;
                    res = await GetRepairOrderCancel(this.tableItem);
                    break;
            }
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.getTableData();
                this.addModel = false;
            }
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await GetRepairOrderDelete(params);
                    if (res.success) {
                        this.tableItem = {};
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.getTableData();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.getTableData();
        }
    }
};
</script>
<style lang="scss">
#FlexForm {
    .el-dialog__body {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        .FlexAddForm {
            width: 48%;
        }
    }
}
</style>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}
.addForm {
    .textfieldbox {
        .el-input {
            width: 100%;
        }
    }
    .textlabel {
        display: inline-flex;
        font-size: 16px;
        margin-right: 25px;
    }
    .el-radio-group {
        height: 40px;
        margin-top: 10px;
    }
    .el-radio__input.is-checked + .el-radio__label {
        color: #3dcd58;
    }
    .el-radio__input.is-checked .el-radio__inner {
        border-color: #3dcd58;
        background: #3dcd58;
    }
    .el-radio__label {
        font-size: 16px;
    }
}
.addForm {
    position: relative;
    .el-date-editor {
        width: 100%;
        position: absolute;
        opacity: 0;
        top: 0;
        .el-input__inner {
            font-size: 16px;
            height: 32.5px;
            color: rgba(0, 0, 0, 0.87);
            border: 1px solid #9e9e9e;
        }
        .el-input__icon {
            line-height: 32.5px;
        }
    }
}
.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

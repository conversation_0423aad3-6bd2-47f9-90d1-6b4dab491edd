import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';
let DFM = 'baseURL_DFM';


// 维修工单列表
export function GetRepairRecordPageList(data) {
    const api = '/api/RepairRecord/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}
// 维修工单保存
export function GetRepairRecordSaveForm(data) {
    const api = '/api/RepairRecord/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}

// 维修工单接单
export function GetRepairRecordReceive(data) {
    const api = '/api/RepairRecord/Receive';
    return getRequestResources(baseURL, api, 'post', data);
}
// 工单来源
export function GetRepairOrderSource(data) {
    const api = '/api/DataItemDetail/GetDetailTree?itemCode=RepairOrderSource';
    return getRequestResources(DFM, api, 'post', data);
}
// 工单类型
export function GetRepairOrderType(data) {
    const api = '/api/DataItemDetail/GetDetailTree?itemCode=RepairOrderType';
    return getRequestResources(DFM, api, 'post', data);
}
//工单状态
export function GetRepairOrderStatus(data) {
    const api = '/api/DataItemDetail/GetDetailTree?itemCode=RepairOrderStatus';
    return getRequestResources(DFM, api, 'post', data);
}
// 维修工单退回
export function GetRepairRecordReject(data) {
    const api = '/api/RepairRecord/Reject';
    return getRequestResources(baseURL, api, 'post', data);
}
// 维修工单改派
export function GetRepairRecordReassign(data) {
    const api = '/api/RepairRecord/Reassign';
    return getRequestResources(baseURL, api, 'post', data);
}
// 维修工单维修记录
export function GetRepairRecordRepairRecord(data, Phenomenon) {
    let api = ""
    if (Phenomenon) {
        api = `/api/RepairRecord/RepairRecord?Phenomenon=${Phenomenon}`;
    } else {
        api = `/api/RepairRecord/RepairRecord`;
    }
    return getRequestResources(baseURL, api, 'post', data);
}
// 维修工单创建服务单
export function GetRepairServiceSaveForm(data) {
    const api = '/api/RepairService/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
// 维修工单创建服务单
export function GetRepairServiceDelete(data) {
    const api = '/api/RepairService/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
// 维修工单创建服务单
export function GetRepairServiceRequest(data) {
    const api = '/api/RepairService/Request';
    return getRequestResources(baseURL, api, 'post', data);
}


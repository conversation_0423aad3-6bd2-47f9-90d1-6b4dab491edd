import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_30015'
//年产量
export function getPackIView(data) {
    const api = '/api/PackIView/AnnualOutPut'
    return getRequestResources(baseURL, api, 'post', data);
}
//12个月产量
export function getEveryMonth(data) {
    const api = '/api/PackIView/GetEveryMonth'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取某年各产线产量
export function getLineOutPutTon(data) {
    const api = '/api/PackIView/LineOutPutTon'
    return getRequestResources(baseURL, api, 'post', data);
}
//月叠加数据
export function getFormulaType(data) {
    const api = '/api/PackIView/FormulaType'
    return getRequestResources(baseURL, api, 'post', data);
}
//按规格类别查询产量
export function getSpecOutPut(data) {
    const api = '/api/PackIView/GetSpecOutPut'
    return getRequestResources(baseURL, api, 'post', data);
}
//查询每个规格分类下的规格产量
export function getPackSpecOutPut(data) {
    const api = '/api/PackIView/PackSpecOutPut'
    return getRequestResources(baseURL, api, 'post', data);
}
//按不同方式统计各月产量
export function getDifferentWays(data) {
    const api = '/api/PackIView/DifferentWays'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取规格分类
export function getSpeType(data) {
    const api = '/api/PackIView/GetSpeType'
    return getRequestResources(baseURL, api, 'post', data);
}
//产量明细表
export function getProductionInfo(data) {
    const api = '/api/ImtableSapreport/ProductionInfo'
    return getRequestResources(baseURL, api, 'post', data);
}
//计划停机
export function getTimeAnalysisView(data) {
    const api = '/api/TimeAnalysisView/AnalyzeDowntime'
    return getRequestResources(baseURL, api, 'post', data);
}
//包材损耗率按分类
export function getImtablePakmatLoss(data) {
    const api = '/api/ImtablePakmatLoss/PackagingRate'
    return getRequestResources(baseURL, api, 'post', data);
}
//包材损耗率按产线
export function getImtablePakmatLossLine(data) {
    const api = '/api/ImtablePakmatLoss/PackagingLine'
    return getRequestResources(baseURL, api, 'post', data);
}
//三厂每月产出率
export function getYield(data) {
    const api = '/api/ImtableSapreport/GetYield'
    return getRequestResources(baseURL, api, 'post', data);
}
//工单一次性完成率
export function getCompletionRate(data) {
    const api = '/api/ImtableSapreport/CompletionRate'
    return getRequestResources(baseURL, api, 'post', data);
}
//非标产品工时及产量
export function getDowntimeGroup(data) {
    const api = '/api/DowntimeGroup/NoStandardPro'
    return getRequestResources(baseURL, api, 'post', data);
}
//返工工单
export function getReworkWorkOrder(data) {
    const api = '/api/ImtableSapreport/ReworkWorkOrder'
    return getRequestResources(baseURL, api, 'post', data);
}
//单位人时劳动力-人时部分
export function getManHour(data) {
    const api = '/api/ImtableSapreport/GetManHour'
    return getRequestResources(baseURL, api, 'post', data);
}
//单位人时劳动力-产量部分
export function getProduction(data) {
    const api = '/api/ImtableSapreport/GetProduction'
    return getRequestResources(baseURL, api, 'post', data);
}
//单位人时劳动力-生产力
export function getProductivity(data) {
    const api = '/api/ImtableSapreport/GetProductivity'
    return getRequestResources(baseURL, api, 'post', data);
}
//煮缸调节率
export function getAdjustmentRate(data) {
    const api = '/api/ImtableCokadjustment/AdjustmentRate'
    return getRequestResources(baseURL, api, 'post', data);
}
//原料损耗率
export function getMaterialLoss(data) {
    const api = '/api/ImtableRowmatLoss/MaterialLoss'
    return getRequestResources(baseURL, api, 'post', data);
}
//包装线设备（灌装机）停机率汇总（停机）
export function getDownTimeList(data) {
    const api = '/api/ImtablePakmatLoss/DownTimeList'
    return getRequestResources(baseURL, api, 'post', data);
}
//计划,非计划停机时间分析
export function getAnalyzeDowntime(data) {
    const api = '/api/TimeAnalysisView/AnalyzeDowntime'
    return getRequestResources(baseURL, api, 'post', data);
}
//OEE
export function getOeeReportView(data) {
    const api = '/api/OeeReportView/GetOeeDate'
    return getRequestResources(baseURL, api, 'post', data);
}



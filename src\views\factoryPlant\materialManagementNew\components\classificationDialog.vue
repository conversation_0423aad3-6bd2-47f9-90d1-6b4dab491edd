<template>
    <v-dialog v-if="dialog" v-model="dialog" scrollable persistent max-width="980px">
        <!-- 字典分类 查询 -->
        <v-card ref="form">
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                <!-- {{ classificationDialog === 'primaryClassification' ? '一级分类管理' : '二级分类管理' }} -->
                {{ $t('DFM_WLGL.WLFLGL') }}
                <v-icon @click="dialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-container>
                    <div class="classification-title">
                        <div class="classification-title-search">
                            <v-text-field v-model="formModel.key" style="width: 20%" outlined dense
                                :label="$t('DFM_WLGL._GJZ')" placeholder="请输入编号"></v-text-field>
                            <v-select v-model="formModel.identity" style="width: 20%" :items="traitList"
                                item-text="ItemName" item-value="ItemValue" clearable dense outlined
                                :label="$t('DFM_WLGL._TZ')" />
                            <v-btn color="primary" @click="getDataList">{{ $t('GLOBAL._CX') }}</v-btn>
                        </div>
                        <div class="classification-title-btns ml-8">
                            <v-btn icon color="primary" @click="getDataList">
                                <v-icon>mdi-cached</v-icon>
                            </v-btn>
                            <v-btn-toggle v-model="toggle_exclusive" dense>
                                <v-btn color="primary" large @click="addCalssify">{{ $t('GLOBAL._XZ') }}</v-btn>
                                <!-- <v-btn color="primary" large>编辑</v-btn> -->
                                <v-btn color="primary" large @click="batchDelete">{{ $t('GLOBAL._PLSC') }}</v-btn>
                            </v-btn-toggle>
                        </div>
                    </div>
                    <Tables ref="table" table-name="DFM_WLFL" :headers="headers" :desserts="desserts" table-height="360"
                        :loading="loading" :page-options="pageOptions" :btn-list="btnList" @selectePages="selectePages"
                        @itemSelected="selectedItems" @toggleSelectAll="selectedItems" @tableClick="tableClick">
                    </Tables>
                    <!-- 一级分类管理 -->
                    <!-- <template v-if="classificationDialog === 'primaryClassification'">
                        <v-data-table :headers="parimaryHeaders" :items="dataList" :items-per-page="itemsPerPage" hide-default-footer class="elevation-1" item-key="xh"></v-data-table>
                    </template> -->
                    <!-- 二级分类管理 -->
                    <!-- <template v-else>
                        <v-data-table :headers="sencondHeaders" :items="dataList" :items-per-page="itemsPerPage" hide-default-footer class="elevation-1" item-key="xh"></v-data-table>
                    </template>
                    <div class="page">
                        <span>共{{ dataList.length }}条数据</span>
                    </div> -->
                </v-container>
                <v-dialog v-model="isShowEditClassify" scrollable width="35%">
                    <EditClassify v-if="isShowEditClassify" :edit-classify-obj="editClassifyObj" @getdata="getDataList"
                        @closePopup="closePopup" />
                </v-dialog>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script>
// import { getSaveForm, getDelete } from '@/api/factoryPlant/supplier.js';
// import { GetPageList } from '@/api/factoryPlant/material.js';
import { getClassifyList, delClassify, getDataDictionary } from '../service';
export default {
    name: 'ClassificationDialog',
    components: {
        Tables: () => import('@/components/Tables/index.vue'),
        EditClassify: () => import('./editClassify.vue')
    },
    props: {
        classificationDialog: {
            type: String,
            default: ''
        },
        headers: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            isShowEditClassify: false,
            updateForm: true,
            // 提交表单数据
            valid: true,
            dialog: false,
            checkbox: false,
            formModel: {
                key: '',
                identity: ''
            },
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            selectedList: [],
            btnList: [
                { text: '编辑', icon: '', code: 'edit', type: 'primary' },
                { text: '删除', icon: '', code: 'delete', type: 'red' }
            ],
            desserts: [],
            toggle_exclusive: '',
            editClassifyObj: {},
            traitList: []
        };
    },
    async created() {
        // this.initData();
        await this.getTraitList();
        this.getDataList();
    },
    methods: {
        async getTraitList() {
            let resp = await getDataDictionary({ lang: 'cn', itemCode: 'MaterialClass' });
            this.traitList = resp.response;
        },
        closePopup() {
            this.isShowEditClassify = false;
        },
        addCalssify() {
            this.editClassifyObj = {};
            this.isShowEditClassify = true;
        },
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.page = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        batchDelete() {
            let selecteds = this.$refs.table.selected;
            if (selecteds.length === 0) {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
                return false;
            }
            let ids = [];
            selecteds.forEach(item => {
                ids.push(item.ID);
            });
            this.delItem(ids, 'batch');
        },
        delItem(ids, type) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let resp = await delClassify(ids);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
                    if (type === 'batch') {
                        this.$refs.table.selected = [];
                    }
                    this.getDataList();
                })
                .catch(() => {});
        },
        // 操作栏按钮
        tableClick(item, type) {
            switch (type) {
                // 复制
                case 'copy':
                    this.copyClick();
                    break;
                // 编辑
                case 'edit':
                    this.editClassifyObj = item;
                    this.isShowEditClassify = true;
                    break;
                // 删除
                case 'delete':
                    this.delItem([item.ID]);
                    break;
                default:
                    break;
            }
        },
        // 获取全部表格数据
        async getDataList() {
            this.loading = true;
            let params = {
                pageIndex: this.pageOptions.page,
                pageSize: this.pageOptions.pageSize
            };
            const res = await getClassifyList({ ...this.formModel, ...params });
            const { success, response } = res || {};
            const { data, dataCount, page, pageCount, pageSize } = response || {};
            if (success) {
                this.desserts = data.map(item => {
                    let trait = this.traitList.find(itm => itm.ItemValue === item.Identities);
                    if (trait) {
                        item.traitName = trait.ItemName || '';
                    }
                    return item;
                });
                this.pageOptions.total = dataCount;
                this.pageOptions.page = page;
                this.pageOptions.pageCount = pageCount;
                this.pageOptions.pageSize = pageSize;
            } else {
                this.desserts = [];
            }
            this.loading = false;
        },
        // 查询
        searchForm() {
            console.log(1111);
        }
    }
};
</script>
<style lang="scss" scoped>
.classification-title {
    width: 100%;
    height: 58px !important;
    display: flex;
    align-items: baseline;
    justify-content: space-between;
    justify-items: center;

    .classification-title-search {
        display: flex;
        justify-content: space-between;
    }

    .classification-title-btns {
        display: flex;
        justify-content: space-between;

        .v-btn {
            height: 39px !important;
        }
    }
}

.v-text-field {
    margin-right: 16px;
}

.col-12 {
    padding: 0;
}

.v-sheet.v-card {
    border-radius: 10px;
}
</style>

<template>
    <div class="themeSetting">
        <v-toolbar color="primary" height="40">
            <v-toolbar-title class="white--text">主题设置</v-toolbar-title>
        </v-toolbar>
        <v-container>
            <v-row column>
                <v-col>
                    <v-subheader class="px-1 my-2">主题颜色</v-subheader>
                    <div class="color-option">
                        <v-item-group v-model="theme">
                            <template v-for="item in themeColorOptions">
                                <v-item :key="item.key" v-slot="{ active }" :value="item.key">
                                    <v-avatar :class="active ? 'active' : ''" class="color-option__item"
                                        :color="item.key" size="56" tile @click="handleChangeColor(item)">
                                        <v-scroll-y-transition>
                                            <v-icon v-if="active" color="white">mdi-check</v-icon>
                                        </v-scroll-y-transition>
                                    </v-avatar>
                                </v-item>
                            </template>
                        </v-item-group>
                    </div>
                    <div class="theme-options">
                        <v-subheader class="px-1 my-2">主题模式</v-subheader>
                        <v-divider></v-divider>
                        <div class="my-3">
                            <v-btn-toggle v-model="sideBarOption">
                                <v-btn text value="dark">深 色</v-btn>
                                <v-btn text value="light">浅 色</v-btn>
                            </v-btn-toggle>
                        </div>
                    </div>
                </v-col>
            </v-row>
        </v-container>
    </div>
</template>
<script>
import colors from '../theme/util/colors';
import { mapGetters } from 'vuex';
export default {
    data() {
        return {
            locale: this._i18n.locale,
            sideBarOption: 'light',
            colors: colors
        };
    },
    computed: {
        ...mapGetters(['getTheme']),
        themeColorOptions() {
            return [
                {
                    key: 'blue'
                },
                {
                    key: 'teal'
                },
                {
                    key: 'red'
                },
                {
                    key: 'orange'
                },
                {
                    key: 'purple'
                },
                {
                    key: 'indigo'
                },
                {
                    key: 'cyan'
                },
                {
                    key: 'pink'
                },
                {
                    key: 'green'
                }
            ];
        },
        theme: {
            get() {
                return this.getTheme;
            },
            set(val) {
                this.$store.commit('setTheme', val);
            }
        }
    },
    watch: {
        sideBarOption: {
            handler(val) {
                this.$vuetify.theme.dark = val === 'dark';
            },
            immediate: true
        },
        updateTheme() { }
    },
    methods: {
        handleChangeColor(option) {
            this.theme = option.key;
            const color = this.colors[option.key].base;
            this.$store.commit('setThemeColor', color);
            this.$vuetify.theme.themes.light.primary = color;
        }
    }
};
</script>
<style lang="sass" scoped>
.color-option
  &__item
    display: inline-flex
    margin: 3px
    cursor: pointer
</style>

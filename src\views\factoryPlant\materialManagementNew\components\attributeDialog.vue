<!-- eslint-disable vue/valid-v-slot -->
<template>
    <v-dialog v-model="dialog" persistent max-width="720px">
        <!-- 字典分类 查询 -->
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                {{ $t('DFM_WLMX._SXKZ') }}
                <v-icon @click="dialog = false">mdi-close</v-icon>
            </v-card-title>
            <!-- 表单内容 -->
            <v-card-text class="mt-4">
                <div class="form-btn-list">
                    <!-- 新 2024-08-02 -->
                    <el-form class="form" size="small" :inline="true" :model="formInline">
                        <el-form-item>
                            <el-input v-model="formInline.PropertyCode" clearable placeholder="关键字查询"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button icon="el-icon-search" @click="searchClick">{{ $t('GLOBAL._CX')
                                }}</el-button>
                        </el-form-item>
                    </el-form>
                    <!-- <v-btn icon color="primary" @click="GetPropertyValuePageList">
                                        <v-icon>mdi-cached</v-icon>
                                    </v-btn> -->
                    <!-- <v-btn color="primary" @click="clickClass('add')">{{ $t('GLOBAL._XZ') }}</v-btn>
                                    <v-btn color="error" :disabled="deleteList.length == 0" @click="clickClass('delete')">{{ $t('GLOBAL._PLSC') }}
                                    </v-btn> -->
                </div>
                <Tables :loading="loading" :footer="true" :headers="headers" table-height="400"
                    table-name="DFM_WLMX_SXKZ" :desserts="tableData" :page-options="pageOptions"
                    @selectePages="selectePages" @tableClick="tableClick" @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems">
                    <template #actions="{ item }">
                        <v-btn v-for="(list, index) in btnList" :key="index"
                            :disabled="item.ID === null && list.disabled" text small class="mx-0 px-0"
                            :color="list.type" @click.stop="tableClick(item, list.code)">
                            {{ list.text }}
                        </v-btn>
                    </template>
                </Tables>
            </v-card-text>
        </v-card>
        <v-dialog v-model="dialogClass" persistent max-width="720px">
            <!-- 新增 -->
            <v-card v-if="clickType === 'add'">
                <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                    {{ $t('DFM_WLMX._TJSX') }}
                    <v-icon @click="dialogClass = false">mdi-close</v-icon>
                </v-card-title>
                <!-- 表单内容 -->
                <v-card-text class="mt-7">
                    <v-container>
                        <v-form ref="attrform" v-model="validclass">
                            <v-row>
                                <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                    <v-text-field ref="PropertyCode" v-model="saveAttrFrom.PropertyCode"
                                        :rules="[v => !!v || '编码不能为空']" outlined dense :label="$t('DFM_WLMX._SXBM')"
                                        required></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                    <v-text-field ref="PropertyValue" v-model="saveAttrFrom.PropertyValue"
                                        :rules="[v => !!v || '值不能为空']" outlined dense :label="$t('DFM_WLMX._SXZ')"
                                        required></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12">
                                    <v-textarea v-model="saveAttrFrom.Remark" rows="2" outlined
                                        :label="$t('DFM_WLMX._SXBZ')"></v-textarea>
                                </v-col>
                            </v-row>
                        </v-form>
                    </v-container>
                </v-card-text>
                <v-card-actions class="py-0">
                    <v-checkbox v-model="classcheckbox" label="确定并关闭窗口"></v-checkbox>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="SaveClassForm('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                    <v-btn @click="dialogClass = false">{{ $t('GLOBAL._GB') }}</v-btn>
                </v-card-actions>
            </v-card>

            <!-- 编辑 -->
            <v-card v-if="clickType === 'edit'">
                <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                    {{ $t('DFM_WLMX._XGSX') }}
                    <v-icon @click="dialogClass = false">mdi-close</v-icon>
                </v-card-title>
                <!-- 表单内容 -->
                <v-card-text class="mt-7">
                    <v-container>
                        <v-form ref="attrform" v-model="validclass">
                            <v-row>
                                <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                    <v-text-field disabled ref="PropertyCode" v-model="form.PropertyCode"
                                        :rules="[v => !!v || '属性/类别不能为空']"  outlined dense :label="$t('DFM_WLMX._SXBM')"
                                        required></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                    <v-text-field ref="PropertyValue" v-model="form.PropertyValue"
                                        :rules="[v => !!v || '值不能为空']" outlined dense :label="$t('DFM_WLMX._SXZ')"
                                        required></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12">
                                    <v-textarea v-model="form.PropertyName" rows="2" outlined
                                        :label="$t('DFM_WLMX._SXBZ')"></v-textarea>
                                </v-col>
                            </v-row>
                        </v-form>
                    </v-container>
                </v-card-text>
                <v-card-actions class="py-4">
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="SaveClassForm('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                    <v-btn @click="dialogClass = false">{{ $t('GLOBAL._GB') }}</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </v-dialog>
</template>

<script>
import { GetMaterialClassList, GetPropertyValuePageList, SaveMaterialMapping, MaterialSaveForm, MaterialDelete, getMaterialPropertyPageList } from '../service.js';
import { attributeDialog, attributeTableHead } from '@/columns/factoryPlant/physicalModel.js';

export default {
    name: 'AttributeDialog',
    props: {
        treeData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            tab: null,
            tableData: [],
            selection: [], // 左侧选中数据
            allItems: [], // 左侧所有套件
            selectionRright: [], //右侧选中数据
            selectionRrightData: [], // 右侧所有数据
            rootitems: [], // 根节点
            rootClasslist: [],
            // 提交表单数据
            validclass: false,
            dialog: false,
            dialogClass: false,
            // 新增字典
            checkbox: false,
            checkboxTree: false,
            loading: false,
            // 分类管理弹窗（新增，修改）
            clickType: '',
            classcheckbox: true,

            //查询条件
            searchkey: '',
            headers: attributeTableHead,

            // 修改数据
            tableItem: {},
            form:{},
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 10, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            deleteList: [],
            //  保存分类入参
            saveAttrFrom: {
                EquipmentId: null,
                PropertyId: null,
                PropertyValue: null,
                Deleted: 0,
                Remark: null,
                PropertyCode: null,
                ID: null
            },
            // 操作按钮
            btnList: [
                {
                    text: '修改',
                    code: 'edit',
                    type: 'primary',
                    icon: ''
                },
                {
                    text: '重置',
                    code: 'delete',
                    disabled: true,
                    type: 'red',
                    icon: ''
                }
            ],
            // 拖拽数据
            drag: false,
            style: {
                minHeight: '240px',
                display: 'block'
            },
            // 新 2024-08-02
            formInline: {
                pageIndex: 1,
                pageSize: 1000
            }
        };
    },
    mounted() { },
    methods: {
        initData(item) {
            this.saveAttrFrom.EquipmentId = item.ID;
            this.form.EquipmentId = item.ID;
            this.formInline.MaterialId = item.ID
            this.materialPropertyPageList()
            // this.GetPropertyValuePageList();
            // this.GetMaterialClassList();
        },
        // 拖拽
        onStart() {
            this.drag = true;
        },
        onEnd() {
            this.drag = false;
        },
        // 获取扩展属性列表
        async materialPropertyPageList() {
            const { response } = await getMaterialPropertyPageList({
                ...this.formInline,
                pageIndex: this.pageOptions.page,
                pageSize: this.pageOptions.pageSize
            })
            this.tableData = response.data
            this.pageOptions.pageCount = response.pageCount;
            this.pageOptions.total = response.dataCount;
        },
        searchClick(){
            this.pageOptions.page = 1
            this.materialPropertyPageList()
        },
        // 获取属性列表
        async GetPropertyValuePageList() {
            let papams = {
                materialid: this.saveAttrFrom.EquipmentId,
                key: this.saveAttrFrom.EquipmentId
            };
            const res = await GetPropertyValuePageList(papams);
            if (res.success) {
                this.tableData = res.response.data;
                this.saveAttrFrom.PropertyCode = null;
                this.saveAttrFrom.PropertyValue = null;
                this.saveAttrFrom.Remark = null;
            } else {
                this.tableData = [];
            }
        },
        // 获取套件配置
        async GetMaterialClassList() {
            let papams = {
                materialid: this.saveAttrFrom.EquipmentId
            };
            const res = await GetMaterialClassList(papams);
            if (res.success) {
                this.allItems = res.response.Item;
                this.selectionRrightData = res.response.SelectItem;
            }
        },

        // 操作按钮
        clickClass(type) {
            switch (type) {
                case 'add':
                    this.clickType = type;
                    this.dialogClass = true;
                    return;
                case 'delete':
                    if (this.deleteList.length) {
                        this.deltable();
                    } else {
                        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'warning' });
                    }
                    return;
            }
        },
        // 属性扩展新增 & 修改
        async SaveClassForm(type) {
            console.log('2222222222222 = ',this.form);
            let fromvalidate = await this.$refs.attrform.validate();
            if (fromvalidate) {
                let params;
                if (type == 'add') {
                    params = {
                        materialid: this.saveAttrFrom.EquipmentId,
                        PropertyValue: this.saveAttrFrom.PropertyValue,
                        Remark: this.saveAttrFrom.Remark,
                        PropertyCode: this.saveAttrFrom.PropertyCode
                    };
                } else {
                    console.log('1111=',this.form);
                    params = {
                        materialid: this.form.EquipmentId,
                        PropertyValue: this.form.PropertyValue,
                        PropertyName: this.form.PropertyName,
                        PropertyCode: this.form.PropertyCode,
                        ID: this.form.ID
                    };
                }

                let res = await MaterialSaveForm(params);
                let { success } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: type == 'add' ? '添加成功' : '修改成功', color: 'success' });
                    this.dialogClass = this.classcheckbox ? false : true;
                    this.materialPropertyPageList();
                }
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.dialogClass = true;
                    this.form = {
                        ...this.form,
                        ...item
                    }
                    this.clickType = type;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除列表
        SelectedItems(item) {
            this.tableItem = {};
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        //分页选择
        selectePages(data) {
            this.pageOptions.page = data.pageCount;
            this.pageOptions.pageSize = data.pageSize;
            this.materialPropertyPageList();
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await MaterialDelete(params);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.materialPropertyPageList();
                        this.deleteList = [];
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 移除添加套件
        //套件保存ClassMappingSave
        async attrSave() {
            console.log(this.saveAttrFrom);
            let romove = this.selectionRrightData.map(item => {
                return {
                    ClassId: item.ID,
                    MappingId: this.saveAttrFrom.EquipmentId
                };
            });
            let params = {
                MappingId: this.saveAttrFrom.EquipmentId,
                MappingList: romove
            };
            const res = await SaveMaterialMapping(params);
            if (res.success) {
                this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });
                this.GetMaterialClassList();
                this.GetPropertyValuePageList();
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.ghostClass {
    background-color: blue !important;
}

.chosenClass {
    background-color: var(--v-primary-lighten2) !important;
    opacity: 1 !important;
}

.dragClass {
    background-color: var(--v-primary-lighten1) !important;
    opacity: 1 !important;
    box-shadow: none !important;
    outline: none !important;
    background-image: none !important;
}

.itxst {
    margin: 12px auto;
    min-height: 320px;
    display: flex;
    overflow: auto;
}

.title {
    padding: 6px 12px;
}

.col {
    width: 40%;
    flex: 1;
    padding: 10px;
    border: solid 1px #eee;
    border-radius: 5px;
    float: left;
}

.col+.col {
    margin-left: 10px;
}

.item {
    padding: 6px 12px;
    border-radius: 3px;
    margin: 0px 10px 0px 10px;
    border: solid 1px var(--v-primary-lighten5);
    background-color: var(--v-primary-lighten5);
}

.item:hover {
    background-color: var(--v-primary-lighten3);
    cursor: move;
}

.item+.item {
    border-top: none;
    margin-top: 6px;
}

.form {
    text-align: left;
    height: 33px;
}
</style>

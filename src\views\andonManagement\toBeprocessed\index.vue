// 报警处置页面
<template>
    <div class="tobe-alarm-view">
        <div class="tobe-alarm-main overflow-auto">
            <v-card>
                <v-card-text class="mt-4 pb-8">
                    <v-row>
                        <v-col :cols="12" class="activ-style activ-height pa-0 height-12" :lg="12"
                            style="border-bottom: none; height: 49px">
                            <v-tabs show-arrows>
                                <v-tab v-for="(i, k) in productionLineList" :key="k" @click="changProductionLine(i)"
                                    class="title-s" :class="bgc[i.problemLevel] || 'sbgc'">{{ i.name }}</v-tab>
                            </v-tabs>
                        </v-col>
                        <v-col :cols="12" class="activ-style" :lg="12">
                            <v-row>
                                <v-col :cols="2" :lg="2" class="sub-row-col pt-8">
                                    <div class="alram-info-title">
                                        <!-- {{ areaCode }} -->
                                        {{ info.ProductLineName }}
                                    </div>
                                    <div class="alram-info-list">
                                        <div v-for="(i, s) in items" :key="s">
                                            <div v-if="i.hasChildren" :key="i.id" class="alram-info-children">
                                                <div class="alram-info-item rounded-0 justify-start pl-8"
                                                    :class="{ 'alarm-list-item-active': isSelId == i.id }">
                                                    <div class="alram-info-folder" @click.stop="iconFatherClick(i)">
                                                        <v-icon class="float-right mr-4 mt-2">{{ i.isExpan ?
                            'mdi-menu-down' : 'mdi-menu-right' }}</v-icon>
                                                    </div>
                                                    <div @click="divFatherClick(i)" class="d-flex">
                                                        <v-icon class="mr-1" style="font-size: 16px"
                                                            :class="fcl[i.problemLevel] || 'sfcl'">mdi-alarm-light</v-icon>
                                                        <span :title="i.name" style="width: 180px"
                                                            class="text-ellipsis folder-span">{{ i.name }}</span>
                                                    </div>
                                                </div>
                                                <div v-if="i.isExpan">
                                                    <div v-for="j in i.children" :key="j.id"
                                                        class="alram-info-item rounded-0 justify-start pl-14"
                                                        :class="{ 'alarm-list-item-active': isSelId == j.id }"
                                                        @click="divFatherClick(j)">
                                                        <v-icon class="mr-1" style="font-size: 16px"
                                                            :class="fcl[j.problemLevel] || 'sfcl'">mdi-alarm-light</v-icon>
                                                        <span :title="j.name" style="width: 160px"
                                                            class="text-ellipsis folder-span">{{ j.name }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-else class="alram-info-item rounded-0 justify-start pl-8"
                                                :class="{ 'alarm-list-item-active': isSelId == i.id }"
                                                @click="divFatherClick(i)">
                                                <v-icon class="mr-1" style="font-size: 16px"
                                                    :class="fcl[i.problemLevel] || 'sfcl'">mdi-alarm-light</v-icon>
                                                <span :title="i.name" style="width: 180px"
                                                    class="text-ellipsis folder-span">{{ i.name }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </v-col>
                                <v-col :cols="8" :lg="9" class="sub-row-col scrollWrap">
                                    <div v-if="alarmList.length == 0" class="alarm-list-null">{{ $t('GLOBAL.noData') }}
                                    </div>
                                    <template v-else>
                                        <v-row class="activ-style alarm-list-item" v-for="(i, k) in alarmList" :key="k"
                                            :class="{ 'alarm-list-item-active': alarmSeletecedObj.ID == i.ID }"
                                            @click="alarmSeletecedObj = i">
                                            <v-col :cols="12" :lg="12" @click="dialog = false">
                                                <v-row>
                                                    <v-col :cols="12" :lg="12"
                                                        class="alarm-list-title pa-2 pb-1 d-flex">
                                                        <v-icon class="mr-3"
                                                            :class="fcl[i.ProblemLevel]">mdi-alarm-light</v-icon>
                                                        <v-row>
                                                            <v-col :cols="8" :lg="8">
                                                                <span :class="fcl[i.ProblemLevel]">{{
                            $getDictionaryVal(i.ProblemLevel, ProblemLevelList,
                                'ItemValue', 'ItemName') }}</span>
                                                                <span class="ml-4">
                                                                    {{ $getDictionaryVal(i.MainAlarmType, typeChildList,
                            'AlarmCode', 'AlarmName') }} -
                                                                    {{ $getDictionaryVal(i.SubAlarmType, typeChildList,
                            'AlarmCode', 'AlarmName') }}
                                                                </span>
                                                            </v-col>
                                                            <v-col :cols="4" :lg="4" class="d-flex flex-row-reverse">
                                                                <span>{{ i.CreateDate }}</span>
                                                            </v-col>
                                                        </v-row>
                                                    </v-col>
                                                    <v-col :cols="12" :lg="12" class="pl-2 alarm-list-status">
                                                        <v-row>
                                                            <v-col :cols="12" :lg="12"
                                                                class="pt-2 pb-2 alarm-list-content">
                                                                <span class="zhkh">[</span>
                                                                {{ info.ProductLineName }}
                                                                <span class="zhkh">]</span>
                                                                <span class="zhkh">[</span>
                                                                {{ i.ProductLineName }}
                                                                <span class="zhkh">]</span>
                                                                <span class="zhkh">[</span>
                                                                {{ i.UnitName }}
                                                                <span class="zhkh">]</span>
                                                                ：
                                                                <span class="tuchu">{{ $getDictionaryVal(i.SubAlarmType,
                            typeChildList, 'AlarmCode', 'AlarmName') }}</span>
                                                                ， 当前已发生
                                                                <span class="font-s" :class="fcl[i.ProblemLevel]">{{
                            i.Total }}</span>
                                                                次， 已持续
                                                                <span class="font-s" :class="fcl[i.ProblemLevel]">{{
                            getContinueTime(i.CreateDate) }}</span>
                                                                min。
                                                                <br />
                                                                当前等级
                                                                <span class="font-s" :class="fcl[i.ProblemLevel]">{{
                            i.EventLevel }}</span>
                                                                级， 由
                                                                <span>{{ i.Currentman || i.Currentduty }}</span>
                                                                负责处置，
                                                                <span class="font-s" :class="fcl[i.ProblemLevel]">{{
                            getUpgradetime(i.Upgradetime, i.CreateDate)
                        }}</span>
                                                                min后自动升级至
                                                                <span class="zhkh">[</span>
                                                                {{ i.Nextduty }}
                                                                <span class="zhkh">]</span>
                                                                。
                                                            </v-col>
                                                        </v-row>
                                                    </v-col>
                                                </v-row>
                                            </v-col>
                                        </v-row>
                                        <div v-if="alarmList.length < dataCount && alarmList.length > 0"
                                            class="alarm-list-null">
                                            <v-btn text color="primary" @click="getMore"
                                                :loading="moreLoading">查看更多</v-btn>
                                        </div>
                                        <!-- <div v-else class="alarm-list-null">
                                            <v-btn text color="primary">没有更多数据啦！</v-btn>
                                        </div> -->
                                    </template>
                                </v-col>

                                <v-col :cols="2" :lg="1" class="sub-row-left pt-8">
                                    <div class="sub-row-opear">
                                        <!-- <div class="activ-style sub-row-btns" @click="showDetailDialog('upgrade')">
                                            <span class="iconfont icon-shengji sub-row-btn"></span>
                                            升级
                                        </div>
                                        <div class="activ-style sub-row-btns" @click="showDetailDialog('toRepair')">
                                            <span class="iconfont icon-xiugai sub-row-btn"></span>
                                            到修
                                        </div>-->
                                        <!-- 详情 -->
                                        <div class="activ-style sub-row-btns"
                                            :class="{ 'sub-row-btns-active': alarmSeletecedObj.ID }"
                                            @click="showHandleDialog('detailDialog')">
                                            <span class="iconfont icon-detail sub-row-btn"></span>
                                            {{ this.$t('ANDON_BJJL.details') }}
                                        </div>
                                        <!-- 关警 -->
                                        <div class="activ-style sub-row-btns"
                                            :class="{ 'sub-row-btns-active': alarmSeletecedObj.ID }"
                                            @click="showHandleDialog('handleDialog')">
                                            <span class="iconfont icon-handle sub-row-btn"></span>
                                            {{ this.$t('ANDON_BJJL.close') }}
                                        </div>
                                        <!-- 维修 -->
                                        <div v-if="alarmSeletecedObj.MainAlarmType == 'DEVICE'"
                                            class="activ-style sub-row-btns"
                                            :class="{ 'sub-row-btns-active': alarmSeletecedObj.ID }"
                                            @click="showDetailDialog('maintenance')">
                                            <span class="iconfont icon-weixiu sub-row-btn"></span>
                                            {{ this.$t('ANDON_BJJL.maintenance') }}
                                        </div>
                                        <!-- <div v-if="alarmSeletecedObj.MainAlarmType == 'DEVICE'" class="activ-style sub-row-btns" :class="{'sub-row-btns-active': alarmSeletecedObj.ID}" 
                                            @click="showDetailDialog('adjustment')">
                                            <span class="iconfont icon-shezhi sub-row-btn"></span>
                                            机器调整
                                        </div> -->
                                        <!-- <div class="activ-style sub-row-btns" @click="showLoseDialog('lose')">
                                            <span class="iconfont icon-sunhao sub-row-btn"></span>
                                            损耗
                                        </div> -->
                                        <!-- <div class="activ-style sub-row-btns replace-box"></div> -->
                                    </div>
                                </v-col>
                            </v-row>
                        </v-col>
                    </v-row>
                </v-card-text>
            </v-card>
            <maintenanceDialog ref="maintenanceDialog" :operaObj="maintenanceObj" @handlePopup="handlePopup" />
            <handleDialog ref="handleDialog" :operaObj="maintenanceObj" @handlePopup="handlePopup" />
            <detailDialog ref="detailDialog" :operaObj="maintenanceObj" @handlePopup="handlePopup" />
        </div>
    </div>
</template>
<script>
import { AlarmRecordCallForRepaired } from '@/api/andonManagement/alarmHome.js';
import { getAlarmTypeRootList, GetListByAlarmId, getAlarmTypeTreetList } from '@/api/andonManagement/alarmType.js';
import { AlarmRecordPageList, GetUgrecyStationCountBySectionCode, GetUgrecySectionCountByLineCode, SendUgrecyLineCount } from '@/api/andonManagement/alarmRecord.js';
import { EquipmentGetEquipmentTree } from '@/api/common.js';
import Util from '@/util';
import * as signalR from '@microsoft/signalr';
export default {
    components: {
        maintenanceDialog: () => import('./components/maintenanceDialog.vue'),
        detailDialog: () => import('./components/detailDialog.vue'),
        handleDialog: () => import('./components/handleDialog.vue')
    },
    data() {
        return {
            maintenanceObj: {},
            maintenanceDialog: false,
            alarmSeletecedObj: '',
            dialog: false,
            loading: false,
            moreLoading: false,
            typeChildList: [],
            alarmRootList: [],
            alarmList: [],
            productLineList: [],
            productionLineList: [],
            ProblemLevelList: [],
            info: {
                ProductLineName: '',
                ProductLineId: '',
                productLine: '',
                productLineName: '',
                UnitId: '',
                UnitName: ''
            },
            bgc: {
                '01': 'gbgc',
                10: 'ybgc',
                11: 'rbgc'
            },
            fcl: {
                '01': 'gfcl',
                10: 'yfcl',
                11: 'rfcl'
            },
            MainAlarmId: '',
            detailList: [
                { type: 'maintenance', title: '报修', fn: AlarmRecordCallForRepaired }
                // { type: 'toRepair', title: '到修', fn: AlarmRecordToBeRepaired },
                // { type: 'upgrade', title: '升级', fn: AlarmRecordUpgrade },
                // { type: 'adjustment', title: '机器调整', fn: AlarmRecordConfig },
                // { type: 'lose', title: '损耗', fn: eventDefectSaveForm }
            ],
            equipmentList: [],
            eventStatusList: [],
            closeStatusList: ['COMPLETE', 'CLOSED'],
            pageIndex: 1,
            dataCount: 1,
            pageSizes: 20,
            items: [],
            isSelId: -1,
            areaCode: '',
            signaRApi: '/api2/chatHub'
        };
    },
    async created() {
        this.getEquipmentList();
        this.getAlarmTypeTreetList();
        this.productLineList = await Util.GetEquipmenByLevel('Line');
        this.equipmentList = await Util.GetEquipmenByLevel('Unit');
        this.eventStatusList = await this.$getDataDictionary('alarmEventStatus');
        this.ProblemLevelList = await this.$getDataDictionary('problemLevel');
        if (this.connection) this.connection.stop();
        this.signalRinit();
    },
    activated() {
        if (this.connection) this.connection.stop();
        this.signalRinit();
    },
    deactivated() {
        this.connection.stop();
    },
    methods: {
        signalRinit() {
            this.connection = new signalR.HubConnectionBuilder()
                .withUrl(this.$signaRbaseURL + this.signaRApi, { skipNegotiation: true, transport: signalR.HttpTransportType.WebSockets })
                .configureLogging(signalR.LogLevel.Information)
                .build();
            this.connection.on('UgrecyLineCount', message => {
                try {
                    const a = JSON.parse(message);
                    console.log(a);
                    this.productionLineList = this.handlerData(this.productionLineList, a, 0);
                    const { ProductLineId, productLine, UnitId } = this.info;
                    this.alarmList = [];
                    this.pageIndex = 1;
                    if (ProductLineId) {
                        const o = this.productionLineList.find(i => i.id == ProductLineId);
                        this.changProductionLine(o, productLine ? 'push' : '');
                        if (productLine) {
                            const { children } = o;
                            const no = children.find(i => i.value == productLine);
                            this.divFatherClick(no || [], UnitId ? 'push' : '');
                            if (UnitId) {
                                const ro = no?.children?.find(i => i.id == UnitId);
                                this.divFatherClick(ro || []);
                            }
                        }
                    }
                    this.$forceUpdate();
                } catch (error) {
                    console.log(error);
                }
            });
            this.connection
                .start()
                .then(() => {
                    console.log('toBeprocessed signalR Connected!');
                    SendUgrecyLineCount();
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // div点击操作
        async divFatherClick(o, type) {
            const { isExpan, id, value, children } = o;
            o.isExpan = isExpan || !isExpan;
            this.isSelId = id;
            this.chooseV(o, type);
            // await this.setSectionCode(value, children, type);
            this.$forceUpdate();
        },
        // 图标点击操作
        async iconFatherClick(o) {
            const { isExpan, children, value } = o;
            o.isExpan = !isExpan;
            // await this.setSectionCode(value, children);
            this.$forceUpdate();
        },
        chooseV(o, type) {
            if (!o) return;
            const { name, extendField, parentId, id, value } = o || {};
            if (extendField == 'ProductLine') {
                this.info.productLineName = name;
                this.info.productLine = value;
                this.info.UnitName = '';
                this.info.UnitId = '';
            } else {
                this.info.UnitName = name;
                this.info.UnitId = id;
                this.items.find(i => {
                    if (i.id == parentId) {
                        this.info.productLineName = i.name;
                        this.info.productLine = i.value;
                        return true;
                    }
                });
            }
            if (type == 'push') return false;
            console.log(1111111111111111);
            this.alarmList = [];
            this.pageIndex = 1;
            this.getData();
        },
        getMore() {
            this.moreLoading = true;
            this.pageIndex++; //页码++
            this.getData();
        },
        // 计算持续时间
        getContinueTime(s, e) {
            const et = e ? new Date(e) : new Date();
            const st = new Date(s);
            const c = et.getTime() - st.getTime();
            const m = Math.floor(c / 60 / 1000);
            return m;
        },
        // 计算升级时间
        getUpgradetime(u, s) {
            const ct = this.getContinueTime(s);
            const ut = u / 1 ? u : 0;
            const p = ut - ct;
            return p < 0 ? 0 : p;
        },
        // 获取产线
        async getEquipmentList() {
            const resp = await EquipmentGetEquipmentTree({ level: '' });
            const { success, response } = resp || {};
            if (success) {
                let productionLineList = [];
                response.forEach(i => {
                    productionLineList = productionLineList.concat(i.children);
                });
                this.productionLineList = productionLineList;
                this.changProductionLine(this.productionLineList[0] || {});
            }
        },
        findChildren(treeData, arr, c) {
            treeData.forEach(e => {
                e.problemLevel = null;
            });
            for (var i = 0; i < treeData.length; i++) {
                const { children, value, problemLevel } = treeData[i];
                if (children && children.length) {
                    treeData[i].children = this.findChildren(children, arr, 1);
                }
                let str = problemLevel;
                arr.find(o => {
                    if (str == '11') {
                        return true;
                    }
                    const { ProblemLevel, ProductLine, UnitCode } = o;
                    let v = UnitCode;
                    if (c < 1) v = ProductLine;
                    if (value == v) {
                        if (str < ProblemLevel || !str) str = ProblemLevel;
                    }
                });
                treeData[i].problemLevel = str;
            }
            return treeData;
        },
        // 数组处理
        handlerData(treeData, arr) {
            treeData.forEach(e => {
                e.problemLevel = null;
            });
            for (var i = 0; i < treeData.length; i++) {
                const { value, problemLevel, children } = treeData[i];
                if (children.length) {
                    treeData[i].children = this.findChildren(children, arr, 0);
                }
                let str = problemLevel;
                arr.find(v => {
                    const { ProblemLevel, areaCode } = v;
                    if (str == '11') {
                        return true;
                    }
                    if (!str || str < ProblemLevel) {
                        if (value == areaCode) {
                            console.log(value == areaCode, ProblemLevel);
                            str = ProblemLevel;
                        }
                    }
                });
                treeData[i].problemLevel = str;
            }
            return treeData;
        },
        // 点击产线
        async changProductionLine(o, type) {
            const { children, name, id, value } = o;
            this.info.ProductLineName = name;
            this.info.ProductLineId = id;
            this.info.productLineName = '';
            this.info.productLine = '';
            this.info.UnitName = '';
            this.info.UnitId = '';
            this.isSelId = -1;
            this.areaCode = value;
            // const result = await this.GetUgrecySectionCountByLineCode(value);
            children.forEach(i => {
                const { hasChildren, children, value } = i;
                // i.problemLevel = null;
                // result.find(e => {
                //     if (value == e.Device_Code) {
                //         i.problemLevel = e.ProblemLevel;
                //         return true;
                //     }
                // });
                if (hasChildren) {
                    children.forEach(o => {
                        delete o.children;
                    });
                }
            });
            this.items = children;
            if (type == 'push') return false;
            console.log(1111111111111111);
            this.alarmList = [];
            this.pageIndex = 1;
            this.getData();
        },
        // 根据产线code获取工段颜色
        async GetUgrecySectionCountByLineCode(areaCode) {
            const res = await GetUgrecySectionCountByLineCode({ areaCode });
            const { response } = res;
            return response || [];
        },
        //
        async setSectionCode(productLine, cd) {
            // 根据工段code获取工站颜色
            const res = await GetUgrecyStationCountBySectionCode({ productLine });
            const { response } = res;
            cd?.forEach(i => {
                const { value } = i;
                i.problemLevel = null;
                response.find(e => {
                    if (value == e.Device_Code) {
                        i.problemLevel = e.ProblemLevel;
                        return true;
                    }
                });
            });
        },
        // 获取告警树
        async getAlarmTypeTreetList() {
            const res = await getAlarmTypeTreetList({});
            const { success, response } = res || {};
            if (success) {
                response.forEach(i => {
                    const { children, hasChildren } = i;
                    this.typeChildList.push(i);
                    if (hasChildren) {
                        children.forEach(o => {
                            this.typeChildList.push(o);
                        });
                    }
                });
            }
        },
        // 获取大类列表
        async getDataList() {
            const res = await getAlarmTypeRootList({});
            const { success, response } = res || {};
            if (success) {
                this.alarmRootList = response;
                const obj = response.find(e => {
                    return e.AlarmCode == this.form.mainAlarmType;
                });
                if (obj) {
                    this.MainAlarmId = obj.ID;
                    this.getTypeChildList();
                }
            }
        },
        async getData() {
            const { ProductLineId, productLine, UnitId } = this.info;
            const params = { areaid: ProductLineId, productLine: productLine, unitId: UnitId, eventStatus: 'UNCLOSED' };
            const res = await AlarmRecordPageList({ ...params, pageIndex: this.pageIndex, pageSize: this.pageSizes, isMain: '1' });
            const { success, response } = res || {};
            const { data, dataCount } = response || {};
            if (success) {
                this.dataCount = dataCount;
                const arr = data || [];
                const { ID } = this.alarmSeletecedObj;
                if (ID) {
                    const o = arr.find(i => {
                        return i.ID == ID;
                    });
                    this.alarmSeletecedObj = o || {};
                }
                this.alarmList = arr.concat(this.alarmList);
            }
            this.moreLoading = false;
        },
        // 获取子级
        async getTypeChildList() {
            this.typeChildList = [];
            const res = await GetListByAlarmId({ alarmId: this.MainAlarmId });
            const { success, response } = res || {};
            if (success) {
                this.typeChildList = response;
            }
        },
        // 弹窗二次确认
        sureDelete() {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => { })
                .catch(err => {
                    console.log(err);
                });
        },
        // 报修, 升级
        showDetailDialog(type) {
            const o = this.alarmSeletecedObj;
            const ProductLineName = this.$getDictionaryVal(o.ProductLine, this.productLineList, 'EquipmentCode', 'EquipmentName');
            const detailObj = this.detailList.find(i => i.type == type);
            const EquipmentName = this.$getDictionaryVal(o.EquipmentCode, this.equipmentList, 'EquipmentCode', 'EquipmentName');
            this.maintenanceObj = { ...o, ProductLineName, detailObj, EquipmentName };
            this.$refs.maintenanceDialog.dialog = true;
        },
        // 处置和详情弹出框
        showHandleDialog(dialog) {
            const { ID, RecordStatus, SubAlarmType, MainAlarmType, CreateDate, ProblemLevel } = this.alarmSeletecedObj;
            if (!ID) return false;
            if (RecordStatus == 'CLOSED' && dialog == 'handleDialog') return this.$store.commit('SHOW_SNACKBAR', { text: '当前告警已关闭！', color: 'error' });
            const SubAlarm = this.$getDictionaryVal(SubAlarmType, this.typeChildList, 'AlarmCode', 'AlarmName');
            const MainAlarm = this.$getDictionaryVal(MainAlarmType, this.typeChildList, 'AlarmCode', 'AlarmName');
            const con = this.getContinueTime(CreateDate) + 'min';
            const problemLevel = this.$getDictionaryVal(ProblemLevel, this.ProblemLevelList, 'ItemValue', 'ItemName');
            this.maintenanceObj = { ...this.alarmSeletecedObj, MainAlarm, SubAlarm, con, problemLevel };
            this.$refs[dialog].dialog = true;
        },
        closePopup() {
            this.dialog = false;
            this.alarmSeletecedObj = {};
            this.$emit('handlePopup');
        },
        handlePopup() {
            this.alarmList = [];
            this.pageIndex = 1;
            this.getData();
        }
    }
};
</script>

<style lang="scss" scoped>
.alram-info-title {
    font-size: 18px;
    padding-bottom: 10px;
    padding-left: 16px;
    border-bottom: 1px solid gainsboro;
}

.alram-info-list {
    height: calc(100% - 30px);
    overflow: auto;
}

.alram-info-children {
    position: relative;
}

.alram-info-item {
    position: relative;
    cursor: pointer;
    height: 40px;
    line-height: 40px;
    // width: 100%;
    display: flex;

    .alram-info-folder {
        position: absolute;
        left: 4px;
        top: -2px;

        i {
            margin-right: 6px;
            font-size: 30px;
        }
    }
}

.item-hover {
    :hover {
        background: ghostwhite;
    }
}

.tuchu {
    color: red;
    font-weight: 600;
    font-size: 16px;
}

.zhkh {
    // color: blue;
}

.text-ellipsis {
    // font-size: 14px;
}

// .folder-span{
//     display: inline-block;
//     vertical-align: middle;
// }
// .active-alram-info {
//     background: #bdbdbd;
// }
::v-deep .v-sheet.v-card:not(.v-sheet--outlined) {
    box-shadow: none;
}

.warn-icon {
    color: #e53935;
}

.warn-status {
    font-weight: 600;
    color: #e53935;
}

.warn-status-close {
    font-weight: 600;
    color: green;
}

.activ-style {
    border: 1px solid #e0e0e0;
    color: black;
    font-weight: 500;
    border-radius: 0;
    // border-bottom: none;
}

.activ-height {
    height: 60px;
    border-radius: 0;
}

.sub-row-col {
    border-right: 1px solid #eeeeee;
    height: calc(100vh - 170px);
    overflow: auto;
}

.sub-row-left {
    justify-content: space-around;
    align-items: center;
}

.sub-row-opear {
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap;
}

.sub-row-btns {
    text-align: center;
    background: gainsboro;
    cursor: not-allowed;
    width: 8rem;
    margin-bottom: 1.4rem;
    height: 8rem;
    display: flex;
    flex-direction: column;
}

.sub-row-btns-active {
    cursor: pointer;
    background: rgba(61, 205, 88, 0.3);
}

// .sub-row-btns:last-child {
//     background: #fff;
// }

.sub-row-btn {
    font-size: 3rem;
    // height: 7rem;
    line-height: 5.6rem;
}

.alarm-list-item {
    padding-right: 10px;
    margin: 22px 10px 30px;
}

.alarm-list-item-active {
    background: rgba(123, 116, 224, 0.1);
}

.alarm-list-null {
    text-align: center;
    font-size: 20px;
    margin-top: 30px;

    .v-btn {
        font-size: 18px;
    }
}

.alarm-list-title {
    font-size: 16px;
    border-bottom: 1px solid #e5d4d4;
}

.alarm-list-status {
    font-size: 16px;
    // margin-top: 6px;
}

.alarm-list-content {
    line-height: 26px;
}

.alarm-list-des {
    height: 39px;
    overflow: auto;
}

.alarm-list-contact {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.replace-box {
    background: #fff;
    border: none;
    cursor: default;
}

.sbgc {
    background: rgb(61 205 88 / 0.2);
}

.rbgc {
    background: rgb(245 34 45 / 0.1);
}

.ybgc {
    background: rgb(245 255 0 / 0.1);
}

.gbgc {
    background: rgb(20 123 250 / 0.2);
}

.title-s {
    margin: 2px 2px 0;
}

.rfcl {
    color: rgb(245 34 45);
}

.yfcl {
    color: #c8cf25;
}

.sfcl {
    color: rgb(61 205 88);
}

.gfcl {
    color: rgb(20 123 250);
}

.font-s {
    font-size: 18px;
    font-weight: 500;
}
</style>
<style lang="scss">
.activ-style {
    .v-treeview {
        height: auto !important;
    }
}

.tobe-alarm-view {

    .col-2,
    .col-lg-2 {
        padding-right: 0;
        padding-left: 0;
    }
}
</style>
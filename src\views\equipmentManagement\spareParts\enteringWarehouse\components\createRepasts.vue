<template>
    <v-dialog v-model="showDialog" max-width="680px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                <!-- {{ $t('DFM_SJZD._FLGL') }} -->
                备件数量录入
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <!-- 表单内容 -->
            <v-card-text class="mt-4">
                <v-form ref="fromQR" v-model="valid" class="d-flex">
                    <v-row class="">
                        <v-col class="" cols="12">
                            <v-text-field v-model="fromQR.Remark" disabled outlined dense label="货架编号"></v-text-field>
                        </v-col>
                        <v-col class="" cols="12">
                            <v-text-field v-model="fromQR.Code" disabled outlined dense label="备件编号"></v-text-field>
                        </v-col>
                        <v-col class="" cols="12">
                            <v-text-field v-model="fromQR.BuyNum" outlined dense label="备件数量"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col class="ml-4" cols="24">
                            <v-btn color="primary" fab @click="getQRcodes">扫码录入</v-btn>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSparePartlist">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
            <QRcode ref="QRcode" @getQRcodesRes="getQRcodesRes"></QRcode>
        </v-card>
    </v-dialog>
</template>
<script>
import { addSparePart } from '@/api/equipmentManagement/sparePart.js';
export default {
    props: {
        dialogType: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            QRcode: '',
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            equipmentSpareType: [],
            fromQR: {
                Remark: '', //货架编号
                Code: '', // 备件编号
                BuyNum: null //入库数量
            }
        };
    },
    computed: {},
    created() {},
    methods: {
        // 扫码录入
        getQRcodes() {
            this.$refs.QRcode.getQRcode();
        },
        // 获取查询结果
        getQRcodesRes(value) {
            let val = JSON.parse(value.text);
            let { Remark, SparePartsCode } = val;
            this.QRcode = value.text;
            this.fromQR.Remark = Remark;
            this.fromQR.Code = SparePartsCode;
        },
        JsonparseQRcodes() {
            try {
                if (this.QRcode) {
                    let { Remark, SparePartsCode } = JSON.parse(this.QRcode);
                    this.fromQR.Remark = Remark;
                    this.fromQR.Code = SparePartsCode;
                }
            } catch (error) {
                console.log(error);
            }
        },
        // 扫码新增备件数量  addSparePart
        async addSparePartlist() {
            let params = {
                Remark: this.fromQR.Remark,
                Code: this.fromQR.Code,
                BuyNum: +this.fromQR.BuyNum
            };
            const res = await addSparePart(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '添加成功', color: 'success' });
                this.$parent.$parent.RepastInfoGetPage();
                this.$parent.$parent.RepastInfoLogGetPage();
                this.$refs.fromQR.reset();
                this.showDialog = this.classcheckbox ? false : true;
            }
        }
    }
};
</script>

import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_SHIFT; // 配置服务url
//  就餐信息列表
export function RepastInfoGetPageList(data) {
    return request({
        url: baseURL + '/shift/RepastInfo/GetPageList',
        method: 'post',
        data
    });
}
// 就餐信息列表-新增
export function RepastInfoSaveForm(data) {
    return request({
        url: baseURL + '/shift/RepastInfo/SaveForm',
        method: 'post',
        data
    });
}

// 就餐信息列表-删除
export function RepastInfoDelete(data) {
    return request({
        url: baseURL + '/shift/RepastInfo/Delete',
        method: 'post',
        data
    });
}





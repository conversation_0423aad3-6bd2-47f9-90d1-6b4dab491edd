export function getlinebydata(value) {
    let option = {
        color: ["#78BE20", "#0084D5", "#4E78A7"],
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(1, 13, 19, 0.9)',
            textStyle: {
                color: 'rgba(212, 232, 254, 1)',
            },
            formatter: function(params) {
                var relVal = params[0].marker + params[0].name + "：" + params[0].value + '%'
                return relVal
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            axisLabel: {
                color: '#DCDFE6',
                fontSize: 12
            },
            axisTick: {
                show: false,
            },
            axisLine: {
                lineStyle: {
                    color: '#DCDFE6'
                }
            },
            splitLine: {
                show: false,
            },
            data: value.xdata
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                color: 'rgb(196, 194, 194)',
                fontSize: 12
            },
            axisLine: {
                show: false,
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: 'rgb(196, 194, 194)',
                    type: 'dashed' //背景色为虚线
                },
            },
        },
        series: [{
            data: value.data,
            type: 'line',
            showAllSymbol: true,
            symbolSize: 15,
            smooth: true,
            tooltip: {
                show: true
            },
            label: {
                color: "#fff",
                show: true,
                formatter: (e) => {
                    return e.value + '%';
                },
            },
        }]
    };
    return option
}
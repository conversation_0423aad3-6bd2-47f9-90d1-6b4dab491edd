<template>
  <div>
    <div class="search-box">
      <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
        <el-form-item :label="$t('GLOBAL._SSL')">
          <el-input clearable v-model="searchForm.Key"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-refresh" @click="getSearchBtn">{{ $t('GLOBAL._SX2') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="success" icon="el-icon-circle-plus-outline"
            @click="showMaterialMappingDialog({})">{{
              $t('GLOBAL._XZ') }}
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="success" icon="el-icon-delete" @click="deleteRows">{{
            $t('GLOBAL._SC') }}
          </el-button>
        </el-form-item>
        <upload-button :option="buttonOption" :searchForm="searchForm" ref="uploadButton"></upload-button>
      </el-form>
    </div>
    <div class="table-box">
      <el-table v-loading="loading" :data="tableData" element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading" style="width: 100%" height="70vh"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55">
        </el-table-column>
        <section v-for="(item, index) in tableHead" :key="index">
          <el-table-column v-if="item.field === 'ID'" :prop="item.field" :label="item.label" width="300">
            <template slot-scope="scope">
              {{ scope.row[item.field] }}
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.field === 'Type'" :prop="item.field" :label="item.label">
            <template slot-scope="scope">
              <el-tag :type="scope.row[item.field] === 'Exclude' ? 'info' : 'success'" size="mini"> {{
                scope.row[item.field] }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column v-else :prop="item.field" :label="item.label"></el-table-column>
        </section>
        <el-table-column prop="operation" width="160" :label="$t('GLOBAL._ACTIONS')" align="center">
          <template slot-scope="scope">
            <i class="el-icon-edit-outline" @click="showMaterialMappingDialog(scope.row)"></i>
            <i class="el-icon-delete" @click="deleteRow(scope.row)"></i>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination class="mt-8p" background :current-page="searchForm.page" :page-size="searchForm.rows"
      layout="->, total, prev, pager, next" :total="total" @current-change="handleCurrentChange" />
    <MaterialMappingDialog ref="dialog" @saveForm="getSearchBtn" />
  </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';
import { getMaterialMappingList, materialMappingDelete } from "../service";
import MaterialMappingDialog from "./material-mapping-dialog.vue"
import {configUrl} from "@/config";
import {GetExportData} from "@/api/equipmentManagement/Equip";
import {GetExportFile} from "@/views/factoryPlant/physicalModelNew/service";
import UploadButton from "@/components/UploadButton.vue";
export default {
  name: 'materialMapping',
  components: {
    UploadButton,
    MaterialMappingDialog
  },
  props: {
    EquipmentId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchForm: {
        Key: '',
        pageIndex: 1,
        pageSize: 10
      },
      total: 0,
      loading: false,
      tableData: [],
      tableHead: [],
      hansObj: this.$t('Formula.Material_Mapping'),
      ids: [],
      uploadDisable:false,
      apiHost:configUrl[process.env.VUE_APP_SERVE]['baseURL_Resource'] + `/api/EquipmentMaterial/ImportData`,
      buttonOption:{
        name:'设备物料映射',
        serveIp:'baseURL_DFM',
        uploadUrl:'/api/EquipmentMaterial/ImportData', //导入
        //exportUrl:'/api/MaterialPropertyValue/ExportPlanPropValueData', //导出
        DownLoadUrl:'/api/EquipmentMaterial/DownLoadTemplate', //下载模板
      }
    }
  },
  mounted() {
    this.initTableHead()
    this.getTableData()
  },
  methods: {
    initTableHead() {
      for (let key in this.hansObj) {
        this.tableHead.push({ field: key, label: this.hansObj[key] })
      }
    },
    async getTableData() {
      this.loading = true
      const { response } = await getMaterialMappingList({
        EquipmentId: this.EquipmentId,
        ...this.searchForm
      })
      this.loading = false
      this.tableData = response.data
      this.total = response.dataCount
    },
    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },
    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },
    handleSelectionChange(selects) {
      this.ids = selects.map(item => item.ID)
    },
    deleteRow(row) {
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        const { msg } = await materialMappingDelete([row.ID])
        this.$message.success(msg)
        this.getTableData()
      }).catch(err => {
        console.log(err);
      });
    },
    deleteRows() {
      if (!this.ids.length) {
        this.$message.warning('请选择需要删除的数据！')
        return
      }
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        const { msg } = await materialMappingDelete(this.ids)
        this.$message.success(msg)
        this.getTableData()
      }).catch(err => {
        console.log(err);
      });
    },
    showMaterialMappingDialog(row) {
      this.$refs.dialog.show(row, this.EquipmentId)
    },
   
  },
}
</script>

<style lang="scss" scoped>
.search-box {
  background: #f5f5f5;
  border: 1px solid #e8e8e8;
  padding: 5px;
  border-bottom: 0;

}

.table-box {
  i {
    cursor: pointer;
    margin-right: 5px;
    font-size: 15px !important;
    color: #67c23a;
  }
}
::v-deep .el-tag.el-tag--success {
  color: #3dcd58 !important;
}
</style>

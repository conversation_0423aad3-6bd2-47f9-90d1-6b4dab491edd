import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_ANDON'
// 告警升级规则

//获取告警升级规则列表
export function getUpgradeRuleList(data) {
    const api =  '/andon/UpgradeRule/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑告警升级规则
export function UpgradeRuleSaveForm(data) {
    const api =  '/andon/UpgradeRule/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除告警升级规则
export function DeleteUpgradeRule(data) {
    const api =  '/andon/UpgradeRule/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取部门树形数据
export function getDepartment(data) {
    const api = '/api/Department/GetTree'
    return getRequestResources('baseURL_DFM', api, 'post', data)
}
// 获取部门全部数据
export function getDepartmentList(data) {
    const api = '/api/Department/GetList'
    return getRequestResources('baseURL_DFM', api, 'post', data)
}

// 获取负责人
export function StaffGetAndonPostList(data) {
    const api = '/api/Post/GetList'
    return getRequestResources('baseURL_DFM', api, 'post', data)
}


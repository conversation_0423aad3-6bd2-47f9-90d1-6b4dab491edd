<template>
    <div class="dictionary-view">
        <TreeView :items="treeData" :activeKey="' '" :title="$t('TPM_SBGL_SBTZGL._SBCX')" @clickClassTree="clickClassTree"></TreeView>
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <!-- <v-btn depressed color="primary" disabled style="position: relative">
                        <v-file-input
                            @change="beforeUpload"
                            accept=".xls,.xlsx"
                            style="position: absolute; z-index: 20; width: 200px; height: 66px; flex: none; background: none; opacity: 0"
                        ></v-file-input>
                        <v-icon class="pr-1">mdi-file-import-outline</v-icon>
                        批量导入
                    </v-btn> -->
                    <v-btn color="primary" v-has="'SBTZGL_ADD'" :disabled="papamstree.LineId == ''" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="primary" v-has="'SBTZGL_ALLREMOVE'" :disabled="!deleteList.length" @click="btnClickEvet('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn>
                    <!-- <v-btn color="primary" @click="QRcodeExport()">{{ $t('GLOBAL._EXPORTQR') }}</v-btn> -->
                    <v-btn color="primary" v-has="'SBTZGL_DR'" @click="handleImport()">{{ $t('GLOBAL._DR') }}</v-btn>
                    <v-btn color="primary" v-has="'SBTZGL_MBXZ'" @click="templateDownload">{{ $t('GLOBAL._MBXZ') }}</v-btn>
                    <v-btn color="primary" @click="handleExport()" v-has="'SBTZGL_DC'">{{ $t('GLOBAL._EXPORT') }}</v-btn>
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_SBTZGL"
                    ref="Tables"
                    :headers="EquipColum"
                    :clickFun="clickFun"
                    :desserts="desserts"
                    @selectePages="selectePages"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"
                ></Tables>
                <createRepast
                    ref="createRepast"
                    :peopleitems="peopleitems"
                    :typecodelist="typecodelist"
                    :statuslist="statuslist"
                    :cycleList="cycleList"
                    :equipmentGroup="equipmentGroup"
                    :equipmentStatus="equipmentStatus"
                    :productionlineList="productionlineList"
                    :dialogType="dialogType"
                    :tableItem="tableItem"
                ></createRepast>
            </v-card>
        </div>
        <el-drawer size="80%" :title="rowtableItem.Name + ' | ' + rowtableItem.Code" :wrapperClosable="false" :visible.sync="detailShow" direction="rtl">
            <v-card class="ma-1">
                <v-tabs v-model="tab" background-color="transparent">
                    <v-tab @click="changeTab(0)" key="0">{{ $t('TPM_SBGL_SBTZGL._BPBJQD') }}</v-tab>
                    <v-tab @click="changeTab(1)" key="1">{{ $t('TPM_SBGL_SBTZGL._SBBOM') }}</v-tab>
                    <v-tab @click="changeTab(2)" key="2">{{ $t('TPM_SBGL_SBTZGL._WJ') }}</v-tab>
                    <v-tab @click="changeTab(3)" key="3">{{ $t('TPM_SBGL_SBTZGL._SBLL') }}</v-tab>
                </v-tabs>
                <v-tabs-items v-model="tab">
                    <v-tab-item>
                        <sparepartsList ref="sparepartsList" :rowtableItem="rowtableItem"></sparepartsList>
                    </v-tab-item>
                    <v-tab-item>
                        <equipmentBom ref="equipmentBom" :rowtableItem="rowtableItem"></equipmentBom>
                    </v-tab-item>
                    <v-tab-item>
                        <equipmentFile ref="equipmentFile" :rowtableItem="rowtableItem"></equipmentFile>
                    </v-tab-item>
                    <!-- <v-tab-item>
                        <equipmentIndicator ref="equipmentIndicator" :rowtableItem="rowtableItem"></equipmentIndicator>
                    </v-tab-item> -->
                    <v-tab-item>
                        <equipmentRecord ref="equipmentRecord" :rowtableItem="rowtableItem"></equipmentRecord>
                    </v-tab-item>
                </v-tabs-items>
            </v-card>
        </el-drawer>
        <div class="loading-box" v-if="importLoading">
            <a-spin tip="导入中..." :spinning="importLoading"></a-spin>
        </div>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';
import { mixins } from '@/util/mixins.js';
import { GetListByLevel, EquipmentGetEquipmentTree } from '@/api/common.js';
import { FactorySiteGetTree } from '@/api/peopleManagement/workforceManagement.js';
import {
    GetDeviceStatus,
    GetDeviceCategory,
    GetDevicePageList,
    ExportData,
    DeviceImport,
    EquipGetPageList,
    DeviceDelete,
    EquipImport,
    EquipSaveForm,
    GetListCostCenter,
    GetListAssetMaster,
    EquipmentExport,
    GetExportData,
    GetPersonList
} from '@/api/equipmentManagement/Equip.js';
import { EquipColum } from '@/columns/equipmentManagement/Equip.js';
import { PrintTplGetPageList } from '@/api/systemManagement/printTemplate.js';
import { StaffSiteGetList } from '@/api/peopleManagement/basicdata.js';
import sparepartsList from './sparepartslist.vue';
import equipmentBom from './equipmentBom.vue';
import equipmentFile from './equipmentFile.vue';
import equipmentIndicator from './equipmentIndicator.vue';
import equipmentRecord from './equipmentRecord.vue';

import { configUrl } from '@/config';
const baseUrl = configUrl[process.env.VUE_APP_SERVE]['baseURL_TPM'] + '/tpm/Equip/ImportExcelTemplates';
const baseUrl2 = configUrl[process.env.VUE_APP_SERVE]['baseURL_EQUIPMENT'] + '/api/Device/DownLoadTemplate';
export default {
    name: 'RepastModel',
    components: {
        sparepartsList, //被品备件
        // equipmentIndicator, //设备指标
        equipmentBom, //设备BOM
        equipmentRecord, // 设备履历
        equipmentFile, // 设备文件
        createRepast: () => import('./components/createRepast.vue')
    },
    mixins: [mixins],
    data() {
        return {
            detailShow: false,
            // tree 字典数据
            importLoading: false,
            tab: 0,
            treeData: [],
            loading: false,
            showFrom: false,
            papamstree: {
                LineId: '',
                LineCode: '',
                Code: '',
                Name: '',
                AssetsNo: '',
                Status: '',
                DeviceUsingStatus: '',
                DeviceCategory: '',
                pageIndex: 1,
                pageSize: 20,
                orderByFileds: 'Code asc'
            },
            //查询条件
            EquipColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            rowtableItem: {},
            deleteList: [], //批量选中
            //类型
            productionlineList: [],
            floors: [],
            equipmentGroup: [], //设备组
            equipmentStatus: [], //设备状态
            cycleList: [],
            statuslist: [], //设备状态
            typecodelist: [], // 设备类型
            peopleitems: [],
            DeviceUsingStatus: [],
            DeviceByData: [],
            DeviceMngData: []
        };
    },
    computed: {
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'Code',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBTZGL._SBBH'),
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Name',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBTZGL._SBMC'),
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'AssetsNo',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBTZGL._ZCBH'),
                    placeholder: ''
                },
                {
                    value: '',
                    selectData: this.statuslist,
                    type: 'select',
                    key: 'Status',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBTZGL._ZT'),
                    placeholder: ''
                },
                {
                    value: '',
                    selectData: this.DeviceUsingStatus,
                    type: 'select',
                    key: 'DeviceUsingStatus',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.UseStatus'),
                    placeholder: ''
                },
                {
                    value: '',
                    selectData: this.typecodelist,
                    type: 'select',
                    key: 'DeviceCategory',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBTZGL._SBFL'),
                    placeholder: ''
                }
                // {
                //     value: '',
                //     key: 'eamCode',  // 此处查询的是列表的code字段，为避免跟左侧树里面的code冲突 所以用了eamCode
                //     icon: 'mdi-account-check',
                //     label: '',
                //     placeholder: 'EAM编号'
                // },
                // {
                //     value: '',
                //     key: 'eipCode',
                //     icon: 'mdi-account-check',
                //     label: '',
                //     placeholder: 'EIP编号'
                // },
                // {
                //     value: '',
                //     key: 'leaveCode',
                //     icon: 'mdi-account-check',
                //     label: '',
                //     placeholder: '出厂编号'
                // },
            ];
        },
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBTZGL_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBTZGL_DELETE'
                }
            ];
        },
        dictionaryList() {
            return [{ arr: this.cycleList, key: 'MyCycle', val: 'ItemValue', text: 'ItemName' }];
        }
    },
    async mounted() {
        let DeviceBy = await GetPersonList('DeviceBy');
        this.DeviceByData = DeviceBy.response[0].ChildNodes;
        this.DeviceByData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        let DeviceMng = await GetPersonList('DeviceMng');
        this.DeviceMngData = DeviceMng.response[0].ChildNodes;
        this.DeviceMngData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.statuslist = await this.$getNewDataDictionary('DeviceStatus');
        this.typecodelist = await this.$getNewDataDictionary('DeviceCategory');
        this.typecodelist.forEach(item => {
            item.ItemValue = item.ItemName;
        });
        let DeviceNature = await this.$getNewDataDictionary('DeviceNature');
        let IsImport = await this.$getNewDataDictionary('IsImport');
        let EntryType = await this.$getNewDataDictionary('EntryType');
        let DeviceClass = await this.$getNewDataDictionary('DeviceClass');
        let DeviceUnit = await this.$getNewDataDictionary('DeviceUnit');
        this.DeviceUsingStatus = await this.$getNewDataDictionary('DeviceUsingStatus');
        let CostCenter = await GetListCostCenter();
        CostCenter.response.forEach(item => {
            item.ItemName = item.Kostl;
            item.ItemValue = item.Kostl;
        });
        this.$refs.createRepast.SbxxList.forEach(item => {
            switch (item.id) {
                case 'DeviceCategory':
                    item.option = this.typecodelist;
                    break;
                case 'DeviceNature':
                    item.option = DeviceNature;
                    break;
                case 'DeviceClass':
                    item.option = DeviceClass;
                    break;
            }
        });
        this.$refs.createRepast.SbsmList.forEach(item => {
            switch (item.id) {
                case 'CostCenter':
                    item.option = CostCenter.response;
                    break;
                case 'Status':
                    item.option = this.statuslist;
                    break;
                case 'IsImport':
                    item.option = IsImport;
                    break;
                case 'EntryType':
                    item.option = EntryType;
                    break;
                case 'Unit':
                    item.option = DeviceUnit;
                    break;
                case 'UsingStatus':
                    item.option = this.DeviceUsingStatus;
                    break;
                case 'Header':
                    item.option = this.DeviceByData;
                    break;
                case 'Manager':
                    item.option = this.DeviceMngData;
                    break;
            }
        });
        this.queryPeoplelist();
        this.GetFactorylineTree();
        this.getProductionlineList();
        this.getProductionlineList();
    },
    methods: {
        async handleExport() {
            let params = {
                ...this.papamstree
            };
            const baseUrl3 = configUrl[process.env.VUE_APP_SERVE]['baseURL_EQUIPMENT'] + `/api/Device/ExportData`;
            let res = await GetExportData(baseUrl3, params);
            let binaryData = [];
            binaryData.push(res);
            const url = window.URL.createObjectURL(new Blob(binaryData));
            console.log(url);
            const link = document.createElement('a');
            link.href = url;
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            let fileName = `设备台账${formattedDateTime}.xlsx`;
            document.body.appendChild(link);
            link.setAttribute('download', fileName);
            link.click();
            window.URL.revokeObjectURL(link.href);
        },
        async updateStatus(item) {
            try {
                let params = JSON.parse(JSON.stringify(item));
                params.Status = params.Status ? 1 : 0;
                await EquipSaveForm(params);
                this.$store.commit('SHOW_SNACKBAR', { text: '修改成功', color: 'success' });
            } catch {
                this.RepastInfoGetPage();
            }
        },
        // 获取人员
        async queryPeoplelist() {
            const res = await StaffSiteGetList({ key: '' });
            let { success, response } = res;
            if (success) {
                this.peopleitems = response;
            }
        },
        // 导入模板下载
        async templateDownload() {
            window.open(baseUrl2, '_blank');
        },
        handleImport() {
            let _this = this;
            let input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xls,.xlsx';
            input.click();
            let factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            input.onchange = async function () {
                let file = input.files[0];
                let formdata = new FormData();
                formdata.append('file', file);

                _this.importLoading = true;
                try {
                    let res = await DeviceImport(formdata, factory);
                    _this.$store.commit('SHOW_SNACKBAR', { text: res.response });
                    _this.RepastInfoGetPage();
                    _this.importLoading = false;
                } catch {
                    _this.importLoading = false;
                }
            };
        },
        // 导入文件
        beforeUpload(file) {
            console.log(file);
        },
        changeTab(v) {
            switch (v) {
                case 0:
                    setTimeout(() => {
                        this.$refs.sparepartsList.GetSparepartGetPageList(this.rowtableItem, true);
                    }, 10);
                    break;
                case 1:
                    setTimeout(() => {
                        this.$refs.equipmentBom.RepastBOMlistTARGetPage(this.rowtableItem, true);
                    }, 10);
                    break;
                case 2:
                    setTimeout(() => {
                        this.$refs.equipmentFile.RepastInfoTARGetPage(this.rowtableItem, true);
                    }, 10);
                    break;
                case 3:
                    setTimeout(() => {
                        this.$refs.equipmentRecord.tab = 0;
                        this.$refs.equipmentRecord.getRecordList(this.rowtableItem, true);
                    }, 10);
                    break;
            }
        },
        //  查看BOM详情
        clickFun(data) {
            this.tableItem = data;
            this.rowtableItem = data || {};
            this.detailShow = true;
            switch (this.tab) {
                case 0:
                    setTimeout(() => {
                        this.$refs.sparepartsList.GetSparepartGetPageList(this.rowtableItem, true);
                    }, 10);
                    break;
                case 1:
                    setTimeout(() => {
                        this.$refs.equipmentBom.RepastBOMlistTARGetPage(this.rowtableItem, true);
                    }, 10);
                    break;
                case 2:
                    setTimeout(() => {
                        this.$refs.equipmentFile.RepastInfoTARGetPage(this.rowtableItem, true);
                    }, 10);
                    break;
                case 3:
                    setTimeout(() => {
                        this.$refs.equipmentRecord.tab = 0;
                        this.$refs.equipmentRecord.getRecordList(this.rowtableItem, true);
                    }, 10);
                    break;
            }
        },
        // 树状点击获取
        clickClassTree(v) {
            this.papamstree.pageIndex = 1;
            if (v.id == this.papamstree.LineId) {
                this.papamstree.LineId = '';
                this.papamstree.LineCode = '';
            } else {
                this.papamstree.LineId = v.id;
                this.papamstree.LineCode = v.name;
            }
            this.RepastInfoGetPage();
        },
        // 获取树形数据
        async GetFactorylineTree() {
            this.loading = true;
            const res = await EquipmentGetEquipmentTree();
            let { success, response } = res;
            if (success) {
                this.treeData = response || [];
                this.papamstree.LineId = '';
            }
            this.RepastInfoGetPage();
        },
        // 获取产线列表
        async getProductionlineList() {
            const res = await GetListByLevel({ key: 'Area' });
            const { success, response } = res || {};
            if (response && success) this.productionlineList = response;
            else this.productionlineList = [];
        },
        //

        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.RepastInfoGetPage();
        },
        // 设备列表查询
        async RepastInfoGetPage() {
            let params = {
                ...this.papamstree
            };
            const res = await GetDevicePageList(params);
            let { success, response } = res;
            response.data.forEach(item => {
                if (item.Header == null) {
                    item.Header = '';
                }
                if (item.Manager == null) {
                    item.Manager = '';
                }
                let str = '';
                if (item.Header.indexOf('|') != -1) {
                    let arr = item.Header.split('|');
                    this.DeviceByData.forEach(it => {
                        arr.forEach(k => {
                            if (k == it.ItemValue) {
                                str += it.ItemName + ' ';
                            }
                        });
                    });
                    item.Header = str;
                    item.HeaderValue = arr.join('|');
                } else {
                    this.DeviceByData.forEach(it => {
                        if (item.Header == it.ItemValue) {
                            item.Header = it.ItemName;
                            item.HeaderValue = it.ItemValue;
                        }
                    });
                }

                let str2 = '';
                if (item.Manager.indexOf('|') != -1) {
                    let arr = item.Manager.split('|');
                    this.DeviceMngData.forEach(it => {
                        arr.forEach(k => {
                            if (k == it.ItemValue) {
                                str2 += it.ItemName + ' ';
                            }
                        });
                    });
                    item.Manager = str2;
                    item.ManagerValue = arr.join('|');
                } else {
                    this.DeviceMngData.forEach(it => {
                        if (item.Manager == it.ItemValue) {
                            item.Manager = it.ItemName;
                            item.ManagerValue = it.ItemValue;
                        }
                    });
                }
                this.statuslist.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                        item.StatusValue = it.ItemValue;
                    }
                });
                this.DeviceUsingStatus.forEach(it => {
                    if (item.UsingStatus == it.ItemValue) {
                        item.UsingStatus = it.ItemName;
                        item.UsingStatusValue = it.ItemValue;
                    }
                });
            });

            // response.data.forEach(item => {
            //     for (let k in item) {
            //         if(item[k] != null){
            //             if(item[k].indexOf('00:00:00') != -1){
            //                 item[k].replace('00:00:00','')
            //             }
            //         }
            //     }
            // });
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.$refs.createRepast.SbxxList.forEach(item => {
                        item.value = '';
                    });
                    this.$refs.createRepast.SbsmList.forEach(item => {
                        item.value = '';
                    });
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    this.$refs.createRepast.LineId = this.papamstree.LineId;
                    this.$refs.createRepast.LineCode = this.papamstree.LineCode;
                    this.$refs.createRepast.GetFactorylineTree();
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.dialogType = 'addedit';
                    for (let k in this.tableItem) {
                        this.$refs.createRepast.SbxxList.forEach(item => {
                            if (k == item.id) {
                                item.value = this.tableItem[k];
                            }
                        });
                        this.$refs.createRepast.SbsmList.forEach(item => {
                            if (k == item.id) {
                                if (item.id == 'Status') {
                                    item.value = this.tableItem.StatusValue;
                                } else if (item.id == 'UsingStatus') {
                                    item.value = this.tableItem.UsingStatusValue;
                                } else if (item.id == 'Header') {
                                    item.value = this.tableItem.HeaderValue.split('|');
                                } else if (item.id == 'Manager') {
                                    item.value = this.tableItem.ManagerValue.split('|');
                                } else {
                                    item.value = this.tableItem[k];
                                }
                            }
                        });
                    }
                    this.$refs.createRepast.showDialog = true;
                    this.$refs.createRepast.DeviceCategoryId = this.tableItem.DeviceCategoryId;
                    this.$refs.createRepast.LineId = this.papamstree.LineId;
                    this.$refs.createRepast.activeKey = this.tableItem.DeviceCategoryId;
                    return;
                case 'delete':
                    this.deltable();
                    return;
                case 'printCode':
                    console.log(item);
                    this.$nextTick(this.PrintTemplateFn(item));
            }
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await DeviceDelete(params);
                    if (res.success) {
                        this.tableItem = {};
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.RepastInfoGetPage();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + item);
            this.deleteList = item;
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}

.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

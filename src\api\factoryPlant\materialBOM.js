// 物料BOM相关接口
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM'

//新增、编辑物料BOM
export function saveForm(data) {
    const api = '/api/MaterialBom/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取物料BOM列表
export function GetPageList(data) {
    const api = '/api/MaterialBom/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 删除物料BOM
export function DeleteMaterials(data) {
    const api = '/api/MaterialBom/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

//新增、编辑物料BOM明细
export function saveDetailForm(data) {
    const api = '/api/MaterialBomDetail/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取物料BOM明细列表
export function GetDetailPageList(data) {
    const api = '/api/MaterialBomDetail/GetTreeListByBomId'
    return getRequestResources(baseURL, api, 'post', data, true);
}

// 删除物料BOM明细
export function DeleteMaterialsDetail(data) {
    const api = '/api/MaterialBomDetail/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

//上级物料 子物料号
export function MaterialGetList(data) {
    const api = '/api/Material/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 成品料号
export function GetClassifyTree(data) {
    const api = '/api/RoutingProduct/GetClassifyTree'
    return getRequestResources(baseURL, api, 'get', data);
}

// 获取单位下拉框数据
export function getUnitList(data) {
    const api = '/api/Unitmanage/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取物料前 n 个
export function getMaterialTopN(data) {
    const api = '/api/Material/GetTopNList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 根据主bom ID查询对应父级物料列表数据
export function getParentMaterial(data) {
    const api = '/api/MaterialBomDetail/GetParentList'
    return getRequestResources(baseURL, api, 'post', data, true);
}

// 查询物料分类下拉框列表
export function getSelectClassifyList(data) {
    const api = '/api/Category/GetList'
    return getRequestResources(baseURL, api, 'post', data, true);
    // return request({
    //     url: baseURL + '/api/Category/GetList?Identities=' + data.Identities,
    //     method: 'post',
    //     data
    // });
}
// 同步操作
export function doSynchronization(data) {
    const api = '/api/MaterialBom/SyncBom'
    return getRequestResources(baseURL, api, 'post', data, true);
}
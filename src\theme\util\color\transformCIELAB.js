"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.fromXYZ = fromXYZ;
exports.toXYZ = toXYZ;
var delta = 0.20689655172413793; // 6÷29

var cielabForwardTransform = function cielabForwardTransform(t) {
  return t > Math.pow(delta, 3) ? Math.cbrt(t) : t / (3 * Math.pow(delta, 2)) + 4 / 29;
};

var cielabReverseTransform = function cielabReverseTransform(t) {
  return t > delta ? Math.pow(t, 3) : 3 * Math.pow(delta, 2) * (t - 4 / 29);
};

function fromXYZ(xyz) {
  var transform = cielabForwardTransform;
  var transformedY = transform(xyz[1]);
  return [116 * transformedY - 16, 500 * (transform(xyz[0] / 0.95047) - transformedY), 200 * (transformedY - transform(xyz[2] / 1.08883))];
}

function toXYZ(lab) {
  var transform = cielabReverseTransform;
  var Ln = (lab[0] + 16) / 116;
  return [transform(Ln + lab[1] / 500) * 0.95047, transform(Ln), transform(Ln - lab[2] / 200) * 1.08883];
}
//# sourceMappingURL=transformCIELAB.js.map
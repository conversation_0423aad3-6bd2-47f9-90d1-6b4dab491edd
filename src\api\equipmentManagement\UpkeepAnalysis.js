import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';
// 近期待保养任务
export function GetMaintainWoList(data) {
    const api = '/api/DeviceChart/GetMaintainWoList';
    return getRequestResources(baseURL, api, 'post', data);
}
//获取超期维保设备列表
export function GetDeviceChartMaintainOverdue(data) {
    const api = '/api/DeviceChart/GetMaintainOverdue';
    return getRequestResources(baseURL, api, 'post', data);
}

//超期维保设备数量
export function GetDeviceChartGetBarChartOverdue(data) {
    const api = '/api/DeviceChart/GetBarChartOverdue';
    return getRequestResources(baseURL, api, 'post', data);
}
//获取保养数量
export function GetDeviceChartGetMaintainQty(data) {
    const api = '/api/DeviceChart/GetMaintainQty';
    return getRequestResources(baseURL, api, 'post', data);
}
//获取保养率
export function GetDeviceChartGetMaintainRate(data) {
    const api = '/api/DeviceChart/GetMaintainRate';
    return getRequestResources(baseURL, api, 'post', data);
}
<template>
    <div class="add-function">
        <a-form :model="form" :label-col="{ span: 8, }" :wrapper-col="{ span: 14, }">
            <a-form-item label="Name">
                <a-input v-model="form.Name" />
            </a-form-item>
            <a-form-item label="Description">
                <a-input v-model="form.Description" />
            </a-form-item>
            <a-form-item label="Action Class">
                <a-input v-model="form.OpcActionClassId" />
                <!-- <a-select v-model="form.OpcActionClassId" placeholder="please select your log transactions">
                    <a-select-option v-for="item in actionClassList" :key="item.ID" :value="item.ID">{{ item.ClassName
                    }}</a-select-option>
                </a-select> -->
            </a-form-item>
            <a-form-item label="Timeout(ms)">
                <a-input type="number" v-model="form.Timeout" />
            </a-form-item>
            <a-form-item label="Log Transactions">
                <a-select show-search v-model="form.LogTransactions" placeholder="please select your log transactions">
                    <a-select-option value="1">Enabled</a-select-option>
                    <a-select-option value="0">Disabled</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="Block Read">
                <a-select show-search v-model="form.BlockRead" placeholder="please select your block read">
                    <a-select-option value="1">Enabled</a-select-option>
                    <a-select-option value="0">Disabled</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="Allow Multiple Instances">
                <a-select show-search v-model="form.AllowMultipleInstances" placeholder="please select your allow multiple instances">
                    <a-select-option value="1">Enabled</a-select-option>
                    <a-select-option value="0">Disabled</a-select-option>
                </a-select>
            </a-form-item>
        </a-form>
    </div>
</template>

<script>
import { getActionClass } from '../service'
export default {
    props: {
        editItemObj: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            actionClassList: [],
            form: {
                Name: '',
                Description: '',
                OpcActionClassId: undefined,
                Timeout: '',
                LogTransactions: undefined,
                AllowMultipleInstances: undefined,
                BlockRead: undefined
            }
        }
    },
    created() {
        if (this.editItemObj && this.editItemObj.ID) {
            for (const key in this.form) {
                this.form[key] = this.editItemObj[key]
            }
            this.form.ID = this.editItemObj.ID
        }
        // this.getActionClassList()
    },
    beforeDestroy() {
        // this.resetForm()
    },
    methods: {
        resetForm() {
            this.form = {
                Name: '',
                Description: '',
                OpcActionClassId: undefined,
                Timeout: '',
                LogTransactions: undefined,
                AllowMultipleInstances: undefined,
                BlockRead: undefined
            }
        },
        async getActionClassList() {
            let resp = await getActionClass({})
            this.actionClassList = resp.response
        }
    }
}
</script>
<style lang="scss" scoped>
.ant-row.ant-form-item {
    margin-bottom: 10px;
}
</style>

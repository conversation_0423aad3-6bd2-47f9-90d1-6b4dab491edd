<template>
    <v-card>
        <v-card-title class="text-h6 justify-space-between primary lighten-2">
            {{ $t('GLOBAL._PLXG') }}
            <v-icon>mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid">
                <v-row class="mt-5">
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" v-model="form.StartTime"
                            type="datetime-local" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYJH.StartTime')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" v-model="form.EndTime"
                            type="datetime-local" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYJH.EndTime')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-autocomplete :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" v-model="form.ExecutorCode"
                            :items="peopleitems" item-value="Code" item-text="Name" multiple return-object flat outlined
                            dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYJH.Executor')">
                            <template #item="data">
                                <template v-if="(typeof data.item) !== 'object'">
                                    <v-list-item-content v-text="data.item"></v-list-item-content>
                                </template>
                                <template v-else>
                                    <v-list-item-content>
                                        <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                        <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                    </v-list-item-content>
                                </template>
                            </template>
                            <template v-slot:selection="{ item, index }">
                                <span v-if="index === 0">{{ item.Name }}</span>
                                <span v-if="index === 1">&nbsp;&nbsp;</span>
                                <span v-if="index === 1" class="grey--text caption">(+{{ form.ExecutorCode.length -
                                    1 }} )</span>
                            </template>
                        </v-autocomplete>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
        <v-divider></v-divider>

        <v-card-actions>
            <!-- <v-spacer></v-spacer> -->
            <v-checkbox class="mr-auto" v-model="isChecked" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { StaffSiteGetList } from '@/api/peopleManagement/basicdata.js';
import { batchEditSpotCheckPlan } from '@/api/equipmentManagement/upkeep.js'
export default {
    props: {
        selected: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            valid: false,
            peopleitems: [],
            isChecked: true,
            form: {
                StartTime: '',
                EndTime: '',
                ExecutorCode: ''
            }
        }
    },
    created() {
        this.queryPeoplelist()
    },
    methods: {
        async submitForm() {
            if (!this.$refs.form.validate()) return false;
            let params = JSON.parse(JSON.stringify(this.form))
            let ids = this.selected.map(item => item.ID)
            params.Id = ids.join(',')
            if (this.form.ExecutorCode && this.form.ExecutorCode.length) {
                let Code = this.form.ExecutorCode.map(item => item.Code).join(',')
                let Name = this.form.ExecutorCode.map(item => item.Name).join(',')
                params.Executor = Name
                params.ExecutorCode = Code
            } else {
                params.Executor = ''
                params.ExecutorCode = ''
            }
            let resp = await batchEditSpotCheckPlan({ ...params })
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
            this.resetForm();
            this.$emit('getdata');
            if (this.isChecked) {
                this.$emit('closePopup');
            }
        },
        resetForm() {
            this.form = {
                StartTime: '',
                EndTime: '',
                ExecutorCode: ''
            }
        },
        closePopup() {
            this.$emit('closePopup')
        },
        // 获取人员
        async queryPeoplelist() {
            const res = await StaffSiteGetList({ key: '' });
            let { success, response } = res;
            if (success) {
                this.peopleitems = response;
            }
        }
    }
}
</script>

<style></style>
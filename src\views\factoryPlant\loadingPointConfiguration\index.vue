<template>
    <div class="disassemble-defective-products">
        <SearchForm class="mt-1" :show-from="showForm" @selectChange="selectChange" :searchinput="searchInputs" @searchForm="searchForm" />
        <div class="form-btn-list">
            <v-btn icon class="float-left mx-4" @click="showForm = !showForm">
                <v-icon>{{ 'mdi-table-search' }}</v-icon>
                {{ $t('GLOBAL._SSL') }}
            </v-btn>
            <v-btn icon color="primary" @click="getdata">
                <v-icon>mdi-cached</v-icon>
            </v-btn>
            <v-btn color="primary" v-has="'SLDPZ_MBXZ'" @click="templateDownload()">{{ $t('DFM_JYXM._DRMBXZ') }}</v-btn>
            <v-btn color="primary" v-has="'SLDPZ_DR'" @click="handleImport()">{{ $t('DFM_JYXM._DR') }}</v-btn>
            <v-btn color="primary" v-has="'SLDPZ_DC'" @click="handleExport()">{{ $t('GLOBAL._EXPORT') }}</v-btn>
            <v-btn color="primary" v-has="'SLDPZ_JY'" @click="batchHandleEnable(0)">{{ $t('GLOBAL._JY') }}</v-btn>
            <v-btn color="primary" v-has="'SLDPZ_QY'" @click="batchHandleEnable(1)">{{ $t('GLOBAL._QY') }}</v-btn>
            <v-btn color="primary" v-has="'SLDPZ_ADD'" @click="add()">{{ $t('GLOBAL._XZ') }}</v-btn>
            <v-btn color="primary" v-has="'SLDPZ_ALLREMOVE'" @click="batchDel()">{{ $t('GLOBAL._PLSC') }}</v-btn>
        </div>
        <Tables ref="table" table-name="DFM_SLDPZ" :page-options="pageOptions" :loading="loading" :headers="headers" :desserts="tableList" @selectePages="selectePages" @tableClick="tableClick">
            <template #Tagert="{ item }">
                <span>{{ item.Tagert * 100 + '%' }}</span>
            </template>
            <!-- <template #Enable="{ item }">
                <span>{{ item.Enable == '1' ? '是' : '否' }}</span>
            </template> -->
        </Tables>

        <v-dialog scrollable persistent v-model="isShowEditPopup" width="55%">
            <EditPopup :lineList="lineList" :materialList="materialList" @closePopup="closePopup" @getdata="getdata" v-if="isShowEditPopup" :editItemObj="editItemObj" />
        </v-dialog>
        <div class="loading-box" v-if="importLoading">
            <a-spin tip="导入中..." :spinning="importLoading"></a-spin>
        </div>
    </div>
</template>

<script>
import { getLoadingPointConfigList, delLoadingPointConfig, doImport, getMaterialList, saveForm, handleChangeEnable, doExport } from './service';
import Util from '@/util';
import EditPopup from './components/editPopup.vue';
import { loadingPointConfigColumns } from '@/columns/factoryPlant/tableHeaders';
import { configUrl } from '@/config';
const baseUrl = configUrl[process.env.VUE_APP_SERVE]['baseURL_DFM'] + '/api/RelMaterialTag/ImportExcelTemplates';
const EnabledList = [
    { Code: 1, NAME: '是' },
    { Code: 0, NAME: '否' }
];
export default {
    components: {
        EditPopup
    },
    data() {
        return {
            searchSegmentList: [],
            materialList: [],
            importLoading: false,
            lineList: [],
            segmentList: [],
            isShowEditPopup: false,
            editItemObj: {},
            loading: false,
            showForm: false,
            paramsObj: {},
            tableList: [],
            headers: loadingPointConfigColumns,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100]
            }
        };
    },
    computed: {
        btnList() {
            return [
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary', authCode: 'SLDPZ_EDIT' },
                { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'red', authCode: 'SLDPZ_DELETE' }
            ];
        },
        searchInputs() {
            let list = [
                {
                    value: '',
                    icon: 'mdi-account-check',
                    label: this.$t('DFM_SLDPZ.ProductlineName'),
                    placeholder: '',
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.lineList, 'EquipmentCode', 'EquipmentName'),
                    key: 'productline'
                },
                {
                    value: '',
                    icon: 'mdi-account-check',
                    label: this.$t('DFM_SLDPZ.SegmentName'),
                    placeholder: '',
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.searchSegmentList, 'EquipmentCode', 'EquipmentName'),
                    key: 'segment'
                },
                {
                    value: '',
                    icon: 'mdi-account-check',
                    label: this.$t('DFM_SLDPZ.MaterialName'),
                    placeholder: '',
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.materialList, 'Code', 'NAME'),
                    key: 'materialcode'
                },
                {
                    key: 'isenabled',
                    type: 'combobox',
                    selectData: this.$changeSelectItems(EnabledList, 'Code', 'NAME'),
                    value: '',
                    icon: '',
                    label: this.$t('DFM_SLDPZ.Enable')
                }
            ];
            return list;
        }
    },
    created() {
        this.getMaterial();
        this.getLineData();
        this.getSegmentData();
        this.getdata();
    },
    methods: {
        async handleExport() {
            let resp = await doExport({ ...this.paramsObj, responseType: 'blob' });
            console.log('====>', resp);
            let blob = new Blob([resp], {
                type: 'application/octet-stream'
            });
            var link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.download = '上料点配置.xls';
            link.click();
            //释放内存
            window.URL.revokeObjectURL(link.href);
        },
        selectChange(item, data) {
            let key = item.key;
            let id = '';
            if (key != 'productline') return false;
            switch (key) {
                case 'productline':
                    this.searchSegmentList = [];
                    if (item.value) {
                        id = item.value.ID;
                        this.searchSegmentList = this.segmentList.filter(item => item.ParentId == id);
                    }
                    break;
                // case 'Segment':
                //     this.searchWorkstationList = [];
                //     if (item.value) {
                //         id = item.value.ID;
                //         this.searchWorkstationList = this.workstationList.filter(item => item.ParentId == id);
                //     }
                //     break;
            }
            for (const key in data) {
                this.searchInputs.map(item => {
                    if (item.key == key) {
                        item.value = data[key];
                    }
                    if (item.key == 'segment') {
                        item.value = '';
                    }
                });
            }
        },
        // 获取成品物料列表
        async getMaterial() {
            let resp = await getMaterialList({ type: '', pageIndex: 1, pageSize: 999999 });
            this.materialList = resp.response.data;
        },
        handleImport() {
            let _this = this;
            let input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xls,.xlsx';
            input.click();
            input.onchange = async function () {
                let file = input.files[0];
                let formdata = new FormData();
                formdata.append('file', file);

                _this.importLoading = true;
                try {
                    await doImport(formdata);
                    _this.$store.commit('SHOW_SNACKBAR', { text: '导入成功', color: 'success' });
                    _this.getdata();
                    _this.importLoading = false;
                } catch {
                    _this.importLoading = false;
                }
            };
        },
        // 导入模板下载
        async templateDownload() {
            window.open(baseUrl, '_blank');
        },
        // 获取产线列表
        async getLineData() {
            this.lineList = await Util.GetEquipmenByLevel('Area');
        },
        // 工段
        async getSegmentData() {
            this.segmentList = await Util.GetEquipmenByLevel('ProductLine');
        },
        closePopup() {
            this.isShowEditPopup = false;
        },
        add() {
            this.editItemObj = {};
            this.isShowEditPopup = true;
        },
        // 批量禁用/启用
        async batchHandleEnable(val) {
            let selecteds = this.$refs.table.selected;
            if (selecteds.length === 0) {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
                return false;
            }
            let ids = selecteds.map(item => item.ID);
            try {
                await handleChangeEnable({
                    ids,
                    isenabled: val
                });
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
                this.getdata();
            } catch {
                this.getdata();
            }
        },
        // 批量删除
        batchDel() {
            let selecteds = this.$refs.table.selected;
            if (selecteds.length === 0) {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
                return false;
            }
            let ids = [];
            selecteds.forEach(item => {
                ids.push(item.ID);
            });
            this.delData(ids);
        },
        delData(ids) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let resp = await delLoadingPointConfig(ids);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });

                    this.getdata();
                })
                .catch(() => {});
        },
        async getdata() {
            this.loading = true;
            try {
                let resp = await getLoadingPointConfigList({ ...this.paramsObj, pageIndex: this.pageOptions.page, pageSize: this.pageOptions.pageSize });
                this.loading = false;
                this.$refs.table.selected = [];
                this.tableList = resp.response.data;
                this.pageOptions.pageCount = resp.response.pageCount;
                this.pageOptions.total = resp.response.dataCount;
            } catch {
                this.loading = false;
            }
        },
        searchForm(params) {
            this.paramsObj = params;
            this.pageOptions.page = 1;
            this.getdata();
        },
        // 分页操作
        selectePages(data) {
            this.pageOptions.page = data.pageCount;
            this.pageOptions.pageSize = data.pageSize;
            this.getdata();
        },
        tableClick(item, type) {
            switch (type) {
                case 'edit':
                    this.editItemObj = item;
                    this.isShowEditPopup = true;
                    break;
                case 'delete':
                    this.delData([item.ID]);
                    break;
                // 启用。禁用
                case 'Enabled':
                    this.setEnabled(item);
                    break;
            }
        },
        async setEnabled(o) {
            const par = { ...o, Enable: o.Enable ? 1 : 0 };
            try {
                const res = await saveForm(par);
                const { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                } else {
                    this.getdata();
                }
            } catch {
                this.getdata();
            }
        }
    }
};
</script>

<style></style>
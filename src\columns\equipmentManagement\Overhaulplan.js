export const OverhaulplanColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
}, {
    text: '年度',
    value: 'Year',
    Namevalue: "nd",
    width: 100,
    sortable: true
}, {
    text: '月份',
    value: 'Month',
    Namevalue: "yf",
    width: 100,
    sortable: true
}, {
    text: '周别',
    value: 'Weekly',
    Namevalue: "zb",
    width: 100,
    sortable: true
},{
    text: '产线',
    value: 'Line',
    Namevalue: "cx",
    width: 100,
    sortable: true
}, {
    text: '大修内容',
    value: 'OverhaulContent',
    Namevalue: "dxnr",
    width: 150,
    sortable: true
}, {
    text: '计划开始日期',
    value: 'PlanStartDate',
    Namevalue: "jhksrq",
    width: 150,
    sortable: true
}, {
    text: '计划结束日期',
    value: 'PlanFinishDate',
    Namevalue: "jhjsrq",
    width: 150,
    sortable: true
},  {
    text: '状态',
    value: 'Status',
    Namevalue: "zt",
    width: 120,
    sortable: true
}, {
    text: '备注',
    value: 'Remark',
    Namevalue: "comment",
    width: 150,
    sortable: true
}, {
    text: '操作',
    value: 'actions',
    Namevalue: "action",
    width: 150,
    sortable: true
},]


export const OverhaulTaskColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
}, {
    text: '大修工单号',
    value: 'OverhaulWo',
    Namevalue: "dxgdh",
    width: 150,
    sortable: true
}, {
    text: '设备名称',
    value: 'DeviceName',
    Namevalue: "sbmc",
    width: 150,
    sortable: true
},  {
    text: '任务内容',
    value: 'OverhaulContent',
    Namevalue: "rwrn",
    width: 150,
    sortable: true
}, {
    text: '计划开始日期',
    value: 'PlanStartDate',
    Namevalue: "jhks",
    width: 150,
    sortable: true
}, {
    text: '计划结束日期',
    value: 'PlanFinishDate',
    Namevalue: "jhjs",
    width: 150,
    sortable: true
}, {
    text: '状态',
    value: 'Status',
    Namevalue: "zt",
    width: 100,
    sortable: true
}, {
    text: '实际开始日期',
    value: 'StartDate',
    Namevalue: "sjks",
    width: 150,
    sortable: true
}, {
    text: '实际结束日期',
    value: 'FinishDate',
    Namevalue: "sjjs",
    width: 150,
    sortable: true
}, {
    text: '负责人',
    value: 'TaskBy',
    Namevalue: "fzr",
    width: 150,
    sortable: true
}, {
    text: '备注',
    value: 'Remark',
    Namevalue: "comment",
    width: 150,
    sortable: true
}, {
    text: '操作',
    value: 'actions',
    Namevalue: "action",
    width: 150,
    sortable: true
},]
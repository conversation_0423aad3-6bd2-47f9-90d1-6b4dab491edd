<template>
    <div class="properties">
        <v-row class="tool-row">
            <v-col :cols="12" :lg="3">
                <a-input-search v-model="keywords" enter-button placeholder="Quick Search" @search="onSearch" />
            </v-col>
            <v-col :cols="12" :lg="9" class="pl-0">
                <v-btn @click="getdata">
                    <v-icon left>mdi-cached</v-icon>
                    Refresh</v-btn>
                <v-btn style="margin-left:10px" @click="addProperty()" color="primary">
                    <v-icon left>mdi-plus</v-icon>
                    New</v-btn>
            </v-col>
        </v-row>
        <div class="table" style="margin-top:10px">
            <vxe-table height="500px" class="mytable-scrollbar" :loading="loading" size="mini" border resizable
                ref="table" :data="tableList">
                <vxe-column v-for="(column, index) in propertiesColumns" :key="index" :field="column.field"
                    :title="column.title" :width="column.width" :fixed="column.fixed">
                    <template #default="{ row }">
                        <span style="color:#3dcd58;cursor: pointer;" @click="openEditProperty(row)"
                            v-if="column.field == 'Name'">
                            <!-- <v-icon @click="openEditProperty(row)" style="cursor: pointer;"
                                size="18" color="#3dcd58">mdi-file-document-outline</v-icon>  -->
                            {{ row[column.field] }}</span>
                        <span v-else-if="column.field == 'Action'">
                            <v-icon @click="handleDel(row)" style="cursor: pointer;" color="#3dcd58"
                                size="18">mdi-delete</v-icon>
                        </span>
                        <span v-else-if="column.field == 'IsBound' || column.field == 'IsSubscribed'">{{
                row[column.field]
                    == '1' ? '是' : '否'
            }}</span>
                        <span v-else>{{ row[column.field] }}</span>
                    </template>
                </vxe-column>
            </vxe-table>
        </div>

        <a-modal :visible="isShowPopup" :width="840" :title="'Edit Function Property'" @cancel="isShowPopup = false">
            <editPropertyPopup v-if="isShowPopup" :editItemObj="editItemObj" ref="editPropertyPopup">
            </editPropertyPopup>
            <template #footer>
                <a-button key="back" @click="isShowPopup = false">Close</a-button>
            </template>
        </a-modal>
        <a-modal :visible="isShowAddPopup" :title="title" @ok="handleOk" @cancel="isShowAddPopup = false">
            <addPropertyPopup :editItemObj="editItemObj" v-if="isShowAddPopup" ref="addPropertyPopup">
            </addPropertyPopup>
        </a-modal>
    </div>
</template>

<script>
import { propertiesColumns } from '@/columns/factoryPlant/Opc.js'
import { getFunctionProperty, addFunctionProperty, delFunctionProperty } from '../service'
import editPropertyPopup from './editPropertyPopup.vue'
import addPropertyPopup from './addPropertyPopup.vue'
export default {
    components: {
        editPropertyPopup,
        addPropertyPopup
    },
    props: {
        currentObj: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            title: 'Add Property',
            isShowAddPopup: false,
            editItemObj: {},
            isShowPopup: false,
            propertiesColumns,
            loading: false,
            keywords: '',
            tableList: []
        }
    },
    created() {
        this.getdata()
    },
    methods: {
        handleDel(data) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: '确认要删除此项吗？',
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await delFunctionProperty([data.ID]);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.getdata()
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        async handleOk() {
            try {
                let params = this.$refs.addPropertyPopup.form
                await addFunctionProperty({ ...params, OpcFunctionId: this.currentObj.ID })
                this.isShowAddPopup = false
                this.$nextTick(() => {
                    this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });
                    this.getdata()
                })
            } catch {
                console.log('error')
            }
        },
        addProperty() {
            this.title = 'Add Property'
            this.editItemObj = {}
            this.isShowAddPopup = true
        },
        openEditProperty(data) {
            this.title = 'Edit Property'
            this.editItemObj = data;
            this.isShowAddPopup = true;
        },
        onSearch() {
            this.getdata()
        },
        async getdata() {
            let resp = await getFunctionProperty({ OpcFunctionId: this.currentObj.ID, key: this.keywords })
            this.tableList = resp.response
        }
    }
}
</script>

<style lang="scss" scoped>
.tool-row {
    background: #f5f5f5;
    padding: 0 3px;
}

.ant-input-search {
    ::v-deep [type=button] {
        background: #3dcd58;
        border-color: #3dcd58;
    }
}

::v-deep .primary {
    background: #3dcd58 !important;
}

.ml-3 {
    margin-left: 6px;
}

::v-deep .ant-modal-body {
    padding: 5px !important;
}
</style>
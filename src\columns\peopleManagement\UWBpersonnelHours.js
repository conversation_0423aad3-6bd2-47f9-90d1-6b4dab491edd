// 人员工时
export const UWBpersonnelHoursColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '员工号',
        value: 'StaffCode',
        width: 120,
        sortable: true
    },
    {
        text: '姓名',
        value: 'StaffName',
        width: 120,
        sortable: true
    },
    {
        text: '班次',
        value: 'Shift',
        width: 120,
        sortable: true
    },
    {
        text: '产线',
        value: 'Line',
        width: 140,
        sortable: true
    },
    {
        text: '工段',
        value: 'Segment',
        width: 160,
        sortable: true
    },
    {
        text: '班组',
        value: 'Team',
        width: 120,
        sortable: true
    },
    {
        text: '时长',
        value: 'WorkTime',
        width: 120,
        sortable: true
    },
    {
        text: '开始时间',
        value: 'BeginTime',
        width: 160,
        sortable: true
    },
    {
        text: '结束时间',
        value: 'EndTime',
        width: 160,
        sortable: true
    },
    {
        text: '日期',
        value: 'Date',
        width: 160,
        sortable: true
    },
    {
        text: '区域编号',
        value: 'GeoNum',
        width: 120,
        sortable: true
    },
    { text: '操作',  width: 150, align: 'center', value: 'actions', sortable: true }
];

export const UWBdetaillHoursColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: 'UWB编号',
        value: 'UWB',
        width: 100,
        sortable: true
    },
    {
        text: '姓名',
        value: 'Name',
        width: 90,
        sortable: true
    },
    {
        text: '区域编号',
        value: 'GeoNum',
        width: 100,
        sortable: true
    },
    {
        text: '开始时间',
        value: 'BeginTime',
        width: 160,
        sortable: true
    },
    {
        text: '结束时间',
        value: 'EndTime',
        width: 160,
        sortable: true
    },
    {
        text: '时长（秒）',
        value: 'Second',
        width: 110,
        sortable: true
    },
    {
        text: '时长（小时）',
        value: 'Hour',
        width: 110,
        sortable: true
    },
]
<template>
    <!-- 告警类型 -->
    <div class="product-module">
        <!-- <SearchForm ref="contactTorm" class="mt-2" :searchinput="searchinput" :show-from="showFrom" @searchForm="searchForm" /> -->
        <v-card class="mt-5">
            <div class="form-btn-list">
                <!-- 搜索栏 -->
                <!-- <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                    <v-icon>{{ 'mdi-table-search' }}</v-icon>
                    {{ $t('GLOBAL._SSL') }}
                </v-btn> -->
                <v-btn icon color="primary">
                    <v-icon @click="getDataList">mdi-cached</v-icon>
                </v-btn>
                <v-btn color="primary" @click="isfold = !isfold">{{ $t('GLOBAL.UnfoldFold') }}</v-btn>
                <v-btn color="primary" v-has="'BJLXGL_ADD'" @click="operaClick({})">{{ $t('GLOBAL._XZ') }}</v-btn>
            </div>
            <tree-table
                ref="treeTable"
                max-height="auto"
                class="tb-cate alarm-type-table"
                border
                stripe
                :is-fold="isfold"
                get-checked-prop
                :data="desserts"
                :columns="initHead"
                :expand-type="false"
                :selection-type="false"
            >
                <!-- 操作 -->
                <template slot="actions" slot-scope="scope">
                    <v-btn color="primary" text small class="mx-0 px-0" @click="tableClick(scope.row, 'Experience')">{{ $t('ANDON_BJZY.JYK') }}</v-btn>
                    <v-btn color="primary" text small class="mx-0 px-0" @click="tableClick(scope.row, 'set')">{{ $t('GLOBAL._SET') }}</v-btn>
                    <v-btn color="primary" v-has="'BJLXGL_EDIT'" text small class="mx-0 px-0" @click="tableClick(scope.row, 'edit')">{{ $t('GLOBAL._BJ') }}</v-btn>
                    <v-btn color="red" v-has="'BJLXGL_DELETE'" text small class="mx-0 px-0" @click="tableClick(scope.row, 'delete')">{{ $t('GLOBAL._SC') }}</v-btn>
                </template>
                <template slot="UwbMessageTemplate" slot-scope="scope">
                    {{ shrinSlightly(scope.row.UwbMessageTemplate) }}
                </template>
                <template slot="MessagePostTag" slot-scope="scope">
                    {{ scope.row.MessagePostTag == 1 ? '是' : '否' }}
                </template>
                <template slot="OverMessagePostTag" slot-scope="scope">
                    {{ scope.row.OverMessagePostTag == 1 ? '是' : '否' }}
                </template>
                <template slot="MessageTemplate" slot-scope="scope">
                    {{ shrinSlightly(scope.row.MessageTemplate) }}
                </template>
                <template slot="OverMessageTemplate" slot-scope="scope">
                    {{ shrinSlightly(scope.row.OverMessageTemplate) }}
                </template>
                <template slot="ParentId" slot-scope="scope">
                    {{ showText(scope.row.ParentId, typeRootList, 'ID', 'AlarmName') }}
                </template>
                <template slot="ProblemLevel" slot-scope="scope">
                    {{ showText(scope.row.ProblemLevel, problemLevelList, 'ItemValue', 'ItemName') }}
                </template>
                <template slot="Enable" slot-scope="scope">
                    <v-switch v-model="scope.row.Enable" class="pa-0 ma-0" style="height: 30px; transform: scale(0.8); transform-origin: left" label="" @click="switchClick(scope.row)"></v-switch>
                </template>
                <template slot="DealType" slot-scope="scope">
                    {{ showText(scope.row.DealType, dealTypeList, 'ItemValue', 'ItemName') }}
                </template>
            </tree-table>
        </v-card>
        <!-- 添加/编辑物料BOM明细 -->
        <updatePopup
            ref="update"
            :problemLevelList="problemLevelList"
            :opera-obj="operaObj"
            :AndonMessageTemplateList="AndonMessageTemplateList"
            :typeRootList="typeRootList"
            :dealTypeList="dealTypeList"
            @handlePopup="handlePopup"
        />
        <SetPopup ref="setdate" />
    </div>
</template>

<script>
import { alarmTypeColumns } from '@/columns/andonManagement/alarmType.js';
import { getAlarmTypeTreetList, DeleteAlarmType, getAlarmTypeRootList, UpdateAlarmTypeStatus } from '@/api/andonManagement/alarmType.js';
export default {
    components: {
        updatePopup: () => import('./components/updatePopup.vue'),
        SetPopup: () => import('./components/SetPopup.vue')
    },
    props: {
        currentSelect: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            isfold: true,
            deleteId: '',
            searchParams: {},
            problemLevelList: [],
            alarmTypeColumns,
            showFrom: false,
            operaObj: {},
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            desserts: [],
            tableHeight: '320px',
            typeRootList: [],
            dealTypeList: [],
            AndonMessageTemplateList: []
        };
    },
    computed: {
        //查询条件
        searchinput() {
            return [
                // 关键字
                {
                    key: 'key',
                    icon: '',
                    value: '',
                    label: '关键字'
                }
            ];
        },
        initHead() {
            let headList = [];
            headList = this.alarmTypeColumns.map(item => {
                item.label = this.$t(`$vuetify.dataTable.ANDON_BJLXGL.${item.prop}`); //  表表头对象名称
                return item;
            });
            return headList;
        },
        btnList() {
            return [
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary' },
                { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'red' }
            ];
        }
    },
    async created() {
        this.problemLevelList = await this.$getDataDictionary('problemLevel');
        this.dealTypeList = await this.$getDataDictionary('andon_DealType');
        this.AndonMessageTemplateList = await this.$getDataDictionary('AndonMessageTemplate');
        this.getDataList();
        this.getTypeRootList();
    },
    methods: {
        async switchClick(item) {
            let flag = null;
            if (item.Enable) {
                flag = 0;
            } else {
                flag = 1;
            }
            let params = {
                ID: item.ID,
                Deleted: flag
            };
            let res = await UpdateAlarmTypeStatus(params);
            this.getDataList();
        },
        shrinSlightly(v) {
            const str = v || '';
            const text = str.length > 30 ? v.substr(0, 30) + '...' : v;
            return text;
        },
        showText(v, arr, k1, k2) {
            let text = '';
            let data = arr.find(item => item[k1] == v);
            if (data) text = data[k2];
            return text;
        },
        // 查询数据
        searchForm(v) {
            this.searchParams = v;
            this.getDataList();
        },
        // 操作栏按钮
        tableClick(item, type) {
            console.log(item);
            switch (type) {
                // 编辑
                case 'edit':
                    this.operaClick(item);
                    break;
                // 删除
                case 'delete':
                    this.deleteId = item.ID;
                    this.sureDelete();
                    break;
                case 'set':
                    this.operaSet(item);
                    break;
                case 'Experience':
                    this.operaExperience(item);
                    break;
                default:
                    break;
            }
        },
        // 获取全部表格数据
        async getDataList() {
            this.loading = true;
            const res = await getAlarmTypeTreetList({ ...this.searchParams });
            const { success, response } = res || {};
            if (success) {
                this.desserts = response;
                this.getTreeArr(this.desserts);
            } else {
                this.desserts = [];
            }
            this.loading = false;
        },
        getTreeArr(data) {
            data.forEach(item => {
                if (item.Deleted == 0) {
                    item.Enable = true;
                } else {
                    item.Enable = false;
                }
                if (item.children.length != 0) {
                    this.getTreeArr(item.children);
                }
            });
        },
        // 获取大类列表
        async getTypeRootList() {
            this.loading = true;
            const res = await getAlarmTypeRootList({});
            const { success, response } = res || {};
            if (success) {
                this.typeRootList = response;
            } else {
                this.typeRootList = [];
            }
            this.loading = false;
        },
        // 新增/编辑BOM
        operaClick(v) {
            this.operaObj = v;
            this.$refs.update.dialog = true;
        },
        operaSet(v) {
            this.$refs.setdate.getTableData(v);
            this.$refs.setdate.Setdialog = true;
        },
        operaExperience(v) {
            this.$refs.setdate.getExperienceTableData(v);
            this.$refs.setdate.dialogExperienceVisible = true;
        },
        handlePopup(type, data) {
            switch (type) {
                case 'refresh':
                    this.getDataList();
                    this.getTypeRootList();
                    break;
                default:
                    break;
            }
        },
        // 确认删除
        sureDelete() {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            }).then(async () => {
                const res = await DeleteAlarmType([this.deleteId]);
                this.deleteId = '';
                const { success, msg } = res;
                if (success) {
                    this.pageOptions.pageCount = 1;
                    this.getDataList();
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.tb-cate {
    height: calc(100vh - 150px);
}

.tb-cate {
    position: relative !important;

    ::v-deep .zk-table__header-wrapper {
        position: absolute !important;
        z-index: 66 !important;
        overflow-y: scroll !important;
    }

    ::v-deep .zk-table__body-wrapper {
        margin-top: 40px !important;
        height: calc(100vh - 200px) !important;
        overflow-y: scroll !important;
    }
}
</style>
<style lang="scss">
.alarm-type-table {
    tbody {
        border-bottom: 1px solid gainsboro;
    }
}
</style>

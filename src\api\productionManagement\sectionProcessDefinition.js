import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_TRACE'
// 获取工段
export function getSapSegmentList(data) {
    const api =  '/api/SapSegment/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 修改 新增工段 
export function SaveOperation(data) {
  const api =  '/api/SapSegment/SaveOperation'
  return getRequestResources(baseURL, api, 'post', data);
}

// 删除工段 工序
export function deleteOperationOrPhase(data) {
  const api =  '/api/SapSegment/Delete'
  return getRequestResources(baseURL, api, 'post', data);
}

// 修改工序
export function SavePhase(data) {
  const api =  '/api/SapSegment/SavePhase'
  return getRequestResources(baseURL, api, 'post', data);
}

<template>
  <div style="width: 100%;height:100%;padding-top: 1%;box-sizing: border-box;">
    <div class="tabHeader">
      <!--  :class="{'active':tabId == index}" -->
      <div
        class="titName"
        v-for="(item,index) in list1"
        :key="index"
        :class="{'active':tabId1 == index}"
        @click="tabClick(item)"
      >{{item.tabName}}</div>
    </div>
    <div
      class="tabCenter"
      ref="tabCenter"
    >
      <div
        v-for="(item,index) in tabList1"
        :key="index"
        :id="`chart-${indexTab}-1`"
        :style="item.style"
      >
        <!-- 九宫格 -->
        <!-- :Position="searchFormObj.PresentDepartmentId + '-SIM1-Table-2'" -->
        <template v-if="item.OlineType.Encode == '11'&& (item.isOpen?.Fullname == '是' || item.hasOwnProperty('isOpen') == false)">
          <!-- <div
            style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
            @click="routeChange(item.routePage?.TagCode)"
          >{{ item.ModularName }}</div> -->
          <div
            class="titimgbox"
            @click="routeChange(item.routePage?.TagCode)"
          >
            <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
            <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
          </div>
          <viewTable3
            :id1="'chart-' + indexTab + '-1-' + parseFloat(item.tabChartIndex)"
            :Position="item.Order + '-'"
            :Order="item.Order"
            :title="item.ModularName"
            :exhibitionType="item.exhibitionType.Fullname"
            :simlevel1="simlevel"
            :BaseTime="BaseTime"
            :fullscreen="fullscreen"
            :backgroundImg="backgroundImg"
          ></viewTable3>
        </template>

        <!-- 柱状图 -->
        <!-- <div v-if="item.OlineType.Encode == '柱状图' && item.Order == indexTab+'-1-'+parseFloat(item.tabChartIndex)">
          <barChart
            style="width: 100%;height: 230%;"
            :key="barKey"
            :id1="'chart' + indexTab + '-1-' + parseFloat(item.tabChartIndex)"
            :Order="Order"
            :title="item.ModularName"
          />
        </div> -->
        <!-- 环形图 -->
        <template v-if="item.OlineType.Encode == '6'&& (item.isOpen?.Fullname == '是' || item.hasOwnProperty('isOpen') == false)">
          <!-- <div style="display: flex;width: 100%;height: 30px;">
            <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
            <div style="width: 50%;display: flex;">
              <dayMonIndex />
            </div>
          </div> -->
          <circularChart
            :key="item.Order+'1'"
            style="height: 100%;"
            :id1="'chart-' + indexTab + '-1-' + parseFloat(item.tabChartIndex)"
            :Order="item.Order"
            :title="item.ModularName"
            :exhibitionType="item.exhibitionType.Fullname"
            :simlevel="simlevel"
            :BaseTime="BaseTime"
            :Dimension="item.DimensionList1"
            :routeList="item.routePage?.TagCode"
            :backgroundImg="backgroundImg"
          />
        </template>
        <!-- 柱状+折线图 -->
        <template v-if="item.OlineType.Encode == '4'&& (item.isOpen?.Fullname == '是' || item.hasOwnProperty('isOpen') == false)">
          <!-- <div style="display: flex;width: 100%;height: 30px;">
            <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
            <div style="width: 50%;display: flex;">
              <dayMonIndex />
            </div>
          </div> -->
          <barLine
            style="height: 100%;"
            :id1="'chart-' + indexTab + '-1-' + parseFloat(item.tabChartIndex)"
            :Order="item.Order"
            :title="item.ModularName"
            :exhibitionType="item.exhibitionType.Fullname"
            :simlevel="simlevel"
            :BaseTime="BaseTime"
            :Dimension="item.DimensionList1"
            :routeList="item.routePage?.TagCode"
            :backgroundImg="backgroundImg"
          />
        </template>
        <!-- 渐变色柱状图 -->
        <template v-if="item.OlineType.Encode == '2' && (item.isOpen?.Fullname == '是' || item.hasOwnProperty('isOpen') == false)">
          <!-- <div style="color: #fff;">{{ item.title }}</div> -->
          <!-- <barChart
              :title="item.ModularName"
              :id1="'chart' + indexTab + '-1-' + parseFloat(item.tabChartIndex)"
              :list="item.listData"
            /> -->
          <!-- <div style="display: flex;width: 100%;height: 30px;">
            <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
            <div style="width: 50%;display: flex;">
              <dayMonIndex />
            </div>
          </div> -->
          <barChart
            :key="item.Order+'1'"
            style="height: 100%;"
            :title="item.ModularName"
            :id1="'chart-' + indexTab + '-1-' + parseFloat(item.tabChartIndex)"
            :Order="item.Order"
            :exhibitionType="item.exhibitionType.Fullname"
            :simlevel="simlevel"
            :BaseTime="BaseTime"
            :Dimension="item.Dimension1"
            :routeList="item.routePage?.TagCode"
            :backgroundImg="backgroundImg"
            :colorList="colorList"
            :cxColor="item.TargetValue"
            :cxStatus="item.TargetValue?.Fullname"
          />
        </template>
        <!-- 横向柱状图 -->
        <template v-if="item.OlineType.Encode == '3'&& (item.isOpen?.Fullname == '是' || item.hasOwnProperty('isOpen') == false)">
          <!-- <div style="display: flex;width: 100%;height: 30px;">
            <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
            <div style="width: 50%;display: flex;">
              <dayMonIndex />
            </div>
          </div> -->
          <barTransverseChart
            :key="item.Order+'1'"
            style="height: 100%;"
            :title="item.ModularName"
            :id1="'chart-' + indexTab + '-1-' + parseFloat(item.tabChartIndex)"
            :Order="item.Order"
            :exhibitionType="item.exhibitionType.Fullname"
            :simlevel="simlevel"
            :BaseTime="BaseTime"
            :Dimension="item.DimensionList1"
            :routeList="item.routePage?.TagCode"
            :backgroundImg="backgroundImg"
          />
        </template>
        <!-- 折线图 -->
        <template v-if="item.OlineType.Encode == '1'&& (item.isOpen?.Fullname == '是' || item.hasOwnProperty('isOpen') == false)">
          <!-- <div style="display: flex;width: 100%;height: 30px;">
            <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
            <div style="width: 50%;display: flex;">
              <dayMonIndex />
            </div>
          </div> -->
          <lineChart
            :key="item.Order+'1'"
            style="height: 100%;"
            :title="item.ModularName"
            :id1="'chart-' + indexTab + '-1-' + parseFloat(item.tabChartIndex)"
            :Order="item.Order"
            :exhibitionType="item.exhibitionType.Fullname"
            :simlevel="simlevel"
            :BaseTime="BaseTime"
            :Dimension="item.DimensionList1"
            :routeList="item.routePage?.TagCode"
            :backgroundImg="backgroundImg"
            :colorList="colorList"
          />
        </template>

        <!-- 饼图 -->
        <template v-if="item.OlineType.Encode == '5'&& (item.isOpen?.Fullname == '是' || item.hasOwnProperty('isOpen') == false)">
          <!-- <div style="display: flex;width: 100%;height: 30px;">
            <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
            <div style="width: 50%;display: flex;">
              <dayMonIndex />
            </div>
          </div> -->
          <pieChart
            :key="item.Order+'1'"
            style="height: 100%;"
            :title="item.ModularName"
            :id1="'chart-' + indexTab + '-1-' + parseFloat(item.tabChartIndex)"
            :Order="item.Order"
            :exhibitionType="item.exhibitionType.Fullname"
            :simlevel="simlevel"
            :BaseTime="BaseTime"
            :Dimension="item.DimensionList1"
            :routeList="item.routePage?.TagCode"
            :backgroundImg="backgroundImg"
          />
        </template>
        <!-- 表格 -->
        <template v-if="item.OlineType.Encode == '8'&& (item.isOpen?.Fullname == '是' || item.hasOwnProperty('isOpen') == false)">
          <!--<div
              style="color: #fff;font-size: 18px;font-weight: bold;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div> -->
          <tableCom
            :key="item.Order+'1'"
            :id1="'chart-' + indexTab + '-1-' + parseFloat(item.tabChartIndex)"
            :Order="item.Order"
            :title="item.ModularName"
            :exhibitionType="item.exhibitionType.Fullname"
            :simlevel="simlevel"
            :BaseTime="BaseTime"
            :routeList="item.routePage?.TagCode"
            :backgroundImg="backgroundImg"
          />
        </template>

        <!-- 旋转展示 -->
        <!-- <template v-if="item.OlineType.Encode == '旋转'">
           <div
              style="color: #fff;font-size: 18px;font-weight: bold;"
              @click="routeChange(item.routePage?.TagCode)"
            >{{ item.ModularName }}</div>
            <rotateChart
              :title="item.ModularName"
              :id1="'chart' + indexTab + '-1-' + parseFloat(item.tabChartIndex)"
              :Order="item.Order"
            />
          </template> -->
        <!-- 仪表盘展示 -->
        <template v-if="item.OlineType.Encode == '7'&& (item.isOpen?.Fullname == '是' || item.hasOwnProperty('isOpen') == false)">
          <!-- <div style="display: flex;width: 100%;height: 30px;">
            <div style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;">{{ item.ModularName }}</div>
            <div style="width: 50%;display: flex;">
              <dayMonIndex />
            </div>
          </div> -->
          <meterChart
            :key="item.Order+'1'"
            style="height: 100%;"
            :title="item.ModularName"
            :id1="'chart-' + indexTab + '-1-' + parseFloat(item.tabChartIndex)"
            :Order="item.Order"
            :exhibitionType="item.exhibitionType.Fullname"
            :simlevel="simlevel"
            :BaseTime="BaseTime"
            :Dimension="item.DimensionList1"
            :routeList="item.routePage?.TagCode"
            :backgroundImg="backgroundImg"
          />
        </template>
        <!-- tab区域展示 -->
        <template v-if="item.OlineType.Encode == '12'&& (item.isOpen?.Fullname == '是' || item.hasOwnProperty('isOpen') == false)">
          <!-- <div style="color: #fff;font-size: 18px;font-weight: bold;">{{ item.title }}</div> -->
          <tabChart
            :id1="'chart' + indexTab + '-1-1'"
            :Order="item.Order"
            :indexTab="index+1"
          />
        </template>
        <!-- 安全十字展示 -->
        <template v-if="item.OlineType.Encode == '13'&& (item.isOpen?.Fullname == '是' || item.hasOwnProperty('isOpen') == false)">
          <!-- <div
            style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
            @click="routeChange(item.routePage?.TagCode)"
          >{{ item.ModularName }}</div> -->
          <div
            class="titimgbox"
            @click="routeChange(item.routePage?.TagCode)"
          >
            <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
            <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
          </div>
          <!-- <securityChart
            :searchFormObj="searchFormObj"
            :curTeamTreeObj="curTeamTreeObj"
            :simlevel="simlevel"
            :BaseTime="BaseTime"
            :Order="item.Order"
          ></securityChart> -->
          <safe
            :searchFormObj="searchFormObj"
            :curTeamTreeObj="curTeamTreeObj"
            :simlevel="simlevel"
            :BaseTime="BaseTime"
            :Order="item.Order"
            :backgroundImg="backgroundImg"
          ></safe>
        </template>
        <!-- 文字描述展示 -->
        <template v-if="item.OlineType.Encode == '10'&& (item.isOpen?.Fullname == '是' || item.hasOwnProperty('isOpen') == false)">
          <!-- <div
            style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
            @click="routeChange(item.routePage?.TagCode)"
          >{{ item.ModularName }}</div> -->
          <div
            class="titimgbox"
            @click="routeChange1(item.routePage?.TagCode,item.listParams,item.OlineType.Encode,item.Order)"
          >
            <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
            <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
          </div>
          <mattersNeeding
            :key="item.Order+'1'"
            :itemCode="item.itemCode"
            :id1="'chart-' + indexTab + '-1-' + parseFloat(item.tabChartIndex)"
            :title="item.ModularName"
            :Order="item.Order"
            :exhibitionType="item.exhibitionType.Fullname"
            :simlevel="simlevel"
            :BaseTime="BaseTime"
            :Dimension="item.DimensionList1"
            :routeList="item.routePage?.TagCode"
            :backgroundImg="backgroundImg"
          />
        </template>
        <!-- 列别组件 -->
        <template v-if="item.OlineType.Encode == '9'&& (item.isOpen?.Fullname == '是' || item.hasOwnProperty('isOpen') == false)">
          <div
            class="titimgbox"
            @click="routeChange1(item.routePage?.TagCode,item.listParams,item.OlineType.Encode,item.Order)"
          >
            <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
            <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ item.ModularName }}</div>
          </div>
          <columnChart
            :key="item.Order+'1'"
            :itemCode="item.itemCode"
            :id1="'chart-' + indexTab + '-1-' + parseFloat(item.tabChartIndex)"
            :title="item.ModularName"
            :Order="item.Order"
            :exhibitionType="item.exhibitionType.Fullname"
            :simlevel="simlevel"
            :BaseTime="BaseTime"
            :Dimension="item.DimensionList1"
            :routeList="item.routePage?.TagCode"
            :backgroundImg="backgroundImg"
          />
        </template>
        <!-- <template v-if="item.OlineType.Encode == '11'">
          <div
            style="color: #fff;font-size: 18px;font-weight: bold;cursor: pointer;"
            @click="routeChange(item.routePage?.TagCode)"
          >{{ item.ModularName }}</div>
          <keyIndicators
            :id1="'chart-' + indexTab + '-1-' + parseFloat(item.tabChartIndex)"
            :Order="item.Order"
          />
        </template> -->
      </div>
    </div>
    <div
      class="tabHeader"
      style="width: 20%;margin: 0 auto;margin-top:20px;"
    >
      <div style="color: #fff"></div>
      <div
        class="titName"
        style="font-size: 16px;width: 50px;"
        v-for="(item,index) in pages.length"
        :key="index"
        :class="{'active1':tabId == (index+1)}"
        @click="goPage(index)"
      >{{index + 1}}</div>
      <!-- <div style="color: #409eff;font-size: 18px;margin-top: -2px;">></div> -->
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import Vue from 'vue'
import { getClassifyList } from '@/api/simConfig/simconfignew.js';

export default {
  props: {
    list: {
      OlineType: Array,
      default: () => []
    },
    index1: {
      OlineType: Number,
      default: 0
    },
    indexTab: {
      OlineType: Number,
      default: 0
    },
    region1: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    Dimension: {
      type: Array,
      default: () => []
    },
    backgroundImg: {
      type: String,
      default: ''
    },
    fullscreen: {
      type: Boolean,
      default: false
    },
    colorList: {
      type: Array,
      default: () => []
    },
    cxColor: {
      type: String,
      default: ''
    }

  },
  // watch: {
  //   list: {
  //     handler(o, n) {
  //       console.log(o, n, 999999);
  //       // if (this.curConfig == undefined) {
  //       //   this.curConfig = o
  //       // }
  //     },
  //     deep: true
  //   }
  // },
  components: {
    barChart: () => import('@/views/simManagement/simNew1/components/barChart.vue'),
    lineChart: () => import('@/views/simManagement/simNew1/components/lineChart.vue'),
    pieChart: () => import('@/views/simManagement/simNew1/components/pieChart.vue'),
    tableCom: () => import('@/views/simManagement/simNew1/components/tableCom.vue'),
    barTransverseChart: () => import('@/views/simManagement/simNew1/components/barTransverseChart.vue'),
    // barStereoscopicChart: () => import('@/views/simManagement/simNew1/components/barStereoscopicChart.vue'),
    // rotateChart: () => import('@/views/simManagement/simNew1/components/rotateChart.vue'),
    meterChart: () => import('@/views/simManagement/simNew1/components/meterChart.vue'),
    // securityChart: () => import('@/views/simManagement/simNew1/components/securityChart.vue'),
    // searchPage: () => import('@/views/simManagement/simNew1/components/searchPage.vue'),
    tabChart: () => import('@/views/simManagement/simNew1/components/tabChart.vue'),
    mattersNeeding: () => import('@/views/simManagement/simNew1/components/mattersNeeding.vue'),
    columnChart: () => import('@/views/simManagement/simNew1/components/columnChart.vue'),
    // keyIndicators: () => import('@/views/simManagement/simNew1/components/keyIndicators.vue'),
    barLine: () => import('@/views/simManagement/simNew1/components/barLine.vue'),
    circularChart: () => import('@/views/simManagement/simNew1/components/circularChart.vue'),
    // dayMonIndex: () => import('@/views/simManagement/simNew1/components/dayMonIndex.vue'),
    viewTable3: () => import('@/views/simManagement/simNew1/components/viewTable3.vue'),
    safe: () => import('@/views/simManagement/simNew1/components/safe.vue'),

  },
  data: () => ({
    tabId1: 0,
    searchFormObj: {
      simLevel: 'SIM1',
      PresentDepartmentId: '',//默认为第一个班组ID
      date: dayjs(new Date()).format('YYYY-MM-DD'),
      timestamp: new Date().getTime()
    },
    list1: [{ tabName: "生产", id: 0 }, { tabName: "设备", id: 1 }, { tabName: "质量", id: 2 }, { tabName: "成本", id: 3 }],
    barKey: 1,
    barTransverseChartKey: 1,
    lineChartKey: 1,
    pieChartKey: 1,
    tableCom: 1,
    rotateChartKey: 1,
    meterChart: 1,
    mattersNeedingKey: 1,
    columnChartKey: 1,
    keyIndicatorsKey: 1,
    indexs: 1,
    barStyle: {
      width: '50px',
      height: '100%',
      backgroundImage: 'url("https://img1.baidu.com/it/u=2756664614,3290369440&fm=253&fmt=auto&app=138&f=JPEG?w=753&h=500")',
      backgroundSize: '100%',
    },
    tabId: 1,
    divsData: [],
    tabList: [],
    tabList1: [],
    validIndices: [],
    pageList: [],
    pages: []
  }),
  computed: {
    //当前班组Parent数据
    curTeamTreeObj() {
      let ProductionLineCode, FactoryCode
      let TeamCodeParent = this.$store.getters.flatEquipmentTeam.find(item => item.id === this.searchFormObj.PresentDepartmentId)
      if (TeamCodeParent) {
        ProductionLineCode = this.$store.getters.flatEquipmentTeam.find(item => item.id === TeamCodeParent.parentId)
      }
      if (ProductionLineCode) {
        FactoryCode = this.$store.getters.flatEquipmentTeam.find(item => item.id === ProductionLineCode.parentId)
      }
      return {
        TeamCode: this.searchFormObj.PresentDepartmentId,
        ProductionLineCode: ProductionLineCode?.id,
        FactoryCode: FactoryCode?.id
      }
    }
  },
  created() {
    console.log(this.cxColor, 'cxColorcxColorcxColorcxColorcxColorcxColorcxColorcxColorcxColorcxColorcxColorcxColorcxColorcxColorcxColor');

    this.tabList = this.list
    this.tabList1 = []
    this.tabList.map(el => {
      if (el.TabTitle == this.list1[0].tabName) {
        this.tabList1.push(el)
      }
    })
    this.$nextTick(() => {
      this.pageChange()
    })
  },
  mounted() {
    console.log(this.tabList1, 'tabList1tabList1tabList1');
    this.tabList1.map(el => {
      if (el.isOpen?.Fullname == '否') {
        el.style.width = '0'
        el.style.height = '0'
      }
    })

  },
  methods: {
    pageChange() {
      this.pages = []
      let currentPage = [];
      let parentWidth = window.innerWidth;
      let parentHeight = window.innerHeight;
      let totalWidth = 0;
      let totalHeight = 0;
      let currentLineWidth = 0;
      let currentLineHeight = 0;
      if (this.tabList1.length > 0) {
        for (let i = 0; i < this.tabList1.length; i++) {
          const item = this.tabList1[i];
          const width = (parentWidth * parseFloat(item.style.width)) / 100;
          const height = (parentHeight * parseFloat(item.style.height)) / 100;

          // 检查是否换行
          if (currentLineWidth + width > parentWidth) {
            totalWidth = Math.max(totalWidth, currentLineWidth);
            totalHeight += currentLineHeight;
            currentLineWidth = width;
            currentLineHeight = height;
          } else {
            currentLineWidth += width;
            currentLineHeight = Math.max(currentLineHeight, height);
          }
          // console.log(totalWidth, 0.95 * parentWidth, totalHeight, 0.97 * parentHeight);

          if (totalWidth >= 0.95 * parentWidth && totalHeight >= 0.97 * parentHeight) {
            // 把当前页的数据加入到 pages 数组中
            this.pages.push(currentPage);
            // 开始新的一页
            currentPage = [];
            totalWidth = width;
            totalHeight = height;
            currentLineWidth = width;
            currentLineHeight = height;
          }

          currentPage.push(item);

          // 处理最后一个元素
          if (i === this.tabList1.length - 1) {
            this.pages.push(currentPage);
          }
        }

      }

      this.tabList1 = this.pages[this.tabId - 1]
      const newItems = this.tabList1.map(item => {
        const updatedItem = {
          ...item,
          ModularName: `${item.ModularName}`
        };
        return updatedItem;
      });
      Vue.set(this, 'tabList1', newItems);
      this.tabList1.map(el => {
        if (el.isOpen?.Fullname == '否') {
          el.style.width = '0'
          el.style.height = '0'
        }
      })
      this.$forceUpdate();
    },
    routeChange(e) {
      this.$router.push({ path: `${e}` })
      // this.$router.push({ path: 'simNew2', query: { code: item.Simcode } });
    },
    async routeChange1(e, e1, e2, e3) {
      let ID = ''
      if (e) {
        let res = await getClassifyList(e1);
        if (res.success) {
          ID = res.response[0].ParentId
          this.$router.push({ path: e, query: { RootId: ID, itemCode: e1 } })
        }

        // if (e2 == '9') {
        //   if (e3.split('-')[0] == 'SIM1') {
        //     this.$router.push({ path: e, query: { RootId: '02312312-2261-6900-163e-0370f6000000', itemCode: e1 } })
        //   }
        //   if (e3.split('-')[0] == 'SIM2') {
        //     this.$router.push({ path: e, query: { RootId: '02312312-2261-6900-163e-0370f6000000', itemCode: e1 } })
        //   }
        // }
        // if (e2 == '10') {
        //   this.$router.push({ path: e, query: { RootId: '02312312-2261-6900-163e-0370f6000000', itemCode: e1 } })
        // }
      }
    },
    tabClick(item) {
      this.tabId = 1
      // this.barKey++
      this.tabList1 = []
      this.tabId1 = item.id
      this.tabList.map(el => {
        if (el.TabTitle == item.tabName) {
          this.tabList1.push(el)
        }
      })
      this.tabList1.map(el => {
        if (el.isOpen?.Fullname == '否') {
          el.style.width = '0'
          el.style.height = '0'
        }
      })
      this.pageChange()

      // this.tabList.map(el => {
      //   el.tabChartIndex = el.Order.split('-')[2]
      // })
      // this.barKey++
    },
    goPage(index) {
      this.tabId = index + 1
      this.tabList1 = this.pages[index]
    }
  }
}
</script>
<style lang="scss" scoped>
.tabHeader {
    width: 100%;
    display: flex;
}
.tabCenter {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
    height: 90%;
    padding: 10px;
    box-sizing: border-box;
    gap: 10px;
    overflow: hidden;
}
.titName {
    flex: 1;
    height: 10%;
    font-size: 24px;
    font-weight: bold;
    // color: rgb(155, 159, 172);
    color: #fff;
    text-align: center;
    // border-right: 1px solid rgb(155, 159, 172);
    cursor: pointer;
}
.active {
    color: #409eff;
    font-weight: bold;
    border-right: 2px solid #409eff;
    box-shadow: 0px 0px 10px #fff;
    border-radius: 5px;
    font-size: 26px;
}
.active1 {
    color: #409eff;
    font-weight: bold;
    border-right: 2px solid #409eff;
    box-shadow: 0px 0px 10px #fff;
    border-radius: 5px;
    font-size: 18px;
}
.titimgbox {
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    width: 50%;
    height: 30px;
    line-height: 30px;
    border-radius: 5px;
    /* background-image: linear-gradient(to right, #056be0 0%, #000b61 100%); */
    display: flex;
    /* border: 1px solid #fff; */
    /* box-shadow: 0px 0px 7px 0px #fff; */
    /* overflow: hidden; */
}
.titimgbox {
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    width: 200px;
    height: 30px;
    line-height: 30px;
    border-radius: 5px;
    /* background-image: linear-gradient(to right, #056be0 0%, #000b61 100%); */
    display: flex;
    /* border: 1px solid #fff; */
    /* box-shadow: 0px 0px 7px 0px #fff; */
    /* overflow: hidden; */
}
</style>
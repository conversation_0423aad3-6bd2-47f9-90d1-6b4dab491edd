<template>
    <v-container v-if="showFrom && searchinput.length" class="px-0 py-3 mx-0 justify-start">
        <v-form ref="form" v-model="valid" class="inline-block">
            <v-row>
                <v-col :cols="20">
                    <v-row>
                        <v-col v-for="(item, index) in searchinput" :key="index" :cols="24" :lg="3">
                            <v-select v-if="item.type == 'select'" v-model="item.value" :items="item.selectData"
                                clearable dense outlined :label="item.label" :placeholder="item.placeholder" required
                                :rules="item.rules" />
                            <!-- 日期 -->
                            <v-menu v-else-if="item.type == 'date'" ref="menu" v-model="menu"
                                :close-on-content-click="false" transition="scale-transition" offset-y max-width="290px"
                                min-width="auto">
                                <template #activator="{ on, attrs }">
                                    <v-text-field v-model="item.value" dense outlined :label="item.label"
                                        persistent-hint v-bind="attrs" @blur="date = parseDate(dateFormatted)"
                                        v-on="on"></v-text-field>
                                </template>
                                <v-date-picker v-model="item.date" range no-title @input="menu = false"></v-date-picker>
                            </v-menu>
                            <v-text-field v-else v-model="item.value" dense outlined :label="item.label"
                                :placeholder="item.placeholder" required :rules="item.rules" />
                        </v-col>
                        <v-col v-if="searchinput.length == 2 || searchinput.length == 1" class="text-lg-left" :cols="4"
                            :lg="3">
                            <v-btn color="primary" @click="handleSubmitForm">查 询</v-btn>
                            <v-btn class="ml-4" @click="handleCancelForm">重 置</v-btn>
                        </v-col>
                    </v-row>
                </v-col>
                <v-col v-if="searchinput.length > 2" class="text-lg-right" :lg="3">
                    <v-btn color="primary" @click="handleSubmitForm">查 询</v-btn>
                    <v-btn class="ml-4" @click="handleCancelForm">重 置</v-btn>
                </v-col>
            </v-row>
        </v-form>
    </v-container>
</template>

<script>
export default {
    components: {},
    props: {
        searchinput: {
            type: Array,
            default: () => []
        },
        showFrom: {
            type: Boolean,
            default: true
        }
    },
    data: () => ({
        valid: true,
        menu: false
    }),
    computed: {
        dateRangeText() {
            return this.dates.join('~');
        }
    },
    methods: {
        // 重置
        handleCancelForm() {
            this.$refs.form.reset();
            this.searchinput.forEach(item => {
                item.value = '';
            });
        },
        handleSubmitForm() {
            // 查询
            let params = {};
            this.searchinput.forEach(item => {
                params[item.key] = item.value;
            });
            this.$emit('searchForm', params);
        }
    }
};
</script>

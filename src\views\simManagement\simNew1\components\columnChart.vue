<template>
  <div class="mattersNeedingBox">
    <div
      class="titName"
      v-for="(item,index) in list"
      :key="index"
    >
      <div class="titName_top">{{ item.ItemName }}</div>
      <!-- <div class="titName_bottom">{{ item.num }}</div> -->
    </div>
  </div>
</template>
<script>
import { GetPageList, getClassifyTree } from '@/api/systemManagement/dataDictionary.js';

export default {
  props: {
    itemCode: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    list: []
  }),
  mounted() {
    this.GetPageList()
  },
  methods: {
    // 获取字典数据
    async getClassifyTree() {
      let papams = {
        lang: this.$store.getters.getLocale
      };
      const res = await getClassifyTree(papams);
      console.log(res);
      if (res && res.success) {
        this.treeData = res.response;
        this.treeData.map((el, index) => {
          if (el.value == 'SIM') {
            this.GetPageList(el.id)
          }
        })
      } else {
        this.treeData = [];
      }
    },
    // 字典数据点击
    async GetPageList(e) {
      let params =
      {
        "lang": "cn",
        "key": "",
        "RootId": e,
        "itemCode": this.itemCode,
        "pageIndex": 1,
        "pageSize": 50
      }
      const res = await GetPageList(params);
      let { success, response } = res || {};
      if (success) {
        this.list = response.data.sort((a, b) => a.ItemValue - b.ItemValue);
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.mattersNeedingBox {
    width: 100%;
    height: 90%;
    overflow-y: auto;
}
.titName {
    width: 100%;
    height: auto;
    margin-top: 10px;
    border-bottom: 1px solid #188df0;
    display: flex;
}
.titName_top,
.titName_bottom {
    flex: 1;
    font-size: 14px;
    color: #fff;
    font-weight: bold;
    letter-spacing: 1px;
}
.titName_bottom {
    text-align: right;
}
</style>
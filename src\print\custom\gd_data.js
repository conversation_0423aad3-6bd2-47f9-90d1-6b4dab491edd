/* eslint-disable */
import { hiprint } from '../../index';
// 自定义设计元素
export const bProvider = function (ops) {
    var addElementTypes = function (context) {
        context.removePrintElementTypes('bProviderModule');
        context.addPrintElementTypes('bProviderModule', [
            new hiprint.PrintElementTypeGroup('客户', [
                {
                    tid: 'bProviderModule.MaterialDescription',
                    title: '物料描述',
                    data: '高级客户',
                    type: 'text',
                    options: {
                        field: 'MaterialDescription',
                        testData: '高级客户',
                        height: 16,
                        fontSize: 6.75,
                        fontWeight: '700',
                        textAlign: 'left',
                        textContentVerticalAlign: 'middle'
                    }
                },
                {
                    tid: 'bProviderModule.MaterialCode',
                    title: '物料号',
                    data: '18888888888',
                    type: 'text',
                    options: {
                        field: 'MaterialCode',
                        testData: '18888888888',
                        height: 16,
                        fontSize: 6.75,
                        fontWeight: '700',
                        textAlign: 'left',
                        textContentVerticalAlign: 'middle'
                    }
                },
                {
                    tid: 'bProviderModule.WoStatus',
                    title: '工单类型',
                    data: '18888888888',
                    type: 'text',
                    options: {
                        field: 'WoStatus',
                        testData: '18888888888',
                        height: 16,
                        fontSize: 6.75,
                        fontWeight: '700',
                        textAlign: 'left',
                        textContentVerticalAlign: 'middle'
                    }
                },
                {
                    tid: 'bProviderModule.WoCode',
                    title: '工单号',
                    data: '18888888888',
                    type: 'text',
                    options: {
                        field: 'WoCode',
                        testData: '18888888888',
                        height: 16,
                        fontSize: 6.75,
                        fontWeight: '700',
                        textAlign: 'left',
                        textContentVerticalAlign: 'middle'
                    }
                },
                {
                    tid: 'bProviderModule.CompanyName',
                    title: '产线',
                    data: '18888888888',
                    type: 'text',
                    options: {
                        field: 'CompanyName',
                        testData: '18888888888',
                        height: 16,
                        fontSize: 6.75,
                        fontWeight: '700',
                        textAlign: 'left',
                        textContentVerticalAlign: 'middle'
                    }
                },
                {
                    tid: 'bProviderModule.ShiftName',
                    title: '班次',
                    data: '18888888888',
                    type: 'text',
                    options: {
                        field: 'ShiftName',
                        testData: '18888888888',
                        height: 16,
                        fontSize: 6.75,
                        fontWeight: '700',
                        textAlign: 'left',
                        textContentVerticalAlign: 'middle'
                    }
                },
                {
                    tid: 'bProviderModule.TeamName',
                    title: '班组',
                    data: '18888888888',
                    type: 'text',
                    options: {
                        field: 'TeamName',
                        testData: '18888888888',
                        height: 16,
                        fontSize: 6.75,
                        fontWeight: '700',
                        textAlign: 'left',
                        textContentVerticalAlign: 'middle'
                    }
                },
                {
                    tid: 'bProviderModule.WoQuantity',
                    title: '数量',
                    data: '18888888888',
                    type: 'text',
                    options: {
                        field: 'WoQuantity',
                        testData: '18888888888',
                        height: 16,
                        fontSize: 6.75,
                        fontWeight: '700',
                        textAlign: 'left',
                        textContentVerticalAlign: 'middle'
                    }
                },
                {
                    tid: 'bProviderModule.ActualStartTime',
                    title: '生产时间',
                    data: '18888888888',
                    type: 'text',
                    options: {
                        field: 'ActualStartTime',
                        testData: '18888888888',
                        height: 16,
                        fontSize: 6.75,
                        fontWeight: '700',
                        textAlign: 'left',
                        textContentVerticalAlign: 'middle'
                    }
                },
                {
                    tid: 'bProviderModule.MaterialCode',
                    title: '批次号',
                    data: '18888888888',
                    type: 'text',
                    options: {
                        field: 'MaterialCode',
                        testData: '18888888888',
                        height: 16,
                        fontSize: 6.75,
                        fontWeight: '700',
                        textAlign: 'left',
                        textContentVerticalAlign: 'middle'
                    }
                }
            ]),
            new hiprint.PrintElementTypeGroup('辅助', [
                {
                    tid: 'bProviderModule.hline',
                    title: '横线',
                    type: 'hline'
                },
                {
                    tid: 'bProviderModule.vline',
                    title: '竖线',
                    type: 'vline'
                },
                {
                    tid: 'bProviderModule.rect',
                    title: '矩形',
                    type: 'rect'
                },
                {
                    tid: 'bProviderModule.oval',
                    title: '椭圆',
                    type: 'oval'
                }
            ])
        ]);
    };
    return {
        addElementTypes: addElementTypes
    };
};

// type: 1供货商 2经销商
export default [
    {
        name: '工单管理',
        value: 'bProviderModule',
        type: 2,
        f: bProvider()
    }
];

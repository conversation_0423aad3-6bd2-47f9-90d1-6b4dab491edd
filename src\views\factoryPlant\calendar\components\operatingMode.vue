<!-- eslint-disable vue/valid-v-slot -->
<template>
    <v-card>
        <v-card-title class="headline primary lighten-2" primary-title>{{ $t('DFM_RL._YZMS') }}</v-card-title>
        <v-card-text>
            <v-row class="mt-4">
                <v-col :cols="12" :lg="4" class="pt-0 pb-0 d-flex">
                    <v-text-field :label="$t('DFM_RL._MC')" dense outlined v-model="keywords"></v-text-field>
                    <v-btn color="primary" class="ml-3" @click="getdata">{{ $t('GLOBAL._CX') }}</v-btn>
                </v-col>
                <v-col :cols="12" :lg="6" class="pt-0 pb-0 ml-auto text-right">
                    <v-btn icon color="primary" @click="getdata">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" class="ml-2" @click="editModePopup = true">{{ $t('GLOBAL._XZ') }}</v-btn>
                </v-col>
            </v-row>
            <v-data-table :items-per-page='999' :headers="headers" hide-default-footer dense height="200"
                :loading="loading" :items="modeList">
                <template #item.actions="{ item }">
                    <v-btn v-for="(list, index) in btnList" :key="index" text small class="mx-0 px-0"
                        @click="handleClick(item, list.code)" :color="list.type">{{ list.text }}</v-btn>
                </template>
            </v-data-table>
        </v-card-text>
        <v-divider></v-divider>

        <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>

        <!-- 添加运转模式 -->
        <v-dialog scrollable v-model="editModePopup" width="35%">
            <EditMode :editObj="editObj" v-if="editModePopup" @getdata="getdata" @closeEditPopup="closeEditPopup" />
        </v-dialog>
    </v-card>
</template>

<script>
import EditMode from './editModePopup.vue';
import { getModeList, deleteMode } from '../service';
export default {
    components: {
        EditMode
    },
    data() {
        return {
            editModePopup: false,
            keywords: '',
            modeList: [],
            loading: false,
            editObj: {}
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: ''
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: ''
                }
            ];
        },
        headers() {
            return [
                { text: this.$t('DFM_RL._MC'), value: 'Name', width: 100 },
                { text: this.$t('DFM_RL._BCS'), value: 'Shifts', width: 120 },
                { text: this.$t('DFM_RL._BZS'), value: 'Teams', width: 120 },
                { text: this.$t('DFM_RL._XH'), value: 'Sequence', width: 80 },
                // { text: '有效', value: 'Enabledmark', width: 80 },
                // { text: '是否删除', value: 'theSerialNumber', width: 100 },
                { text: this.$t('DFM_RL._BZ'), value: 'Description', width: 100 },
                { text: this.$t('DFM_RL._CZ'), value: 'actions', width: 120 }
            ];
        }
    },
    created() {
        this.getdata();
    },
    methods: {
        closeEditPopup() {
            this.editModePopup = false;
        },
        handleClick(item, type) {
            switch (type) {
                case 'edit':
                    this.editObj = item;
                    this.editModePopup = true;
                    break;
                case 'delete':
                    this.delData(item.ID);
                    break;
            }
        },
        async delData(id) {
            this.$confirms({
                message: this.$t('GLOBAL._SCTIPS'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    await deleteMode([id]);
                    this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
                    this.getdata();
                })
                .catch();
        },
        async getdata() {
            this.loading = true;
            try {
                let resp = await getModeList({ key: this.keywords });
                this.modeList = resp.response;
                this.loading = false;
            } catch {
                this.loading = false;
            }
        },
        closePopup() {
            this.$emit('handlePopup', false, 'mode');
        }
    }
};
</script>

<style>

</style>
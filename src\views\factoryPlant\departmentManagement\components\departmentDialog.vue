<!-- add 新增 -->
<!-- delete 批量删除 -->
<!-- edit 修改 -->
<template>
    <v-dialog v-model="dialog" persistent max-width="720px">
        <!-- 新增 -->
        <v-card v-if="dialogType === 'add'" ref="form">
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                {{ $t('DFM_ZZJM._XZZZ') }}
                <v-icon @click="dialog = false">mdi-close</v-icon>
            </v-card-title>
            <!-- 表单内容 -->
            <v-card-text class="mt-7">
                <v-container>
                    <v-form ref="addform" v-model="valid">
                        <v-row>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <treeselect
                                    v-model="ParentNameId.ParentId"
                                    no-results-text="暂无数据"
                                    :multiple="false"
                                    :placeholder="$t('DFM_WLMX._SJCD')"
                                    :options="treeDatas"
                                    :normalizer="normalizer"
                                />
                            </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-text-field ref="Fullname" v-model="formModel.Fullname" :rules="[v => !!v || '名称不能为空']" outlined dense :label="$t('DFM_ZZJM._ZZMC')" required></v-text-field>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-text-field ref="Encode" v-model="formModel.Encode" :rules="[v => !!v || '编码不能为空']" outlined dense :label="$t('DFM_ZZJM._ZZBM')" required></v-text-field>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-text-field ref="Shortname" v-model="formModel.Shortname" outlined dense :label="$t('DFM_ZZJM._ZZJC')"></v-text-field>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-select v-model="formModel.LEVEL" :items="rootitems" clearable dense item-text="ItemName" item-value="ItemValue" outlined :label="$t('DFM_ZZJM._ZZLX')"></v-select>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-text-field ref="EquipmentCode" v-model="formModel.Outerphone" outlined dense :label="$t('DFM_ZZJM._DHH')" required></v-text-field>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12">
                                <v-textarea v-model="formModel.Description" rows="2" outlined :label="$t('DFM_ZZJM._BZ')"></v-textarea>
                            </v-col>
                        </v-row>
                    </v-form>
                </v-container>
            </v-card-text>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSubmit">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="dialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <!-- 修改 -->
        <v-card v-if="dialogType === 'edit'" ref="form">
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                {{ $t('DFM_ZZJM._XGZZ') }}
                <v-icon @click="dialog = false">mdi-close</v-icon>
            </v-card-title>
            <!-- 表单内容 -->
            <v-card-text class="mt-7">
                <v-container>
                    <v-form ref="addform" v-model="valid">
                        <v-row>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <treeselect
                                    v-model="editformModel.ParentId"
                                    no-results-text="暂无数据"
                                    :multiple="false"
                                    :placeholder="$t('DFM_WLMX._SJCD')"
                                    :options="treeDatas"
                                    :normalizer="normalizer"
                                />

                                <!-- <v-text-field ref="ParentName" v-model="editformModel.ParentName" outlined disabled dense :label="$t('DFM_WLMX._SJCD')"></v-text-field> -->
                            </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-text-field
                                    ref="Fullname"
                                    v-model="editformModel.Fullname"
                                    :rules="[v => !!v || '名称不能为空']"
                                    outlined
                                    dense
                                    :label="$t('DFM_ZZJM._ZZMC')"
                                    required
                                ></v-text-field>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-text-field ref="Encode" v-model="editformModel.Encode" :rules="[v => !!v || '编码不能为空']" outlined dense :label="$t('DFM_ZZJM._ZZBM')" required></v-text-field>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-text-field ref="Shortname" v-model="editformModel.Shortname" outlined dense :label="$t('DFM_ZZJM._ZZJC')"></v-text-field>
                            </v-col>
                            <!-- <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-text-field ref="ParentId" v-model="editformModel.ParentId" outlined disabled dense :label="$t('DFM_ZZJM._SJZZ')"></v-text-field>
                            </v-col> -->
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-select
                                    v-model="editformModel.LEVEL"
                                    :items="rootitems"
                                    item-text="ItemName"
                                    item-value="ItemValue"
                                    clearable
                                    dense
                                    outlined
                                    :label="$t('DFM_ZZJM._ZZLX')"
                                ></v-select>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                <v-text-field ref="EquipmentCode" v-model="editformModel.Outerphone" outlined dense :label="$t('DFM_ZZJM._DHH')" required></v-text-field>
                            </v-col>
                            <v-col class="py-0 px-3" cols="12">
                                <v-textarea v-model="editformModel.Description" rows="2" outlined :label="$t('DFM_ZZJM._BZ')"></v-textarea>
                            </v-col>
                        </v-row>
                    </v-form>
                </v-container>
            </v-card-text>
            <v-card-actions class="lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="editSubmit">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="dialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
        <v-card v-if="dialogType === 'import'">
            <v-card-title class="d-flex justify-space-between text-h6 primary lighten-2">
                导入数据
                <v-icon @click="dialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-file-input accept="image/*" label="File input"></v-file-input>
        </v-card>
    </v-dialog>
</template>

<script>
import { DepartmentSaveForm } from '@/api/factoryPlant/departmentManagement.js';

export default {
    name: 'DataDictionaryDialog',
    props: {
        treeDatas: {
            type: Array,
            default: () => []
        },
        rootitems: {
            type: Array,
            default: () => []
        },
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        },
        deleteList: {
            type: Array,
            default: () => []
        },
        hasChildren: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            // 提交表单数据
            valid: true,
            dialog: false,
            checkbox: true,
            formModel: {
                Companyid: null, //公司ID
                Parentid: null, // 父级Id
                Fullname: null, //组织名称
                Encode: null, //组织编码
                Shortname: null, // 组织简称
                LEVEL: null, //组织性质
                Outerphone: null, //电话号
                Description: null //备注
            },
            options: [],
            normalizer(node) {
                return {
                    id: node.id,
                    label: node.name,
                    children: node.children
                };
            }
        };
    },

    computed: {
        ParentNameId() {
            return {
                ParentId: this.hasChildren.id
            };
        },
        editformModel() {
            return {
                // ParentId: this.hasChildren.hasChildren ? this.hasChildren.name : '',
                ParentId: this.tableItem.Parentid == '0' ? null : this.tableItem.Parentid,
                Fullname: this.tableItem.Fullname, //组织名称
                Encode: this.tableItem.Encode + '', //组织编码
                Shortname: this.tableItem.Shortname, // 组织简称
                LEVEL: this.tableItem.LEVEL, //组织性质
                Outerphone: this.tableItem.Outerphone, //电话号
                Description: this.tableItem.Description //备注
            };
        }
    },
    methods: {
        // 初始化表单
        initFrom() {
            this.$refs.addform.reset();
        },
        //新增
        async addSubmit() {
            let params = {
                Companyid: '', //公司ID
                // Parentid: this.hasChildren.id || '0', // 父级Id
                ParentId: this.ParentNameId.ParentId || '0',
                Fullname: this.formModel.Fullname, //组织名称
                Encode: this.formModel.Encode, //组织编码
                Shortname: this.formModel.Shortname, // 组织简称
                LEVEL: this.formModel.LEVEL, //组织性质
                Outerphone: this.formModel.Outerphone, //电话号
                Description: this.formModel.Description //备注
            };
            let res = await DepartmentSaveForm(params);
            if (res.success) {
                this.$store.commit('SHOW_SNACKBAR', { text: '添加成功', color: 'success' });
                this.dialog = this.checkbox ? false : true;
                this.$parent.$parent.DepartmentGetPageList();
                this.$parent.$parent.companyTree();
                await this.initFrom();
            }
        },
        //编辑
        async editSubmit() {
            console.log(this.tableItem);
            let params = {
                Companyid: '', //公司ID
                ParentId: this.editformModel.ParentId || '0',
                Fullname: this.editformModel.Fullname, //组织名称
                Encode: this.editformModel.Encode, //组织编码
                Shortname: this.editformModel.Shortname, // 组织简称
                LEVEL: this.editformModel.LEVEL, //组织性质
                Outerphone: this.editformModel.Outerphone, //电话号
                Description: this.editformModel.Description, //备注
                ID: this.tableItem.ID
            };
            let res = await DepartmentSaveForm(params);

            if (res.success) {
                this.$store.commit('SHOW_SNACKBAR', { text: '修改成功', color: 'success' });
                this.dialog = this.checkbox ? false : true;
                this.$parent.$parent.DepartmentGetPageList();
                this.$parent.$parent.companyTree();
            }
        }
    }
};
</script>
<style lang="scss" scoped></style>

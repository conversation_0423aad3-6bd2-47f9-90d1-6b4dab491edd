<template>
  <div class="table-content">
    <SearchForm
      class="mt-1"
      :show-from="isShowSearch"
      :searchinput="searchInputs"
      @searchForm="searchForm"
    />
    <vxe-toolbar ref="xToolbar">
      <template #buttons>
        <v-btn
          icon
          class="float-left mx-4"
          @click="isShowSearch = !isShowSearch"
        >
          <v-icon>{{ 'mdi-table-search' }}</v-icon>
          {{ $t('GLOBAL._SSL') }}
        </v-btn>
        <v-btn
          style="margin-right:10px;margin-left:10px;"
          color="primary"
          @click="add()"
        >{{ $t('GLOBAL._XZ') }}</v-btn>
        <v-btn
          style="margin-right:10px;"
          color="primary"
          @click="modify()"
        >{{ $t('GLOBAL._XG') }}</v-btn>
        <v-btn
          style="margin-right:10px;"
          color="primary"
          @click="removeChange()"
        >{{ $t('GLOBAL._SC') }}</v-btn>
        <!-- <v-btn
          color="primary"
          @click="device()"
        >导出</v-btn> -->
      </template>
    </vxe-toolbar>
    <!-- 表格 -->
    <!-- <a-table :dataSource="dataSource" :columns="columns" :pagination="pagination" bordered/> -->
    <div class="table-box">
      <vxe-table
        ref="vxeTable"
        :data="tableList"
        :show-footer="false"
        :loading="false"
        class="mytable-scrollbar"
        :row-config="{ isHover: true }"
        :column-config="{ resizable: true }"
        v-bind="$attrs"
        height="720"
        v-on="$listeners"
        border
        size="mini"
        style="font-size: 20px;"
        @checkbox-change="handleCheckboxChange"
      >
        <vxe-table-column
          fixed="left"
          type="checkbox"
          width="60"
        ></vxe-table-column>
        <vxe-column
          v-for="(column, index) in columns"
          :type="column.type"
          :width="column.width"
          :fixed="column.fixed"
          :key="index"
          :sortable="column.sortable"
          :field="column.value"
          :title="column.text"
        >
          <!-- <template #default="{ row }">
            <span v-if="column.field == 'PresentUserId'">
              {{ getStaffName(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'PresentDepartmentId'">
              {{ getDepartmentName(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'PresentProdProcessId'">
              {{ getPresentProdProcessName(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'ReceiveDepartmentId'">
              {{ getDepartmentName(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'ReceiveUserId'">
              {{ getStaffName(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'TargetEDRCode'">
              {{ getTargetEDRName(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'CurrentLevel'">
              {{ getCurrentLevel(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'Severity'">
              {{ getSeverity(row[column.field]) }}
            </span>
            <span v-else-if="column.field == 'Closed'">
              {{ getCloseStatus(row[column.field]) }}
            </span>
            <span v-else>{{ row[column.field] }}</span>
          </template> -->
        </vxe-column>
      </vxe-table>
      <vxe-pager
        :current-page.sync="pageOptions.pageIndex"
        :page-size.sync="pageOptions.pageSize"
        :page-sizes="pageOptions.pageSizeitems"
        :total="pageOptions.total"
        @page-change="getListFn"
        size="medium"
      />
    </div>

    <!--新增 -->
    <v-dialog
      v-if="showInfoDialogLeft"
      v-model="showInfoDialogLeft"
      scrollable
      persistent
      width="55%"
    >
      <InfoDialogLeft
        v-if="showInfoDialogLeft"
        :key="InfoDialogLeftkey"
        :editItemObjLeft="editItemObjLeft"
        @closePopupLeft="closePopupLeft"
      >
      </InfoDialogLeft>
    </v-dialog>
  </div>
</template>

<script>
import InfoDialogLeft from './components/InfoDialogLeft.vue'
import { getLeftPageList, DeleteLeft, getStaffByDepartId } from './service.js'
import dayjs from 'dayjs';
import { mapGetters } from 'vuex';

export default {
  components: {
    InfoDialogLeft
  },
  props: {},
  data() {
    return {
      searchFormObj: {
        CheckDate: "",
        Host: "",
        CheckResult: '',
        Director: '',
        PlanCompleteDate: '',
        key: '',
      },
      staffListByDepart: [],
      showInfoDialogLeft: false,
      InfoDialogLeftkey: 1,
      editItemObjLeft: {},
      selectList: [],
      loading: false,
      pageOptions: {
        total: 0,
        pageIndex: 1,
        pageSize: 20,
        pageSizeitems: [50, 150, 300, 500]
      },
      isShowSearch: false,
      tableList: [],
      columns: [
        { text: '检查日期', value: 'CheckDate', width: 130 },
        { text: '主持人', value: 'HostText', width: 150 },
        { text: '检查指标', value: 'CheckItem', width: 150 },
        { text: '检查区域', value: 'CheckLocation', width: 150 },
        { text: '检查内容', value: 'CheckContent', width: 587 },
        { text: '检查结果', value: 'CheckResultText', width: 100 },
        { text: '负责人', value: 'DirectorText', width: 150 },
        { text: '计划完成日期', value: 'PlanCompleteDate', width: 150 },
      ],
      closeList: [
        {
          ItemName: '是',
          ItemValue: '1',
        },
        {
          ItemName: '否',
          ItemValue: '0',
        }
      ],
    };
  },
  computed: {
    ...mapGetters(['getUserinfolist']),
    searchInputs() {
      return [
        {
          value: '',
          icon: 'mdi-account-check',
          label: '主持人',
          placeholder: '',
          type: 'select',
          selectData: this.$changeSelectItems(this.staffListByDepart, 'LoginName', 'UserName'),
          key: 'Host'
        },
        {
          value: '',
          icon: 'mdi-account-check',
          label: '负责人',
          placeholder: '',
          type: 'select',
          selectData: this.$changeSelectItems(this.staffListByDepart, 'LoginName', 'UserName'),
          key: 'Director'
        },
        {
          value: '',
          icon: 'mdi-account-check',
          label: '检查结果',
          type: 'select',
          placeholder: '',
          selectData: this.$changeSelectItems(this.closeList, 'ItemValue', 'ItemName'),
          key: 'CheckResult',
        },
        {
          value: '',
          type: 'date',
          icon: 'mdi-account-check',
          label: '检查日期',
          placeholder: '',
          key: 'CheckDate'
        },
        {
          value: '',
          type: 'date',
          icon: 'mdi-account-check',
          label: '计划完成日期',
          placeholder: '',
          key: 'PlanCompleteDate'
        },
        {
          value: '',
          icon: 'mdi-account-check',
          label: '关键字',
          placeholder: '',
          type: 'input',
          key: 'key'
        },
      ]
    }
  },

  created() {
    this.getTeamList1();
    this.getListFn()
  },
  methods: {
    async getTeamList1(val) {
      let resp = await getStaffByDepartId({ Departmentid: this.getUserinfolist[0].Departmentid })
      if (resp && resp.response) {
        this.staffListByDepart = resp.response
      } else {
        this.staffListByDepart = []
      }
    },
    searchForm(form) {
      if (form.CheckResult == null) {
        this.searchFormObj.CheckResult = ''
      } else {
        this.searchFormObj.CheckResult = form.CheckResult
      }
      this.searchFormObj.CheckDate = form.CheckDate
      this.searchFormObj.Host = form.Host
      this.searchFormObj.PlanCompleteDate = form.PlanCompleteDate
      this.searchFormObj.Director = form.Director
      this.searchFormObj.key = form.key
      this.getListFn()
    },
    async getListFn() {
      let params1 = {
        "key": this.searchFormObj.key,
        "CheckDate": this.searchFormObj.CheckDate,
        "CheckResult": this.searchFormObj.CheckResult,
        "Host": this.searchFormObj.Host,
        "PlanCompleteDate": this.searchFormObj.PlanCompleteDate,
        "Director": this.searchFormObj.Director,
        "pageIndex": this.pageOptions.pageIndex,
        "pageSize": this.pageOptions.pageSize,
        "orderByFileds": '',
      }
      let { response } = await getLeftPageList(params1)
      response.data.map(item => {
        if (item.CheckDate != null && item.CheckDate != undefined && item.CheckDate != '') {
          item.CheckDate = dayjs(item.CheckDate).format('YYYY-MM-DD');
        }
        if (item.PlanCompleteDate != null && item.PlanCompleteDate != undefined && item.PlanCompleteDate != '') {
          item.PlanCompleteDate = dayjs(item.PlanCompleteDate).format('YYYY-MM-DD');
        }
      });
      this.tableList = response.data
      this.pageOptions.total = response.dataCount
    },
    handleCheckboxChange({ checked, records }) {
      this.selectList = records;
    },
    // 新增
    add() {
      this.editItemObjLeft = {}
      this.InfoDialogLeftkey++
      this.showInfoDialogLeft = true
    },
    // 编辑
    modify() {
      if (this.selectList.length <= 0) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'red' });
        return false;
      }
      if (this.selectList.length > 1) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('只能选择一条数据'), color: 'red' });
        return false;
      }

      this.editItemObjLeft = this.selectList[0]
      this.InfoDialogLeftkey++
      this.showInfoDialogLeft = true
    },
    // 删除
    async removeChange() {
      if (this.selectList.length <= 0) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'red' });
        return false;
      }
      let arr = []
      this.selectList.map(item => {
        arr.push(item.ID);
      });
      const res = await DeleteLeft(arr);
      if (res.success) {
        this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'green' });
        this.getListFn()
      }
    },
    closePopupLeft() {
      this.showInfoDialogLeft = false
      this.getListFn()
    }
  }
};
</script>
<style scoped>
.titName {
    flex: 1;
    height: 40px;
    font-size: 20px;
    color: #000;
    text-align: center;
    cursor: pointer;
    width: auto;
    height: 40px;
    text-align: center;
    line-height: 40px;
}
.active {
    width: auto;
    height: 40px;
    text-align: center;
    line-height: 40px;
    background: #3dcd58;
    color: #fff;
    font-weight: bold;
    border-radius: 5px;
    font-size: 20px;
}
.table-content {
    height: 100%;
}
.table-box {
    height: calc(100% - 240px);
}
/deep/ .vxe-checkbox--icon::before {
    color: #5fea72 !important;
}
/deep/ .vxe-icon-checkbox-checked-fill::before {
    color: #5fea72 !important;
}
</style>
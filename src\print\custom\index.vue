<!-- eslint-disable no-undef -->
<template>
    <a-modal v-model="dialog" title="模板设计" :footer="null" width="90%">
        <a-card>
            <a-row :gutter="[8, 0]" style="margin-bottom: 10px">
                <a-col :span="4">
                    <!-- <a-select v-model="mode" showSearch @change="changeMode" :defaultValue="0" option-label-prop="label" style="width: 100%">
                        <a-select-option v-for="(opt, idx) in modeList" :key="idx" :label="opt.name" :value="idx">
                            {{ opt.name }}
                        </a-select-option>
                    </a-select> -->
                </a-col>
                <a-col :span="20">
                    <a-space>
                        <!-- 纸张设置 -->
                        <a-button-group>
                            <template v-for="(value, type) in paperTypes">
                                <a-button :type="curPaperType === type ? 'primary' : 'info'" @click="setPaper(type, value)" :key="type">
                                    {{ type }}
                                </a-button>
                            </template>
                            <a-popover v-model="paperPopVisible" title="设置纸张宽高(mm)" trigger="click">
                                <div slot="content">
                                    <a-input-group compact style="margin: 10px 10px">
                                        <a-input type="number" v-model="paperWidth" style="width: 100px; text-align: center" placeholder="宽(mm)" />
                                        <a-input style="width: 30px; border-left: 0; pointer-events: none; backgroundcolor: #fff" placeholder="~" disabled />
                                        <a-input type="number" v-model="paperHeight" style="width: 100px; text-align: center; border-left: 0" placeholder="高(mm)" />
                                    </a-input-group>
                                    <a-button type="primary" style="width: 100%" @click="otherPaper">确定</a-button>
                                </div>
                                <a-button :type="'other' == curPaperType ? 'primary' : ''">自定义纸张</a-button>
                            </a-popover>
                        </a-button-group>
                        <a-button type="text" icon="zoom-out" @click="changeScale(false)"></a-button>
                        <a-input-number
                            :value="scaleValue"
                            :min="scaleMin"
                            :max="scaleMax"
                            :step="0.1"
                            disabled
                            style="width: 70px"
                            :formatter="value => `${(value * 100).toFixed(0)}%`"
                            :parser="value => value.replace('%', '')"
                        />
                        <a-button type="text" icon="zoom-in" @click="changeScale(true)"></a-button>
                        <!-- 预览/打印 -->
                        <a-button-group>
                            <a-button type="primary" icon="eye" @click="preView">预览</a-button>
                            <a-button type="primary" @click="print">
                                直接打印
                                <a-icon type="printer" />
                            </a-button>
                        </a-button-group>
                        <!-- 保存/清空 -->
                        <a-button-group>
                            <a-button type="primary" icon="save" @click="save">保存</a-button>
                            <a-popconfirm title="是否确认清空?" okType="danger" okText="确定清空" @confirm="clearPaper">
                                <a-icon slot="icon" type="question-circle-o" style="color: red" />
                                <a-button type="danger">
                                    清空
                                    <a-icon type="close" />
                                </a-button>
                            </a-popconfirm>
                        </a-button-group>
                    </a-space>
                </a-col>
            </a-row>
            <a-row :gutter="[8, 0]">
                <a-col :span="4">
                    <a-card style="height: (100vh - 160px)">
                        <a-row>
                            <a-col :span="24" class="rect-printElement-types hiprintEpContainer"></a-col>
                        </a-row>
                    </a-card>
                </a-col>
                <a-col :span="14">
                    <a-card class="card-design">
                        <div id="hiprint-printTemplate" class="hiprint-printTemplate"></div>
                    </a-card>
                </a-col>
                <a-col :span="6" class="params_setting_container">
                    <a-card>
                        <a-row class="hinnn-layout-sider">
                            <div id="PrintElementOptionSetting"></div>
                        </a-row>
                    </a-card>
                </a-col>
            </a-row>
            <!-- 预览 -->
            <print-preview ref="preView" />
        </a-card>
    </a-modal>
</template>

<script>
import { PrintTplSaveTPL } from '@/api/systemManagement/printTemplate.js';

import printPreview from './preview';

import { hiprint } from '../../index';
import providers from './providers';

let hiprintTemplate;
export default {
    name: 'PrintCustom',
    components: { printPreview },
    props: {
        seleteItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            dialog: false,
            // deactivated: false,
            // 模板选择
            mode: 0,
            modeList: [
                {
                    name: '标签',
                    value: 'aProviderModule',
                    type: 1
                },
                {
                    name: '订单',
                    value: 'bProviderModule',
                    type: 2
                }
            ],
            // 当前纸张
            curPaper: {
                type: 'other',
                width: 220,
                height: 80
            },
            // 纸张类型
            paperTypes: {
                A3: {
                    width: 420,
                    height: 296.6
                },
                A4: {
                    width: 210,
                    height: 296.6
                },
                A5: {
                    width: 210,
                    height: 147.6
                },
                B3: {
                    width: 500,
                    height: 352.6
                },
                B4: {
                    width: 250,
                    height: 352.6
                },
                B5: {
                    width: 250,
                    height: 175.6
                }
            },
            // 映射纸张类型
            paperType: [
                { type: 'A3', width: 420, height: 296.6 },
                { type: 'A4', width: 210, height: 296.6 },
                { type: 'A5', width: 210, height: 147.6 },
                { type: 'B3', width: 500, height: 352.6 },
                { type: 'B4', width: 250, height: 352.6 },
                { type: 'B5', width: 250, height: 175.6 }
            ],
            scaleValue: 1,
            scaleMax: 5,
            scaleMin: 0.5,
            // 自定义纸张
            paperPopVisible: false,
            paperWidth: 220,
            paperHeight: 80,
            lastjson: ''
        };
    },
    computed: {
        curPaperType() {
            let type = 'other';
            let types = this.paperTypes;
            for (const key in types) {
                let item = types[key];
                let { width, height } = this.curPaper;
                if (item.width === width && item.height === height) {
                    type = key;
                }
            }
            return type;
        }
    },
    mounted() {
        this.init();
    },
    // activated() {
    //     // 重新再实例化, 处理切换demo, 无法拖拽问题
    //     if (this.deactivated) {
    //         this.init();
    //         this.deactivated = false;
    //     }
    // },
    // deactivated() {
    //     this.deactivated = true;
    // },
    methods: {
        // init() {
        //     this.modeList = providers.map(e => {
        //         return { type: e.type, name: e.name, value: e.value };
        //     });
        //     console.dir(this.modeList)
        //     this.changeMode();
        // },
        // changeMode() {},
        init(item) {
            let { mode } = this;
            let provider = providers[mode];
            hiprint.init({
                providers: [provider.f]
            });
            console.log(provider);
            // eslint-disable-next-line no-undef
            $('.hiprintEpContainer').empty();
            hiprint.PrintElementTypeManager.build('.hiprintEpContainer', provider.value);
            // eslint-disable-next-line no-undef
            $('#hiprint-printTemplate').empty();
            let templates = this.$ls.get('KEY_TEMPLATES', {});
            console.log(templates);
            // console.log(templates[provider.value] + '*** 初始化数据***');
            // console.log(item);
            let template;
            let Atype;
            let valobj = {};
            // 处理模板 纸张返回大小展示
            if (item) {
                if (item.TplJson) {
                    template = JSON.parse(item.TplJson);
                    Atype = this.sizeinit(template, this.paperType);
                    valobj = this.paperTypes[Atype] || template.panels[0]; // 展示自定义 和 非自定义
                    console.log(Atype, this.paperTypes, valobj);
                    this.setPaper(Atype, valobj);
                } else {
                    template = {};
                }
            } else {
                template = {};
            }
            // let template = item ? (item.TplJson ? JSON.parse(item.TplJson) : {}) : this.seleteItem.TplJson ? JSON.parse(this.seleteItem.TplJson) : {};
            hiprintTemplate = new hiprint.PrintTemplate({
                template: template,
                dataMode: 1, // 1:getJson 其他：getJsonTid 默认1
                history: false, // 是否需要 撤销重做功能
                onDataChanged: (type, json) => {
                    console.log(type); // 新增、移动、删除、修改(参数调整)、大小、旋转
                    console.log(json); // 返回 template
                    // 更新模板
                    // hiprintTemplate.update(json);
                    // console.log(hiprintTemplate.historyList)
                },
                settingContainer: '#PrintElementOptionSetting',
                paginationContainer: '.hiprint-printPagination'
            });
            hiprintTemplate.design('#hiprint-printTemplate');
            // 获取当前放大比例, 当zoom时传true 才会有
            this.scaleValue = hiprintTemplate.editingPanel.scale || 1;
        },
        /**
         * 设置纸张大小
         * @param type [A3, A4, A5, B3, B4, B5, other]
         * @param value {width,height} mm
         */
        setPaper(type, value) {
            try {
                if (Object.keys(this.paperTypes).includes(type)) {
                    this.curPaper = { type: type, width: value.width, height: value.height };
                    setTimeout(() => {
                        hiprintTemplate.setPaper(value.width, value.height);
                    }, 0);
                } else {
                    this.curPaper = { type: 'other', width: value.width, height: value.height };
                    setTimeout(() => {
                        hiprintTemplate.setPaper(value.width, value.height);
                    }, 0);
                }
            } catch (error) {
                console.log(error);
                this.$message.error(`操作失败: ${error}`);
            }
        },
        changeScale(big) {
            let scaleValue = this.scaleValue;
            if (big) {
                scaleValue += 0.1;
                if (scaleValue > this.scaleMax) scaleValue = 5;
            } else {
                scaleValue -= 0.1;
                if (scaleValue < this.scaleMin) scaleValue = 0.5;
            }
            if (hiprintTemplate) {
                // scaleValue: 放大缩小值, false: 不保存(不传也一样), 如果传 true, 打印时也会放大
                hiprintTemplate.zoom(scaleValue, true);
                this.scaleValue = scaleValue;
            }
        },
        otherPaper() {
            let value = {};
            value.width = this.paperWidth;
            value.height = this.paperHeight;
            this.paperPopVisible = false;
            this.setPaper('other', value);
        },
        preView() {
            let { width } = this.curPaper;
            this.$refs.preView.show(hiprintTemplate, {}, width);
        },
        print() {
            // if (window.hiwebSocket.opened) {
            const printerList = hiprintTemplate.getPrinterList();
            console.log(printerList);
            hiprintTemplate.print({}, {});
            // return;
            // }
            // this.$message.error('客户端未连接,无法直接打印');
        },
        save() {
            let { mode } = this;
            let provider = providers[mode];
            this.setTemplate({
                name: provider.value,
                json: hiprintTemplate.getJson()
            });
        },
        async setTemplate(payload) {
            let templates = this.$ls.get('KEY_TEMPLATES', {});
            console.log(JSON.stringify(payload.json));
            templates[payload.name] = payload.json;
            await PrintTplSaveTPL({ ID: this.seleteItem.ID, TplJson: JSON.stringify(payload.json) });
            this.$ls.set('KEY_TEMPLATES', templates);
            this.$parent.GetSchedulerCenterGetJobs();
            await this.$message.info('保存成功');
        },
        clearPaper() {
            try {
                hiprintTemplate.clear();
            } catch (error) {
                this.$message.error(`操作失败: ${error}`);
            }
        },
        // 纸张模板大小回显
        sizeinit(init1, type) {
            let Atype;
            let obj = init1.panels[0];
            type.forEach(item => {
                if (item.width == obj.width && item.height == obj.height) {
                    Atype = item.type;
                }
            });
            return Atype || 'other'; // 不匹配 就算自定义
        }
    }
};
</script>

<style lang="scss" scoped>
// build 拖拽
::v-deep .hiprint-printElement-type > li > ul > li > a {
    padding: 4px 4px !important;
    color: #1296db !important;
    line-height: 1 !important;
    height: auto !important;
    text-overflow: ellipsis !important;
}

// 默认图片
::v-deep .hiprint-printElement-image-content {
    img {
        content: url('~@/assets/logo.png');
    }
}

// 设计容器
.card-design {
    overflow: hidden;
    overflow-x: auto;
    overflow-y: auto;
}
</style>

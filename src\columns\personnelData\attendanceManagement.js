// 上班考勤信息
export const upattendanceColums = [
    {
        text: '序号',
        value: 'Index',
        width: 50,
        sortable: true
    },
    {
        text: '日期',
        value: 'Date',
        width: 110,
        sortable: true
    },
    {
        text: '工段',
        value: 'Segment',
        width: 120,
        sortable: true
    },
    {
        text: '员工号',
        value: 'StaffCode',
        width: 100,
        sortable: true
    },
    {
        text: '姓名',
        value: 'StaffName',
        width: 80,
        sortable: true
    },
    {
        text: '班次',
        value: 'Shift',
        width: 80,
        sortable: true
    },
    {
        text: '班组',
        value: 'Team',
        width: 80,
        sortable: true
    },
    {
        text: '上班打卡',
        value: 'ClockInTime',
        width: 160,
        sortable: true
    },
    {
        text: '员工分类',
        value: 'Type',
        width: 80,
        sortable: true
    },
    {
        text: '类型',
        value: 'Type2',
        width: 80,
        sortable: true
    },
    {
        text: '类别',
        value: 'Type3',
        width: 80,
        sortable: true
    },
    { text: '', width: 0, align: 'center', value: 'actions1', sortable: false }
    // {
    //     text: '来源',
    //     value: 'Source',
    //     width: 80,
    //     sortable: true
    // }
    // { text: '操作',  width: 120, align: 'center', value: 'actions', sortable: true }
];
// 下班考勤信息
export const attendanceColums = [
    {
        text: '序号',
        value: 'Index',
        width: 50,
        sortable: true
    },
    {
        text: '日期',
        value: 'Date',
        width: 110,
        sortable: true
    },
    {
        text: '工段',
        value: 'Segment',
        width: 110,
        sortable: true
    },
    {
        text: '员工号',
        value: 'StaffCode',
        width: 100,
        sortable: true
    },
    {
        text: '姓名',
        value: 'StaffName',
        width: 80,
        sortable: true
    },


    {
        text: '班次',
        value: 'Shift',
        width: 80,
        sortable: true
    },
    {
        text: '班组',
        value: 'Team',
        width: 80,
        sortable: true
    },
    {
        text: '人员类型',
        value: 'Type',
        width: 80,
        sortable: true
    },
    {
        text: '上班打卡',
        value: 'ClockInTime',
        width: 160,
        sortable: true
    },
    {
        text: '下班打卡',
        value: 'ClockOffTime',
        width: 160,
        sortable: true
    },
    {
        text: '打卡时长',
        value: 'HrTimes',
        width: 100,
        sortable: true,
        align: true
    },
    // {
    //     text: '休息时长',
    //     value: 'RestTimes',
    //     width: 140,
    //     sortable: true
    // },
    // {
    //     text: '有效工时(UWB)',
    //     value: 'WorkTimes',
    //     width: 140,
    //     sortable: true,
    //     align: 'right'
    // },
    {
        text: 'UWB考勤工时',
        value: 'UwbWorkTimes',
        width: 140,
        sortable: true,
        align: 'right'
    },
    // {
    //     text: '工时',
    //     value: 'ConfirmTimes',
    //     width: 100,
    //     sortable: true,
    //     align: 'right'
    // }
    { text: '', width: 0, align: 'center', value: 'actions1', sortable: false }
    // { text: '操作',  width: 120, align: 'center', value: 'actions', sortable: true }
];
// 人员计件
export const personnelPieceworkColumns = [
    {
        text: '序号',
        value: 'Index',
        width: 50,
        sortable: true
    },
    {
        text: '日期',
        value: 'Date',
        width: 110,
        sortable: true
    },
    {
        text: '工段',
        value: 'Segment',
        width: 110,
        sortable: true
    },
    {
        text: '员工号',
        value: 'StaffCode',
        width: 100,
        sortable: true
    },
    {
        text: '姓名',
        value: 'StaffName',
        width: 80,
        sortable: true
    },


    {
        text: '班次',
        value: 'Shift',
        width: 80,
        sortable: true
    },
    {
        text: '班组',
        value: 'Team',
        width: 80,
        sortable: true
    },
    {
        text: '员工分类',
        value: 'Type',
        width: 80,
        sortable: true
    },


    {
        text: '有效工时(UWB)',
        value: 'WorkTimes',
        width: 140,
        sortable: true,
        align: true
    },
    {
        text: '产量',
        value: 'Yield',
        width: 100,
        sortable: true,
        align: 'right'
    },
    {
        text: '计件岗位',
        value: 'PieceworkPost',
        width: 130,
        sortable: true
    },
    {
        text: '计件工资',
        value: 'PieceworkWage',
        width: 100,
        sortable: true,
        align: 'right'
    },
    {
        text: '超产奖励',
        value: 'Bonus',
        width: 100,
        sortable: true,
        align: 'right'
    },
    {
        text: '类型',
        value: 'Type2',
        width: 80,
        sortable: true
    },
    {
        text: '类别',
        value: 'Type3',
        width: 80,
        sortable: true
    },
    { text: '', width: 0, align: 'center', value: 'actions1', sortable: false }

    // { text: '操作',  width: 120, align: 'center', value: 'actions', sortable: true }
];

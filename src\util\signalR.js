import * as signalR from '@microsoft/signalr';
import { configUrl } from '@/config';
// const baseURL = 'http://10.133.128.13:30012/api2/chatHub', api = '';
// const baseURL = 'baseURL_API2', api = '/api2/chatHub';
export default {
    SR: {},
    //初始化连接
    initSR: function (pars) {
        const pbu = pars.baseURL;
        const baseURL = configUrl[process.env.VUE_APP_SERVE][pbu]; // 配置服务url
        const that = this;
        console.log(baseURL, pars.api);
        // 建立连接
        that.SR = new signalR.HubConnectionBuilder()
            .withUrl(baseURL + pars.api)
            .configureLogging(signalR.LogLevel.Information)
            .build();
        // 注册方法 
        that.SR.on("ReceiveMessage", function(res) {
            console.log(res);
        });
        that.SR.on("receiveupdate", function(res) {
            console.log(res);
        });
        that.SR.on("recordStatusCount", function(res) {
            console.log(res);
        });
        that.SR.on("recordinfo", function(res) {
            console.log(res);
        });
        // 开始连接
        that.SR
            .start()
            .then( () => console.log("Connected!"))
            .catch((err) => {
                console.log(err);
            });
    },
    // 停止连接（这个方法好像没啥用，先放着吧）
    stopSR: function () {
        const that = this;
        that.SR.stop();
    }
};
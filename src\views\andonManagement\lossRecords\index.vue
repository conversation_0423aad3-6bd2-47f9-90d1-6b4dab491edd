// 损耗记录列表
<template>
    <div class="line-side-view">
        <div class="line-side-main">
            <SearchForm ref="contactTorm" class="mt-2" :searchinput="searchinput"
                :show-from="showFrom" @searchForm="searchForm" />
            <v-card outlined>
                <div class="form-btn-list">
                    <!-- 搜索栏 -->
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary">
                        <v-icon @click="getDataList">mdi-cached</v-icon>
                    </v-btn>
                    <!-- <v-btn color="primary" @click="operaClick({})">新增</v-btn>
                    <v-btn color="primary" @click="sureItems()">{{ $t('GLOBAL._PLSC') }}</v-btn> -->
                </div>
                <Tables :showSelect="false" :headers="headers" :desserts="desserts" :loading="loading"
                    :page-options="pageOptions" :btn-list="btnList" :dictionaryList="dictionaryList"
                    @selectePages="selectePages" @itemSelected="selectedItems" @toggleSelectAll="selectedItems"
                    @tableClick="tableClick"></Tables>
            </v-card>
            <detailsDialog ref="detailsDialog" :opera-obj="operaObj" @handlePopup="handlePopup" />
            <updateDialog ref="updateDialog" :opera-obj="operaObj" @handlePopup="handlePopup" />
        </div>
    </div>
</template>
<script>
import { getReasonInfoList } from '@/api/factoryPlant/reasonInfo.js';
import { DeleteUpgradeRule } from '@/api/andonManagement/upgradeRule.js';
import { EventDefectPageList } from '@/api/andonManagement/lossRecords.js';
import { lossRecordsColumns } from '@/columns/andonManagement/lossRecords.js';
import Util from '@/util';
export default {
    name: 'UpgradeRule',
    components: {
        detailsDialog: () => import('./components/detailsDialog.vue'),
        updateDialog: () => import('./components/updateDialog.vue')
    },
    data() {
        return {
            operaObj: {},
            showFrom: false,
            headers: lossRecordsColumns,
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            deleteId: [],
            selectedList: [],
            desserts: [],
            reasonList: [],
            alarmTypeRootList: [],
            subAlarmTypeList: [],
            alarmTypeList: [],
            startTime: '',
            endTime: '',
            searchParams: {}
        }
    },
    computed: {
        //查询条件
        searchinput() {
            return [
                // 开始时间
                {
                    key: 'startTime',
                    type: 'date',
                    icon: '',
                    value: this.startTime,
                    label: '开始时间'
                },
                // 结束时间
                {
                    key: 'endTime',
                    type: 'date',
                    icon: '',
                    value: this.endTime,
                    label: '结束时间'
                },
                 // 原因
                {
                    key: 'reasoncode',
                    icon: '',
                    type: 'select',
                    selectData: this.$changeSelectItems(this.reasonList, 'ReasontreeCode', 'ReasontreeName'),
                    value: '',
                    label: '原因'
                },
            ];
        },
        btnList() {
            return [
                { text: "关联订单号", icon: '', code: 'order', type: 'primary', isDisabled: 'WoId' },
                { text: "关联安灯事件", icon: '', code: 'andon', type: 'primary' }
            ];
        },
        dictionaryList(){
            return [
                {arr: this.reasonList, key: 'ReasonCode', val: 'ReasontreeCode', text: 'ReasontreeName'}
            ]
        }        
    },
    async created(){
        this.init();
        this.getDataList();
        this.getReasonList();
    },
    methods: {
        init() {
            const startTime = Util.formatDate(new Date().getTime() - 6*24*60*60*1000);
            const endTime = Util.formatDate(new Date());
            this.startTime = startTime
            this.endTime = endTime
            this.searchParams = { startTime, endTime }
        },
        // 获取原因树
        async getReasonList() {
             let params = {
                key: "",
                pageIndex: 1,
                pageSize: 999
            };
            const res = await getReasonInfoList(params);
            const { success, response } = res;
            if(success){
                this.reasonList = response.data || []
            }
        },
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        // 操作栏按钮
        tableClick(item, type) {
            this.operaObj = item;
            switch (type) {
                // 关联订单号
                case 'order':
                    this.operaClick(item)
                    break;
                // 关联安灯事件
                case 'andon':
                    this.$refs.detailsDialog.dialog = true
                    break;
                default:
                    break;
            }
        },
        // 获取全部表格数据
        async getDataList() {
            this.loading = true;
            const { startTime, endTime } = this.searchParams
            let params = {
                ...this.searchParams,
                startTime: (startTime || this.startTime) + ' 00:00:00',
                endTime: (endTime || this.endTime) + ' 23:59:59',
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await EventDefectPageList(params);
            const { success, response } = res || {};
            const { data, dataCount, page } = response || {};
            this.desserts = []
            if (success && data) {
                const arr = data || [];
                this.pageOptions.total = dataCount;
                this.pageOptions.page = page;
                this.desserts = data;
            }
            this.loading = false;
        },
        // 查询数据
        searchForm(v) {
            this.searchParams = v;
            this.getDataList();
        },
        // 新增
        operaClick(o) {
            this.operaObj = o || {};
            this.$refs.updateDialog.dialog = true;
        },
        // 批量删除
        sureItems(){
            if (this.selectedList.length > 0) {
                this.deleteId = ''
                this.sureDelete();
            } else {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'error' });
            }
        },
        // 删除二次确认
        sureDelete() {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    const params = [];
                    if (this.deleteId) {
                        params.push(this.deleteId);
                    } else {
                        this.selectedList.forEach(e => {
                            params.push(e.ID);
                        });
                    }
                    const res = await DeleteUpgradeRule(params);
                    this.selectedList = [];
                    this.deleteId = '';
                    const { success, msg } = res;
                    if (success) {
                        this.pageOptions.pageCount = 1;
                        this.getDataList();
                        this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 根据子组件返回来值
        handlePopup(type, data) {
            this.getDataList();
            // switch (type) {
            //     case 'refresh':
            //         this.getDataList();
            //         break;
            //     case 'detail':
            //         this.receivedorderid = data?.ID
            //         this.$refs.materailDetailDialog.dialog = true;
            //         break;
            //     default:
            //         break;
            // }
        }
    }
};
</script>
<style lang="scss" scoped>
.line-side-view {
    display: flex;

    .line-side-main {
        flex: 1;
        width: 100%;

        .v-data-table {
            width: 100%;
        }
    }
}
</style>
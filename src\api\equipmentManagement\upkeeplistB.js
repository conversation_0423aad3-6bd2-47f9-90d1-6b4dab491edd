import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';
let DFM = 'baseURL_DFM';


// 任务列表
export function GetMaintainWoPageList(data) {
    const api = '/api/MaintainWo/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}
// 任务编辑
export function GetMaintainWoSaveForm(data) {
    const api = '/api/MaintainWo/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
//任务生成
export function GetCreateMaintainWoByItems(data, id,PlanStartDate) {
    const api = `/api/MaintainWo/CreateMaintainWoByItems?deviceId=${id}&PlanCheckDate=${PlanStartDate}`
    return getRequestResources(baseURL, api, 'post', data);
}
//任务删除
export function GetMaintainWoDelete(data) {
    const api = '/api/MaintainWo/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//点检任务删除
export function GetMaintainWoItemDelete(data) {
    const api = '/api/MaintainWoItem/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

//点检任务
export function GetMaintainWoItemPageList(data) {
    const api = '/api/MaintainWoItem/GetListByWoId'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMaintains(data) {
    const api = '/api/MaintainWoItem/Maintains'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMaintain(data) {
    const api = '/api/MaintainWoItem/Maintain'
    return getRequestResources(baseURL, api, 'post', data);
}
//点检状态
export function GetMaintainWoStatus(data) {
    const api = '/api/DataItemDetail/GetDetailTree?itemCode=MaintainWoStatus'
    return getRequestResources(DFM, api, 'post', data);
}
//派工
export function GetMaintainWoAssign(data) {
    const api = '/api/MaintainWo/Assign'
    return getRequestResources(baseURL, api, 'post', data);
}

//导出
export function GetMaintainWoExport(data) {
    const api = '/api/MaintainWo/Export'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetPartsInventoryFromSap(data) {
    const api = '/api/PartsInventory/GetListFromSap'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetPartsInventorySaveForm(data) {
    const api = '/api/PartsInventory/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//备件列表
export function GetPartsHistoryDetailGetList(data) {
    const api = '/api/PartsHistoryDetail/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//备件删除
export function GetPartsHistoryDetailDelete(data) {
    const api = '/api/PartsHistoryDetail/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
//备件新增
export function GetPartsHistoryDetailSaveForm(data) {
    const api = '/api/PartsHistoryDetail/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//备件申请
export function GetPartsHistoryDetailRequest(data) {
    const api = '/api/PartsHistoryDetail/Request'
    return getRequestResources(baseURL, api, 'post', data);
}

//筛选物料
export function GetPartsListByLike(data) {
    const api = '/api/Parts/GetListByLike'
    return getRequestResources(baseURL, api, 'post', data);
}

//库存查询
export function GetPartsListFromSapByPartCode(data) {
    const api = '/api/PartsInventory/GetListFromSapByPartCode'
    return getRequestResources(baseURL, api, 'post', data);
}
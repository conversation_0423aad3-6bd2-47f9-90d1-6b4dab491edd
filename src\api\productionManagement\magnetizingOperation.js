import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TRACE; // 配置服务url
// 获取产线
export function GetFeedingProcDevice(data) {
    return request({
        url: baseURL + '/trace/Feeding/GetEquipmentDevice',
        method: 'post',
        data
    });
}
//充磁录入
export function ccintoBatchAndBatchRech(data) {
    return request({
        url: baseURL + '/trace/wo/ccintoBatchAndBatchRech',
        method: 'post',
        data
    });
}

//充磁列表
export function BatchGetPageList(data) {
    return request({
        url: baseURL + '/trace/BatchFlowRecord/GetPageList',
        method: 'post',
        data
    });
}



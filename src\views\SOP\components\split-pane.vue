<template>
  <div class="sop-split-pane" :class="{'is-vertical': split === 'vertical'}" @mousemove="handleMouseMove" @mouseup="handleMouseUp" @mouseleave="handleMouseLeave">
    <div class="sop-split-pane__left" :style="{[sizeAttr]: `${leftSize}%`}" ref="left">
      <slot name="pane1"></slot>
    </div>
    <div class="sop-split-pane__resizer" @mousedown="handleMouseDown"></div>
    <div class="sop-split-pane__right" :style="{[sizeAttr]: `${100 - leftSize}%`}" ref="right">
      <slot name="pane2"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SplitPane',
  props: {
    split: {
      type: String,
      default: 'vertical',
      validator: value => ['vertical', 'horizontal'].includes(value)
    },
    minPercent: {
      type: Number,
      default: 10
    },
    maxPercent: {
      type: Number,
      default: 90
    },
    defaultPercent: {
      type: Number,
      default: 50
    }
  },
  data() {
    return {
      leftSize: this.defaultPercent,
      dragging: false,
      startPageX: 0,
      startPageY: 0,
      startSize: 0
    }
  },
  computed: {
    sizeAttr() {
      return this.split === 'vertical' ? 'width' : 'height'
    }
  },
  methods: {
    handleMouseDown(e) {
      this.dragging = true
      this.startPageX = e.pageX
      this.startPageY = e.pageY
      this.startSize = this.leftSize
      document.body.style.cursor = this.split === 'vertical' ? 'col-resize' : 'row-resize'
      document.body.style.userSelect = 'none'
    },
    handleMouseMove(e) {
      if (!this.dragging) return

      const container = this.$el.getBoundingClientRect()
      const containerSize = this.split === 'vertical' ? container.width : container.height
      const offset = this.split === 'vertical' ? (e.pageX - this.startPageX) : (e.pageY - this.startPageY)
      const newSize = this.startSize + (offset / containerSize * 100)

      this.leftSize = Math.min(Math.max(this.minPercent, newSize), this.maxPercent)
      this.$emit('resize', this.leftSize)
    },
    handleMouseUp() {
      this.dragging = false
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    },
    handleMouseLeave() {
      if (this.dragging) {
        this.handleMouseUp()
      }
    }
  },
  beforeDestroy() {
    // 清理样式
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }
}
</script>

<style lang="scss" scoped>
.sop-split-pane {
  display: flex;
  width: 100%;
  height: 100%;

  &.is-vertical {
    flex-direction: row;

    .sop-split-pane__resizer {
      width: 6px;
      margin: 0 -3px;
      cursor: col-resize;

      &:hover {
        background-color: #ebeef5;
      }
    }
  }

  &:not(.is-vertical) {
    flex-direction: column;

    .sop-split-pane__resizer {
      height: 6px;
      margin: -3px 0;
      cursor: row-resize;

      &:hover {
        background-color: #ebeef5;
      }
    }
  }

  &__left, &__right {
    overflow: hidden;
    position: relative;
  }

  &__resizer {
    position: relative;
    z-index: 1;
    background-color: transparent;
    transition: background-color 0.2s;

    &:active {
      background-color: #409eff !important;
    }
  }
}
</style>

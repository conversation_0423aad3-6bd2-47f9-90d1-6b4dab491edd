<template>
    <v-card>
        <v-card-title class="text-h6 justify-space-between primary lighten-2">
            {{ $t('TPM_SBGL_SBWXJL._TZBYGZ') }}
            <v-icon @click="closePopup()">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid">
                <v-row class="mt-5">
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-autocomplete
                            v-model="form.MaintainProjectId"
                            :items="ruleList"
                            item-value="MaintainProjectId"
                            item-text="MaintainProject"
                            return-object
                            outlined
                            required
                            :rules="[v => !!v || $t('GLOBAL._MANDATORY')]"
                            dense
                            :label="$t('TPM_SBGL_SBWXJL._GZMC')"
                        ></v-autocomplete>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select
                            :label="$t('TPM_SBGL_SBWXJL._GZZ')"
                            required
                            :rules="[v => !!v || $t('GLOBAL._MANDATORY')]"
                            outlined
                            :items="cycleList"
                            dense
                            v-model="form.MaintainCycle"
                            item-value="ItemValue"
                            item-text="ItemName"
                        ></v-select>
                    </v-col>
                    <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <v-textarea :label="$t('TPM_SBGL_SBWXJL._XGYY')" required :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" height="70" dense outlined v-model="form.Remark"></v-textarea>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
        <v-divider></v-divider>

        <v-card-actions>
            <v-spacer></v-spacer>
            <!-- <v-checkbox class="mr-auto" v-model="isChecked" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox> -->
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { getMaintainRuleList } from '@/api/equipmentManagement/Repair.js';
export default {
    props: {
        ruleForm: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            cycleList: [],
            ruleList: [],
            valid: false,
            isChecked: true,
            form: {
                MaintainProjectId: '',
                MaintainProject: '',
                MaintainCycle: '',
                Remark: ''
            }
        };
    },
    created() {
        this.getCycle();
        this.getRule();

        if (this.ruleForm) {
            for (const key in this.form) {
                this.form[key] = this.ruleForm[key];
            }
        }
    },
    methods: {
        async getCycle() {
            this.cycleList = await this.$getDataDictionary('Cycle');
        },
        async getRule() {
            let resp = await getMaintainRuleList({});
            this.ruleList = resp.response;
        },
        submitForm() {
            if (!this.$refs.form.validate()) return false;

            let params = JSON.parse(JSON.stringify(this.form));
            if (params.MaintainProjectId) {
                params.MaintainProject = params.MaintainProjectId.MaintainProject;
                params.MaintainProjectId = params.MaintainProjectId.MaintainProjectId;
            }
            this.$emit('closePopup', params);
        },
        closePopup() {
            this.$emit('closePopup');
        }
    }
};
</script>

<style>
</style>
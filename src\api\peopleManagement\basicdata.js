import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url
//获取人员基础数据
export function StaffGetPageList(data) {
    return request({
        url: baseURL + '/api/Staff/GetPageList',
        method: 'post',
        data
    });
}
export function StaffSiteGetList(data) {
    return request({
        url: baseURL + '/api/Staff/GetList',
        method: 'post',
        data
    });
}
//新增人员
export function StaffSaveForm(data) {
    return request({
        url: baseURL + '/api/Staff/SaveForm',
        method: 'post',
        data
    });
}
//删除
export function StaffDelete(data) {
    return request({
        url: baseURL + '/api/Staff/Delete',
        method: 'post',
        data
    });
}
// 计件标准查询
export function GetEntity(id) {
    return request({
        url: baseURL + '/api/PieceBasis/GetEntity/' + id,
        method: 'get',
    });
}
//技能

//获取技能列表
export function SkillGetPageList(data) {
    return request({
        url: baseURL + '/api/Skill/GetPageList',
        method: 'post',
        data
    });
}
export function SkillGetList(data) {
    return request({
        url: baseURL + '/api/Skill/GetList',
        method: 'post',
        data
    });
}

//新增技能或修改
export function SkillSaveForm(data) {
    return request({
        url: baseURL + '/api/Skill/SaveForm',
        method: 'post',
        data
    });
}
//删除技能
export function SkillDelete(data) {
    return request({
        url: baseURL + '/api/Skill/Delete',
        method: 'post',
        data
    });
}


// 员工关联技能列表
export function StaffSkillGetPageList(data) {
    return request({
        url: baseURL + '/api/StaffSkill/GetPageList',
        method: 'post',
        data
    });
}

// 员工技能关联
export function StaffSkillSaveForm(data) {
    return request({
        url: baseURL + '/api/StaffSkill/SaveForm',
        method: 'post',
        data
    });
}
// 员工技能删除
export function StaffSkillDelete(data) {
    return request({
        url: baseURL + '/api/StaffSkill/Delete',
        method: 'post',
        data
    });
}

// 原因明细管理
import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url

//获取tree
export function GetReasontree(data) {
    return request({
        url: baseURL + '/api/ReasontreeDetail/GetReasontree',
        method: 'post',
        data
    });
}

// 新增、编辑
export function SaveForm(data) {
    return request({
        url: baseURL + '/api/ReasontreeDetail/SaveForm',
        method: 'post',
        data
    });
}

// 删除
export function DeleteDetail(data) {
    return request({
        url: baseURL + '/api/ReasontreeDetail/Delete',
        method: 'post',
        data
    });
}

// 列表
export function GetPageList(data) {
    return request({
        url: baseURL + '/api/ReasontreeDetail/GetPageList',
        method: 'post',
        data
    });
}

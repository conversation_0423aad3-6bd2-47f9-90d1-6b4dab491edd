
export const processMappingColum = [
    // { text: '', value: 'data-table-expand' },
    {
        text: 'SAP同步状态',
        value: 'SendSapFlag',
        dictionary: true,
        width: 130
    },
    {
        text: '工单状态',
        value: 'WoStatus',
        width: 100
    },
    {
        text: 'SAP订单',
        value: 'SapWoCode',
        width: 100
    },
    {
        text: '工单',
        value: 'WoCode',
        width: 100
    },
    {
        text: '班组',
        value: 'TeamName',
        width: 100
    },
    {
        text: '班次',
        value: 'ShiftName',
        width: 100
    },

    {
        text: '线体',
        value: 'FullLineName',
        width: 160,
    },
    {
        text: '工段',
        value: 'CompanyName',
        width: 170
    },

    {
        text: '产品料号',
        value: 'MaterialCode',
        width: 180
    },
    {
        text: '产品描述',
        value: 'MaterialDescription',
        width: 250,
    },
    {
        text: '计划量(PCS)',
        width: 180,
        value: 'WoQuantity',
        semicolonFormat: true
    },
    {
        text: '成批量(PCS)',
        value: 'WoSumBatchQuantity',
        semicolonFormat: true,
        width: 120
    },
    {
        text: '确认量(PCS)',
        value: 'WoConfirmBatchQuantity',
        semicolonFormat: true,
        width: 190
    },
    {
        text: '完工量(PCS)',
        value: 'WoCompleteQuantity',
        semicolonFormat: true,
        width: 180
    },
    {
        text: '计划开始',
        value: 'PlanStartTime',
        width: 160
    },
    {
        text: '计划结束',
        value: 'PlanEndTime',
        width: 160
    },
    {
        text: '实际开始',
        value: 'ActualStartTime',
        width: 160
    },
    {
        text: '实际结束',
        value: 'ActualEndTime',
        width: 160
    },
    {
        text: '关单原因',
        value: 'Reason',
        width: 160
    },
    { text: '操作', align: 'center', width: 100, value: 'actions' }
];

export const workReportColum = [
    {
        text: '物料号',
        value: 'MaterialCode',
        width: '110px'
    },
    {
        text: '物料描述',
        value: 'MaterialDescription',
        width: '180px'
    },
    {
        text: '数采计数',
        width: '120px',
        value: 'ScadaTagQty'
    },
    {
        text: '消耗量',
        width: '100px',
        value: 'ScadaConsumptionQty'
    },
    {
        text: '确认消耗量',
        width: '120px',
        value: 'WoConsumptionQty'
    },
    {
        text: '单位',
        value: 'MaterialUom',
        width: '80px'
    }
];
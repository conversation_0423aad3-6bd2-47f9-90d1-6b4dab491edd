import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url

//获取全部数据(不分页)
export function getAllList(data) {
    return request({
        url: baseURL + '/api/Reasontree/GetList',
        method: 'post',
        data
    });
}
//获取全部数据
export function getReasonTreeList(data) {
    return request({
        url: baseURL + '/api/Reasontree/GetPageList',
        method: 'post',
        data
    });
}

export function getAddReasonTree(data) {
    return request({
        url: baseURL + '/api/Reasontree/SaveForm',
        method: 'post',
        data
    });
}

export function getDelReasonTree(data) {
    return request({
        url: baseURL + '/api/Reasontree/Delete',
        method: 'post',
        data
    });
}

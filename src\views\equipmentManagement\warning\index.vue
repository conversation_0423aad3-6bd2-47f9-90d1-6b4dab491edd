<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'BJFFGL_ADD'" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <!-- <v-btn color="primary" @click="handleExport">{{ $t('TPM_SBGL_BJYJCX._DC') }}</v-btn> -->
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    @tableClick="tableClick"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_BJYJCX"
                    :headers="warningColum"
                    :desserts="desserts"
                    @selectePages="selectePages"
                ></Tables>
            </v-card>
        </div>
        <createRepast ref="createRepast" @loadData="RepastInfoGetPage" :dialogType="dialogType" :tableItem="tableItem"></createRepast>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';

import { GetPartsAlarmPageList, GetPartsAlarmDelete } from '@/api/equipmentManagement/Parts.js';
import { warningColum } from '@/columns/equipmentManagement/warning.js';
import { configUrl } from '@/config';
import { GetExportData } from '@/api/equipmentManagement/Equip.js';

export default {
    components: {
        createRepast: () => import('./components/createRepast.vue')
    },
    name: 'RepastModel',
    data() {
        return {
            loading: true,
            showFrom: false,
            papamstree: {
                Code: '',
                Name: '',
                Model: '',
                stardate: '',
                enddate: '',
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            warningColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            hasChildren: {}, // 新增字典详情判断-子节点才能新增
            typecodelist: [],
            PartUnit: [],
            //
            mcCyclelist: [] // 维修周期
        };
    },
    computed: {
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'Code',
                    label: this.$t('TPM_SBGL_BJYJCX._BJBM'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Name',
                    label: this.$t('TPM_SBGL_BJYJCX._BJMC'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Model',
                    label: this.$t('TPM_SBGL_BJYJCX._GGXH'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'stardate',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_BJYJCX._YJKSSJ'),
                    placeholder: this.$t('TPM_SBGL_BJYJCX._YJKSSJ')
                },
                {
                    value: '',
                    key: 'enddate',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_BJYJCX._YJJSSJ'),
                    placeholder: this.$t('TPM_SBGL_BJYJCX._YJJSSJ')
                }
            ];
        },
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'BJFFGL_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'BJFFGL_DELET'
                }
            ];
        }
    },
    async mounted() {
        this.RepastInfoGetPage();
        this.typecodelist = await this.$getNewDataDictionary('PartType');
        this.PartUnit = await this.$getNewDataDictionary('PartUnit');
    },
    methods: {
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.RepastInfoGetPage();
        },

        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.$refs.createRepast.getPartList();
                    this.$refs.createRepast.SbxxList[1].options = this.typecodelist;
                    this.$refs.createRepast.SbxxList[4].options = this.PartUnit;

                    this.$refs.createRepast.SbxxList.forEach(item => {
                        item.value = '';
                    });
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    for (let k in this.tableItem) {
                        this.$refs.createRepast.SbxxList.forEach(item => {
                            if (k == item.id) {
                                item.value = this.tableItem[k];
                            }
                        });
                    }
                    this.$refs.createRepast.getPartList();
                    this.$refs.createRepast.SbxxList[1].options = this.typecodelist;
                    this.$refs.createRepast.SbxxList[4].options = this.PartUnit;

                    this.$refs.createRepast.SbxxList[0].value = this.tableItem.PartsId + '|' + this.tableItem.Name + '|' + this.tableItem.Code + '|' + this.tableItem.Model;
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable(type);
                    return;
            }
        },
        // 删除
        deltable(type) {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    if (type == 'delete') {
                        let res = await GetPartsAlarmDelete(params);
                        if (res.success) {
                            this.tableItem = {};
                            this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                            this.RepastInfoGetPage();
                        }
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        getdata() {
            this.RepastInfoGetPage();
        },
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                ...this.papamstree
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading = true;
            const res = await GetPartsAlarmPageList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        async handleExport() {
            let params = {
                ...this.papamstree
            };
            const baseUrl3 = configUrl[process.env.VUE_APP_SERVE]['baseURL_EQUIPMENT'] + `/api/PartsAlarm/ExportData`;
            let res = await GetExportData(baseUrl3, params);
            let binaryData = [];
            binaryData.push(res);
            const url = window.URL.createObjectURL(new Blob(binaryData));
            console.log(url);
            const link = document.createElement('a');
            link.href = url;
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            let fileName = `设备预警查询${formattedDateTime}.xlsx`;
            document.body.appendChild(link);
            link.setAttribute('download', fileName);
            link.click();
            window.URL.revokeObjectURL(link.href);
        },
        // 按钮操作
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}

.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

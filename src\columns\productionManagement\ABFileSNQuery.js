export const ABFileSNQueryColum = [
    { text: '序号', value: 'Index', width: '80px' },
    { text: '线体', value: 'ProductLine', width: '150px' },
    { text: '型号', value: 'ProductModel', width: '150px' },
    { text: '工单', value: 'WoCode', width: '120px' },
    { text: '批次', value: 'BatchNo', width: '120px' },
    { text: 'SN', value: 'Sn', width: '140px' },
    { text: '生产日期', value: 'SnProductionDate', width: '120px' },
    { text: '生产班次', value: 'SnProductionShiftName', width: '100px' },
    { text: '测试档次', value: 'TestLevel', width: '120px' },
    { text: '测试结果', value: 'TestResult', width: '160px' },
    { text: '测试机台', value: 'ProcessName', width: '160px' },
    { text: '测试时间', value: 'TestTime', width: '180px' },
    { text: '测试文件名称', value: 'TestFileName', width: '180px' },
    // { text: '备注信息', value: 'Remark', width: '180px' },
    // { text: '创建时间', value: 'CreateDate', width: '160px' },
    // { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '',
        width: '0',
        align: 'center',
        value: 'noActions',
    }
];
export function getsimplePie(data) {
    let option = {
        color: ["#78BE20", "#3366cc", "#cc6633", "#33ffff", "#BEBBF5"],
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: 10,
            show: true
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        series: [
            {
                name: '',
                type: 'pie',
                radius: '50%',
                data: data,
                label: {
                    normal: {
                        color: "black",
                        show: true,
                        formatter: '{b}: {d}%'
                    }
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    };
    return option
}

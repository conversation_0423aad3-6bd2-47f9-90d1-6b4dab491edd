import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_TRACE'
//A,B档SN查询

//统计数据
export function SnTestRecordGetSelectCount(data) {
    const api =  '/trace/SnTestRecord/GetSelectCount'
    return getRequestResources(baseURL, api, 'post', data);
}
//获取A,B档SN查询列表
export function getSnTestRecordList(data) {
    const api =  '/trace/SnTestRecord/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑A,B档SN查询
export function SnTestRecordSaveForm(data) {
    const api =  '/trace/SnTestRecord/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除A,B档SN查询
export function DeleteSnTestRecord(data) {
    const api =  '/trace/SnTestRecord/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
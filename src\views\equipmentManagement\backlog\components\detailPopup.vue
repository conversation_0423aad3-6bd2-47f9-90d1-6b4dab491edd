<template>
    <v-card>
        <v-card-title class="headline primary lighten-2" primary-title>{{ $t('GLOBAL._XQ') }} </v-card-title>
        <v-card-text>
            <Tables :showSelect="false" class="mt-3" tableHeight="300px" :page-options="pageOptions" :loading="loading"
                :headers="headers" :desserts="tableList" @selectePages="selectePages" />
        </v-card-text>
        <v-divider></v-divider>

        <v-card-actions>
            <v-spacer></v-spacer>
            <!-- <v-checkbox class="mr-auto" v-model="isChecked" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn> -->
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { keepListdetilsColum, keepListdetilsBColum } from '@/columns/equipmentManagement/upkeep.js';
import { getPointCheckDetail, getMaintainDetail } from '../service'
export default {
    props: {
        id: {
            type: String,
            default: ''
        },
        isPointCheck: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            loading: false,
            tableList: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            }
        }
    },
    computed: {
        headers() {
            let list = this.isPointCheck ? keepListdetilsColum : keepListdetilsBColum
            list[list.length - 1] = { text: '', align: 'center', width: 0, value: 'noActions' }
            return list
        }
    },
    created() {
        this.getdata()
    },
    methods: {
        // 分页操作
        selectePages(data) {
            this.pageOptions.page = data.pageCount;
            this.pageOptions.pageSize = data.pageSize;
            this.getdata();
        },
        async getdata() {
            this.loading = true
            try {
                let resp;
                let params = {
                    mid: this.id,
                    pageIndex: this.pageOptions.page,
                    pageSize: this.pageOptions.pageSize
                }
                if (this.isPointCheck) resp = await getPointCheckDetail({ ...params })
                if (!this.isPointCheck) resp = await getMaintainDetail({ ...params })
                this.loading = false;
                this.tableList = resp.response.data;
                this.pageOptions.pageCount = resp.response.pageCount;
                this.pageOptions.total = resp.response.dataCount;
            } catch {
                this.loading = false
            }
        },
        closePopup() {
            this.$emit('closePopup')
        }
    }
}
</script>

<style></style>
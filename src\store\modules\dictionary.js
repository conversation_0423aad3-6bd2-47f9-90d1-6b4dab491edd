import { dataItemDetailGetAll } from '@/api/common';
import { configUrl } from '@/config';

const state = {
    dictionaryData: null, // 缓存所有数据字典
    lastUpdateTime: null   // 最后更新时间
};

const getters = {
    getDictionaryData: state => state.dictionaryData,
    getLastUpdateTime: state => state.lastUpdateTime
};

const actions = {
    // 获取所有数据字典并缓存
    fetchAllDictionary({ state, commit }) {
        const CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000; // 1天缓存
        if (state.dictionaryData && state.lastUpdateTime && 
            (Date.now() - state.lastUpdateTime) < CACHE_EXPIRE_TIME) {
            // console.log('Fetching dictionary from cache.');
            return Promise.resolve(state.dictionaryData);
        }
        // console.log('Fetching dictionary from API.');
        return dataItemDetailGetAll().then(resp => {
            // console.log('Dictionary data fetched from API:', resp.response);
            commit('SET_DICTIONARY_DATA', resp.response);
            return resp.response;
        }).catch(error => {
            // console.error('Failed to fetch dictionary from API:', error);
            throw error; // 重新抛出错误以便上层捕获
        });
    },

    // 刷新缓存
    refreshDictionary({ commit, dispatch }) {
        return dispatch('fetchAllDictionary');
    },
    
    // 统一获取数据字典方法（增强版）
    async getDictionary({ dispatch, getters, state }, dictType) {
        // 1. 检查缓存是否有效
        const CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟缓存
        const cacheValid = state.dictionaryData && 
                         state.lastUpdateTime && 
                         (Date.now() - state.lastUpdateTime) < CACHE_EXPIRE_TIME;
        
        // 2. 如果缓存无效或没有请求的字典类型，则刷新缓存
        if (!cacheValid || !state.dictionaryData?.[dictType]) {
            await dispatch('fetchAllDictionary');
        }
        
        // 3. 返回请求的字典数据
        const allData = getters.getDictionaryData;
        if (!allData) {
            throw new Error('Dictionary data not loaded');
        }
        return allData[dictType] || [];
    },
    
    // 保存字典数据
    saveDictionary({ commit, state }, data) {
        return new Promise((resolve) => {
            // 更新本地缓存
            const newData = {...state.dictionaryData};
            if (!newData[data.ItemCode]) {
                newData[data.ItemCode] = [];
            }
            // 更新或添加条目
            const index = newData[data.ItemCode].findIndex(item => item.ID === data.ID);
            if (index >= 0) {
                newData[data.ItemCode][index] = data;
            } else {
                newData[data.ItemCode].push(data);
            }
            commit('SET_DICTIONARY_DATA', newData);
            commit('SET_LAST_UPDATE_TIME', Date.now());
            resolve();
        });
    }
};

const mutations = {
    SET_DICTIONARY_DATA(state, data) {
        state.dictionaryData = data;
        state.lastUpdateTime = new Date().getTime();
    },

    CLEAR_DICTIONARY(state) {
        state.dictionaryData = null;
        state.lastUpdateTime = null;
    }
};

export default {
    namespaced: true,
    state,
    getters,
    actions,
    mutations
};
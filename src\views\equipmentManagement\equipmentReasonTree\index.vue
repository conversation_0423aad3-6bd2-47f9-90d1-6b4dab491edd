<template>
    <div class="dictionary-view">
        <TreeView :items="treeData" :title="$t('TPM_SBGL_GZZSK._GZYYS')" @clickClassTree="clickClassTree"></TreeView>
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'GZZSK_MBXZ'" @click="templateDownload('equipmentReasonTree')">{{
                        $t('GLOBAL._MBXZ') }}</v-btn>
                    <v-btn color="primary" v-has="'GZZSK_DR'" @click="handleImport('equipmentReasonTree')">{{
                        $t('GLOBAL._DR') }}</v-btn>
                    <v-btn color="primary" v-has="'GZZSK_ADD'" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="primary" v-has="'GZZSK_ALLREMOVE'" :disabled="!deleteList.length"
                        @click="btnClickEvet('delete')">{{
                            $t('GLOBAL._PLSC') }}</v-btn>
                </div>
                <Tables :page-options="pageOptions" :loading="loading" :btn-list="btnList" tableHeight="calc(100vh - 180px)"
                    table-name="TPM_SBGL_GZZSK" ref="Tables" :clickFun="clickFun" :headers="ReasonColum"
                    :desserts="desserts" @selectePages="selectePages" @tableClick="tableClick" @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"></Tables>
                <createRepast ref="createRepast" :dialogType="dialogType" :ressionList="ressionList" :tableItem="tableItem">
                </createRepast>
            </v-card>
        </div>

        <div class="loading-box" v-if="importLoading">
            <a-spin tip="导入中..." :spinning="importLoading"></a-spin>
        </div>
    </div>
</template>
<script>
import { GetReasontree } from '@/api/factoryPlant/reasonDetail.js';
import { DeviceRepairProjectGetPageList, DeviceRepairProjectDelete } from '@/api/equipmentManagement/equipmentReasonTree.js';
import { DeviceRepairGetPageList } from '@/api/equipmentManagement/Repair.js';
import { ReasonColum } from '@/columns/equipmentManagement/equipmentReasonTree.js';
import { RepairPlanColum } from '@/columns/equipmentManagement/Repair.js';
import equipment from '@/mixins/equipment'
export default {
    name: 'RepastModel',
    components: {
        createRepast: () => import('./components/createRepast.vue')
    },
    mixins: [equipment],
    data() {
        return {
            // tree 字典数据
            loading: false,
            loading1: false,
            treeData: [],
            showFrom: false,
            papamstree: {
                RepairProject: '',
                key: null,
                reasons: '',
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            ReasonColum,
            RepairPlanColum,
            desserts: [],
            desserts1: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            pageOptions1: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            ressionList: [],
            rowtableItem: {},
            hasChildren: {} // 新增字典详情判断-子节点才能新增
        };
    },
    computed: {
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'ItemName',
                    icon: 'mdi-account-check',
                    label: '',
                    placeholder: '请输入机器名称/编码'
                },
                {
                    value: '',
                    key: 'RepairProject',
                    icon: 'mdi-account-check',
                    label: '',
                    placeholder: '案例名称'
                }
            ];
        },
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'GZZSK_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'GZZSK_DELETE'
                }
            ];
        },
        btnList1() {
            return [];
        }
    },
    mounted() {
        this.RepastInfoGetPage();
        this.getReasonTreeList();
    },
    methods: {
        // 获取原因树型
        async getReasonTreeList() {
            const res = await GetReasontree();
            const { success, response } = res;
            if (success) {
                const datalist = response;
                this.treeData = datalist.filter(item => item.name == '停机原因');
                this.ressionList = this.treeData[0].children;
            }
        },
        // 查询数据
        searchForm(value) {
            this.papamstree.key = value.ItemName;
            this.papamstree.RepairProject = value.RepairProject;
            this.RepastInfoGetPage();
        },
        getdata() {
            this.RepastInfoGetPage()
        },
        clickClassTree(v) {
            this.papamstree.reasons = v.id;
            this.RepastInfoGetPage();
        },
        //  查看BOM详情
        clickFun(data) {
            console.log(this.tab);
            this.tableItem = data;
            this.$refs.Tables.selected = [data];
            this.rowtableItem = data || {};
            this.GetDeviceRepairGetPageList();
        },
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                key: this.papamstree.key,
                RepairProject: this.papamstree.RepairProject,
                personcode: this.papamstree.reasons,
                pageIndex: this.papamstree.pageIndex,
                pageSize: this.papamstree.pageSize
            };
            this.loading = true;
            const res = await DeviceRepairProjectGetPageList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
                this.rowtableItem = this.desserts[0] || {};
            }
        },
        // 记录列表查询
        async GetDeviceRepairGetPageList() {
            let params = {
                woid: this.rowtableItem.ID,
                pageIndex: this.papamstree.pageIndex,
                pageSize: this.papamstree.pageSize
            };
            this.loading1 = true;
            const res = await DeviceRepairGetPageList(params);
            let { success, response } = res;
            if (success) {
                this.loading1 = false;
                this.desserts1 = (response || {}).data || [];
                this.pageOptions1.total = response.dataCount;
                this.pageOptions1.page = response.page;
                this.pageOptions1.pageCount = response.pageCount;
                this.pageOptions1.pageSize = response.pageSize;
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await DeviceRepairProjectDelete(params);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.RepastInfoGetPage();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        },
        selectePages2(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.GetDeviceRepairGetPageList();
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}

.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

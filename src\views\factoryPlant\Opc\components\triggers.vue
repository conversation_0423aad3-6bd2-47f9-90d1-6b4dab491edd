<template>
    <div class="triggers">
        <v-row class="tool-row">
            <v-col :cols="12" :lg="3">
                <a-input-search v-model="keywords" enter-button placeholder="Quick Search" @search="onSearch" />
            </v-col>
            <v-col :cols="12" :lg="9" class="pl-0">
                <v-btn @click="getdata">
                    <v-icon left>mdi-cached</v-icon>
                    Refresh</v-btn>
                <v-btn style="margin-left:10px" @click="addTrigger()" color="primary">
                    <v-icon left>mdi-plus</v-icon>
                    New</v-btn>
            </v-col>
        </v-row>
        <div class="table" style="margin-top:10px">
            <vxe-table height="500px" class="mytable-scrollbar" :loading="loading" size="mini" border resizable
                ref="table" :data="tableList">
                <vxe-column v-for="(column, index) in triggersColumns" :key="index" :field="column.field"
                    :title="column.title" :min-width="column.minWidth" :width="column.width" :fixed="column.fixed">
                    <template #default="{ row }">
                        <span style="color:#3dcd58;cursor: pointer;" @click="openEditTrigger(row)"
                            v-if="column.field == 'TriggerType'">
                            {{ row[column.field] }}</span>
                        <span v-else-if="column.field == 'Action'">
                            <v-icon @click="handleDel(row)" style="cursor: pointer;" color="#3dcd58"
                                size="18">mdi-delete</v-icon>
                        </span>
                        <!-- <span v-else-if="column.field == 'OpcActionClassId'">{{
                            getActionClassText(row[column.field]) }}</span> -->
                        <span v-else-if="column.field == 'OpcFunctionPropertyId'">
                            {{ getPropertyText(row[column.field]) }}</span>
                        <span v-else>{{ row[column.field] }}</span>
                    </template>
                </vxe-column>
            </vxe-table>
        </div>
        <a-modal :visible="isShowAddPopup" :title="title" @ok="handleOk" @cancel="isShowAddPopup = false">
            <addTriggers :propertyList="propertyList" :actionClassList="actionClassList" :editItemObj="editItemObj"
                v-if="isShowAddPopup" ref="addTrigger"></addTriggers>
        </a-modal>
    </div>
</template>

<script>
import { triggersColumns } from '@/columns/factoryPlant/Opc.js'
import { getFunctionTrigger, getActionClass, getFunctionProperty, addFunctionTrigger, delFunctionTrigger } from '../service'
import addTriggers from './addTriggers.vue'
export default {
    components: {
        addTriggers
    },
    props: {
        currentObj: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            propertyList: [],
            actionClassList: [],
            title: 'Add Trigger',
            isShowAddPopup: false,
            triggersColumns,
            loading: false,
            keywords: '',
            tableList: []
        }
    },
    created() {
        this.getdata()
        // this.getActionClassList()
        this.getProperty()
    },
    methods: {
        getActionClassText(id) {
            return this.actionClassList.find(item => item.ID == id)?.ClassName || ''
        },
        getPropertyText(id) {
            return this.propertyList.find(item => item.ID == id)?.Name || ''
        },
        handleDel(data) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: '确认要删除此项吗？',
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await delFunctionTrigger([data.ID]);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.getdata()
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        async handleOk() {
            try {
                let params = this.$refs.addTrigger.form
                await addFunctionTrigger({ ...params, OpcFunctionId: this.currentObj.ID })
                this.isShowAddPopup = false
                this.$nextTick(() => {
                    this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });
                    this.getdata()
                })
            } catch {
                console.log('error')
            }
        },
        async getProperty() {
            let resp = await getFunctionProperty({ OpcFunctionId: this.currentObj.ID })
            this.propertyList = resp.response
        },
        async getActionClassList() {
            let resp = await getActionClass({})
            this.actionClassList = resp.response
        },
        addTrigger() {
            this.title = 'Add Trigger'
            this.editItemObj = {}
            this.isShowAddPopup = true
        },
        openEditTrigger(data) {
            this.title = 'Edit Trigger'
            this.editItemObj = data
            this.isShowAddPopup = true
        },
        onSearch() {
            this.getdata()
        },
        async getdata() {
            let resp = await getFunctionTrigger({ OpcFunctionId: this.currentObj.ID, key: this.keywords })
            this.tableList = resp.response
        }
    }
}
</script>

<style lang="scss" scoped>
.tool-row {
    background: #f5f5f5;
    padding: 0 3px;
}

.ant-input-search {
    ::v-deep [type=button] {
        background: #3dcd58;
        border-color: #3dcd58;
    }
}

::v-deep .primary {
    background: #3dcd58 !important;
}

.ml-3 {
    margin-left: 6px;
}
</style>
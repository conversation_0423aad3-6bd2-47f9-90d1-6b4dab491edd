import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_MATERIAL'

//分页获取报废列表
export function getScrapList(data) {
    const api = '/materail/UselessOrder/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

//根据报废单 查询相应报废物料明细
export function getScrapMaterial(data) {
    const api = '/materail/UselessMaterial/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//根据扫描条码查询相应物料信息
export function getMaterialByBarCode(data) {
    const api = '/materail/IssueMaterial/ScanningCodeGetList'
    return getRequestResources(baseURL, api, 'post', data);
}

//新增报废单表单提交
export function saveScrapOrderForm(data) {
    const api = '/materail/UselessOrder/UselessSaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}

//更新报废单表单提交
export function updateScrapOrderForm(data) {
    const api = '/materail/UselessOrder/UselessUpdateForm'
    return getRequestResources(baseURL, api, 'post', data);
}

//删除报废单
export function deleteScrapOrder(data) {
    const api = '/materail/UselessOrder/DeleteOrder'
    return getRequestResources(baseURL, api, 'post', data);
}


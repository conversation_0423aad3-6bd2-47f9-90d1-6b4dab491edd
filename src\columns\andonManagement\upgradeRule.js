export const upgradeRuleColum = [
    { text: '一级分类', value: 'MainAlarmType', width: '100px', dictionary: true },
    { text: '二级分类', value: 'SubAlarmType', width: '190px', dictionary: true },
    { text: '事件等级', value: 'EventLevel', width: '100px' },
    { text: '升级时间(min)', value: 'OutTime', width: '140px', semicolonFormat: true },
    { text: '通知方式', value: 'NoticeType', width: '120px' },
    { text: '负责人', value: 'Dutydetail', width: '260px' },
    // { text: '通知角色', value: 'NoterDepart', width: '260px' },
    { text: '通知人', value: 'NoterName', width: '260px' },
    { text: '处置方式', value: 'DealMode', width: '120px', dictionary: true },
    // { text: '是否向下群发', value: 'IsDownAllSend', width: '140px', dictionary: true },
    // { text: '响应等级', value: 'ResponseLevel', width: '120px', dictionary: true },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        width: '120',
        align: 'center',
        value: 'actions',
    }
];

export const dutyColum = [
    { text: '岗位名称', value: 'PostName', width: '200px' },
    { text: '岗位编码', value: 'PostCode', width: '160px' },
    { text: '是否需要当班', value: 'IsNeedOn', width: '140px', dictionary: true },
    { text: '修改时间', value: 'ModifyDate', width: '180px' },
    {
        text: '',
        width: '0',
        align: 'center',
        value: 'noActions'
    }
];
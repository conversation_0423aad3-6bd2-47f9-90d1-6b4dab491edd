export const tagColumns = [
    { field: 'text', title: '', width: 50 },
    { field: 'Name', title: 'Name' },
    { field: 'PlantNode', title: 'Plant Node' },
    { field: 'Category', title: 'Category' },
    { field: 'UOM', title: 'UOM' },
    { field: 'Source', title: 'Source' },
    { field: 'Status', title: 'Status' },
    { field: 'Purge', title: 'Purge' },
    { field: 'Action', title: '' },
]

export const limitsColumns = [
    { field: 'Target', title: 'Target' },
    { field: 'Min', title: 'Min' },
    { field: 'Max', title: 'Max' },
    { field: 'Material', title: 'Material' },
    { field: 'EffectiveDate', title: 'Effective Date' },
]

export const valueColumns = [
    { field: 'Date', title: 'Date' },
    { field: 'Value', title: 'Value' },
    { field: 'InsertAt', title: 'Insert At' },
]

export const propertyColumns = [
    { field: 'Feature', title: 'Feature' },
    { field: 'Name', title: 'Name' },
    { field: 'Description', title: 'Description' },
    { field: 'Value', title: 'Value' },
    { field: 'Default', title: 'Default' },
    { field: 'Updated', title: 'Updated' },
]

export const tagGroupsColumns = [
    { field: 'text', title: '', width: 50 },
    { field: 'Description', title: 'Description' },
    { field: 'ShortDescription', title: 'Short Description' },
    { field: 'ShortDescription', title: 'Number of Tags' },
    { field: 'ShortDescription', title: 'Updated At' },
    { field: 'ShortDescription', title: 'Updated By' },
    { field: 'Action', title: '' },
]

export const categoryColumns = [
    { field: 'Name', title: 'Name' },
    { field: 'Description', title: 'Description' },
    { field: 'TagCount', title: 'Tag Count' },
    { field: 'Color', title: 'Color' },
    { field: 'Action', title: '', width: 80 },
]
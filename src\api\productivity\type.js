import { getRequestResources } from '@/api/fetch';
const baseURL_30015 = 'baseURL_30015'
const DFM = 'baseURL_DFM'


//Type新增
export function GetTypeSaveForm(data) {
    const api = '/api/ProdtgtSaucetype/SaveForm'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Type类型
export function GetTypeSauceType(data) {
    const api = '/api/ProdtgtSaucetype/GetSauceType'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Type删除
export function GetTypeDelete(data) {
    const api = '/api/ProdtgtSaucetype/Delete'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Type列表
export function GetTypePageList(data) {
    const api = '/api/ProdtgtSaucetype/GetPageList'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Type导入
export function GetTypeImportData(data) {
    const api = '/api/ProdtgtSaucetype/ImportData'
    return getRequestResources(baseURL_30015, api, 'post', data);
}

import request from '@/util/request';
export function ExportData(url, data) {
    return request({
        url: url,
        method: 'post',
        data,
        responseType: 'blob'


    });}
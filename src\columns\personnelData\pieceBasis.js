
export const pieceBasisColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '物料号',
        value: 'MaterielCode',
        width: 150,
        sortable: true
    },
    {
        text: '物料描述',
        value: 'MaterielDescription',
        width: 220,
        sortable: true
    },
    {
        text: '工段',
        value: 'SubsectionOne',
        width: 120,
        sortable: true
    },
    {
        text: '班制',
        value: 'ShiftSystem',
        width: 120,
        sortable: true
    },
    {
        text: '工序',
        value: 'SubsectionTwo',
        width: 140,
        sortable: true
    },
    {
        text: '岗位名称',
        value: 'JobContent',
        width: 160,
        sortable: true
    },
    {
        text: '部门分类',
        value: 'Department',
        width: 130,
        sortable: true
    },
    {
        text: '配置岗位职称',
        value: 'JobTitle',
        width: 120,
        sortable: true
    },
    {
        text: '阶段目标（pcs/班/机）',
        value: 'StageTarget',
        width: 180,
        sortable: true,
        align: 'right',
    },
    {
        text: '人机比',
        value: 'ManMachineRatio',
        width: 100,
        sortable: true,
        align: 'right',
    },
    {
        text: '计件基准产能',
        value: 'BasicOutput',
        width: 120,
        sortable: true,
        align: 'right',
    },
    {
        text: '目标PPM（生产&工艺制定，质量复核）',
        value: 'TargetPpm',
        width: 250,
        sortable: true,
        align: 'right',
    },
    {
        text: '基准工资',
        value: 'BasicWage',
        width: 100,
        sortable: true,
        align: 'right',
    },
    {
        text: '基准单价（基准工资/目标产出）',
        value: 'BasicPrice',
        width: 210,
        sortable: true,
        align: 'right',
    },
    {
        text: '日津贴标准',
        value: 'AllowanceDay',
        width: 120,
        sortable: true,
        align: 'right',
    },
    { text: '操作', width: 180, align: 'center', value: 'actions', sortable: true }
]
export const pieceRateColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '最小百分比',
        value: 'MinPercent',
        width: 120,
        sortable: true,
        align: 'right',
    },
    {
        text: '最大百分比',
        value: 'MaxPercent',
        width: 150,
        sortable: true,
        align: 'right',
    },
    {
        text: '津贴占比%',
        value: 'AllowancePercent',
        width: 100,
        sortable: true,
        align: 'right',
    },
    { text: '操作', width: 150, align: 'center', value: 'actions', sortable: true }
]
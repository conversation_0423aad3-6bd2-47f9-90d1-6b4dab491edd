import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TPM; // 配置服务url
//获取保养&点检项目
export function DeviceMcProjectGetPageList(data) {
    return request({
        url: baseURL + '/tpm/DeviceMcProject/GetPageList',
        method: 'post',
        data
    });
}
// 不分页
export function DeviceMcProjectGetList(data) {
    return request({
        url: baseURL + '/tpm/DeviceMcProject/GetList',
        method: 'post',
        data
    });
}
//新增&保存
export function DeviceMcProjectSaveForm(data) {
    return request({
        url: baseURL + '/tpm/DeviceMcProject/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function DeviceMcProjectDelete(data) {
    return request({
        url: baseURL + '/tpm/DeviceMcProject/Delete',
        method: 'post',
        data
    });
}
//获取保养点检规则
export function DeviceMcRuleGetPagesList(data) {
    return request({
        url: baseURL + '/tpm/DeviceMcRule/GetPageList',
        method: 'post',
        data
    });
}
//新增&保存
export function DeviceMcRuleSavesForm(data) {
    return request({
        url: baseURL + '/tpm/DeviceMcRule/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function DeviceMcRulesDelete(data) {
    return request({
        url: baseURL + '/tpm/DeviceMcRule/Delete',
        method: 'post',
        data
    });
}

//获取保养规则列表
export function MaintainruleGetPagesList(data) {
    return request({
        url: baseURL + '/tpm/Maintainrule/GetPageList',
        method: 'post',
        data
    });
}
//新增&保存
export function MaintainruleSavesForm(data) {
    return request({
        url: baseURL + '/tpm/Maintainrule/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function MaintainrulesDelete(data) {
    return request({
        url: baseURL + '/tpm/Maintainrule/Delete',
        method: 'post',
        data
    });
}

// 点检计划
export function MaintainCheckGetPagesList(data) {
    return request({
        url: baseURL + '/tpm/MaintainCheck/GetPageDate',
        method: 'post',
        data
    });
}
//新增&保存
export function MaintainCheckSavesForm(data) {
    return request({
        url: baseURL + '/tpm/MaintainCheck/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function MaintainChecksDelete(data) {
    return request({
        url: baseURL + '/tpm/MaintainCheck/Delete',
        method: 'post',
        data
    });
}

// 点检计划 - 任务明细
export function MaintainCheckDetailGetPagesList(data) {
    return request({
        url: baseURL + '/tpm/MaintainCheckDetail/GetPageList',
        method: 'post',
        data
    });
}
//新增&保存
export function MaintainCheckDetailSavesForm(data) {
    return request({
        url: baseURL + '/tpm/MaintainCheckDetail/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function MaintainCheckDetailsDelete(data) {
    return request({
        url: baseURL + '/tpm/MaintainCheckDetail/Delete',
        method: 'post',
        data
    });
}


// 保养计划 -N
export function MaintainGetPagesList(data) {
    return request({
        url: baseURL + '/tpm/Maintain/GetPageList',
        method: 'post',
        data
    });
}
// 批量修改保养计划开始结束时间
export function batchEditUpkeepPlan(data) {
    return request({
        url: baseURL + '/tpm/Maintain/updateDate',
        method: 'post',
        data
    })
}
//新增&保存
export function MaintainSavesForm(data) {
    return request({
        url: baseURL + '/tpm/Maintain/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function MaintainsDelete(data) {
    return request({
        url: baseURL + '/tpm/Maintain/Delete',
        method: 'post',
        data
    });
}

// 保养计划明细 - N 
export function MaintainDetailGetPagesList(data) {
    return request({
        url: baseURL + '/tpm/MaintainDetail/GetPageList',
        method: 'post',
        data
    });
}
//新增&保存
export function MaintainDetailSavesForm(data) {
    return request({
        url: baseURL + '/tpm/MaintainDetail/SaveForm',
        method: 'post',
        data
    });
}
export function MaintainDetailUpdate(data) {
    return request({
        url: baseURL + '/tpm/MaintainDetail/updateDate',
        method: 'post',
        data
    })
}
// 删除
export function MaintainDetailsDelete(data) {
    return request({
        url: baseURL + '/tpm/MaintainDetail/Delete',
        method: 'post',
        data
    });
}



// 保养规则 -N
export function MaintainRuleGetPagesList(data) {
    return request({
        url: baseURL + '/tpm/MaintainRule/GetPageList',
        method: 'post',
        data
    });
}
//新增&保存
export function MaintainRuleSavesForm(data) {
    return request({
        url: baseURL + '/tpm/MaintainRule/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function MaintainRulesDelete(data) {
    return request({
        url: baseURL + '/tpm/MaintainRule/Delete',
        method: 'post',
        data
    });
}
// 保养&点检项目导入
export function DeviceMcProjectImport(data) {
    return request({
        url: baseURL + '/tpm/DeviceMcProject/ImportExcel',
        method: 'post',
        data
    })
}
// 保养规则导入
export function maintainRuleImport(data) {
    return request({
        url: baseURL + '/tpm/MaintainRule/ImportExcel',
        method: 'post',
        data
    })
}
// 点检规则导入
export function spockRuleImport(data) {
    return request({
        url: baseURL + '/tpm/DeviceMcRule/ImportExcel',
        method: 'post',
        data
    })
}
// 批量修改点检计划开始结束时间
export function batchEditSpotCheckPlan(data) {
    return request({
        url: baseURL + '/tpm/MaintainCheckDetail/updateStatus',
        method: 'post',
        data
    })
}
// 查询设备列表(不带分页)
export function getDeviceListNoPage(data) {
    return request({
        url: baseURL + '/tpm/Equip/GetDisctinctList',
        method: 'post',
        data
    })
}
// 手动生成保养计划
export function createMaintenanceSchedule(data) {
    return request({
        url: baseURL + '/tpm/Maintain/GenerateMaintenancescheduleByDate',
        method: 'post',
        data
    })
}
// 手动生成点检计划
export function createSpotCheckSchedule(data) {
    return request({
        url: baseURL + '/tpm/MaintainCheck/GenerateMaintenancescheduleByDate',
        method: 'post',
        data
    })
}
// 点检
export function doSpotCheck(data) {
    return request({
        url: baseURL + '/tpm/MaintainCheckDetail/updateStatus',
        method: 'post',
        data
    })
}
// 复检
export function doRecheck(data) {
    return request({
        url: baseURL + '/tpm/MaintainCheckDetail/updateFjStatus',
        method: 'post',
        data
    })
}
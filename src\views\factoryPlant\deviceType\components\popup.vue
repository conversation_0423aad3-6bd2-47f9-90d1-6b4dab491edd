<template>
    <v-card>
        <v-card-title class="headline primary lighten-2" primary-title>{{ $t('DFM_SBLX._BJSBLX') }}</v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid">
                <v-row class="mt-5">
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_SBLX.CATEGORY_NAME')" :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" required dense outlined v-model="form.CategoryName"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <!-- <v-text-field label="上级" required dense outlined v-model="form.ParentId"></v-text-field> -->
                        <Treeselect
                            :noChildrenText="$t('DFM_SBLX._NODATA')"
                            :noOptionsText="$t('DFM_SBLX._NODATA')"
                            :normalizer="normalizer"
                            :options="DeviceTreeData"
                            :placeholder="$t('DFM_SBLX._PLACEHOLDER')"
                            v-model="form.ParentId"
                        />
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_SBLX.REMARK')" dense outlined v-model="form.Remark"></v-text-field>
                    </v-col>
                    <v-col class="pt-0 pb-0" :cols="12" :lg="12">
                        <v-textarea :label="$t('DFM_SBLX.DESCRIBE')" v-model="form.Describe" :value="form.Describe" outlined height="90"></v-textarea>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
        <v-divider></v-divider>

        <v-card-actions>
            <!-- <v-spacer></v-spacer> -->
            <v-checkbox class="mr-auto" v-model="isChecked" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { getDeviceTypeTreeData, saveDeviceType } from '../service';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
export default {
    components: {
        Treeselect
    },
    props: {
        deviceTypeObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            valid: false,
            isChecked: true,
            DeviceTreeData: [],
            form: {
                CategoryName: '',
                ParentId: null,
                Remark: '',
                Describe: ''
            },
            normalizer(node) {
                return {
                    id: node.id,
                    label: node.name,
                    children: node.children
                };
            }
        };
    },
    async created() {
        await this.getDeviceTreeData();
        if (this.deviceTypeObj && this.deviceTypeObj.CATEGORY_ID) {
            this.form.CategoryName = this.deviceTypeObj.CATEGORY_NAME;
            this.form.ParentId = this.deviceTypeObj.PARENT_ID ? this.deviceTypeObj.PARENT_ID : null;
            this.form.Remark = this.deviceTypeObj.REMARK;
            this.form.Describe = this.deviceTypeObj.DESCRIBE;
            this.form.ID = this.deviceTypeObj.CATEGORY_ID;
        }
    },
    methods: {
        async getDeviceTreeData() {
            let resp = await getDeviceTypeTreeData();
            this.DeviceTreeData = resp.response;
        },
        resetForm() {
            this.form = {
                CategoryName: '',
                ParentId: null,
                Remark: '',
                Describe: ''
            };
        },
        async submitForm() {
            if (!this.$refs.form.validate()) return false;

            let resp = await saveDeviceType({ ...this.form });
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
            this.resetForm();
            this.$emit('getdata');
            if (this.isChecked) {
                this.isChecked = !this.isChecked;
                this.$emit('handlePopup', false);
            }
        },
        closePopup() {
            this.$emit('handlePopup', false);
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep(.vue-treeselect) {
    .vue-treeselect__placeholder {
        color: rgba($color: #000000, $alpha: 0.6);
    }
    .vue-treeselect__control {
        min-height: 30px;
        border-color: #999;
    }
    .vue-treeselect__control:hover {
        border-color: #000;
    }
}
</style>
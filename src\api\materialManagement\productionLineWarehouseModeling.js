import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_MATERIAL'
// 产线MES仓库，库位关系建模

//分页获取关系建模列表
export function getLineMesAgvModelingPageList(data) {
    const api =  '/materail/LineMesAgvModeling/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑关系建模
export function LineMesAgvModelingSaveForm(data) {
    const api =  '/materail/LineMesAgvModeling/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除关系建模
export function DeleteLineMesAgvModeling(data) {
    const api =  '/materail/LineMesAgvModeling/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

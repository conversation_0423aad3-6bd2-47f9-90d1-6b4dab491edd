<template>
    <div class="tag-address-popup">
        <a-form :model="serverForm" :label-col="{ span: 6, }" :wrapper-col="{ span: 14, }" v-if="type == 'Server'">
            <a-form-item label="URI">
                <a-input v-model="serverForm.Uri" />
            </a-form-item>
            <a-form-item label="Description">
                <a-input v-model="serverForm.Description" />
            </a-form-item>
            <a-form-item label="Type">
                <a-select show-search v-model="serverForm.ServerType" placeholder="please select your type">
                    <a-select-option value="General">General</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="URL Prefix">
                <a-input v-model="serverForm.UriPrefix" />
            </a-form-item>
        </a-form>
        <a-form :model="groupForm" :label-col="{ span: 6, }" :wrapper-col="{ span: 14, }" v-if="type == 'Group'">
            <a-form-item label="Server">
                <a-select show-search v-model="groupForm.OpcServerId" placeholder="please select your server">
                    <a-select-option v-for="item in serverList" :key="item.ID" :value="item.ID">{{ item.Uri
                    }}</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="Name">
                <a-input v-model="groupForm.Name" />
            </a-form-item>
            <a-form-item label="Description">
                <a-input v-model="groupForm.Description" />
            </a-form-item>
            <a-form-item label="Scan Rate(ms)">
                <a-input v-model="groupForm.ScanRate" />
            </a-form-item>
        </a-form>
        <a-form :model="tagForm" :label-col="{ span: 6, }" :wrapper-col="{ span: 14, }" v-if="type == 'Tag'">
            <a-form-item label="Group">
                <a-select show-search v-model="tagForm.OpcGroupId" placeholder="please select your group">
                    <a-select-option v-for="item in groupList" :key="item.ID" :value="item.ID">{{ item.Name
                    }}</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="Name">
                <a-input v-model="tagForm.Name" />
            </a-form-item>
            <a-form-item label="Description">
                <a-input v-model="tagForm.Description" />
            </a-form-item>
            <a-form-item label="Data Type">
                <a-input v-model="tagForm.DataType" />
            </a-form-item>
            <a-form-item label="Raw PLC Tag">
                <a-input v-model="tagForm.RawPlcTagId" />
            </a-form-item>
            <a-form-item label="Factor">
                <a-input v-model="tagForm.Factor" />
            </a-form-item>
        </a-form>
    </div>
</template>

<script>
import { getServerList, getGroupList, getServerById, getGroupById, getTagById } from '../service';
export default {
    props: {
        currentItem: {
            type: Object,
            default: () => { }
        },
        type: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            serverList: [],
            groupList: [],
            serverForm: {
                Uri: '',
                Description: '',
                ServerType: undefined,
                UriPrefix: ''
            },
            groupForm: {
                OpcServerId: undefined,
                Name: '',
                Description: '',
                ScanRate: ''
            },
            tagForm: {
                OpcGroupId: undefined,
                Name: '',
                Description: '',
                DataType: '',
                RawPlcTagId: '',
                Factor: ''
            }
        }
    },
    async created() {
        if (this.type == 'Group') this.getServer()
        if (this.type == 'Tag') this.getGroup()

        if (this.currentItem && this.currentItem.Id) {
            let resp;
            let id = this.currentItem.Id
            let obj = {}
            switch (this.type) {
                case 'Server':
                    resp = await getServerById({ id })
                    obj = resp.response
                    for (const key in this.serverForm) {
                        this.serverForm[key] = obj[key]
                    }
                    this.serverForm.ID = obj.ID
                    break;
                case 'Group':
                    resp = await getGroupById({ id })
                    obj = resp.response
                    for (const key in this.groupForm) {
                        this.groupForm[key] = obj[key]
                    }
                    this.groupForm.ID = obj.ID
                    break
                case 'Tag':
                    resp = await getTagById({ id })
                    obj = resp.response
                    for (const key in this.tagForm) {
                        this.tagForm[key] = obj[key]
                    }
                    this.tagForm.ID = obj.ID
                    break
            }
        }
    },
    methods: {
        async getServer() {
            let resp = await getServerList({})
            this.serverList = resp.response
        },
        async getGroup() {
            let resp = await getGroupList({})
            this.groupList = resp.response
        }
    }
}
</script>

<style lang="scss" scoped>
.ant-row.ant-form-item {
    margin-bottom: 10px;
}
</style>

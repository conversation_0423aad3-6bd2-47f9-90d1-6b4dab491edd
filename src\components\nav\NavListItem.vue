<template>
    <v-list-item target="_blank" v-if="item.path.indexOf('http://') !== -1 || item.path.indexOf('https//:') !== -1"
        :href="item.path" :title="item.title">
        <v-list-item-icon>
            <v-icon v-text="item.icon" />
        </v-list-item-icon>
        <v-list-item-content>
            <v-list-item-title v-text="item.title" />
        </v-list-item-content>
        <!-- <v-list-item-action v-if="item.isNew">
            <v-icon color="green">mdi-new-box</v-icon>
        </v-list-item-action> -->
    </v-list-item>
    <v-list-item v-else :to="item.path" :title="item.title">
        <v-list-item-icon>
            <v-icon v-text="item.icon" />
        </v-list-item-icon>
        <v-list-item-content>
            <v-list-item-title v-text="item.title" />
        </v-list-item-content>
        <!-- <v-list-item-action v-if="item.isNew">
            <v-icon color="green">mdi-new-box</v-icon>
        </v-list-item-action> -->
    </v-list-item>
</template>

<script>
import NavList from './NavList.vue';
export default {
    components: {
        // NavList
    },
    name: 'NavListItem',
    props: {
        item: Object
    },
    computed: {},
    methods: {
    }
};
</script>

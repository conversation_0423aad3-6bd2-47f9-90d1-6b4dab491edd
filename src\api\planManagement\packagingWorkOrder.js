import { getRequestResources } from '@/api/fetch';
const baseURL3 = 'baseURL_30015'

//获取排序工单列表
export function getSortList(data) {
  const api = '/ppm/Formulaschedule/GetPackageOrderToSortList'
  return getRequestResources(baseURL3, api, 'post', data);
}

//获取排序工单列表
export function getPackOrderByFillNo(data) {
  const api = '/ppm/Formulaschedule/GetPackOrderByFillNo'
  return getRequestResources(baseURL3, api, 'post', data);
}

//获取排序工单列表
export function saveSortList(data) {
  const api = '/ppm/Formulaschedule/SavePackageOrderSortList'
  return getRequestResources(baseURL3, api, 'post', data);
}
//获取包装工单列表
export function getPackOrderList(data) {
  const api = '/ppm/Formulaschedule/GetPackOrderList'
  return getRequestResources(baseURL3, api, 'post', data);
}
//包装自动排序
export function packAutoSorting(data) {
  const api = '/ppm/Formulaschedule/PackOrderSort'
  return getRequestResources(baseURL3, api, 'post', data);
}

//获取工单属性
export function getPackOrderProperty(data) {
  const api = '/ppm/Formulaschedule/GetPackOrderProperty'
  return getRequestResources(baseURL3, api, 'post', data);
}
//修改工单属性
export function AddPackOrderProperty(data) {
  const api = '/ppm/Formulaschedule/AddPackOrderProperty'
  return getRequestResources(baseURL3, api, 'post', data);
}
//修改工单备注
export function EditPackOrderRemark(data) {
  const api = '/ppm/Formulaschedule/EditPackOrderRemark'
  return getRequestResources(baseURL3, api, 'post', data);
}
//获取包装工单BOM
export function getOrderBom(data) {
  const api = '/ppm/OrderBom/GetList'
  return getRequestResources(baseURL3, api, 'post', data);
}


<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm ref="search" :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'SBBYJH_SCBYJH'" @click="handleCreatePlan()">{{ $t('TPM_SBGL_SBBYJH._SCBYJH') }}</v-btn>
                    <!-- <v-btn color="primary" v-has="'SBBYJH_SCBYJH'" :disabled="JSON.stringify(rowtableItem) == '{}'" @click="send()">{{ $t('GLOBAL._PG') }}</v-btn> -->
                    <v-btn color="primary" v-has="'SBBYJH_ALLREMOVE'" :disabled="!deleteList.length" @click="btnClickEvet('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn>
                    <v-btn color="primary" v-has="'SBBYJH_DC'" @click="handleExport">{{ $t('GLOBAL._EXPORT') }}</v-btn>
                </div>
                <!-- :currentSelectId="rowtableItem.ID" -->
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    ref="Tables"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_SBBYJH"
                    :headers="keepListBColum"
                    :clickFun="clickFun"
                    :desserts="desserts"
                    @selectePages="selectePages"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"
                ></Tables>
            </v-card>

            <el-drawer size="80%" :title="drawTitle" :wrapperClosable="false" :visible.sync="detailShow" direction="rtl">
                <v-card class="ma-1">
                    <div class="form-btn-list">
                        <v-btn color="primary" v-has="'SBBYJH_JHMX_ADD'" @click="adddetail()">{{ $t('GLOBAL._XZ') }}</v-btn>
                        <v-btn color="primary" v-has="'SBBYJH_JHMX_ALLREMOVE'" :disabled="!Flag" @click="btnClickEvet('delete2')">{{ $t('GLOBAL._PLSC') }}</v-btn>
                    </div>
                    <Tables
                        :page-options="pageOptions2"
                        :loading="loading2"
                        :clickFun="clickFun2"
                        :btn-list="btnList2"
                        tableHeight="calc(100vh - 220px)"
                        table-name="TPM_SBGL_SBBYJH_XQ"
                        :headers="unkeepPlanColum"
                        :desserts="desserts2"
                        @selectePages="selectePages2"
                        @tableClick="tableClick"
                        @itemSelected="SelectedItems2"
                        @toggleSelectAll="SelectedItems2"
                    ></Tables>
                </v-card>
            </el-drawer>
            <createRepast
                :maintenanceType="itemType"
                :mcCyclelist="Inscycle"
                :InputTypeList="InputTypeList"
                ref="createRepast"
                :rowtableItem="rowtableItem"
                :statusId="statusId"
                @loadData2="loadData2"
                :DeviceCategoryId="rowtableItem.DeviceCategoryId"
                :dialogType="dialogType"
                :tableItem="tableItem"
            ></createRepast>
            <el-dialog :title="$t('GLOBAL._PG')" :visible.sync="addModel" width="30%">
                <div class="addForm">
                    <v-autocomplete
                        clearable
                        v-model="user"
                        :items="gprList"
                        item-text="ItemName"
                        item-value="ItemValue"
                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYJH_XQ.MaintainUser1') + '*'"
                        clear
                        dense
                        outlined
                    ></v-autocomplete>
                    <!-- <v-text-field v-model="user" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYJH_XQ.MaintainUser1')"></v-text-field> -->
                </div>
                <div class="addForm">
                    <v-text-field v-model="startDate" :clearable="true" outlined dense :label="$t('GLOBAL.StartTime')" readonly></v-text-field>
                    <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="startDate" type="datetime" :placeholder="$t('GLOBAL.StartTime')"></el-date-picker>
                </div>
                <div class="addForm">
                    <v-text-field v-model="endDate" :clearable="true" outlined dense :label="$t('GLOBAL.EndTime')" readonly></v-text-field>
                    <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="endDate" type="datetime" :placeholder="$t('GLOBAL.EndTime')"></el-date-picker>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="addModel = false">取 消</el-button>
                    <el-button type="primary" @click="Save()">确 定</el-button>
                </span>
            </el-dialog>
            <v-dialog v-model="isShowcreatTable" persistent scrollable width="70%">
                <creatTable ref="creatTable" @closePopup="isShowcreatTable = false" @loadData="loadData" v-if="isShowcreatTable" />
            </v-dialog>
        </div>
        <div class="loading-box" v-if="importLoading">
            <a-spin tip="导入中..." :spinning="importLoading"></a-spin>
        </div>
        <createRepast2
            :rowtableItem="rowtableItem"
            :itemType="itemType"
            :Insfrequency="Insfrequency"
            :statusId="statusId"
            :Inscycle="Inscycle"
            :InputTypeList="InputTypeList"
            @loadData2="loadData2"
            ref="createRepast2"
            :DeviceCategoryId="rowtableItem.DeviceCategoryId"
            :dialogType="dialogType"
            :tableItem="tableItem"
        ></createRepast2>
        <editRepast ref="editRepast" @loadData="loadData" :DeviceCategoryId="rowtableItem.ID" :dialogType="dialogType" :tableItem="tableItem"></editRepast>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';

import {
    GetMaintainWoPageList,
    GetMaintainWoDelete,
    GetMaintainWoItemPageList,
    GetMaintainWoItemDelete,
    GetCreateMaintainWoByItems,
    GetMaintainWoStatus,
    GetMaintainWoAssign,
    GetMaintainWoExport
} from '@/api/equipmentManagement/upkeeplistB.js';
import { GetExportData, GetPersonList } from '@/api/equipmentManagement/Equip.js';
import { SparepartGetList } from '@/api/equipmentManagement/sparePart.js';
import { EquipmentGetEquipmentTree } from '@/api/common.js';
import { keepListBColum, unkeepPlanColum } from '@/columns/equipmentManagement/upkeep.js';

import moment from 'moment';
import { configUrl } from '@/config';

export default {
    name: 'RepastModel',
    components: {
        editRepast: () => import('./components/edit.vue'),
        createRepast: () => import('./components/createRepast.vue'),
        creatTable: () => import('./components/creatTable.vue'),
        createRepast2: () => import('./components/createRepast2.vue')
    },
    data() {
        return {
            detailShow: false,
            drawTitle: '',
            importLoading: false,
            isBatchUpkeep: false,
            isShowBatchEdit: false,
            loading: true,
            loading2: false,
            Flag: false,
            showFrom: false,
            papamstree: {
                DeviceCode: '',
                AssetName: '',
                DeviceName: '',
                Status: '',
                CheckBy: '',
                PlanMaintainDateFrom: '',
                PlanMaintainDateTo: '',
                pageIndex: 1,
                pageSize: 20,
                orderByFileds: 'CreateDate desc'
            },
            //查询条件
            keepListBColum,
            unkeepPlanColum,
            desserts: [],
            desserts2: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            pageOptions2: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            gprList: [],
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            tableItem2: {}, // 选择操作数据
            deleteList: [], //批量选中
            deleteList2: [], //批量选中
            rowtableItem: {},
            statusList: [],
            isShowcreatTable: false,
            startDate: '',
            endDate: '',
            statusId: '',
            user: this.$store.getters.getUserinfolist[0].LoginName,
            addModel: false,
            itemType: [],
            Insfrequency: [],
            Inscycle: [],
            InputTypeList: [],
            DeviceMngData: [],
            MaintainByData: []
        };
    },
    computed: {
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'DeviceCode',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipCode'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipCode')
                },
                {
                    value: '',
                    key: 'DeviceName',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Name'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Name')
                },
                {
                    value: '',
                    key: 'PlanMaintainDateFrom',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBDXJH.jhksrq'),
                    placeholder: this.$t('TPM_SBGL_SBDXJH.jhksrq')
                },
                {
                    value: '',
                    key: 'PlanMaintainDateTo',
                    type: 'date',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_SBDXJH.jhjsrq'),
                    placeholder: this.$t('TPM_SBGL_SBDXJH.jhjsrq')
                },
                {
                    value: '',
                    key: 'AssetName',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.AssetName'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.AssetName')
                },
                {
                    value: '',
                    key: 'MaintainBy',
                    icon: 'mdi-account-check',
                    selectData: this.gprList,
                    byValue: 'ItemValue',
                    type: 'select',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.PersonName'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.PersonName')
                },

                {
                    value: '',
                    key: 'Status',
                    selectData: this.statusList,
                    type: 'select',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Status'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Status')
                }
            ];
        },
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._PG'),
                    code: 'send',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBBYJH_PG'
                },
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBBYJH_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    showList: ['未保养', '保养中'],
                    showKey: 'Status',
                    type: 'red',
                    icon: '',
                    authCode: 'SBBYJH_DELETE'
                }
            ];
        },
        btnList2() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit2',
                    showList: ['未保养'],
                    showKey: 'Status',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBBYJH_JHMX_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete2',
                    showList: ['未保养', '保养中'],
                    showKey: 'Status',
                    type: 'red',
                    icon: '',
                    authCode: 'SBBYJH_JHMX_DELET'
                }
            ];
        }
    },
    async mounted() {
        let DeviceMng = await GetPersonList('DeviceMng');
        this.DeviceMngData = DeviceMng.response[0].ChildNodes;
        this.DeviceMngData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        let MaintainBy = await GetPersonList('MaintenanceGroup');
        this.MaintainByData = MaintainBy.response[0].ChildNodes;
        this.MaintainByData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.gprList = this.MaintainByData;
        this.itemType = await this.$getNewDataDictionary('MaintainItemType');
        this.Insfrequency = await this.$getNewDataDictionary('MaintainFrequency');
        this.Inscycle = await this.$getNewDataDictionary('MaintainCycle');
        this.InputTypeList = await this.$getNewDataDictionary('MaintainInputType');
        this.statusList = await this.$getNewDataDictionary('MaintainPlanStatus');
        this.RepastInfoGetPage();
    },
    methods: {
        loadData2() {
            this.RepastInfoLogGetPage();
        },
        adddetail() {
            console.log(this.rowtableItem.Status);
            this.statusList.some(item => {
                if (item.ItemName == this.rowtableItem.Status) {
                    this.statusId = item.ID;
                }
            });
            console.log(this.statusId);
            this.dialogType = 'add';
            this.$refs.createRepast.showDialog = true;
        },
        loadData() {
            this.RepastInfoGetPage();
        },
        send() {
            this.user = this.$store.getters.getUserinfolist[0].LoginName;
            this.startDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.addModel = true;
        },
        async handleImport() {
            let _this = this;
            let input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xls,.xlsx';
            input.click();
            let Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            input.onchange = async function () {
                let file = input.files[0];
                let formdata = new FormData();
                formdata.append('file', file);
                _this.importLoading = true;
                try {
                    let res = await GetMaintainWoExport(formdata, Factory);
                    _this.$store.commit('SHOW_SNACKBAR', { text: res.response });
                    _this.RepastInfoGetPage();
                    _this.importLoading = false;
                } catch {
                    _this.importLoading = false;
                }
            };
        },
        async handleExport() {
            let params = {
                ...this.papamstree
            };
            const baseUrl3 = configUrl[process.env.VUE_APP_SERVE]['baseURL_EQUIPMENT'] + `/api/MaintainWo/ExportData`;
            let res = await GetExportData(baseUrl3, params);
            let binaryData = [];
            binaryData.push(res);
            const url = window.URL.createObjectURL(new Blob(binaryData));
            console.log(url);
            const link = document.createElement('a');
            link.href = url;
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            let fileName = `设备保养计划${formattedDateTime}.xlsx`;
            document.body.appendChild(link);
            link.setAttribute('download', fileName);
            link.click();
            window.URL.revokeObjectURL(link.href);
        },
        async Save() {
            this.tableItem.StartMaintainDate = this.startDate;
            this.tableItem.FinishMaintainDate = this.endDate;
            this.tableItem.MaintainBy = this.user;
            let res = await GetMaintainWoAssign(this.tableItem);
            this.$store.commit('SHOW_SNACKBAR', { text: res.msg || '创建成功', color: 'success' });
            this.RepastInfoGetPage();
            this.addModel = false;
        },
        handleCreatePlan() {
            this.isShowcreatTable = true;
            setTimeout(() => {
                this.$refs.creatTable.PlanStartDate = '';
            }, 500);
            // let resp = await GetCreateMaintainWoByItems({});
            // this.$store.commit('SHOW_SNACKBAR', { text: resp.msg, color: 'success' });
            // this.RepastInfoGetPage();
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.RepastInfoGetPage();
        },
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                ...this.papamstree
            };
            this.loading = true;
            const res = await GetMaintainWoPageList(params);
            let { success, response } = res;
            response.data.forEach(item => {
                this.statusList.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                        item.StatusValue = it.ItemValue;
                    }
                });
                this.MaintainByData.forEach(it => {
                    if (item.MaintainBy == it.ItemValue) {
                        item.MaintainBy = it.ItemName;
                        item.MaintainByValue = it.ItemValue;
                    }
                });
                this.DeviceMngData.forEach(it => {
                    if (item.Manager == it.ItemValue) {
                        item.Manager = it.ItemName;
                        item.ManagerValue = it.ItemValue;
                    }
                });
            });
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        clickFun(data) {
            // this.$refs.Tables.selected = [data];
            this.tableItem = data || {};
            this.rowtableItem = data || {};
            this.drawTitle = this.rowtableItem.DeviceName + ' | ' + this.rowtableItem.DeviceCode;
            this.detailShow = true;
            this.RepastInfoLogGetPage();
        },
        clickFun2(data) {
            // this.$refs.Tables.selected = [data];
            this.tableItem2 = data || {};
        },
        // 明细列表查询
        async RepastInfoLogGetPage() {
            if (!this.rowtableItem.ID) {
                return false;
            }
            let params = {
                WoId: this.rowtableItem.ID
            };
            this.loading2 = true;
            const res = await GetMaintainWoItemPageList(params);
            let { success, response } = res;
            response.forEach(item => {
                this.statusList.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                        item.StatusValue = it.ItemValue;
                    }
                });
            });
            if (success) {
                this.loading2 = false;
                this.desserts2 = response || {} || [];
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    break;
                case 'upkeep':
                    this.isBatchUpkeep = true;
                    this.$refs.maintenanceDialog.updateDialog = true;
                    console.log(val);
                    break;
                case 'delete':
                    this.deltable(val);
                    break;
                case 'delete2':
                    this.deltable(val);
                    break;

                case 'edit':
                    this.isShowBatchEdit = true;
                    break;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            if (type == 'delete2') {
                this.tableItem2 = item;
            } else {
                this.tableItem = item;
            }
            switch (type) {
                case 'edit':
                    this.$refs.editRepast.SbxxList.forEach(item => {
                        for (let k in this.tableItem) {
                            if (item.id == k) {
                                if (k == 'MaintainBy') {
                                    item.value = this.tableItem.MaintainByValue;
                                } else if (k == 'Manager') {
                                    item.value = this.tableItem.ManagerValue;
                                } else {
                                    item.value = this.tableItem[k];
                                }
                            }
                        }
                    });
                    this.$refs.editRepast.SbxxList[9].options = this.DeviceMngData;
                    this.$refs.editRepast.SbxxList[10].options = this.MaintainByData;
                    this.$refs.editRepast.showDialog = true;
                    return;
                case 'send':
                    this.send();
                    break;
                case 'edit2':
                    this.statusList.some(item => {
                        if (item.ItemName == this.rowtableItem.Status) {
                            this.statusId = item.ID;
                        }
                    });
                    this.$refs.createRepast2.showDialog = true;
                    return;
                case 'newAdd':
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable(type);
                    return;
                case 'delete2':
                    this.deltable(type);
                    return;
                case 'upkeep':
                    this.isBatchUpkeep = false;
                    this.$refs.maintenanceDialog.updateDialog = true;
                    return;
            }
        },
        // 删除
        deltable(type) {
            console.log(type);
            let params = [];
            let item = type == 'delete' ? this.tableItem : this.tableItem2;
            // eslint-disable-next-line no-prototype-builtins
            if (item.hasOwnProperty('ID')) {
                let id = type == 'delete' ? this.tableItem.ID : this.tableItem2.ID;
                params = [id];
            } else {
                let list = type == 'delete' ? this.deleteList : this.deleteList2;
                list.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    if (type == 'delete') {
                        let res = await GetMaintainWoDelete(params);
                        if (res.success) {
                            this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                            this.RepastInfoGetPage();
                        }
                    } else if (type == 'delete2') {
                        let res = await GetMaintainWoItemDelete(params);
                        if (res.success) {
                            this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                            this.RepastInfoLogGetPage();
                        }
                    }
                    this.tableItem = {};
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            this.deleteList = [...item];
        },
        SelectedItems2(item) {
            this.deleteList2 = [...item];
            if (this.deleteList2.length == 0) {
                this.Flag = false;
            } else {
                this.Flag = this.deleteList2.every(item => {
                    return item.Status == '未保养';
                });
            }
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        },
        selectePages2(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoLogGetPage();
        },
        handlePopup() {
            this.RepastInfoLogGetPage();
        }
    }
};
</script>
<style lang="scss">
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}
.addForm {
    position: relative;
    .el-date-editor {
        width: 100%;
        position: absolute;
        opacity: 0;
        top: 0;
        .el-input__inner {
            font-size: 16px;
            height: 32.5px;
            color: rgba(0, 0, 0, 0.87);
            border: 1px solid #9e9e9e;
        }
        .el-input__icon {
            line-height: 32.5px;
        }
    }
}
</style>

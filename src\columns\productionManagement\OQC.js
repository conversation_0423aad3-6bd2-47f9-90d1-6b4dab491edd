export const oqclistColum = [
    {
        text: '工序',
        value: 'CurrentProcName',
        width: 140,
        sortable: true
    },
    {
        text: '工单',
        value: 'WoCode',
        width: 100,
        sortable: true
    },
    {
        text: '产线',
        value: 'CompanyName',
        width: 140,
        sortable: true
    },
    {
        text: '产品料号',
        value: 'MaterialCode',
        width: 120,
        sortable: true
    },
    {
        text: '产品描述',
        width: 260,
        value: 'MaterialDescription',
        sortable: true
    },
    // {
    //     text: '检验批',
    //     value: 'BatchNo',
    //     sortable: true
    // },
    {
        text: '批次号',
        value: 'BatchNo',
        width: 100,
        sortable: true
    },
    {
        text: '批次数量(PCS)',
        width: 140,
        value: 'BatchQuantity',
        semicolonFormat: true,
        sortable: true
    },
    // {
    //     text: '不良数量(PCS)',
    //     value: 'RemainQuantity',
    //     isEditCell: true,
    //     sortable: true
    // },
    {
        text: '状态',
        width: 80,
        value: 'CurrentProcStatus',
        sortable: true
    },
    {
        text: '检验时间',
        width: 180,
        value: 'CreateDate',
        sortable: true
    }
];
export const oqcColum = [
    // {
    //     text: '序号',
    //     value: 'Index',
    //     sortable: true
    // },
    {
        text: '产线',
        value: 'CompanyName',
        width: 140,
        sortable: true
    },
    {
        text: '产品料号',
        value: 'MaterialCode',
        width: 120,
        sortable: true
    },
    {
        text: '班组',
        value: 'TeamName',
        width: 80,
        sortable: true
    },
    {
        text: '班次',
        value: 'ShiftName',
        width: 80,
        sortable: true
    },
    {
        text: '批次号',
        value: 'BatchNo',
        width: 100,
        sortable: true
    },
    {
        text: '批次数量',
        width: 100,
        value: 'RemainQuantity',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: '不良数量',
        width: 100,
        isEditCell: true,
        value: 'WoQuantity',
        sortable: true
    },
];

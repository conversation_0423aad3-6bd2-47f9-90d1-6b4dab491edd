import request from '@/util/request';
import { getRequestResources } from '@/api/fetch';
import { configUrl } from '@/config';
const baseURL2 = 'baseURL_EQUIPMENT';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TPM; // 配置服务url
//获取设备备件列表
export function SparepartGetPageList(data) {
    return request({
        url: baseURL + '/tpm/SparePartsStock/GetPageList',
        method: 'post',
        data
    });
}
// 不分页
export function SparepartGetList(data) {
    return request({
        url: baseURL + '/tpm/SparePartsStock/GetList',
        method: 'post',
        data
    });
}

//导出
export function PartImport(data, key) {
    const api = `/api/Parts/ImportData?factory=${key}`;
    return getRequestResources(baseURL2, api, 'post', data);
}

//新增&保存
export function SparepartSaveForm(data) {
    return request({
        url: baseURL + '/tpm/SparePartsStock/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function SparepartDelete(data) {
    return request({
        url: baseURL + '/tpm/SparePartsStock/Delete',
        method: 'post',
        data
    });
}
//获取设备备件log列表
export function SparepartuselogGetPageList(data) {
    return request({
        url: baseURL + '/tpm/Sparepartuselog/GetPageList',
        method: 'post',
        data
    });
}
//新增&保存
export function SparepartuselogSaveForm(data) {
    return request({
        url: baseURL + '/tpm/Sparepartuselog/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function SparepartuselogDelete(data) {
    return request({
        url: baseURL + '/tpm/Sparepartuselog/Delete',
        method: 'post',
        data
    });
}


//  扫码录入 备件数量
export function addSparePart(data) {
    return request({
        url: baseURL + '/tpm/Sparepartuselog/JoinSaveForm',
        method: 'post',
        data
    });
}

// 领取备件列表  
export function GetPageSparePartsList(data) {
    return request({
        url: baseURL + '/tpm/SparePartsStock/GetPageSparePartsList',
        method: 'post',
        data
    });
}


// 备件仓库管理
// 获取列表
export function WarehouseManageGetPageList(data) {
    return request({
        url: baseURL + '/tpm/WarehouseManage/GetPageList',
        method: 'post',
        data
    });
}
// 获取仓位列表
export function WarehouseManageGetList(data) {
    return request({
        url: baseURL + '/tpm/WarehouseManage/GetList',
        method: 'post',
        data
    });
}
//新增&保存
export function WarehouseManageSaveForm(data) {
    return request({
        url: baseURL + '/tpm/WarehouseManage/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function WarehouseManageDelete(data) {
    return request({
        url: baseURL + '/tpm/WarehouseManage/Delete',
        method: 'post',
        data
    });
}

// 出入库记录
// 列表
export function GetPageSparePartsInOutList(data) {
    return request({
        url: baseURL + '/tpm/SparePartsStock/GetPageSparePartsInOutList',
        method: 'post',
        data
    });
}

// 导入
export function doImport(data) {
    return request({
        url: baseURL + '/tpm/SparePartsStock/ImportExcel',
        method: 'post',
        data
    });
}

// 入库记录
export function getInStorageData(data) {
    return request({
        url: baseURL + '/tpm/SparePartsStock/GetPageSparePartsInList',
        method: 'post',
        data
    });
}
// 出库记录
export function getOutStorageData(data) {
    return request({
        url: baseURL + '/tpm/SparePartsStock/GetPageSparePartsOutList',
        method: 'post',
        data
    });
}
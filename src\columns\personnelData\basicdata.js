// 人员信息
export const personalColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '员工号',
        value: 'Code',
        width: 100,
        sortable: true
    },
    {
        text: '姓名',
        value: 'Name',
        width: 100,
        sortable: true
    },
    {
        text: '员工头像',
        value: 'UserAvatar',
        width: 130,
        sortable: true
    },
    {
        text: '人事子范围',
        value: 'BusinessDesc',
        width: 120,
        sortable: true
    },
    {
        text: '员工子组',
        value: 'StaffGroup',
        width: 120,
        sortable: true
    },
    {
        text: '厂区',
        value: 'WorkshopDesc',
        width: 120,
        sortable: true
    },
    {
        text: '业务类型描述',
        value: 'BusinessDesc1',
        width: 140,
        sortable: true
    },

    {
        text: '车间描述',
        value: 'WorkshopDesc1',
        width: 100,
        sortable: true
    },
    {
        text: '虚拟组织',
        value: 'VirtualOrganization',
        align: 'center',
        width: 220,
        sortable: true
    },
    {
        text: '上级领导',
        value: 'LeaderName',
        align: 'center',
        width: 100,
        sortable: true
    },
    {
        text: '上级领导工号',
        value: 'LeaderCode',
        width: 120,
        sortable: true,
        align: 'right'
    },
    {
        text: 'UWB标签号',
        value: 'Uwb',
        width: 120,
        sortable: true,
        align: 'right'
    },
    {
        text: '职位',
        value: 'Rank',
        align: 'center',
        width: 140,
        sortable: true
    },
    {
        text: '职称',
        value: 'JobTitle',
        width: 120,
        sortable: true
    },
    {
        text: '星级',
        value: 'StarLevel',
        width: 120,
        sortable: true,
        align: 'right'
    },
    {
        text: '飞书',
        value: 'Feishu',
        width: 140,
        sortable: true,
        align: 'right'
    },
    {
        text: '维修成本单价',
        value: 'Price',
        width: 100,
        sortable: true,
        semicolonFormat: true
    },
    {
        text: '单价有效起始时间',
        value: 'StartValidTime',
        width: 140,
        sortable: true
    },
    {
        text: '单价有效截止时间',
        value: 'EndValidTime',
        width: 140,
        sortable: true
    },

    { text: '操作', width: 180, align: 'center', value: 'actions', sortable: true }
];
// 人员信息
export const personalsColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '员工号',
        value: 'Code',
        width: 100,
        sortable: true
    },
    {
        text: '姓名',
        value: 'Name',
        width: 100,
        sortable: true
    },
    {
        text: '员工头像',
        value: 'UserAvatar',
        width: 130,
        sortable: true
    },
    {
        text: '人事子范围',
        value: 'Region',
        width: 120,
        sortable: true
    },
    {
        text: '员工子组',
        value: 'StaffGroup',
        width: 120,
        sortable: true
    },
    {
        text: '厂区',
        value: 'Factory',
        width: 120,
        sortable: true
    },
    {
        text: '业务类型描述',
        value: 'BusinessDesc',
        width: 140,
        sortable: true
    },

    {
        text: '车间描述',
        value: 'WorkshopDesc',
        width: 100,
        sortable: true
    },
    {
        text: '虚拟组织',
        value: 'VirtualOrganization',
        align: 'center',
        width: 220,
        sortable: true
    },
    {
        text: '上级领导',
        value: 'LeaderName',
        align: 'center',
        width: 100,
        sortable: true
    },
    {
        text: '上级领导工号',
        value: 'LeaderCode',
        width: 120,
        sortable: true,
        align: 'right'
    },
    {
        text: 'UWB标签号',
        value: 'Uwb',
        width: 120,
        sortable: true,
        align: 'right'
    },
    {
        text: '职位',
        value: 'Rank',
        align: 'center',
        width: 140,
        sortable: true
    },
    {
        text: '职称',
        value: 'JobTitle',
        width: 120,
        sortable: true
    },
    {
        text: '星级',
        value: 'StarLevel',
        width: 120,
        sortable: true,
        align: 'right',

    },
    {
        text: '飞书',
        value: 'Feishu',
        width: 140,
        sortable: true,
        align: 'right',
    }
];

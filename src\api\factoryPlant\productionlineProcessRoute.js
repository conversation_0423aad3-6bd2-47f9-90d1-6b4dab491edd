import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM'
// 产线工艺路线

//获取工艺路线列表
export function getRoutingHeadList(data) {
    const api =  '/api/RoutingHead/GetList'
    return getRequestResources(baseURL, api, 'get', data);
}
//获取产线工艺路线列表数据
export function getProductlineResPageList(data) {
    const api =  '/api/ProductlineRes/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑产线工艺路线
export function ProductlineResSaveForm(data) {
    const api =  '/api/ProductlineRes/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除产线工艺路线
export function DeleteProductlineRes(data) {
    const api =  '/api/ProductlineRes/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

<template>
    <v-dialog v-model="showDialog" max-width="980px">
        <v-card class="" v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" style="height: 450px">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Name" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.ProjectName') + '*'"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Code" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.ProjectCode') + '*'"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select
                                v-model="form.Type"
                                outlined
                                dense
                                :items="SpotCheckType"
                                item-value="ItemName"
                                item-text="ItemName"
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Classify') + '*'"
                            ></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select
                                v-model="form.Cycle"
                                :items="SpotCheckCycle"
                                item-text="ItemName"
                                item-value="ItemName"
                                outlined
                                dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Inscycle') + '*'"
                            ></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Frequency" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Insfrequency') + '*'"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select
                                v-model="form.ExecutionTime"
                                :items="SpotCheckExecutionTime"
                                item-text="ItemName"
                                item-value="ItemName"
                                outlined
                                dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.executiontime')"
                            ></v-select>
                            <!-- 
                            <v-text-field v-model="form.ExecutionTime" :clearable="true" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.executiontime')" readonly></v-text-field>
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="form.ExecutionTime"
                                type="datetime"
                                :placeholder="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.executiontime')"
                            ></el-date-picker> -->
                            <!-- <v-text-field v-model="form.ExecutionTime" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.executiontime')"></v-text-field> -->
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.CheckStandard" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.CheckStandard')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Method" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Methods')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.LowerBound" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.UpperLimit')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.UpperBound" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.LowerLimit')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select
                                v-model="form.InputType"
                                :items="SpotCheckInputType"
                                item-text="ItemName"
                                item-value="ItemName"
                                outlined
                                dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.InputType') + '*'"
                            ></v-select>

                            <!-- <v-text-field v-model="form.InputType" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.InputType')"></v-text-field> -->
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.SpotPeriod" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Inspectiontime')"></v-text-field>
                        </v-col>

                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="form.Remark" outlined rows="2" dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Remark')"></v-textarea>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <el-upload
                                ref="upload"
                                class="upload-demo"
                                :auto-upload="false"
                                action=""
                                :on-change="FileChange"
                                :on-remove="FileRemove"
                                multiple
                                :limit="1"
                                :file-list="Fileform.FileList"
                            >
                                <v-btn color="primary">{{ $t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.FileUpload') }}</v-btn>
                                <div slot="tip" class="el-upload__tip">{{ $t('GLOBAL.PDFOnly') }}</div>
                            </el-upload>
                        </v-col>
                        <!-- <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Tools" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Tools')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.InputType" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.InputType')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.DataFrom" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.DataFrom')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="form.Classify" outlined dense :items="maintenanceType" item-value="ItemName"
                                item-text="ItemName" :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Classify')"></v-select>
                        </v-col>
                    
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field type="number" v-model="form.Duration" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Duration')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="form.Isenable" :items="stateList" item-text="name" item-value="value"
                                outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Isenable')"></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete v-model="form.PersonCode" :loading="loading" :items="peopleitems"
                                item-value="Code" item-text="Name" multiple :search-input="form.PersonName" flat outlined
                                return-object dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.PersonName')">
                                <template #item="data">
                                    <template v-if="(typeof data.item) !== 'object'">
                                        <v-list-item-content v-text="data.item"></v-list-item-content>
                                    </template>
                                    <template v-else>
                                        <v-list-item-content>
                                            <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                            <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                        </v-list-item-content>
                                    </template>
                                </template>
                                <template v-slot:selection="{ item, index }">
                                    <span v-if="index === 0">{{ item.Name }}</span>
                                    <span v-if="index === 1">&nbsp;&nbsp;</span>
                                    <span v-if="index === 1" class="grey--text caption">(+{{ form.PersonCode.length -
                                        1 }} )</span>
                                </template>
                            </v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="form.Remark" outlined rows="2" dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Remark')"></v-textarea>
                        </v-col> -->
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'edit'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._BJ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Name" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.ProjectName') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Code" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.ProjectCode') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select
                            v-model="editedItem.Type"
                            outlined
                            dense
                            :items="SpotCheckType"
                            item-value="ItemName"
                            item-text="ItemName"
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Classify') + '*'"
                        ></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select
                            v-model="editedItem.Cycle"
                            :items="SpotCheckCycle"
                            item-text="ItemName"
                            item-value="ItemName"
                            outlined
                            dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Inscycle') + '*'"
                        ></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Frequency" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Insfrequency') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select
                            v-model="editedItem.ExecutionTime"
                            :items="SpotCheckExecutionTime"
                            item-text="ItemName"
                            item-value="ItemName"
                            outlined
                            dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.executiontime')"
                        ></v-select>

                        <!-- <v-text-field v-model="editedItem.ExecutionTime" :clearable="true" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.executiontime')" readonly></v-text-field>
                        <el-date-picker
                            value-format="yyyy-MM-dd HH:mm:ss"
                            v-model="editedItem.ExecutionTime"
                            type="datetime"
                            :placeholder="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.executiontime')"
                        ></el-date-picker> -->
                        <!-- <v-text-field v-model="editedItem.ExecutionTime" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.executiontime')"></v-text-field> -->
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.CheckStandard" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.CheckStandard')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Method" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Methods')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.LowerBound" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.UpperLimit')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.UpperBound" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.LowerLimit')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select
                            v-model="editedItem.InputType"
                            :items="SpotCheckInputType"
                            item-text="ItemName"
                            item-value="ItemName"
                            outlined
                            dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.InputType') + '*'"
                        ></v-select>
                        <!-- <v-text-field v-model="editedItem.InputType" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.InputType')"></v-text-field> -->
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.SpotPeriod" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Inspectiontime')"></v-text-field>
                    </v-col>

                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea v-model="editedItem.Remark" outlined rows="2" dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Remark')"></v-textarea>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" action="" :on-change="FileChange" :on-remove="FileRemove" multiple :limit="1" :file-list="Fileform.FileList">
                            <v-btn color="primary">{{ $t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBWJ.FileUpload') }}</v-btn>
                            <div slot="tip" class="el-upload__tip">{{ $t('GLOBAL.PDFOnly') }}</div>
                        </el-upload>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3 py-3">
                <!-- <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox> -->
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { StaffSiteGetList } from '@/api/peopleManagement/basicdata.js';
import { GetSpotCheckItemSaveForm } from '@/api/equipmentManagement/SpotCheckItem.js';
import { DeviceuploadFile } from '@/api/equipmentManagement/EquipParts.js';
import { Message, MessageBox } from 'element-ui';

const planType = ['点检'];
export default {
    props: {
        DeviceCategoryId: {
            type: String,
            default: ''
        },
        mcCyclelist: {
            type: Array,
            default: () => []
        },
        SpotCheckType: {
            type: Array,
            default: () => []
        },
        repastTypelist: {
            type: Array,
            default: () => []
        },
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        },
        maintenanceType: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            stateList: [
                { name: '启用', value: '1' },
                { name: '禁用', value: '0' }
            ],
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            planType,
            peopleitems: [],
            Fileform: {
                Name: '',
                Type: '',
                Version: '',
                Size: '',
                Uploaddate: '',
                FilePath: '',
                FileList: []
            },
            form: {
                Name: '',
                Code: '',
                Type: '',
                Cycle: '',
                Frequency: '',
                ExecutionTime: '',
                CheckStandard: '',
                Method: '',
                LowerBound: '',
                UpperBound: '',
                InputType: '',
                SpotPeriod: '',
                Remark: ''
            },
            SpotCheckExecutionTime: [],
            SpotCheckCycle: [],
            SpotCheckInputType: []
        };
    },
    computed: {
        editedItem() {
            const { Name, Code, Type, Cycle, Frequency, ExecutionTime, CheckStandard, Method, LowerBound, UpperBound, InputType, SpotPeriod, Remark } = this.tableItem;
            return {
                Name,
                Code,
                Type,
                Cycle,
                Frequency,
                ExecutionTime,
                CheckStandard,
                Method,
                LowerBound,
                UpperBound,
                InputType,
                SpotPeriod,
                Remark
            };
        }
    },
    async created() {
        this.SpotCheckExecutionTime = await this.$getNewDataDictionary('SpotCheckExecutionTime');
        this.SpotCheckCycle = await this.$getNewDataDictionary('SpotCheckCycle');
        this.SpotCheckInputType = await this.$getNewDataDictionary('SpotCheckInputType');
    },
    methods: {
        clearFiles() {
            this.Fileform.FileList = [];
            if (this.$refs.upload) {
                this.$refs.upload.clearFiles();
            }
            if (this.$refs.upload2) {
                this.$refs.upload2.clearFiles();
            }
        },
        async FileChange(File, fileList) {
            console.log(File.raw.type);
            const isPDf = File.raw.type === 'application/pdf';
            if (isPDf == false) {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL.PDFOnly'), color: 'error' });
                this.clearFiles();
                return false;
            }
            const formData = new FormData();
            formData.append('file', File.raw);
            let res = await DeviceuploadFile(formData);
            // let res = await fileUpload(formData);
            this.Fileform.Size = File.size;
            this.Fileform.Uploaddate = new Date();
            this.Fileform.Name = File.name;
            this.Fileform.FilePath = res.response.FilePath;
        },
        FileRemove(File, fileList) {
            this.Fileform.Name = '';
            this.Fileform.Size = '';
            this.Fileform.Uploaddate = '';
            this.Fileform.FilePath = '';
        },
        closeEquip() {
            this.showDialog = false;
            this.$refs.form.reset();
        },
        async addSave(type) {
            const params = type == 'add' ? this.form : this.editedItem;
            for(let k in params){
                if(params[k] == null){
                    params[k] = ""
                }
            }
            params.FilePath = this.Fileform.FilePath;
            params.FileName = this.Fileform.Name;
            params.DeviceCategoryId = this.DeviceCategoryId;
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            if (type == 'edit') {
                params.ID = this.tableItem.ID;
                if (
                    this.editedItem.Name == '' ||
                    this.editedItem.Type == '' ||
                    this.editedItem.Code == '' ||
                    this.editedItem.Cycle == '' ||
                    this.editedItem.Frequency == '' ||
                    this.editedItem.InputType == ''
                ) {
                    Message({
                        message: `${this.$t('Inventory.ToOver')}`,
                        type: 'error'
                    });
                    return;
                }
            } else {
                if (this.form.Name == '' || this.form.Code == '' || this.form.Type == '' || this.form.Cycle == '' || this.form.Frequency == '' || this.form.InputType == '') {
                    Message({
                        message: `${this.$t('Inventory.ToOver')}`,
                        type: 'error'
                    });
                    return;
                }
            }
            const res = await GetSpotCheckItemSaveForm(params);
            let { success, msg } = res;
            if (success) {
                for (let k in this.form) {
                    this.form[k] = '';
                }
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.$parent.RepastInfoGetPage();
                this.showDialog = this.classcheckbox ? false : true;
            }
        }
    }
};
</script>

<style lang="scss">
.py-0 {
    position: relative;
    .el-date-editor {
        width: 100%;
        position: absolute;
        opacity: 0;
        top: 0;
        .el-input__inner {
            font-size: 16px;
            height: 32.5px;
            color: rgba(0, 0, 0, 0.87);
            border: 1px solid #9e9e9e;
        }
        .el-input__icon {
            line-height: 32.5px;
        }
    }
}
</style>

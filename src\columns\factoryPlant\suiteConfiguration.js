// import i18n from '@/plugins/i18n';
export const suiteConfigurationColum = [
    {
        text: '序号',
        value: 'Index',
        width: '120px'
    },
    { text: '套件名称', value: 'ClassName', width: '120px' },
    { text: '套件编码', value: 'ClassCode', width: '120px' },
    { text: '套件类型', value: 'ClassType',width: '120px' },
    { text: '属性', align: 'center', value: 'attribute' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    { text: '操作', align: 'center', value: 'actions',width: '120px' }
];
export const attributeDialog = [
    { text: '编码', value: 'PropertyCode', sortable: true },
    { text: '名称', value: 'PropertyName', sortable: true },
    { text: '值', value: 'DefaultValue', sortable: true },
    { text: '备注', value: 'Remark', sortable: true },
    // { text: '有效', align: 'center', value: 'Enable' },
    { text: '操作', align: 'center', value: 'actions', sortable: true }
];

import { getRequestResources } from '@/api/fetch';
import store from '@/store';
const baseURL = 'baseURL_Inventory'
const baseURL2 = 'baseURL_MATERIAL'
const baseURL3 = 'baseURL_DFM'

export function GetMachineGBZ(data) {
    const api = '/api/ProductionHistoryView/GetProductionMachineGBZ'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMachineZZ(data) {
    const api = '/api/ProductionHistoryView/GetProductionMachineZZ'
    return getRequestResources(baseURL, api, 'post', data);
}




export function GetReasonCode(data) {
    const api = '/api/Dataitemdetail/GetReasonCode'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetPageLists(data) {
    const api = '/api/ProductionHistoryView/GetPageLists'
    return getRequestResources(baseURL, api, 'post', data);
}
export function Reverse(data) {
    const api = '/api/ProductionHistoryView/Reverse'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetProduceReportSwitch(data) {
    // 检查缓存是否存在且未过期（1小时）
    const cachedData = store.getters['dictionary/getDictionaryByCode']('ProduceReportSwitch');
    const lastUpdate = store.getters['dictionary/getLastUpdateTime'];
    const oneHour = 24 * 60 * 60 * 1000;
    
    if (cachedData && lastUpdate && (Date.now() - lastUpdate < oneHour)) {
        return Promise.resolve({ response: cachedData });
    }
    
    // 缓存不存在或已过期，从API获取
    const api = '/api/DataItemDetail/GetList?itemCode=ProduceReportSwitch&lang=cn';
    return getRequestResources(baseURL3, api, 'post', data).then(resp => {
        // 更新缓存
        store.dispatch('dictionary/saveDictionary', {
            code: 'ProduceReportSwitch',
            data: resp.response
        });
        return resp;
    });
}
export function ProduceReportSwitchSaveForm(data) {
    const api = '/api/DataItemDetail/SaveForm';
    return getRequestResources(baseURL3, api, 'post', data).then(resp => {
        // 清除缓存，确保下次获取最新数据
        store.dispatch('dictionary/clearDictionary', 'ProduceReportSwitch');
        return resp;
    });
}
export function RepeatData(data) {
    const api = `/api/ProductionHistoryView/RepeatPoProducedActual`
    return getRequestResources(baseURL, api, 'post', data);
}
export function  ScanWMS(data) {
    const api = `/api/ProductionHistoryView/ScanPoProducedActualWMS`
    return getRequestResources(baseURL, api, 'post', data,true);
}
export function  ScanWMSGBZ(data) {
    const api = `/api/ProductionHistoryView/ScanPoProducedActualWMSGBZ`
    return getRequestResources(baseURL, api, 'post', data,true);
}
export function GetReverseQty(data) {
    const api = `/api/ProductionHistoryView/GetReverseQty`
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetEquipmentStorege(data) {
    const api = `/api/ProductionHistoryView/GetEquipmentStorege`
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetProduceSumList(data) {
    const api = `/api/ProductionHistoryView/GetProduceSumList`
    return getRequestResources(baseURL, api, 'post', data);
}
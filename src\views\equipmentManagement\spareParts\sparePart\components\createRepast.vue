<template>
    <v-dialog v-model="showDialog" max-width="960px">
        <v-card class="" v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete v-model="form.Remark" :items="warehouseManagelist" item-text="Code"
                                item-value="Code" :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.Remark')" clearable dense
                                outlined></v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.SparePartsName" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.SparePartsName')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="form.SType" :items="equipmentSpareType" item-text="ItemName"
                                item-value="ItemName" :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.SType')" clearable
                                dense outlined></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.SparePartsCode" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.SparePartsCode')"></v-text-field>
                        </v-col>
                        <!-- <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Unit" outlined dense label="单位"></v-text-field>
                        </v-col> -->
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.UnitPrice" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.UnitPrice')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.CurrentStock" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.CurrentStock')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.MaxStock" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.MaxStock')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.MinStock" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.MinStock')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Cycle" type="number" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.Cycle')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field type="date" v-model="form.ExtendTime" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.ExtendTime')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="form.Introduce" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.Introduce')"></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'edit'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XG') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-autocomplete v-model="editedItem.Remark" :items="warehouseManagelist" item-text="Code"
                            item-value="Code" :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.Remark')" clearable dense
                            outlined></v-autocomplete>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.SparePartsName" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.SparePartsName')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select v-model="editedItem.SType" :items="equipmentSpareType" item-text="ItemName"
                            item-value="ItemName" :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.SType')" clearable dense
                            outlined></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.SparePartsCode" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.SparePartsCode')"></v-text-field>
                    </v-col>
                    <!-- <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="editedItem.Unit" outlined dense label="单位"></v-text-field>
                        </v-col> -->
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.UnitPrice" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.UnitPrice')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.CurrentStock" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.CurrentStock')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.MaxStock" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.MaxStock')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.MinStock" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.MinStock')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Cycle" type="number" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.Cycle')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field type="date" v-model="editedItem.ExtendTime" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.ExtendTime')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea v-model="editedItem.Introduce" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.Introduce')"></v-textarea>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { SparepartSaveForm, SparepartuselogSaveForm, addSparePart, WarehouseManageGetList } from '@/api/equipmentManagement/sparePart.js';
export default {
    props: {
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => { }
        },
        equipmentSpareType: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            warehouseManagelist: [],
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            form: {
                SparePartsName: '',
                SType: '',
                SparePartsCode: '',
                Unit: '',
                UnitPrice: '',
                CurrentStock: '',
                MaxStock: '',
                MinStock: '',
                Remark: '',
                Cycle: '',
                ExtendTime: '',
                Introduce: ''
            }
        };
    },
    computed: {
        editedItem() {
            const { ExtendTime, SparePartsName, SType, SparePartsCode, Unit, UnitPrice, CurrentStock, MaxStock, MinStock, Remark, Cycle, Introduce } = this.tableItem;
            return {
                ExtendTime,
                SparePartsName,
                SType,
                SparePartsCode,
                Unit,
                UnitPrice,
                CurrentStock,
                MaxStock,
                MinStock,
                Remark,
                Cycle,
                Introduce
            };
        }
    },
    created() {
        this.getWarehouseManage();
    },
    methods: {
        closeEquip() {
            this.showDialog = false;
            this.$refs.form.reset();
        },
        async addSave(type) {
            const paramsKey = Object.keys(this.form);
            const paramsObj = type == 'add' ? this.form : this.editedItem;
            let params = {};
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            if (type == 'edit') {
                params.ID = this.tableItem.ID;
            }
            const res = await SparepartSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.$parent.RepastInfoGetPage();
                this.showDialog = this.classcheckbox ? false : true;
            }
        },
        // 获取备件库
        async getWarehouseManage() {
            let params = {
                pageIndex: 1,
                pageSize: 10
            };
            const res = await WarehouseManageGetList(params);
            let { success, response } = res;
            if (success) {
                this.warehouseManagelist = response || [];
            }
        }
    }
};
</script>

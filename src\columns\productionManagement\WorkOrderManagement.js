export const workOrdeManagementColum = [
    {
        text: '工单状态',
        value: 'WoStatus',
        width: 100,
        sortable: true
    },
    {
        text: '工单类型',
        value: 'WoType',
        width: 100,
        sortable: true
    },
    {
        text: 'SAP订单',
        value: 'SapWoCode',
        width: 100,
        sortable: true
    },
    {
        text: '工单',
        value: 'WoCode',
        width: 100,
        sortable: true
    },
    { text: '成品物料', value: 'SapWoProductionCode', width: 190, dictionary: true },
    {
        text: '半成品料号',
        value: 'MaterialCode',
        width: 130,
        sortable: true
    },
    {
        text: '半成品描述',
        value: 'MaterialDescription',
        width: 250,
        sortable: true
    },
    {
        text: '班组',
        value: 'TeamName',
        width: 100,
        sortable: true
    },
    {
        text: '班次',
        value: 'ShiftName',
        width: 100,
        sortable: true
    },

    {
        text: '线体',
        value: 'FullLineName',
        width: 160,
        sortable: true
    },
    {
        text: '工段',
        value: 'CompanyName',
        width: 170,
        sortable: true
    },
    {
        text: '计划量(PCS)',
        width: 180,
        value: 'WoQuantity',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: '成批量(PCS)',
        value: 'WoSumBatchQuantity',
        semicolonFormat: true,
        width: 120,
        sortable: true
    },
    {
        text: '确认量(PCS)',
        value: 'WoConfirmBatchQuantity',
        semicolonFormat: true,
        width: 190,
        sortable: true
    },
    {
        text: '完工量(PCS)',
        value: 'WoCompleteQuantity',
        semicolonFormat: true,
        width: 180,
        sortable: false
    },
    {
        text: '计划开始',
        value: 'PlanStartTime',
        width: 160,
        sortable: true
    },
    {
        text: '计划结束',
        value: 'PlanEndTime',
        width: 160,
        sortable: true
    },
    {
        text: '实际开始',
        value: 'ActualStartTime',
        width: 160,
        sortable: true
    },
    {
        text: '实际结束',
        value: 'ActualEndTime',
        width: 160,
        sortable: true
    },
    {
        text: '关单原因',
        value: 'Reason',
        width: 160,
        sortable: true
    },
    { text: '操作', align: 'center', width: 100, value: 'actions' }
];
export const workOrdeManagementsColum = [
    {
        text: '工单状态',
        value: 'WoStatus',
        width: 150,
        sortable: true
    },
    {
        text: '线体',
        value: 'FullLineName',
        width: 140,
        sortable: true
    },
    {
        text: '工段',
        value: 'CompanyName',
        width: 170,
        sortable: true
    },
    {
        text: 'SAP订单',
        value: 'SapWoCode',
        width: 100,
        sortable: true
    },
    {
        text: '工单',
        value: 'WoCode',
        width: 100,
        sortable: true
    },
    {
        text: '班次',
        value: 'ShiftName',
        width: 100,
        sortable: true
    },
    {
        text: '班组',
        value: 'TeamName',
        width: 100,
        sortable: true
    },
    {
        text: '产品料号',
        value: 'MaterialCode',
        width: 140,
        sortable: true
    },
    {
        text: '产品描述',
        value: 'MaterialDescription',
        width: 250,
        sortable: true
    },
    {
        text: '计划量(PCS)',
        width: 175,
        value: 'WoQuantity',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: '计划开始时间',
        value: 'PlanStartTime',
        width: 180,
        sortable: true
    },
    {
        text: '计划结束时间',
        value: 'PlanEndTime',
        width: 180,
        sortable: true
    }
];

export const TestingdetaisColum = [
    {
        text: '批次号',
        value: 'BatchNo',
        width: 100,
        sortable: true
    },
    {
        text: '产品料号',
        value: 'MaterialCode',
        width: 120,
        sortable: true
    },
    {
        text: '数量(PCS)',
        width: 130,
        value: 'BatchQuantity',
        align: 'right',
        // semicolonFormat: true,
        sortable: true
    },
    {
        text: '班组',
        value: 'TeamName',
        width: 100,
        sortable: true
    },
    {
        text: '班次',
        value: 'ShiftName',
        width: 100,
        sortable: true
    },
    {
        text: '开始时间',
        value: 'BatchStartTime',
        width: 160,
        sortable: true
    },
    {
        text: '结束时间',
        value: 'BatchEndTime',
        width: 160,
        sortable: true
    },
    {
        text: '创建人',
        value: 'CreateUserId',
        width: 120,
        sortable: false
    },
    {
        text: '修改人',
        value: 'ModifyUserId',
        width: 120,
        sortable: false
    },
    { text: '操作', align: 'center', width: 100, value: 'actions', sortable: true }
];
export const accountBillColum = [
    {
        text: '序号',
        value: 'Index',
        width: 60
    },
    {
        text: '物料号',
        value: 'MaterialCode',
        width: 110
    },
    {
        text: '物料描述',
        value: 'MaterialDescription',
        width: 130
    },
    {
        text: '数采计数',
        width: 110,
        semicolonFormat: true,
        value: 'ScadaTagQty'
    },
    {
        text: '消耗量',
        width: 100,
        semicolonFormat: true,
        value: 'ScadaConsumptionQty'
    },
    {
        text: '确认消耗量',
        width: 110,
        value: 'WoConsumptionQty',
    },
    {
        text: '当班结存量',
        value: 'CompanyCreditQty',
        width: 110,
        isEditCell: true,
        digitalConver: true,
    },
    {
        text: '单位',
        value: 'MaterialUom',
        width: 80
    },
    { text: '操作', align: 'center', width: 80, value: 'actions' }
]
export const materialDetailColumns = [
    {
        text: '序号',
        value: 'Index',
        width: 60
    },
    {
        text: '物料号',
        value: 'MaterialCode',
        width: 110
    },
    {
        text: '物料描述',
        value: 'MaterialDescription',
        width: 130
    },
    {
        text: '物料批次',
        value: 'MaterialBatchNo',
        width: 130
    },
    {
        text: '数量',
        value: 'Quantity',
        width: 130
    },
    {
        text: '单位',
        value: 'MaterialUom',
        width: 130
    },
    { text: '操作', align: 'center', width: 80, value: 'actions' }
]
<template>
  <div>
    <div style="display: flex;width: 100%;height: 1.5rem;">
      <!-- <div
        style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;cursor: pointer;"
        @click="routeChange()"
      >{{ title1 }}</div> -->
      <div
        style="width: 70%;"
        class="titimgbox"
        @click="routeChange()"
      >
        <div style="width:10px;height:10px;border-radius:50%;background:#fff;margin-right:10px;margin-top:10px;"></div>
        <div style="font-size:18px;color:#fff;font-weight:blod;line-height:30px;">{{ title1 }}</div>
      </div>
      <div style="width: 30%;display: flex;">
        <dayMonIndex
          :simlevel='simlevel'
          :position="Order"
          @showChack="getExcel"
          :Dimension="Dimension"
          :Particle="Particle"
          :id1="id1"
          :BaseTime="BaseTime"
          :titlecir="title"
          :echarstType="6"
          :backgroundImg="backgroundImg"
        />
      </div>
    </div>
    <div
      :id="id1"
      style="width:100%;height:100%;"
    ></div>
    <!-- <div
      v-if="!showData"
      style="font-size: .7rem;color: #fff;text-align: center;margin-top: -5rem;"
    >暂无数据</div> -->
    <!-- <keyIndicatorslist
      ref="keyIndicatorsref"
      :exhibitionType="exhibitionType"
      :jtitle="title"
      :simlevel="simlevel"
      :Order="Order"
      :isSql="0"
      :BaseTime="BaseTime"
      :barName="barName"
    ></keyIndicatorslist> -->

    <keyIndicatorslistnew
      ref="keyIndicatorsrefnew"
      v-if="keyIndicatorslistnewShow"
      v-model="showkeyIndicatorslistnew"
      :exhibitionType="exhibitionType"
      :jtitle="title"
      :simlevel="simlevel"
      :Order="Order"
      :isSql="isSql1"
      :BaseTime="BaseTime"
      :barName="barName"
      :backgroundImg="backgroundImg"
      :Particle="Particle1"
      :legendData="legendData"
      @keynew="heandleKeycir"
    ></keyIndicatorslistnew>
  </div>
</template>
<script>
import { getqueryZ, getqueryLcr, getChartStructure, getTableList } from '@/api/simConfig/simconfignew.js';

export default {
  components: {
    dayMonIndex: () => import('@/views/simManagement/simNew1/components/dayMonIndex.vue'),
    // keyIndicatorslist: () => import('@/views/simManagement/simNew1/components/keyIndicatorslist.vue'),
    keyIndicatorslistnew: () => import('@/views/simManagement/simNew1/components/keyIndicatorslistnew.vue'),
  }
  ,
  props: {
    id1: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    Order: {
      type: String,
      default: ''
    },
    configFlag: {
      type: String,
      default: ''
    },
    exhibitionType: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    Dimension: {
      type: Array,
      default: () => []
    },
    routeList: {
      type: String,
      default: ''
    },
    backgroundImg: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      legendData: [],
      isSql1: '',
      Particle1: '',
      kpiName: '',
      title1: '',
      keyIndicatorslistnewShow: false,
      showkeyIndicatorslistnew: false,
      barName: '',
      showData: true,
      Particle: '',
      jtitle: '',
      chartPieh: null,
      pieTime: null,
      curConfig: {},
      // BaseTime: '2024-07-01',
      // TeamCode: 'A1180240614',
      // ProductionLineCode: 'A11802406',
      // FactoryCode: 'A118024',
      barTransverseChart: null,
      scrollTime: null,
      //当前时间颗粒度
      curShift: {
        KpiValues: []
      },
      myShiftList: [],
      id: '',
      yAxisOption: {
        type: 'value',
        // show: false
        axisLine: {
          show: false
        },
        axisLabel: {
          show: true
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      },
    }
  },
  computed: {
    //x轴配置
    xAxisOption() {
      let list = ['7', '6', '5', '4', '3', '2', '1']
      if (this.curShift.ChartData && this.curShift.ChartData.x) {
        list = this.curShift.ChartData.x.map(item => {
          // let month = dayjs(item).$M+1
          // let day = dayjs(item).$D
          if (['日', '周'].includes(this.curShift.TimeDimension)) {
            let month = item.split('-')[1]
            let day = item.split('-')[2]
            // 这里需要匹配颗粒度,来输出不同的x轴数据.
            // return `${month}-${day}`
            return `${month}-${day}`
          } else if (['月', '季度'].includes(this.curShift.TimeDimension)) {
            let label = item.split('年')[1]
            return label
          } else {
            return item
          }
        })
        // list.reverse()
        // list = this.curShift.ChartData.x
      }
      return {
        type: 'category',
        // boundaryGap: false,
        axisLine: {
          onZero: false,
          lineStyle: {
            color: '#888'
          },
        },
        axisLabel: {
          interval: 0,
          color: '#000'
        },
        data: list
      }
    },
    //目标值配置
    lineVisualMap() {
      // let list = []
      let obj = {
        show: false,
        // seriesIndex: 0,
        dimension: this.curConfig.ChartType === '3' ? '0' : '1',
        // dimension: 0,
        pieces: [
          {
            gt: 0,
            // lte: kpi.targetValue,
            lte: this.curShift.TargetValue,
            // color: kpi.noReachColor
            color: this.curShift.BelowTargetColer
            // color: 'red'
          },
          {
            // gt: kpi.targetValue,
            gt: this.curShift.TargetValue,
            // lte: 200,
            // color: kpi.reachColor
            color: this.curShift.AboveTargetColor
            // color: 'yellow'

          },
        ],
        // outOfRange: {
        //   color: '#999'
        // }
      }
      return [obj]

      // return [obj,obj2]
    },
    //折线图 Series  柱状图Series
    lineSeries() {
      if (this.curShift.ChartData.x.length <= 0) {
        this.$nextTick(() => {
          const dom = document.getElementById(this.id1);
          dom.innerHTML = '<div class="noDataBox">暂无数据</div>';
          dom.removeAttribute('_echarts_instance_');
          return
        })
      }
      //区分横竖
      let axisMarkLine = this.curConfig.ChartType === '6'
        ? [{ xAxis: this.curShift.TargetValue || '' }]
        : [{ yAxis: this.curShift.TargetValue || '' }]

      // let obj2 = {
      //   name: `${this.curShift.KpiName}实际值`,
      //   type: ['2','3'].includes(this.curConfig.ChartType)?'bar':'line',
      //   symbol: 'circle',
      //   symbolSize: 4,
      //   data: this.curShift.KpiValues.map(item=>item.DataValue),
      //   markLine: {//目标值线条
      //     silent: true,
      //     lineStyle: {
      //       color: this.curShift.TargetColor || 'gray'
      //       // color: 'red'
      //     },
      //     data: axisMarkLine
      //     // data: [{xAxis: 20 }]
      //   }
      // }
      let list = []
      Object.keys(this.curShift.ChartData).forEach(key => {
        if (['x', 'x', '目标值'].includes(key)) {
          return
        }
        let obj = {
          // name: `${this.curShift.KpiName}实际值`,
          // name: key.split(':')[1],
          name: `${key}实际值`,
          type: ['2', '3'].includes(this.curConfig.ChartType) ? 'bar' : 'line',
          symbol: 'circle',
          symbolSize: 4,
          // data: this.curShift.KpiValues.map(item=>item.DataValue),
          data: this.curShift.ChartData[key],
          markLine: {//目标值线条
            silent: true,
            lineStyle: {
              color: this.curShift.TargetColor || 'gray'
              // color: 'red'
            },
            data: axisMarkLine
            // data: [{xAxis: 20 }]
          }
        }
        list.push(obj)
      })
      return list
      // return [obj2]
    },
    //折线图配置
    lineOption() {
      return {
        symbol: 'circle',
        tooltip: {
          trigger: 'axis',
          extraCssText: 'z-index:999',
          axisPointer: {
            type: 'shadow',
          }
        },
        // legend: {
        //   data: this.lineLegend
        // },
        grid: {
          left: '5%',
          right: '6%',
          bottom: '3%',
          top: 20,
          containLabel: true
        },
        // visualMap: this.lineVisualMap,
        xAxis: this.xAxisOption,
        yAxis: this.yAxisOption,
        series: this.lineSeries
      }
    },
    bar1Option() {
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          }
        },
        // legend: {
        //   data: this.lineLegend
        // },
        grid: {
          left: '5%',
          right: '4%',
          bottom: '3%',
          top: 20,
          containLabel: true
        },
        // visualMap: this.lineVisualMap,
        xAxis: this.xAxisOption,
        yAxis: this.yAxisOption,
        series: this.lineSeries
      }
    },
    bar2Option() {
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          }
        },
        // legend: {
        //   data: this.lineLegend
        // },
        grid: {
          left: '5%',
          right: '4%',
          bottom: '3%',
          top: 20,
          containLabel: true
        },
        // visualMap: this.lineVisualMap,
        xAxis: this.yAxisOption,
        yAxis: this.xAxisOption,
        series: this.lineSeries
      }
    },
    //饼图配置
    pieOption() {

      return {
        tooltip: {
          trigger: 'item'
        },
        // legend: {
        //   orient: 'vertical',
        //   left: 'left'
        // },
        series: [
          {
            name: `${this.curShift.KpiName}`,
            type: 'pie',
            radius: [50, 250],
            center: ['50%', '50%'],
            // roseType: 'area',
            itemStyle: {
              borderRadius: 8,
            },
            // data: this.xAxisOption.data.map((item,index)=>{
            //   return {
            //     value: this.KPIList[0].practicalValueList[index],
            //     name: item
            //   }
            // }),
            label: {
              textStyle: {
                color: '#fff'
              }
            },
            data: this.curShift.KpiValues.map(item => {
              return {
                value: item.DataValue,
                name: item.DataTime,
              }
            }),
            // [
            //   { value: 1048, name: 'Search Engine' },
            //   { value: 735, name: 'Direct' },
            //   { value: 580, name: 'Email' },
            //   { value: 484, name: 'Union Ads' },
            //   { value: 300, name: 'Video Ads' }
            // ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
    },
    //右侧时间列表
    // myTimeList(){
    //   return this.curConfig.timeTypeList.map(item=>{
    //     let target = this.mapList.find(foo=>foo.value == item)
    //     return target
    //   })
    // },
    //左侧班次列表
    // myShiftList(){
    //   if(this.curTime.name == '班次'){
    //     return this.shiftList1
    //   }else if(['日班','月班'].includes(this.curTime.name) ){
    //     return this.shiftList2
    //   }else{
    //     return []
    //   }
    // }
  },
  created() {
    this.getBarList()
  },
  methods: {
    heandleKeycir() {
      this.keyIndicatorslistnewShow = false
    },
    routeChange() {
      if (this.routeList != '' && this.routeList != null && this.routeList != undefined) {
        this.$router.push({ path: `${this.routeList}`, query: { ProblemSource: this.Order.split('-')[0] } })
      }
      // this.$router.push({ path: 'simNew2', query: { code: item.Simcode } });
    },
    // openPopup() {
    //   if (this.configFlag == '是') {
    //     this.$refs.keyIndicatorsref.flagChange()
    //     this.$refs.keyIndicatorsref.showDialog = true;
    //   }
    // },
    getExcel(data) {
      this.Particle = data
      this.getBarList()
    },
    async getBarList() {
      let params = {
        "Position": this.Order,
        "BaseTime": this.BaseTime,
        "TeamCode": this.simlevel,
        // "ProductionLineCode": this.ProductionLineCode,
        // "FactoryCode": this.FactoryCode
      }
      let { response } = await getChartStructure(params)
      this.curConfig = response
      if (this.curConfig.IsSql == '1') {
        this.getBarList1()
      } else {
        if (this.curConfig?.ChartConfigs != null) {
          if (this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit == undefined) {
            this.title1 = this.title
          } else {
            this.title1 = this.title + '(' + this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit + ')'
          }
          // this.id = this.curConfig.ID;
          // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
          this.curConfig?.ChartConfigs.map(item => {
            item.KpiName = this.curConfig?.ChartConfigs.KpiName
            if (item.KpiValues[0]) {
              item.KpiCode = item.KpiValues[0].KpiCode
              item.TargetValue = item.KpiValues[0].TargetValue || 0
            }
          })
          //图表配置整体赋值
          // this.curConfig = response
          //时间颗粒度列表
          this.myShiftList = this.curConfig?.ChartConfigs.filter(item => {
            return item.TargetVisible === 1
          })
          //默认激活第一个时间颗粒度
          if (this.Particle != '') {
            this.myShiftList.map((el, index) => {
              if (this.Particle == el.TimeDimension) {
                this.curShift = this.myShiftList[index]
                this.$nextTick(() => {
                  this.query1(true)
                })
              } else {
                this.$nextTick(() => {
                  this.query1(false)
                })
              }
            })
          } else {
            this.curShift = this.myShiftList[0]
            this.Particle = this.curShift.TimeDimension
            this.$nextTick(() => {
              this.query1(true)
            })
          }

        } else {
          this.title1 = this.title
        }
      }


    },
    // async getBarList1() {
    //   let params = {
    //     "simLevel": this.$route.path == '/simManagement/simSpot' ? 'SIM2' : this.Order.split('-')[0],
    //     "position": [
    //       this.Order
    //     ],
    //     "paramList": [
    //       this.simlevel,
    //       this.BaseTime
    //     ]
    //   }
    //   let { response } = await getTableList(params)
    //   this.curConfig = response
    //   if (this.curConfig?.ChartConfigs != null) {
    //     if (this.curConfig.Unit == undefined || this.curConfig.Unit == '' || this.curConfig.Unit == null) {
    //       this.title1 = this.title
    //     } else {
    //       // this.title1 = this.title + '(' + this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit + ')'
    //       this.title1 = this.title + '(' + this.curConfig.Unit + ')'
    //     }
    //     // this.id = this.curConfig.ID;
    //     // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
    //     this.curConfig.ChartConfigs.map(item => {
    //       item.KpiName = this.curConfig.ChartConfigs.KpiName
    //       if (item.KpiValues[0]) {
    //         item.KpiCode = item.KpiValues[0].KpiCode
    //         item.TargetValue = item.KpiValues[0].TargetValue || 0
    //       }
    //     })
    //     //图表配置整体赋值
    //     // this.curConfig = response
    //     //时间颗粒度列表
    //     this.myShiftList = this.curConfig?.ChartConfigs.filter(item => {
    //       return item.TargetVisible === 1
    //     })
    //     console.log(this.myShiftList, ' this.myShiftList this.myShiftList this.myShiftList this.myShiftList');

    //     //默认激活第一个时间颗粒度
    //     if (this.Particle != '') {
    //       this.myShiftList.map((el, index) => {
    //         if (this.Particle == el.TimeDimension) {
    //           this.curShift = this.myShiftList[index]
    //           this.$nextTick(() => {
    //             this.query1(true)
    //           })
    //         } else {
    //           this.$nextTick(() => {
    //             this.query1(false)
    //           })
    //         }
    //       })
    //     } else {
    //       this.curShift = this.myShiftList[0]
    //       this.Particle = this.curShift.TimeDimension
    //       this.$nextTick(() => {
    //         this.query1(true)
    //       })
    //     }
    //   } else {
    //     this.title1 = this.title
    //   }

    // },
    async getBarList1() {
      let params = {
        "simLevel": this.$route.path == '/simManagement/simSpot' ? 'SIM2' : this.Order.split('-')[0],
        "position": [
          this.Order
        ],
        "paramList": [
          this.simlevel,
          this.BaseTime
        ]
      }
      let { response } = await getTableList(params)
      this.title1 = this.title
      this.curShift.KpiValues = response[0].positionResult
      this.kpiName = response[0].KpiName
      this.query1()

    },
    query1(data) {
      if (this.chartPieh) {
        this.chartPieh.clear()
        return
      }
      this.chartPieh = document.getElementById(this.id1);
      var myChart = this.$echarts.init(this.chartPieh);
      var colorList = ['#646DD5', '#1155cc', '#e69138', '#9900ff', '#ff9900'];

      var option
      option = {

        tooltip: {
          trigger: 'item'
        },
        itemStyle: {
          color: function () {
            return (
              'rgb(' +
              [
                Math.round(Math.random() * 270),
                Math.round(Math.random() * 370),
                Math.round(Math.random() * 400)
              ].join(',') +
              ')'
            );
          },
          borderRadius: 8
        },
        series: [
          {
            name: `${this.curShift.KpiName == undefined ? this.kpiName : this.curShift.KpiName}`,
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '50%'],
            // roseType: 'area',
            itemStyle: {
              borderRadius: 8,
              color: function (params) {
                return colorList[params.dataIndex % colorList.length];
              },
            },
            data: this.curShift.KpiValues.map((item, index) => {
              console.log(item, 'item');

              return {
                value: item.DataValue == undefined ? item.value : item.DataValue,
                name: item.DataTime == undefined ? item.name : item.DataTime,
              }
            }),
            label: {
              show: true,
              fontSize: 16,
              color: '#fff',
              formatter: function (data) {
                return data.name + '(' + data.value + '' + ')'
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      var that = this
      myChart.off('click');
      myChart.on('click', function (param) {
        that.$router.push({ path: '/simManagement/sim4', query: { name: param.name, ProblemSource: that.$route.path == '/simManagement/simSpot' ? 'SIM2' : that.Order.split('-')[0], } });

        // if (that.configFlag == '是') {
        //   that.barName = param.name
        //   that.$refs.keyIndicatorsref.flagChange()
        //   that.$refs.keyIndicatorsref.showDialog = true;
        // }
        // that.barName = param.seriesName.split('实际值')[0]
        // that.Particle1 = that.Particle
        // that.isSql1 = that.curConfig.IsSql
        // // that.$refs.keyIndicatorsref.flagChange()
        // // that.$refs.keyIndicatorsref.showDialog = true;
        // that.keyIndicatorslistnewShow = true
        // that.showkeyIndicatorslistnew = true
      });



      myChart.setOption(option, true);
      // var cityIndex = 0;
      // this.pieTime = setInterval(function () {
      //   if (cityIndex < bingData.length) {
      //     myChart.dispatchAction({ type: 'downplay', seriesIndex: 0 });
      //     myChart.dispatchAction({ type: 'highlight', seriesIndex: 0, dataIndex: cityIndex });
      //     myChart.dispatchAction({ type: 'showTip', seriesIndex: 0, dataIndex: cityIndex });
      //     cityIndex++
      //   } else {
      //     cityIndex = 0;
      //   }
      // }, 4000);
      window.addEventListener("resize", () => {
        myChart.resize()
      }, false);
    },
    query() {
      this.chartPieh = document.getElementById(this.id1);
      var myChart = this.$echarts.init(this.chartPieh);
      var bingData = [
        { value: 10, name: '事项1', itemStyle: { color: '#646DD5' } },
        { value: 5, name: '事项2', itemStyle: { color: '#4391F4' } },
        { value: 5, name: '事项3', itemStyle: { color: '#38BBE5' } },
        { value: 10, name: '事项4', itemStyle: { color: '#69D6FD' } },
        { value: 5, name: '事项5', itemStyle: { color: '#36C6A0' } },
        // { value: 5, name: 'M6', itemStyle: { color: '#4472c4' } },
        // { value: 10, name: 'M7', itemStyle: { color: '#70ad47' } },
        // { value: 5, name: 'M8', itemStyle: { color: '#255e91' } }
      ]
      var DataSum = 0
      for (var i = 0; i < bingData.length; i++) {
        DataSum += parseInt(bingData[i].value);
      }
      var colorList = ['#646DD5', '#1155cc', '#e69138', '#9900ff', '#93c47d', '#ff9900', '#674ea7', '#00ff00', '#e69138', '#ff9900', '#255e91', '#70ad47', '#4472c4', '#36C6A0', '#69D6FD'];
      var option
      option = {
        title: {
          text: this.title,
          textStyle: { // 标题样式
            color: '#fff'
          }
        },
        legend: {
          data: bingData,
          selectedMode: true,
          // type: 'scroll',
          top: 60,
          height: '75%',
          orient: 'vertical',
          left: '60%',
          textStyle: {
            color: '#fff'
          },
          formatter: name => {
            var data = option.series[0].data;
            var total = 0;
            var tarValue;
            for (var i = 0; i < data.length; i++) {
              total += data[i].value;
              if (data[i].name == name) {
                tarValue = data[i].value;
              }
            }
            var v = tarValue;
            var p = Math.round(((tarValue / DataSum) * 100));
            return `${name}(${p}%) ${v} `;
          },
        },
        //tooltip: {
        //    trigger: 'item',
        //},
        series: [
          {
            type: 'pie',
            center: ['30%', '50%'],
            radius: ['40%', '70%'],
            data: bingData,

            label: {
              normal: {
                show: false,
                formatter: function (name) {
                  return `\n${name.data.value} \n\n ${name.data.name}`;
                },
                position: "center",
                fontSize: 16,
                color: 'white'
              },
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 16,
                fontWeight: 'bold',
                textStyle: {
                  color: 'white'
                }
              },
            },
          },
        ]
      }
      myChart.setOption(option, true);
      //自动切换顺序
      var cityIndex = 0;
      this.pieTime = setInterval(function () {
        if (cityIndex < bingData.length) {
          myChart.dispatchAction({ type: 'downplay', seriesIndex: 0 });
          myChart.dispatchAction({ type: 'highlight', seriesIndex: 0, dataIndex: cityIndex });
          myChart.dispatchAction({ type: 'showTip', seriesIndex: 0, dataIndex: cityIndex });
          cityIndex++
        } else {
          cityIndex = 0;
        }
      }, 4000);
      window.addEventListener("resize", () => {
        myChart.resize()
      }, false);
    }
  },
  beforeDestroy() {
    clearInterval(this.pieTime);
  },
}
</script>
<style lang="scss" scoped>
#chartPieh {
    width: 100%;
    height: 100%;
}
::v-deep .noDataBox {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
}
.titimgbox {
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    width: 50%;
    height: 30px;
    line-height: 30px;
    border-radius: 5px;
    /* background-image: linear-gradient(to right, #056be0 0%, #000b61 100%); */
    display: flex;
    /* border: 1px solid #fff; */
    /* box-shadow: 0px 0px 7px 0px #fff; */
    /* overflow: hidden; */
}
</style>
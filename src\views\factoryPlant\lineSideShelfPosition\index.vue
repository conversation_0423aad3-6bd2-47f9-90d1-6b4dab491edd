// 线边货架管理
<template>
    <div class="double-table-view">
        <div class="double-table-main">
            <SearchForm ref="contactTorm" :searchinput="searchinput" :show-from="showFrom" @searchForm="searchForm" />
            <v-card outlined>
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        <!-- 搜索栏 -->
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" @click="operaClick({})">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="primary" @click="deleteItems()">{{ $t('GLOBAL._PLSC') }}</v-btn>
                </div>
                <Tables :table-height="tableHeight" :headers="headers" :desserts="desserts" :loading="loading"
                    :page-options="pageOptions" :btn-list="btnList" :click-fun="selectedPath"
                    :current-select-id="currentSelectId" table-name="DFM_XBHJW" @selectePages="selectePages"
                    @itemSelected="selectedItems" @toggleSelectAll="selectedItems" @tableClick="tableClick"></Tables>
            </v-card>
            <v-card class="mt-5">
                <div class="d-flex justify-space-between align-center the-child-table-menu">
                    <div>
                        <v-tabs>
                            <v-tab @click="activeTab = 0">{{ $t('DFM_XBHJW.XBHJW') }}</v-tab>
                            <v-tab @click="activeTab = 1">{{ $t('DFM_XBHJW.equipmentRacking') }}</v-tab>
                        </v-tabs>
                    </div>
                    <div>
                        <v-btn icon color="primary">
                            <v-icon>mdi-cached</v-icon>
                        </v-btn>
                        <v-btn color="primary" @click="operaDetailPop('')">{{ $t('GLOBAL._XZ') }}</v-btn>
                        <v-btn color="primary" @click="operaDetailPop('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn>
                    </div>
                </div>
                <MachineEquipment v-if="activeTab === 1" ref="machineEquipment" :current-select-id="currentSelectId">
                </MachineEquipment>
                <Product v-else ref="product" :current-select-id="currentSelectId" />
            </v-card>
            <!-- 添加/增加线边货架信息 -->
            <v-dialog v-model="isUpdate" scrollable width="55%">
                <updatePopup v-if="isUpdate" :opera-obj="operaObj" @handlePopup="handlePopup" />
            </v-dialog>
        </div>
    </div>
</template>
<script>
import { GetPageList, DeleteRacking } from '@/api/factoryPlant/sideLine.js';
import { lineSideShelfColumns } from '@/columns/factoryPlant/lineSideShelfPosition.js';
export default {
    name: 'ReasonDetail',
    components: {
        updatePopup: () => import('./components/updatePopup.vue'),
        Product: () => import('./components/position.vue'),
        MachineEquipment: () => import('./components/machineEquipment.vue')
    },
    data() {
        return {
            showFrom: false,
            deleteId: '',
            operaObj: {},
            isUpdate: false,
            headers: lineSideShelfColumns,
            activeTab: 0,
            currentSelectId: '',
            updateType: '',
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            selectedList: [],
            desserts: [],
            tableHeight: '320',
            searchParams: {}
        };
    },
    computed: {
        // 查询条件
        searchinput() {
            return [
                // '工厂'
                {
                    key: 'Factory',
                    value: '40012008',
                    icon: '',
                    label: this.$t('DFM_WLBOMGL.Factory')
                },
                // 货架代码
                {
                    key: 'RackingCode',
                    value: '',
                    icon: '',
                    label: this.$t('$vuetify.dataTable.DFM_XBHJW.RackingCode')
                },
                // '货架名称'
                {
                    key: 'RackingName',
                    value: '',
                    icon: '',
                    label: this.$t('$vuetify.dataTable.DFM_XBHJW.RackingName')
                }
            ];
        },
        btnList() {
            return [
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary' },
                { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'red' }
            ];
        }
    },
    created() {
        this.getDataList();
    },
    methods: {
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        //点击表格行
        selectedPath(o) {
            setTimeout(() => {
                this.currentSelectId = o.ID;
            }, 100);
        },
        // 操作栏按钮
        tableClick(item, type) {
            switch (type) {
                // 编辑
                case 'edit':
                    this.operaClick(item);
                    break;
                // 删除
                case 'delete':
                    this.deleteId = item.ID;
                    this.sureDelete();
                    break;
                default:
                    break;
            }
        },
        // 获取全部表格数据
        async getDataList() {
            this.loading = true;
            let params = {
                ...this.searchParams,
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await GetPageList(params);
            const { success, response } = res || {};
            const { data, dataCount, page } = response || {};
            if (success) {
                this.desserts = data || [];
                this.selectedPath(this.desserts.length > 0 ? this.desserts[0] : {});
                this.pageOptions.total = dataCount;
                this.pageOptions.page = page;
            } else {
                this.desserts = [];
                this.selectedPath({});
            }
            this.loading = false;
        },
        // 查询数据
        searchForm(o) {
            this.searchParams = o;
            this.getDataList();
        },
        // 新增/编辑线边货架
        operaClick(O) {
            this.operaObj = O;
            this.isUpdate = true;
        },
        // 批量删除
        deleteItems() {
            if (this.selectedList.length > 0) {
                this.deleteId = '';
                this.sureDelete();
            } else {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'error' });
            }
        },
        // 确认删除
        sureDelete() {
            this.$confirms({
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            }).then(async () => {
                const params = [];
                if (this.deleteId) {
                    params.push(this.deleteId);
                } else {
                    this.selectedList.forEach(e => {
                        params.push(e.ID);
                    });
                }
                const res = await DeleteRacking(params);
                this.selectedList = [];
                this.deleteId = '';
                const { success, msg } = res;
                if (success) {
                    this.pageOptions.pageCount = 1;
                    this.getDataList();
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                }
            });
        },
        // 根据子组件返回来的类型来进行操作
        handlePopup(type, data) {
            switch (type) {
                case 'refresh':
                    this.isUpdate = false;
                    this.getDataList();
                    break;
                case 'close':
                    this.isUpdate = false;
                    break;
                default:
                    break;
            }
        },
        // 子表的新增和删除
        operaDetailPop(type) {
            if (type === 'delete') {
                this.activeTab === 0 ? this.$refs.product.deleteItems() : this.$refs.machineEquipment.deleteItems();
            } else {
                if (!this.currentSelectId) {
                    return this.$store.commit('SHOW_SNACKBAR', { text: '请选择一个线边货架！', color: 'error' });
                }
                if (this.activeTab === 0) {
                    this.$refs.product.operaObj = {};
                    this.$refs.product.isUpdateDetail = true;
                } else {
                    this.$refs.machineEquipment.operaObj = {};
                    this.$refs.machineEquipment.isUpdateDetail = true;
                }
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.double-table-view {
    display: flex;

    .double-table-main {
        width: 100%;
    }
}
</style>

<template>
  <div class="sop-doc-form">
    <el-dialog 
      :title="dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')" 
      :visible.sync="dialogVisible" 
      width="700px"
      :close-on-click-modal="false" 
      :modal-append-to-body="false" 
      :close-on-press-escape="false"
      @close="dialogVisible = false">
      <el-form ref="dialogForm" :model="dialogForm" :rules="rules" label-width="100px">
        <div class="form-body">
          <el-row :gutter="20">
            <el-col :span="12" v-if="opertype === 2">
              <el-form-item label="主键">{{dialogForm.id}}</el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="所属目录" prop="dirId">
                <tree-select
                  v-model="dialogForm.dirId"
                  :data="treeData"
                  :props="{
                    children: 'children',
                    label: 'name',
                    value: 'id'
                  }"
                  @change="handleDirChange"
                  placeholder="请选择所属目录">
                </tree-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="文档编码" prop="docCode">
                <el-input v-model="dialogForm.docCode" placeholder="请输入文档编码"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="文档版本" prop="docVersion">
                <el-input v-model="dialogForm.docVersion" placeholder="请输入文档版本"></el-input>
              </el-form-item>
            </el-col>

            <!-- <el-col :span="12">
              <el-form-item label="是否有效" prop="docStatus">
                <el-radio-group v-model="dialogForm.docStatus">
                  <el-radio :label="1">有效</el-radio>
                  <el-radio :label="0">无效</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col> -->

            <el-col :span="24">
              <el-form-item label="文件上传" prop="docList">
                <div class="upload-box">
                  <el-upload
                    class="upload-demo"
                    :action="uploadUrl"
                    :on-success="handleUploadSuccess"
                    :on-error="handleUploadError"
                    :before-upload="beforeUpload"
                    :file-list="fileList"
                    :limit="1">
                    <el-button size="small" type="primary" icon="el-icon-upload2">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip">只能上传pdf/doc/docx文件</div>
                  </el-upload>
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="24" v-if="fileList.length">
              <el-form-item label="文件信息">
                <el-table :data="dialogForm.docList" border>
                  <el-table-column prop="docName" label="文件名" width="180"></el-table-column>
                  <el-table-column prop="docCode" label="编码" width="120">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.docCode" size="small"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column prop="docVersion" label="版本" width="100">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.docVersion" size="small"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column prop="docStatus" label="状态" width="100">
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.docStatus" size="small">
                        <el-option label="有效" :value="1"></el-option>
                        <el-option label="无效" :value="0"></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80">
                    <template slot-scope="scope">
                      <el-button type="text" size="small" @click="removeFile(scope.$index)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div class="btn-group">
          <el-button size="small" @click="dialogVisible = false">取 消</el-button>
          <el-button 
            type="primary" 
            size="small" 
            v-loading="formLoading" 
            :disabled="formLoading || !dialogForm.docList.length"
            @click="submitForm">
            确 定
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSopDocDetail,
  saveSopDocForm,
  batchAddSopDoc
} from "@/api/SOP/sopDoc";
import { getSopDirTree } from "@/api/SOP/sopDir";
import { configUrl } from '@/config'
import TreeSelect from '../components/tree-select'

export default {
  name: 'FormDialog',
  components: {
    TreeSelect
  },
  props: {
    treeData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogForm: {
        id: undefined,
        dirId: '',
        docCode: '',
        docVersion: '',
        docStatus: 1, // 默认有效
        deleted: 0,
        docList: [] // 存储多个文档记录
      },
      rules: {
        dirId: [
          { required: true, message: '请选择所属目录', trigger: 'change' }
        ],
        docCode: [
          { required: true, message: '请输入文档编码', trigger: 'blur' }
        ],
        docVersion: [
          { required: true, message: '请输入文档版本', trigger: 'blur' }
        ],
        docList: [
          { required: true, message: '请上传文档文件', trigger: 'change' }
        ]
      },
      dialogVisible: false,
      formLoading: false,
      opertype: 1, // 1-新增 2-编辑
      dirIdOptions: [], // 目录树选项
      docStatusOptions: [], // 状态选项
      deletedOptions: [], // 是否生效选项
      fileList: [],
      uploadUrl: `${configUrl[process.env.VUE_APP_SERVE].baseURL_DFM}/api/SopDoc/Upload`
    }
  },
  mounted() {
    this.getDictData()
  },
  methods: {
    async getDictData() {
      try {
        this.docStatusOptions = await this.$getNewDataDictionary('docStatus')
      } catch (err) {
        console.error('获取字典数据失败:', err)
        this.$message.error('获取字典数据失败')
      }
    },
    async submitForm() {
      try {
        await this.$refs.dialogForm.validate()
        this.formLoading = true

        // 使用批量保存接口
        const res = await batchAddSopDoc(this.dialogForm.docList)
        if (res.success) {
          this.$message.success(res.msg || '保存成功')
          this.$emit('saveForm')
          this.dialogVisible = false
        } else {
          this.$message.error(res.msg || '保存失败')
        }
      } catch (err) {
        if (err === false) return // 表单验证失败
        console.error('保存失败:', err)
        this.$message.error('保存失败')
      } finally {
        this.formLoading = false
      }
    },
    show(data) {
      this.opertype = data.ID ? 2 : 1
      this.dialogForm = {
        id: undefined,
        dirId: '',
        docCode: '',
        docVersion: '',
        docStatus: 1,
        deleted: 0,
        docList: []
      }
      this.fileList = []
      this.dialogVisible = true
      this.$nextTick(async () => {
        if (data.ID) {
          await this.getDialogDetail(data.ID)
        } else {
          // 如果是新增且dirId为空，默认取根目录的ID
          if (!this.dialogForm.dirId && this.treeData.length > 0) {
            this.dialogForm.dirId = this.treeData[0].id
            this.handleDirChange(this.treeData[0]) // 更新文件路径
          }
        }
      })
    },
    async getDialogDetail(id) {
      try {
        const res = await getSopDocDetail(id)
        if (res.success) {
          this.dialogForm = res.response || {}
          if (this.dialogForm.fileUuid) {
            this.fileList = [{
              name: this.dialogForm.docName,
              url: this.dialogForm.filePath
            }]
          }
        } else {
          this.$message.error(res.msg || '获取详情失败')
        }
      } catch (err) {
        console.error('获取详情失败:', err)
        this.$message.error('获取详情失败')
      }
    },
    handleUploadSuccess(res, file) {
      if (res.success) {
        // 清空之前的文档记录
        this.dialogForm.docList = []
        // 创建新的文档记录
        const docRecord = {
          dirId: this.dialogForm.dirId,
          docName: file.name,
          docCode: this.dialogForm.docCode || '', // 使用表单默认值或空字符串
          docVersion: this.dialogForm.docVersion || '',
          fileUuid: res.response,
          filePath: this.dialogForm.filePath,
          fileSize: file.size,
          docStatus: this.dialogForm.docStatus || 1,
          deleted: 0
        }
        this.dialogForm.docList.push(docRecord)
        this.$message.success('上传成功')
      } else {
        this.$message.error(res.msg || '上传失败')
      }
    },

    // 删除文件
    removeFile(index) {
      this.dialogForm.docList.splice(index, 1)
      this.fileList = []
    },
    getFullPath(nodeId) {
      const path = [];
      const findPath = (data, targetId) => {
        for (let node of data) {
          if (node.id === targetId) {
            path.unshift(node.name);
            return true;
          }
          if (node.children && findPath(node.children, targetId)) {
            path.unshift(node.name);
            return true;
          }
        }
        return false;
      };
      findPath(this.treeData, nodeId);
      return path.join('/');
    },
    handleDirChange(node) {
      // 当目录选择改变时，更新路径
      this.dialogForm.filePath = this.getFullPath(node.id);
    },
    handleUploadError(err) {
      console.error('文件上传失败:', err)
      this.$message.error('文件上传失败')
    },
    beforeUpload(file) {
      const validTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
      if (!validTypes.includes(file.type)) {
        this.$message.error('只能上传PDF/DOC/DOCX格式文件!')
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.sop-doc-form {
  :deep(.el-dialog) {
    border-radius: 8px;

    .el-dialog__header {
      padding: 15px 20px;
      border-bottom: 1px solid #ebeef5;
      margin: 0;
    }
    
    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__footer {
      padding: 15px 20px;
      border-top: 1px solid #ebeef5;
      background-color: #f9fafb;

      .btn-group {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
      }
    }
  }

  .form-body {
    padding: 15px 0;

    .el-form-item {
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }

      .el-form-item__content {
        line-height: 32px;
      }
    }

    .el-radio-group {
      display: flex;
      align-items: center;
      gap: 20px;

      .el-radio {
        margin-right: 0;
        
        :deep(.el-radio__label) {
          padding-left: 8px;
        }
      }
    }

    .upload-box {
      width: 100%;
      padding: 20px;
      border: 1px dashed #dcdfe6;
      border-radius: 4px;
      background-color: #fafafa;
      text-align: center;
      
      :deep(.el-upload) {
        .el-button {
          padding: 8px 15px;
          margin-bottom: 10px;
          font-size: 13px;
          
          i {
            margin-right: 4px;
          }
        }

        .el-upload__tip {
          color: #909399;
          font-size: 12px;
          line-height: 1.4;
        }
      }

      :deep(.el-upload-list) {
        text-align: left;
        padding: 0 10px;
        margin-top: 10px;

        .el-upload-list__item {
          transition: all 0.3s;
          
          &:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }
  }

  // 输入框统一样式
  :deep(.el-input),
  :deep(.el-select),
  :deep(.el-tree-select) {
    width: 100%;
    .el-input__inner {
      line-height: 32px;
      height: 32px;
    }
  }
}
</style>

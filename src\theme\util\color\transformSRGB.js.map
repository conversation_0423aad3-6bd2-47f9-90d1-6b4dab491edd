{"version": 3, "sources": ["../../../src/util/color/transformSRGB.ts"], "names": [], "mappings": ";;;;;;;;AACA;;AAEA;AACA,IAAM,iBAAiB,GAAG,CACxB,CAAC,MAAD,EAAS,CAAC,MAAV,EAAkB,CAAC,MAAnB,CADwB,EAExB,CAAC,CAAC,MAAF,EAAU,MAAV,EAAkB,MAAlB,CAFwB,EAGxB,CAAC,MAAD,EAAS,CAAC,MAAV,EAAkB,MAAlB,CAHwB,CAA1B,C,CAMA;;AACA,IAAM,oBAAoB,GAAG,SAAvB,oBAAuB,CAAC,CAAD;AAAA,SAC3B,CAAC,IAAI,SAAL,GACI,CAAC,GAAG,KADR,GAEI,iBAAQ,CAAR,EAAc,IAAI,GAAlB,IAAyB,KAHF;AAAA,CAA7B,C,CAMA;;;AACA,IAAM,iBAAiB,GAAG,CACxB,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,CADwB,EAExB,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,CAFwB,EAGxB,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,CAHwB,CAA1B,C,CAMA;;AACA,IAAM,oBAAoB,GAAG,SAAvB,oBAAuB,CAAC,CAAD;AAAA,SAC3B,CAAC,IAAI,OAAL,GACI,CAAC,GAAG,KADR,YAEK,CAAC,CAAC,GAAG,KAAL,IAAc,KAFnB,EAE6B,GAF7B,CAD2B;AAAA,CAA7B;;AAMM,SAAU,OAAV,CAAmB,GAAnB,EAA2B;AAC/B,MAAM,GAAG,GAAG,KAAK,CAAC,CAAD,CAAjB;AACA,MAAM,SAAS,GAAG,oBAAlB;AACA,MAAM,MAAM,GAAG,iBAAf,CAH+B,CAK/B;;AACA,OAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,CAApB,EAAuB,EAAE,CAAzB,EAA4B;AAC1B,IAAA,GAAG,CAAC,CAAD,CAAH,GAAS,IAAI,CAAC,KAAL,CAAW,oBAAM,SAAS,CACjC,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,IAAe,GAAG,CAAC,CAAD,CAAlB,GACA,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,IAAe,GAAG,CAAC,CAAD,CADlB,GAEA,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,IAAe,GAAG,CAAC,CAAD,CAHe,CAAf,IAIf,GAJI,CAAT;AAKD,GAZ8B,CAc/B;;;AACA,SAAO,CAAC,GAAG,CAAC,CAAD,CAAH,IAAU,EAAX,KAAkB,GAAG,CAAC,CAAD,CAAH,IAAU,CAA5B,KAAkC,GAAG,CAAC,CAAD,CAAH,IAAU,CAA5C,CAAP;AACD;;AAEK,SAAU,KAAV,CAAiB,GAAjB,EAA8B;AAClC,MAAM,GAAG,GAAQ,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAjB;AACA,MAAM,SAAS,GAAG,oBAAlB;AACA,MAAM,MAAM,GAAG,iBAAf,CAHkC,CAKlC;;AACA,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,IAAI,EAAP,GAAY,IAAb,IAAqB,GAAtB,CAAnB;AACA,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,IAAI,CAAP,GAAW,IAAZ,IAAoB,GAArB,CAAnB;AACA,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,IAAI,CAAP,GAAW,IAAZ,IAAoB,GAArB,CAAnB,CARkC,CAUlC;;AACA,OAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,CAApB,EAAuB,EAAE,CAAzB,EAA4B;AAC1B,IAAA,GAAG,CAAC,CAAD,CAAH,GAAS,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,IAAe,CAAf,GAAmB,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,IAAe,CAAlC,GAAsC,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,IAAe,CAA9D;AACD;;AAED,SAAO,GAAP;AACD", "sourcesContent": ["import { ColorInt, XYZ } from '../colorUtils'\nimport { clamp } from '../../util/helpers'\n\n// For converting XYZ to sRGB\nconst srgbForwardMatrix = [\n  [3.2406, -1.5372, -0.4986],\n  [-0.9689, 1.8758, 0.0415],\n  [0.0557, -0.2040, 1.0570],\n]\n\n// Forward gamma adjust\nconst srgbForwardTransform = (C: number): number => (\n  C <= 0.0031308\n    ? C * 12.92\n    : 1.055 * C ** (1 / 2.4) - 0.055\n)\n\n// For converting sRGB to XYZ\nconst srgbReverseMatrix = [\n  [0.4124, 0.3576, 0.1805],\n  [0.2126, 0.7152, 0.0722],\n  [0.0193, 0.1192, 0.9505],\n]\n\n// Reverse gamma adjust\nconst srgbReverseTransform = (C: number): number => (\n  C <= 0.04045\n    ? C / 12.92\n    : ((C + 0.055) / 1.055) ** 2.4\n)\n\nexport function fromXYZ (xyz: XYZ): ColorInt {\n  const rgb = Array(3)\n  const transform = srgbForwardTransform\n  const matrix = srgbForwardMatrix\n\n  // Matrix transform, then gamma adjustment\n  for (let i = 0; i < 3; ++i) {\n    rgb[i] = Math.round(clamp(transform(\n      matrix[i][0] * xyz[0] +\n      matrix[i][1] * xyz[1] +\n      matrix[i][2] * xyz[2]\n    )) * 255)\n  }\n\n  // Rescale back to [0, 255]\n  return (rgb[0] << 16) + (rgb[1] << 8) + (rgb[2] << 0)\n}\n\nexport function toXYZ (rgb: ColorInt): XYZ {\n  const xyz: XYZ = [0, 0, 0]\n  const transform = srgbReverseTransform\n  const matrix = srgbReverseMatrix\n\n  // Rescale from [0, 255] to [0, 1] then adjust sRGB gamma to linear RGB\n  const r = transform((rgb >> 16 & 0xff) / 255)\n  const g = transform((rgb >> 8 & 0xff) / 255)\n  const b = transform((rgb >> 0 & 0xff) / 255)\n\n  // Matrix color space transform\n  for (let i = 0; i < 3; ++i) {\n    xyz[i] = matrix[i][0] * r + matrix[i][1] * g + matrix[i][2] * b\n  }\n\n  return xyz\n}\n"], "sourceRoot": "", "file": "transformSRGB.js"}
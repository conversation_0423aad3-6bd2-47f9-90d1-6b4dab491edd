<template>
  <el-dialog
    :style="backgroundVar"
    :title="title1"
    :append-to-body="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="value"
    :before-close="handleClose1"
    lock-scroll
    :fullscreen="true"
  >
    <div
      :id="id1 + '-123'"
      style="width: 100%;height: 90vh;"
    >
    </div>
    <div
      v-if="tableData.length>0"
      style="font-size: 18px;color:#fff;font-weight: bold;"
    >{{ barTitle }}:</div>
    <el-table
      v-if="tableData.length>0"
      :data="tableData"
      border
      style="width: 100%;margin-top: 20px;color:#fff; font-size: 12px;font-weight: bold;overflow-y: auto;"
      :header-cell-style="{background:'#fafafa',textAlign: 'center'}"
      :row-style="{height: '35px'}"
      show-overflow-tooltip
      :tooltip-effect="'dark'"
      :height="tableHeight"
    >
      <el-table-column
        prop="date"
        label="日期"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="value"
        label="值"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="target"
        label="目标值"
        align="center"
        show-overflow-tooltip
      />
    </el-table>
  </el-dialog>
</template>
<script>
import { getChartStructure, getTableList } from '@/api/simConfig/simconfignew.js';
import { textStyle } from 'echarts/lib/theme/dark';

export default {
  props: {
    // 是否显示弹出框
    value: {
      type: Boolean,
      default: false
    },
    position: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    id1: {
      type: String,
      default: ''
    },
    titlepie: {
      type: String,
      default: ''
    },
    backgroundImg: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      title1: '',
      kpiName: '',
      barTitle: '',
      tableData: [],
      tableHeight: 0,
      lineLegend: ['实际值', '目标值'],
      //当前时间颗粒度
      curShift: {
        KpiValues: []
      },
      myShiftList: [],
      chartBartc: null,
      yAxisOption: {
        type: 'value',
        // show: false
        axisLine: {
          show: false
        },
        axisLabel: {
          show: true
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      },
    }
  },
  computed: {
    backgroundVar() {
      return {
        '--background': this.backgroundImg
      }
    },
    //x轴配置
    xAxisOption() {
      let list = ['7', '6', '5', '4', '3', '2', '1']
      if (this.curShift.ChartData && this.curShift.ChartData.x) {
        list = this.curShift.ChartData.x.map(item => {
          // let month = dayjs(item).$M+1
          // let day = dayjs(item).$D
          if (['日', '周'].includes(this.curShift.TimeDimension)) {
            let month = item.split('-')[1]
            let day = item.split('-')[2]
            // 这里需要匹配颗粒度,来输出不同的x轴数据.
            // return `${month}-${day}`
            return `${month}-${day}`
          } else if (['月', '季度'].includes(this.curShift.TimeDimension)) {
            let label = item.split('年')[1]
            return label
          } else {
            return item
          }
        })
        // list.reverse()
        // list = this.curShift.ChartData.x
      }
      return {
        type: 'category',
        // boundaryGap: false,
        axisLine: {
          onZero: false,
          lineStyle: {
            color: '#fff'
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff'
        },
        data: list
      }
    },
    lineSeries() {
      if (this.curShift.ChartData.x.length <= 0) {
        this.$nextTick(() => {
          const dom = document.getElementById(this.id1 + '-123');
          dom.innerHTML = '<div class="noDataBox">暂无数据</div>';
          dom.removeAttribute('_echarts_instance_');
          return
        })
      }
      //区分横竖
      let axisMarkLine = this.curConfig.ChartType === '5'
        ? [{ xAxis: this.curShift.TargetValue || '' }]
        : [{ yAxis: this.curShift.TargetValue || '' }]

      // let obj2 = {
      //   name: `${this.curShift.KpiName}实际值`,
      //   type: ['2','3'].includes(this.curConfig.ChartType)?'bar':'line',
      //   symbol: 'circle',
      //   symbolSize: 4,
      //   data: this.curShift.KpiValues.map(item=>item.DataValue),
      //   markLine: {//目标值线条
      //     silent: true,
      //     lineStyle: {
      //       color: this.curShift.TargetColor || 'gray'
      //       // color: 'red'
      //     },
      //     data: axisMarkLine
      //     // data: [{xAxis: 20 }]
      //   }
      // }
      let list = []
      Object.keys(this.curShift.ChartData).forEach(key => {
        if (['x', 'x', '目标值'].includes(key)) {
          return
        }
        let obj = {
          // name: `${this.curShift.KpiName}实际值`,
          // name: key.split(':')[1],
          name: `${key}实际值`,
          type: ['2', '3'].includes(this.curConfig.ChartType) ? 'bar' : 'line',
          symbol: 'circle',
          symbolSize: 4,
          barWidth: 10,
          itemStyle: {
            normal: {
              barBorderRadius: [4, 4, 4, 4],
            }
          },
          // data: this.curShift.KpiValues.map(item=>item.DataValue),
          data: this.curShift.ChartData[key],
          markLine: {//目标值线条
            silent: true,
            lineStyle: {
              color: this.curShift.TargetColor || 'gray'
              // color: 'red'
            },
            data: axisMarkLine
            // data: [{xAxis: 20 }]
          }
        }
        list.push(obj)
      })
      return list
    },
  },
  created() {
    this.getBarList()
  },
  mounted() {
    this.$nextTick(function () {
      this.tableHeight = window.innerHeight - 574;
      let self = this;
      window.onresize = function () {
        self.tableHeight = window.innerHeight - 574;
      }
    })
  },
  methods: {
    async getBarList() {
      let params = {
        "Position": this.position,
        "BaseTime": this.BaseTime,
        "TeamCode": this.simlevel,
        // "ProductionLineCode": this.ProductionLineCode,
        // "FactoryCode": this.FactoryCode
      }
      let { response } = await getChartStructure(params)
      this.curConfig = response
      if (this.curConfig.IsSql == '1') {
        this.getBarList1()
      } else {
        if (this.curConfig?.ChartConfigs != null) {
          if (this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit == undefined) {
            this.title1 = this.titlepie
          } else {
            this.title1 = this.titlepie + '(' + this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit + ')'
          }
          // this.id = this.curConfig.ID;
          // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
          this.curConfig.ChartConfigs.map(item => {
            item.KpiName = this.curConfig.ChartConfigs.KpiName
            if (item.KpiValues[0]) {
              item.KpiCode = item.KpiValues[0].KpiCode
              item.TargetValue = item.KpiValues[0].TargetValue || 0
            }
          })
          //图表配置整体赋值
          // this.curConfig = response
          //时间颗粒度列表
          this.myShiftList = this.curConfig.ChartConfigs.filter(item => {
            return item.TargetVisible === 1
          })
          //默认激活第一个时间颗粒度
          this.curShift = this.myShiftList[0]
          this.query1()
          this.tableDataChange(this.curConfig.ChartConfigs[0]?.ChartData)
        } else {
          this.title1 = this.titlepie
        }
      }

    },
    async getBarList1() {
      let params = {
        "simLevel": this.position.split('-')[0],
        "position": [
          this.position
        ],
        "paramList": [
          this.simlevel,
          this.BaseTime
        ]
      }
      let { response } = await getTableList(params)
      if (this.curConfig?.ChartConfigs != null) {
        if (this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit == undefined) {
          this.title1 = this.titlepie
        } else {
          this.title1 = this.titlepie + '(' + this.curConfig?.ChartConfigs[0]?.KpiValues[0]?.Unit + ')'
        }
      } else {
        this.title1 = this.titlepie
      }
      this.curShift.KpiValues = response[0].positionResult
      this.kpiName = response[0].KpiName
      this.query1()
    },
    tableDataChange(data) {
      if (data.length <= 0) return
      const keys = Object.keys(data);
      this.barTitle = keys[0]
      const firstKey = keys[0];
      const secondKey = keys[1];
      const thirdKey = keys[2];
      const dataArray = data[firstKey];
      const dateArray = data[secondKey];
      const targetArray = data[thirdKey];
      this.tableData = [];
      for (let i = 0; i < dateArray.length; i++) {
        this.tableData.push({ date: dateArray[i], value: dataArray[i], target: targetArray[i] });
      }
    },
    query1() {
      if (this.chartBartc) {
        this.chartBartc.clear()
        return
      }
      this.chartBartc = this.$echarts.init(document.getElementById(this.id1 + '-123'));
      var option
      var colorList = ['#e69138', '#646DD5', '#1155cc', '#9900ff', '#ff9900'];
      option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: this.curShift.KpiValues.map(item => item.DataTime),
          textStyle: {
            color: '#fff'
          }
        },
        itemStyle: {
          color: function () {
            return (
              'rgb(' +
              [
                Math.round(Math.random() * 270),
                Math.round(Math.random() * 370),
                Math.round(Math.random() * 400)
              ].join(',') +
              ')'
            );
          },
          borderRadius: 8
        },
        series: [
          {
            name: `${this.curShift.KpiName == undefined ? this.kpiName : this.curShift.KpiName}`,
            type: 'pie',
            radius: '60%',
            itemStyle: {
              borderRadius: 8,
              color: function (params) {
                return colorList[params.dataIndex % colorList.length];
              },
            },
            data: this.curShift.KpiValues.map((item, index) => {
              return {
                value: item.DataValue == undefined ? item.value : item.DataValue,
                name: item.DataTime == undefined ? item.name : item.DataTime,
              }
            }),
            label: {
              show: true,
              fontSize: 16,
              color: '#fff',
              formatter: function (data) {
                return data.name + '(' + data.value + '' + ')'
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      this.chartBartc.setOption(option, true);
      window.addEventListener("resize", () => {
        this.chartBartc.resize()
      }, false);
    },
    handleClose1() {
      this.$emit('showCheck5')
    }
  }
}
</script>
<style scoped>
/deep/ .el-dialog__title {
    color: #fff !important;
}
/deep/ .el-dialog {
    background: var(--background) no-repeat 0 0;
    background-size: 100% 100% !important;
    overflow: hidden;
}
/deep/ .el-textarea__inner {
    font-size: 16px !important;
}
/deep/ .el-dialog__headerbtn .el-dialog__close {
    font-size: 22px;
}
/deep/ .el-dialog__header {
    border: none !important;
}
/deep/.el-textarea__inner {
    background-color: rgba(225, 225, 225, 0);
    border: none !important;
    color: black;
    font-size: 16px;
    /* font-weight: bold; */
    /* font-family: "Lucida Calligraphy", cursive, serif, sans-serif; */
}

/deep/ .el-dialog__headerbtn .el-dialog__close {
    font-size: 22px;
}
/deep/ .el-dialog__header {
    border: none !important;
}

/deep/.el-textarea__inner {
    /* background-color: rgba(225, 225, 225, 0); */
    background-color: #fff !important;
    border: none !important;
    color: black;
    font-size: 20px;
    /* font-weight: bold; */
}
/deep/.el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}
/deep/ .el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}

/deep/ .el-table tr {
    background-color: transparent !important;
    border: none;
}
/deep/ .el-table--enable-row-transition .el-table__body td,
.el-table .cell {
    background-color: transparent !important;
}
/deep/ .el-table th.el-table__cell {
    background-color: transparent !important;
    color: #fff;
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}
/deep/ .el-dialog__title {
    color: #fff !important;
}

/deep/ .el-table tbody tr {
    pointer-events: none;
}
::v-deep .noDataBox {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
}
/deep/ .el-table__header-wrapper {
    background-color: #4391f4 !important;
}
</style>
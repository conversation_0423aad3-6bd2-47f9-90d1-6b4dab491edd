<template>
    <div>
        <Tables
            :page-options="pageOptions"
            ref="Tables"
            :footer="false"
            :showSelect="false"
            :loading="loading"
            :btn-list="btnList"
            :clickFun="clickFun"
            tableHeight="calc(50vh - 50px) !important"
            table-name="TPM_SBGL_SBWXGD"
            :headers="fwcgtopColum"
            :desserts="desserts"
            @tableClick="tableClick"
        ></Tables>

        <Tables
            :page-options="pageOptions"
            ref="recordTable"
            :footer="false"
            :showSelect="false"
            :loading="loading2"
            :btn-list="[]"
            tableHeight="calc(50vh - 50px) !important"
            table-name="TPM_SBGL_SBWXGD"
            :headers="fwcgdownColum"
            :desserts="desserts2"
        ></Tables>
        <el-dialog :title="$t('GLOBAL._BJ')" :visible.sync="SaveFormModel" width="30%">
            <div class="addForm" v-for="(item, index) in SaveFormList" :key="index">
                <v-text-field v-if="item.type == 'input'" :id="item.id + 'SbxxList'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                <el-radio-group v-model="item.value" v-if="item.type == 'radio'">
                    <div class="textlabel">{{ item.label }}:</div>
                    <el-radio :label="it.value" :key="ind" v-for="(it, ind) in item.radiolist">{{ it.label }}</el-radio>
                </el-radio-group>
                <v-autocomplete
                    v-if="item.type == 'select'"
                    :multiple="item.multiple"
                    clearable
                    v-model="item.value"
                    :items="item.option"
                    item-text="ItemName"
                    item-value="ItemValue"
                    :label="item.label"
                    clear
                    dense
                    outlined
                ></v-autocomplete>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="SaveFormModel = false">取 消</el-button>
                <el-button type="primary" @click="SaveFormSave()">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { Message, MessageBox } from 'element-ui';
import {
    GetRepairRecordRepairRecord,
    GetRepairServiceSaveForm,
    GetRepairServiceDelete,
    GetRepairServiceRequest,
    GetRepairRecordPageList,
    GetRepairRecordReceive,
    GetRepairOrderSource,
    GetRepairOrderStatus,
    GetRepairRecordReject,
    GetRepairRecordReassign,
    GetRepairOrderType,
    GetRepairRecordSaveForm
} from '@/api/equipmentManagement/MyWorkorder.js';
import { GetRepairServiceGetList, GetRepairAcceptanceItem } from '@/api/equipmentManagement/NewRepair.js';
import { fwcgtopColum, fwcgdownColum } from '@/columns/equipmentManagement/Repair.js';
export default {
    components: {},
    data() {
        return {
            fwcgtopColum,
            RepairWoId:"",
            loading: false,
            loading2: false,
            desserts: [],
            desserts2: [],
            fwcgdownColum,
            dialogType: '',
            tableItem: {},
            StatusList: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            SaveFormModel: false,
            SaveFormList: [
                {
                    label: this.$t('TPM_SBGL_WDGD.xqyy') + ' *',
                    type: 'input',
                    require: true,
                    id: 'RequestReason',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.fwnr') + ' *',
                    type: 'input',
                    require: true,
                    id: 'RequestContent',
                    value: ''
                },

                {
                    label: this.$t('TPM_SBGL_WDGD.xykxc') + ' *',
                    require: true,
                    type: 'radio',
                    radiolist: [
                        {
                            label: '是',
                            value: '是'
                        },
                        {
                            label: '否',
                            value: '否'
                        }
                    ],
                    id: 'IsLookSite',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.csr'),
                    type: 'select',
                    multiple: true,
                    option: [],
                    id: 'CarbonCopy',
                    value: ''
                },
                {
                    label: this.$t('TPM_SBGL_WDGD.bz'),
                    type: 'input',
                    id: 'RequestRemark',
                    value: ''
                }
            ]
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: ''
                },
                {
                    text: this.$t('GLOBAL._shenqing'),
                    code: 'sq',
                    type: 'primary',
                    icon: '',
                    authCode: ''
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: ''
                }
            ];
        }
    },
    async created() {
        this.StatusList = await this.$getNewDataDictionary('RepairServiceStatus');
    },
    methods: {
        async SaveFormSave() {
            let flag = this.SaveFormList.some(item => {
                if (item.require) {
                    return item.value == '' || item.value == null;
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            this.SaveFormList.forEach(item => {
                if (item.multiple) {
                    if (item.value.length == 0) {
                        this.tableItem[item.id] = '';
                    } else {
                        this.tableItem[item.id] = item.value.join('|');
                    }
                } else {
                    this.tableItem[item.id] = item.value;
                }
            });
            let res = await GetRepairServiceSaveForm(this.tableItem);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '修改成功', color: 'success' });
                this.MyGetRepairAcceptanceItem();
                this.SaveFormModel = false;
            }
        },
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.SaveFormList.forEach(item => {
                        for (let k in this.tableItem) {
                            if (item.id == k) {
                                if (k == 'CarbonCopy') {
                                    item.value = this.tableItem[k].split('|');
                                } else {
                                    item.value = this.tableItem[k];
                                }
                            }
                        }
                    });
                    this.SaveFormModel = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
                case 'sq':
                    this.sqtable();
                    return;
            }
        },
        async sqtable() {
            let res = await GetRepairServiceRequest(this.tableItem);
            if (res.success) {
                this.tableItem = {};
                this.$store.commit('SHOW_SNACKBAR', { text: '申请成功', color: 'success' });
                this.MyGetRepairServiceGetList();
            }
        },
        async deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await GetRepairServiceDelete(params);
                    if (res.success) {
                        this.tableItem = {};
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.MyGetRepairServiceGetList();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        //  查看BOM详情
        clickFun(data) {
            this.tableItem = data;
            this.$refs.Tables.selected = [data];
            this.MyGetRepairAcceptanceItem();
        },
        async MyGetRepairServiceGetList(row, data) {
                if (row) {
                this.RepairWoId = row.RepairWoId;
            }
            let params = {
                Factory: this.$route.query.Factory ? this.$route.query.Factory : '2010',
                RepairWoId: this.RepairWoId
            };
            if (data) {
                this.SaveFormList[3].option = data;
            }
            this.loading = true;
            let res = await GetRepairServiceGetList(params);
            let mydata = res.response;
            for (let k in mydata) {
                let item = mydata[k];
                item.RequestByName = await this.$getPerson(item.RequestBy);
                this.StatusList.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
            }
            this.desserts = res.response;
            this.loading = false;
        },
        async MyGetRepairAcceptanceItem() {
            let params = {
                SupplierServiceId: this.tableItem.ID
            };
            this.loading2 = true;
            let res = await GetRepairAcceptanceItem(params);
            this.desserts2 = res.response;
            this.loading2 = false;
        }
    }
};
</script>

<style lang="scss">
.addForm {
    .textfieldbox {
        .el-input {
            width: 100%;
        }
    }
    .textlabel {
        display: inline-flex;
        font-size: 16px;
        margin-right: 25px;
    }
    .el-radio-group {
        height: 40px;
        margin-top: 10px;
    }
    .el-radio__input.is-checked + .el-radio__label {
        color: #3dcd58;
    }
    .el-radio__input.is-checked .el-radio__inner {
        border-color: #3dcd58;
        background: #3dcd58;
    }
    .el-radio__label {
        font-size: 16px;
    }
}
</style>

<template>
  <v-card>
    <v-card-title
      class="headline primary lighten-2"
      primary-title
    >
      升级
    </v-card-title>
    <v-card-text>
      <v-form
        ref="form"
        v-model="valid"
      >
        <v-row class="mt-5">
          <v-col
            :cols="124"
            :lg="12"
            class="pt-0 pb-0"
          >
            <!-- <v-select
              :items="sendOutList"
              label="发送人"
              required
              dense
              outlined
              v-model="sendOut"
            ></v-select> -->
            <v-autocomplete
              v-model="sendOut"
              label="用户"
              :items="sendOutList"
              item-text="UserName"
              item-value="LoginName"
              clearable
              dense
              outlined
              multiple
              chips
              placeholder="用户"
              @change="handleClick"
            >
            </v-autocomplete>
          </v-col>
          <v-col
            :cols="12"
            :lg="12"
            class="pt-0 pb-0"
          >
            <div
              v-for="(item,index) in userList"
              :key="index"
              style="width: 100%;border-radius: 10px;box-shadow: 0px 0px 10px 0px #ccc;margin-bottom: 8px;padding-left: 5px;box-sizing: border-box;"
            >
              <div style="width: 100%;height: 40px;display: flex;">
                <div style="color: #000000;font-size: 14px;font-weight: bold;line-height: 40px;width: 120px;">用户:&nbsp;&nbsp;{{ item.name }}</div>
                <div style="color: #000000;font-size: 14px;font-weight: bold;line-height: 40px;overflow-y: auto;">部门:&nbsp;&nbsp;{{ item.bm }}</div>
              </div>
            </div>
          </v-col>
          <v-col
            :cols="12"
            :lg="12"
            class="pt-0 pb-0"
          >
            <v-textarea
              height="auto"
              rows="3"
              v-model="remark"
              clearable
              label="协调事项"
              outlined
              dense
              required
            ></v-textarea>
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
    <v-divider></v-divider>

    <v-card-actions>
      <v-btn
        color="primary"
        @click="submitForm"
      >{{ $t('GLOBAL._QD') }}</v-btn>
      <v-btn
        color="normal"
        @click="closePopup"
      >{{ $t('GLOBAL._GB') }}</v-btn>
    </v-card-actions>
  </v-card>
</template>

<script>
import { LevelUp } from "./service"
import { queryUser, getSim3LevelUp, getStaffByDepartId } from '@/api/simConfig/simconfignew.js';

export default {
  props: {
    id: {
      type: String,
      default: ''
    },
    simLevel1: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      userList: [],
      sendOutList: [],
      sendOut: '',
      remark: '',
    };
  },
  created() {
    if (localStorage.getItem('Sender')) {
      this.sendOut = JSON.parse(localStorage.getItem('Sender'))
    }
    this.getTeamList1()
  },

  methods: {
    // 获取用户数据
    async getTeamList1(val) {
      let resp = await getStaffByDepartId({ UserName: "", pageIndex: 1, pageSize: 5000 })
      if (resp && resp.response) {
        console.log(resp.response, 'resp.responseresp.response12345');

        this.sendOutList = resp.response.data
      } else {
        this.sendOutList = []
      }
    },
    handleClick(value) {
      this.userList = []
      console.log(value, '000000000000000');
      this.sendOutList.map(el => {
        value.map(el1 => {
          if (el.LoginName == el1) {
            this.userList.push({
              name: el.UserName,
              bm: el.PostName
            })
          }
        })
      })
    },
    async submitForm() {
      let noticePersonId
      if (localStorage.getItem('Sender')) {
        noticePersonId = JSON.parse(localStorage.getItem('Sender')).join(',')
      } else {
        noticePersonId = this.sendOut.join(',')
      }
      let params = {
        "id": this.id,
        "simLevel": '',
        "remark": this.remark,
        "levelUpPerson": this.$store.getters.getUserinfolist[0].LoginName,
        "noticePersonId": noticePersonId,
      }
      let res = await getSim3LevelUp(params)
      if (res.success) {
        this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
        localStorage.setItem('Sender', JSON.stringify(this.sendOut))
        this.$emit('closeup');
      }
    },
    //关闭
    closePopup() {
      this.$emit('closeup');
    }
  }
};
</script>

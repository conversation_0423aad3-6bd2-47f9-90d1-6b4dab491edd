<template>
    <div class="add-property">
        <a-form :model="form" :label-col="{ span: 6, }" :wrapper-col="{ span: 14, }">
            <a-form-item label="Name">
                <a-input v-model="form.Name" />
            </a-form-item>
            <a-form-item label="Description">
                <a-input v-model="form.Description" />
            </a-form-item>
            <a-form-item label="Type">
                <a-input v-model="form.Type" />
            </a-form-item>
            <a-form-item label="Data Type">
                <a-input v-model="form.DataType" />
            </a-form-item>
            <a-form-item label="Initial Value">
                <a-input v-model="form.InitialValue" />
            </a-form-item>
            <a-form-item label="Null Value">
                <a-input v-model="form.NullValue" />
            </a-form-item>
            <a-form-item label="Bound">
                <a-select show-search v-model="form.IsBound" placeholder="please select your bound">
                    <a-select-option value="1">是</a-select-option>
                    <a-select-option value="0">否</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="Subscription">
                <a-select show-search v-model="form.IsSubscribed" placeholder="please select your subscription">
                    <a-select-option value="1">是</a-select-option>
                    <a-select-option value="0">否</a-select-option>
                </a-select>
            </a-form-item>
            <!-- <a-form-item label="Type">
                <a-select v-model="form.ServerType" placeholder="please select your type">
                    <a-select-option value="General">General</a-select-option>
                </a-select>
            </a-form-item> -->
        </a-form>
    </div>
</template>

<script>
export default {
    props: {
        editItemObj: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            form: {
                Name: '',
                Description: '',
                Type: '',
                DataType: '',
                InitialValue: '',
                NullValue: '',
                IsBound: undefined,
                IsSubscribed: undefined
            }
        }
    },
    created() {
        if (this.editItemObj && this.editItemObj.ID) {
            for (const key in this.form) {
                this.form[key] = this.editItemObj[key]
            }
            this.form.ID = this.editItemObj.ID
        }
    }
}
</script>

<style lang="scss" scoped>
.ant-row.ant-form-item {
    margin-bottom: 10px;
}
</style>

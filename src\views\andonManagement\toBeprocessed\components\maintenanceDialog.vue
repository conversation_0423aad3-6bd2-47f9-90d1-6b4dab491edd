<template>
    <!-- 处理告警 -->
    <v-dialog v-model="dialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                <span style="color: #e84d4d">{{ $t('ANDON_BJJL.maintenance') }}</span>
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="mt-6 alarm-home-pdm">
                <v-row>
                        <!-- 工段 -->
                        <v-col :cols="12" class="activ-style activ-txt" :lg="12">
                            {{ $t('$vuetify.dataTable.ANDON_BJJL.ProductLineName') }}
                        </v-col>
                        <v-col :cols="12" class="activ-style activ-height activ-background" :lg="12">
                            <v-text-field class="white-bk" style="width: 50%" v-model="form.ProductLineName" disabled dense/>
                        </v-col>
                        <!-- 设备 -->
                        <v-col :cols="6" class="activ-style activ-txt alarm-bdmr" :lg="6">
                            {{ $t('$vuetify.dataTable.ANDON_BJJL.EquipmentName') }}
                        </v-col>
                        <!-- 维修人员 -->
                        <v-col :cols="6" class="activ-style activ-txt" :lg="6">
                            {{ $t('$vuetify.dataTable.ANDON_BJJL.repair_man') }}
                        </v-col>
                        <!-- 设备 -->
                        <v-col :cols="6" class="activ-style activ-height alarm-bdmr activ-background" :lg="6">
                            <v-text-field class="white-bk" v-model="form.EquipmentName" disabled dense/>
                        </v-col>
                        <!-- 维修人员 -->
                        <v-col :cols="6" :lg="6" class="activ-style activ-height activ-background">
                            <v-select
                                class="white-bk"
                                v-if="option.type=='maintenance'"
                                v-model="form.repair_man"
                                :rules="rules.repair_man"
                                :items="equipmentList"
                                item-text="EquipmentName"
                                clearable
                                item-value="ID"
                                dense
                                outlined
                            ></v-select>
                            <v-text-field v-else v-model="form.repair_man" class="white-bk" disabled dense/>
                        </v-col>
                        <!-- 故障代码 -->
                        <v-col :cols="6" class="activ-style activ-txt alarm-bdmr" :lg="6">
                            {{ $t('$vuetify.dataTable.ANDON_BJJL.ReasonCode') }}
                        </v-col>
                        <!-- 故障时间 -->
                        <v-col :cols="6" class="activ-style activ-txt" :lg="6">
                            {{ $t('$vuetify.dataTable.ANDON_BJJL.CreateDate') }}
                        </v-col>
                        <!-- 故障代码 -->
                        <v-col :cols="6" class="activ-style activ-height alarm-bdmr activ-background" :lg="6">
                            <v-text-field class="white-bk" v-model="form.ReasonCode" disabled dense/>
                        </v-col>
                        <!-- 故障时间 -->
                        <v-col :cols="6" :lg="6" class="activ-style activ-height activ-background">
                            <v-text-field class="white-bk" v-model="form.CreateDate" disabled dense/>
                        </v-col>
                        <!-- 描述   -->
                        <v-col :cols="12" :lg="12" class="activ-style activ-txt">
                            {{ $t('$vuetify.dataTable.ANDON_BJJL.AlarmContent') }}
                        </v-col>
                        <!-- 说明 -->
                        <v-col :cols="7" :lg="7" class="activ-style opear-message alarm-bdmr activ-background">
                            <v-textarea disabled class="white-bk" v-model="form.AlarmContent" rows="4" dense/>
                        </v-col>
                        <!-- 操作 -->
                        <v-col :cols="5" :lg="5" class="activ-style opear-message opear-btns activ-background">
                            <div class="white-bk" @click="closeForm">
                                <span class="iconfont icon-cuowu"></span>
                            </div>
                            <div class="agree-btn" @click="submitForm">
                                <span class="iconfont icon-zhengque"></span>
                            </div>
                        </v-col>
                    </v-row>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script>
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            equipmentList: [],
            valid: true,
            dialog: false,
            form: {
                ID: '',
                EquipmentName: '',
                ProductLineName: '',
                EquipmentCode: '',
                ReasonCode: '',
                AlarmContent: '',
                CreateDate: '',
                repair_man: ''
            },
            rules: {
                ProductLine	: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            },
            option: {}
        };
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal) {
                    console.log(this.operaObj);
                    for (const key in this.form) {
                        if (Object.hasOwnProperty.call(this.form, key)) {
                            this.form[key] = this.operaObj[key];
                        }
                    }
                    this.option = this.operaObj.detailObj
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        //
        closeForm() {
            this.dialog = false;
        },
        // 提交
        async submitForm() {
            const res = await this.option.fn({ id: this.operaObj.ID, repair_man: this.form.repair_man });
            const { success, msg } = res;
            if(success){
                this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                this.$emit('handlePopup', 'refresh');
                this.closeForm()
            }else{
                this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'error' });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.activ-txt{
    line-height: 6px;
}
.activ-style{
    border: 1px solid #bdbdbd;
    border-bottom: none;
}
.activ-height{
    height: 60px;
}
.alarm-message{
    height: 80px;
}
.opear-message{
    height: 130px;
    border-bottom: 1px solid #bdbdbd;
}
.opear-btns{
    display: flex;
    justify-content: space-around;
    align-items: center;
    div{
        cursor: pointer;
        border: 1px solid gainsboro;
        height: 100%;
        width: 40%;
        display: flex;
        justify-content: space-around;
        align-items: center;
    }
    .agree-btn{
        background: #f2c85d;
    }
}
.white-bk{
    background: #fff;
}
.alarm-bdmr{
    border-right: none;
}
// .activ-style:last-child{
//     border-bottom: 1px solid;
// }
.col-lg-6.col-12,
.col-lg-6.col-6,
.col-6,
.col-12
.col-lg-6,
.col-lg-12 {
    padding: 6x;
}
</style>
<style lang="scss">
.alarm-home-pdm{
    .v-text-field.v-text-field--enclosed .v-text-field__details{
        margin-bottom: 0;
    }
    .v-text-field__details{
        display: none;
    }
    .activ-background{
        background: #f5f5f5
    }
    .iconfont{
        font-size: 80px;
    }
    .v-input--dense > .v-input__control > .v-input__slot{
        margin-bottom: 1px;
    }
    legend{
        display: none;
    }
    .v-text-field--outlined fieldset{
        top: -1px;
    }
}
</style>
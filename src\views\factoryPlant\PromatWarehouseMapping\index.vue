<template>
  <div class="usemystyle PromatWarehouseMapping">
    <div class="InventorySearchBox">
      <div class="searchbox">
        <div class="inputformbox" v-for="(item, index) in searchlist" :key="index">
          <el-select
            v-if="item.type === 'select'"
            clearable
            filterable
            v-model="item.value"
            :placeholder="item.name"
          >
            <el-option
              v-for="(option, idx) in item.options"
              :key="idx"
              :label="option.name"
              :value="option.id"
            >
            </el-option>
          </el-select>
          <el-input
            v-if="item.type === 'input'"
            v-model="item.value"
            :placeholder="item.name"
          ></el-input>
        </div>
      </div>
      <div class="searchbox">
        <el-input
          class="quickSearchinput"
          :placeholder="$t('BatchPallets.QuickSearch')"
          v-model="QuickSearch"
        >
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <el-button
          style="margin-left: 5px"
          size="small"
          icon="el-icon-refresh"
          @click="getsearch()"
          >{{ this.$t('Inventory.refresh') }}</el-button
        >
        <el-button
          class="tablebtn"
          size="small"
          style="margin-left: 5px"
          icon="el-icon-plus"
          @click="handleAdd()"
          >{{ $t('GLOBAL._XZ') }}</el-button
        >
        <el-button
          class="tablebtn"
          size="small"
          style="margin-left: 5px"
          icon="el-icon-delete"
          :disabled="selectedRows.length === 0"
          @click="handleBatchDelete()"
          >{{ $t('GLOBAL._PLSC') }}</el-button
        >
      </div>
    </div>
    <div class="tablebox">
      <el-table
        :data="tableList"
        style="width: 100%"
        height="700"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column
          v-for="(item, index) in header"
          :key="index"
          :align="item.align"
          :prop="item.prop"
          :label="$t(`$vuetify.dataTable.PromatWarehouseMapping.${item.value}`)"
          :width="item.width"
        >
        </el-table-column>
        <el-table-column
          :label="$t('GLOBAL._ACTIONS')"
          align="center"
          width="150"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="handleEdit(scope.row)">{{
              $t('GLOBAL._BJ')
            }}</el-button>
            <el-button type="text" @click="handleDelete(scope.row)">{{
              $t('GLOBAL._SC')
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="paginationbox">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageOptions.page"
          :page-sizes="pageOptions.pageSizeitems"
          :page-size="pageOptions.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageOptions.total"
          background
        ></el-pagination>
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        :model="editForm"
        :rules="editFormRules"
        ref="editForm"
        label-width="120px"
      >
        <el-form-item :label="$t('$vuetify.dataTable.PromatWarehouseMapping.Area')" prop="AreaId">
          <el-select
            v-model="editForm.AreaId"
            filterable
            clearable
            :placeholder="$t('$vuetify.dataTable.PromatWarehouseMapping.PleaseSelectArea')"
            style="width: 100%"
            @change="handleAreaChange"
          >
            <el-option
              v-for="item in areaOptions"
              :key="item.ID"
              :label="`${item.EquipmentCode} ${item.EquipmentName}`"
              :value="item.ID"
            >
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item :label="$t('$vuetify.dataTable.PromatWarehouseMapping.Line')" prop="LineId">
          <el-select
            v-model="editForm.LineId"
            filterable
            clearable
            :placeholder="$t('$vuetify.dataTable.PromatWarehouseMapping.PleaseSelectLine')"
            style="width: 100%"
            @change="handleLineChange"
          >
            <el-option
              v-for="item in lineOptions"
              :key="item.ID"
              :label="`${item.EquipmentCode} ${item.EquipmentName}`"
              :value="item.ID"
            >
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item :label="$t('$vuetify.dataTable.PromatWarehouseMapping.Material')" prop="MaterialId">
          <el-select
            v-model="editForm.MaterialId"
            filterable
            clearable
            :placeholder="$t('$vuetify.dataTable.PromatWarehouseMapping.PleaseSelectMaterial')"
            style="width: 100%"
            @change="handleMaterialChange"
          >
            <el-option
              v-for="item in materialOptions"
              :key="item.ID"
              :label="`${item.Code} ${item.NAME}`"
              :value="item.ID"
            >
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item :label="$t('$vuetify.dataTable.PromatWarehouseMapping.WarehouseCode')" prop="WarehouseCode">
          <el-select
            v-model="editForm.WarehouseCode"
            filterable
            clearable
            :placeholder="$t('$vuetify.dataTable.PromatWarehouseMapping.PleaseSelectWarehouse')"
            style="width: 100%"
            @change="handleWarehouseChange"
          >
            <el-option
              v-for="item in warehouseOptions"
              :key="item.ID"
              :label="`${item.EquipmentCode} ${item.EquipmentName}`"
              :value="item.ID"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{
          $t('GLOBAL._QX')
        }}</el-button>
        <el-button type="primary" @click="submitForm">{{
          $t('GLOBAL._QD')
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';
import {
  getPageList,
  getList,
  getEntity,
  saveForm,
  deleteEntities,
  getAreaList,
  getLineList,
  getWarehouseList,
  getMaterialList
} from '@/api/factoryPlant/PromatWarehouseMapping.js';
import { Message, MessageBox } from 'element-ui';

export default {
  name: 'PromatWarehouseMapping',
  data() {
    return {
      QuickSearch: '',
      searchlist: [
        {
          type: 'select',
          name: this.$t('$vuetify.dataTable.PromatWarehouseMapping.Area'),
          id: 'AreaId',
          value: '',
          options: []
        },
        {
          type: 'select',
          name: this.$t('$vuetify.dataTable.PromatWarehouseMapping.Line'),
          id: 'LineId',
          value: '',
          options: []
        },
        {
          type: 'select',
          name: this.$t('$vuetify.dataTable.PromatWarehouseMapping.Material'),
          id: 'MaterialId',
          value: '',
          options: []
        },
        {
          type: 'select',
          name: this.$t('$vuetify.dataTable.PromatWarehouseMapping.WarehouseCode'),
          id: 'WarehouseCode',
          value: '',
          options: []
        }
      ],
      tableList: [],
      header: [
        { prop: 'AreaCode', value: 'AreaCode', width: '120' },
        { prop: 'AreaName', value: 'AreaName', width: '150' },
        { prop: 'LineCode', value: 'LineCode', width: '120' },
        { prop: 'LineName', value: 'LineName', width: '150' },
        { prop: 'MaterialCode', value: 'MaterialCode', width: '120' },
        { prop: 'MaterialName', value: 'MaterialName', width: '150' },
        { prop: 'WarehouseCode', value: 'WarehouseCode', width: '120' }
      ],
      tableId: 'PromatWarehouseMapping',
      pageOptions: {
        total: 0,
        page: 1,
        pageSize: 20,
        pageCount: 1,
        pageSizeitems: [10, 20, 50, 100, 500]
      },
      selectedRows: [],
      dialogVisible: false,
      dialogTitle: '',
      isEdit: false,
      editForm: {
        ID: '',
        AreaId: '',
        AreaCode: '',
        AreaName: '',
        LineId: '',
        LineCode: '',
        LineName: '',
        MaterialId: '',
        MaterialCode: '',
        MaterialName: '',
        WarehouseId: '',
        WarehouseCode: '',
        WarehouseName: ''
      },
      editFormRules: {
        AreaId: [
          { required: true, message: this.$t('$vuetify.dataTable.PromatWarehouseMapping.AreaRequired'), trigger: 'change' }
        ],
        LineId: [
          { required: true, message: this.$t('$vuetify.dataTable.PromatWarehouseMapping.LineRequired'), trigger: 'change' }
        ],
        MaterialId: [
          { required: true, message: this.$t('$vuetify.dataTable.PromatWarehouseMapping.MaterialRequired'), trigger: 'change' }
        ],
        WarehouseCode: [
          { required: true, message: this.$t('$vuetify.dataTable.PromatWarehouseMapping.WarehouseRequired'), trigger: 'change' }
        ]
      },
      areaOptions: [],
      lineOptions: [],
      materialOptions: [],
      warehouseOptions: []
    };
  },
  mounted() {
    this.getDataList();
    this.getAreaOptions();
    this.getLineOptions();
    this.getMaterialOptions();
    this.getWarehouseOptions();
  },
  methods: {
    // 获取数据列表
    async getDataList() {
      const params = {
        pageIndex: this.pageOptions.page,
        pageSize: this.pageOptions.pageSize,
        AreaId: this.searchlist[0].value,
        LineId: this.searchlist[1].value,
        MaterialId: this.searchlist[2].value,
        WarehouseCode: this.searchlist[3].value
      };

      try {
        const res = await getPageList(params);
        if (res.success) {
          this.tableList = res.response.data;
          this.pageOptions.total = res.response.dataCount;
        }
      } catch (error) {
        console.error(error);
      }
    },

    // 获取车间选项
    async getAreaOptions() {
      try {
        const res = await getAreaList();
        if (res.success) {
          this.areaOptions = res.response;
          this.searchlist[0].options = res.response.map(item => ({
            id: item.ID,
            name: `${item.EquipmentCode} ${item.EquipmentName}`
          }));
        }
      } catch (error) {
        console.error(error);
      }
    },

    // 获取产线选项
    async getLineOptions() {
      try {
        const res = await getLineList();
        if (res.success) {
          this.lineOptions = res.response;
          this.searchlist[1].options = res.response.map(item => ({
            id: item.ID,
            name: `${item.EquipmentCode} ${item.EquipmentName}`
          }));
        }
      } catch (error) {
        console.error(error);
      }
    },

    // 获取物料选项
    async getMaterialOptions() {
      try {
        const res = await getMaterialList({});
        if (res.success) {
          this.materialOptions = res.response;
          this.searchlist[2].options = res.response.map(item => ({
            id: item.ID,
            name: `${item.Code} ${item.NAME}`
          }));
        }
      } catch (error) {
        console.error(error);
      }
    },

    // 获取仓库选项
    async getWarehouseOptions() {
      try {
        const res = await getWarehouseList();
        if (res.success) {
          this.warehouseOptions = res.response;
          this.searchlist[3].options = res.response.map(item => ({
            id: item.ID,
            name: `${item.EquipmentCode} ${item.EquipmentName}`
          }));
        }
      } catch (error) {
        console.error(error);
      }
    },

    // 查询
    getsearch() {
      this.pageOptions.page = 1;
      this.getDataList();
    },

    // 重置查询
    getempty() {
      this.searchlist.forEach(item => {
        item.value = '';
      });
      this.pageOptions.page = 1;
      this.getDataList();
    },

    // 分页相关方法
    handleSizeChange(val) {
      this.pageOptions.pageSize = val;
      this.getDataList();
    },

    handleCurrentChange(val) {
      this.pageOptions.page = val;
      this.getDataList();
    },

    // 表格选择
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    // 新增
    handleAdd() {
      this.isEdit = false;
      this.dialogTitle = this.$t('$vuetify.dataTable.PromatWarehouseMapping.AddTitle');
      this.editForm = {
        ID: '',
        AreaId: '',
        AreaCode: '',
        AreaName: '',
        LineId: '',
        LineCode: '',
        LineName: '',
        MaterialId: '',
        MaterialCode: '',
        MaterialName: '',
        WarehouseId: '',
        WarehouseCode: '',
        WarehouseName: ''
      };
      this.dialogVisible = true;
    },

    // 编辑
    handleEdit(row) {
      this.isEdit = true;
      this.dialogTitle = this.$t('$vuetify.dataTable.PromatWarehouseMapping.EditTitle');
      this.editForm = { ...row };
      this.dialogVisible = true;
    },

    // 删除
    handleDelete(row) {
      MessageBox.confirm(
        this.$t('$vuetify.dataTable.PromatWarehouseMapping.ConfirmDelete'),
        this.$t('GLOBAL._TS'),
        {
          confirmButtonText: this.$t('GLOBAL._QD'),
          cancelButtonText: this.$t('GLOBAL._QX'),
          type: 'warning'
        }
      ).then(async () => {
        try {
          const res = await deleteEntities([row.ID]);
          if (res.success) {
            Message.success(this.$t('GLOBAL._SCCG'));
            this.getDataList();
          } else {
            Message.error(res.msg);
          }
        } catch (error) {
          console.error(error);
          Message.error(this.$t('GLOBAL._SCSB'));
        }
      });
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        Message.warning(this.$t('$vuetify.dataTable.PromatWarehouseMapping.PleaseSelectData'));
        return;
      }

      MessageBox.confirm(
        this.$t('$vuetify.dataTable.PromatWarehouseMapping.ConfirmBatchDelete'),
        this.$t('GLOBAL._TS'),
        {
          confirmButtonText: this.$t('GLOBAL._QD'),
          cancelButtonText: this.$t('GLOBAL._QX'),
          type: 'warning'
        }
      ).then(async () => {
        const ids = this.selectedRows.map(item => item.ID);
        try {
          const res = await deleteEntities(ids);
          if (res.success) {
            Message.success(this.$t('GLOBAL._SCCG'));
            this.selectedRows = [];
            this.getDataList();
          } else {
            Message.error(res.msg);
          }
        } catch (error) {
          console.error(error);
          Message.error(this.$t('GLOBAL._SCSB'));
        }
      });
    },

    // 提交表单
    submitForm() {
      this.$refs.editForm.validate(async valid => {
        if (valid) {
          try {
            const res = await saveForm(this.editForm);
            if (res.success) {
              Message.success(
                this.isEdit
                  ? this.$t('GLOBAL._XGCG')
                  : this.$t('GLOBAL._XZCG')
              );
              this.dialogVisible = false;
              this.getDataList();
            } else {
              Message.error(res.msg);
            }
          } catch (error) {
            console.error(error);
            Message.error(
              this.isEdit
                ? this.$t('GLOBAL._XGSB')
                : this.$t('GLOBAL._XZSB')
            );
          }
        }
      });
    },

    // 弹窗关闭
    handleDialogClose() {
      this.$refs.editForm.resetFields();
    },

    // 车间变化
    handleAreaChange(value) {
      // 根据选择的车间ID查找对应的车间信息
      const selectedArea = this.areaOptions.find(item => item.ID === value);
      if (selectedArea) {
        this.editForm.AreaId = selectedArea.ID;
        this.editForm.AreaCode = selectedArea.EquipmentCode;
        this.editForm.AreaName = selectedArea.EquipmentName;
      } else {
        this.editForm.AreaId = '';
        this.editForm.AreaCode = '';
        this.editForm.AreaName = '';
      }
    },

    // 产线变化
    handleLineChange(value) {
      // 根据选择的产线ID查找对应的产线信息
      const selectedLine = this.lineOptions.find(item => item.ID === value);
      if (selectedLine) {
        this.editForm.LineId = selectedLine.ID;
        this.editForm.LineCode = selectedLine.EquipmentCode;
        this.editForm.LineName = selectedLine.EquipmentName;
      } else {
        this.editForm.LineId = '';
        this.editForm.LineCode = '';
        this.editForm.LineName = '';
      }
    },

    // 物料变化
    handleMaterialChange(value) {
      // 根据选择的物料ID查找对应的物料信息
      const selectedMaterial = this.materialOptions.find(item => item.ID === value);
      if (selectedMaterial) {
        this.editForm.MaterialId = selectedMaterial.ID;
        this.editForm.MaterialCode = selectedMaterial.Code;
        this.editForm.MaterialName = selectedMaterial.NAME;
      } else {
        this.editForm.MaterialId = '';
        this.editForm.MaterialCode = '';
        this.editForm.MaterialName = '';
      }
    },

    // 仓库变化
    handleWarehouseChange(value) {
      // 根据选择的仓库ID查找对应的仓库信息
      const selectedWarehouse = this.warehouseOptions.find(item => item.ID === value);
      if (selectedWarehouse) {
        this.editForm.WarehouseId = selectedWarehouse.ID;
        this.editForm.WarehouseCode = selectedWarehouse.EquipmentCode;
        this.editForm.WarehouseName = selectedWarehouse.EquipmentName;
      } else {
        this.editForm.WarehouseId = '';
        this.editForm.WarehouseCode = '';
        this.editForm.WarehouseName = '';
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.PromatWarehouseMapping {
  .InventorySearchBox {
    .searchbox {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 10px;

      .inputformbox {
        width: 200px;
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }

    .quickSearchinput {
      width: 200px;
      margin-right: 10px;
    }
  }

  .tablebox {
    height: calc(100% - 120px);

    .paginationbox {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}
</style>
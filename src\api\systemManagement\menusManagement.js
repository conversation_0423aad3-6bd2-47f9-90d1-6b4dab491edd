import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url
//获取菜单列表数据
export function MenuGetPageList(data) {
    return request({
        url: baseURL + '/api/Menu/GetMenuTree',
        method: 'post',
        data
    });
}
//获取菜单Tree下拉列表数据
export function GetListByMenuType(data) {
    return request({
        url: baseURL + '/api/Menu/GetListByMenuType',
        method: 'post',
        data
    });
}
//新增&保存菜单
export function MenuSaveForm(data) {
    return request({
        url: baseURL + '/api/Menu/SaveForm',
        method: 'post',
        data
    });
}

// 删除菜单
export function MenuDelete(data) {
    return request({
        url: baseURL + '/api/Menu/Delete',
        method: 'post',
        data
    });
}

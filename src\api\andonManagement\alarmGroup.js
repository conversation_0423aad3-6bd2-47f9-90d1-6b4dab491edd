import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_ANDON'
// 接警组

//获取接警组列表
export function getAlarmgroups(data) {
    const api =  '/andon/Alarmgroup/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//分页获取接警组列表
export function getAlarmgroupList(data) {
    const api =  '/andon/Alarmgroup/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑接警组
export function AlarmgroupSaveForm(data) {
    const api =  '/andon/Alarmgroup/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除接警组
export function DeleteAlarmgroup(data) {
    const api =  '/andon/Alarmgroup/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

//获取接警组详情列表
export function getAlarmgroupPostList(data) {
    const api =  '/andon/AlarmgroupPost/GetList'
    return getRequestResources(baseURL, api, 'post', data, true);
}
//新增、编辑接警组详情
export function AlarmgroupPostSaveForm(data) {
    const api =  '/andon/AlarmgroupPost/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除接警组详情
export function DeleteAlarmgroupPost(data) {
    const api =  '/andon/AlarmgroupPost/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

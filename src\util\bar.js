import echarts from "echarts";

const windowW = document.documentElement.clientWidth;
const vw = windowW / 100;
// 获取线形，柱状echart配置
export const getChartOption = function (obj, seriesData, xData) {
    let xName = 'xAxis'
    let yName = 'yAxis'
    let newArr = []
    if(seriesData[0]){
        seriesData[0].forEach(e => {
            if (e) {
                newArr.push(e.value)
            }
        })
    }
    if (obj.isAntitone) {
        xName = 'yAxis'
        yName = 'xAxis'
    }
    const chartOption = {
        grid: {
            top: obj.gridTop ? obj.gridTop : 'auto',
            bottom: obj.gridBottom ? obj.gridBottom : 'auto',
            left: obj.gridLeft ? obj.gridLeft : '10%',
            right: obj.gridRight ? obj.gridRight : '15%',
        },
        title: {
            top: '5%',
            show: obj.title || false,
            text: obj.title,
            textStyle: {
                fontSize: obj.titleSize ? vw * obj.titleSize : vw * 0.6,
            },
            left: obj.titleLeft ? obj.titleLeft : 0,
        },
        legend: {
            top: obj && obj.legendTop ? obj.legendTop : 'auto',
            left: obj && obj.legendLeft ? obj.legendLeft : 'auto',
            right: obj && obj.legendRight ? obj.legendRight : 'auto',
            show: !!obj.isLegend,
            data: obj.legend,
            bottom: obj.legendBottom ? obj.legendBottom : '8%',
            itemWidth: obj.legendWidth ? vw * obj.legendWidth : vw * 0.6,
            itemHeight: obj.legendHeight ? vw * obj.legendHeight : vw * 0.6,
            textStyle: {
                color: obj.fontColor ? obj.fontColor : '#333',
                fontSize: obj.legendSize ? vw * obj.legendSize : vw * 0.8
            },
        },
        [xName]: {
            type: 'category',
            data: xData,
            axisLabel: {
                color: obj.fontColor ? obj.fontColor : '#333',
                fontSize: obj.xLabelSize ? vw * obj.xLabelSize : vw * 0.8,
                // X轴是否隔数据显示
                interval: obj.xLabelInterval ? obj.xLabelInterval : 0,
                // X轴文字旋转角度
                rotate: obj.axisLabelRotate ? obj.axisLabelRotate : 0
            },
        },
        [yName]: {
            type: 'value',
            splitLine: {
                lineStyle: {
                    type: 'dashed',
                    color: 'rgba(255,255,255,0.2)'
                }
            },
            axisLabel: {
                color: obj.fontColor ? obj.fontColor : '#333',
                fontSize: obj.xLabelSize ? vw * obj.xLabelSize : vw * 0.8,
            },
            minInterval: obj.minInterval ? obj.minInterval : 0
        },
        series: []
    }
    let minVal = (Math.min(...newArr) - 1).toFixed(2)
    let maxVal = (Math.max(...newArr) + 0).toFixed(2)
    // 设置最大值，最小值
    if (obj.isMin) {
        chartOption[yName].min = minVal <= 0 ? 0 : minVal
        chartOption[yName].max = minVal >= 100 ? 100 : maxVal
    }
    //垂直还是横向，默认横向
    if (obj.isOrient) {
        chartOption.legend.orient = 'vertical'
    }
    for (let i = 0; i < seriesData.length; i++) {
        const seriesObj = {
            name: obj.legend[i],
            data: seriesData[i],
            type: obj.lineType[i],
            barMaxWidth: obj.barMaxWidth ? vw * obj.barMaxWidth : vw * 1.6,
            // 设置线条圆点的大小
            symbolSize: obj.lineSymbolSize ? vw * obj.lineSymbolSize : vw * 0.6,
            shadowColor: '#333',
            shadowBlur: 6,
            // 是否显示柱状背景
            showBackground: !!obj.isBackground,
            backgroundStyle: {
                color: 'rgba(180, 180, 180, 0.2)'
            },
            areaStyle: {
                opacity: obj.areaStyle ? obj.areaStyle : 0
            },
            itemStyle:{

            },
            zlevel: obj.zlevel && obj.zlevel[i] ? obj.zlevel[i] : 0, // 设置多柱状或多线条的层级
            // yAxisIndex: obj.zlevel && obj.zlevel[i] ? obj.zlevel[i] : 0, // 设置多柱状或多线条的层级
            label: {
                color: obj.labelColor ? obj.labelColor : '#333',
                show: true,
                // label文字的位置
                position: obj.legendPosition && obj.legendPosition.length ? obj.legendPosition[i] : 'inside',
                // label文字的大小
                fontSize: obj.xLabelSize ? vw * obj.xLabelSize : vw * 0.8,
                textBorderColor: obj.textBorderColor ? obj.textBorderColor : 'transparent',
                textBorderWidth: obj.textBorderWidth ? Number(obj.textBorderWidth) : 0,
                rotate: obj.labelRotate
            }
        }
        if (obj&&obj.itemColor&&obj.itemColor.length) {
            seriesObj.itemStyle.color = obj.itemColor[i]
        }
        chartOption.series.push(seriesObj)
    }
    return chartOption
}
export const getPieChartOption = function (obj, seriesData) {
    const chartOption = {
        title: {
            show: obj.isTitle,
            text: obj.title ? obj.title : '',
            top: obj.titleTop ? obj.titleTop : 'center',
            left: obj.titleLeft ? obj.titleLeft : 'center',
            textStyle: {
                color: obj.fontColor ? obj.fontColor : '#fff',
                fontSize: obj.titleSize ? vw * obj.titleSize : vw * 1.2,
            }
        },
        series: [
            {
                name: 'Access From',
                type: 'pie',
                radius: obj.radius,
                label: {
                    show: false,
                    position: 'center'
                },
                labelLine: {
                    show: false
                },
                data: [
                    {
                        value: obj.values[0],
                        itemStyle: {color: 'rgba(200,200,255,0.1)', borderRadius: 60, borderWidth: 10}
                    },
                    {
                        value: obj.values[1],
                        itemStyle: {
                            // 0, 0, 0, 1 代表右/下/左/上
                            // offset 范围0-1 表示什么时候开始使用对应颜色
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: obj.color[0]}, // 开始位置
                                {offset: 1, color: obj.color[1]} // 结束位置
                            ])
                        }
                    },
                ]
            }
        ]
    }
    return chartOption
}

let data = [
    {
        name: '过量完成',
        children: [
            {
                name: '生产做多',
                value: 4,
            },
            {
                name: 'PMC要求',
                value: 2,
            },
            {
                name: '连续生产',
                value: 2,
            }
        ]
    },
    {
        name: '一次性完成',
        children: [
            {
                name: '一次性完成',
                value: 15,
            }
        ]
    },
    {
        name: '未完成',
        children: [
            {
                name: '其他不可抗力因素',
                value: 1,
            },
            {
                name: '原物料问题',
                value: 1,
            },
            {
                name: '制造供料不足',
                value: 2,
            },
            {
                name: '包装不良品多',
                value: 2,
            }
        ]
    }
];
export const getSunburstChartOption = function (obj, seriesData) {
   const option = {
       tooltip: {
           show:true
       },
       series: {
           type: 'sunburst',
           data: data,
           radius: ['25%', '90%'],
           label: {
               rotate: 'radial',
               formatter:function(a){
                   return a.name + '('+ a.value + ')'
               },
               minMargin:120,
               fontSize:vw*0.6
           },
           levels: [
               {
                   itemStyle: {
                       color:['#237cd6','#bd8e65','#6937d5']
                   },
               }
           ]
       }
    };
   return option
}
export const getPieChartOption2 = function (obj, seriesData) {
  let  option = {
        series: [
            {
                type: 'pie',
                radius: '80%',
                data: [
                    { value: 1048, name: '茄汁',sort:'第一' ,itemStyle: {color: '#129bff'}},
                    { value: 735, name: '酱料' ,sort:'第二',itemStyle: {color: '#0a68eb'}},
                    { value: 580, name: '耗油' ,sort:'第三',itemStyle: {color: '#0fc95d'}},
                ],
                label: {
                    alignTo: 'edge',
                    formatter: '{b}:\n{d}%',
                    minMargin: 65,
                    edgeDistance: 10,
                    lineHeight: 15,
                    color: '#fff',
                    fontSize: vw * 0.8
                },
            }
        ]
    };
  return option
}
export const getRadarChartOption = function (obj, seriesData) {
   let option = {
       tooltip: {
           show:true
       },
        legend: {
            data: ['每月计划金额', '每月累计金额'],
            textStyle: {
                color: '#fff'
            },
            right: '0%',
            top: '5%',
        },
        radar: {
            // shape: 'circle',
            indicator: [
                { name: '自然月人工费用', },
                { name: '自然月消耗品费用',  },
                { name: '自然月维修费用' },
                { name: '自然月动力耗用费用'},
                { name: '自然月折旧费用'},
                { name: '其他费用' }
            ],
            axisName: {
                formatter: '{part1|自然月}{part2|人工费用}',
                color: '#fff',
                rich: {
                    // 示例：设置特定文本样式
                    part1: {
                        color: 'red',
                    },
                    part2: {
                        color: 'blue',
                    }
                }
            },
            radius: '55%',
            center: ['38%', '52%'],
        },
        series: [
            {
                type: 'radar',
                label: {
                    show:false,
                },
                data: [
                    {
                        value: [4200, 3000, 20000, 35000, 50000, 18000],
                        name: '每月计划金额',
                        areaStyle: {
                            color: 'rgba(76, 150, 255, 0.3)'
                        },
                        itemStyle: {
                            color: '#4CA6FF'
                        },
                    },
                    {
                        value: [5000, 14000, 28000, 26000, 42000, 21000],
                        name: '每月累计金额',
                        areaStyle: {
                            color: 'rgba(88, 237, 95, 0.3)'
                        },
                        itemStyle: {
                            color: '#58ED5F'
                        }
                    }
                ]
            }
        ]
    };
   return option
}
export const getTwoChartOption = function (obj, seriesData, xData) {
    const colors = ['#5470C6', '#91CC75'];
   let option = {
        color: colors,
       grid: {
           left: '10%',
           right: '10%',
           top: '25%',
           bottom: '15%'
       },
        legend: {
            data: ['实际占比', '累计占比'],
            textStyle: {
                color: '#fff'
            },
            top:'3%'
        },
        xAxis: [
            {
                type: 'category',
                axisTick: {
                    alignWithLabel: true
                },
                axisLabel: {
                    color: '#fff',
                    interval: 0
                },
                // prettier-ignore
                data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '实际占比',
                position: 'right',
                alignTicks: true,
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: colors[0]
                    }
                },
                axisLabel: {
                    formatter: '{value} %',
                    interval: 0,
                    color: '#fff'
                },
                splitLine: {
                    show:false,
                    lineStyle: {
                        type: 'dashed',
                        color: 'rgba(255,255,255,0.2)'
                    }
                },
            },
            {
                type: 'value',
                name: '累计占比',
                position: 'left',
                alignTicks: true,
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: colors[1]
                    }
                },
                axisLabel: {
                    formatter: '{value} %',
                    color: '#fff'
                },
                splitLine: {
                    show:true,
                    lineStyle: {
                        type: 'dashed',
                        color: 'rgba(255,255,255,0.2)'
                    }
                },
            }
        ],
        series: [
            {
                name: '实际占比',
                type: 'bar',
                yAxisIndex: 0,
                data: [
                    2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
                ]
            },
            {
                name: '累计占比',
                type: 'line',
                yAxisIndex: 1,
                data: [2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2]
            }
        ]
    }
    return option
}

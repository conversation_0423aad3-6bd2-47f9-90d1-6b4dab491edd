// dictionary: true, isEditCell: true
export const productionLineWarehouseModeling = [
    { text: '序号', value: 'Index', width: '80px' },
    { text: '产线', value: 'AreaCode', width: '120px', dictionary: true },
    { text: '工段', value: 'LineCode', width: '180px', dictionary: true },
    { text: '目标料号', value: 'ProductCode', width: '160px' },
    { text: '原材料料仓', value: 'ProductWarehouseCode', width: '200px', dictionary: true },
    { text: '原材料暂存料仓', value: 'StagingWarehouseCode', width: '200px', dictionary: true },
    { text: 'MES产出虚拟仓', value: 'MesWarehouseCode', width: '200px', dictionary: true },
    // { text: 'MES产出虚拟仓编码', value: 'MesWarehouseCode', width: '160px' },
    { text: 'AGV产出下料库区', value: 'AGVBelongAreaName', width: '220px' },
    // { text: 'AGV下料库区编码', value: 'AGVBelongAreaCode', width: '220px' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '120px'
    }
];

# 2级x轴刻度坐标功能说明

## 功能概述

当返回结果的 `GroupField` 中包含 `"SpecificationCode"` 规格字段时，柱状图和折线图会自动将 `ChartData` 的结果转换成2级x轴刻度坐标，其中：
- 上层刻度：规格信息
- 下层刻度：日期信息

## 数据结构要求

### 输入数据格式

```javascript
// 示例数据结构
const chartData = {
  // GroupField 必须包含 SpecificationCode
  GroupField: ["SpecificationCode", "Date"],
  
  // x轴数据（日期）
  x: ["2024-01-01", "2024-01-02", "2024-01-03"],
  
  // 按规格分组的数据，键格式：规格_指标名称
  "规格A_产量": [100, 120, 110],
  "规格A_质量": [95, 98, 96],
  "规格B_产量": [80, 85, 90],
  "规格B_质量": [92, 94, 93],
  
  // 目标值数据（可选）
  "规格A_产量_目标值": [110, 110, 110],
  "规格B_产量_目标值": [85, 85, 85]
}
```

### 转换后的x轴标签格式

```
规格A        规格A        规格A        规格B        规格B        规格B
01-01       01-02       01-03       01-01       01-02       01-03
```

## 实现原理

### 1. 检测机制

组件会检查 `curShift.GroupField` 是否包含 `"SpecificationCode"`：

```javascript
const hasSpecification = this.curShift?.GroupField && 
  this.curShift.GroupField.includes('SpecificationCode')
```

### 2. x轴配置转换

当检测到规格字段时，会调用 `getTwoLevelXAxis()` 方法：

```javascript
if (hasSpecification) {
  return this.getTwoLevelXAxis()
} else {
  return this.getSingleLevelXAxis()
}
```

### 3. 数据转换

数据会通过 `transformDataForTwoLevelAxis()` 方法进行转换：

```javascript
data: hasSpecification ? 
  this.transformDataForTwoLevelAxis(key, this.curShift.ChartData[key]) : 
  this.curShift.ChartData[key]
```

## 支持的图表类型

- ✅ 柱状图 (barChart.vue)
- ✅ 折线图 (lineChart.vue)
- ✅ 柱状+折线图 (barLine.vue)
- 🔄 横向柱状图 (barTransverseChart.vue) - 待实现

## 使用示例

### 1. 基本用法

```vue
<template>
  <barChart
    :id1="'chart-1'"
    :title="'生产数据'"
    :Order="'sim-order-1'"
    :simlevel="'1.5'"
    :BaseTime="'2024-01-01'"
    :Dimension="dimensionList"
  />
</template>
```

### 2. 数据准备

确保后端返回的数据包含：
- `GroupField` 中有 `"SpecificationCode"`
- `ChartData` 中的键按 `规格_指标名称` 格式命名
- `x` 数组包含日期数据

### 3. 样式自定义

可以通过修改以下CSS来调整2级x轴的显示效果：

```css
.echarts-container .xAxis-label {
  font-size: 10px;
  line-height: 14px;
}
```

## 注意事项

1. **数据键格式**：必须按照 `规格_指标名称` 的格式命名数据键
2. **规格提取**：系统会自动从数据键中提取下划线前的部分作为规格
3. **日期格式**：支持 `YYYY-MM-DD` 格式的日期
4. **性能考虑**：大量规格和日期组合可能影响渲染性能
5. **兼容性**：当没有规格字段时，会自动回退到单级x轴模式

## 故障排除

### 问题1：2级x轴没有显示
- 检查 `GroupField` 是否包含 `"SpecificationCode"`
- 确认数据键格式是否正确（规格_指标名称）

### 问题2：数据显示不正确
- 检查数据键是否按规格正确分组
- 确认日期数组长度与数据数组长度一致

### 问题3：x轴标签重叠
- 调整图表容器宽度
- 修改 `axisLabel.fontSize` 和 `lineHeight` 参数

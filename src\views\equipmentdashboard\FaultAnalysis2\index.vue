<template>
    <div :class="isFull ? 'device-kanban-full' : 'device-kanban-not'">
        <dv-full-screen-container>
            <div class="sbkb">
                <div class="all">
                    <div class="title">
                        故障分析
                        <div class="searchbox" style="top: 10px">
                            <div class="dashboardinputbox" style="width: 270px">
                                <el-date-picker value-format="yyyy-MM-dd" v-model="time" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                            </div>
                        </div>
                        <div class="searchbox">
                            <div class="dashboardinputbox" v-for="(item, index) in inputlist" :key="index">
                                <el-input v-model="item.value" v-if="item.type == 'input'" :placeholder="item.name"></el-input>
                                <el-select clearable v-if="item.type == 'select'" v-model="item.value" filterable :placeholder="item.name">
                                    <el-option v-for="(it, ind) in item.option" :key="ind" :label="it.ItemName" :value="it.ItemValue"></el-option>
                                </el-select>
                            </div>
                            <div class="dashboardinputbox"><el-button icon="el-icon-search" size="mini" @click="search">查询</el-button></div>
                            <div class="nowtimebox">
                                {{ nowTime }}
                            </div>
                        </div>
                    </div>
                    <div class="tabbox">
                        <div class="tabboxrow">
                            <div class="tabboxbox" :style="{ width: item.width }" v-for="(item, index) in tablelist1" :key="index">
                                <div class="tabboxboxtitle">{{ item.title }}</div>
                                <div class="tabboxboxcenter" :id="item.id" v-loading="item.loading"></div>
                            </div>
                        </div>
                        <div class="tabboxrow">
                            <div class="tabboxbox" :style="{ width: item.width }" v-for="(item, index) in tablelist2" :key="index">
                                <div class="tabboxboxtitle">{{ item.title }}</div>
                                <div class="tabboxboxcenter" :id="item.id" v-loading="item.loading"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </dv-full-screen-container>
    </div>
</template>
<script>
import { DeviceChartGetFailureRate, DeviceChartGetReasonCategory, DeviceChartGetStopNature, DeviceChartGetBarChartMTTR, DeviceChartGetBarChartMTBF } from '@/api/equipmentManagement/FaultAnalysis2';
import { jdrwheader, byrwheader, dxrwheader, wxrwheader, jjrwheader } from '@/columns/equipmentdashboard/tableheader';
import { getbarstack } from '@/components/echarts/stackBar.js';
import { getlinebydata } from '@/components/echarts/Line.js';
import { getCircleBar } from '@/components/echarts/CircleBar.js';
import { getsimpleBar } from '@/components/echarts/simpleBar.js';

import moment from 'moment';
import '@/views/equipmentdashboard/style.scss';
export default {
    data() {
        return {
            isFull: false,
            myChart1: null,
            myChart2: null,
            myChart3: null,
            myChart4: null,
            myChart5: null,
            inputlist: [
                {
                    id: 'DeviceCode',
                    value: '',
                    type: 'input',
                    name: '设备编码'
                },
                {
                    id: 'DeviceCategroy',
                    value: '',
                    type: 'select',
                    option: [],
                    name: '设备类型'
                },
                {
                    id: 'Department',
                    type: 'input',
                    value: '',
                    name: '工作区'
                }
            ],
            time: [moment().startOf('week').format('YYYY-MM-DD'), moment().endOf('week').format('YYYY-MM-DD')],
            tablelist1: [
                {
                    title: '设备稳定性',
                    width: '33%',
                    id: 'chart1',
                    loading: true
                },
                {
                    title: '故障现象分析',
                    width: '33%',
                    id: 'chart2',
                    loading: true
                },
                {
                    title: '故障性质分析',
                    width: '33%',
                    id: 'chart3',
                    loading: true
                }
            ],
            tablelist2: [
                {
                    title: '平均修复时间(MTTR)',
                    width: '50%',
                    id: 'chart4',
                    loading: true
                },
                {
                    title: '平均修复时间(MTTR)',
                    width: '50%',
                    id: 'chart5',
                    loading: true
                }
            ],
            Timeinterval: null,
            nowTime: '',
            SearchParams: {}
        };
    },
    async mounted() {
        this.inputlist.forEach(item => {
            this.SearchParams[item.id] = item.value;
        });
        this.SearchParams.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
        this.SearchParams.queryStart = this.time[0];
        this.SearchParams.queryEnd =  this.time[1] + ' 23:59:59';
        this.inputlist[1].option = await this.$getNewDataDictionary('DeviceCategory');
        this.gettime();
        setTimeout(() => {
            this.getChart1();
            this.getChart2();
            this.getChart3();
            this.getChart4();
            this.getChart5();
            window.addEventListener('resize', this.handleResize);
        }, 500);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.myChart1) {
            this.myChart1.dispose(); // 清理图表实例
            this.myChart2.dispose(); // 清理图表实例
            this.myChart3.dispose(); // 清理图表实例
            this.myChart4.dispose(); // 清理图表实例
            this.myChart5.dispose(); // 清理图表实例
        }
        this.Timeinterval = null;
    },
    methods: {
        search() {
            if (this.time == null) {
                this.time = [];
            }
            this.inputlist.forEach(item => {
                this.SearchParams[item.id] = item.value;
            });
            this.SearchParams.queryStart = this.time[0];
            this.SearchParams.queryEnd =  this.time[1] + ' 23:59:59';
            this.getChart1();
            this.getChart2();
            this.getChart3();
            this.getChart4();
            this.getChart5();
        },
        gettime() {
            this.nowTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.Timeinterval = setInterval(() => {
                this.nowTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            }, 1000);
        },
        async getChart1() {
            let chartDom = document.getElementById('chart1');
            this.myChart1 = this.$echarts.init(chartDom);
            let res = await DeviceChartGetFailureRate(this.SearchParams);
            let data = res.response;
            let data1 = {
                xdata: data.categorydata,
                data: data.series[0].data
            };
            let option1 = getlinebydata(data1);
            this.myChart1.setOption(option1, true);
        },
        async getChart2() {
            let chartDom = document.getElementById('chart2');
            this.myChart2 = this.$echarts.init(chartDom);
            let res = await DeviceChartGetReasonCategory(this.SearchParams);
            let data = res.response.series;
            let option = getCircleBar(data);
            this.myChart2.setOption(option, true);
        },
        async getChart3() {
            let chartDom = document.getElementById('chart3');
            this.myChart3 = this.$echarts.init(chartDom);
            let res = await DeviceChartGetStopNature(this.SearchParams);
            let data = res.response.series;
            // let data = [
            //     { value: 4, name: '日常保养不到位' },
            //     { value: 6, name: '专业维修不到位' }
            // ];
            let option = getCircleBar(data);
            option.series[0].radius = ['40%', '70%'];
            this.myChart3.setOption(option, true);
        },
        async getChart4() {
            let chartDom = document.getElementById('chart4');
            this.myChart4 = this.$echarts.init(chartDom);
            let res = await DeviceChartGetBarChartMTTR(this.SearchParams);
            let data = res.response;
            let data1 = {
                xdata: data.categorydata,
                data: data.series[0].data
            };
            let option = getsimpleBar(data1, 'min', '#2EDDC8');
            this.myChart4.setOption(option, true);
        },
        async getChart5() {
            let chartDom = document.getElementById('chart5');
            this.myChart5 = this.$echarts.init(chartDom);
            let res = await DeviceChartGetBarChartMTBF(this.SearchParams);
            let data = res.response;
            let data1 = {
                xdata: data.categorydata,
                data: data.series[0].data
            };
            let option = getsimpleBar(data1, 'min', '#5EF02B');
            this.myChart5.setOption(option, true);
        },
        handleResize() {
            if (this.myChart1) {
                this.myChart1.resize();
                this.myChart2.resize();
                this.myChart3.resize();
                this.myChart4.resize();
                this.myChart5.resize();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.page_wrapper {
    padding: 0 !important;
}
</style>

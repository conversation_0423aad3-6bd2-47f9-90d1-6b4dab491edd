import { getRequestResources } from '@/api/fetch';
const baseURL_default = 'baseURL_MATERIAL'
const baseURL_concert = 'baseURL_30015'

//3/4冷库
export function GetSafetMsg(data) {
    const api = '/api/Safetytgt/GetSafetMsg'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
export function getColdInventoryView(data) {
    const api = '/api/ColdInventoryView/GetPageList'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
export function getDayOutStock(data) {
    const api = '/api/ColdInventoryView/DayOutStock'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
export function getColdQuantity(data) {
    const api = '/api/ColdInventoryView/GetColdQuantity'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
//原料任务
export function getGaveMaterialView(data) {
    const api = '/api/GaveMaterialView/GetListTop'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
export function getTaskWarnList(data) {
    const api = '/api/GaveMaterialView/GetPageList'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
export function getExpiredMaterialsList(data) {
    const api = '/api/GaveMaterialView/GetListDown'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
//包装看板
export function getProductivity(data) {
    const api = '/api/PackTechnologyView/GetProductivity'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
export function getPackTechnology(data) {
    const api = '/api/PackTechnologyView/GetPackTechnology'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
export function getCurrentWorkOrderInfo(data) {
    const api = '/api/PackTechnologyView/CurrentWorkOrderInfo'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
export function getNextOrderInfo(data) {
    const api = '/api/PackTechnologyView/NextOrderInfo'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
export function getDowntime(data) {
    const api = '/api/PackTechnologyView/GetDowntime'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
//备料
export function getMPOrder(data) {
    const api = '/api/MpreDayView/GetMPOrder'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
//备料看板
export function getMpreDayView(data) {
    const api = '/api/MpreDayView/Preprogress'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
//投料看板
export function getLineTipping(data) {
    const api = '/api/TippingEquipmentView/GetLineTipping'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
export function getTippingVersion(data) {
    const api = '/api/TippingEquipmentView/GetTippingVersion'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
export function getFeedingMes(data) {
    const api = '/api/TippingEquipmentView/GetFeedingMes'
    return getRequestResources(baseURL_concert, api, 'post', data);
}
//煮料
export function getSchedule(data) {
    const api = '/api/CookRecipeView/GetSchedule'
    return getRequestResources(baseURL_concert, api, 'post', data);
}

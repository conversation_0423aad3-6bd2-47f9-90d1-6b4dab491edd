<template>
    <v-dialog v-model="updateDialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ operaObj.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ') }}
                <v-icon @click="updateDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" class="mt-8">
                    <v-row>
                        <!-- 物料组 -->
                        <v-col :cols="12" :lg="12">
                            <v-select
                                v-model="form.Categorycode"
                                :rules="rules.Categorycode"
                                :items="materialGroupList"
                                item-text="Name"
                                item-value="Code"
                                :label="$t('$vuetify.dataTable.DFM_WLGXYS.Type')"
                                persistent-hint
                                dense
                                outlined
                            ></v-select>
                        </v-col>
                        <!-- 物料类别 -->
                        <v-col :cols="12" :lg="12">
                            <v-select
                                v-model="form.Type"
                                :rules="rules.Type"
                                :items="materialTypeList"
                                item-text="Name"
                                item-value="Code"
                                :label="$t('$vuetify.dataTable.DFM_WLGXYS.Categorycode')"
                                persistent-hint
                                dense
                                outlined
                            ></v-select>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn color="normal" @click="updateDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { MaterialTypeRelSaveForm } from '@/api/factoryPlant/materialRelationshipMapping.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        materialGroupList: {
            type: Array,
            default: () => []
        },
        materialTypeList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            checkbox: true,
            valid: true,
            updateDialog: false,
            form: {
                ID: '',
                Categorycode: '',
                Type: ''
            },
            rules: {
                Categorycode: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                Type: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            },
            parentCodeList: []
        };
    },
    watch: {
        updateDialog: {
            handler(curVal) {
                if(curVal) for (const key in this.form) {
                    if (Object.hasOwnProperty.call(this.form, key)) {
                        this.form[key] = this.operaObj[key];
                    }
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                const res = await MaterialTypeRelSaveForm({...this.form, ProcType: 2});
                const { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    this.updateDialog = !this.checkbox;
                    this.$emit('handlePopup', 'refresh'); 
                    this.getDataList();
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>

import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Inventory2'

export function GetTippingPageList(data) {
    const api = '/api/TippingPrecheck/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetScanContainerCode(data) {
    const api = '/api/TippingPrecheck/ScanContainerCode'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetTippingPrecheckViewList(data) {
    const api = '/api/TippingPrecheckView/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetTippingCount(data) {
    const api = '/api/TippingPrecheckView/GetCount'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetScanTraceCode(data) {
    const api = '/api/TippingPrecheckView/ScanTraceCode'
    return getRequestResources(baseURL, api, 'post', data);
}
//工艺长文本状态更新
export function UpdateMaterialProcessDataStatus(data) {
    const api = '/api/ProcessDataView/UpdateMaterialProcessDataStatus'
    return getRequestResources(baseURL, api, 'post', data);
}
// 工艺路线相关接口
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM'

//工艺路线模块列表
export function GetRoutingDetailList(data) {
    const api =  '/api/RoutingDetail/GetGraphList'
    return getRequestResources(baseURL, api, 'get', data);
}

//保存工艺路线模块详情
export function SaveAll(data) {
    const api =  '/api/RoutingDetail/SaveAll'
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取工艺路线模板列表
export function GetMatterList(data) {
    const api =  '/api/Process/GetList'
    return getRequestResources(baseURL, api, 'get', data);
}

// 获取模板主键ID
export function GetId(data) {
    const api =  '/api/RoutingDetail/GetId'
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取工艺路线列表
export function GetList(data) {
    const api =  '/api/RoutingHead/GetList'
    return getRequestResources(baseURL, api, 'get', data);
}

// 新建工艺路线信息
export function saveOperationalPath(data){
    const api =  '/api/RoutingHead/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}

//  删除路线
export function deleteOperationalPath(data){
    const api = '/api/RoutingHead/Delete'
    return getRequestResources(baseURL, api, 'post', data);
  }

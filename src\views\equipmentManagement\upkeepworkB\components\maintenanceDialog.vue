<template>
    <v-dialog v-model="updateDialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ `设备保养` }}
                <!-- {{ `设备保养--${operaObj.MaintainProject}` }} -->
                <v-icon @click="updateDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" class="mt-6">
                    <v-row class="divider-line mt-2">
                        <v-col v-if="!isBatchUpkeep" :cols="12" :lg="6">
                            <v-text-field v-model="form.MaintainValue" :rules="rules.MaintainValue" label="保养值" dense
                                outlined></v-text-field>
                        </v-col>
                        <v-col :cols="12" :lg="6">
                            <v-select :items="stateList" item-text="name" item-value="value" v-model="form.MaintainStatus"
                                :rules="rules.MaintainStatus" label="保养状态" dense outlined></v-select>
                        </v-col>
                        <v-col :cols="12" :lg="6">
                            <v-text-field disabled v-model="form.Executor" :rules="rules.MaintainValue" label="实际执行人" dense
                                outlined></v-text-field>
                        </v-col>
                        <!-- <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-autocomplete :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" disabled
                                v-model="form.ExecutorCode" :items="peopleitems" item-value="Code" item-text="Name" flat
                                outlined dense label="实际执行人">
                                <template #item="data">
                                    <template v-if="(typeof data.item) !== 'object'">
                                        <v-list-item-content v-text="data.item"></v-list-item-content>
                                    </template>
                                    <template v-else>
                                        <v-list-item-content>
                                            <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                            <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                        </v-list-item-content>
                                    </template>
                                </template>
                                <template v-slot:selection="{ item, index }">
                                    <span v-if="index === 0">{{ item.Name }}</span>
                                    <span v-if="index === 1">&nbsp;&nbsp;</span>
                                    <span v-if="index === 1" class="grey--text caption">(+{{ form.ExecutorCode.length -
                                        1 }} )</span>
                                </template>
                            </v-autocomplete>
                        </v-col> -->
                    </v-row>
                </v-form>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <span></span>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn color="normal" @click="updateDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { MaintainDetailSavesForm, MaintainDetailUpdate } from '@/api/equipmentManagement/upkeep.js';
import { StaffSiteGetList } from '@/api/peopleManagement/basicdata.js';
const stateList = [
    { name: '已完成', value: '1' },
    { name: '未完成', value: '0' }
]
export default {
    props: {
        isBatchUpkeep: {
            type: Boolean,
            default: false
        },
        operaObj: {
            type: Object,
            default: () => { }
        },
        selected: {
            type: Array,
            default: () => []
        },
        MaintainId: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            peopleitems: [],
            stateList,
            search: null,
            valid: true,
            updateDialog: false,
            form: {
                Executor: '',
                ExecutorCode: '',
                MaintainValue: '',
                MaintainStatus: '1',
            },
            rules: {
                MaintainStatus: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                MaintainValue: [v => this.form.MaintainValue == 0 || !!v || this.$t('GLOBAL._MANDATORY')],
            },
            MaterialList: []
        };
    },
    computed: {
        userInfo() {
            let userInfo = this.$store.state.auth.userinfolist[0];
            return userInfo;
        },
    },
    watch: {
        updateDialog: {
            handler(curVal) {
                if (curVal) {
                    this.$nextTick(() => {
                        this.$refs.form.reset();
                        this.queryPeoplelist()

                        setTimeout(() => {
                            this.form.ExecutorCode = this.userInfo.UserNo
                            this.form.Executor = this.userInfo.UserName
                            if (this.isBatchUpkeep) {
                                this.form.MaintainStatus = '1'
                                this.form.MaintainValue = ''
                            } else {
                                this.form.MaintainStatus = this.operaObj.MaintainStatus
                                this.form.MaintainValue = this.operaObj.MaintainValue
                            }

                        });
                    });
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        // 获取人员
        async queryPeoplelist() {
            const res = await StaffSiteGetList({ key: '' });
            let { success, response } = res;
            if (success) {
                this.peopleitems = response;
            }
        },
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                let params = {
                    MaintainId: this.MaintainId,
                    Id: this.isBatchUpkeep ? this.selected.map(item => item.ID).join(',') : this.operaObj.ID,
                    ...this.form,
                }
                const res = await MaintainDetailUpdate({ ...params });
                const { success, msg } = res;
                if (success) {
                    this.updateDialog = false;
                    this.$emit('handlePopup', 'refresh');
                }
                this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>

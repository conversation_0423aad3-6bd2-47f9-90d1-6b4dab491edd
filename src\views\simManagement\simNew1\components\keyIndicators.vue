<template>
  <div class="keyIndicatorsBox">
    <div
      class="titName"
      v-for="(item,index) in list"
      :key="index"
      @click="tabClick(item)"
    >
      <div class="titName_top">{{ item.KpiName }}</div>
      <div class="titName_bottom">0</div>
      <!-- {{ iitem.positionResult[0] }} -->
    </div>
    <keyIndicatorslist
      style="z-index: 9999;"
      ref="keyIndicatorsref"
      :keyTitle1="keyTitle"
    ></keyIndicatorslist>
  </div>
</template>
<script>
import { getqueryZ, getqueryLcr, getChartStructure, getTableList } from '@/api/simConfig/simconfignew.js';

export default {
  props: {
    Order: {
      type: String,
      default: ''
    }
  },
  components: {
    keyIndicatorslist: () => import('@/views/simManagement/simNew1/components/keyIndicatorslist.vue'),
  },
  data: () => ({
    keyTitle: "",
    list: [
      // { name: "GDS报警次数", num: 100 },
      // { name: "生产异常时间次数", num: 90 },
      // { name: "杜邦检查分数", num: 80 },
      // { name: "成品最终合格率", num: 80 },
      // { name: "GDS报警次数", num: 100 },
      // { name: "生产异常时间次数", num: 90 },
    ]
  }),
  created() {
    this.getBarList()
  },
  methods: {
    tabClick(item) {
      this.keyTitle = item.name
      setTimeout(() => {
        this.$refs.keyIndicatorsref.showDialog = true;
      })
    },
    async getBarList() {
      // {
      //   "Position": this.Order,
      //   "BaseTime": this.BaseTime,
      //   "TeamCode": this.TeamCode,
      //   "ProductionLineCode": this.ProductionLineCode,
      //   "FactoryCode": this.FactoryCode
      // }
      let params =
      {
        "simLevel": "SIM1",
        "position": [
          "A1180290603-SIM1-Table-2-0",
          "A1180290603-SIM1-Table-2-1",
          "A1180290603-SIM1-Table-2-2",
          "A1180290603-SIM1-Table-2-3",
          "A1180290603-SIM1-Table-2-4",
          "A1180290603-SIM1-Table-2-5",
          "A1180290603-SIM1-Table-2-6",
          "A1180290603-SIM1-Table-2-7",
          "A1180290603-SIM1-Table-2-8"
        ],
        "paramList": [
          "A1180290603",
          "2024-08-28"
        ],
        "pageIndex": 1
      }
      let { response } = await getTableList(params)
      this.list = response

    },

  },
}
</script>
<style lang="scss" scoped>
.keyIndicatorsBox {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding-top: 3%;
    box-sizing: border-box;
}
.titName {
    width: 30%;
    height: 40%;
    margin-top: 3%;
}
.titName_top,
.titName_bottom {
    width: 100%;
    height: 50%;
    font-size: 16px;
    color: #fff;
    text-align: center;
    font-weight: 600;
    background: linear-gradient(to right, #6b74e4, #4391f4);
    padding-top: 10%;
    box-sizing: border-box;
}
.titName_bottom {
    padding-top: 4%;
    font-size: 22px;
}
</style>
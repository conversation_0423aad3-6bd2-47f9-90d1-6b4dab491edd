import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TRACE; // 配置服务url
// 获取产线信息
export function GetFeedingProcDevice(data) {
    return request({
        url: baseURL + '/trace/Feeding/GetEquipmentDevice',
        method: 'post',
        data
    });
}
//等离子保存
export function PlasmaOperation(data) {
    return request({
        url: baseURL + '/trace/PlasmaRecord/PlasmaOperation',
        method: 'post',
        data
    });
}

//等离子列表
export function GetPageList(data) {
    return request({
        url: baseURL + '/trace/PlasmaRecord/GetPageList',
        method: 'post',
        data
    });
}



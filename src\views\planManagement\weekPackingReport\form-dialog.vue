<template>
    <el-dialog :title="$t('GLOBAL._BJ')" :visible.sync="dialogVisible" width="80%"
      :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
      @close="dialogVisible = false">
      <el-form ref="dialogForm" :model="dialogForm" label-width="130px">

          <el-col :lg="6">
            <el-form-item label="计划编号" prop="WeekScheduleOrderNo">
              <el-input v-model="dialogForm.WeekScheduleOrderNo" placeholder="计划编号" disabled />
            </el-form-item>            
          </el-col>

          <el-col :lg="6">
            <el-form-item label="产线" prop="LineCode">
              <el-input v-model="dialogForm.LineCode" placeholder="产线" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="6">
            <el-form-item label="工序" prop="SegmentCode">
              <el-input v-model="dialogForm.SegmentCode" placeholder="工序" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="6">
            <el-form-item label="工单号" prop="ProductionOrderNo">
              <el-input v-model="dialogForm.ProductionOrderNo" placeholder="工单号" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="6">
            <el-form-item label="产品代码" prop="MaterialCode">
              <el-input v-model="dialogForm.MaterialCode" placeholder="产品代码" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="6">
            <el-form-item label="产品名称" prop="MaterialName">
              <el-input v-model="dialogForm.MaterialName" placeholder="产品名称" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="6">
            <el-form-item label="计划开始时间" prop="PlanStartTime">
              <el-input v-model="dialogForm.PlanStartTime" placeholder="请输入计划开始时间" disabled />
            </el-form-item>
          </el-col>
          
          <el-col :lg="6">
            <el-form-item label="计划结束时间" prop="PlanEndTime">
              <el-input v-model="dialogForm.PlanEndTime" placeholder="请输入计划结束时间" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="6">
            <el-form-item label="计划数量" prop="PlanQty">
              <el-input v-model="dialogForm.PlanQty" placeholder="请输入计划数量" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="6">
            <el-form-item label="备注" prop="Remark">
              <el-input v-model="dialogForm.Remark" placeholder="请输入备注" disabled />
            </el-form-item>
          </el-col>

          <!-- <el-col :lg="24">
            <el-form-item label="" > -->
              <el-table class="mt-3"
                :height="200"
                border
                :data="tableData"
                style="width: 100%">
                <el-table-column prop="operation" width="100" :label="$t('GLOBAL._ACTIONS')" align="center">
                  <template slot-scope="scope">
                    <div class="combination">
                      <el-button size="mini" type="text" @click="showBomDetailDialog(scope.row)">{{ $t('GLOBAL._TD') }}</el-button>
                    </div>                        
                  </template>
                </el-table-column>
                <el-table-column v-for="(item) in tableName"
                                :default-sort="{prop: 'date', order: 'descending'}"
                                :key="item.ID"
                                :prop="item.field"
                                :label="item.label"
                                :width="item.width"
                                :align="item.alignType"
                                sortable
                                show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    {{ scope.row[item.field] }}
                  </template>
                </el-table-column>
              </el-table>
            <!-- </el-form-item>
          </el-col> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small" @click="submit()">保存</el-button>
      </div>      
      
    </el-dialog>
  </template>

<script>
  import {
    //getLineList,
    getSplitBatchInfo,
    updatelProductionOrder,
    getPOConsumeRequirementlList
  } from "@/api/planManagement/weekSchedule";
  import {getTableHead} from "@/util/dataDictionary.js";
  export default {
    components:{
      
    },
    data() {
      return {
        dialogForm: {},
        dialogVisible: false,
        formLoading: false,
        // factoryOptions: [],
        // workshopOptions: [],
        // lineOptions: [],        
        // categoryOptions: [],
        // shiftOptions: [],
        // typeOptions: [],
        standardPeriodTypeOptions: [],
        tableName : [],
        hansObjDrawer: this.$t('WeekFormulationDispatch.bomDetail'),
        tableOption: [
          {code: 'MaterialCode', width: 180, align: 'left'},
          {code: 'MaterialDescription', width: 280, align: 'left'},
          {code: 'Quantity', width: 100, align: 'right'},
          {code: 'Unit', width: 100, align: 'center'},
          {code: 'MaterialLotNo', width: 380, align: 'left'},
        ],
        tableData : [],
        currentRow: {},
        currentBomRow: {},
        matInfo:{}
      }
    },
    created() {
      this.initDictList();
      //this.getLineList();
    },
    mounted() {
    },
    methods: {
      async initDictList(){
        this.standardPeriodTypeOptions = await this.$getDataDictionary('StandardPeriodType');
      },
      // async getLineList() {
      //   const { response } = await getLineList({
      //    //areaCode: 'PackingArea'
      //    areaCode: 'Formulation'
      //   })
      //   console.log(response)
      //   this.lineOptions = response
      // },
      submit() {
        updatelProductionOrder(this.dialogForm).then(res=>{
          this.$message.success(res.msg)
          this.$emit('saveForm')
          this.dialogVisible = false
        })
      },
      show(data) {
        this.dialogForm = data
        this.currentRow = data
        this.dialogVisible = true
        this.initTableHead()
        this.$nextTick(_ => {
          if(data.ID){
            //console.log("show")
            this.getDialogInfo(data)
          }
        })
      },
      updateDialogInfo(){
        getPOConsumeRequirementlList(this.currentRow).then(res => {
          console.log(res);
          this.tableData = res.response.data
        })
      },
      initTableHead() {
        this.tableName = getTableHead(this.hansObjDrawer, this.tableOption)
      },
      getDialogInfo(data){
        //console.log("getDialogInfo")
        getPOConsumeRequirementlList(data).then(res => {
          console.log(res);
          this.tableData = res.response.data
        })
      },
    }
  }
  </script>
{"version": 3, "sources": ["../../src/util/dedupeModelListeners.ts"], "names": [], "mappings": ";;;;;;;AAEA;;;;;AAKG;AACW,SAAU,oBAAV,CAAgC,IAAhC,EAA+C;AAC3D,MAAI,IAAI,CAAC,KAAL,IAAc,IAAI,CAAC,EAAnB,IAAyB,IAAI,CAAC,EAAL,CAAQ,KAArC,EAA4C;AAC1C,QAAI,KAAK,CAAC,OAAN,CAAc,IAAI,CAAC,EAAL,CAAQ,KAAtB,CAAJ,EAAkC;AAChC,UAAM,CAAC,GAAG,IAAI,CAAC,EAAL,CAAQ,KAAR,CAAc,OAAd,CAAsB,IAAI,CAAC,KAAL,CAAW,QAAjC,CAAV;AACA,UAAI,CAAC,GAAG,CAAC,CAAT,EAAY,IAAI,CAAC,EAAL,CAAQ,KAAR,CAAc,MAAd,CAAqB,CAArB,EAAwB,CAAxB;AACb,KAHD,MAGO;AACL,aAAO,IAAI,CAAC,EAAL,CAAQ,KAAf;AACD;AACF;AACF", "sourcesContent": ["import { VNodeData } from 'vue'\n\n/**\n * Removes duplicate `@input` listeners when\n * using v-model with functional components\n *\n * @see https://github.com/vuetifyjs/vuetify/issues/4460\n */\nexport default function dedupeModelListeners (data: VNodeData): void {\n  if (data.model && data.on && data.on.input) {\n    if (Array.isArray(data.on.input)) {\n      const i = data.on.input.indexOf(data.model.callback)\n      if (i > -1) data.on.input.splice(i, 1)\n    } else {\n      delete data.on.input\n    }\n  }\n}\n"], "sourceRoot": "", "file": "dedupeModelListeners.js"}
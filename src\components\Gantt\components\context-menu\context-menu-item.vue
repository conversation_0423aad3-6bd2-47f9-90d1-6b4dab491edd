<template>
    <!--右键点击菜单  条目 -->
    <div class="context-menu-item" @click="doClick">
        {{ title }}
    </div>
</template>

<script>
export default {
    name: 'ContextMenuItem',
    props: {
        title: {
            type: String,
            default: ''
        }
    },
    data() {
        return {};
    },
    methods: {
        doClick() {
            this.$emit('click');
        }
    }
};
</script>

<style scoped lang="scss">
@import '../../common/css/global-property';
.context-menu-item {
    line-height: 38px;
    height: 38px;
    padding: 0 15px;
    cursor: pointer;
    &:hover {
        background-color: $tableRowActiveColor;
    }
}
</style>

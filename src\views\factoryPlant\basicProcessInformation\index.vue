// 工序基础信息
<template>
    <div class="basic-process-view">
        <div class="basic-process-main">
            <SearchForm class="mt-4" ref="contactTorm" :searchinput="searchinput" :show-from="showFrom"
                @searchForm="searchForm" />
            <v-card outlined>
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        <!-- 搜索栏 -->
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary">
                        <v-icon @click="getDataList">mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'GYJCXX_ADD'" @click="operaClick({})">{{ $t('GLOBAL._XZ') }}</v-btn>
                    <v-btn color="primary" v-has="'GYJCXX_ALLREMOVE'" @click="deleteItems()">{{ $t('GLOBAL._PLSC')
                    }}</v-btn>
                </div>
                <!-- <tree-table ref="treeTable" class="tb-cate" border stripe :is-fold="isfold" :data="desserts" :columns="headers" :expand-type="false" :selection-type="false">
                    <template slot="ProcType" slot-scope="scope">
                        {{ scope.row.ProcType | getName(processTypeList, 'ItemName', 'ItemValue') }}
                    </template>
                    <template slot="IsCreateOrder" slot-scope="scope">
                        {{ scope.row.IsCreateOrder | getName(isCreateOrderList, 'name', 'type') }}
                    </template>
                    <template slot="actions" slot-scope="scope">
                        <v-btn color="primary" text small class="mx-0 px-0" @click="tableClick(scope.row, 'edit')">{{ $t('GLOBAL._BJ') }}</v-btn>
                        <v-btn color="red" text small class="mx-0 px-0" @click="tableClick(scope.row, 'delete')">{{ $t('GLOBAL._SC') }}</v-btn>
                    </template>
                </tree-table> -->
                <Tables table-name="DFM_GYGL" :desserts="desserts" :loading="loading" :page-options="pageOptions"
                    :btn-list="btnList" :headers="headers"
                    :tableHeight="showFrom ? 'calc(100vh - 241px)' : 'calc(100vh - 175px)'" :dictionaryList="dictionaryList"
                    @selectePages="selectePages" @itemSelected="selectedItems" @toggleSelectAll="selectedItems"
                    @tableClick="tableClick"></Tables>
            </v-card>
            <update-dialog ref="updateDialog" :process-type-list="processTypeList" :opera-obj="operaObj"
                @handlePopup="handlePopup"></update-dialog>
        </div>
    </div>
</template>
<script>
import { GetPageList, DeleteProcess } from '@/api/factoryPlant/process.js';
import { processBasedColum } from '@/columns/factoryPlant/process.js';
export default {
    name: 'BasicProcessInformation',
    components: {
        UpdateDialog: () => import('./components/updateDialog.vue')
    },
    data() {
        return {
            isfold: true,
            operaObj: {},
            showFrom: false,
            deleteId: '',
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            headers: processBasedColum,
            selectedList: [],
            desserts: [],
            searchParams: {},
            MaterialCode: {},
            processTypeList: []
            // isCreateOrderList: [
            //     {
            //         type: '1',
            //         name: '是'
            //     },
            //     {
            //         type: '0',
            //         name: '否'
            //     }
            // ]
        };
    },
    computed: {
        //查询条件
        searchinput() {
            return [
                // 关键字
                {
                    key: 'key',
                    value: '',
                    icon: '',
                    label: this.$t('GLOBAL._KEY')
                },
                // 工序组
                {
                    key: 'parentId',
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.processTypeList, 'ItemValue', 'ItemName'),
                    value: '',
                    icon: '',
                    label: this.$t('$vuetify.dataTable.DFM_GYGL.ParentId')
                }
            ];
        },
        btnList() {
            return [
                { text: this.$t('GLOBAL._BJ'), icon: '', code: 'edit', type: 'primary', authCode: 'GYJCXX_EDIT' },
                { text: this.$t('GLOBAL._SC'), icon: '', code: 'delete', type: 'red', authCode: 'GYJCXX_DELETE' }
            ];
        },
        dictionaryList() {
            return [{ arr: this.processTypeList, key: 'ParentId', val: 'ItemValue', text: 'ItemName' }];
        }
    },
    mounted() {
        this.getDataList();
        this.initData();
    },
    methods: {
        async initData() {
            // 获取工序类型
            this.processTypeList = await this.$getDataDictionary('PROCESS');
        },
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        // 操作栏按钮
        tableClick(item, type) {
            switch (type) {
                // 编辑
                case 'edit':
                    this.operaClick(item);
                    break;
                // 删除
                case 'delete':
                    this.deleteId = item.ID;
                    this.sureDelete();
                    break;
                default:
                    break;
            }
        },
        // 获取全部表格数据
        async getDataList() {
            this.loading = true;
            let params = {
                ...this.searchParams,
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await GetPageList(params);
            const { success, response } = res || {};
            const { data, dataCount, page } = response || {};
            if (success) {
                this.desserts = data;
                this.pageOptions.total = dataCount;
                this.pageOptions.page = page;
            } else {
                this.desserts = [];
            }
            this.loading = false;
        },
        // 查询数据
        searchForm(v) {
            this.searchParams = v;
            this.getDataList();
        },
        // 新增/编辑基础工序
        operaClick(o) {
            this.operaObj = o;
            this.$refs.updateDialog.updateDialog = true;
        },
        // 批量删除
        async deleteItems() {
            if (this.selectedList.length > 0) {
                this.deleteId = '';
                this.sureDelete();
            } else {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'error' });
            }
        },
        // 确认删除
        sureDelete() {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            }).then(async () => {
                const params = [];
                if (this.deleteId) {
                    params.push(this.deleteId);
                } else {
                    this.selectedList.forEach(e => {
                        params.push(e.ID);
                    });
                }
                const res = await DeleteProcess(params);
                this.selectedList = [];
                this.deleteId = '';
                const { success, msg } = res;
                if (success) {
                    this.pageOptions.pageCount = 1;
                    this.getDataList();
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                }
            });
        },
        // 根据子组件返回来值
        handlePopup() {
            this.getDataList();
        }
    }
};
</script>
<style lang="scss" scoped>
.basic-process-view {
    display: flex;

    .basic-process-main {
        width: 100%;
        overflow: hidden;
    }
}
</style>

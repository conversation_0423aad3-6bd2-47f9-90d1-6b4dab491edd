<template>
    <div>
        <div class="form-btn-list">
            <v-btn color="primary" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ') }}</v-btn>
            <v-btn color="primary" :disabled="!SqFlag" @click="btnClickEvet('sq')">{{ $t('GLOBAL._shenqing') }}</v-btn>
            <v-btn color="primary" :disabled="!deleteList.length" @click="btnClickEvet('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn>
        </div>
        <Tables
            ref="recordTable"
            :page-options="pageOptions"
            :footer="false"
            :loading="loading"
            :btn-list="btnList"
            tableHeight="calc(100vh - 220px)"
            table-name="TPM_SBGL_SBBYJH_XQ"
            :headers="keepListBjBColum"
            :desserts="desserts"
            @tableClick="tableClick"
            @itemSelected="SelectedItems"
            @toggleSelectAll="SelectedItems"
        ></Tables>
        <BjcreateRepast ref="BjcreateRepast" @loadData="loadData3" :Wo="rowtableItem.Wo" :dialogType="dialogType" :tableItem="tableItem" :rowtableItem="rowtableItem"></BjcreateRepast>
        <el-dialog :title="$t('GLOBAL._SQBJ')" :visible.sync="RequestModel" width="30%">
            <div class="addForm">
                <v-text-field v-model="user" disabled outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.Creator')"></v-text-field>
            </div>
            <div class="addForm">
                <v-text-field v-model="userDate" :clearable="true" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.CreateDate')" readonly></v-text-field>
                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="userDate" type="datetime" :placeholder="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.CreateDate')"></el-date-picker>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="RequestModel = false">取 消</el-button>
                <el-button type="primary" @click="RequestSave()">确 定</el-button>
            </span>
        </el-dialog>
        <!-- <el-dialog :title="dialogType == 'add' ? $t('GLOBAL._XZ') : $t('GLOBAL._BJ')" :visible.sync="addModel" width="30%">
            <div class="addForm" v-for="(item, index) in SaveFormList" :key="index">
                <v-text-field v-if="item.type == 'input'" :id="item.id + 'SbxxList'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addModel = false">取 消</el-button>
                <el-button type="primary" @click="SaveFormSave()">确 定</el-button>
            </span>
        </el-dialog> -->
    </div>
</template>

<script>
import moment from 'moment';
import { keepListBjBColum } from '@/columns/equipmentManagement/upkeep.js';
import { GetPartsHistoryDetailPageList, GetPartsHistoryDetailDelete, GetPartsHistoryDetailRequest, GetPartsHistoryDetailSaveForm } from '@/api/equipmentManagement/NewRepair.js';
export default {
    components: {
        BjcreateRepast: () => import('./BjcreateRepast.vue')
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: ''
                }
            ];
        }
    },
    data() {
        return {
            keepListBjBColum,
            loading: false,
            desserts: [],
            PartOutstockStatus: [],
            deleteList: [],
            tableItem: {},
            dialogType: '',
            SqFlag: false,
            rowtableItem: {},
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            RequestModel: false,
            user: this.$store.getters.getUserinfolist[0].LoginName,
            userDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        };
    },
    async created() {
        this.PartOutstockStatus = await this.$getNewDataDictionary('PartOutstockStatus');
    },
    methods: {
        loadData3() {
            this.MyGetPartsHistoryDetailPageList();
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'sq':
                    this.tableItem = {};
                    this.Request();
                    return;
                case 'add':
                    this.dialogType = 'add';
                    this.$refs.BjcreateRepast.Code = '';
                    this.$refs.BjcreateRepast.Name = '';
                    this.$refs.BjcreateRepast.Listform.Qty = '';
                    this.$refs.BjcreateRepast.Model = '';
                    this.$refs.BjcreateRepast.material = [];
                    this.$refs.BjcreateRepast.materialList = [];
                    this.$refs.BjcreateRepast.showDialog = true;
                    this.$refs.BjcreateRepast.getData();
                    return;
                case 'delete':
                    this.bjDelect();
                    // this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'warning' });
                    return;
            }
        },
        Request() {
            this.user = this.$store.getters.getUserinfolist[0].LoginName;
            this.userDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.RequestModel = true;
        },
        async RequestSave() {
            this.deleteList.forEach(item => {
                item.DeviceId = this.rowtableItem.DeviceId;
                item.Requester = this.user;
                item.RequestDate = this.userDate;
            });
            let res = await GetPartsHistoryDetailRequest(this.deleteList);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.MyGetPartsHistoryDetailPageList();
                this.RequestModel = false;
            }
        },
        async bjDelect() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await GetPartsHistoryDetailDelete(params);
                    if (res.success) {
                        this.tableItem = {};
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.MyGetPartsHistoryDetailPageList();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.SaveFormList.forEach(it => {
                        for (let k in item) {
                            if (it.id == k) {
                                it.value = item[k];
                            }
                        }
                    });
                    this.addModel = true;
                    return;
                case 'delete':
                    this.bjDelect();
                    return;
            }
        },
        async MyGetPartsHistoryDetailPageList(row) {
            console.log(row);
            if (row) {
                this.rowtableItem = row;
            }
            let params = {
                ReferNo: this.rowtableItem.RepairWo,
                Factory: this.$route.query.Factory ? this.$route.query.Factory : '2010'
            };
            this.loading = true;
            let res = await GetPartsHistoryDetailPageList(params);
            let { response } = res;
            response.forEach(item => {
                this.PartOutstockStatus.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
            });
            this.desserts = res.response;
            this.loading = false;
        },
        // 删除列表
        SelectedItems(item) {
            this.deleteList = [...item];
            if (this.deleteList.length == 0) {
                this.SqFlag = false;
            } else {
                this.SqFlag = this.deleteList.every(item => {
                    return item.Status == '未申请';
                });
            }
        }
    }
};
</script>

<style></style>

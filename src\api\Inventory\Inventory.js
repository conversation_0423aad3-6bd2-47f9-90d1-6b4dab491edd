import { getRequestResources } from '@/api/fetch';
import store from '@/store';
import request from '@/util/request';

const baseURL = 'baseURL_Inventory';
const baseURL2 = 'baseURL_MATERIAL';

//table数据
export function GetPageInVentList(data) {
    const api = '/api/MaterialInventory/GetPageInVentList';
    return getRequestResources(baseURL, api, 'post', data);
}
//原料
export function GetPageList_Partial(data) {
    const api = '/api/InventorylistingView/GetPageList_Partial';
    return getRequestResources(baseURL, api, 'post', data);
}
//原料
export function GetInventList_YL(data) {
    const api = '/api/InventorylistingView/GetInventList_YL';
    return getRequestResources(baseURL, api, 'post', data);
}
//物料
export function GetInventList_WL(data) {
    const api = '/api/InventorylistingView/GetInventList_WL';
    return getRequestResources(baseURL, api, 'post', data);
}
export function getLotStatus(data) {
    // 检查缓存是否存在且未过期（1小时）
    const cachedData = store.getters['dictionary/getDictionaryByCode']('LotStatus');
    const lastUpdate = store.getters['dictionary/getLastUpdateTime'];
    const oneHour = 24 * 60 * 60 * 1000;
    
    if (cachedData && lastUpdate && (Date.now() - lastUpdate < oneHour)) {
        return Promise.resolve({ response: cachedData });
    }
    
    // 缓存不存在或已过期，从API获取
    const api = '/api/DataItemDetail/GetList?itemCode=LotStatus';
    return getRequestResources(baseURL2, api, 'post', data).then(resp => {
        // 更新缓存
        store.dispatch('dictionary/saveDictionary', {
            code: 'LotStatus',
            data: resp.response
        });
        return resp;
    });
}

export function getSublotStatus(data, itemCode) {
    // 检查缓存是否存在且未过期（1小时）
    const cachedData = store.getters['dictionary/getDictionaryByCode'](itemCode);
    const lastUpdate = store.getters['dictionary/getLastUpdateTime'];
    const oneHour = 24 *60 * 60 * 1000;
    
    if (cachedData && lastUpdate && (Date.now() - lastUpdate < oneHour)) {
        return Promise.resolve({ response: cachedData });
    }
    
    // 缓存不存在或已过期，从API获取
    const api = `/api/DataItemDetail/GetList?itemCode=${itemCode}`;
    return getRequestResources(baseURL2, api, 'post', data).then(resp => {
        // 更新缓存
        store.dispatch('dictionary/saveDictionary', {
            code: itemCode,
            data: resp.response
        });
        return resp;
    });
}
export function GetDestinationList(data) {
    const api = '/api/Container/GetDestinationList';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMyDestinationList_YL(data) {
    const api = '/api/Container/GetDestinationList_YL';
    return getRequestResources(baseURL, api, 'post', data);
}
export function InventoryChangeUnit(data) {
    const api = '/api/Container/ChangeUnit';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMyDestinationList_CYC(data) {
    const api = '/api/Container/GetDestinationList_CYC';
    return getRequestResources(baseURL, api, 'post', data);
}
export function TransferContainer(data) {
    const api = '/api/Container/TransferContainer';
    return getRequestResources(baseURL, api, 'post', data);
}
export function TransferContainerWL(data) {
    const api = '/api/Container/TransferContainer_WL';
    return getRequestResources(baseURL, api, 'post', data);
}
export function BlockInventory(data) {
    const api = '/api/MaterialInventory/BlockInventory';
    return getRequestResources(baseURL, api, 'post', data, true);
}
export function UBlockInventory(data) {
    const api = '/api/MaterialInventory/UBlockInventory';
    return getRequestResources(baseURL, api, 'post', data, true);
}
export function DeleteInventory(data) {
    const api = '/api/MaterialInventory/DeleteInventory';
    return getRequestResources(baseURL, api, 'post', data);
}
export function UnbindInventory(data) {
    const api = '/api/MaterialInventory/UnbindInventory';
    return getRequestResources(baseURL, api, 'post', data);
}
export function ChangeTimesInventory(data) {
    const api = '/api/MaterialInventory/ChangeTimesInventory';
    return getRequestResources(baseURL, api, 'post', data, true);
}

export function DeliveryInventory(data) {
    const api = '/api/MaterialInventory/DeliveryInventory';
    return getRequestResources(baseURL, api, 'post', data);
}

export function PrintInventLable(data) {
    const api = '/api/MaterialInventory/PrintInventLable';
    return getRequestResources(baseURL, api, 'post', data);
}
export function PrintInventLableAll(data) {
    const api = '/api/MaterialInventory/PrintInventLableAll';
    return getRequestResources(baseURL, api, 'post', data);
}
export function PrintMinLableALL(data) {
    const api = '/api/MaterialInventory/PrintMinLableALL';
    return getRequestResources(baseURL, api, 'post', data);
}
export function ChangeRemarkInventory(data) {
    const api = '/api/MaterialInventory/ChangeRemarkInventory';
    return getRequestResources(baseURL, api, 'post', data);
}
export function ChangeQuantityInventory(data) {
    const api = '/api/MaterialInventory/ChangeQuantityInventory';
    return getRequestResources(baseURL, api, 'post', data, true);
}
export function ToValidata(data) {
    const api = '/api/MaterialInventory/Validata';
    return getRequestResources(baseURL, api, 'post', data);
}
export function AddInventory(data) {
    const api = '/api/MaterialInventory/NewAddInventory';
    return getRequestResources(baseURL, api, 'post', data);
}
export function NewAddInventoryByTotal(data) {
    const api = '/api/MaterialInventory/NewAddInventoryByTotal';
    return getRequestResources(baseURL, api, 'post', data);
}
export function NewAddInvenCYC(data) {
    const api = '/api/MaterialInventory/NewAddInvenCYC';
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetContainerSelectList() {
    const api = '/api/MaterialInventory/GetContainerSelectList';
    return getRequestResources(baseURL, api, 'get');
}
export function GetMaterialSelectList(data) {
    const api = '/api/MaterialInventory/GetMaterialSelectList';
    return getRequestResources(baseURL, api, 'get');
}
export function GetUniqueNumber(data) {
    const api = '/api/BaseUniqueNumber/GetUniqueNumber';
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetMergeList(data) {
    const api = '/api/MaterialInventory/GetInventoryMergeOK';
    return getRequestResources(baseURL, api, 'post', data);
}
export function InventoryMerges(data) {
    const api = '/api/MaterialInventory/InventoryMerges';
    return getRequestResources(baseURL, api, 'post', data);
}
export function InventorySplit(data) {
    const api = '/api/MaterialInventory/InventorySplit';
    return getRequestResources(baseURL, api, 'post', data);
}
export function CreateWMSLable(data) {
    const api = '/api/MaterialInventory/CreateWMSLable';
    return getRequestResources(baseURL, api, 'post', data);
}

export function ScrapInventory(data) {
    const api = '/api/Container/ScrapInventory';
    return getRequestResources(baseURL, api, 'post', data);
}

export function ReturnInventory(data) {
    const api = '/api/Container/ReturnInventory';
    return getRequestResources(baseURL, api, 'post', data);
}
export function CherkReturnData(data) {
    const api = '/api/Container/CherkReturnData';
    return getRequestResources(baseURL, api, 'post', data);
}
export function ReturnInventoryALL(data) {
    const api = '/api/Container/ReturnInventoryALL';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetEquipmentSap(data) {
    const api = '/api/MaterialInventory/GetEquipmentSap';
    return getRequestResources(baseURL, api, 'get', data);
}

export function GetMaterialType(data) {
    const api = '/api/Category/GetList?Identities=MaterialType';
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetMaterialSelectListClass(data) {
    const api = '/api/MaterialInventory/GetMaterialSelectListClass';
    return getRequestResources(baseURL, api, 'get', data);
}
export function GetMSelectListClass(data) {
    const api = '/api/MaterialInventory/GetMSelectListByClass'
    return getRequestResources(baseURL, api, 'get', data);
}
export function GetUnitList(data) {
    const api = '/api/Unitmanage/GetList';
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetInventoryWeightEntityByID(data) {
    const api = '/api/MaterialInventory/GetWeightEntityByID';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetArrByGroups(data) {
    const api = '/api/MaterialInventory/GetInvnetGroup';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetArrByGroups2(data) {
    const api = '/api/Container/MergePDFData';
    return getRequestResources(baseURL, api, 'post', data);
}

export function ClearLineByEquipment(data) {
    const api = '/api/Container/ClearLineByEquipment';
    return getRequestResources(baseURL, api, 'post', data);
}

// export function ExportInventData(data) {
//     const api = '/api/MaterialInventory/ExportInventData'
//     return getRequestResources(baseURL, api, 'post', data);
// }

// 设备台账导出
export function ExportInventData(url, data) {
    return request({
        url: url,
        method: 'post',
        data,
        responseType: 'blob'
    });
    // return request({
    //     url: '/api/MaterialInventory/ExportInventData',
    //     method: 'post',
    //     data,
    //     responseType: 'blob'
    // })
}
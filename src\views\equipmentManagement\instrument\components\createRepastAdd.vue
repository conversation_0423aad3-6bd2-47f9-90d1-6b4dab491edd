<template>
    <v-dialog v-model="showDialog" max-width="1080px">
        <v-card>
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ dialogType == 'add' ? $t('GLOBAL._XZ') : $t('GLOBAL._BJ') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" :sm="item.sm ? item.sm : 3" :md="item.sm ? item.sm : 3" v-for="(item, index) in AddList" :key="index">
                            <v-text-field v-if="item.type == 'input'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                            <v-autocomplete
                                v-if="item.type == 'select'"
                                clearable
                                v-model="item.value"
                                :items="item.options"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="item.label"
                                clear
                                dense
                                outlined
                            ></v-autocomplete>
                            <v-menu
                                v-if="item.type == 'date' || item.type == 'datetime'"
                                :ref="'menu' + index"
                                v-model="menu[index]"
                                :close-on-content-click="false"
                                :nudge-right="40"
                                transition="scale-transition"
                                offset-y
                                max-width="290px"
                                min-width="290px"
                            >
                                <template #activator="{ on, attrs }">
                                    <v-text-field
                                        v-model="item.value"
                                        :clearable="item.isClearable ? item.isClearable : true"
                                        outlined
                                        dense
                                        :label="item.label"
                                        readonly
                                        v-bind="attrs"
                                        v-on="on"
                                    ></v-text-field>
                                </template>
                                <v-date-picker :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></v-date-picker>
                            </v-menu>
                            <div class="textfieldbox">
                                <v-text-field
                                    v-model="item.value"
                                    :clearable="item.isClearable ? item.isClearable : true"
                                    outlined
                                    dense
                                    v-if="item.type == 'time'"
                                    :label="item.label"
                                    readonly
                                ></v-text-field>
                                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-if="item.type == 'time'" v-model="item.value" type="datetime" :placeholder="item.label"></el-date-picker>
                            </div>
                            <div class="textfieldbox">
                                <v-text-field
                                    v-model="item.value"
                                    :clearable="item.isClearable ? item.isClearable : true"
                                    outlined
                                    dense
                                    v-if="item.type == 'daterange'"
                                    :label="item.label"
                                    readonly
                                ></v-text-field>
                                <el-date-picker
                                    v-model="item.value"
                                    value-format="yyyy-MM-dd"
                                    format="yyyy-MM-dd"
                                    v-if="item.type == 'daterange'"
                                    type="daterange"
                                    range-separator="至"
                                    :start-placeholder="$t('DFM_RL.DFM_RL')"
                                    end-placeholder="$t('DFM_RL._JSRQ')"
                                ></el-date-picker>
                            </div>

                            <el-radio-group v-model="item.value" v-if="item.type == 'radio'">
                                <div class="textlabel">{{ item.label }}:</div>
                                <el-radio :label="it.value" :key="ind" v-for="(it, ind) in item.radiolist">{{ it.label }}</el-radio>
                            </el-radio-group>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { GetMeasureAccountSaveForm } from '@/api/equipmentManagement/instrument.js';
import { fwcgdownColum } from '@/columns/equipmentManagement/Repair.js';
import { Message, MessageBox } from 'element-ui';

export default {
    components: {},
    props: {
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        },
        VerifyCategory: {
            type: Array,
            default: () => []
        },
        VerifyMethod: {
            type: Array,
            default: () => []
        },
        MeasureType: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            fwcgdownColum,
            loading2: false,
            desserts2: [],
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            spjg: '',
            menu: [],
            FileList: [],
            AddList: [
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.bh'),
                    value: '',
                    sm: 4,
                    id: 'MeasureNo',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.sbmc'),
                    value: '',
                    sm: 4,
                    id: 'Name',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.xh'),
                    require: true,
                    value: '',
                    sm: 4,
                    id: 'Model',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.gg'),
                    value: '',
                    sm: 4,
                    id: 'Spec',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.abcfl'),
                    value: '',
                    sm: 4,
                    id: 'Type',
                    type: 'select',
                    options: this.MeasureType
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.lb'),
                    value: '',
                    sm: 4,
                    id: 'Category',
                    type: 'select',
                    options: this.VerifyCategory
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.yxq'),
                    value: '',
                    sm: 4,
                    id: 'ExpirationDate',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.clfw'),
                    value: '',
                    sm: 4,
                    id: 'MeasureRange',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.jd'),
                    value: '',
                    sm: 4,
                    id: 'Grade',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.dsjfdz'),
                    value: '',
                    sm: 4,
                    id: 'DActualScaleInterval',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.ejdfdz'),
                    value: '',
                    sm: 4,
                    id: 'EVerifyScaleInterval',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.kxs'),
                    value: '',
                    sm: 4,
                    id: 'KCoefficient',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.yc'),
                    value: '',
                    sm: 4,
                    id: 'Tolerance',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.zzcjmc'),
                    value: '',
                    sm: 4,
                    id: 'Manufacturer',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.ccrq'),
                    value: '',
                    sm: 4,
                    id: 'ManufactureDate',
                    type: 'date'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.ccbh'),
                    value: '',
                    sm: 4,
                    id: 'FactoryNo',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.jbh'),
                    value: '',
                    sm: 4,
                    id: 'OldMeasureNo',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.sqjdrq'),
                    value: '',
                    sm: 4,
                    id: 'RequestVerifyDate',
                    type: 'date'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.jyzq'),
                    value: '',
                    sm: 4,
                    id: 'VerifyCycle',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.jdff'),
                    value: '',
                    sm: 4,
                    id: 'VerifyMethod',
                    type: 'select',
                    options: this.VerifyMethod
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.jyrq'),
                    value: '',
                    sm: 4,
                    id: 'CalibrateDate',
                    type: 'date'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.jdbm'),
                    value: '',
                    sm: 4,
                    id: 'VerifyDepartment',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.sybm'),
                    value: '',
                    sm: 4,
                    id: 'UsingDepartment',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.fbm'),
                    value: '',
                    sm: 4,
                    id: 'SubDepartment',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.fzdd'),
                    value: '',
                    sm: 4,
                    id: 'StorageLocation',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.qjglry'),
                    value: '',
                    sm: 4,
                    id: 'Manager',
                    type: 'select',
                    options: []
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.yzzcm'),
                    value: '',
                    sm: 4,
                    id: 'AccountAssetNo',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.wzzcm'),
                    value: '',
                    sm: 4,
                    id: 'NonAccountAssetNo',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.cxm'),
                    value: '',
                    sm: 4,
                    id: 'MagneticCode',
                    type: 'input'
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.jyzt'),
                    value: '',
                    sm: 4,
                    id: 'AdjustmentStatus',
                    type: 'select',
                    options: []
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.zt'),
                    value: '',
                    sm: 4,
                    id: 'Status',
                    type: 'select',
                    options: []
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_JLQJGL.bc'),
                    value: '',
                    sm: 12,
                    id: 'Remark',
                    type: 'input'
                }
            ]
        };
    },
    computed: {
        locale() {
            return this.$store.state.app.locale || 'zh';
        }
    },
    async mounted() {
        this.AddList.forEach(item => {
            if (item.require) {
                item.label = item.label + ' *';
            }
        });
    },
    watch: {},
    methods: {
        async addSave() {
            let flag = this.AddList.some(item => {
                if (item.require) {
                    return item.value == '' || item.value == null;
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            let params = {};
            this.AddList.forEach(item => {
                params[item.id] = item.value;
            });
            if (this.dialogType == 'edit') {
                params.ID = this.tableItem.ID;
            }
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            let res = await GetMeasureAccountSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$emit('loadData');
                this.showDialog = this.classcheckbox ? false : true;
            }
        },
        formatDate(d, d2) {
            var date = new Date(d);
            var YY = date.getFullYear() + '-';
            var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
            var DD = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
            let str1 = YY + MM + DD;
            var date2 = new Date(d2);
            var YY2 = date2.getFullYear() + '-';
            var MM2 = (date2.getMonth() + 1 < 10 ? '0' + (date2.getMonth() + 1) : date2.getMonth() + 1) + '-';
            var DD2 = date2.getDate() < 10 ? '0' + date2.getDate() : date2.getDate();
            let str2 = YY2 + MM2 + DD2;
            return [str1, str2];
        },
        closeEquip() {
            this.showDialog = false;
            // this.$refs.form.reset();
        },
        closeDatePicker(index) {
            this.$set(this.menu, index, false);
        }
    }
};
</script>
<style lang="scss">
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
    .textlabel {
        display: inline-flex;
        font-size: 16px;
        margin-right: 25px;
    }
    .el-radio-group {
        height: 40px;
        margin-top: 10px;
    }
    .el-radio__input.is-checked + .el-radio__label {
        color: #3dcd58;
    }
    .el-radio__input.is-checked .el-radio__inner {
        border-color: #3dcd58;
        background: #3dcd58;
    }
    .el-radio__label {
        font-size: 16px;
    }
    .el-date-editor {
        width: 100%;
        position: absolute;
        opacity: 0;
        top: 0;
        .el-input__inner {
            font-size: 16px;
            height: 32.5px;
            color: rgba(0, 0, 0, 0.87);
            border: 1px solid #9e9e9e;
        }
        .el-input__icon {
            line-height: 32.5px;
        }
    }
}
</style>

<style lang="scss" scoped>
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
    .textfieldbox {
        position: relative;
    }
}

.ant-calendar-picker {
    ::v-deep input {
        border-color: #9e9e9e;
    }

    ::v-deep i {
        color: #757575;
    }
}
</style>

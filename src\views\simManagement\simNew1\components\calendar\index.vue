<template>
  <div class="calendar-box">
    <div class="line line-1">
      <div
        v-for="item in list.slice(0, 3)"
        :key="item.day"
        class="day-item"
        @click="open(item)"
      >
        <template v-if="item.situation == 0">
          <img
            :src="happyImg"
            alt=""
            class="happy"
          >
        </template>
        <template v-else-if="item.situation > 0">
          <img
            :src="sadImg"
            alt=""
            class="happy"
          >
        </template>
        <template v-else>
          <div class="num">{{ item.day }}</div>
        </template>
      </div>
    </div>
    <div class="line line-1">
      <div
        v-for="item in list.slice(3, 6)"
        :key="item.day"
        class="day-item"
        @click="open(item)"
      >
        <template v-if="item.situation == 0">
          <img
            :src="happyImg"
            alt=""
            class="happy"
          >
        </template>
        <template v-else-if="item.situation > 0">
          <img
            :src="sadImg"
            alt=""
            class="happy"
          >
        </template>
        <template v-else>
          <div class="num">{{ item.day }}</div>
        </template>
      </div>
    </div>
    <div class="line line-2">
      <div
        v-for="item in list.slice(6, 13)"
        :key="item.day"
        class="day-item"
        @click="open(item)"
      >
        <template v-if="item.situation == 0">
          <img
            :src="happyImg"
            alt=""
            class="happy"
          >
        </template>
        <template v-else-if="item.situation > 0">
          <img
            :src="sadImg"
            alt=""
            class="happy"
          >
        </template>
        <template v-else>
          <div class="num">{{ item.day }}</div>
        </template>
      </div>
    </div>
    <div class="line line-2">
      <div
        v-for="item in list.slice(13, 20)"
        :key="item.day"
        class="day-item"
        @click="open(item)"
      >
        <template v-if="item.situation == 0">
          <img
            :src="happyImg"
            alt=""
            class="happy"
          >
        </template>
        <template v-else-if="item.situation > 0">
          <img
            :src="sadImg"
            alt=""
            class="happy"
          >
        </template>
        <template v-else>
          <div class="num">{{ item.day }}</div>
        </template>
      </div>
    </div>
    <div class="line line-2">
      <div
        v-for="item in list.slice(20, 27)"
        :key="item.day"
        class="day-item"
        @click="open(item)"
      >
        <template v-if="item.situation == 0">
          <img
            :src="happyImg"
            alt=""
            class="happy"
          >
        </template>
        <template v-else-if="item.situation > 0">
          <img
            :src="sadImg"
            alt=""
            class="happy"
          >
        </template>
        <template v-else>
          <div class="num">{{ item.day }}</div>
        </template>
      </div>
    </div>
    <div class="line line-1">
      <div
        v-for="item in list.slice(27, 30)"
        :key="item.day"
        class="day-item"
        @click="open(item)"
      >
        <template v-if="item.situation == 0">
          <img
            :src="happyImg"
            alt=""
            class="happy"
          >
        </template>
        <template v-else-if="item.situation > 0">
          <img
            :src="sadImg"
            alt=""
            class="happy"
          >
        </template>
        <template v-else>
          <div class="num">{{ item.day }}</div>
        </template>
      </div>
    </div>
    <div class="line line-1">
      <div
        v-for="item in list.slice(30, 33)"
        :key="item.day"
        class="day-item"
        @click="open(item)"
      >
        <template v-if="item.situation == 0">
          <img
            :src="happyImg"
            alt=""
            class="happy"
          >
        </template>
        <template v-else-if="item.situation > 0">
          <img
            :src="sadImg"
            alt=""
            class="happy"
          >
        </template>
        <template v-else>
          <div class="num">{{ item.day }}</div>
        </template>
      </div>
    </div>
    <!-- 问题列表弹窗 -->
    <!-- <v-dialog
      v-model="showInfoDialog"
      scrollable
      persistent
      width="55%"
    >
      <CalendarDialog
        v-if="showInfoDialog"
        :curTeamTreeObj="curTeamTreeObj"
        :searchFormObj="searchFormObj"
        :curDay="curDay"
        @closePopup="closeDialog"
      >
      </CalendarDialog>
    </v-dialog> -->
    <calendarDialognew
      v-if="calendarDialognewShow"
      v-model="showcalendarDialognew"
      :curDay="curDay"
      :currentYearMonth1="currentYearMonth"
      :curTeamTreeObj="curTeamTreeObj"
      :searchFormObj="searchFormObj"
      :backgroundImg="backgroundImg"
      :fullscreen="fullscreen"
      :bcode="bcode"
      @checkcalendarDialognew="calendarDialognewHandle"
    />
  </div>
</template>

<script>
import calendarDialognew from "./calendarDialognew.vue"
import CalendarDialog from "./calendarDialog.vue"
import happyImgSrc from "@/assets/imgs/happy.png"
import sadImgSrc from "@/assets/imgs/sad.png"
export default {
  name: "Calendar",
  components: {
    // CalendarDialog,
    calendarDialognew
  },
  props: {
    curTeamTreeObj: {
      type: Object,
      default: () => { }
    },
    days: {
      type: Array,
      default: () => []
    },
    searchFormObj: {
      type: Object,
      default: () => { }
    },
    backgroundImg: {
      type: String,
      default: ''
    },
    fullscreen: {
      type: Boolean,
      default: false
    },
    bcode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      currentYearMonth: '',
      calendarDialognewShow: false,
      showcalendarDialognew: false,
      happyImg: happyImgSrc,
      sadImg: sadImgSrc,
      showInfoDialog: false,
      curDay: ''
    };
  },
  computed: {
    list() {
      let curLen = this.days.length
      let needPushLen = 33 - curLen
      let cloneDays = JSON.parse(JSON.stringify(this.days))
      let temp = {
        day: undefined,
        type: undefined
      }
      //补充缺少的数量,保持十字形状
      for (let i = 0; i < needPushLen; i++) {
        cloneDays.push(temp)
      }
      return cloneDays
    }
  },
  watch: {},
  created() { },
  mounted() {

  },
  methods: {
    open(item) {
      console.log(item);
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      this.currentYearMonth = `${year}-${month < 10 ? '0' + month : month}` + '-' + item.day;

      if (item.situation < 0) return
      this.curDay = item
      // this.showInfoDialog = true
      this.calendarDialognewShow = true
      this.showcalendarDialognew = true
    },
    calendarDialognewHandle() {
      this.calendarDialognewShow = false
      this.$emit('change');
    },
    //关闭新增弹窗
    // closeDialog() {
    //   this.showInfoDialog = false
    //   // this.addDialogVisable = false
    //   // this.editItemObj = {}
    //   this.$emit('change');

    // }
  }
};
</script>
<style lang="less" scoped>
.calendar-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.line {
    display: flex;
    height: 14%;

    &.line-1 {
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: center;
    }

    &.line-2 {
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: center;
    }

    .day-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 14%;
        height: 100%;
        flex-shrink: 0;
        cursor: pointer;

        // text-align: center;
        .happy {
            width: 85%;

            // height: 85%;
            &:hover {
                // width: 100%;
                // height: 100%;
                transform: scale(1.3);
            }
        }

        .num {
            width: 100%;
            height: 100%;
            // background: #cfd8dc;
            color: #fff;
            text-align: center;
            line-height: 34px;
            // border: 1px solid #fff;

            &:hover {
                background: #888;
            }
        }
    }
}
</style>
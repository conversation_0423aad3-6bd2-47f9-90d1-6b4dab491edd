<!--甘特图首页-->
<template>
    <div class="gantt-table">
        <!-- <div class="gantt-head">
            <div class="gantt-fixed">
                <div class="gantt-head">
                    <span>时间区间</span>
                    <span>
                        {{ `${choiceTime[0].format('yyyy-MM-dd')} - ${choiceTime[1].format('yyyy-MM-dd')}` }}
                    </span>
                </div>
            </div>
        </div> -->
        <div class="gantt-box">
            <div class="left" ref="left">
                <div class="head">
                    <!--    左边栏的列表头      -->
                    <div class="head-box">
                        <GanttRow>
                            <GanttBlock>序号</GanttBlock>
                            <GanttBlock>机型</GanttBlock>
                            <GanttBlock>机号</GanttBlock>
                        </GanttRow>
                    </div>
                </div>
                <!--  左边栏的列表内容      -->
                <div class="left-box" :style="{ transform: `translateY(-${transFormYValue}px)` }">
                    <GanttRow
                        v-for="(item, index) in ganttData"
                        :key="index"
                        :style="{
                            height: 40 + 'px'
                        }"
                    >
                        <GanttBlock>{{ item.id }}</GanttBlock>
                        <GanttBlock>{{ item.type }}</GanttBlock>
                        <GanttBlock>{{ item.code }}</GanttBlock>
                    </GanttRow>
                </div>
            </div>
            <!--    右边的内容      -->
            <div class="right" ref="right" @contextmenu.prevent="cancelRightClick" @click="cancelRightClick">
                <!--  时间头      -->
                <div class="head">
                    <div class="head-box">
                        <GanttRow :style="{ transform: `translatex(-${transFormXValue}px)` }" class="transition-transForm">
                            <GanttTimeBlock :target-time="item.format('yyyy-MM-dd')" v-for="(item, index) in choiceTimeArr" :key="index"></GanttTimeBlock>
                        </GanttRow>
                    </div>
                </div>
                <div class="content" ref="contentGrid">
                    <!--      甘特图中具体的内容   -->
                    <div class="gantt-grid" ref="box" id="ganttGrid">
                        <!--   网格状图表  -->
                        <GanttRow
                            v-for="(item, rowIndex) in ganttData"
                            :key="item.id"
                            :id="`ganttRow-${rowIndex}`"
                            class="gantt-row-relative"
                            :class="{ 'gantt-row-active': nowSuck === rowIndex }"
                            :style="{
                                height: 40 + 'px'
                            }"
                        >
                            <template v-if="item.timeBlock">
                                <!--可操作模块-->
                                <action-block
                                    enableDragSide
                                    enableDragFree
                                    v-for="(times, timeIndex) in item.timeBlock"
                                    :key="times.specialId"
                                    :isActive="multipleChoice.indexOf(`${item.id}|${times.specialId}`) > -1"
                                    :activeArr="multipleChoice"
                                    :blockData="times"
                                    startTimeCol="startTime"
                                    endTimeCol="endTime"
                                    @moveSideChange="() => doTier(item)"
                                    @freeMove="top => doFreeMove(top, rowIndex)"
                                    @freeMoveUp="obj => doFreeMoveUp(obj, rowIndex, timeIndex)"
                                    @moreFreeMoveUp="doMoreFreeMoveUp"
                                    :scrollTop="transFormYValue"
                                    :choiceTime="choiceTime"
                                >
                                    <div class="time-block" @click="choiceTimeBlock(item.id, times.specialId)">{{ times.id }}</div>
                                </action-block>
                            </template>
                            <!--      block => 180  因为timeBlock => 60    60*3 = 180px   -->
                            <GanttBlock v-for="rowIndex in (24 / 3) * choiceTimeArr.length" :key="rowIndex" style="flex-shrink: 0"></GanttBlock>
                        </GanttRow>
                    </div>
                </div>
            </div>
        </div>

        <!--右键菜单   -->
        <context-menu :is-show="rightMenu.isShow" :left.sync="rightMenu.left" :top.sync="rightMenu.top">
            <context-menu-item title="菜单1"></context-menu-item>
            <context-menu-item title="菜单2"></context-menu-item>
            <context-menu-item title="菜单3"></context-menu-item>
            <context-menu-item title="菜单4"></context-menu-item>
        </context-menu>
    </div>
</template>

<script>
import GanttRow from './components/gantt/gantt-row';
import GanttBlock from './components/gantt/gantt-block';
import GanttTimeBlock from './components/gantt/gantt-time-block';
import ganttMixin from './components/gantt/mixin/gantt-mixin';
import airPlaneData from './fake-data/airPlaneData.js';
import ActionBlock from './components/gantt/action-block';
import ContextMenu from './components/context-menu/context-menu';
import ContextMenuItem from './components/context-menu/context-menu-item';
export default {
    name: 'GanttIndex',
    components: { ContextMenuItem, ContextMenu, ActionBlock, GanttBlock, GanttRow, GanttTimeBlock },
    mixins: [ganttMixin],
    data() {
        return {
            //选择的时间区间
            choiceTime: [new Date('Fri Jul 25 2021 00:00:00 GMT+0800 (中国标准时间)'), new Date('Fri Jul 31 2021 00:00:00 GMT+0800 (中国标准时间)')],
            nowSuck: -1,
            //用于判断 当前移动到的行，用于磁性吸附
            offsetTopArr: [],
            //默认的高度
            defaultTop: 10
        };
    },
    computed: {},
    mounted() {
        //开始滚动交互事件
        this.getScrollEvent();
        this.getData();
    },
    methods: {
        //加载数据
        getData() {
            //开始索引
            let start = (this.page.current - 1) * this.page.pageCount;
            //开始分页 结束索引等于开始索引+pageCount
            let arr = airPlaneData.slice(start, start + this.page.pageCount);

            setTimeout(() => {
                //在此处进行防重叠计算

                arr.forEach(big => {
                    let item = big.timeBlock;
                    item.forEach(t => {
                        //赋值唯一id
                        t.specialId = this.generateUUID();
                    });
                });

                arr.forEach(big => {
                    //对每一条计算层级
                    this.handleSingleFold(big, 'startTime', 'endTime');
                });
                console.log(arr);
                this.ganttData.push(...arr);

                //获得每一行所在box的高度 用户拖拽磁性吸附用
                this.$nextTick(() => {
                    this.getOffsetTop();
                });
            }, 300);
        },
        //进行懒加载处理
        lazyLoad() {
            //这个getData是最后想要赋值还是push，看有没有懒加载的需求，如果需要懒加载，那么上述getData的this.ganttData = arr 就要改成this.ganttData.push(...arr)
            //如果getData不需要懒加载，那么这个函数里面甚至什么都可以不用写，如果滚动到底部触发函数后又渲染了一遍（不是往底部推送数据），那么会执行两次造成性能浪费
            this.getData();
            console.log('开始加载');
        },
        //获得每一行的offsetTop
        getOffsetTop() {
            let rowDom = this.$el.getElementsByClassName('gantt-row-relative');
            let offsetTopArr = [];
            for (let i = 0; i < rowDom.length; i++) {
                let top = rowDom[i].offsetTop;
                offsetTopArr.push(top);
            }
            this.offsetTopArr = offsetTopArr;
        },
        //时间条拉伸
        doTier(v) {
            //让右键菜单消失
            this.rightMenu.isShow = false;

            //完成拉拽的时候 删除自定义的字段 然后重新渲染  不然这些属性将会叠加
            delete v.height;
            v.timeBlock.forEach(e => {
                delete e.tier;
                delete e.top;
            });
            this.handleSingleFold(v, 'startTime', 'endTime');
            this.$nextTick(() => {
                this.getOffsetTop();
            });
        },
        //时间块被移动，这里移动的作用，其实就是行亮色
        doFreeMove(top, index) {
            //让右键菜单消失
            this.rightMenu.isShow = false;

            let dom = document.getElementById(`ganttRow-${index}`);

            let domTop = dom.offsetTop;

            //因为这个top是时间块相对于行的top  所以还需要进一步计算
            top = top + domTop;

            //offsetTopArr
            for (let i = 0; i < this.offsetTopArr.length; i++) {
                let itemV = this.offsetTopArr[i];
                if (top > itemV) {
                    this.nowSuck = i;
                }
            }
        },
        //时间块移动后鼠标松开，进行磁性吸附
        doFreeMoveUp(obj, rowIndex, timeIndex) {
            //如果nowSuck为-1 说明没有进行移动
            if (this.nowSuck == -1) {
                return;
            }

            //原数据删除
            this.ganttData[rowIndex].timeBlock.splice(timeIndex, 1);

            this.$nextTick(() => {
                delete this.ganttData[rowIndex].height;
                this.ganttData[rowIndex].timeBlock.forEach(e => {
                    delete e.tier;
                    delete e.top;
                });
                //防碰撞渲染
                this.handleSingleFold(this.ganttData[rowIndex], 'startTime', 'endTime');
                this.$nextTick(() => {
                    //重新计算每一行所在的坐标
                    this.getOffsetTop();

                    this.$nextTick(() => {
                        //新数据放置
                        this.ganttData[this.nowSuck].timeBlock.push(obj);

                        this.$nextTick(() => {
                            delete this.ganttData[this.nowSuck].height;
                            this.ganttData[this.nowSuck].timeBlock.forEach(e => {
                                delete e.tier;
                                delete e.top;
                            });
                            //防碰撞渲染
                            this.handleSingleFold(this.ganttData[this.nowSuck], 'startTime', 'endTime');

                            this.$nextTick(() => {
                                //重新计算每一行所在的坐标
                                this.getOffsetTop();

                                //时间块重新部署完成，nowSuck重置
                                this.nowSuck = -1;
                            });
                        });
                    });
                });
            });
        },
        //批量进行 时间块拖拽的时候
        // eslint-disable-next-line no-unused-vars
        doMoreFreeMoveUp(arr) {
            //让右键菜单消失
            this.rightMenu.isShow = false;

            if (this.nowSuck == -1) {
                return;
            }
            //获得rowId 顺便做个去重
            let pidArr = Array.from(new Set(arr.map(e => parseInt(e.split('|')[0]))));

            //遍历父id
            for (let i = 0; i < pidArr.length; i++) {
                //得到当前pid下的子数组
                let sonArr = arr.filter(e => parseInt(e.split('|')[0]) == pidArr[i]).map(e => e.split('|')[1]);
                //获得pid 在 数组中的索引
                let pIndex = this.ganttData.findIndex(e => e.id == pidArr[i]);

                //移除sonArr 中id 对应的时间条
                for (let j = 0; j < sonArr.length; j++) {
                    //找到sid的索引
                    let sIndex = this.ganttData[pIndex].timeBlock.findIndex(e => e.specialId == sonArr[j]);

                    //存下这个被删除的对象
                    let obj = this.ganttData[pIndex].timeBlock[sIndex];

                    //移除
                    this.ganttData[pIndex].timeBlock.splice(sIndex, 1);

                    //自定义字段去除
                    delete this.ganttData[pIndex].height;
                    this.ganttData[pIndex].timeBlock.forEach(e => {
                        delete e.tier;
                        delete e.top;
                    });

                    //防碰撞渲染
                    this.handleSingleFold(this.ganttData[pIndex], 'startTime', 'endTime');
                    this.$nextTick(() => {
                        //重新计算每一行所在的坐标
                        this.getOffsetTop();

                        //上述删除后，把删除掉的东西再加到新的行里
                        this.$nextTick(() => {
                            //新数据放置
                            this.ganttData[this.nowSuck].timeBlock.push(obj);

                            this.$nextTick(() => {
                                delete this.ganttData[this.nowSuck].height;
                                this.ganttData[this.nowSuck].timeBlock.forEach(e => {
                                    delete e.tier;
                                    delete e.top;
                                });
                                //防碰撞渲染
                                this.handleSingleFold(this.ganttData[this.nowSuck], 'startTime', 'endTime');

                                this.$nextTick(() => {
                                    //重新计算每一行所在的坐标
                                    this.getOffsetTop();

                                    //时间块重新部署完成，nowSuck重置
                                    this.nowSuck = -1;

                                    //清空多选数组，以免与下次的多选拖拽交互产生冲突
                                    this.multipleChoice = [];
                                });
                            });
                        });
                    });
                }
            }
        },
        //右键点击
        rightClick(times, event) {
            let delay = 0;

            //如果是已经是展开状态，如果再展开，那就延时一下，让菜单消失的不那么突兀
            if (this.rightMenu.isShow == true) {
                delay = 150;
                this.cancelRightClick();
            }
            setTimeout(() => {
                this.rightMenu.isShow = true;

                this.rightMenu.left = event.clientX;

                this.rightMenu.top = event.clientY;
            }, delay);
        },
        //取消右键点击
        cancelRightClick() {
            this.rightMenu.isShow = false;
        }
    }
};
</script>

<style scoped lang="scss">
@import './common/css/global-property';

.gantt-table {
    .gantt-head {
        width: 100%;
        height: $topBarHeight;

        .gantt-fixed {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: $topBarHeight;

            background-image: $topBarColor;
            .gantt-head {
                padding-left: 15px;
                font-weight: bold;
                line-height: $topBarHeight;
                color: $tableHeadColor;
            }
        }
    }

    .gantt-box {
        display: flex;
        width: 100%;
        height: calc(100vh - #{$topBarHeight});
        .left {
            width: $leftSideWidth;
            overflow-y: hidden;
            overflow-x: scroll;
            .head {
                height: $tableHeadHeight;
                position: relative;
                z-index: 3;
                .head-box {
                    background-color: $tableHeadColor;
                    height: $tableHeadHeight;
                    color: $tableHeadFontColor;
                    width: 100%;
                }
            }
            .left-box {
                transition: transform 0.3s;
                background-color: $tableLeftColor;
            }
        }

        .right {
            width: calc(100% - #{$leftSideWidth});
            .head {
                height: $tableHeadHeight;
                .transition-transForm {
                    transition: transform 0.3s;
                }
                .head-box {
                    overflow: hidden; //transform 移动出的东西 隐藏
                    box-sizing: border-box;
                    color: $tableHeadFontColor;
                    background-color: $tableHeadColor;
                    height: $tableHeadHeight;
                    z-index: 2;
                }
            }

            .content {
                height: calc(100vh - #{$topBarHeight} - #{$tableHeadHeight});
                overflow-x: auto;
                .gantt-grid {
                    position: relative;
                    display: inline-block;
                    overflow: hidden;
                    z-index: 1;

                    .gantt-row-relative {
                        position: relative;
                        transition: background-color 0.2s;
                        &.gantt-row-active {
                            background-color: $tableRowActiveColor;
                        }
                        //时间块的样式
                        .time-block {
                            height: 100%;
                        }
                    }
                }
            }
        }
    }
}
</style>

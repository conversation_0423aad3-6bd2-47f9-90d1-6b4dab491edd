// 替代料管理

import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM'


//新增、编辑
export function DatamodelSaveForm(data) {
    const api = '/api/Datamodel/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取列表
export function GetDatamodelList(data) {
    const api = '/api/Datamodel/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 删除
export function DeleteDatamodel(data) {
    const api = '/api/Datamodel/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

// 导入模板下载
export function ImportExcelTemplates() {
    const api = '/api/Datamodel/ImportExcelTemplates'
    return api;
}

// 批量导入
export function ImportExcel(data) {
    const api = '/api/Datamodel/ImportExcel'
    return getRequestResources(baseURL, api, 'post', data);
}

// 批量启用。禁用
export function UpdateIsenabled(data) {
    const api = '/api/Datamodel/UpdateIsenabled'
    return getRequestResources(baseURL, api, 'post', data);
}
// 导出
export function doExport(data) {
    const api = '/api/Datamodel/ExportExcelTemplates'
    return getRequestResources(baseURL, api, 'post', data);
}
<!-- eslint-disable vue/valid-v-slot -->
<template>
    <v-dialog v-model="dialog" persistent max-width="860px">
        <!-- 字典分类 查询 -->
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                {{ $t('DFM_WLMX._SXKZ') }}
                <v-icon @click="dialog = false">mdi-close</v-icon>
            </v-card-title>
            <!-- 表单内容 -->
            <v-card-text class="mt-4">
                <v-tabs v-model="tab">
                    <v-tab>{{ $t('DFM_WLMX._SXLB') }}</v-tab>
                    <v-tab>{{ $t('DFM_WLMX._TJPZ') }}</v-tab>
                </v-tabs>
                <v-tabs-items v-model="tab">
                    <v-tab-item>
                        <div class="dictionary-main">
                            <v-card class="px-2 py-2" min-height="300">
                                <div class="form-btn-list">
                                    <v-btn icon color="primary" @click="EquipmentGetPropertyValuePageList">
                                        <v-icon>mdi-cached</v-icon>
                                    </v-btn>
                                    <v-btn color="primary" @click="clickClass('add')">{{ $t('GLOBAL._XZ') }}</v-btn>
                                    <!-- <v-btn color="error" :disabled="deleteList.length == 0" @click="clickClass('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn> -->
                                </div>
                                <Tables
                                    ref="table"
                                    :loading="loading"
                                    :footer="false"
                                    :headers="headers"
                                    :showSelect="false"
                                    table-height="320"
                                    table-name="DFM_WLMX_SXKZ"
                                    :desserts="tableData"
                                    :page-options="pageOptions"
                                    @selectePages="selectePages"
                                    @tableClick="tableClick"
                                >
                                    <template #actions="{ item }">
                                        <v-btn
                                            v-for="(list, index) in btnList"
                                            :key="index"
                                            :disabled="item.ClassId !== null && list.disabled"
                                            text
                                            small
                                            class="mx-0 px-0 mr-2"
                                            :color="list.type"
                                            @click.stop="tableClick(item, list.code)"
                                        >
                                            {{ list.text }}
                                        </v-btn>
                                    </template>
                                </Tables>
                            </v-card>
                        </div>
                    </v-tab-item>
                    <v-tab-item>
                        <v-card>
                            <div class="itxst">
                                <div class="col">
                                    <div class="title">
                                        <span>{{ $t('DFM_WLMX._KXTJ') }}</span>
                                        <span class="float-right">{{ allItems.length }}</span>
                                    </div>
                                    <vuedraggable
                                        v-model="allItems"
                                        group="site"
                                        animation="300"
                                        drag-class="dragClass"
                                        ghost-class="ghostClass"
                                        chosen-class="chosenClass"
                                        @start="onStart"
                                        @end="onEnd"
                                    >
                                        <transition-group :style="style">
                                            <div v-for="item in allItems" :key="item.ID" class="item">{{ item.ClassName }}</div>
                                        </transition-group>
                                    </vuedraggable>
                                </div>
                                <div class="col">
                                    <div class="title">
                                        <span>{{ $t('DFM_WLMX._YXTJ') }}</span>
                                        <span class="float-right">{{ selectionRrightData.length }}</span>
                                    </div>
                                    <vuedraggable
                                        v-model="selectionRrightData"
                                        group="site"
                                        animation="100"
                                        drag-class="dragClass"
                                        ghost-class="ghostClass"
                                        chosen-class="chosenClass"
                                        @start="onStart"
                                        @end="onEnd"
                                    >
                                        <transition-group :style="style">
                                            <div v-for="item in selectionRrightData" :key="item.ID" class="item">{{ item.ClassName }}</div>
                                        </transition-group>
                                    </vuedraggable>
                                </div>
                            </div>
                            <v-card-actions class="lighten-3">
                                <v-spacer></v-spacer>
                                <v-btn color="primary" @click="attrSave()">{{ $t('GLOBAL._QD') }}</v-btn>
                                <v-btn @click="dialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
                            </v-card-actions>
                        </v-card>
                    </v-tab-item>
                </v-tabs-items>
            </v-card-text>
        </v-card>
        <v-dialog v-model="dialogClass" persistent max-width="860px">
            <!-- 新增 -->
            <v-card v-if="clickType === 'add'">
                <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                    {{ $t('DFM_WLMX._TJSX') }}
                    <v-icon @click="dialogClass = false">mdi-close</v-icon>
                </v-card-title>
                <!-- 表单内容 -->
                <v-card-text class="mt-7">
                    <v-container>
                        <v-form ref="attrform" v-model="validclass">
                            <v-row>
                                <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                    <v-text-field
                                        ref="PropertyCode"
                                        v-model="saveAttrFrom.PropertyCode"
                                        :rules="[v => !!v || '编码不能为空']"
                                        outlined
                                        dense
                                        :label="$t('DFM_WLMX._SXBM')"
                                        required
                                    ></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                    <v-text-field
                                        ref="PropertyValue"
                                        v-model="saveAttrFrom.PropertyValue"
                                        :rules="[v => !!v || '值不能为空']"
                                        outlined
                                        dense
                                        :label="$t('DFM_WLMX._SXZ')"
                                        required
                                    ></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12">
                                    <v-textarea v-model="saveAttrFrom.Remark" rows="2" outlined :label="$t('DFM_WLMX._SXBZ')"></v-textarea>
                                </v-col>
                            </v-row>
                        </v-form>
                    </v-container>
                </v-card-text>
                <v-card-actions class="py-0">
                    <v-checkbox v-model="classcheckbox" label="确定并关闭窗口"></v-checkbox>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="SaveClassForm('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                    <v-btn @click="dialogClass = false">{{ $t('GLOBAL._GB') }}</v-btn>
                </v-card-actions>
            </v-card>

            <!-- 编辑 -->
            <v-card v-if="clickType === 'edit'">
                <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                    {{ $t('DFM_WLMX._XGSX') }}
                    <v-icon @click="dialogClass = false">mdi-close</v-icon>
                </v-card-title>
                <!-- 表单内容 -->
                <v-card-text class="mt-7">
                    <v-container>
                        <v-form ref="attrform" v-model="validclass">
                            <v-row>
                                <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                    <v-text-field
                                        ref="PropertyCode"
                                        v-model="tableItem.PropertyCode"
                                        :rules="[v => !!v || '属性/类别不能为空']"
                                        outlined
                                        dense
                                        :label="$t('DFM_WLMX._SXBM')"
                                        required
                                    ></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                                    <v-text-field
                                        ref="PropertyValue"
                                        v-model="tableItem.PropertyValue"
                                        :rules="[v => !!v || '值不能为空']"
                                        outlined
                                        dense
                                        :label="$t('DFM_WLMX._SXZ')"
                                        required
                                    ></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12">
                                    <v-textarea v-model="tableItem.Remark" rows="2" outlined :label="$t('DFM_WLMX._SXBZ')"></v-textarea>
                                </v-col>
                            </v-row>
                        </v-form>
                    </v-container>
                </v-card-text>
                <v-card-actions class="py-4">
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="SaveClassForm('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                    <v-btn @click="dialogClass = false">{{ $t('GLOBAL._GB') }}</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </v-dialog>
</template>

<script>
import { ClassMappingSave } from '@/api/common.js';

import { EquipmentGetPropertyValuePageList, EquipmentAttrSaveForm, EquipmentAttrDelete, GetEquipmentClassList } from '@/api/factoryPlant/physicalModel.js';

import { attributeDialog } from '@/columns/factoryPlant/physicalModel.js';

export default {
    name: 'AttributeDialog',
    props: {
        treeData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            tab: null,
            tableData: [],
            selection: [], // 左侧选中数据
            allItems: [], // 左侧所有套件
            selectionRright: [], //右侧选中数据
            selectionRrightData: [], // 右侧所有数据
            rootitems: [], // 根节点
            rootClasslist: [],
            // 提交表单数据
            validclass: true,
            dialog: false,
            dialogClass: false,
            // 新增字典
            checkbox: false,
            checkboxTree: false,
            loading: false,
            // 分类管理弹窗（新增，修改）
            clickType: '',
            classcheckbox: true,

            //查询条件
            searchkey: '',
            headers: attributeDialog,

            // 修改数据
            tableItem: {},
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            deleteList: [],
            //  保存分类入参
            saveAttrFrom: {
                EquipmentId: null,
                PropertyValue: null,
                Remark: null,
                PropertyCode: null
            },
            // 操作按钮
            btnList: [
                {
                    text: '修改',
                    code: 'edit',
                    type: 'primary',
                    icon: ''
                },
                {
                    text: '删除',
                    code: 'delete',
                    disabled: true,
                    type: 'red',
                    icon: ''
                }
            ],
            // 拖拽数据
            drag: false,
            style: {
                minHeight: '240px',
                display: 'block'
            },
            EquipmentId: ''
        };
    },
    mounted() {},
    methods: {
        initData(item) {
            this.EquipmentId = item.ID;
            this.EquipmentGetPropertyValuePageList();
            this.GetEquipmentClassList();
        },
        // 拖拽
        onStart() {
            this.drag = true;
        },
        onEnd() {
            this.drag = false;
        },
        // 获取属性列表
        async EquipmentGetPropertyValuePageList() {
            let papams = {
                equipmentId: this.EquipmentId,
                key: ''
            };
            const res = await EquipmentGetPropertyValuePageList(papams);
            if (res.success) {
                this.tableData = res.response;
            } else {
                this.tableData = [];
            }
        },
        // 获取套件配置
        async GetEquipmentClassList() {
            let papams = {
                equipmentId: this.EquipmentId
            };
            const res = await GetEquipmentClassList(papams);
            if (res.success) {
                this.allItems = res.response.Item;
                this.selectionRrightData = res.response.SelectItem;
            }
        },

        // 操作按钮
        clickClass(type) {
            switch (type) {
                case 'add':
                    for (const key in this.saveAttrFrom) {
                        if (Object.hasOwnProperty.call(this.saveAttrFrom, key)) {
                            this.saveAttrFrom[key] = '';
                        }
                    }
                    this.clickType = type;
                    this.dialogClass = true;
                    return;
                case 'delete':
                    if (this.deleteList.length) {
                        this.deltable();
                    } else {
                        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'warning' });
                    }
                    return;
            }
        },
        // 属性扩展新增 & 修改
        async SaveClassForm(type) {
            let fromvalidate = await this.$refs.attrform.validate();
            if (fromvalidate) {
                let params;
                if (type == 'add') {
                    params = {
                        Deleted: 0,
                        EquipmentId: this.EquipmentId,
                        PropertyValue: this.saveAttrFrom.PropertyValue,
                        Remark: this.saveAttrFrom.Remark,
                        PropertyCode: this.saveAttrFrom.PropertyCode
                    };
                } else {
                    params = {
                        EquipmentId: this.EquipmentId,
                        PropertyValue: this.tableItem.PropertyValue,
                        Remark: this.tableItem.Remark,
                        PropertyCode: this.tableItem.PropertyCode,
                        ID: this.tableItem.ID
                    };
                }

                let res = await EquipmentAttrSaveForm(params);
                let { success } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: type == 'add' ? '添加成功' : '修改成功', color: 'success' });
                    this.dialogClass = this.classcheckbox ? false : true;
                    this.EquipmentGetPropertyValuePageList();
                }
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.tableItem = JSON.parse(JSON.stringify(item || {}));
            switch (type) {
                case 'edit':
                    this.dialogClass = true;
                    this.clickType = type;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除列表
        SelectedItems(item) {
            this.tableItem = {};
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        //分页选择
        selectePages() {
            console.log(111);
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await EquipmentAttrDelete(params);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.EquipmentGetPropertyValuePageList();
                        this.deleteList = [];
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        //套件保存ClassMappingSave
        async attrSave() {
            let romove = this.selectionRrightData.map(item => {
                return {
                    ClassId: item.ID,
                    MappingId: this.EquipmentId
                };
            });
            let params = {
                MappingId: this.EquipmentId,
                MappingList: romove
            };
            const res = await ClassMappingSave(params);
            if (res.success) {
                this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });
                this.GetEquipmentClassList();
                this.EquipmentGetPropertyValuePageList();
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.ghostClass {
    background-color: blue !important;
}

.chosenClass {
    background-color: var(--v-primary-lighten2) !important;
    opacity: 1 !important;
}

.dragClass {
    background-color: var(--v-primary-lighten1) !important;
    opacity: 1 !important;
    box-shadow: none !important;
    outline: none !important;
    background-image: none !important;
}

.itxst {
    margin: 12px auto;
    min-height: 320px;
    display: flex;
    overflow: auto;
}

.title {
    padding: 6px 12px;
}

.col {
    width: 40%;
    flex: 1;
    padding: 10px;
    border: solid 1px #eee;
    border-radius: 5px;
    float: left;
}

.col + .col {
    margin-left: 10px;
}

.item {
    padding: 6px 12px;
    border-radius: 3px;
    margin: 0px 10px 0px 10px;
    border: solid 1px var(--v-primary-lighten5);
    background-color: var(--v-primary-lighten5);
}

.item:hover {
    background-color: var(--v-primary-lighten3);
    cursor: move;
}

.item + .item {
    border-top: none;
    margin-top: 6px;
}
</style>

// 通知记录
<template>
    <div class="line-side-view">
        <div class="line-side-main overflow-auto">
            <SearchForm ref="contactTorm" class="mt-2" @selectChange="selectChange" :searchinput="searchinput"
                :show-from="showFrom" @searchForm="searchForm" />
            <v-card outlined>
                <div class="form-btn-list">
                    <!-- 搜索栏 -->
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary">
                        <v-icon @click="getDataList">mdi-cached</v-icon>
                    </v-btn>
                    <v-btn color="primary" v-has="'RYCXPZ_SJDC'" @click="handleExport()">{{ $t('SHIFT_RYSJ_RYCXPZ._DC')
                    }}</v-btn>
                    <!-- <v-btn color="primary" @click="operaClick({})">新增</v-btn>
                    <v-btn color="primary" disabled @click="sureItems()">{{ $t('GLOBAL._PLSC') }}</v-btn> -->
                </div>
                <Tables :headers="headers" :desserts="desserts" :loading="loading" :page-options="pageOptions"
                    table-name="ANDON_BJTZJL" :btn-list="btnList" :dictionaryList="dictionaryList"
                    @selectePages="selectePages" :showSelect="false"
                    :tableHeight="showFrom ? 'calc(100vh - 370px)' : 'calc(100vh - 200px)'"
                    @itemSelected="selectedItems" @toggleSelectAll="selectedItems" @tableClick="tableClick"></Tables>
            </v-card>
            <update-dialog ref="updateDialog" :opera-obj="operaObj" :pushTypeList="pushTypeList"
                @handlePopup="handlePopup"></update-dialog>
        </div>
    </div>
</template>
<script>
import { getAlarmTypeRootList, getAlarmTypeTreetList } from '@/api/andonManagement/alarmType.js';
import { getNoticeRecordList, DeleteNoticeRecord, ExportNoticeRecordToExcel } from '@/api/andonManagement/noticeRecord.js';
import { noticeRecordColum } from '@/columns/andonManagement/noticeRecord.js';
import Util from '@/util';
import dayjs from 'dayjs';
import physicalModel from '@/mixins/physicalModel';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_ANDON;
export default {
    name: 'UpgradeRule',
    components: {
        UpdateDialog: () => import('./components/updateDialog.vue')
    },
    mixins: [physicalModel],
    data() {
        return {
            operaObj: {},
            showFrom: false,
            headers: noticeRecordColum,
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            deleteId: [],
            selectedList: [],
            desserts: [],
            searchParams: {},
            pushTypeList: [],
            pushStatusList: [],
            alarmTypeRootList: [],
            alarmTypeList: [],
            AreaList: [],
            LineCodeList: [],
            equipmentList: [],
            unitList: [],
            start: '',
            end: ''
        }
    },
    computed: {
        //查询条件
        searchinput() {
            return [
                // 开始时间
                {
                    key: 'start',
                    type: 'time',
                    icon: '',
                    value: this.start,
                    label: this.$t('GLOBAL.StartTime')
                },
                // 结束时间
                {
                    key: 'end',
                    type: 'time',
                    icon: '',
                    value: this.end,
                    label: this.$t('GLOBAL.EndTime')
                },
                // 产品线
                {
                    key: 'areacode',
                    icon: '',
                    type: 'combobox',
                    search: 'areacode',
                    linkageKey: 'firstLevel',
                    childrenLinkageArray: this.LineCodeList,
                    selectData: this.$changeSelectItems(this.AreaList, 'EquipmentCode', 'EquipmentName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.AreaCode')
                },
                // 工段
                {
                    key: 'productLine',
                    icon: '',
                    type: 'combobox',
                    search: 'productLine',
                    linkageKey: 'secondLevel',
                    childrenLinkageArray: this.unitList,
                    selectData: this.$changeSelectItems(this.secondLevel, 'EquipmentCode', 'EquipmentName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.ProductLineName')
                },
                // 工站
                {
                    key: 'unitcode',
                    icon: '',
                    type: 'combobox',
                    search: 'unitcode',
                    linkageKey: 'thirthLevel',
                    childrenLinkageArray: this.equipmentList,
                    selectData: this.$changeSelectItems(this.thirthLevel, 'EquipmentCode', 'EquipmentName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.UnitName')
                },
                // 设备
                {
                    key: 'equipmentCode',
                    icon: '',
                    type: 'combobox',
                    search: 'equipmentCode',
                    linkageKey: 'fourthLevel',
                    childrenLinkageArray: [],
                    selectData: this.$changeSelectItems(this.fourthLevel, 'EquipmentCode', 'EquipmentName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.EquipmentName')
                },
                // 一级分类
                {
                    key: 'MainAlarmType',
                    icon: '',
                    type: 'combobox',
                    search: 'MainAlarmType',
                    linkageKey: 'fivthLevel',
                    childrenLinkageArray: this.alarmTypeList,
                    selectData: this.$changeSelectItems(this.alarmTypeRootList, 'AlarmCode', 'AlarmName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.MainAlarmType')
                },
                // 二级分类
                {
                    key: 'SubAlarmType',
                    icon: '',
                    type: 'combobox',
                    search: 'SubAlarmType',
                    linkageKey: 'sixthLevel',
                    childrenLinkageArray: [],
                    selectData: this.$changeSelectItems(this.sixthLevel, 'AlarmCode', 'AlarmName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJJL.SubAlarmType')
                },
                // 推送类型
                {
                    key: 'PushType',
                    icon: '',
                    byValue:"ItemValue",
                    type: 'select',
                    selectData: this.$changeSelectItems(this.pushTypeList, 'ItemValue', 'ItemName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJTZJL.PushType')
                },
                // 发送状态
                {
                    key: 'status',
                    icon: '',
                    byValue:"ItemValue",
                    type: 'select',
                    selectData: this.$changeSelectItems(this.pushStatusList, 'ItemValue', 'ItemName'),
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJTZJL.Status')
                },
                // 推送者
                {
                    key: 'empLark',
                    icon: '',
                    value: '',
                    label: this.$t('$vuetify.dataTable.ANDON_BJTZJL.Reciver')
                }
            ];
        },
        btnList() {
            return [
                { text: "编辑", icon: '', code: 'edit', type: 'primary' },
                { text: '删除', icon: '', code: 'delete', type: 'primary' }
            ];
        },
        dictionaryList() {
            return [
                { arr: this.AreaList, key: 'AreaCode', val: 'EquipmentCode', text: 'EquipmentName' },
                { arr: this.LineCodeList, key: 'ProductLine', val: 'EquipmentCode', text: 'EquipmentName' },
                { arr: this.unitList, key: 'UnitCode', val: 'EquipmentCode', text: 'EquipmentName' },
                { arr: this.equipmentList, key: 'EquipmentCode', val: 'EquipmentCode', text: 'EquipmentName' },
                { arr: this.alarmTypeList, key: 'MainAlarmType', val: 'AlarmCode', text: 'AlarmName' },
                { arr: this.alarmTypeList, key: 'SubAlarmType', val: 'AlarmCode', text: 'AlarmName' },
                { arr: this.pushTypeList, key: 'PushType', val: 'ItemValue', text: 'ItemName' },
                { arr: this.pushStatusList, key: 'Status', val: 'ItemValue', text: 'ItemName' }
            ]
        }
    },
    async created() {
        this.initData();
        await this.getAlarmTypeList();
        await this.getalarmTypeList();
        this.getDataList();
    },
    methods: {
        async initData() {
            const nowTime = new Date(); ``
            const start = dayjs(nowTime).format('YYYY-MM-DD') + ' 00:00:00';
            const end = dayjs(nowTime).format('YYYY-MM-DD HH:mm:ss');
            this.start = start;
            this.end = end;
            this.searchParams = { start, end }
            // 获取推送类型列表
            this.pushTypeList = await this.$getDataDictionary('AndonPushType');
            // 获取发送列表
            this.pushStatusList = await this.$getDataDictionary('PushStatus');
            // 获取产线
            this.LineCodeList = await Util.GetEquipmenByLevel('Line');
            // 产品线
            this.AreaList = await Util.GetEquipmenByLevel('Area');
            // 获取工站
            this.unitList = await Util.GetEquipmenByLevel('Segment');
            // 设备
            this.equipmentList = await Util.GetEquipmenByLevel('Unit');
        },
        async handleExport() {
            const { startTime, endTime } = this.searchParams
            let params = {
                ...this.searchParams,
                startTime: (startTime || this.startTime),
                endTime: (endTime || this.endTime)
            };
            let resp = await ExportNoticeRecordToExcel({ ...params });
            let blob = new Blob([resp], {
                type: 'application/octet-stream'
            });
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            var link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.download = `安灯通知记录${formattedDateTime}.xls`; // 修正模板字符串语法
            link.click();
            //释放内存
            window.URL.revokeObjectURL(link.href);
        },
        // 获取告警类型列表
        async getalarmTypeList() {
            const res = await getAlarmTypeTreetList({});
            this.alarmTypeList = []
            const { success, response } = res || {};
            if (response && success) {
                response.forEach(e => {
                    this.alarmTypeList.push(e)
                    const { children } = e
                    if (children && children.length) {
                        children.forEach(i => {
                            this.alarmTypeList.push(i)
                        })
                    }
                })
            }
        },
        // 获取大类列表
        async getAlarmTypeList() {
            const res = await getAlarmTypeRootList({});
            const { success, response } = res || {};
            if (success) {
                this.alarmTypeRootList = response || [];
            } else {
                this.alarmTypeRootList = [];
            }
        },
        // 获取表格组件的分页信息
        selectePages(v) {
            this.pageOptions.pageCount = v.pageCount;
            this.pageOptions.pageSize = v.pageSize;
            this.getDataList();
        },
        // 获取表格的勾选数据
        selectedItems(item) {
            this.selectedList = [...item];
        },
        // 操作栏按钮
        tableClick(item, type) {
            switch (type) {
                // 编辑
                case 'edit':
                    this.operaClick(item);
                    break;
                // 删除
                case 'delete':
                    this.deleteId = item?.ID
                    this.sureDelete()
                    break;
                default:
                    break;
            }
        },
        // 获取全部表格数据
        async getDataList() {
            this.loading = true;
            const { start, end } = this.searchParams;
            console.log(start, end);
            let params = {
                ...this.searchParams,
                start: start || this.start,
                end: end || this.end,
                pageIndex: this.pageOptions.pageCount,
                pageSize: this.pageOptions.pageSize
            };
            const res = await getNoticeRecordList(params);
            const { success, response } = res || {};
            const { data, dataCount, page } = response || {};
            this.desserts = []
            if (success && data) {
                const arr = data || [];
                arr.forEach(e => {
                    this.desserts.push({ ...e, ModifyDate: e.Status == "SUCESS" ? e.ModifyDate : '' })
                });
                this.pageOptions.total = dataCount;
                this.pageOptions.page = page;
            } else {
                this.desserts = [];
            }
            this.loading = false;
        },
        // 查询数据
        searchForm(v) {
            this.searchParams = v;
            this.getDataList();
        },
        // 新增
        operaClick(o) {
            this.operaObj = o || {};
            this.$refs.updateDialog.dialog = true;
        },
        // 批量删除
        sureItems() {
            if (this.selectedList.length > 0) {
                this.deleteId = ''
                this.sureDelete();
            } else {
                this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'error' });
            }
        },
        // 删除二次确认
        sureDelete() {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    const params = [];
                    if (this.deleteId) {
                        params.push(this.deleteId);
                    } else {
                        this.selectedList.forEach(e => {
                            params.push(e.ID);
                        });
                    }
                    const res = await DeleteNoticeRecord(params);
                    this.selectedList = [];
                    this.deleteId = '';
                    const { success, msg } = res;
                    if (success) {
                        this.pageOptions.pageCount = 1;
                        this.getDataList();
                        this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 根据子组件返回来值
        handlePopup(type, data) {
            this.getDataList();
            // switch (type) {
            //     case 'refresh':
            //         this.getDataList();
            //         break;
            //     case 'detail':
            //         this.receivedorderid = data?.ID
            //         this.$refs.materailDetailDialog.dialog = true;
            //         break;
            //     default:
            //         break;
            // }
        }
    }
};
</script>
<style lang="scss" scoped>
.line-side-view {
    display: flex;

    .line-side-main {
        flex: 1;
        width: 100%;

        .v-data-table {
            width: 100%;
        }
    }
}
</style>
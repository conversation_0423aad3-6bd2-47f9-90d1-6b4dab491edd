<template>
  <div class="bigBox_center_l_t_b">
    <div class="quan1">
      <div class="quan_z">
        <div class="twoBox_left"></div>
        <div
          class="quan_z1"
          style="color:#3cc75c;font-size:20px;"
        >10</div>
        <div
          class="quan_z2"
          style="color:#fff;"
        >日产量</div>
      </div>
    </div>

    <div class="quan1">
      <div class="quan_z">
        <div class="twoBox_center"></div>
        <div
          class="quan_z3"
          style=""
        ><span style="color: #aaffff;font-size:20px;">300</span></div>
        <div
          class="quan_z4"
          style="color:#fff;"
        >月产量</div>
      </div>
    </div>

    <div class="quan1">
      <div class="quan_z">
        <div class="twoBox_right"></div>
        <div class="quan_z5"><span style="color: #ffb017;font-size:20px;">3000</span></div>
        <div
          class="quan_z6"
          style="color:#fff;"
        >年产量</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    id1: {
      type: String,
      default: ''
    },
    list: {
      type: Array,
      default: () => []
    },
    styles: {
      type: Object,
      default: () => { }
    },
  },
  data: () => ({
    chartBar: null,
    // styless: {
    //   width: '100%',
    //   height: '100%',
    //   backgroundImage: 'url("https://img1.baidu.com/it/u=2756664614,3290369440&fm=253&fmt=auto&app=138&f=JPEG?w=753&h=500")',
    //   backgroundSize: '100%',
    // }
  }),
  created() {
    console.log(this.styles, 'style');
    // this.styless.backgroundImage = this.styles.backgroundImage
  },
  methods: {

  },
}
</script>
<style lang="scss" scoped>
.bigBox_center_l_t_b {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-around;
    padding-top: 1%;
    box-sizing: border-box;
}
.bigBox_center_l_b_b {
    width: 100%;
    height: 85%;
}
.bigBox_center_l_b_tab {
    width: 100%;
    height: 40px;
    margin-bottom: 10px;
    margin-top: 10px;
    display: flex;
}
.btnbox {
    width: 70px;
    height: 30px;
    font-size: 14px;
    color: skyblue;
    line-height: 30px;
    text-align: center;
    cursor: pointer;
    border-radius: 5px;
    box-shadow: 0px 0px 10px 0px #fff;
}
.active {
    font-size: 18px;
    border-bottom: 2px solid #ffb017;
    font-weight: bold;
    color: #ffb017;
}
@keyframes rotation {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}
.quan1 {
    width: 11.5rem;
    height: 11.5rem;
    position: relative;
    box-sizing: border-box;
}
.quan_z {
    width: 100%;
    height: 100%;
    position: absolute;
    top: -10%;
    transform: translate(0%, 20%);
    background: url(../image/bj11.png) no-repeat 0 0;
    background-size: 100% 100%;
}
.twoBox_left,
.twoBox_center,
.twoBox_right {
    width: 100%;
    height: 100%;
}
.twoBox_left {
    background: url(../image/bj9.png) no-repeat 0 0;
    background-size: 100% 100%;
    animation: rotation 4.5s linear infinite;
    position: relative;
}
.twoBox_center {
    background: url(../image/bj7.png) no-repeat 0 0;
    background-size: 100% 100%;
    animation: rotation 4.5s linear infinite;
    position: relative;
}
.twoBox_right {
    background: url(../image/bj8.png) no-repeat 0 0;
    background-size: 100% 100%;
    animation: rotation 4.5s linear infinite;
    position: relative;
}
.quan_z1 {
    position: absolute;
    top: 58px;
    font-size: 20px;
    color: #aaffff;
    font-weight: bold;
    width: 100%;
    text-align: center;
}
.quan_z2 {
    position: absolute;
    top: 90px;
    font-size: 18px;
    color: #aaffff;
    font-weight: bold;
    width: 100%;
    text-align: center;
}
.quan_z3 {
    position: absolute;
    top: 58px;
    font-size: 20px;
    color: #3cc75c;
    font-weight: bold;
    width: 100%;
    text-align: center;
    color: #fa1111;
}
.quan_z4 {
    position: absolute;
    top: 90px;
    font-size: 18px;
    color: #3cc75c;
    font-weight: bold;
    width: 100%;
    text-align: center;
}
.quan_z5 {
    position: absolute;
    top: 58px;
    font-size: 20px;
    color: #3cc75c;
    font-weight: bold;
    width: 100%;
    text-align: center;
    color: #fa1111;
}
.quan_z6 {
    position: absolute;
    top: 90px;
    font-size: 18px;
    color: #3cc75c;
    font-weight: bold;
    width: 100%;
    text-align: center;
}
.bigBox_center_l_bnew_t {
    width: 100%;
    height: 19.5%;
    display: flex;
    justify-content: space-around;
    margin-bottom: 10px;
}
.bigBox_center_l_bnew_t_b {
    width: 24%;
    height: 100%;
    border: 1px solid #4472c4;
}
</style>
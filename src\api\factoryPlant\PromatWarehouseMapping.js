import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM';

// 获取分页列表
export function getPageList(data) {
    const api = '/api/PromatWarehouseMapping/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取全部列表
export function getList(data) {
    const api = '/api/PromatWarehouseMapping/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取详情
export function getEntity(id) {
    const api = `/api/PromatWarehouseMapping/GetEntity/${id}`;
    return getRequestResources(baseURL, api, 'get');
}

// 保存表单
export function saveForm(data) {
    const api = '/api/PromatWarehouseMapping/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}

// 删除记录
export function deleteEntities(data) {
    const api = '/api/PromatWarehouseMapping/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取车间信息
export function getAreaList() {
    const api = '/api/Equipment/GetListByLevel?key=Area';
    return getRequestResources(baseURL, api, 'post', { key: 'Area' });
}

// 获取产线信息
export function getLineList() {
    const api = '/api/Equipment/GetListByLevel?key=Line';
    return getRequestResources(baseURL, api, 'post', { key: 'Line' });
}

// 获取仓库信息
export function getWarehouseList() {
    const api = '/api/Equipment/GetListByLevel?key=Storage';
    return getRequestResources(baseURL, api, 'post', { key: 'Storage' });
}

// 获取物料信息
export function getMaterialList(data) {
    const api = '/api/Material/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
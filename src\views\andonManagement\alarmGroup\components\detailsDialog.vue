<template>
    <!-- 告警升级规则 -->
    <v-dialog v-model="dialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ operaObj.ID ? '编辑接警组详情' : '新增接警组详情' }}
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" class="mt-8 mb-2">
                    <v-row>
                        <!-- 接警组名称 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.GroupName" disabled label="接警组名称" required dense outlined />
                        </v-col>
                        <!-- 接警组编码 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.GroupCode" disabled label="接警组编码" required dense outlined />
                        </v-col>
                        <!-- 岗位 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.PostCode"
                                :items="dutyList"
                                item-text="Fullname"
                                item-value="Encode"
                                label="岗位"
                                return-object
                                dense
                                outlined
                            >
                            </v-select>
                        </v-col>
                        <!-- :是否需要当班 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.IsNeedOn"
                                :rules="rules.IsNeedOn"
                                :items="needOnTypes"
                                item-text="ItemName"
                                item-value="ItemValue"
                                label="是否需要当班"
                                dense
                                outlined
                            ></v-select>
                        </v-col>
                        <!-- :是否关联产线 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.IsRelatedPline"
                                :rules="rules.IsRelatedPline"
                                :items="needOnTypes"
                                item-text="ItemName"
                                item-value="ItemValue"
                                label="是否关联产线"
                                dense
                                outlined
                            ></v-select>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn color="normal" @click="closeForm">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { AlarmgroupPostSaveForm } from '@/api/andonManagement/alarmGroup.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        needOnTypes: {
            type: Array,
            default: () => []
        },
        dutyList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            checkbox: true,
            valid: true,
            dialog: false,
            form: {
                ID: '',
                GroupName: '',
                GroupCode: '',
                PostCode: '',
                PostName: '',
                IsNeedOn: 1,
                IsRelatedPline: 1
            },
            rules: {
                Duty: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                PostCode: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                IsNeedOn: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                IsRelatedPline: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            },
            departmentData: [],
            typeRootList: [],
            typeChildList: []
        };
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal) {
                    for (const key in this.form) {
                        if (Object.hasOwnProperty.call(this.form, key)) {
                            this.form[key] = this.operaObj[key];
                        }
                    }
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        //关闭
        closeForm() {
            this.dialog = false;
        },
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                let infos = {}
                const { PostCode } = this.form;
                if (PostCode?.Encode) {
                    const { Encode, Fullname } = PostCode
                    infos = { PostCode: Encode, PostName: Fullname };
                }
                const res = await AlarmgroupPostSaveForm({ ...this.form, ...infos });
                const { success, msg } = res;
                if(success){
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    this.$refs.form.reset();
                    this.$emit('handlePopup', 'refresh');
                    if (this.operaObj.ID || this.checkbox) {
                        this.closeForm()
                    }
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>
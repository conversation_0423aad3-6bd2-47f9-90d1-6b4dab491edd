<template>
    <div class="back-log">
        <TreeView :items="treeData" title="" :activeKey="activeKey" @clickClassTree="clickClassTree"></TreeView>
        <div class="back-log-content">
            <v-row class="mt-2 pb-2" align="center" no-gutters>
                <v-col :cols="12" :lg="2" class="pr-4">
                    <v-menu offset-y transition="scale-transition">
                        <template v-slot:activator="{ on, attrs }">
                            <v-text-field v-bind="attrs" v-on="on" hide-details readonly outlined v-model="tomonth"
                                dense></v-text-field>
                        </template>
                        <v-date-picker no-title @change="getMonth" v-model="selectMonth" type="month"></v-date-picker>
                    </v-menu>
                </v-col>
                <v-col :cols="12" :lg="2" class="pr-4">
                    <v-select v-model="type" hide-details outlined dense :items="['点检', '保养']"></v-select>
                </v-col>
                <v-col :cols="12" :lg="2">
                    <v-btn color="primary" @click="getdata">{{ $t('GLOBAL._CX') }}</v-btn>
                </v-col>
            </v-row>
            <v-sheet class="mt-2" color="transparent" v-if="!showGlobal" tile rounded>
                <v-card elevation="4" min-height="82vh">
                    <v-slide-y-transition class="py-0" group>
                        <template v-for="(task, i) in items">

                            <v-list-item class="list-item" :key="`${i}-${task.Name}`">
                                <v-list-item-action>
                                    <v-checkbox v-model="task.Done" :color="(task.Done && 'grey') || 'primary'">
                                    </v-checkbox>
                                </v-list-item-action>
                                <v-row class="my-1 list-item-content" no-gutters>
                                    <div>
                                        <a :class="(task.Done && 'grey--text') || 'primary--text'
                                            " @click="openEditDialog(task)">
                                            检查项目xx</a>
                                    </div>

                                    <div>目测{{ task.Owner }}</div>
                                    <div>负责人 {{ task.Owner }}</div>
                                    <div>2023/1/1: 7:00~9:00 {{ task.Owner }}</div>
                                </v-row>
                                <v-spacer></v-spacer>
                                <div>
                                    <v-scroll-x-transition>
                                        <div>
                                            <v-icon v-show="task.Done" color="success">
                                                mdi-check
                                            </v-icon>
                                        </div>
                                    </v-scroll-x-transition>
                                </div>
                            </v-list-item>
                            <v-divider :key="`${i}-divider`"></v-divider>
                        </template>
                    </v-slide-y-transition>
                </v-card>
                <!-- <keep-alive exclude="name">
                        <component :is="currentcomponent" :currentR="currentR" />
                    </keep-alive> -->
            </v-sheet>
            <overview v-if="showGlobal" :headers="headers" @openDetail="openDetail" :totalDate="totalDate"
                :tableList="tableList" ref="overview" />
        </div>
        <v-dialog scrollable persistent v-model="isShowDetailPopup" width="55%">
            <DetailPopup @closePopup="isShowDetailPopup = false" :isPointCheck="isPointCheck" :id="id"
                v-if="isShowDetailPopup" />
        </v-dialog>
    </div>
</template>

<script>
import overview from './components/overview.vue'
import DetailPopup from './components/detailPopup.vue'
import { getTreeData, getPointCheckList, getMaintainList } from './service.js'
export default {
    components: {
        overview,
        DetailPopup
    },
    data() {
        return {
            isShowDetailPopup: false,
            id: '',
            totalDate: 0,
            type: "点检",
            isPointCheck: true,
            equipCodeList: [],
            activeKey: '',
            treeData: [],
            showGlobal: true,
            selectTodoType: 0,
            selectMonth: '',
            tomonth: (new Date().getFullYear()) + "-" + (new Date().getMonth() + 1).toString().padStart(2, '0'),
            today: new Date().getDate() - 1,
            days: 0,
            todotype: [
                { name: "点检" },
                { name: "保养" },
                { name: "维修" },
                { name: "巡检" },
            ],
            headers: [
                { text: '设备', value: 'DeviceCode', width: 120 }
            ],
            tableList: [],
            items: [
                {
                    shift: "白班",
                    position: "内磁",
                    device: "清洗机",
                    staff: "产线",
                    skill: "电工高级",
                    source: "借调",
                    WOCode: "PSAd001",
                    starttime: "07:00",
                    endtime: "19:00",
                    duration: "",
                },
                { title: "产线/设备" },
                { title: "产线/设备" },
                { title: "产线/设备" },
                { title: "产线/设备" },
            ],
        }
    },
    created() {
        this.init()
        this.getTree()
    },
    methods: {
        openDetail(id, type) {
            this.id = id
            this.isPointCheck = type == '点检'
            this.isShowDetailPopup = true
        },
        clickClassTree(val) {
            this.equipCodeList = []
            this.getEquipCodeList([val])
            this.getdata()
        },
        initHeader(nv) {
            this.headers = [
                { text: '设备', value: 'DeviceCode', width: 120 }
            ]
            let dateArr = nv.split('-')
            let date = new Date(dateArr[0], dateArr[1], 0).getDate()
            this.totalDate = date
            for (let i = 1; i < date + 1; i++) {
                this.headers.push({ text: i, value: i.toString(), sortable: false })
            }
        },
        async getdata() {
            this.tableList = []
            this.initHeader(this.tomonth)
            let dateArr = this.tomonth.split('-')
            let params = {
                EquipmentCodeList: this.equipCodeList,
                EndDate: this.tomonth + "-" + new Date(dateArr[0], dateArr[1], 0).getDate(),
                StartDate: this.tomonth + '-01'
            }
            let resp = null
            if (this.type == '点检') {
                resp = await getPointCheckList(params)
            } else {
                resp = await getMaintainList(params)
            }
            let dataList = resp.response
            let equipList = [...new Set(dataList.map(item => item.DeviceCode))]
            equipList.forEach(item => {
                let obj = {
                    DeviceCode: item
                }
                dataList.forEach(itm => {
                    if (itm.DeviceCode == item) {
                        let date = new Date(this.type == '点检' ? itm.McStardate : itm.StartTime).getDate()
                        let status = this.type == '点检' ? itm.McStatus : itm.Status
                        obj[date] = `${status == '0' ? 'x' : '√'}_${itm.ID}_${this.type}`

                    }
                })
                this.tableList.push(obj)
            })
        },
        async getTree() {
            let resp = await getTreeData()
            this.treeData = resp.response
            this.getEquipCodeList(this.treeData)
            this.getdata()
        },
        getEquipCodeList(list) {
            // "Unit"
            list.forEach(item => {
                if (item.extendField === 'Unit') {
                    this.equipCodeList.push(item.value)
                }
                if (item.children && item.children.length) {
                    this.getEquipCodeList(item.children)
                }
            });
        },
        getMonth(val) {
            if (!val) return false
            // this.tomonth = val.split('-')[1]
            this.tomonth = val
        },
        init() {
            this.days = this.mGetDate();
        },
        mGetDate() {
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var d = new Date(year, month, 0);
            return d.getDate();
        },
    }
}
</script>
<style lang="scss" scoped>
.back-log {
    display: flex;
}

.back-log-content {
    width: 100%;
    overflow: hidden;
}
</style>

import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_TRACE'
//工序映射

//查询列表
export function getSapMesRoutingList(data) {
    const api =  '/trace/SapMesRouting/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑
export function SapMesRoutingSaveForm(data) {
    const api =  '/trace/SapMesRouting/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除
export function DeleteSapMesRouting(data) {
    const api =  '/trace/SapMesRouting/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
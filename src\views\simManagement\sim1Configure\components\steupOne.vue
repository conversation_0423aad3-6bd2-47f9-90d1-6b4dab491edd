<template>
  <div>
    <v-form ref="form">
      <v-row class="ma-4">
        <span style="color: red;">*</span>
        <v-col
          class="py-0 px-3"
          cols="12"
          sm="6"
          md="4"
        >
          <v-select
            :disabled="dialogType == 'edit'"
            v-model="form.Moudelname"
            :items="dutyList"
            item-text="Fullname"
            item-value="Encode"
            :label="$t('$vuetify.dataTable.SIM_CONFIG.Moudelname')"
            return-object
            dense
            outlined
            :rules="[v => !!v || $t('GLOBAL._MANDATORY')]"
          >
          </v-select>
        </v-col>
        <span style="color: red;">*</span>
        <v-col
          class="py-0 px-3"
          cols="12"
          sm="6"
          md="4"
        >
          <v-text-field
            v-model="form.Olinetit"
            outlined
            dense
            :label="$t('$vuetify.dataTable.SIM_CONFIG.Olinetit')"
            :rules="[v => !!v || $t('GLOBAL._MANDATORY')]"
          >
          </v-text-field>
        </v-col>

      </v-row>
      <div style="font-size: 16px;margin-bottom: 10px;margin-left: 30px;"> <span style="color: red;">*</span>上传背景图片：</div>

      <v-row class="ma-4">
        <v-col
          class="py-0 px-3"
          cols="12"
          sm="6"
          md="4"
        >
          <el-upload
            ref="xiangqtu"
            class="upload-demo"
            style="width:800px"
            accept="image/jpeg,image/gif,image/png"
            :action="'https://sim.fhtdchem.com'+'/simapi/PageConfig/Upload'"
            :before-upload="beforeAvatarUpload1"
            multiple
            :on-preview="handlePreview"
            :on-remove="handleRemove4"
            :on-success="handleAvatarSuccess4"
            :file-list="deailFileList"
            list-type="picture-card"
            :rules="[v => !!v || $t('GLOBAL._MANDATORY')]"
          >
            <i
              slot="default"
              class="el-icon-plus"
            />
            <div
              slot="file"
              slot-scope="{file}"
            >
              <img
                :src="file.url"
                alt=""
                class="el-upload-list__item-thumbnail"
              >
              <span class="el-upload-list__item-actions">
                <!-- <span
                  class="el-upload-list__item-delete"
                  @click="handlePreview(file)"
                >
                  <i class="el-icon-zoom-in" />
                </span> -->
                <span
                  class="el-upload-list__item-delete"
                  @click="handleRemove4(file,deailFileList)"
                >
                  <i class="el-icon-delete" />
                </span>
              </span>
            </div>
            <!-- <el-button size="small" type="primary">点击上传</el-button> -->
          </el-upload>
        </v-col>
      </v-row>
      <!-- <SearchForm
        class="mt-1"
        :show-from="true"
        :searchinput="searchInputs"
      /> -->
    </v-form>
  </div>
</template>
<script>
import { getEditEntityByCodeone, getImageUel } from '@/api/simConfig/simconfignew.js';
import { configUrl } from '@/config';
import { isThisHour } from 'date-fns';
export default {
  props: {
    OneId: {
      type: String,
      default: ''
    },
    dialogType: {
      type: String,
      default: ''
    },
    oneList1: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      baseURL: configUrl[process.env.VUE_APP_SERVE].baseURL_DFM,
      deailFileList: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      filterText: '',
      form: {
        Moudelname: '',
        Olinetit: '',
        img: '',
      },
      dutyList: [
        {
          Fullname: 'SIM1',
          Encode: 'SIM1'
        },
        {
          Fullname: 'SIM2',
          Encode: 'SIM2'
        },
        {
          Fullname: 'SIM3',
          Encode: 'SIM3'
        },
        {
          Fullname: 'SIM4',
          Encode: 'SIM4'
        },
      ],
      rules: {
        Moudelname: [v => !!v || this.$t('GLOBAL.Moudelname')],
        Olinetit: [v => !!v || this.$t('GLOBAL.Olinetit')],
      },
    };
  },

  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    if (this.dialogType == 'add') {
      this.form.Moudelname = ''
      this.form.Olinetit = ''
      this.form.img = ''
    }
  },
  mounted() {
    if (this.dialogType == 'add') {
      this.form.Moudelname = ''
      this.form.Olinetit = ''
      this.form.img = ''
    }
    if (this.dialogType == 'edit') {
      this.queryEditListone()
    }
  },
  methods: {
    beforeAvatarUpload1(file) {
      const isLtSize = file.size / 1024 < 3072
      if (!isLtSize) {
        this.$message.error('上传图片大小不能超过3M')
        return false
      }
      return true
    },
    handlePreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleRemove4(file, deailFileList) {
      for (let i = 0; i < deailFileList.length; i++) {
        if (file.name === deailFileList[i].name) {
          deailFileList.splice(i, 1)
          this.deailFileList = deailFileList
        }
      }
    },
    handleAvatarSuccess4(file) {
      this.form.img = file.response.fileName
      // 图片上传成功
      this.deailFileList.push({
        name: file.response.fileName,
        url: file.response.fileUrl,
      })
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleCheck(data) {
      this.selectName = data.name
      this.form.selectCode = data.id
    },
    async queryEditListone() {
      // this.deailFileList = []
      // const params = {
      //   code: this.OneId
      // }
      // const res = await getEditEntityByCodeone(params);

      // if (res.success) {
      //   this.form.Moudelname = res.response.Moudelname
      //   this.form.Olinetit = res.response.Olinetit
      //   this.form.selectCode = res.response.Simlevel
      //   this.selectName = res.response.Simlevel
      //   if (res.response.Baroundimg) {
      //     const res2 = await getImageUel(res.response.Baroundimg);
      //     this.form.img = res.response.Baroundimg
      //     this.deailFileList.push({
      //       name: res.response.Baroundimg,
      //       url: res2.response
      //     })

      //   }
      this.form.Moudelname = this.oneList1.Moudelname
      this.form.Olinetit = this.oneList1.Olinetit
      this.form.selectCode = this.oneList1.Simlevel
      this.selectName = this.oneList1.Simlevel
      if (this.oneList1.Baroundimg) {
        const res2 = await getImageUel(this.oneList1.Baroundimg);
        this.form.img = this.oneList1.Baroundimg
        this.deailFileList.push({
          name: this.oneList1.Baroundimg,
          url: res2.response
        })

      }
    },
    save() {
      if (this.dialogType == 'edit') {
        // this.queryEditListone()

      }
      this.$emit('savesteupOne', this.form, this.deailFileList)
    },
    save1() {
      this.$refs.form.reset();
      this.deailFileList = []
    },
    save2() {
      if (!this.$refs.form.validate()) return false;
    }
  },
}
</script>
<style scoped>
/deep/ .upload-demo {
    display: flex;
}
</style>
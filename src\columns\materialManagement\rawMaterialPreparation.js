// dictionary: true, isEditCell: true
export const rawMaterialPreparationColum = [
    { text: '序号', value: 'Index', width: '60px' },
    { text: '备料单号', value: 'PrepareOrderid', width: '140px' },
    { text: '产线', value: 'LineCode', width: '160px', dictionary: true },
    { text: '工单号', value: 'WoCode', width: '160px' },
    { text: '备料仓库', value: 'PrepareWarehouseId', width: '140px', dictionary: true },
    { text: '目标仓库', value: 'TargetWarehouseId', width: '146px', dictionary: true },
    // { text: '料车停车位', value: 'Materialtruck', width: '140px' },
    { text: '状态', value: 'Status', width: '140px', dictionary: true },
    { text: '配送时间', value: 'DeliveryDate', width: '160px' },
    { text: '备料时间', value: 'PrepareDate', width: '160px' },
    { text: '备料人', value: 'Prepareuserid', width: '146px' },
    { text: '确认人', value: 'Confirmuserid', width: '140px' },
    { text: '确认时间', value: 'Confirmdate', width: '160px' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '220px'
    }
];

export const rawMaterialPreparationDetailColum = [
    // { text: '序号', value: 'Index', width: '100px' },
    { text: '物料编码', value: 'MaterialCode', width: '160px' },
    { text: '物料名称', value: 'Materialname', width: '160px' },
    { text: '物料类型', value: 'Materialtype', width: '120px' },
    { text: '状态', value: 'Status', width: '80px', dictionary: true },
    { text: '批次', value: 'Batchcode', width: '140px', },
    { text: '追溯批次', value: 'Backbatchcode', width: '140px' },
    { text: '数量', value: 'Num', width: '160px', semicolonFormat: true },
    { text: '计量单位', value: 'Unit', width: '140px' },
    // { text: '入库时间', value: 'Producedate', width: '160px' },
    { text: '生产日期', value: 'BroduceDate', width: '160px' },
    // { text: '失效日期', value: 'Producedate', width: '160px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '100px'
    }
];
export const rawMaterialPreparationBOM = [
    // { text: '序号', value: 'Index', width: '100px' },
    { text: '物料号', value: 'MaterialCode', width: '160px' },
    { text: '物料描述', value: 'MaterialName', width: '220px' },
    { text: 'BOM单量', value: 'BomNum', width: '120px', semicolonFormat: true },
    { text: '工单需求数量', value: 'DemandNum', width: '160px', semicolonFormat: true },
    { text: '备料数量', value: 'quantityInStorage', width: '140px', semicolonFormat: true },
    { text: '单位', value: 'BomUnit', width: '100px' },
    {
        text: '',
        align: 'center',
        value: 'noActions',
        width: ''
    }
];

export const preparationWorkorderColum = [
    // { text: '序号', value: 'Index', width: '100px' },
    { text: '工单号', value: 'WoCode', width: '150px' },
    { text: '物料号名称', value: 'ProductionName', width: '190px' },
    { text: '数量', value: 'PlanQty', width: '80px', semicolonFormat: true },
    { text: '班次', value: 'Shift', width: '80px' },
    { text: '班组', value: 'Team', width: '80px' },
    // { text: '计划日期', value: 'PlanDate', width: '140px' },
    { text: '计划开始时间', value: 'PlanStartTime', width: '160px' },
    { text: '计划结束时间', value: 'PlanEndTime', width: '160px' },
    {
        text: '',
        align: 'center',
        value: 'noActions',
        width: ''
    }
];
<!-- eslint-disable vue/valid-v-slot -->
<template>
    <v-container id="regular-tables" tag="section" class="px-0">
        <v-data-table height="82vh" :headers="headers" :search="search" :items="tableList" :items-per-page="99999"
            class="elevation-1" hide-default-footer>
            <template v-for="(itm, index) in slotList" v-slot:[itm.slotName]="{ item }">
                <span @click="openDetail(item[itm.num])"
                    :style="`cursor: pointer;color:${showContent(item[itm.num]) == 'x' ? 'red' : '#3dcd58'}`"
                    :key="index">{{
            showContent(item[itm.num])
        }}</span>
            </template>
        </v-data-table>
    </v-container>
</template>
<script>
export default {
    props: {
        totalDate: {
            type: Number,
            default: 0
        },
        headers: {
            type: Array,
            default: () => []
        },
        tableList: {
            type: Array,
            default: () => []
        },
    },
    data: () => ({
        toggle_datarange: "week",
        search: "",
    }),
    computed: {
        slotList() {
            let list = []
            for (let i = 1; i < this.totalDate + 1; i++) {
                list.push({
                    num: i,
                    slotName: `item.` + i
                })
            }
            return list
        }
    },
    methods: {
        showContent(val) {
            if (!val) return ''
            return val.split("_")[0]
        },
        openDetail(data) {
            if (!data) return false
            let id = data.split('_')[1]
            let type = data.split('_')[2]
            this.$emit('openDetail', id, type)
            console.log("data=======", data)
        }
    }
};
</script>
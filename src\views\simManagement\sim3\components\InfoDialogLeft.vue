<template>
  <v-card>
    <v-card-title
      class="headline primary lighten-2"
      primary-title
    >
      {{ editItemObjLeft.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ') }}
    </v-card-title>
    <v-card-text>
      <v-form
        ref="form"
        v-model="valid"
      >
        <v-row class="mt-5">
          <v-col
            :cols="12"
            :lg="6"
            class="pt-0 pb-0"
          >
            <v-autocomplete
              v-model="form.Host"
              :items="staffListByDepart"
              item-text="UserName"
              item-value="LoginName"
              clearable
              dense
              outlined
              label="主持人"
              placeholder="主持人"
            >
            </v-autocomplete>
          </v-col>
          <v-col
            :cols="12"
            :lg="6"
            class="pt-0 pb-0"
          >
            <v-autocomplete
              v-model="form.Director"
              :items="staffListByDepart"
              item-text="UserName"
              item-value="LoginName"
              clearable
              dense
              outlined
              label="负责人"
              placeholder="负责人"
            >
            </v-autocomplete>
          </v-col>
          <v-col
            :cols="12"
            :lg="6"
            class="pt-0 pb-0"
          >
            <v-menu
              :close-on-content-click="true"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              max-width="290px"
              min-width="290px"
            >
              <template #activator="{ on, attrs }">
                <v-text-field
                  v-model="form.CheckDate"
                  :clearable="true"
                  outlined
                  dense
                  label="检查日期"
                  readonly
                  v-bind="attrs"
                  v-on="on"
                >
                </v-text-field>
              </template>
              <v-date-picker
                v-model="form.CheckDate"
                placeholder="检查日期"
                :locale="locale"
              >
              </v-date-picker>
            </v-menu>
          </v-col>
          <v-col
            :cols="12"
            :lg="6"
            class="pt-0 pb-0"
          >
            <v-menu
              :close-on-content-click="true"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              max-width="290px"
              min-width="290px"
            >
              <template #activator="{ on, attrs }">
                <v-text-field
                  v-model="form.PlanCompleteDate"
                  :clearable="true"
                  outlined
                  dense
                  label="计划完成日期"
                  readonly
                  v-bind="attrs"
                  v-on="on"
                >
                </v-text-field>
              </template>
              <v-date-picker
                v-model="form.PlanCompleteDate"
                placeholder="计划完成日期"
                :locale="locale"
              >
              </v-date-picker>
            </v-menu>
          </v-col>
          <v-col
            :cols="12"
            :lg="6"
            class="pt-0 pb-0"
          >
            <v-select
              :items="closeList"
              label="检查结果"
              item-text="ItemName"
              item-value="ItemValue"
              required
              dense
              outlined
              v-model="form.CheckResult"
            ></v-select>
          </v-col>
          <v-col
            :cols="12"
            :lg="12"
            class="pt-0 pb-0"
          >
            <v-textarea
              v-model="form.CheckItem"
              clearable
              label="检查指标"
              outlined
              dense
              required
            ></v-textarea>
          </v-col>
          <v-col
            :cols="12"
            :lg="12"
            class="pt-0 pb-0"
          >
            <v-textarea
              v-model="form.CheckLocation"
              clearable
              label="检查区域"
              outlined
              dense
              required
            ></v-textarea>
          </v-col>
          <v-col
            :cols="12"
            :lg="12"
            class="pt-0 pb-0"
          >
            <v-textarea
              v-model="form.CheckContent"
              clearable
              label="检查内容"
              outlined
              dense
              required
            ></v-textarea>
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
    <v-divider></v-divider>

    <v-card-actions>
      <!-- <v-spacer></v-spacer> -->
      <v-checkbox
        disabled
        class="mr-auto"
        v-model="isChecked"
        :label="$t('GLOBAL._QDBGBTC')"
      ></v-checkbox>
      <v-btn
        color="primary"
        @click="submitForm"
      >{{ $t('GLOBAL._QD') }}</v-btn>
      <v-btn
        color="normal"
        @click="closePopupLeft"
      >{{ $t('GLOBAL._GB') }}</v-btn>
    </v-card-actions>
  </v-card>
</template>

<script>
import { getStaff, getStaffByDepartId, saveTestFormLeft } from '../service';
import { EquipmentGetPageList } from '@/api/common.js';
import { mapGetters } from 'vuex';
export default {
  props: {
    editItemObjLeft: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      staffListByDepart: [],
      closeList: [
        {
          ItemName: '正常',
          ItemValue: '1',
        },
        {
          ItemName: '异常',
          ItemValue: '0',
        }
      ],
      valid: false,//校验
      isChecked: true,//确定并关闭
      form: {
        CheckDate: '',
        Host: '',
        CheckItem: '',
        CheckLocation: '',
        CheckContent: '',
        CheckResult: '',
        Director: '',
        PlanCompleteDate: '',
      },
    };
  },
  computed: {
    ...mapGetters(['getUserinfolist']),
    locale() {
      return this.$store.state.app.locale || 'zh';
    },
  },
  created() {
    console.log(this.getUserinfolist[0].Departmentid, '8888888888888');
    this.getTeamList1()
    if (this.editItemObjLeft && this.editItemObjLeft.ID) {
      for (const key in this.form) {
        this.form[key] = this.editItemObjLeft[key];
      }
    }
  },
  methods: {
    async getTeamList1(val) {
      let resp = await getStaffByDepartId({ Departmentid: this.getUserinfolist[0].Departmentid })
      if (resp && resp.response) {
        this.staffListByDepart = resp.response
      } else {
        this.staffListByDepart = []
      }
    },
    //确定
    async submitForm() {
      let ID = ''
      if (this.editItemObjLeft.ID) {
        ID = this.editItemObjLeft.ID
      } else {
        ID = ''
      }
      let params = {
        "CheckDate": this.form.CheckDate,
        "Host": this.form.Host,
        "CheckItem": this.form.CheckItem,
        "CheckLocation": this.form.CheckLocation,
        "CheckContent": this.form.CheckContent,
        "CheckResult": this.form.CheckResult,
        "Director": this.form.Director,
        "PlanCompleteDate": this.form.PlanCompleteDate,
        "ID": ID,
      }
      console.log(params, 'paramsparamsparams');
      let resp = await saveTestFormLeft({ ...params });
      if (resp.success) {
        this.$store.commit('SHOW_SNACKBAR', { text: resp.msg, color: 'success' });
        this.$emit('closePopupLeft');
      }
      // if (this.isChecked) {
      //   this.$emit('closePopupLeft');
      // }
    },
    //关闭
    closePopupLeft() {
      this.$emit('closePopupLeft');
    }
  }
};
</script>

import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Inventory2'
const baseURL2 = 'baseURL_Inventory3'
const baseDFM = 'baseURL_DFM'
export function GetEquipmentGroups(data) {
    const api = '/api/EquipmentFunctionView/GetEquipmentGroups'
    return getRequestResources(baseURL, api, 'get', data);
}
export function GetEquipmentFunctionView(data) {
    const api = '/api/EquipmentFunctionView/GetList'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function GetEquipmentProcessOrderView(data) {
    const api = '/api/EquipmentProcessOrderView/GetPageList'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function getCheckTippingType(data) {
    const api = '/api/TippingMlistView/CheckTippingType'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function GetProcessOrderView(data) {
    const api = '/api/ProcessOrderView/GetPageList'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function GetProcessOrderView2(data) {
    const api = '/api/ProcessOrderView/GetPageList2'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function GetBBatchListView(data) {
    const api = '/ppm/BBatchListView/GetList'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function GetBatchCode(data) {
    const api = `/ppm/PoProducedExecution/GetBatchCode`
    return getRequestResources(baseURL, api, 'Post', data);
}
export function GetBatchCodeByProLine(data) {
    const api = `/ppm/PoProducedExecution/GetBatchCodeByProLine`
    return getRequestResources(baseURL, api, 'Post', data);
}
export function PoProducedStart(data) {
    const api = '/ppm/PoProducedExecution/Start'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function PoProducedStop(data) {
    const api = '/ppm/PoProducedExecution/Stop'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function PoProducedHold(data) {
    const api = '/ppm/PoProducedExecution/Hold'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function PoProducedResume(data) {
    const api = '/ppm/PoProducedExecution/Resume'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function PoProducedUpdatePo(data) {
    const api = '/ppm/PoProducedExecution/UpdatePo'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function AutoReport(data,params) {
    const api = `/api/ConsumeView/AutoReport?${params}`
    return getRequestResources(baseURL, api, 'Post', data);
}
export function UpdateOrderRemark(data) {
    const api = '/ppm/PoProducedExecution/UpdateOrderRemark'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function StartNextBatch(data) {
    const api = '/ppm/PoProducedExecution/StartNextBatch'
    return getRequestResources(baseURL, api, 'Post', data);
}

//工艺长文本
export function GetCookOrderLtexts(data) {
    const api = `/ppm/PoProducedExecution/GetCookOrderLtexts`
    return getRequestResources(baseURL, api, 'post', data);
}
export function PoExecutionHistroy(data) {
    const api = '/api/PoExecutionHistroy/GetPageList'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function GetRunOrder(data, params) {
    const api = `/ppm/PoProducedExecution/GetRunOrder/${params}`
    return getRequestResources(baseURL, api, 'get', data);
}
export function GetTippingMlistView(data) {
    const api = `/api/TippingMlistView/GetPageList`
    return getRequestResources(baseURL, api, 'Post', data);
}
export function StartTipping(data) {
    const api = `/api/TippingMlistView/StartTipping`
    return getRequestResources(baseURL, api, 'Post', data);
}
export function ScanTipping(data) {
    const api = `/api/TippingMlistView/Tipping`
    return getRequestResources(baseURL, api, 'Post', data);
}
export function OverTipping(data) {
    const api = `/api/TippingMlistView/ComplateTipping`
    return getRequestResources(baseURL, api, 'Post', data);
}
export function GetBatchEntity(data) {
    const api = `/api/TippingMlistView/GetTippingStatus`
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetTippingSclist(data, params) {
    const api = `/api/TippingSclist/GetList`
    return getRequestResources(baseURL, api, 'post', data);
}
export function TippingCount(data, params) {
    const api = `/api/TippingMlistView/GetTippingCount`
    return getRequestResources(baseURL, api, 'post', data);
}

export function ConsumeViewPageList(data, params) {
    const api = `/api/ConsumeView/GetPageList`
    return getRequestResources(baseURL, api, 'post', data);
}
export function ConsumeScanSSCC(data, params) {
    const api = `/api/ConsumeView/ScanSSCC`
    return getRequestResources(baseURL, api, 'post', data);
}
export function ConsumeViewSave(data, params) {
    const api = `/api/ConsumeView/Save`
    return getRequestResources(baseURL, api, 'post', data);
}
export function ConsumeSelectList(data, params) {
    const api = `/api/ConsumeSelect/GetPageList`
    return getRequestResources(baseURL, api, 'post', data);
}
export function getConsumeViewEntity(id) {
    const api = `/api/ConsumeView/GetEntity/${id}`
    return getRequestResources(baseURL, api, 'get');
}


export function ProduceViewPageList(data, params) {
    const api = `/api/ProduceView/GetPageList`
    return getRequestResources(baseURL, api, 'post', data);
}
export function ProduceActualList(data, params) {
    const api = `/api/ProduceActualView/GetPageList`
    return getRequestResources(baseURL, api, 'post', data);
}
export function ProduceLocation(data, params) {
    const api = `/api/ProduceLocationView/GetList`
    return getRequestResources(baseURL, api, 'post', data);
}
export function ProduceSave(data, params) {
    const api = `/api/ProduceView/Produce`
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetSugarPre(data, params) {
    const api = `/api/MaterialInventory/GetSugarPre`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetSugarPreAll(data, params) {
    const api = `/api/MaterialInventory/GetSugarPreAll`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetSugarPreCount(data, params) {
    const api = `/api/MaterialInventory/GetSugarPreCount`
    return getRequestResources(baseURL2, api, 'post', data);
}

export function GetScanDataList(data, params) {
    const api = `/api/MaterialInventory/GetScanDataList`
    return getRequestResources(baseURL2, api, 'post', data);
}

export function TransferEquipment(data, params) {
    const api = `/api/MaterialInventory/TransferEquipment`
    return getRequestResources(baseURL2, api, 'post', data);
}

export function TransferContainer(data, params) {
    const api = `/api/Container/TransferContainer`
    return getRequestResources(baseURL2, api, 'post', data);
}

export function SugarPreMerges(data, params) {
    const api = `/api/MaterialInventory/SugarPreMerges`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function SugarPrePutMtr(data, params) {
    const api = `/api/MaterialInventory/SugarPrePutMtr`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function SugarPreSplit(data, params) {
    const api = `/api/MaterialInventory/SugarPreSplit`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetSugarPrePut(data, params) {
    const api = `/api/MaterialInventory/SugarPrePut`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetDestinationList(data, params) {
    const api = `/api/Container/GetDestinationList`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function StorageOpen(data, params) {
    const api = `/api/MaterialInventory/StorageOpen`
    return getRequestResources(baseURL2, api, 'post', data, true);
}
export function GetSugarPutOrPrePutMtrDP(data, params) {
    const api = `/api/MaterialInventory/SugarPutOrPrePutMtrDP`
    return getRequestResources(baseURL2, api, 'post', data);
}

//Performance
//获取进度条数据
export function GetProgressBar(data, params) {
    const api = `/api/Performance/GetProgressBar`
    return getRequestResources(baseURL2, api, 'post', data);
}
//获取事件列表
export function GetPerformancePageList(data, params) {
    const api = `/api/Performance/GetPageList`
    return getRequestResources(baseURL2, api, 'post', data);
}

//History
export function GetPerformanceHistory(data, params) {
    const api = `/api/PerformanceHistory/GetList`
    return getRequestResources(baseURL2, api, 'post', data);
}
//根据时间获取工单
export function GetPoExecutionHistroyList(data, params) {
    const api = `/api/PoExecutionHistroy/GetRunOrderList`
    return getRequestResources(baseURL2, api, 'post', data);
}
//根据设备ID获取事件原因树状下拉选
export function GetReasonTreeByEquipmentId(data, params) {
    const api = `/api/Performance/GetReasonTreeByEquipmentId/${params}`
    return getRequestResources(baseURL2, api, 'get', data);
}
//获取产线下拉列表
export function GetPerformanceLineList(data, params) {
    const api = `/api/Performance/GetLineList`
    return getRequestResources(baseURL2, api, 'post', data);
}
//获取机器下拉列表
export function GetEquipmentList(data, params) {
    const api = `/api/Performance/GetEquipmentList`
    return getRequestResources(baseURL2, api, 'post', data);
}
//获取子分类下拉列表
export function GetmyGroupList(data, params) {
    const api = `/api/Performance/GetGroupList`
    return getRequestResources(baseURL2, api, 'post', data);
}
//获取工单下拉列表
export function GetPerformancePoList(data, params) {
    const api = `/api/Performance/GetOrderList`
    return getRequestResources(baseURL2, api, 'post', data);
}
//获取原因下拉列表
export function GetPerformanceReasonList(data, params) {
    const api = `/api/Performance/GetReasonList`
    return getRequestResources(baseURL2, api, 'post', data);
}
//获取原因下拉列表
export function GetDowntimeCategroyList(data, params) {
    const api = `/api/DowntimeCategroy/GetList`
    return getRequestResources(baseURL2, api, 'post', data);
}
//PerformanceSaveEvent
export function PerformanceSaveEvent(data, params) {
    const api = `/api/Performance/SaveEvent`
    return getRequestResources(baseURL2, api, 'post', data);
}
//PerformanceCrewSize
export function PerformanceCrewSize(data, params) {
    const api = `/api/Performance/CrewSize`
    return getRequestResources(baseURL2, api, 'post', data);
}

//PerformanceSplit
export function PerformanceSplit(data, params) {
    const api = `/api/Performance/Split`
    return getRequestResources(baseURL2, api, 'post', data);
}
//PerformanceMerge
export function PerformanceMerge(data, params) {
    const api = `/api/Performance/Merge`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function PerformanceDelete(data, params) {
    const api = `/api/Downtime/Delete`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetPerformanceEventCollection(data, params) {
    const api = `/api/Performance/GetEventCollection/${params}`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function UpdatePerformanceEventCollection(data, params, params2) {
    const api = `/api/Performance/UpdateEventCollection/${params}/${params2}`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function PerformanceGetLinkList(data, params) {
    const api = `/api/Performance/GetLinkList2`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function PerformanceSaveLinkEvent(data, params) {
    const api = `/api/Performance/SaveLinkEvent`
    return getRequestResources(baseURL2, api, 'post', data);
}
//Reclassify
export function PerformanceReclassify(data, params) {
    const api = `/api/Performance/Reclassify`
    return getRequestResources(baseURL2, api, 'post', data);
}
//Reclassify
export function TimeOverlapList(data, params) {
    const api = `/api/Performance/GetTimeOverlapList`
    return getRequestResources(baseURL2, api, 'post', data);
}
// export function GetEquipmentGroups(data) {
//     const api = '/api/EquipmentFunctionView/GetEquipmentGroups'
//     return getRequestResources(baseURL, api, 'post', data);
// }
// export function GetEquipmentGroups(data) {
//     const api = '/api/EquipmentFunctionView/GetEquipmentGroups'
//     return getRequestResources(baseURL, api, 'post', data);
// }
//Tippingscan
//获取下拉选Destination
export function TippingTransferSelect(data, params) {
    const api = `/api/TippingMlistView/GetTippingTransferSelectList/${params}`
    return getRequestResources(baseURL2, api, 'get', data);
}
//保存下拉选Destination
export function SaveTippingTransferSelect(data) {
    const api = `/api/TippingMlistView/SaveSelect`
    return getRequestResources(baseURL2, api, 'post', data);
}
// dock扫码
export function GetDockScan(data) {
    const api = `/api/TippingMlistView/DockScan`
    return getRequestResources(baseURL2, api, 'post', data);
}
// 获取库存
export function GetTippingScanPage(data) {
    const api = `/api/MaterialInventory/GetPageInVentList`
    return getRequestResources(baseURL2, api, 'post', data);
}

// 获取库存2
export function GetTippingScanPage2(data) {
    const api = `/api/MaterialInventory/GetPageInVentList2`
    return getRequestResources(baseURL2, api, 'post', data);
}

//logsheets
export function GetLogSheetListByPoId(data) {
    const api = `/api/Logsheet/GetLogSheetListByPoId`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetLogSheetList(data) {
    const api = `/api/Logsheet/GetLogSheetList`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetInsertNewEntry(data) {
    const api = `/api/Logsheet/InsertNewEntry`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetLogSheetAndDetailList(data) {
    const api = `/api/Logsheet/GetLogSheetAndDetailList`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetLogSheetAndDetailBySheetId(data) {
    const api = `/api/Logsheet/GetLogSheetAndDetailBySheetId`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function LogsheetSaveForm(data) {
    const api = `/api/Logsheet/SaveForm`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function LogsheetDetailSaveForm(data) {
    const api = `/api/LogsheetDetail/SaveForm`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function LogsheetUpDataList(data) {
    const api = `/api/LogsheetDetail/UpDataList`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function LogsheetuploadFile(data) {
    const api = `/api/LogsheetDetail/uploadFile`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function SaveLogsheetuploadFile(data) {
    const api = `/api/LogsheetImage/SaveForm`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetLogSheetImgList(data) {
    const api = `/api/LogsheetImage/GetList`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetProductionOrderLtexts(data) {
    const api = `/ppm/BProductionOrderListView/GetLtexts`
    return getRequestResources(baseURL2, api, 'post', data);
}

export function GetAllLogList(data) {
    const api = `/api/Logsheet/GetAllLogList`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetPageAllLogList(data) {
    const api = `/api/Logsheet/GetPageAllLogList`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetGroupList(data) {
    const api = `/api/Logsheet/GetGroupName`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetApproveConfirm(data) {
    const api = `/api/Logsheet/ApproveConfirm`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetQaPass(data) {
    const api = `/api/Login/QAPermissionCheck`
    return getRequestResources(baseDFM, api, 'post', data);
}


export function MyGetPoRecipeData(data) {
    const api = `/ppm/BProductionOrderListView/GetPoRecipeData`
    return getRequestResources(baseURL2, api, 'post', data);
}

export function GetSampleListVPageList(data) {
    const api = `/api/SampleListV/GetPageList`
    return getRequestResources(baseURL2, api, 'post', data);
}
export function GetProcessOrderViewSegments(data) {
    const api = `/api/ProcessOrderView/GetSegments`
    return getRequestResources(baseURL2, api, 'post', data);
}

export function GetProcessOrderViewSegmentUnits(data, params) {
    params = encodeURIComponent(params);
    const api = `/api/ProcessOrderView/GetSegmentUnits/${params}`
    return getRequestResources(baseURL2, api, 'get', data);
}

export function GetPoList(data) {
    const api = '/api/PoList/GetPageList'
    return getRequestResources(baseURL, api, 'Post', data);
}



export function GetDefaultSamplingQuantity(data, key) {
    const api = `/api/BWeightRecords/GetDefaultSamplingQuantity/${key}`
    return getRequestResources(baseURL, api, 'Get', data);
}

export function CreateSamplingRecord(data) {
    const api = '/api/BWeightRecords/CreateSamplingRecord'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function SaveScaleReading(data) {
    const api = '/api/BWeightRecords/SaveScaleReading'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function ClearWeightRecordList(data) {
    const api = '/api/BWeightRecords/ClearWeightRecordList'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function GetWeightRecordsPageList(data) {
    const api = '/api/BWeightRecords/GetPageList'
    return getRequestResources(baseURL, api, 'Post', data);
}
export function GetWeightRecordsDelete(data) {
    const api = '/api/BWeightRecords/DeleteAllByIds '
    return getRequestResources(baseURL, api, 'Post', data);
}
export function GetWeightRecordList(data) {
    const api = '/api/WeightRecordList/GetList'
    return getRequestResources(baseURL, api, 'Post', data);
}

export function GetScaleListByEquipmentId(data, key) {
    const api = `/api/BWeightRecords/GetScaleListByEquipmentId/${key}`
    return getRequestResources(baseURL, api, 'Get', data);
}

export function StartDetection(data, params) {
    const api = `/api/SampleList/StartDetection`
    return getRequestResources(baseURL2, api, 'post', data);
}

export function AddSampling(data, params) {
    const api = `/api/SampleList/AddSampling`
    return getRequestResources(baseURL2, api, 'post', data);
}

export function MygetContent(data, params) {
    const api = `/api/InterfaceLog/GetLastLog`
    return getRequestResources(baseURL2, api, 'post', data);
}

export function MygetContentValue(data, params) {
    const api = `/api/FunctionProperty/GetPropertyV`
    return getRequestResources(baseURL2, api, 'post', data);
}


export function GetBPEquipmentsSelect(data, params) {
    const api = `/ppm/BProductionOrderListView/GetEquipmentsSelect`
    return getRequestResources(baseURL2, api, 'get', data);
}
export function GetProduceOpen(data, params) {
    const api = `/ppm/BProductionOrderListView/ProduceOpen`
    return getRequestResources(baseURL2, api, 'post', data);
}







import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TRACE; // 配置服务url
const materailBaseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_MATERIAL; // 配置服务url
//设备是否有产线
export function GetFeedingProcDevice(data) {
    return request({
        url: baseURL + '/trace/Feeding/GetFeedingProcDevice',
        method: 'post',
        data
    });
}

//胶水上料保存
export function GlueRecordUpdateLoading(data) {
    return request({
        url: materailBaseURL + '/materail/GlueRecord/UpdateLoading',
        method: 'post',
        data
    });
}

// 获取上料列表
export function traceGetPageList(data) {
    return request({
        url: baseURL + '/trace/Feeding/GetPageList',
        method: 'post',
        data
    });
}
// 删除
export function traceFeedingDelete(data) {
    return request({
        url: baseURL + '/trace/Feeding/Delete',
        method: 'post',
        data
    });
}
// 编辑
export function traceFeedingSaveForm(data) {
    return request({
        url: baseURL + '/trace/Feeding/SaveForm',
        method: 'post',
        data
    });
}



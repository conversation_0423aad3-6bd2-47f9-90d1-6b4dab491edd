import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TPM; // 配置服务url
//获取知识库列表
export function DeviceRepairProjectGetPageList(data) {
    return request({
        url: baseURL + '/tpm/DeviceRepairProject/GetPageList',
        method: 'post',
        data
    });
}
export function DeviceRepairProjectGetList(data) {
    return request({
        url: baseURL + '/tpm/DeviceRepairProject/GetList',
        method: 'post',
        data
    });
}
// 新增知识库
export function DeviceRepairProjectSaveForm(data) {
    return request({
        url: baseURL + '/tpm/DeviceRepairProject/SaveForm',
        method: 'post',
        data
    });
}
// 删除知识库
export function DeviceRepairProjectDelete(data) {
    return request({
        url: baseURL + '/tpm/DeviceRepairProject/Delete',
        method: 'post',
        data
    });
}
// 导入
export function DeviceRepairProjectImport(data) {
    return request({
        url: baseURL + '/tpm/DeviceRepairProject/ImportExcel',
        method: 'post',
        data
    });
}

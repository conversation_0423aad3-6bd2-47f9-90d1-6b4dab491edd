import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TRACE; // 配置服务url
//获取列表
export function BatchGetPageList(data) {
    return request({
        url: baseURL + '/trace/Batch/GetPageList',
        method: 'post',
        data
    });
}
//获取列表
export function BatchFlowRecordGetPageList(data) {
    return request({
        url: baseURL + '/trace/BatchFlowRecord/GetPageList',
        method: 'post',
        data
    });
}
//扫描录入
export function xnintoBatchdata(data) {
    return request({
        url: baseURL + '/trace/wo/GetBatchData',
        method: 'post',
        data
    });
}

// 性能测试录入确定
export function xnintoBatchAndBatchRech(data) {
    return request({
        url: baseURL + '/trace/wo/BatchReceive',
        method: 'post',
        data
    });
}

// 分档
export function xnoutabCreateBatch(data) {
    return request({
        url: baseURL + '/trace/Wo/CreateTestBatchNew',
        method: 'post',
        data
    });
}

// 性能确认
export function xnoutCommitCreateBatch(data) {
    return request({
        url: baseURL + '/trace/wo/BatchComplete',
        method: 'post',
        data
    });
}

// 详情
export function xnlevelBatchData(data) {
    return request({
        url: baseURL + '/trace/wo/xnlevelBatchData',
        method: 'post',
        data
    });
}




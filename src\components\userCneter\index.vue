<template>
    <v-row justify="center">
        <v-dialog v-model="dialog" class="mt-10" fullscreen hide-overlay transition="dialog-bottom-transition">
            <v-card>
                <v-toolbar dark height="40" color="primary">
                    <!-- <v-toolbar-title>个人中心</v-toolbar-title> -->
                    <v-spacer></v-spacer>
                    <v-toolbar-items>
                        <v-btn icon dark @click="dialog = false">
                            <v-icon>mdi-close</v-icon>
                        </v-btn>
                    </v-toolbar-items>
                </v-toolbar>
                <div class="tabs-style">
                    <!-- <v-card>个人中心</v-card> -->
                    <h2 width="220" class="my-4 mx-auto text-center">个人中心</h2>
                    <v-tabs vertical>
                        <v-card class="ma-1" outline min-width="220" height="calc( 100vh - 200px)">
                            <v-tab>
                                <!-- <v-icon left>mdi-account</v-icon> -->
                                基本信息
                            </v-tab>
                            <v-tab>
                                <!-- <v-icon left>mdi-lock</v-icon> -->
                                头像设置
                            </v-tab>
                            <v-tab>
                                <!-- <v-icon left>mdi-access-point</v-icon> -->
                                修改密码
                            </v-tab>
                            <v-tab>
                                <!-- <v-icon left>mdi-access-point</v-icon> -->
                                消息中心
                            </v-tab>
                        </v-card>

                        <v-tab-item>
                            <v-card width="600" class="my-1 mx-3" height="calc( 100vh - 200px)">
                                <v-card-text>
                                    <information></information>
                                </v-card-text>
                            </v-card>
                        </v-tab-item>
                        <v-tab-item>
                            <v-card width="600" class="my-1 mx-3" height="calc( 100vh - 200px)">
                                <v-card-text>
                                    <settinghead></settinghead>
                                </v-card-text>
                            </v-card>
                        </v-tab-item>
                        <v-tab-item>
                            <v-card width="600" class="my-1 mx-3" height="calc( 100vh - 200px)">
                                <v-card-text>
                                    <changePassword></changePassword>
                                </v-card-text>
                            </v-card>
                        </v-tab-item>
                        <v-tab-item>
                            <v-card width="600" class="my-1 mx-3" height="calc( 100vh - 200px)">
                                <v-card-text></v-card-text>
                            </v-card>
                        </v-tab-item>
                    </v-tabs>
                </div>
            </v-card>
        </v-dialog>
    </v-row>
</template>
<script>
export default {
    components: {
        information: () => import('./components/information.vue'),
        settinghead: () => import('./components/settinghead.vue'),
        changePassword: () => import('./components/changePassword.vue')
    },
    data() {
        return {
            dialog: false,
            notifications: false,
            sound: true,
            widgets: false
        };
    }
};
</script>
<style lang="scss" scoped>
.tabs-style {
    margin: 8px auto;
    width: 960px;
}
</style>

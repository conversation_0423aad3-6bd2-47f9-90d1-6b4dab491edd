export const operationalPath = [
    { text: '序号', value: 'Index', width: 60, sortable: true },
    { text: '路线代码', value: 'RoutingCode', width: 120, sortable: true },
    { text: '路线名称', value: 'RoutingName', width: 120, sortable: true },
    { text: '路线版本', value: 'Version', width: 130, sortable: true },
    { text: '工艺类型', value: 'RoutingType', width: 120, sortable: true },
    { text: '生效自', value: 'EffectStart', width: 150 },
    { text: '生效至', value: 'EffectEnd', width: 150 },
    { text: '状态', value: 'Status', width: 100 },
    { text: '描述', value: 'Description', width: 200, sortable: true },
    { text: '备注', value: 'Notes', width: 150, sortable: true },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', align: 'center', value: 'actions', width: 150, sortable: true }
];
export const operationalPathProduct = [
    { text: '序号', value: 'Index', width: 60, sortable: true },
    { text: '成品料号', value: 'MaterialCode', width: 100 },
    { text: '产品名称', value: 'MaterialName', width: 150 },
    { text: '成品料号版本', value: 'MaterialVersion', width: 150 },
    { text: '备注', value: 'Remark', width: 150 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', align: 'center', value: 'actions', width: 140 }
];
export const operationalPathDetail = [
    { text: '序号', value: 'Index', width: 60, sortable: true },
    { text: '工序编号', value: 'ProcCode', width: 100 },
    { text: '工序名称', value: 'ProcName', width: 100 },
    { text: '版本', value: 'Version', width: 100 },
    { text: '工序类型', value: 'ProcType', width: 100 },
    { text: '经营单位', value: 'Unit', width: 100 },
    { text: '生效自', value: 'EffectStart', width: 150 },
    { text: '生效至', value: 'EffectEnd', width: 150 },
    { text: '工时基准', value: 'Timb', width: 150 },
    { text: '运行机器', value: 'Runm', width: 150 },
    { text: '运行人工', value: 'Runl', width: 100 },
    { text: '设置人工', value: 'Setl', width: 100 },
    { text: '搬运小时数', value: 'Movd', width: 120 },
    { text: '排队小时数', value: 'Qued', width: 120 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', align: 'center', value: 'actions', width: 140 }
];
// 设备类型
export const deviceType = [
    { text: '序号', value: 'Index', width: 60, sortable: true },
    { text: '设备类型', value: 'CATEGORY_NAME', width: 100 },
    { text: '上级', value: 'PARENT_NAME', width: 100 },
    { text: '上上级', value: 'PARENT_TEST', width: 100 },
    { text: '描述', value: 'DESCRIBE', width: 200 },
    { text: '备注', value: 'REMARK', width: 150 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', align: 'center', value: 'actions', width: 140 }
];
// 工序资源
export const processResources = [
    { text: '序号', value: 'Index', align: 'center', width: '40px', sortable: true },
    { text: '工艺路线', value: 'RoutingName', width: 120, keyName: 'RoutingName' },
    { text: '产品号', value: 'MaterialCode', width: 120, keyName: 'ProductNumber' },
    { text: '工序', value: 'ProcName', width: 120, keyName: 'ProcessName' },
    { text: '工艺类型', align: 'center', value: 'ProcType', width: 180, keyName: 'RoutingType' }
];
// 刀具维护
export const cutter = [
    { text: '序号', value: 'theSerialNumber', width: 60, sortable: true },
    { text: '刀具料号', value: 'theSerialNumber', width: 100 },
    { text: '平均百件产品需要数量', value: 'theSerialNumber', width: 180 },
    { text: '需要数量', value: 'theSerialNumber', width: 100 },
    { text: '设备类型', value: 'theSerialNumber', width: 100 },
    { text: '备注', value: 'theSerialNumber', width: 100 },
    { text: '是否映射所有选中产品号工序', value: 'theSerialNumber', width: 210 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', align: 'center', value: 'actions', width: 80 }
];
// 夹具维护
export const fixture = [
    { text: '序号', value: 'Index', width: 60, sortable: true },
    { text: '类型', value: 'FixturesType', width: 80 },
    { text: '料号', value: 'MaterialCode', width: 80 },
    { text: '需要数量', value: 'Quantity', width: 100, semicolonFormat: true },
    { text: '设备类型', value: 'DeviceTypeName', width: 100 },
    { text: '备注', value: 'Remark', width: 100 },
    // { text: '是否映射所有选中产品号工序', value: 'theSerialNumber', width: 210 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', align: 'center', value: 'actions', width: 140 }
];
//  辅料维护
export const ingredients = [
    { text: '序号', value: 'Index', width: 60, sortable: true },
    { text: '辅料料号', value: 'MaterialCode', width: 100 },
    { text: '需要数量', value: 'Quantity', width: 100, semicolonFormat: true },
    { text: '单位', value: 'Unit', width: 80 },
    { text: '备注', value: 'Remark', width: 100 },
    // { text: '是否映射所有选中产品号工序', value: 'theSerialNumber', width: 180 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', align: 'center', value: 'actions', width: 140 }
];
// 设备维护
export const device = [
    { text: '序号', value: 'theSerialNumber', width: 60, sortable: true },
    { text: '设备号', value: 'theSerialNumber', width: 100 },
    { text: '工时基准', value: 'theSerialNumber', width: 100 },
    { text: '运行机器', value: 'theSerialNumber', width: 100 },
    { text: '运行人工', value: 'theSerialNumber', width: 100 },
    { text: '设置人工', value: 'theSerialNumber', width: 100 },
    { text: '搬运小时数', value: 'theSerialNumber', width: 120 },
    { text: '排队小时数', value: 'theSerialNumber', width: 120 },
    { text: '是否映射所有选中产品号工序', value: 'theSerialNumber', width: 210 },
    { text: '最近更新人', value: 'theSerialNumber', width: 120 },
    { text: '最近更新时间', value: 'theSerialNumber', width: 150 },
    { text: '操作', align: 'center', value: 'actions', width: 80 }
];
// 机加程序维护
export const machiningProgram = [
    { text: '序号', value: 'theSerialNumber', width: 60, sortable: true },
    { text: '程序编号', value: 'theSerialNumber', width: 100 },
    { text: '程序名称', value: 'theSerialNumber', width: 100 },
    { text: '版本', value: 'theSerialNumber', width: 80 },
    { text: '程序文件名称', value: 'theSerialNumber', width: 140 },
    { text: '程序格式', value: 'theSerialNumber', width: 100 },
    { text: '预定生效日期', value: 'theSerialNumber', width: 150 },
    { text: '是否映射所有选中产品号工序', value: 'theSerialNumber', width: 210 },
    { text: '最近更新人', value: 'theSerialNumber', width: 120 },
    { text: '最近更新时间', value: 'theSerialNumber', width: 150 },
    { text: '操作', align: 'center', value: 'actions', width: 80 }
];
//   添加机加程序信息
export const programTable = [
    { text: '序号', value: 'theSerialNumber', width: 60, sortable: true },
    { text: '程序单号', value: 'theSerialNumber', width: 100 },
    { text: '设备类型', value: 'theSerialNumber', width: 100 },
    { text: '程序名称', value: 'theSerialNumber', width: 100 },
    { text: '版本', value: 'theSerialNumber', width: 80 },
    { text: '生效日期', value: 'theSerialNumber', width: 100 },
    { text: '文件名称', value: 'theSerialNumber', width: 100 },
    { text: '文件路径', value: 'theSerialNumber', width: 120 }
];
// 机加信息台账
export const processParameter = [
    { text: '序号', value: 'theSerialNumber', width: 60, sortable: true },
    { text: '程序单号', value: 'theSerialNumber', width: 100 },
    { text: '设备类型', value: 'theSerialNumber', width: 100 },
    { text: '程序名称', value: 'theSerialNumber', width: 100 },
    { text: '版本', value: 'theSerialNumber', width: 80 },
    { text: '生效日期', value: 'theSerialNumber', width: 100 },
    { text: '文件名称', value: 'theSerialNumber', width: 100 },
    { text: '文件路径', value: 'theSerialNumber', width: 120 },
    { text: '文件格式', value: 'theSerialNumber', width: 120 },
    { text: '备注', value: 'theSerialNumber', width: 150 },
    { text: '操作', align: 'center', value: 'actions', width: 80 }
];
// 用户列表
export const userColumnsList = [
    { text: '序号', value: 'Index', width: 60, sortable: true },
    { text: '公司', value: 'CompanyName', width: 120 },
    { text: '部门', value: 'DepartmentName', width: 130 },
    { text: '岗位', value: 'PostName', width: 100 },
    { text: '登录名', value: 'LoginName', width: 100 },
    { text: '员工号', value: 'UserNo', width: 100 },
    { text: '用户姓名', value: 'UserName', width: 120 },
    { text: '电话', value: 'Tel', width: 120 },
    { text: '邮箱', value: 'EMAIL', width: 120 },
    { text: '状态', value: 'Status', width: 110 },
    { text: '备注', value: 'Remark', width: 150 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', align: 'center', value: 'actions', width: 120 }
];
// 角色管理
export const roleColumnsList = [
    { text: '', value: 'data-table-expand' },
    { text: '角色名称', value: 'Name', width: 120 },
    { text: '状态', value: 'Enable', width: 100 },
    { text: '描述', value: 'Remark', width: 150 },
    // { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    // { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    // { text: '创建时间', value: 'CreateDate', width: 160 },
    // { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', align: 'center', value: 'actions', width: 180 }
];

// 角色绑定用户列表表头
export const roleBindUserColumnsList = [
    // { text: '序号', value: 'Index', width: 80, sortable: true },
    { text: '登录名', value: 'LoginName', width: 120, sortable: true },
    { text: '用户名称', value: 'UserName', width: 120 },
    { text: '员工号', value: 'UserNo', width: 120 },
    { text: '手机号', value: 'Tel', width: 120 },
    { text: '备注', value: 'Remark', width: 120 },
    { text: '操作', align: 'center', value: 'actions', width: 120, sortable: true }
];
// 添加用户展示表头
export const roleBindUserColumnsLists = [
    { text: '登录名', value: 'LoginName', width: 120 },
    { text: '用户名称', value: 'UserName', width: 120 },
    { text: '员工号', value: 'UserNo', width: 120 },
    { text: '手机号', value: 'Tel', width: 120 },
    { text: '备注', value: 'Remark', width: 120 }
];

// kpi 标签管理表头
export const tagHeadTitleList = [
    // { text: '序号', value: 'theSerialNumber', width: 60, sortable: true },
    { text: '标签名称', value: 'TagName', width: 120, sortable: true },
    { text: '标签编码', value: 'TagCode', width: 120, sortable: true },
    { text: '单位', value: 'Unit', width: 120, sortable: true },
    { text: '计算方式', value: 'Automatic', width: 120, sortable: true },
    { text: '有效', value: 'Enable', width: 120, sortable: true },
    { text: '描述', value: 'Description', width: 120, sortable: true },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', align: 'center', value: 'actions', width: 120, sortable: true }
];

// kpi 标签管理弹窗表头
export const tagPopupHeadTitleList = [
    // { text: '序号', value: 'theSerialNumber', width: 60, sortable: true },
    { text: '标签名称', value: 'TagName', width: 120, sortable: true },
    { text: '标签编码', value: 'TagCode', width: 120, sortable: true },
    { text: '单位', value: 'Unit', width: 120, sortable: true },
    { text: '计算方式', value: 'Automatic', width: 120, sortable: true },
    { text: '有效', value: 'Enable', width: 120, sortable: true },
    { text: '描述', value: 'Description', width: 120, sortable: true },
    { text: '', align: 'center', value: '', width: 0, sortable: false }
]
// kpi 模型管理表头
export const moduleManagementHeadTitleList = [
    // { text: '序号', value: 'theSerialNumber', width: 60, sortable: true },
    { text: 'SIM级别', value: 'SimLevel', width: 100, sortable: true },
    // { text: '厂别/基地/产线', value: 'LineName', width: 120, sortable: true },
    // { text: '工序', value: 'ProcessCode', width: 80, sortable: true },
    { text: 'KPI名称', value: 'KpiName', width: 100, sortable: true },
    { text: 'KPI编码', value: 'KpiCode', width: 100, sortable: true },
    // { text: '颗粒度', value: 'Period', width: 80, sortable: true, dictionary: true },
    { text: '单位', value: 'Unit', width: 80, sortable: true },
    { text: 'Sql', value: 'SqlText', width: 120, sortable: true },
    { text: '公式', value: 'Expression', width: 120, sortable: true },
    { text: '是否启用', value: 'Enable', width: 100, sortable: true },
    { text: '描述', value: 'Description', width: 120, sortable: true },
    // { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    // { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    // { text: '创建时间', value: 'CreateDate', width: 160 },
    // { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', align: 'center', value: 'actions', width: 120, sortable: true }
];
// kpi模型录入第一个表头
export const moduleEnteringHeadTitleList = [
    // { text: '序号', value: 'theSerialNumber', width: 60, sortable: true },
    { text: 'SIM级别', value: 'SIMName', width: 100, sortable: true },
    { text: '厂别/基地/产线', value: 'LineName', width: 120, sortable: true },
    { text: 'KPI名称', value: 'KPIName', width: 100, sortable: true },
    { text: 'KPI编码', value: 'KPICode', width: 100, sortable: true },
    { text: '颗粒度', value: 'PeriodName', width: 80, sortable: true },
    { text: '单位', value: 'Unit', width: 80, sortable: true },
    { text: '公式', value: 'Expression', width: 120, sortable: true },
    // { text: '是否启用', value: 'EnabledMark', width: 80, sortable: true },
    { text: '描述', value: 'Description', width: 120, sortable: true },
    { text: '', align: 'center', value: 'actions1', width: 0, sortable: false }
];
// kpi模型录入第二个表头
export const moduleEnteringBottomHeadTitleList = [
    // { text: '序号', value: 'theSerialNumber', width: 60, sortable: true },
    { text: '公式参数', value: 'UnitName', width: 100, sortable: true },
    { text: '标签名称', value: 'TagName', width: 100, sortable: true },
    { text: '标签编码', value: 'UnitCode', width: 100, sortable: true },
    { text: '采集方式', value: 'Automatic', width: 100, sortable: true },
    { text: '类型', value: 'Type', width: 100, sortable: true },
    { text: '描述', value: 'Description', width: 150, sortable: true },
    { text: '', align: 'center', value: 'actions1', width: 0, sortable: false }
];

// 数据录入
export const dataEentering = [
    // { text: '', value: 'Index', width: 60, sortable: true },
    { text: '汇总日期', value: 'SummaryDate', width: 100, sortable: true },
    { text: '厂别/基地/产线', value: 'LineName', width: 120, sortable: true },
    { text: 'KPI名称', value: 'KPIName', width: 100, sortable: true },
    { text: '颗粒度', value: 'Period', width: 100, sortable: true },
    { text: '班组/班次', value: 'Team', width: 100, sortable: true },
    { text: 'KPI公式参数', value: 'UnitName', width: 100, sortable: true },
    { text: '标签名称', value: 'TagName', width: 100, sortable: true },
    { text: '参数值', value: 'Value', width: 100, sortable: true }
]
//  目标值录入
export const targetDataEntering = [
    // { text: '', value: 'Index', width: 60, sortable: true },
    { text: '汇总日期', value: 'SummaryDate', width: 100, sortable: true },
    { text: '厂别/基地/产线', value: 'LineName', width: 120, sortable: true },
    { text: 'KPI名称', value: 'KPIName', width: 150, sortable: true },
    { text: '颗粒度', value: 'Period', width: 100, sortable: true },
    { text: '班组/班次', value: 'Team', width: 100, sortable: true },
    { text: 'KPI基准目标值', value: 'Value', width: 120, sortable: true },
    { text: 'KPI上限目标值', value: 'AboveTarget', width: 120, sortable: true },
    { text: 'KPI下限目标值', value: 'BelowTarget', width: 120, sortable: true }
]

// 数据管理WLPOlist
export const dataManagement = [
    // { text: '', value: 'Index', width: 60, sortable: true },
    { text: '日期', value: 'SummaryDate', width: 100, sortable: true },
    { text: '公式参数', value: 'UnitName', width: 120, sortable: true },
    { text: '标签名称', value: 'TagName', width: 100, sortable: true },
    { text: '颗粒度', value: 'Period', width: 100, sortable: true },
    { text: '班组/班次', value: 'Team', width: 100, sortable: true },
    { text: '参数值', value: 'Value', width: 120, sortable: true },
]

// 单位管理表头
export const unitTableHead = [
    { text: '序号', value: 'Index', width: 60, sortable: true },
    { text: '单位名称', value: 'Name', width: 100, sortable: true },
    { text: '单位简称', value: 'Shortname', width: 100, sortable: true },
    { text: '类型名称', value: 'TYPENAME', width: 100, sortable: true },
    { text: '是否启用', value: 'Enable', width: 100, sortable: true },
    { text: '描述', value: 'Description', width: 100, sortable: true },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', align: 'center', value: 'actions', width: 120, sortable: false }
]

// 设备列表表头
export const deviceHeadTitle = [{
    text: '序号',
    value: 'Index',
    width: 80,
    sortable: true
},
{ text: '编码', value: 'EquipmentCode', width: 140 },
{ text: '名称', value: 'EquipmentName', width: 145 },
{ text: '描述', value: 'Remark', width: 180 },
{ text: '类型', value: 'Level', width: 120 },
{ text: '最近修改时间', value: 'ModifyDate', width: 160 },
{ text: '最近修改人', value: 'ModifyUserId', width: 120 },
{ text: '创建时间', value: 'CreateDate', width: 160 },
{ text: '创建人', value: 'CreateUserId', width: 120 },
{ text: '操作', align: 'center', value: 'actions', width: 100, sortable: false }
]
// 设备列表表头
export const deviceHeadTitlePopup = [{
    text: '序号',
    value: 'Index',
    width: 80,
    sortable: true
},
{ text: '编码', value: 'EquipmentCode', width: 140 },
{ text: '名称', value: 'EquipmentName', width: 145 },
{ text: '描述', value: 'Remark', width: 180 },
{ text: '类型', value: 'Level', width: 120 },
{ text: '最近修改时间', value: 'ModifyDate', width: 160 },
{ text: '最近修改人', value: 'ModifyUserId', width: 120 },
{ text: '创建时间', value: 'CreateDate', width: 160 },
{ text: '创建人', value: 'CreateUserId', width: 120 },
{ text: '', align: 'center', value: 'actions1', width: 0, sortable: false }
]
export const deviceNewHeadTitlePopup = [{
    text: '序号',
    value: 'Index',
    width: 80,
    sortable: true
},
{ text: '产线', value: 'Productlinename', width: 140 },
{ text: '工段', value: 'Segmentname', width: 140 },
{ text: '工站', value: 'Processname', width: 140 },
]

// 订单管理表头
export const orderHeadTitle = [
    { text: 'SAP生产订单号', value: 'SapWoCode', width: 140 },
    { text: '物料号', value: 'ProductionCode', width: 100 },
    { text: '物料名称', value: 'ProductionName', width: 190 },
    { text: '阶段', value: 'Stage', width: 80 },
    { text: '产线', value: 'Line', width: 120 },
    { text: '是否锁定', value: 'LockStatus', width: 100 },
    { text: '计划日期', value: 'PlanDate', width: 120 },
    { text: '开始时间', value: 'ActualStartTime', width: 160 },
    { text: '结束时间', value: 'ActualEndTime', width: 160 },
    { text: '订单类型', value: 'Type', width: 110, dictionary: true },
    { text: '计划数量', value: 'PlanQty', width: 100, semicolonFormat: true },
    { text: '状态', value: 'Status', width: 100, dictionary: true },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', value: 'actions', width: 249 },
]

// 生产工单
export const productionWorkOrderHeadTitle = [
    { text: 'SAP生产订单号', value: 'SapWoCode', width: 140 },
    { text: 'WO单号', value: 'WoCode', width: 130 },
    { text: '物料号', value: 'ProductionCode', width: 130 },
    { text: '产线', value: 'ProductionLineCode', width: 100 },
    { text: '工段', value: 'LineName', width: 160 },
    { text: '计划日期', value: 'PlanDate', width: 120 },
    { text: '班次', value: 'Shift', width: 100 },
    { text: '状态', value: 'Status', width: 100 },
    { text: '计划开始时间', value: 'PlanStartTime', width: 160 },
    { text: '计划结束时间', value: 'PlanEndTime', width: 160 },
    { text: '计划数量', value: 'PlanQty', width: 100, semicolonFormat: true },
    { text: '实际开始时间', value: 'ActualStartTime', width: 160 },
    { text: '实际结束时间', value: 'ActualEndTime', width: 160 },
    { text: '良品数量', value: 'QuantityQty', width: 100, semicolonFormat: true },
    { text: '次品数量', value: 'UnquantityQty', width: 100, semicolonFormat: true },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', value: 'actions', width: 140 },
]
// 生产工单查询
export const productionWorkOrderSearchHeadTitle = [
    { text: 'SAP生产订单号', value: 'SapWoCode', width: 150 },
    { text: 'WO单号', value: 'WoCode', width: 130 },
    { text: '物料号', value: 'ProductionCode', width: 130 },
    { text: '物料名称', value: 'ProductionName', width: 200 },
    { text: '产线', value: 'ProductionLineCode', width: 100 },
    { text: '工段', value: 'LineName', width: 160 },
    { text: '计划日期', value: 'PlanDate', width: 120 },
    { text: '班次', value: 'Shift', width: 100 },
    { text: '状态', value: 'Status', width: 100 },
    { text: '计划开始时间', value: 'PlanStartTime', width: 160 },
    { text: '计划结束时间', value: 'PlanEndTime', width: 160 },
    { text: '计划数量', value: 'PlanQty', width: 100, semicolonFormat: true },
    { text: '实际开始时间', value: 'ActualStartTime', width: 160 },
    { text: '实际结束时间', value: 'ActualEndTime', width: 160 },
    { text: '良品数量', value: 'QuantityQty', width: 100, semicolonFormat: true },
    { text: '次品数量', value: 'UnquantityQty', width: 100, semicolonFormat: true },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '', value: 'actions1', width: 0, sortable: false },
]

// 日工单详情
export const dayOrderDetailHeadTitle = [
    { text: '物料号', value: 'Productioncode', width: 130 },
    { text: '物料名称', value: 'Productionname', width: 150 },
    { text: '库存', value: 'Stock', width: 100 },
    { text: '在制工单号', value: 'Wipno', width: 130 },
    { text: '在制数量', value: 'Wipqty', width: 100, semicolonFormat: true },
    { text: '开始时间', value: 'Starttime', width: 130 },
    { text: '结束时间', value: 'Endtime', width: 130 },
    { text: '在途数量', value: 'Onwayqty', width: 100, semicolonFormat: true },
    { text: '来源', value: 'Source', width: 100 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', value: 'actions', width: 120 },
]

// 人员列表表头
export const personHeadTitle = [
    { text: '员工号', value: 'Staffcode', width: 120 },
    { text: '员工名字', value: 'Staffname', width: 100 },
    { text: '开始时间', value: 'Starttime', width: 150 },
    { text: '结束时间', value: 'Endtime', width: 150 },
    { text: '时长（分）', value: 'Duration', width: 120 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', value: 'actions', width: 120 },
]

// 年计划表头
export const YPlanNormalHeadTitle = [
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 170 },
    { text: '工厂', value: 'Factory', width: 120 },
    { text: '年份', value: 'Year', width: 120 },
    { text: '数量', value: 'Quantity', width: 120, semicolonFormat: true },
    { text: '操作', value: 'actions', width: 120 },
]
// 年计划表头
export const YPlanFormatHeadTitle = [
    { text: '物料号', value: 'ProductionCode', width: 120, },
    { text: '物料名称', value: 'ProductionName', width: 170 },
    { text: '工厂', value: 'Factory', width: 120 },
]

// 月计划  日计划表头
export const MDPlanFormatHeadTitle = [
    { text: '物料号', value: 'ProductionCode', width: 120, },
    { text: '物料名称', value: 'ProductionName', width: 170 },
    { text: '工厂', value: 'Factory', width: 120 },
    { text: '楼层', value: 'Floor', width: 120 },
    { text: '线体编号', value: 'LineCoding', width: 120 },
]

// 月计划 表头 
export const MPlanNormalHeadTitle = [
    { text: '物料号', value: 'ProductionCode', width: 120, },
    { text: '物料名称', value: 'ProductionName', width: 170 },
    { text: '工厂', value: 'Factory', width: 120 },
    { text: '楼层', value: 'Floor', width: 120 },
    { text: '线体编号', value: 'LineCoding', width: 120 },
    { text: '月份', value: 'Month', width: 120 },
    { text: '数量', value: 'Quantity', width: 120, semicolonFormat: true },
    { text: '操作', value: 'actions', width: 120 },
]
// 日计划表头 
export const DPlanNormalHeadTitle = [
    { text: '物料号', value: 'ProductionCode', width: 120, },
    { text: '物料名称', value: 'ProductionName', width: 170 },
    { text: '工厂', value: 'Factory', width: 120 },
    { text: '楼层', value: 'Floor', width: 120 },
    { text: '线体编号', value: 'LineCoding', width: 120 },
    { text: '日期', value: 'Days', width: 120 },
    { text: '数量', value: 'Quantity', width: 120, semicolonFormat: true },
    { text: '操作', value: 'actions', width: 120 },
]
// 次品拆分表头
export const splitColumnsHeaderTitle = [
    { text: 'SN', value: 'Sn', width: 120, },
    { text: '物料号', value: 'ProductionCode', width: 120, },
    { text: '产线', value: 'Line', width: 120, },
    { text: '生产工单', value: 'WoCode', width: 120, },
    { text: '生产批次', value: 'BatchNo', width: 120, },
    { text: '异常原因', value: 'ErrorDesc', width: 120, },
    { text: '拆解时间', value: 'WorkTime', width: 120, },
    { text: '拆解人', value: 'Operator', width: 120, },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', value: 'actions', width: 120 },
]
// 巡检记录表头
export const inspectionColumnsHeaderTitle = [
    { text: '巡检单号', value: 'InspectionSheet', width: 120, },
    { text: '物料号', value: 'ProductionCode', width: 120, },
    { text: '物料名称', value: 'ProductionName', width: 120, },
    { text: '产线', value: 'Line', width: 130, dictionary: true },
    { text: '工段', value: 'Segment', width: 130, dictionary: true },
    { text: '工站', value: 'Units', width: 130, dictionary: true },
    { text: 'Andon状态', value: 'IsClose', width: 120, dictionary: true },
    { text: '日期', value: 'ExecutionTime', width: 160, },
    { text: '执行人', value: 'Executor', width: 120, },
    { text: '状态', value: 'Status', width: 120, dictionary: true },
    { text: '到达现场', value: 'IsDuty', width: 120 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '操作', value: 'actions', width: 150 },
]
// 产品隔离 表头
export const isolationProductsColumns = [
    { text: '隔离单号', value: 'InspectionSheet', width: 120, },
    { text: '物料号', value: 'ProductionCode', width: 120, },
    { text: '物料名称', value: 'ProductionName', width: 120, },
    { text: '转标物料号', value: 'ProductionCodeModel', width: 120, },
    { text: '产线', value: 'Line', width: 140, dictionary: true },
    { text: '隔离工段', value: 'Segment', width: 140, dictionary: true },
    { text: '批次号', value: 'BatchNo', width: 120, },
    { text: '状态', value: 'Status', width: 120, dictionary: true },
    // { text: '产品名', value: 'ProductName', width: 120, },
    { text: '计划隔离数量', value: 'PlanIsolationQty', width: 130, semicolonFormat: true },
    { text: '实际隔离数量', value: 'ActualIsolationQty', width: 130, semicolonFormat: true },
    { text: '隔离人', value: 'D1CreaterName', width: 120, },
    { text: '隔离人账号', value: 'D1CreaterCode', width: 130, },
    { text: '登记', value: 'DealReason', width: 160 },
    { text: '失败原因', value: 'FailureReason', width: 150 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '操作', value: 'actions', width: 120, sortable: false },
]
// 检验项目表头
export const inspectionItemColumns = [
    { text: '检验项目名', value: 'TestItem', width: 120, },
    { text: '物料号', value: 'ProductionCode', width: 120, },
    { text: '物料名称', value: 'ProductionName', width: 180, },
    { text: '产线', value: 'Line', width: 150, dictionary: true },
    { text: '工段', value: 'Segment', width: 120, dictionary: true },
    { text: '工站', value: 'Units', width: 120, dictionary: true },
    { text: '判定方式', value: 'InspectionType', width: 120, dictionary: true },
    { text: '检验类别', value: 'InspectionCategory', width: 120, dictionary: true },
    { text: '采样频次', value: 'Frequency', width: 130, dictionary: true },
    { text: '样本容量', value: 'SampleSize', width: 130, },
    { text: '单位', value: 'Unit', width: 100, },
    { text: '标准值', value: 'StandardValue', width: 100, },
    { text: '最小值', value: 'Minvalue', width: 100, },
    { text: '最大值', value: 'Maxvalue', width: 100, },
    // { text: '结果类型', value: 'QualitativeResults', width: 100, dictionary: true },
    { text: '检测工具', value: 'Testtool', width: 150, dictionary: true },
    { text: '量具型号', value: 'Toolmodel', width: 130, },
    { text: '数据输入方式', value: 'Inputmode', width: 130, dictionary: true },
    { text: '检验方式', value: 'IsAuto', width: 100, dictionary: true },
    { text: '执行人', value: 'Executor', width: 100 },
    // { text: '是否为SPC参数', value: 'Isspc', width: 135, },
    { text: '是否启用', value: 'Status', width: 120, },
    { text: '备注', value: 'Remark', width: 130, },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', value: 'actions', width: 120 },
]
// 检验组表头
export const inspectionSectionColumns = [
    { text: '检验组名', value: 'TestGroup', width: 120 },
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 180, },
    { text: '检验项目', value: 'TestItemList', width: 180 },
    { text: '采样频次', value: 'Frequency', width: 140, dictionary: true },
    { text: '样品容量', value: 'Samplesize', width: 120 },
    { text: '颗粒度', value: 'Granularity', width: 120, dictionary: true },
    { text: '检验类型', value: 'InspectionCategory', width: 120, dictionary: true },
    { text: '自动任务', value: 'Isauto', width: 120 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', value: 'actions', width: 120 },
]
//检验模板维护 表头
export const inspectionTemplateColumns = [
    { text: '模板名称', value: 'TemplateName', width: 120 },
    { text: '类型', value: 'TestType', width: 80, dictionary: true },
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 180, },
    { text: '检验组', value: 'TestGroupList', width: 120 },
    { text: '产线', value: 'Line', width: 120, dictionary: true },
    { text: '工段', value: 'Segment', width: 120, dictionary: true },
    { text: '机台', value: 'Unit', width: 120, dictionary: true },
    // { text: '记录表格', value: 'TestGroup', width: 120 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', value: 'actions', width: 120 },
]
//检验计划维护 表头
export const inspectionPlanColumns = [
    { text: '检验计划类型', value: 'TestType', width: 130, dictionary: true },
    { text: '检验项目', value: 'TestItem', width: 130, },
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 180, },
    { text: '产线', value: 'Line', width: 120, dictionary: true },
    { text: '工段', value: 'Segment', width: 140, dictionary: true },
    { text: '工站', value: 'Units', width: 120, dictionary: true },
    { text: '执行时间', value: 'ExecutionTime', width: 180 },
    { text: '执行人', value: 'Executor', width: 120 },
    { text: '是否有效', value: 'Status', width: 120 },
    { text: '操作', value: 'actions', width: 120 },
]
// 首检记录表头
export const firstInspectionColumns = [
    { text: '首检单号', value: 'InspectionSheet', width: 120 },
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 180, },
    { text: '产线', value: 'Line', width: 140, dictionary: true },
    { text: '工段', value: 'Segment', width: 120, dictionary: true },
    { text: '工站', value: 'Units', width: 120, dictionary: true },
    { text: 'Andon状态', value: 'IsClose', width: 120, dictionary: true },
    { text: '时间', value: 'ExecutionTime', width: 160 },
    { text: '执行人', value: 'Executor', width: 100 },
    { text: '状态', value: 'Status', width: 120, dictionary: true },
    { text: '到达现场', value: 'IsDuty', width: 120 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '操作', value: 'actions', width: 150 },
]
export const finalInspectionColumns = [
    { text: '末检单号', value: 'InspectionSheet', width: 120 },
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 180, },
    { text: '产线', value: 'Line', width: 140, dictionary: true },
    { text: '工段', value: 'Segment', width: 120, dictionary: true },
    { text: '工站', value: 'Units', width: 120, dictionary: true },
    { text: 'Andon状态', value: 'IsClose', width: 120, dictionary: true },
    { text: '时间', value: 'ExecutionTime', width: 160 },
    { text: '执行人', value: 'Executor', width: 100 },
    { text: '状态', value: 'Status', width: 120, dictionary: true },
    { text: '到达现场', value: 'IsDuty', width: 120 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '操作', value: 'actions', width: 150 },
]
// 首检记录弹窗表头
export const firstInspectionPopupColumns = [
    { text: '检验单号', value: 'InspectionSheet', width: 120 },
    { text: '检验项目', value: 'TestItem', width: 120 },
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 190 },
    { text: '最小值', value: 'Minvalue', width: 70, sortable: false },
    { text: '测量值', value: 'MeasuredValue', width: 100, sortable: false },
    { text: '最大值', value: 'Maxvalue', width: 70, sortable: false },
    { text: '标准值', value: 'Standardvalue', width: 70, sortable: false },
    { text: '定性值', value: 'SizingStandard', width: 300 },
    { text: 'OK/NG', value: 'InspectionText', width: 200 },
    { text: '图片管理', value: 'PictureFile', width: 150 },
]
// 过程抽检表头
export const spockCheckColumns = [
    { text: '抽检单号', value: 'InspectionSheet', width: 120 },
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 180, },
    { text: '产线', value: 'Line', width: 140, dictionary: true },
    { text: '工段', value: 'Segment', width: 140, dictionary: true },
    { text: '工站', value: 'Units', width: 140, dictionary: true },
    { text: 'Andon状态', value: 'IsClose', width: 120, dictionary: true },
    { text: '状态', value: 'Status', width: 120, dictionary: true },
    { text: '执行时间', value: 'ExecutionTime', width: 180 },
    { text: '执行人', value: 'Executor', width: 120 },
    { text: '到达现场', value: 'IsDuty', width: 120 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '操作', value: 'actions', width: 120 },
]
// 抽检详情（弹窗）表头
export const spotCheckPopupColumns = [
    { text: '检验项目', value: 'TestItem', width: 120 },
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 180, },
    { text: '计划抽检数量', value: 'Granularity', width: 120, semicolonFormat: true },
    { text: '实际抽检数量', value: 'Actualqty', width: 120, align: 'center' },
]
// 抽检登记表头
export const spotCheckRegisterColumns = [
    { text: '抽检单号', value: 'InspectionSheet', width: 120 },
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 180, },
    { text: '批次号', value: 'BatchNo', width: 120 },
    { text: '阈值数', value: 'UnQualified', width: 100 },
    { text: '产线', value: 'Line', width: 140, dictionary: true },
    { text: '工段', value: 'Segment', width: 140, dictionary: true },
    { text: '工站', value: 'Units', width: 140, dictionary: true },
    { text: '状态', value: 'Status', width: 120, dictionary: true },
    { text: '操作', value: 'actions', width: 150 },
]
// 接收抽检单弹窗表头
export const receiveSpotCheckColumns = [
    { text: '检验项目', value: 'TestItem', width: 120 },
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 180, },
    { text: '批次号', value: 'BatchNo', width: 120 },
    { text: '抽检线体', value: 'Line', width: 140 },
    { text: '工段', value: 'Segment', width: 140 },
    { text: '工站', value: 'Units', width: 140 },
    { text: '阈值数', value: 'UnQualified', width: 120 },
    { text: '计划抽检数量', value: 'Granularity', width: 130, semicolonFormat: true },
    { text: '实际抽检数量', value: 'Actualqty', width: 130, semicolonFormat: true },
]


export const receiveSpotCheckDataEditColumns = [
    { text: '检验项目', value: 'TestItem', width: 120 },
    { text: '最小值', value: 'Minvalue', width: 100 },
    { text: '测量值', value: 'MeasuredValue', width: 100 },
    { text: '最大值', value: 'Maxvalue', width: 100 },
]

// 隔离处置
export const isolationDisposeColumns = [
    { text: '隔离单号', value: 'InspectionSheet', width: 120, },
    { text: '物料号', value: 'ProductionCode', width: 120, },
    { text: '物料名称', value: 'ProductionName', width: 180, },
    { text: '产线', value: 'Line', width: 140, dictionary: true },
    { text: '隔离工段', value: 'Segment', width: 140, dictionary: true },
    { text: '批次号', value: 'BatchNo', width: 120, },
    // { text: '产品名', value: 'ProductName', width: 120, },
    { text: '计划隔离数量', value: 'PlanIsolationQty', width: 130, semicolonFormat: true },
    { text: '实际隔离数量', value: 'ActualIsolationQty', width: 130, semicolonFormat: true },
    { text: '评审状态', value: 'Status', width: 120, },
    { text: '隔离人', value: 'Executor', width: 120, },
    { text: '操作', value: 'actions', width: 250 },
]
// 隔离签核
export const isolationSignOffColumns = [
    { text: '隔离单号', value: 'InspectionSheet', width: 120 },
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 180 },
    { text: '产线', value: 'Line', width: 140, dictionary: true },
    { text: '隔离工段', value: 'Segment', width: 140, dictionary: true },
    { text: '批次号', value: 'BatchNo', width: 120 },
    // { text: '产品名', value: 'ProductName', width: 120 },
    { text: '隔离数量', value: 'PlanIsolationQty', width: 130, semicolonFormat: true },
    { text: '处理状态', value: 'DealStatus', width: 120 },
    { text: '操作', value: 'actions', width: 120, sortable: false },
]
// 生产追溯
export const productionTraceabilityColumns = [
    { text: 'SN', value: 'Sn', width: 120 },
    { text: '箱号', value: 'Sn', width: 120 },
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 120 },
    { text: '产线', value: 'Line', width: 120 },
    { text: '工站', value: 'Segment', width: 120 },
    { text: '状态', value: 'Status', width: 120 },
]
// 异常事件通知
export const anomalousEventColumns = [
    { text: '发起人', value: 'CreateBy', width: 120, dictionary: true },
    { text: '发生时间', value: 'EventTime', width: 160 },
    { text: '事件描述', value: 'Eventcontent', width: 180 },
    { text: '来源', value: 'UeSource', width: 120 },
    { text: '事件类型', value: 'UeType', width: 120 },
    { text: '发生地', value: 'UePlace', width: 120 },
    { text: '事件等级', value: 'UeLevel', width: 120 },
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 120 },
    { text: '产品阶段', value: 'Stage', width: 120 },
    { text: '产线', value: 'Line', width: 130, dictionary: true },
    { text: '工段', value: 'Section', width: 130, dictionary: true },
    { text: '产品隔离', value: 'Result', width: 120 },
    { text: '隔离人', value: 'D1CreaterName', width: 120 },
    { text: '隔离人账号', value: 'D1CreaterCode', width: 130 },
    { text: '是否发送', value: 'Status', width: 120 },
    { text: '操作', value: 'actions', width: 120, sortable: false },
]
// 异常事件详情
export const detailAnomalousEventColumns = [
    // { text: '产品隔离', value: 'ReResult', width: 120 },
    { text: '处罚方式', value: 'Result', width: 120 },
    { text: '数量', value: 'DealQty', width: 120 },
    { text: '处理人', value: 'UserCode', width: 120, dictionary: true },
    { text: '处罚日期', value: 'DealTime', width: 120 },
]
// 良率查询
export const yieldQueryColumns = [
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 120 },
    { text: '产线', value: 'Line', width: 120 },
    { text: '工段', value: 'Segment', width: 120 },
    { text: '开始时间', value: 'StartTime', width: 120 },
    { text: '结束时间', value: 'EndTime', width: 120 },
    { text: '一次良率', value: 'CreateBy', width: 120 },
    { text: '良率标注', value: 'CreateBy', width: 120 },
]
// 性能测试
export const performanceTestColumns = [
    { text: 'SN', value: 'Sn', width: 120 },
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 120 },
    { text: '产线', value: 'Line', width: 120 },
    { text: '测试时间', value: 'TestTime', width: 120 },
    { text: '测试机台号', value: 'Line', width: 120 },
    { text: '测试结果', value: 'TestResult', width: 120 },
]
// 外观检测处理
export const appearanceInspectionColumns = [
    { text: '检验批次', value: 'TestBatchNo', width: 120 },
    { text: '物料号', value: 'ProductionCode', width: 120 },
    { text: '物料名称', value: 'ProductionName', width: 120 },
    { text: '检测时间', value: 'TestTime', width: 120 },
    { text: '检测人', value: 'TestUser', width: 120 },
    { text: '合格数', value: 'Sn', width: 120, semicolonFormat: true },
    { text: '不合格数', value: 'Sn', width: 120, semicolonFormat: true },
]

//点位配置表头
export const pointConfigColumns = [
    { text: '产线', value: 'AreaName', width: 120 },
    { text: '工段', value: 'Prodouctname', width: 120 },
    { text: '工站', value: 'Segmentname', width: 120 },
    { text: 'TAG点', value: 'TargetPoint', width: 120 },
    // { text: '胶水', value: 'GlueWater', width: 130 },
    { text: '创建时间', value: 'CreateDate', width: 120 },
    { text: '操作', value: 'actions', width: 120, sortable: false },
]

export const bPointConfigColumns = [
    { text: '点位名', value: 'PointName', width: 120 },
    { text: '参数', value: 'Parameter', width: 120 },
    { text: '检验项目', value: 'TestItem', width: 120 },
    { text: '是否开启', value: 'EnableMonitoring', width: 120 },
    { text: '管控规则', value: 'MonitoringRule', width: 120, dictionary: true },
    { text: 'Goal值', value: 'GoValue', width: 120 },
    { text: '操作', value: 'actions', width: 190, sortable: false },
]

// 数据导入表头
export const dataImportColumns = [
    { text: '文件名称', value: 'FileName', width: 120 },
    { text: '导入人', value: 'CreateUserId', width: 120 },
    { text: '类型', value: 'Remark', width: 120, dictionary: true },
    { text: '导入时间', value: 'CreateDate', width: 120 },
]

// 智能运维配置表头
export const maticMaintenanceColumns = [
    { text: '厂区', value: 'Factory', width: 100 },
    { text: '产品系列', value: 'ProductSeries', width: 100, dictionary: true },
    { text: '监控类型', value: 'TypeName', width: 120 },
    { text: '楼栋', value: 'Building', width: 120 },
    { text: '楼层', value: 'Floor', width: 120 },
    { text: '产线', value: 'Linename', width: 140 },
    { text: '工段', value: 'SegmentName', width: 120 },
    { text: '工站', value: 'StationLinename', width: 130 },
    { text: '设备', value: 'Device_Name', width: 130 },
    { text: '数据标签名称', value: 'DataTag', width: 140 },
    { text: '数据来源', value: 'DataFrom', width: 120 },
    { text: '位置', value: 'Position', width: 120 },
    { text: '运维负责人', value: 'Duty_Person', width: 110 },
    { text: '接警人', value: 'Recipient', width: 110 },
    { text: '群接警人', value: 'GroupRecipient', width: 150 },
    { text: '类型', value: 'DeviceType', width: 90 },
    { text: '状态', value: 'State', width: 90, dictionary: true },
    { text: '更新时间', value: 'ModifyDate', width: 160 },
    { text: '操作', value: 'actions', width: 120, sortable: false },
]

// 运维看板异常事件弹窗表头
export const maintenanceAbnormalEvent = [
    // { text: '安灯单号', value: 'EventNo', width: 120 },
    { text: '产线', value: 'AreaName', width: 140 },
    { text: '工段', value: 'ProductLineName', width: 140 },
    { text: '监控设备', value: 'EquipmentName', width: 140 },
    { text: '工单异常说明', value: 'AlarmContent', width: 180 },
    { text: '安灯状况', value: 'RecordStatus', width: 140 },
    { text: '当前安灯处理人', value: 'Currentman', width: 160 },
    { text: '安灯时间', value: 'CreateDate', width: 160 },
    { text: '处理时长', value: 'Predicttime', width: 140 },
]

// 一次良率分析表头
export const primaryYieldAnalysisColumns = [
    { text: '日期', value: 'PlanDate', width: 120 },
    { text: '班次', value: 'ShiftName', width: 100 },
    { text: '产线', value: 'ProductLine', width: 140 },
    { text: '良率分析', value: 'ErrorMsg', width: 150 },
    { text: '多项', value: '多项', width: 100 },
]

// 订单管理安灯记录
export const orderAndonColumns = [
    { text: 'Andon状态', value: 'Status', width: 120 },
    { text: '异常类型', value: 'Type', width: 120 },
    { text: '报警信息', value: 'ErrorMessage', width: 120 },
    { text: '报警时间', value: 'CreateDate', width: 120 },
]
// 工段收盘站采集点配置
export const collectionPointConfigColumns = [
    { text: '产线', value: 'ProductlineName', width: 140 },
    { text: '工段', value: 'SegmentName', width: 120 },
    { text: '成品物料', value: 'Materialname', width: 140 },
    { text: '产出采集点', value: 'Intag', width: 120 },
    { text: '合格产出点', value: 'Oktag', width: 120 },
    { text: '是否为组装工段', value: 'Islastsegment', width: 120, sortable: false },
    { text: 'PLC产出TAG点', value: 'PlcOktag', width: 120, sortable: false },
    { text: '是否启用', value: 'Enable', width: 120 },
    { text: '操作', value: 'actions', width: 120, sortable: false },
]
// 上料点配置
export const loadingPointConfigColumns = [
    { text: '产线', value: 'ProductlineName', width: 140 },
    { text: '工段', value: 'SegmentName', width: 120 },
    { text: '工站', value: 'ProcessName', width: 130 },
    { text: '成品物料', value: 'MaterialName', width: 120 },
    { text: '物料类别', value: 'MaterialType', width: 120 },
    { text: '组件物料', value: 'ComponentName', width: 180 },
    { text: '组件料号', value: 'ComponentCode', width: 120 },
    { text: '组件类别', value: 'ComponentClass', width: 120 },
    { text: '采集点位', value: 'TagId', width: 120 },
    { text: '单位', value: 'Uom', width: 100 },
    { text: '目标利用率', value: 'Tagert', width: 110, align: 'right' },
    { text: '单位用量', value: 'BomQty', width: 100, semicolonFormat: true },
    { text: '单价', value: 'Price', width: 100, semicolonFormat: true },
    { text: '排序', value: 'Seq', width: 100, align: 'right' },
    { text: '是否启用', value: 'Enable', width: 100 },
    { text: '操作', value: 'actions', width: 120, sortable: false },
]
// 操作日志
export const operationLogColumns = [
    { text: '操作人', value: 'UserName', width: 120 },
    // { text: '操作人工号', value: 'UserCode', width: 130 },
    { text: '服务名', value: 'ServiceName', width: 130 },
    { text: '方法名', value: 'MethodName', width: 130 },
    { text: '操作说明', value: 'MethodRemark', width: 140 },
    { text: '操作结果', value: 'ReturnValue', width: 140 },
    { text: '浏览器信息', value: 'BrowserInfo', width: 220 },
    { text: '客户端信息', value: 'ClientName', width: 150 },
    { text: '客户端IP', value: 'ClientIpAddress', width: 130 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '', value: 'noActions', width: 0, sortable: false },
]
export const unitConversionColumn = [
    { text: '物料', value: 'MaterialCode', width: 120, dictionary: true },
    { text: '源单位名称', value: 'FormUnitName', width: 120 },
    { text: '源单位数量', value: 'ConvertFormQty', width: 120 },
    { text: '转换单位名称', value: 'ToUnitName', width: 120 },
    { text: '转换单位数量', value: 'ConvertToQty', width: 120 },
    { text: '生效开始时间', value: 'EffectiveBeginDate', width: 120 },
    { text: '生效结束时间', value: 'EffectiveEndDate', width: 120 },
    { text: '备注', value: 'Remark', width: 120 },
    { text: '操作', value: 'actions', width: 120, sortable: false },
]
//库存清单表头
export const InventoryListColumn = [
    { text: '详情', value: 'detail', width: 60, fixed: 'left' },
    { text: '物料', value: 'Material', width: 200, sortable: true },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', prop: "BatchId", width: 120, sortable: true },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'SSCC/Container', width: 220, sortable: true },
    { text: '生效开始时间', value: 'Quantity', width: 100, sortable: true },
    { text: '生效结束时间', value: 'Location', width: 100, sortable: true },
    { text: '生效结束时间', value: 'Expiration', width: 150 },
    { text: '工单号', value: 'PpmPro', prop: "ProBatch", width: 150, sortable: true },
    { text: '工单号', value: 'Sapformula', prop: "Sapformula", width: 150, sortable: true },
    { text: '源单位名称', value: 'Class', prop: "ClassDec", width: 120, sortable: true },

    { text: '生效结束时间', value: 'Bucketnum', prop: "Bucketnum", width: 100, sortable: true },

    { text: '生效结束时间', value: 'Suppiername', prop: "Suppiername", width: 150, sortable: true },

    { text: '生效结束时间', value: 'Remark', prop: "Remark", width: 150, sortable: true },
    { text: '生效结束时间', value: 'Created', width: 150 },

]
//库存清单表头
export const InventoryListColumnWithNotdetail = [
    { text: '物料', value: 'Material', width: 200, sortable: true },
    { text: '工单号', value: 'PpmPro', prop: "ProBatch", width: 150, sortable: true },
    { text: '工单号', value: 'Formula', prop: "Formula", width: 150, sortable: true },
    { text: '源单位名称', value: 'Class', prop: "ClassDec", width: 120, sortable: true },
    { text: '源单位名称', value: 'Bucketnum', prop: "Bucketnum", width: 150, sortable: true },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', prop: "BatchId", width: 200, sortable: true },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'SSCC/Container', width: 200, sortable: true },
    { text: '生效开始时间', value: 'Quantity', width: 120, sortable: true },
    { text: '生效结束时间', value: 'Location', width: 100, sortable: true },
    { text: '生效结束时间', value: 'Suppiername', prop: "Suppiername", width: 150, sortable: true },
    { text: '生效结束时间', value: 'Expiration', width: 150 },
    { text: '生效结束时间', value: 'Remark', prop: "Remark", width: 150, sortable: true },
    { text: '生效结束时间', value: 'Created', width: 150 },

]
//库存清单表头
export const InventoryListColumnNodetail = [
    { text: '详情', value: 'detail', width: 60, fixed: 'left' },
    { text: '物料', value: 'Material', width: 200, sortable: true },
    { text: '工单号', value: 'PpmPro', prop: "ProBatch", width: 150, sortable: true },
    { text: '工单号', value: 'Formula', prop: "Formula", width: 150, sortable: true },
    { text: '源单位名称', value: 'Class', prop: "ClassDec", width: 120, sortable: true },
    { text: '源单位名称', value: 'Bucketnum', prop: "Bucketnum", width: 150, sortable: true },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', prop: "BatchId", width: 200, sortable: true },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'SSCC/Container', width: 200, sortable: true },
    { text: '生效开始时间', value: 'Quantity', width: 120, sortable: true },
    { text: '生效结束时间', value: 'Location', width: 100, sortable: true },
    { text: '生效结束时间', value: 'Suppiername', prop: "Suppiername", width: 150, sortable: true },
    { text: '生效结束时间', value: 'Expiration', width: 150 },
    { text: '生效结束时间', value: 'Remark', prop: "Remark", width: 150, sortable: true },
    { text: '生效结束时间', value: 'Created', width: 150 },

]
//库存清单表头
export const InventoryStorageListColumn = [
    { text: '详情', value: 'detail', width: 60, fixed: 'left' },
    { text: '物料', value: 'Material', width: 200, Sortable: true },
    { text: '工单号', value: 'PpmPro', prop: "ProBatch", width: 150, Sortable: true },
    // { text: '生效开始时间', value: 'Formula', prop: "Formula", width: 150 },
    { text: '源单位名称', value: 'Class', prop: "ClassDec", width: 80, Sortable: true },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', prop: "BatchId", width: 100, Sortable: true },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'SSCC/Container', width: 200, Sortable: true },
    { text: '生效开始时间', value: 'Quantity', width: 100, Sortable: true },
    { text: '生效结束时间', value: 'Location', width: 120, Sortable: true },
    { text: '源单位数量', value: 'Suppiername', prop: "Suppiername", width: 150, Sortable: true },
    { text: '生效结束时间', value: 'Expiration', width: 150 },
    { text: '生效结束时间', value: 'Created', width: 150 },
]

export const InventoryTransferListColumn = [
    { text: '物料', value: 'Material' },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', prop: "BatchId", width: 120 },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'SSCC/Container', width: 120 },
    { text: '生效开始时间', value: 'Quantity', width: 100 },
    { text: '生效结束时间', value: 'Expiration', width: 180 },
]
export const InventoryMergeListColumn = [
    { text: '物料', value: 'Material' },
    { text: '源单位数量', value: 'Batch', prop: "BatchId", width: 200 },
    { text: '转换单位数量', value: 'SSCC/Container', width: 200 },
    { text: '生效开始时间', value: 'Quantity', width: 60 },
    { text: '生效结束时间', value: 'UOM', prop: "MinUnit", width: 50 },
]

//生产历史表头
export const ProductionHistoryColumn = [
    { text: '详情', value: 'ProcessOrder', prop: "ProcessOrder", width: 140 },
    { text: '生效开始时间', value: 'Formula', prop: "Formula", width: 80 },
    { text: '物料', value: 'Material', width: 100 },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', prop: "LotCode", width: 120 },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'SSCC', prop: "SUB_LOT_ID", width: 120 },
    { text: '生效开始时间', value: 'Quantity', prop: "Quantity", width: 100 },
    { text: '生效开始时间', value: 'Machine', prop: "Machine", width: 100 },
    { text: '源单位名称', value: 'Destination', prop: "Destination", width: 150 },
    { text: '生效结束时间', value: 'ShiftId', prop: "ShiftName", width: 70 },
    { text: '生效结束时间', value: 'Source', width: 100 },
    { text: '生效结束时间', value: 'SendStates', prop: "SendStates", width: 150 },
    { text: '凭证', value: 'Mblnr', prop: "Mblnr", width: 120 },
    { text: 'Type', value: 'Type', prop: "Type", width: 120 },
    { text: 'Msg', value: 'Msg', prop: "Msg", width: 120 },
    { text: 'SendTime', value: 'SendTime', prop: "SendTime", width: 120 },
    { text: '生效结束时间', value: 'Reason', prop: "ReasonCode", width: 70 },
    { text: '生效结束时间', value: 'Comment', prop: "ActuaComment", width: 100 },
    { text: '生效结束时间', value: 'ProductDate', prop: "CreateDate", width: 120 },
    { text: '生效结束时间', value: 'Date', prop: "ModifyDate", width: 120 },
    { text: '生效结束时间', value: 'operate', width: 100, fixed: "right" },
]

//生产统计表头
export const ProductionSummaryColumn = [
    { text: '详情', value: 'detail', width: 60, fixed: 'left' },
    { text: '详情', value: 'Line', prop: "LineName", width: 200 },
    { text: '物料', value: 'Machine', prop: "Machine", width: 120 },
    { text: '物料', value: 'ExecutionStatus', width: 50 },
    { text: '源单位数量', value: 'ProcessOrder', prop: "ProcessOrder", width: 200 },
    { text: '转换单位数量', value: 'Formula', prop: "Formula", width: 80 },
    { text: '源单位数量', value: 'Material' },
    { text: '转换单位名称', value: 'ShiftDate', width: 200 },
    { text: '转换单位数量', value: 'Planned', width: 100 },
    { text: '生效开始时间', value: 'Shift', width: 100 },
    { text: '生效开始时间', value: 'Daily', width: 100 },
    { text: '源单位名称', value: 'Total', width: 100 },
    { text: '生效结束时间', value: 'Complete', width: 250 },
]
export const ProductionSummaryDrawColumn = [
    { text: '详情', value: 'Destination', prop: "Loction" },
    { text: '详情', value: 'LastProduction', prop: "CreateDate", width: 200 },
    { text: '物料', value: 'Material', width: 120 },
    { text: '源单位数量', value: 'Batch', prop: "LotCode", width: 100 },
    { text: '转换单位名称', value: 'Quantity', width: 120 },
    { text: '转换单位数量', value: 'Inventory', width: 120 },
    { text: '生效开始时间', value: 'SublotCount', prop: "Sublotcount", width: 120 },
    { text: '生效结束时间', value: 'operate', width: 80, fixed: "right" },
]

//消费历史
export const ConsumptionHistoryColumn = [
    { text: '详情', value: 'ProcessOrder', prop: "ProcessOrder", width: 140 },
    { text: '物料', value: 'Material', width: 150 },
    { text: '物料', value: 'Suppiername', width: 150 },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', prop: "BatchCode", width: 120 },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'SSCCValue', width: 150 },
    { text: '生效开始时间', value: 'Quantity' },
    { text: '生效开始时间', value: 'Machine', prop: "MachineName", width: 120 },
    { text: '生效结束时间', value: 'Source', width: 120 },
    { text: '生效结束时间', value: 'ShiftId', prop: "ShiftName", width: 80 },
    { text: '生效结束时间', value: 'Date', prop: "CreateDate", width: 180 },
    { text: '生效结束时间', value: 'SendStates', prop: "SendStates", width: 150 },
    { text: '凭证', value: 'Mblnr', prop: "Mblnr", width: 120 },
    { text: 'Type', value: 'Type', prop: "Type", width: 120 },
    { text: 'Msg', value: 'Msg', prop: "Msg", width: 120 },
    { text: 'Msg', value: 'SendTime', prop: "SendTime", width: 120 },
    //{ text: '生效结束时间', value: 'ReverseState', prop: "ReverseState", width: 100 },
    { text: '生效结束时间', value: 'operate', width: 80, fixed: "right" },
]

//传输历史
export const TransferHistoryColumn = [
    { text: '详情', value: 'OldLocation', width: 120 },
    { text: '物料', value: 'NewLocation', width: 150 },
    { text: '物料', value: 'Suppiername', width: 150 },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'OldLotId', width: 100 },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'sscc/container1', width: 200 },
    { text: '生效开始时间', value: 'Destination', width: 100 },
    // { text: '生效开始时间', value: 'DestinationMaterial', width: 100 },
    { text: '源单位数量', value: 'BatchStatus2', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'NewLotId', width: 100 },
    { text: '转换单位名称', value: 'SSCCStatus2', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'sscc/container2', width: 200 },
    { text: '生效结束时间', value: 'Quantity', width: 100 },
    // { text: '生效结束时间', value: 'SAP', width: 80 },
    { text: '生效结束时间', value: 'Type', width: 80 },
    { text: '生效结束时间', value: 'TMode', prop: "TMode", width: 80 },
    { text: '生效结束时间', value: 'Date', width: 150 },
    // { text: '生效结束时间', value: 'WmsPrintno', width: 150 },
    { text: '生效结束时间', value: 'SapPrintno', width: 100 },
    { text: '生效结束时间', value: 'Tranremarks',  width: 80 },
    { text: '生效结束时间', value: 'operate', width: 80, fixed: "right" },
]

//托盘清单
export const PalletListColumn = [
    { text: '详情', value: 'detail', width: 60, fixed: "left" },
    { text: '详情', value: 'Machine', prop: "Machine", width: 150 },
    { text: '物料', value: 'ProcessOrder', prop: "ProcessOrder", width: 200 },
    { text: '源单位数量', value: 'Material', width: 200 },
    { text: '源单位数量', value: 'Pallet', width: 200 },
    { text: '转换单位名称', value: 'Destination', width: 160 },
    { text: '转换单位数量', value: 'Quantity', width: 100 },
    { text: '生效开始时间', value: 'PrintCount', prop: "PrintCount", width: 150 },
    { text: '生效结束时间', value: 'InsortedAt', prop: "CreateDate", width: 150 },
    { text: '生效结束时间', value: 'Verified', prop: "Verified", width: 100 },
    { text: '生效结束时间', value: 'Produced', prop: "Produced", width: 100 },
    { text: '生效结束时间', value: 'operate', width: 120, fixed: "right" },
]

//批次托盘
export const BatchPalletsColumn = [
    { text: '详情', value: 'detail', width: 50, fixed: "left" },
    { text: '转换单位数量', value: "PlanTime", width: 150 },
    // { text: '详情', value: 'PlanStartTime', prop: "PlanStartTime", width: 150 },
    { text: '详情', value: 'id', prop: "ContainerName", width: 150 },
    { text: '物料', value: 'Loaction', prop: "LocationF", width: 100 },
    { text: '源单位数量', value: 'Bin', prop: "LocationS", width: 100 },
    { text: '源单位数量', value: 'PO' },
    { text: '源单位数量', value: 'Sequence' },
    { text: '生效开始时间', value: 'Formula', prop: "Formula", width: 80 },
    // { text: '转换单位名称', value: 'Machine', prop: "CMachine"},
    // { text: '转换单位数量', value: 'Batch', prop: "BNubmber", width: 100 },
    { text: '生效开始时间', value: 'Material', width: 250 },
    { text: '生效结束时间', value: 'FullBags', prop: "FullNumber", width: 60 },
    { text: '生效结束时间', value: 'PartialBags', prop: "PartialNumber", width: 60 },
    { text: '生效结束时间', value: 'TotalBags', prop: "AllNumber", width: 60 },
    { text: '生效结束时间', value: 'Complete', width: 80 },
    { text: '生效结束时间', value: 'CreatedBy', prop: "CreateUserId", width: 80 },
    { text: '生效结束时间', value: 'Date', prop: "CreateDate", width: 150 },

]

export const BatchPalletsDrawColumn = [
    { text: '生效开始时间', value: 'Material', width: 200 },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', prop: "BatchId", width: 100 },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    // { text: '转换单位名称', value: 'SourceSSCC', prop: "TranSscc", width: 120 },
    { text: '转换单位数量', value: 'SSCC', prop: "Sscc", width: 100 },
    { text: '转换单位数量', value: 'Quantity', width: 80 },
    { text: '详情', value: 'Type', prop: "TranType", width: 100 },
    { text: '物料', value: 'Expiry', prop: "ExpirationDate", width: 120 },
    { text: '源单位数量', value: 'User', prop: "TranUser", width: 80 },
    { text: '源单位数量', value: 'TransferDate', prop: "TranDate", width: 100 },


]

export const BatchPalletsTransferColumn = [
    { text: '生效开始时间', value: 'Date', width: 150 },
    { text: '源单位数量', value: 'Action', width: 120 },
    { text: '转换单位数量', value: 'Details' },
    { text: '转换单位数量', value: 'User', width: 100 },

]
//容器管理
export const ContainerManagementColumn = [
    { text: '详情', value: 'detail', width: 60, fixed: "left" },
    { text: '转换单位数量', value: 'id', width: 150 },
    { text: '源单位数量', value: 'Status', width: 100, prop: "ContainerState" },
    { text: '源单位数量', value: 'Location', width: 200 },
    { text: '转换单位数量', value: 'Material', width: 200 },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', width: 100, prop: "LotId" },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'SSCC', width: 150, prop: "SubLotId" },
    { text: '转换单位数量', value: 'Quantity', width: 80 },
    { text: '转换单位数量', value: 'Expiration', width: 150, prop: "ExpirationDate" },
    { text: '转换单位数量', value: 'PO', width: 120, prop: "BatchCode" },
    { text: '转换单位数量', value: 'StatusTime', width: 150, prop: "StatesTime" },
]
export const ContainerManagementTransferColumn = [
    { text: '物料', value: 'Material', width: 200 },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', width: 120 },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'SSCC/Container', width: 150 },
    { text: '生效开始时间', value: 'Quantity', width: 120 },
    { text: '生效结束时间', value: 'Expiration', width: 150 },
    { text: '生效结束时间', value: 'Result', width: 100 },
]
export const ContainerManagementInventoryColumn = [
    { text: '物料', value: 'split', width: 60, fixed: 'left' },
    { text: '物料', value: 'Material', width: 200 },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', prop: "BatchId", width: 130 },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'SSCC/Container', width: 150 },
    { text: '生效开始时间', value: 'Quantity', width: 80 },
    { text: '源单位数量', value: 'Location', width: 150 },
    { text: '生效结束时间', value: 'Expiration', width: 150 },
]
export const ContainerManagementHistoryColumn = [
    { text: '生效开始时间', value: 'Date', prop: "CreateDate", width: 200 },
    { text: '源单位数量', value: 'Action', prop: "TType", width: 150 },
    { text: '转换单位数量', value: 'Comment', prop: "CComment" },
    { text: '转换单位数量', value: 'User', prop: "CreateUserId", width: 100 },
]
//复称
export const RepeatedweighingColumn = [
    { text: '生效开始时间', value: 'result', prop: "IsPrechecked", width: 80 },
    { text: '转换单位数量', value: 'Batch', width: 180 },
    // { text: '生效开始时间', value: 'TraceCode', prop: "SbSscc", width: 150 },
    { text: '生效开始时间', value: 'MaterialPF', prop: "MaterialPF", width: 200 },
    // { text: '生效开始时间', value: 'Batches', prop: "BatchCode", width: 200 },
    { text: '生效开始时间', value: 'Quantity', width: 100 },
    { text: '生效开始时间', value: 'Repeatedweight', width: 100 },
    { text: '源单位数量', value: 'PlanTime', width: 100 },
    { text: '源单位数量', value: 'LineCode', prop: "LineCode", width: 100 },
    { text: '转换单位数量', value: 'ProcessOrder(Batch)', prop: "ProductionOrderNo", width: 120 },
    { text: '源单位数量', value: 'Sapformula', prop: "Sapformula", width: 100 },
    { text: '源单位数量', value: 'Sequences', width: 70 },
    { text: '源单位数量', value: 'PROMaterial' },
    { text: '源单位数量', value: 'ShiftName', prop: "ShiftName", width: 100 },
    { text: '源单位数量', value: 'Date/User', width: 150 },




]
//复称详情
export const RepeatedweighingDrawColumn = [
    { text: '转换单位数量', value: 'Material', prop: "Material", width: 200 },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batches', prop: "LBatch", width: 200 },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'TraceCode', prop: "SbSscc", width: 200 },
    { text: '详情', value: 'Type', prop: "HType", width: 100 },
    { text: '生效开始时间', value: 'Quantity', width: 100 },
    { text: '生效结束时间', value: 'room', prop: "Location", width: 100 },
    { text: '生效结束时间', value: 'Location', prop: "EBin", width: 100 },
    { text: '生效结束时间', value: 'Expiration', width: 150 },
]
//物料原料管理
export const MaterialLabelTable = [
    { text: '转换单位数量', value: 'ProcessOrder', prop: "ProductionOrderNo", width: 150 },
    { text: '转换单位数量', value: 'BNumber', prop: "BNumber", width: 80 },
    { text: '生效开始时间', value: 'Material', width: 150 },
    { text: '生效开始时间', value: 'TagerQty', prop: "TagerQty", width: 150 },
    { text: '生效开始时间', value: 'BagSize', prop: "BagSize", width: 150 },
    { text: '生效开始时间', value: 'LotCode', width: 150 },
    { text: '生效开始时间', value: 'SubLotcode', width: 280 },
    { text: '生效开始时间', value: 'InventQty', prop: "InventQty", width: 200 },
    // { text: '生效开始时间', value: 'FullPage', prop: "FullPage", width: 150 },
    // { text: '生效开始时间', value: 'InventPqty', prop: "InventPqty", width: 150 },
    { text: '生效开始时间', value: 'Expiration', prop: "ExdateQty", width: 250 },
    { text: '生效开始时间', value: 'operate', width: 120 },
]
export const MaterialLabelTablecopy = [
    { text: '转换单位数量', value: 'index', prop: "Xnumber", width: 60 },
    { text: '转换单位数量', value: 'ProcessOrder', prop: "ProductionOrderNo", width: 150 },
    { text: '转换单位数量', value: 'Sapformula', prop: "Sapformula", width: 150 },
    { text: '转换单位数量', value: 'BNumber', prop: "BNumber", width: 80 },
    { text: '生效开始时间', value: 'Material', width: 150 },
    { text: '生效开始时间', value: 'TagerQty', prop: "TagerQty", width: 150 },
    { text: '生效开始时间', value: 'BagSize', prop: "BagSize", width: 150 },
    { text: '生效开始时间', value: 'LotCode', width: 150 },
    { text: '生效开始时间', value: 'SubLotcode', width: 280 },
    { text: '生效开始时间', value: 'InventQty', prop: "InventQty", width: 200 },
    { text: '生效开始时间', value: 'Remark', prop: "Remark", width: 200 },

    // { text: '生效开始时间', value: 'FullPage', prop: "FullPage", width: 150 },
    // { text: '生效开始时间', value: 'InventPqty', prop: "InventPqty", width: 150 },
    { text: '生效开始时间', value: 'Expiration', prop: "ExdateQty", width: 250 },
]
export const MaterialPreparationColumnLable = [
    { text: '源单位数量', value: 'Line', prop: "ProLine", width: 100 },
    { text: '转换单位数量', value: 'ProcessOrder', prop: "ProOrder", width: 140 },
    { text: '转换单位数量', value: 'FormulaNo', width: 80 },
    { text: '转换单位数量', value: 'ShiftName', prop: "ShiftName", width: 80 },
    { text: '转换单位数量', value: 'Resource', prop: "ProResource", width: 100 },
    { text: '生效开始时间', value: 'POStatus', prop: "ProStatus", width: 100 },
    { text: '生效开始时间', value: 'Material', width: 290 },
    { text: '生效开始时间', value: 'Mdetialdesc', prop: "Mdetialdesc", width: 280 },
    { text: '生效开始时间', value: 'ProductFamily', prop: "ProductFamily", width: 100 },
    { text: '生效开始时间', value: 'Quantity', width: 80 },
    { text: '生效开始时间', value: 'Batches', width: 80 },
    { text: '生效开始时间', value: 'Start', prop: "CreateDate", width: 150 },

]
export const MaterialPreparationColumnLableNew = [
    { text: '源单位数量', value: 'Line', prop: "ProLine", width: 100 },
    { text: '转换单位数量', value: 'ProcessOrder', prop: "ProOrder", width: 140 },
    { text: '转换单位数量', value: 'JLCode', prop: "Material", width: 150 },
    // { text: '转换单位数量', value: 'JLName', prop: "JLName" , width: 150},
    { text: '转换单位数量', value: 'JLNumber', prop: "Quantity", width: 120 },
    { text: '转换单位数量', value: 'FormulaNo', prop: "FormulaNo", width: 80 },
    // { text: '转换单位数量', value: 'ShiftName', prop: "ShiftName" , width: 80 },
    { text: '转换单位数量', value: 'Resource', prop: "ProResource", width: 100 },
    { text: '生效开始时间', value: 'POStatus', prop: "ProStatus", width: 100 },
    { text: '生效开始时间', value: 'Material', width: 200 },
    { text: '生效开始时间', value: 'Mdetialdesc', prop: "Mdetialdesc", width: 350 },
    // { text: '生效开始时间', value: 'ProductFamily', prop: "ProductFamily", width: 100 },
    // { text: '生效开始时间', value: 'Quantity', width: 80 },
    { text: '生效开始时间', value: 'Sequences', width: 80 },
    { text: '生效开始时间', value: 'SAPDate', prop: "CreateDate" },

]
//材料制备栏
export const MaterialPreparationColumn = [
    { text: '生效开始时间', value: 'Starts', width: 160 },
    { text: '源单位数量', value: 'Line', prop: "LineCode", width: 100 },
    { text: '转换单位数量', value: 'ProcessOrder', prop: "ProOrder" ,width: 150},
    { text: '转换单位数量', value: 'FormulaNo', prop: "FormulaNo" , width: 100 },
    { text: '转换单位数量', value: 'Sequence', width: 100  },
    { text: '转换单位数量', value: 'ShiftName', prop: "ShiftName", width: 100 },
    { text: '转换单位数量', value: 'Resource', prop: "ProResource", width: 150 },
    { text: '生效开始时间', value: 'NowState', prop: "NowState", width: 150 },
    { text: '生效开始时间', value: 'POStatus', prop: "ProStatus", width: 150 },
    { text: '生效开始时间', value: 'Material' },
    // { text: '生效开始时间', value: 'Mdetialdesc',prop: "Mdetialdesc" , width: 300  },
    // { text: '生效开始时间', value: 'ProductFamily', prop: "ProductFamily", width: 200 },
    { text: '生效开始时间', value: 'Quantity', width: 100 },
    { text: '生效开始时间', value: 'Batches', width: 100 },


]
export const BuildPalletsColumn = [
    { text: '源单位数量', value: 'PO', prop: "ProductionOrderNo", width: 150 },
    { text: '转换单位数量', value: 'LineCode', prop: "LineCode", width: 150 },
    { text: '转换单位数量', value: 'Batch', prop: "MBatchNumber", width: 150 },
    { text: '生效开始时间', value: 'Material' },

    { text: '生效开始时间', value: 'Quantity', width: 150 },
    { text: '生效开始时间', value: 'Inventory', width: 100 },
    { text: '生效开始时间', value: 'BagSize', width: 100 },
    { text: '生效开始时间', value: 'FullBags', prop: "FullPage", width: 100 },
    { text: '生效开始时间', value: 'PartialBags', width: 150 },
    { text: '生效开始时间', value: 'Complete', prop: "CompleteStates", width: 150 },
    { text: '生效开始时间', value: 'Consumed', prop: "ConsumedStates", width: 150 },

]
export const BuildPalletsColumnCLBL = [
    { text: '源单位数量', value: 'PO', prop: "ProductionOrderNo", width: 200 },
    { text: '转换单位数量', value: 'LineCode', prop: "LineCode", width: 150 },
    // { text: '转换单位数量', value: 'Machine', prop: "EquipmentName", width: 300 },
    { text: '转换单位数量', value: 'Batch', prop: "MBatchNumber", width: 100 },
    { text: '生效开始时间', value: 'Material' },
    { text: '转换单位数量', value: 'CheakState', prop: "CheakState", width: 50 },
    { text: '生效开始时间', value: 'Quantity', width: 200 },
    { text: '生效开始时间', value: 'Inventory', width: 100 },
    { text: '生效开始时间', value: 'BagSize', width: 100 },
    { text: '生效开始时间', value: 'PartialBags', width: 200 },
    { text: '生效开始时间', value: 'Complete', prop: "CompleteStates", width: 100 },
    { text: '生效开始时间', value: 'Consumed', prop: "ConsumedStates", width: 100 },

]
export const BuildPalletsColumnMaterial = [
    { text: '生效开始时间', value: 'Material' },
    // { text: '转换单位数量', value: 'Phase', prop: "SegmentCode" },
    // { text: '转换单位数量', value: 'Machine', prop: "EquipmentName", width: 300 },
    { text: '生效开始时间', value: 'QuantityRequired', width: 150 },
    { text: '生效开始时间', value: 'Inventory', width: 200 },
    // { text: '生效开始时间', value: 'CompleteStates', width: 100 },
]
export const MaterialPreparationBuildColumn = [
    { text: '源单位数量', value: 'Batch', prop: "BatchNumber" },
    { text: '转换单位数量', value: 'Quantity' },
    { text: '转换单位数量', value: 'PrepStatus', prop: "ItemName", width: 200 },
    { text: '生效开始时间', value: 'TippingDone', prop: "TippingDone" },
    { text: '生效开始时间', value: 'Ingredients' },
]

export const AvallableInventoryColumn = [
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 150, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', prop: "LBatch", width: 200 },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 150, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'SSCC/Container', prop: "SbSscc" },
    { text: '生效开始时间', value: 'Quantity', width: 150 },
    // { text: '生效开始时间', value: 'Type', prop: "HType", width: 150 },
    { text: '生效结束时间', value: 'Location', prop: "InventLocation", width: 200 },
    { text: '生效结束时间', value: 'Bin', prop: "InventBin", width: 150 },
    { text: '生效结束时间', value: 'Expiration', prop: "ExpirationDate", width: 200 },
]
export const POInventoryColumn = [
    { text: '源单位数量', value: 'SSCC/Batch', width: 200 },
    { text: '源单位数量', value: 'SSCCStatus', width: 100 },
    { text: '源单位数量', value: 'Material' },
    { text: '转换单位名称', value: 'Quantity', width: 150 },
    { text: '转换单位数量', value: 'Type', width: 100 },
    { text: '生效开始时间', value: 'Expiry', width: 200 },
    { text: '生效开始时间', value: 'User', width: 150 },
    { text: '生效结束时间', value: 'TransferDate', width: 200 },
]
export const POBatchPalletsColumn = [
    { text: '源单位数量', value: 'SSCC/Batch', width: 300 }, //value 是label   单行  prop: 
    { text: '源单位数量', value: 'SSCCStatus', width: 60 },
    { text: '源单位数量', value: 'Material' },
    { text: '转换单位名称', value: 'Quantity', width: 150 },
    { text: '转换单位数量', value: 'Type', prop: "HType", width: 100 },
    // { text: '生效开始时间', value: 'Expiry', prop: "ExpirationDate" },
    { text: '生效开始时间', value: 'Expiry', prop: "ExpirationDate", width: 250 },
    { text: '生效开始时间', value: 'User', prop: "InvenUser", width: 150 },
    { text: '生效结束时间', value: 'TransferDate', prop: "TranDate", width: 250 },
    // { text: '生效结束时间', value: 'operate',  width: 150 },

]
export const POInventoryPalletsColumn = [
    { text: '源单位数量', value: 'Batch', prop: "LBatch" }, //value 是label   单行  prop: 
    { text: '源单位数量', value: 'BatchStatus', prop: "LStatus", width: 100 },
    { text: '源单位数量', value: 'SSCC', prop: "SbSscc" }, //value 是label   单行  prop: 
    { text: '源单位数量', value: 'SSCCStatus', prop: "SbStatus", width: 100 },
    { text: '转换单位名称', value: 'Quantity', width: 150 },
    { text: '转换单位名称', value: 'Type', prop: "HType", width: 100 },
    { text: '转换单位数量', value: 'ProcessOrder', prop: "ProductionOrderNo", width: 100 },
    { text: '生效开始时间', value: 'Batch', prop: "BatchCode", width: 150 },
    { text: '生效开始时间', value: 'Bin', prop: "InventBin", width: 200 },
    { text: '生效开始时间', value: 'Expiry', prop: "ExpirationDate", width: 200 },
]
//PO Managemen
export const POManagemenOverview = [
    { text: '源单位数量', value: 'PlantNode', width: 200 },
    { text: '转换单位数量', value: 'po', prop: "ProcessOrder", width: 130 },
    { text: '转换单位数量', value: 'Sequence', prop: "Sequence", width: 100 },
    { text: '转换单位数量', value: 'MaterialCode', prop: "MaterialCode",width: 150 },
    { text: '转换单位数量', value: 'MaterialName', prop: "MaterialName",width: 250 },
    //{ text: '生效开始时间', value: 'Formula', prop: "Formula", width: 150 },
    { text: '生效开始时间', value: 'Batch', prop: "BatchCode", width: 100 },
    { text: '生效开始时间', value: 'BatchQty', width: 100 },
    { text: '生效开始时间', value: 'Planned', prop: "TargetQuantity", width: 100 },
    { text: '生效开始时间', value: 'Total', prop: "Total", width: 100 },
    { text: '生效开始时间', value: 'Complete', prop: "Complete" },

]

//PO Managemen
export const POManagemenPoList = [
    { text: '生效开始时间', value: 'PlanStartTime', prop: "PlanStartTime", width: 150 },
    { text: '生效开始时间', value: 'PlanEndTime', prop: "PlanEndTime", width: 150 },
    //{ text: '生效开始时间', value: 'ScheduledStart', prop: "PlanStartTime", width: 180 },
    { text: '源单位数量', value: 'ProcessOrder', prop: "ProcessOrder", width: 200 },
    { text: '转换单位数量', value: 'Sequence', prop: "Sequence", width: 100 },
    { text: '转换单位数量', value: 'Resource', prop: "Resource", width: 120 },
    //{ text: '转换单位数量', value: 'FillLineCode', prop: "FillLineCode", width: 150 },
    { text: '转换单位数量', value: 'SegmentCode', prop: "SegmentCode",width: 120 },
    { text: '转换单位数量', value: 'MaterialCode', prop: "MaterialCode",width: 150 },
    { text: '转换单位数量', value: 'MaterialName', prop: "MaterialName",width: 250 },
    //{ text: '生效开始时间', value: 'Formula', prop: "Formula", width: 200 },
    // { text: '生效开始时间', value: 'Instruction', width: 200 },
    { text: '生效开始时间', value: 'TargetQuantity', prop: "TargetQuantity", width: 180 },
    { text: '生效开始时间', value: 'Unit1', prop: "Unit1", width: 70 },
    //{ text: '转换单位数量', value: 'CipDetail', prop: "CipDetail", width: 150 },
    // { text: '生效开始时间', value: 'LineNominalSpeed', width: 150 },
    //{ text: '生效开始时间', value: 'Grille', prop: "Grille", width: 100 },
    //{ text: '生效开始时间', value: 'IsHavePreservative', prop: "IsHavePreservative", width: 100 },
    { text: '生效开始时间', value: 'BoilingStatus', prop: "BoilingStatus", width: 180 },
    { text: '生效开始时间', value: 'Comment', prop: "Comment" }
    //{ text: '生效开始时间', value: 'operate', width: 120 },

]

export const POManagemenAvailable = [
    //{ text: '生效开始时间', value: 'SapDate', prop: "SapDate", width: 180 },
    { text: '生效开始时间', value: 'PlanStartTime', prop: "PlanStartTime", width: 150 },
    { text: '生效开始时间', value: 'PlanEndTime', prop: "PlanEndTime", width: 150 },
    { text: '源单位数量', value: 'ProcessOrder', prop: "ProcessOrder", width: 200 },
    { text: '转换单位数量', value: 'Sequence', prop: "Sequence", width: 100 },
    { text: '转换单位数量', value: 'Resource', prop: "Resource", width: 120 },
    { text: '转换单位数量', value: 'SegmentCode', prop: "SegmentCode",width: 120 },
    //{ text: '转换单位数量', value: 'FillLineCode', prop: "FillLineCode", width: 150 },
    { text: '转换单位数量', value: 'MaterialCode', prop: "MaterialCode",width: 150 },
    { text: '转换单位数量', value: 'MaterialName', prop: "MaterialName",width: 250 },
    //{ text: '生效开始时间', value: 'Formula', prop: "Formula", width: 200 },
    // { text: '生效开始时间', value: 'Instruction', width: 200 },
    { text: '生效开始时间', value: 'TargetQuantity', prop: "TargetQuantity", width: 180 },
    { text: '生效开始时间', value: 'Unit1', prop: "Unit1", width: 70 },
    // { text: '生效开始时间', value: 'LineNominalSpeed', width: 150 },
    //{ text: '生效开始时间', value: 'Grille', prop: "Grille", width: 100 },
    //{ text: '生效开始时间', value: 'IsHavePreservative', prop: "IsHavePreservative", width: 100 },
    { text: '生效开始时间', value: 'Comment', prop: "Comment" },
    { text: '生效开始时间', value: 'operate', width: 120 },

]
export const POManagemenActive = [
    
    { text: '转换单位数量', value: 'Resource', prop: "Resource", width: 120 },
    { text: '转换单位数量', value: 'SegmentCode', prop: "SegmentCode",width: 120 },
    { text: '源单位数量', value: 'ProcessOrder', prop: "ProcessOrder", width: 250 },
    // { text: '转换单位数量', value: 'Sequence', prop: "Sequence", width: 100 },
    { text: '转换单位数量', value: 'MaterialCode', prop: "MaterialCode",width: 150 },
    { text: '转换单位数量', value: 'MaterialName', prop: "MaterialName",width: 250 },
    //{ text: '生效开始时间', value: 'Formula', prop: "Formula", width: 200 },
    { text: '生效开始时间', value: 'Quantity', prop: "TargetQuantity", width: 200 },
    { text: '生效开始时间', value: 'Unit1', prop: "Unit1", width: 70 },
    //{ text: '生效开始时间', value: 'LineNominalSpeed', width: 200 },
    { text: '生效开始时间', value: 'Comment', prop: "Comment" },
    //{ text: '生效开始时间', value: 'Execution', prop: "Status", width: 150 },
]

export const POManagemenHistory = [
    { text: '转换单位数量', value: 'BatchCode', prop: "BatchCode" },
    { text: '源单位数量', value: 'start', prop: "StartTime", width: 250 },
    { text: '转换单位数量', value: 'end', prop: "EndTime", width: 250 },
    { text: '生效开始时间', value: 'ProcessOrder', prop: "ProcessOrder", },
    { text: '转换单位数量', value: 'Resource', prop: "Resource", width: 120 },
    { text: '转换单位数量', value: 'SegmentCode', prop: "SegmentCode",width: 120 },
    { text: '转换单位数量', value: 'MaterialCode', prop: "Sequence",width: 150 },
    { text: '转换单位数量', value: 'MaterialName', prop: "Sequence",width: 250 },
    { text: '生效开始时间', value: 'Quantity', prop: "TargetQuantity", width: 150 },
    { text: '生效开始时间', value: 'NominalSpeed', width: 150 },
    { text: '生效开始时间', value: 'Execution', prop: "Status", width: 150 },
    { text: '生效开始时间', value: 'Comment', prop: "Comments", width: 150 },
    { text: '生效开始时间', value: 'User', width: 150, prop: "ModifyUserId" },
]

export const POManagemenConsume = [
    { text: '源单位数量', value: 'Operation', width: 200 },
    { text: '转换单位数量', value: 'MaterialLotNo', width: 150 },
    { text: '转换单位数量', value: 'Type', width: 150 },
    { text: '转换单位数量', value: 'Select', width: 80 },
    { text: '生效开始时间', value: 'Material' },
    { text: '生效开始时间', value: 'Quantity', width: 250 },
    { text: '生效开始时间', value: 'Available', width: 200 },
    { text: '生效开始时间', value: 'StorageBin', width: 200 },
    { text: '生效开始时间', value: 'operate', width: 150 },
]
export const POManagemenConsumeIn = [
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', prop: "Batch" },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'SSCC', prop: "Sscc" },
    { text: '源单位数量', value: 'Quantity', width: 150 },
    { text: '源单位数量', value: 'Expiration', width: 200 },
    { text: '源单位数量', value: 'Loaction', prop: 'Loaction2', width: 200 },
    { text: '源单位数量', value: 'operate', width: 150 },


]
export const POManagemenTipping = [
    { text: '源单位数量', value: 'SortOrder', prop: "SortOrder", width: 100 },
    { text: '转换单位数量', value: 'Material', prop: "MaterialCode" },
    { text: '转换单位数量', value: 'Description', prop: "MaterialName" },
    { text: '生效开始时间', value: 'Required', prop: "Quantity" },
    //{ text: '生效开始时间', value: 'Staged', prop: "Quantity2" },
    { text: '生效开始时间', value: 'Consumed', prop: "Quantity4" },
    { text: '生效开始时间', value: 'IsOver', prop: "PrepStatus" },
]
export const POManagemenWeight = [
    { text: '源单位数量', value: 'detail', prop: "detail", width: 100 },
    { text: '源单位数量', value: 'Sequence', prop: "Num", width: 100 },
    { text: '转换单位数量', value: 'Item', prop: "Item" },
    { text: '转换单位数量', value: 'Po', prop: "ProductionOrderNo" },
    { text: '生效开始时间', value: 'Formula', prop: "Formula" },
    { text: '生效开始时间', value: 'Batch', prop: "CylinderNumber" },
    { text: '生效开始时间', value: 'AverageWeight', prop: "AverageWeight" },
    { text: '生效开始时间', value: 'ModifyDate', prop: "ModifyDate" },
    { text: '源单位数量', value: 'operate', width: 150 },
]
export const POManagemenWeightDetail = [
    { text: '源单位数量', value: 'Sequence', prop: "Num", width: 100 },
    { text: '生效开始时间', value: 'Weight', prop: "Weight" },
    { text: '生效开始时间', value: 'ModifyDate', prop: "ModifyDate" },
    { text: '生效开始时间', value: 'ModifyUserId', prop: "ModifyUserId" },
    { text: '生效开始时间', value: 'operate', width: 150 },
]

export const POManagemenProduce = [
    { text: '转换单位数量', value: 'Type', width: 150 },
    { text: '生效开始时间', value: 'Material' },
    { text: '生效开始时间', value: 'Quantity' },
    { text: '生效开始时间', value: 'StorageBin' },
    { text: '生效开始时间', value: 'operate', width: 150 },
]

export const POManagemenProduceIn = [
    { text: '转换单位数量', value: 'Date', prop: "CreateDate", width: 150 },
    { text: '生效开始时间', value: 'Shift', prop: "Shift", width: 100 },
    { text: '生效开始时间', value: 'BatchCode', prop: "BatchCode", width: 150 },
    { text: '生效开始时间', value: 'SSCC', prop: "Sscc" },
    { text: '生效开始时间', value: 'Quantity', width: 150 },
    { text: '生效开始时间', value: 'Destination', width: 150 },
    { text: '生效开始时间', value: 'Source', width: 150 },
    { text: '生效开始时间', value: 'User', prop: "CreateUserId", width: 100 },
    { text: '生效开始时间', value: 'Comment', prop: "Comment", width: 100 },
    // { text: '生效开始时间', value: 'operate', width: 150 },

]

// 叫料单主表列配置
export const CallMaterialHeaderColumn = [
    { text: '叫料单号', value: 'callOrderNo', width: 150 },
    { text: '叫料人', value: 'callerId', width: 120 },
    { text: '叫料时间', value: 'callTime', width: 180 },
    { text: '线边仓编码', value: 'lineSideWarehouse', width: 150 },
    { text: '叫料点', value: 'callPoint', width: 150 },
    { text: '操作', value: 'actions', width: 150, fixed: 'right' }
];

// 叫料单明细表列配置
export const CallMaterialDetailColumn = [
    { text: '物料ID', value: 'materialId', width: 150 },
    { text: '物料版本ID', value: 'materialVersionId', width: 150 },
    { text: '叫料数量', value: 'quantity', width: 120 },
    { text: '批次ID', value: 'lotId', width: 150 },
    { text: '托盘ID', value: 'containerId', width: 150 },
    { text: '操作', value: 'actions', width: 150, fixed: 'right' }
];

//POlist
export const POlist = [
    { text: '转换单位数量', value: 'detail', width: 50 },
    { text: '生效开始时间', value: 'PlanStartDate', prop: "PlanStartTime", width: 150 },
    { text: '生效开始时间', value: 'PlanEndDate', prop: "PlanEndTime", width: 150 },

    { text: '转换单位数量', value: 'SegmentCode', prop: "SegmentCode", width: 100 },
    { text: '转换单位数量', value: 'ProcessOrder', prop: "ProductionOrderNo", width: 120 },
    { text: '生效开始时间', value: 'MaterialCode', prop: "MaterialCode", width: 120 },
    { text: '生效开始时间', value: 'Material', prop: "MaterialDescription", width: 150 },
    { text: '转换单位数量', value: 'Sequence', align: 'center', prop: "Sequence", width: 100 },
    { text: '生效开始时间', value: 'Status', align: 'center', prop: "PoStatus", width: 100 },
    { text: '生效开始时间', value: 'Execute', align: 'center', prop: "Execute", width: 100 },
    { text: '生效开始时间', value: 'Source', prop: "Resource", width: 100 },
    { text: '生效开始时间', value: 'PlanQty', prop: "PlanQty", width: 100 },
    { text: '生效开始时间', value: 'ActualQty', prop: "ActualQty", width: 100 },
    { text: '转换单位数量', value: 'ShiftName', prop: "ShiftName", width: 150 },
    { text: '生效开始时间', value: 'QaStatus', prop: "QaStatus", width: 150 },
    // { text: '生效开始时间', value: 'Batches', prop: "BatchCount", width: 150 },
    // { text: '生效开始时间', value: 'Batches', prop: "Count", width: 150 },
    // { text: '生效开始时间', value: 'Formula', prop: "Formula", width: 150 },
    // { text: '生效开始时间', value: 'productionversion', prop: "MaterialVersionNumber", width: 150 },
    // { text: '生效開始時間', value: 'ActualQty', prop: "ActualQty", width: 100 },
    { text: '生效開始時間', value: 'Grille', prop: "Grille", width: 100 },
    { text: '生效開始時間', value: 'IsHavePreservative', prop: "IsHavePreservative", width: 100 },
    { text: '生效開始時間', value: 'ProduceStatus', prop: "ProduceStatus", width: 150 },
    { text: '生效開始時間', value: 'Reason', prop: "Reason", width: 100 },

    { text: '生效開始時間', value: 'PlanEndDate', prop: "PlanEndTime", width: 180 },
    { text: '生效開始時間', align: 'center', value: 'operate', width: 248, fixed: "right" },

]
//POlist
export const CalculatePOlist = [
    { text: '生效開始時間', value: 'SapDate', prop: "SapDate", width: 100 },
    { text: '生效開始時間', value: 'PlanStartDate', prop: "PlanStartTime", width: 150 },
    { text: '生效開始時間', value: 'PlanEndDate', prop: "PlanEndTime", width: 150 },

    { text: '轉換單位數量', value: 'SegmentCode', prop: "SegmentCode", width: 100 },
    { text: '轉換單位數量', value: 'ProcessOrder', prop: "ProductionOrderNo", width: 120 },
    { text: '生效開始時間', value: 'MaterialCode', prop: "MaterialCode", width: 120 },
    { text: '生效開始時間', value: 'Material', prop: "MaterialDescription", width: 150 },
    { text: '轉換單位數量', value: 'Sequence', align: 'center', prop: "Sequence", width: 100 },
    { text: '生效開始時間', value: 'Status', align: 'center', prop: "PoStatus", width: 100 },
    { text: '生效開始時間', value: 'Execute', align: 'center', prop: "Execute", width: 100 },
    { text: '生效開始時間', value: 'Source', prop: "Resource", width: 100 },
    { text: '生效開始時間', value: 'PlanQty', prop: "PlanQty", width: 100 },
    { text: '生效開始時間', value: 'ActualQty', prop: "ActualQty", width: 100 },
    { text: '轉換單位數量', value: 'ShiftName', prop: "ShiftName", width: 150 },
    { text: '生效開始時間', value: 'QaStatus', prop: "QaStatus", width: 150 },
    { text: '生效開始時間', value: 'Grille', prop: "Grille", width: 100 },
    { text: '生效開始時間', value: 'IsHavePreservative', prop: "IsHavePreservative", width: 100 },
    { text: '生效開始時間', value: 'ProduceStatus', prop: "ProduceStatus", width: 150 },
    { text: '生效開始時間', value: 'Reason', prop: "Reason", width: 100 },
    { text: '生效開始時間', value: 'PlanEndDate', prop: "PlanEndTime", width: 180 },
]
//WLPOlist
export const WLPOlist = [
    { text: '轉換單位數量', value: 'detail', width: 50 },
    { text: '生效開始時間', value: 'PlanStartDate', prop: "PlanStartTime", width: 150 },
    { text: '生效開始時間', value: 'PlanEndDate', prop: "PlanEndTime", width: 150 },
    { text: '生效開始時間', value: 'Source', prop: "Resource", width: 100 },
    // { text: '生效開始時間', value: 'SapDate', prop: "SapDate", width: 100 },
    //{ text: '轉換單位數量', value: 'FillLineCode', prop: "FillLineCode", width: 100 },
    { text: '轉換單位數量', value: 'LineCode', prop: "LineCode", width: 150 },
    { text: '轉換單位數量', value: 'Sequence', align: 'center', prop: "Sequence", width: 100 },
    { text: '轉換單位數量', value: 'ProcessOrder', prop: "ProductionOrderNo", width: 180 },
    { text: '生效開始時間', value: 'MaterialCode', prop: "MaterialCode", width: 150 },
    { text: '生效開始時間', value: 'MaterialDescription', prop: "MaterialDescription", width: 200 },
    { text: '生效开始时间', value: 'PlanQty', prop: "PlanQty", width: 150 },
    { text: '生效开始时间', value: 'ActualQty', prop: "ActualQty", width: 150 },
    { text: '生效开始时间', value: 'Status', align: 'center', prop: "PoStatus", width: 120 },
    { text: '生效开始时间', value: 'BoilingStatus', prop: "BoilingStatus", width: 120 },
    //{ text: '生效开始时间', value: 'ProduceStatus', prop: "ProduceStatus", width: 120 },
    { text: '生效开始时间', value: 'QaStatus', prop: "QaStatus", width: 120 },
    //{ text: '生效开始时间', value: 'Execute', align: 'center', prop: "Execute", width: 100 },
    //{ text: '转换单位数量', value: 'Bezei', align: 'center', prop: "Bezei", width: 140 },
   
   
    //{ text: '转换单位数量', value: 'CipDetail', prop: "CipDetail", width: 150 },
    // { text: '转换单位数量', value: 'ShiftName', prop: "ShiftName", width: 150 },
    // { text: '生效开始时间', value: 'Batches', prop: "BatchCount", width: 150 },
    //{ text: '生效开始时间', value: 'Batches', prop: "Count", width: 150 },
    // { text: '生效开始时间', value: 'Formula', prop: "Formula", width: 150 },
    // { text: '生效开始时间', value: 'productionversion', prop: "MaterialVersionNumber", width: 150 },
    // { text: '生效开始时间', value: 'ActualQty', prop: "ActualQty", width: 100 },
    //{ text: '生效开始时间', value: 'Grille', prop: "Grille", width: 100 },
    //{ text: '生效开始时间', value: 'IsHavePreservative', prop: "IsHavePreservative", width: 100 },
    //{ text: '生效开始时间', value: 'Reason', prop: "Reason", width: 100 },
    //{ text: '生效开始时间', value: 'PlanEndDate', prop: "PlanEndTime", width: 180 },
    { text: '生效开始时间', align: 'center', value: 'operate', width: 248, fixed: "right" },
]
export const Inspectionlist = [
    { text: '转换单位数量', value: 'detail', width: 80 },
    { text: '生效开始时间', value: 'PlanStartDate', prop: "PlanStartTime", width: 180 },
    { text: '生效开始时间', value: 'Formula', prop: "Formula", width: 150 },
    { text: '生效开始时间', value: 'Bezei', prop: "Bezei", width: 100 },
    { text: '生效开始时间', value: 'Material', prop: "MaterialDescription", width: 300 },
    { text: '生效开始时间', value: 'Landz', prop: "Landz", width: 100 },
    { text: '转换单位数量', value: 'SegmentCode', prop: "SegmentCode", width: 80 },
    { text: '生效开始时间', value: 'MaterialCode', prop: "MaterialCode", width: 150 },
    { text: '生效开始时间', value: 'Ltext1', prop: "Ltext1", width: 250 },
    { text: '转换单位数量', value: 'ProcessOrder', prop: "ProductionOrderNo", width: 150 },
    { text: '生效开始时间', value: 'PlanQty', prop: "PlanQty", width: 100 },
    { text: '生效开始时间', value: 'QaStatus', prop: "QaStatus", width: 150 },
    { text: '生效开始时间', value: 'SapDate', prop: "SapDate", width: 180 },
    // { text: '转换单位数量', value: 'ShiftName', prop: "ShiftName", width: 150 },
    { text: '转换单位数量', value: 'Sequence', prop: "Sequence", width: 100 },
    { text: '生效开始时间', value: 'Source', prop: "Resource", width: 200 },
    { text: '生效开始时间', value: 'Status', prop: "PoStatus", width: 100 },
    { text: '生效开始时间', value: 'Execute', prop: "Execute", width: 100 },
    { text: '生效开始时间', value: 'Batches', prop: "BatchCount", width: 150 },
    { text: '生效开始时间', value: 'productionversion', prop: "MaterialVersionNumber", width: 150 },
    // { text: '生效开始时间', value: 'ActualQty', prop: "ActualQty", width: 100 },
    // { text: '生效开始时间', value: 'ProduceStatus', prop: "ProduceStatus", width: 150 },
    // { text: '生效开始时间', value: 'Reason', prop: "Reason", width: 100 },
    { text: '生效开始时间', value: 'PlanEndDate', prop: "PlanEndTime", width: 180 },
]
export const POlistExecute = [
    { text: '转换单位数量', value: 'BatchCode', prop: "BatchCode" },
    { text: '转换单位数量', value: 'Machine' },
    { text: '转换单位数量', value: 'Stage', prop: "SegmentName" },
    { text: '转换单位数量', value: 'StageCode', prop: "SegmentCode" },
    { text: '生效开始时间', value: 'Status', width: 120 },
    { text: '生效开始时间', value: 'TargetNum', prop: "TargetQuantity", width: 100 },
    { text: '生效开始时间', value: 'StartDate', prop: "StartTime" },
    { text: '生效开始时间', value: 'EndDate', prop: "EndTime" },
    { text: '生效开始时间', value: 'Comment', prop: "Comments", width: 150 },
    { text: '生效开始时间', value: 'User', width: 150, prop: "ModifyUserId" },
]

export const POlistBatch = [
    { text: '转换单位数量', value: 'operate', width: 150 },
    { text: '转换单位数量', value: 'Batch', prop: "Number", width: 150 },
    { text: '转换单位数量', value: 'Material', width: 200 },
    { text: '转换单位数量', value: 'TargetNum', prop: "TargetQuantity", width: 150 },
    { text: '生效开始时间', value: 'readiness', prop: "PrepStatus", width: 150 },
    { text: '生效开始时间', value: 'Status', prop: "Status", width: 150 },
    { text: '生效开始时间', value: 'delete', width: 50 },

]


export const POlistProduce = [
    { text: '转换单位数量', value: 'Stage', prop: "SegmentName", width: 150 },
    { text: '转换单位数量', value: 'Resource', prop: "Resource", width: 150 },
    { text: '转换单位数量', value: 'Material', },
    { text: '转换单位数量', value: 'Quantity', width: 150 },
    { text: '转换单位数量', value: 'ActualProduce', width: 150 },
    { text: '生效开始时间', value: 'StorageArea', prop: "StorageArea", width: 150 },

]
export const POlistproperty = [
    { text: '转换单位数量', value: 'name', prop: "name", align: "center" },
    { text: '转换单位数量', value: 'value', prop: "value" },

]
export const POManagemenLogsheetsListTable = [
    { text: '转换单位数量', value: 'Date', prop: "CreateDate", width: 200 },
    { text: '转换单位数量', value: 'Shift', prop: "ShiftId", width: 100 },
    { text: '转换单位数量', value: 'User', prop: "CreateUserId", width: 150 },
    { text: '转换单位数量', value: 'Comment', prop: "Comment", width: 200 },
    { text: '转换单位数量', value: 'Status', align: "center", prop: "Status", width: 150 },
    { text: '转换单位数量', value: 'ProcessOrder', prop: "PoExecutionId" },
    // { text: '转换单位数量', value: 'Supplier', prop: "Supplier", width: 150 },
    // { text: '转换单位数量', value: 'Quantity', prop: "Quantity", width: 150 },
    // { text: '转换单位数量', value: 'Code', prop: "Code", width: 150 },
    // { text: '转换单位数量', value: 'Defectiveproject', prop: "Defectiveproject", width: 150 },
    // { text: '转换单位数量', value: 'Spec', prop: "Spec", width: 150 },

]
export const SamplingTable = [
    { text: '转换单位数量', value: 'qysj', prop: "SamplingTime" },
    { text: '转换单位数量', value: 'rqbh', prop: "ContainerCode" },
    { text: '转换单位数量', value: 'gdh', prop: "PoBatch" },
    { text: '生效开始时间', value: 'Formula', prop: "Formula" },
    { text: '转换单位数量', value: 'sbmc', prop: "EquipmentCode" },
    { text: '转换单位数量', value: 'cpbm', prop: "MaterialCode" },
    { text: '转换单位数量', value: 'gc', prop: "gc" },
    { text: '转换单位数量', value: 'rqzt', align: "center", prop: "Status" },
    { text: '转换单位数量', value: 'TestingTime', align: "center", prop: "TestingTime" },
    { text: '转换单位数量', value: 'glbdzt', align: "center", prop: "Logsheetstatus" },
    { text: '转换单位数量', value: 'Type', align: "center", prop: "Type" },

]

// export const POManagemenQualityResultTable = [
//     { text: '转换单位数量', value: 'Equipment', prop: "EquipmentName", width: 150 },
//     { text: '转换单位数量', value: 'GroupName', prop: "GroupName", width: 150 },
//     { text: '转换单位数量', value: 'Date', prop: "Date", width: 150 },
//     { text: '转换单位数量', value: 'Shift', prop: "Shift", width: 100 },
//     // { text: '转换单位数量', value: 'Performance', prop: "Performance", width: 100 },
//     { text: '转换单位数量', value: 'PO', prop: "PoCode", width: 150 },
//     { text: '生效开始时间', value: 'Formula', prop: "Formula", width: 150 },
//     // { text: '转换单位数量', value: 'Batch', prop: "Batch", width: 150 },
//     // { text: '转换单位数量', value: 'SSCC/Container', prop: "SSCC/Container", width: 150 },
//     { text: '转换单位数量', value: 'Material', prop: "Material", width: 150 },
//     { text: '转换单位数量', value: 'Score', align: 'center', prop: "Score", width: 350 },
//     { text: '转换单位数量', value: 'Status', align: 'center', prop: "Status", width: 150 },
//     // { text: '转换单位数量', value: 'Approvals', prop: "Approvals", width: 100 },
//     // { text: '转换单位数量', value: 'ok', prop: "", width: 50 },
//     // { text: '转换单位数量', value: 'SH', prop: "SH", width: 150 },
//     { text: '转换单位数量', value: 'Comments', prop: "Comment", width: 150 },
// ]

export const POManagemenQualityResultTable = [
    { text: '转换单位数量', value: 'PlanStartTime', prop: "PlanStartTime", width: 100 },
    { text: '转换单位数量', value: 'Equipment', prop: "EquipmentName", width: 150 },
    { text: '转换单位数量', value: 'GroupName', prop: "GroupName", width: 150 },
    { text: '转换单位数量', value: 'Date', prop: "Date", width: 150 },
    { text: '转换单位数量', value: 'Shift', prop: "Shift", width: 100 },
    { text: '转换单位数量', value: 'PO', prop: "PoCode", width: 150 },
    { text: '生效开始时间', value: 'Formula', prop: "Formula", width: 150 },
    { text: '转换单位数量', value: 'Sequence', prop: "Sequence", width: 150 },
    { text: '转换单位数量', value: 'FormulaNo', prop: "FormulaNo", width: 150 },
    { text: '转换单位数量', value: 'Material', prop: "Material", width: 150 },
    { text: '转换单位数量', value: 'Score', align: 'center', prop: "Score", width: 350 },
    { text: '转换单位数量', value: 'Status', align: 'center', prop: "Status", width: 150 },
    { text: '转换单位数量', value: 'CreateDate', prop: "CreateDate", width: 150 },
    { text: '转换单位数量', value: 'CreateUserId', prop: "CreateUserId", width: 150 },
    // { text: '转换单位数量', value: 'ModifyDate', prop: "ModifyDate", width: 150 },
    { text: '转换单位数量', value: 'TestingTime', prop: "TestingTime", width: 150 },
    { text: '转换单位数量', value: 'TestingDuration', prop: "TestingDuration", width: 150 },
    { text: '转换单位数量', value: 'TestingUser', prop: "TestingUser", width: 150 },
    // { text: '转换单位数量', value: 'ModifyUserId', prop: "ModifyUserId", width: 150 },
    { text: '转换单位数量', value: 'Comments', prop: "Comment", width: 150 },
    { text: '源单位数量', value: 'operate', width: 150, fixed: "right" },
]

export const POManagemenQAQualityResultTable = [
    { text: '转换单位数量', value: 'PlanStartTime', prop: "PlanStartTime", width: 100 },
    { text: '转换单位数量', value: 'Equipment', prop: "EquipmentName", width: 150 },
    { text: '转换单位数量', value: 'GroupName', prop: "GroupName", width: 150 },
    { text: '转换单位数量', value: 'Date', prop: "Date", width: 150 },
    { text: '转换单位数量', value: 'Shift', prop: "Shift", width: 100 },
    { text: '转换单位数量', value: 'PO', prop: "PoCode", width: 150 },
    { text: '生效开始时间', value: 'Formula', prop: "Formula", width: 150 },
    { text: '转换单位数量', value: 'Sequence', prop: "Sequence", width: 150 },
    { text: '转换单位数量', value: 'FormulaNo', prop: "FormulaNo", width: 150 },
    { text: '转换单位数量', value: 'Material', prop: "Material", width: 150 },
    { text: '转换单位数量', value: 'Score', align: 'center', prop: "Score", width: 350 },
    { text: '转换单位数量', value: 'Status', align: 'center', prop: "Status", width: 150 },
    { text: '转换单位数量', value: 'CreateDate', prop: "CreateDate", width: 150 },
    { text: '转换单位数量', value: 'CreateUserId', prop: "CreateUserId", width: 150 },
    // { text: '转换单位数量', value: 'ModifyDate', prop: "ModifyDate", width: 150 },
    { text: '转换单位数量', value: 'TestingTime', prop: "TestingTime", width: 150 },
    { text: '转换单位数量', value: 'TestingDuration', prop: "TestingDuration", width: 150 },
    { text: '转换单位数量', value: 'TestingUser', prop: "TestingUser", width: 150 },
    // { text: '转换单位数量', value: 'ModifyUserId', prop: "ModifyUserId", width: 150 },
    { text: '转换单位数量', value: 'Comments', prop: "Comment", width: 150 },
    { text: '源单位数量', value: 'operate', width: 150, fixed: "right" },
]
export const POManagemenLogsheetsList = [
    { text: '转换单位数量', value: 'Parameter', prop: "ParameterName" },
    { text: '转换单位数量', value: 'value', prop: "Value", width: 400 },
    { text: '转换单位数量', value: 'Safety', align: 'center', prop: "Safety" },
    { text: '转换单位数量', value: 'ProdictAttributes', prop: "ProdictAttributes" },
    { text: '转换单位数量', value: 'Instructions', prop: "Instructions" },
]
export const POManagemenLogsheets = [
    { text: '转换单位数量', value: 'Logsheet', prop: "SheetName" },
    { text: '转换单位数量', value: 'operate', prop: "", width: 150 },
    { text: '转换单位数量', value: 'Machine', prop: "EquipmentName", width: 200 },
    // { text: '转换单位数量', value: 'PoId', prop: "PoId" },
    { text: '转换单位数量', value: 'PO', prop: "PO" },
    { text: '生效开始时间', value: 'Formula', prop: "Formula", width: 150 },
    { text: '转换单位数量', value: 'Frequency', prop: "FrequencyName" },
    { text: '转换单位数量', value: 'CheckResult', prop: "CheckResult", align: "center", },
    { text: '转换单位数量', value: 'LastEntry', prop: "LastTimeEntity", width: 200 },
]
export const POManagemenPerformance = [
    { text: '转换单位数量', value: 'detail', width: 100 },
    { text: '转换单位数量', value: 'Category', prop: "Categroy", width: 200 },
    { text: '转换单位数量', value: 'Machine', prop: "Machine", width: 150 },
    { text: '转换单位数量', value: 'SubCategory', prop: "Group", width: 200 },
    { text: '转换单位数量', value: 'Reason', prop: "Reason", width: 150 },
    { text: '转换单位数量', value: 'Code', prop: "PlcCode", width: 80 },
    { text: '转换单位数量', value: 'Duration', prop: "Duration", width: 120 },
    { text: '转换单位数量', value: 'ProcessOrder', prop: "ProductionOrderNo", width: 200 },
    { text: '生效开始时间', value: 'Formula', prop: "Formula", width: 150 },
    { text: '转换单位数量', value: 'CrewSize', prop: "CrewSize", width: 150 },
    { text: '转换单位数量', value: 'StartTime', prop: "StartTimeUtc", width: 180 },
    { text: '转换单位数量', value: 'EndTime', prop: "EndTimeUtc", width: 180 },
    { text: '转换单位数量', value: 'Comment', prop: "Comment", width: 150 }

]
export const POManagemenPerformanceResult = [
    { text: '转换单位数量', value: 'detail', width: 100 },
    { text: '转换单位数量', value: 'Line', prop: "LineName", width: 200 },
    { text: '转换单位数量', value: 'Machine', prop: "Machine", width: 150 },
    { text: '转换单位数量', value: 'Category', prop: "Categroy", width: 200 },
    { text: '转换单位数量', value: 'SubCategory', prop: "Group", width: 200 },
    { text: '转换单位数量', value: 'Reason', prop: "Reason", width: 150 },
    { text: '转换单位数量', value: 'Code', prop: "PlcCode", width: 80 },
    { text: '转换单位数量', value: 'ProcessOrder', prop: "ProductionOrderNo", width: 150 },
    { text: '转换单位数量', value: 'Formula', prop: "Formula", width: 150 },
    { text: '转换单位数量', value: 'StartTime', prop: "StartTimeUtc", width: 150 },
    { text: '转换单位数量', value: 'EndTime', prop: "EndTimeUtc", width: 150 },
    { text: '转换单位数量', value: 'Duration', prop: "Duration", width: 120 },
    { text: '转换单位数量', value: 'CrewSize', prop: "CrewSize", width: 120 },
    { text: '转换单位数量', value: 'Comment', prop: "Comment", width: 150 }

]
export const POManagemenPerformanceMerge = [
    { text: '转换单位数量', value: 'Category', prop: "Categroy" },
    { text: '转换单位数量', value: 'Reason', prop: "Reason" },
    { text: '转换单位数量', value: 'StartTime', prop: "StartTimeUtc" },
    { text: '转换单位数量', value: 'EndTime', prop: "EndTimeUtc" },
]
export const POManagemenPerformanceCrewSize = [
    { text: '转换单位数量', value: 'Category', prop: "Categroy", width: 150 },
    { text: '转换单位数量', value: 'Machine', prop: "Machine", width: 100 },
    { text: '转换单位数量', value: 'Group', prop: "Group", width: 100 },
    { text: '转换单位数量', value: 'Reason', prop: "Reason", width: 100 },
    { text: '转换单位数量', value: 'PO', prop: "ProductionOrderNo", width: 150 },
    { text: '转换单位数量', value: 'Formula', prop: "Formula", width: 150 },
    { text: '转换单位数量', value: 'CrewSize', prop: "CrewSize", width: 100 },
    { text: '转换单位数量', value: 'StartTime', prop: "StartTimeUtc", width: 150 },
    { text: '转换单位数量', value: 'EndTime', prop: "EndTimeUtc", width: 150 }

]
export const POManagemenDrawPerformance = [
    { text: '转换单位数量', value: 'Updated', prop: "ModifyDate", width: 150 },
    { text: '转换单位数量', value: 'User', prop: "ModifyUserId", width: 100 },
    { text: '转换单位数量', value: 'SourceType', prop: "SourceType", width: 100 },
    { text: '转换单位数量', value: 'Category', prop: "Categroy", width: 150 },
    { text: '转换单位数量', value: 'Machine', prop: "Machine", width: 150 },
    { text: '转换单位数量', value: 'Group', prop: "Group", width: 150 },
    { text: '转换单位数量', value: 'Reason', prop: "Reason", width: 150 },
    { text: '转换单位数量', value: 'Code', prop: "PlcCode", width: 100 },
    { text: '转换单位数量', value: 'PO', prop: "ProductionOrderNo", width: 120 },
    { text: '转换单位数量', value: 'Formula', prop: "Formula", width: 150 },
    { text: '转换单位数量', value: 'CrewSize', prop: "CrewSize", width: 150 },
    { text: '转换单位数量', value: 'StartTime', prop: "StartTimeUtc", width: 150 },
    { text: '转换单位数量', value: 'EndTime', prop: "EndTimeUtc", width: 150 }
]
export const POlistparameter = [

    { text: '转换单位数量', value: 'EquipmentCode', prop: "EquipmentCode" , width: 200},
    { text: '转换单位数量', value: 'ParameterName', prop: "ParameterName", width: 200 },
    { text: '转换单位数量', value: 'ParameterValue', prop: "ParameterValue", width: 200 },
    { text: '生效开始时间', value: 'DataBlock', prop: "DataBlock", width: 200 },
    { text: '生效开始时间', value: 'DataBlockItem', prop: "DataBlockItem" },
]
export const PODcsInfoColumn = [

    { text: '生效开始时间', value: 'Type', prop: "Type", width: 200 },
    { text: '转换单位数量', value: 'UnitId', prop: "UnitId" , width: 200},
    { text: '转换单位数量', value: 'OpName', prop: "OpName", width: 200 },
    { text: '转换单位数量', value: 'OpId', prop: "OpId", width: 200 },
    { text: '生效开始时间', value: 'InputMaterialEquCode', prop: "InputMaterialEquCode", width: 200 },
    { text: '生效开始时间', value: 'MaterialName', prop: "MaterialName", width: 200 },
    { text: '生效开始时间', value: 'StorageTank', prop: "StorageTank", width: 200 },
    { text: '生效开始时间', value: 'Quantity', prop: "Quantity", width: 200 },
    { text: '生效开始时间', value: 'State', prop: "State", width: 200 },
    { text: '生效开始时间', value: 'Message', prop: "Message", width: 200 },
    { text: '生效开始时间', value: 'MsgTime', prop: "MsgTime" },
]
export const PrecheckColumn = [
    { text: '转换单位数量', value: 'TraceCode', width: 180 },
    { text: '转换单位数量', value: 'BatchPallet', width: 150 },
    { text: '转换单位数量', value: 'Loaction', width: 150 },
    { text: '转换单位数量', value: 'ProcessOrder', width: 200 },
    { text: '生效开始时间', value: 'Material' },
    { text: '生效开始时间', value: 'Batch', width: 150 },
    { text: '生效开始时间', value: 'Quantity', width: 130 },
    { text: '生效开始时间', value: 'Deadline', width: 200 },
    { text: '生效开始时间', value: 'PreCheck', width: 100 },

]
export const PrecheckDrawColumn = [
    { text: '转换单位数量', value: 'TraceCode', prop: "Tracecode", width: 180 },
    { text: '转换单位数量', value: 'BatchPallet', prop: "ContainerName", width: 150 },
    { text: '生效开始时间', value: 'Material' },
    { text: '生效开始时间', value: 'Quantity', prop: "Quantity", width: 130 },
    { text: '生效开始时间', value: 'PreCheck', prop: "Status", width: 100 },

]
export const TippingDrawColumn = [
    { text: '转换单位数量', value: 'TraceCode', prop: "Tracecode", width: 180 },
    { text: '转换单位数量', value: 'BatchPallet', prop: "ContainerName", width: 150 },
    { text: '生效开始时间', value: 'Material' },
    { text: '生效开始时间', value: 'Quantity', prop: "Quantity", width: 130 },
    { text: '生效开始时间', value: 'Tipping', prop: "Precheckestatus", width: 100 },

]
export const StorageDrawColumn = [
    { text: '生效开始时间', value: 'Workcenter', prop: "Workcenter", width: 150 },
    { text: '生效开始时间', value: 'Material' },
    { text: '源单位数量', value: 'Batch', prop: "NewLotId", width: 200 },
    { text: '转换单位数量', value: 'SSCC/Container', prop: "NewSubLotId", width: 300 },
    { text: '生效开始时间', value: 'Quantity', prop: "Quantity", width: 100 },
    { text: 'CreateDate', value: 'Created', prop: "CreateDate", width: 150 },

    { text: '生效结束时间', value: 'Expiration', width: 150 },
    // { text: '生效结束时间', value: 'Created', width: 150 },
    // { text: '生效开始时间', value: 'Precheckestatus', prop: "Precheckestatus", width: 100 },
]
export const StorageDrawColumnNoWRemark = [
    { text: '生效开始时间', value: 'Material' },
    { text: '源单位数量', value: 'Batch', prop: "NewLotId", width: 200 },
    { text: '转换单位数量', value: 'SSCC/Container', prop: "NewSubLotId", width: 300 },
    { text: '生效开始时间', value: 'Quantity', prop: "Quantity", width: 130 },
    { text: '生效结束时间', value: 'Expiration', width: 150 },
    // { text: '生效结束时间', value: 'Created', width: 150 },
    // { text: '生效开始时间', value: 'Precheckestatus', prop: "Precheckestatus", width: 100 },
]
export const FeedingListColumn = [
    { text: '生效开始时间', value: 'detail', width: 150 },
    { text: '生效开始时间', value: 'Material' },
    // { text: '转换单位数量', value: 'EquipmentName', prop: "EquipmentName", width: 100 },
    { text: '生效开始时间', value: 'ActualQuantity', width: 150 },
    { text: '生效开始时间', value: 'Inventqty', prop: "Inventqty", width: 150 },
    { text: '生效开始时间', value: 'NeedQuantity', width: 150 },
    { text: '生效开始时间', value: 'Complete', width: 150 },
    { text: '生效开始时间', value: 'Requesttype', width: 150 },
    // { text: '生效开始时间', value: 'Status', width: 100 },
    { text: '生效开始时间', value: 'ModifyDate', width: 200 },
    { text: '生效开始时间', value: 'PlannedTime', width: 200 },

]
export const FeedingAddListColumn = [
    { text: '生效开始时间', value: 'Index', width: 80 },
    { text: '转换单位数量', value: 'LotNo' },
    { text: '转换单位数量', value: 'Material' },
    { text: '生效开始时间', value: 'BagSize' },
    { text: '生效开始时间', value: 'Quantity', width: 200 },
    { text: '生效开始时间', value: 'TrayNumber' },
    { text: '生效开始时间', value: 'UserNumber' },
    { text: '生效开始时间', value: 'ArraveTime' },
    { text: '生效开始时间', value: 'Remark' },
]
export const FeedingDetailListColumn = [
    { text: '转换单位数量', value: 'Batchno' },
    { text: '转换单位数量', value: 'Material' },
    { text: '生效开始时间', value: 'NeedQuantity', prop: "NeedQuantity" },
    { text: '生效开始时间', value: 'ActualQuantity', prop: "ActualQuantity" },
    // { text: '生效开始时间', value: 'Status' },
    { text: '生效开始时间', value: 'CreateDate' },
    { text: '生效开始时间', value: 'PlannedTime' },
]

export const FeedingListColumnFULL = [
    { text: '生效开始时间', value: 'detail', width: 150 },
    { text: '生效开始时间', value: 'Material' },
    // { text: '转换单位数量', value: 'EquipmentName', prop: "EquipmentName", width: 100 },
    { text: '生效开始时间', value: 'ActualQuantity', width: 150 },
    { text: '生效开始时间', value: 'Inventqty', prop: "Inventqty", width: 150 },
    // { text: '生效开始时间', value: 'NeedQuantity', width: 150 },
    // { text: '生效开始时间', value: 'Complete', width: 130 },
    { text: '生效开始时间', value: 'Requesttype', width: 150 },
    // { text: '生效开始时间', value: 'Status', width: 100 },
    { text: '生效开始时间', value: 'ModifyDate', width: 200 },
    { text: '生效开始时间', value: 'PlannedTime', width: 200 },

]
export const FeedingAddListColumnFULL = [
    { text: '生效开始时间', value: 'Index', width: 80 },
    { text: '转换单位数量', value: 'LotNo' },
    { text: '转换单位数量', value: 'Material' },
    // { text: '生效开始时间', value: 'BagSize' },
    { text: '生效开始时间', value: 'Quantity', width: 200 },
    // { text: '生效开始时间', value: 'TrayNumber' },
    { text: '生效开始时间', value: 'UserNumber' },
    { text: '生效开始时间', value: 'ArraveTime' },
    { text: '生效开始时间', value: 'Remark' },
]
export const FeedingDetailListColumnFULL = [
    //  { text: '转换单位数量', value: 'Batchno' },
    { text: '转换单位数量', value: 'Material' },
    { text: '生效开始时间', value: 'NeedQuantity', prop: "NeedQuantity" },
    { text: '生效开始时间', value: 'ActualQuantity', prop: "ActualQuantity" },
    // { text: '生效开始时间', value: 'Status' },
    { text: '生效开始时间', value: 'CreateDate' },
    { text: '生效开始时间', value: 'PlannedTime' },
]

//材料制备栏
export const PrecheckFeedingColumn = [
    { text: '生效开始时间', value: 'detail', width: 80 },
    { text: '生效开始时间', value: 'PlanStartDate', prop: "PlanStartTime", width: 180 },
    { text: '源单位数量', value: 'Label', prop: "Name", width: 200 },
    { text: '转换单位数量', value: 'Line', prop: "LineName", width: 150 },
    { text: '转换单位数量', value: 'Po', prop: "ProductionOrderNo", width: 150 },
    { text: '转换单位数量', value: 'Sequence', width: 100 },
    { text: '转换单位数量', value: 'FormulaNo', prop: "FormulaNo", width: 150 },
    // { text: '生效开始时间', value: 'Machine', prop: "Machine", width: 150 },
    //{ text: '生效开始时间', value: 'Batch', prop: "BatchCode", width: 200 },
    { text: '生效开始时间', value: 'Material', width: 200 },
    { text: '生效开始时间', value: 'IsOver', prop: "PrepStatus", width: 150 },
    { text: '生效开始时间', value: 'Confirmed', prop: "Reviewuserid", width: 150 },
    { text: '生效开始时间', value: 'ConfirmedDate', prop: "Reviewtime", width: 200 },

]

export const MaterialPreparationPrecheckColumn = [
    { text: '生效开始时间', value: 'detail', width: 80 },
    { text: '转换单位数量', value: 'PlanStartTime', prop: "PlanTime", width: 150 },
    { text: '源单位数量', value: 'Label', prop: "Name", width: 200 },
    { text: '转换单位数量', value: 'Line', prop: "LineName", width: 150 },
    { text: '转换单位数量', value: 'Po', prop: "ProductionOrderNo", width: 150 },
    { text: '转换单位数量', value: 'Sequence', width: 100 },
    { text: '转换单位数量', value: 'FormulaNo', prop: "FormulaNo", width: 150 },
    // { text: '转换单位数量', value: 'ShiftName', prop: "ShiftName", width: 150 },  

    // { text: '生效开始时间', value: 'Machine', prop: "Machine", width: 150 },
    //{ text: '生效开始时间', value: 'Batch', prop: "BatchCode", width: 50 },
    { text: '生效开始时间', value: 'Material', width: 200 },
    { text: '生效开始时间', value: 'IsOver', prop: "PrepStatus", width: 150 },
    { text: '生效开始时间', value: 'Confirmed', prop: "Reviewuserid", width: 150 },
    { text: '生效开始时间', value: 'ConfirmedDate', prop: "Reviewtime", width: 200 },

]
export const MaterialPrecheckDrawColumn = [
    { text: '转换单位数量', value: 'TraceCode', prop: "Tracecode", width: 180 },
    { text: '转换单位数量', value: 'BatchPallet', prop: "ContainerName", width: 150 },
    { text: '生效开始时间', value: 'Material' },
    { text: '生效开始时间', value: 'Quantity', prop: "Quantity", width: 130 },
    { text: '生效开始时间', value: 'PreCheck', prop: "Status", width: 100 },

]

//白糖预处理表头
export const StorageListColumn = [
    { text: '物料', value: 'Material' },
    { text: '源单位名称', value: 'Class', prop: "ClassDec", width: 120 },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', prop: "BatchId", width: 200 },
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'SSCC/Container', width: 200 },
    { text: '生效开始时间', value: 'Quantity', width: 120 },
    { text: '生效结束时间', value: 'Expiration', width: 150 },
    { text: '生效结束时间', value: 'Created', width: 150 },
]

export const TippingscanInformationColumn = [
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'SSCC/Container', width: 250 },
    { text: '物料', value: 'Material' },
    { text: '生效开始时间', value: 'Group', prop: "MaterialGroups", width: 150 },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', prop: "BatchId", width: 250 },
    { text: '生效开始时间', value: 'Quantity', width: 150 },
    { text: '生效结束时间', value: 'Expiration', width: 180 },
]

export const TippingscanInventoryColumn = [
    { text: '转换单位名称', value: 'SSCCStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '转换单位数量', value: 'SSCC/Container', width: 250 },
    { text: '物料', value: 'Material' },
    { text: '生效开始时间', value: 'Group', prop: "MaterialGroups", width: 150 },
    { text: '源单位数量', value: 'BatchStatus', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '源单位数量', value: 'Batch', prop: "BatchId", width: 250 },
    { text: '生效开始时间', value: 'Quantity', width: 150 },
    { text: '生效开始时间', value: 'Location', width: 150 },
    { text: '生效结束时间', value: 'Expiration', width: 180 },
]
//原料盘点表头
export const InventorycountColumnWL = [
    { text: '详情', value: 'detail', width: 60, fixed: 'left' },
    { text: '详情', value: 'Plandate' },
    { text: '物料', value: 'TaskStatus' },
    { text: '物料', value: 'Tasktype' },
    { text: '物料', value: 'ModifyDate' },
    { text: '物料', value: 'CreateUserId' },
]
//物料盘点表头
export const InventorycountColumn = [
    { text: '详情', value: 'detail', width: 60, fixed: 'left' },
    { text: '详情', value: 'Plandate' },
    { text: '物料', value: 'TaskStatus' },
    { text: '物料', value: 'ModifyDate' },
    { text: '物料', value: 'CreateUserId' },
]

//原料盘点弹出框表头
export const InventorycountDrawColumn = [
    { text: '详情', value: 'Material', width: 200 },
    { text: '物料', value: 'StatusLot', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '物料', value: 'LotId', width: 120 },
    { text: '物料', value: 'StatusSlot', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '物料', value: 'SubLotId', width: 120 },
    { text: '物料', value: 'Equipment', width: 150 },
    { text: '物料', value: 'Movetime', width: 150 },
    { text: '物料', value: 'CurrentQuantity', width: 120 },
    { text: '物料', value: 'ActualQuantity', width: 150 },
    { text: '源单位数量', value: 'operate', width: 220 },
    { text: '物料', value: 'Result', width: 150 },
    { text: '物料', value: 'Difference', width: 90 },
    { text: '物料', value: 'Diff', width: 80 },
    { text: '物料', value: 'Reason', width: 200 },
    { text: '物料', value: 'CreateUserId', width: 100 },
]
//原料盘点弹出框表头
export const InventorycountReturnDrawColumn = [
    { text: '详情', value: 'Material', width: 200, fixed: "left" },
    { text: '物料', value: 'StatusLot', align: "center", width: 60, icon: "el-icon-collection-tag", fixed: "left" },
    { text: '物料', value: 'LotId', width: 120, fixed: "left" },
    { text: '物料', value: 'StatusSlot', align: "center", width: 60, icon: "el-icon-collection-tag", fixed: "left" },
    { text: '物料', value: 'SubLotId', width: 120, fixed: "left" },
    { text: '物料', value: 'Equipment', width: 100, fixed: "left" },
    { text: '物料', value: 'Movetime', width: 150 },
    { text: '物料', value: 'CurrentQuantity', width: 100 },
    { text: '物料', value: 'ActualQuantity', width: 180 },
    { text: '详情', value: 'Inventtype', width: 100 },
    { text: '源单位数量', value: 'operate', width: 200 },
    { text: '物料', value: 'Result', width: 80 },
    { text: '物料', value: 'Difference', width: 70 },
    { text: '物料', value: 'Diff', width: 90 },
    { text: '物料', value: 'Reason', width: 200 },
    { text: '物料', value: 'Remark', width: 200 },
    { text: '物料', value: 'Isread', width: 50 },
    { text: '物料', value: 'Sapno', width: 100 },
    { text: '物料', value: 'CreateUserId', width: 100 },
]
export const InventorycountDrawColumnHT = [
    { text: '详情', value: 'Material', width: 200 },
    { text: '物料', value: 'StatusLot', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '物料', value: 'LotId', width: 120 },
    { text: '物料', value: 'StatusSlot', align: "center", width: 60, icon: "el-icon-collection-tag" },
    { text: '物料', value: 'SubLotId', width: 120 },
    { text: '物料', value: 'Sapformula', width: 120 },
    { text: '物料', value: 'Equipment', width: 150 },
    { text: '物料', value: 'Movetime', width: 150 },
    { text: '物料', value: 'CurrentQuantity', width: 120 },
    { text: '物料', value: 'ActualQuantity', width: 150 },
    { text: '源单位数量', value: 'operate', width: 220 },
    { text: '物料', value: 'Result', width: 150 },
    { text: '物料', value: 'Difference', width: 90 },
    { text: '物料', value: 'Diff', width: 80 },
    { text: '物料', value: 'Reason', width: 200 },
    { text: '物料', value: 'CreateUserId', width: 100 },
]
export const ReportingworkorderBZPOList = [
    { text: '详情', value: 'order', prop: "ProductionOrderNo", width: 150 },
    { text: '详情', value: 'cp', width: 250 },
    { text: '物料', value: 'pch', prop: "LotCode", width: 150 },
    { text: '物料', value: 'scrq', prop: "PlanStartDate", width: 150 },
    { text: '物料', value: 'gg', prop: "MngPu", width: 150 },
    { text: '物料', value: 'sjslx', prop: "Q1", width: 120 },
    { text: '物料', value: 'sjslz', prop: "Q2", width: 120 },
    { text: '物料', value: 'zzs', prop: "Q3", width: 150 },
    { text: '源单位数量', value: 'tjsl', prop: "Q4", width: 150 },
    { text: '物料', value: 'ftbgzzs', prop: "Q5", width: 150 },
    { text: '物料', value: 'bcpxh', prop: "Q6", width: 150 },
]
export const ReportingworkorderGZPOList = [
    { text: '详情', value: 'order', prop: "ProductionOrderNo", width: 150 },
    { text: '详情', value: 'bcp', width: 250 },
    { text: '物料', value: 'pch', prop: "LotCode", width: 120 },
    { text: '物料', value: 'scrq', prop: "PlanStartDate", width: 150 },
    { text: '物料', value: 'dzpjzl', prop: "AVG", width: 150 },
    { text: '物料', value: 'bcpcczs', prop: "Q1", width: 120 },
    { text: '物料', value: 'jlxh', prop: "Q2", width: 150 },
]
export const ReportingworkorderZZPOList = [
    { text: '详情', value: 'order', prop: "ProductionOrderNo", width: 150 },
    { text: '详情', value: 'pf', width: 250 },
    { text: '物料', value: 'pch', prop: "LotCode", width: 120 },
    { text: '物料', value: 'scrq', prop: "PlanStartDate", width: 150 },
    { text: '物料', value: 'jlccsl', prop: "Q1", width: 150 },
]
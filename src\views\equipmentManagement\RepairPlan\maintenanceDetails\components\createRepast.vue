<template>
    <v-dialog v-model="showDialog" max-width="980px">
        <v-card class="" v-if="dialogType == 'repair'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                维修
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea v-model="form.ExceptionDesc" rows="3" outlined dense label="异常描述"></v-textarea>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea v-model="form.RepairProcess" rows="3" outlined dense label="维修记录"></v-textarea>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea v-model="form.CurrentSituation" rows="3" outlined dense label="原因分析—现状"></v-textarea>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea v-model="form.Reasons1" outlined rows="3" dense label="原因分析"></v-textarea>
                    </v-col>
                    <v-row class="pa-3">
                        <v-col class="py-0" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Parts1" outlined dense label="消耗备件"></v-text-field>
                        </v-col>
                        <v-col class="py-0" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Parts1Num" outlined dense label="消耗备件数量"></v-text-field>
                        </v-col>
                        <v-col class="py-0" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Parts1Unit" outlined dense label="消耗备件单位"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select v-model="form.RepairStatus" :items="equipStatuslist" item-value="ItemValue" item-text="ItemName" outlined dense label="维修状态"></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="form.RepairNature" outlined dense label="维修性质"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-autocomplete
                            v-model="form.RepairUser"
                            :loading="loading"
                            :items="peopleitems"
                            item-value="Code"
                            item-text="Name"
                            :search-input="form.RepairUser"
                            flat
                            outlined
                            dense
                            label="承修人"
                        >
                            <template #item="data">
                                <template v-if="typeof data.item !== 'object'">
                                    <v-list-item-content v-text="data.item"></v-list-item-content>
                                </template>
                                <template v-else>
                                    <v-list-item-content>
                                        <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                        <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                    </v-list-item-content>
                                </template>
                            </template>
                        </v-autocomplete>
                        <!-- <v-text-field v-model="form.RepairUser" outlined dense label="承修人"></v-text-field> -->
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="form.Starttime" type="datetime-local" outlined dense label="开始时间"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="form.Endtime" type="datetime-local" outlined dense label="结束时间"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="form.RepairHours" outlined dense label="维修时长 "></v-text-field>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions pa-4 class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'debug'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                调试
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="debugForm.Debugtype" outlined dense label="调试类型"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-autocomplete
                            v-model="debugForm.Maintenanceperson"
                            :loading="loading"
                            :items="peopleitems"
                            item-value="Code"
                            item-text="Name"
                            :search-input="debugForm.Maintenanceperson"
                            flat
                            outlined
                            dense
                            label="处理人姓名"
                        >
                            <template #item="data">
                                <template v-if="typeof data.item !== 'object'">
                                    <v-list-item-content v-text="data.item"></v-list-item-content>
                                </template>
                                <template v-else>
                                    <v-list-item-content>
                                        <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                        <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                    </v-list-item-content>
                                </template>
                            </template>
                        </v-autocomplete>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select v-model="debugForm.Status" :items="equipStatuslist" item-value="ItemValue" item-text="ItemName" outlined dense label="设备状态"></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea v-model="debugForm.Failuredescription" outlined rows="3" dense label="调式描述"></v-textarea>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="debugForm.Dealtime" type="datetime-local" outlined dense label="开始处理时间"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="debugForm.Fixedtime" type="datetime-local" outlined dense label="调试结束时间 "></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea v-model="debugForm.Remark" outlined rows="2" dense label="备注"></v-textarea>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="adddebug">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { CommissioningRecordsSaveForm, DeviceRepairSaveForm } from '@/api/equipmentManagement/Repair.js';
import { StaffSiteGetList } from '@/api/peopleManagement/basicdata.js';
export default {
    props: {
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        },
        equipStatuslist: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            valid: false,
            showDialog: false,
            classcheckbox: true,
            peopleitems: [],
            strbatchNo: '',
            form: {
                ExceptionDesc: '',
                RepairProcess: '',
                CurrentSituation: '',
                Reasons1: '',
                Parts1: '',
                Parts1Num: '',
                Parts1Unit: '',
                RepairStatus: '',
                RepairNature: '',
                RepairUser: '',
                Starttime: '',
                Endtime: '',
                RepairHours: ''
            },
            debugForm: {
                Debugtype: '',
                Debugcode: '',
                Failuredescription: '',
                Maintenanceperson: '',
                Dealtime: '',
                Fixedtime: '',
                Status: ''
            }
        };
    },
    computed: {
        editedItem() {
            const { ExceptionDesc, RepairProcess, CurrentSituation, Reasons1, Parts1, Parts1Num, Parts1Unit, RepairStatus, RepairNature, RepairUser, Starttime, Endtime, RepairHours } = this.tableItem;
            return {
                ExceptionDesc,
                RepairProcess,
                CurrentSituation,
                Reasons1,
                Parts1,
                Parts1Num,
                Parts1Unit,
                RepairStatus,
                RepairNature,
                RepairUser,
                Starttime,
                Endtime,
                RepairHours
            };
        }
    },
    created() {
        this.queryPeoplelist();
    },
    methods: {
        closeEquip(){
            this.showDialog = false;
            this.$refs.form.reset();
        },
        // 获取人员
        async queryPeoplelist() {
            this.loading = true;
            const res = await StaffSiteGetList({ key: '' });
            let { success, response } = res;
            if (success) {
                this.peopleitems = response;
                this.loading = false;
            }
        },
        // 维修
        async addSave() {
            const paramsKey = Object.keys(this.form);
            const paramsObj = this.form;
            let params = {};
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            params.WoId = this.tableItem.ID;
            params.DeviceName = this.tableItem.DeviceName;
            params.DeviceCode = this.tableItem.DeviceCode;
            const res = await DeviceRepairSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.showDialog = this.classcheckbox ? false : true;
                this.$parent.$parent.RepastInfologGetPage();
            }
        },
        // 调试
        async adddebug() {
            const paramsKey = Object.keys(this.debugForm);
            const paramsObj = this.debugForm;
            let params = {};
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            params.Equipid = this.tableItem.ID;
            const res = await CommissioningRecordsSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.showDialog = this.classcheckbox ? false : true;
                this.$parent.$parent.CommissioningRecords();
            }
        }
    }
};
</script>

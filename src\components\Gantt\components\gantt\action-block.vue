<!-- eslint-disable vue/no-mutating-props -->
<template>
    <div
        class="action-block"
        ref="box"
        :class="[{ active: isActive }]"
        :style="{
            width: `${actionWidth}px`,
            left: `${actionLeft}px`,
            top: `${blockData ? (blockData.top ? blockData.top + 'px' : defaultTop + 'px') : '0px'}`
        }"
    >
        <div class="action-box">
            <!--用于拉伸X 坐标，让他变长缩短-->
            <div class="left-box action" v-if="enableDragSide" @mousedown="onMouseLineDown($event, -1)" @contextmenu.prevent="() => {}"></div>

            <!--时间块的具体内容 拖拽事件-->
            <div class="action-content" @mousedown="enableDragFree ? (isActive ? onMouseMoreFreeDown($event) : onMouseFreeDown($event)) : null">
                <slot></slot>
            </div>
            <!--用于拉伸X 坐标，让他变长缩短-->
            <div class="right-box action" v-if="enableDragSide" @mousedown="onMouseLineDown($event, 1)" @contextmenu.prevent="() => {}"></div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ActionBlock',
    props: {
        //时间块数据
        blockData: {
            type: Object,
            default: () => {}
        },
        //在外头选择的时间
        choiceTime: {
            type: Array,
            default: () => []
        },
        //左右可拖拽
        enableDragSide: {
            type: Boolean,
            default: false
        },
        //是否允许自由拖拽，但是该组件的自由拖拽不进行真实交互，只是回调事件
        enableDragFree: {
            type: Boolean,
            default: false
        },
        //开始时间的字段
        startTimeCol: {
            type: String,
            default: 'startTime'
        },
        //结束时间的字段
        endTimeCol: {
            type: String,
            default: 'endTime'
        },
        //滚动条的距离
        scrollTop: {
            type: Number,
            default: 0
        },
        //是否为激活状态
        isActive: {
            type: Boolean,
            default: false
        },
        //那些激活的的数组，用于emit 反馈
        activeArr: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            //默认的高度
            defaultTop: 10,
            mousePosition: {
                left: 0,
                right: 0
            }
        };
    },
    computed: {
        //时间块的宽度
        actionWidth() {
            if (this.blockData) {
                //计算差值
                let diffTime = (this.blockData[this.endTimeCol].getTime() - this.blockData[this.startTimeCol].getTime()) / 1000 / 60;
                return diffTime;
            }
            return 0;
        },
        //时间块的左边偏移量
        actionLeft() {
            if (this.blockData) {
                //计算时间块的左边距离甘特图边界的距离
                let diffTime = (this.blockData[this.startTimeCol].getTime() - this.choiceTime[0].getTime()) / 1000 / 60;
                return diffTime;
            }
            return 0;
        }
    },
    methods: {
        //时间块左右两边的可拖拽滑块 direction: -1 左边；1右边
        onMouseLineDown(e, direction) {
            let startTime = this.blockData[this.startTimeCol].getTime();
            let endTime = this.blockData[this.endTimeCol].getTime();

            //可滚动区域
            let wordDom = document.getElementById('ganttGrid');

            wordDom.onmousemove = ee => {
                // 用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
                let v = ee.clientX - e.clientX;
                //v左边移动减少
                if (direction == -1) {
                    //左边拉长是减少
                    if (this.mousePosition.left < v) {
                        //如果是缩短，那么就可以进行width不为-1的判断了
                        if (this.actionWidth <= 0) {
                            return;
                        }
                    }

                    //分钟赋值后，设置成new Date
                    // eslint-disable-next-line vue/no-mutating-props
                    this.blockData[this.startTimeCol] = new Date(startTime + v * 1000 * 60);

                    // 如果拉过头，那么就强制让startTime 与 endTime 一致
                    // 拉过头是因为 this.actionWidth 方法计算是异步的，而v差不多是直接响应出来的，有时候v值是新的，但是actionWidth还是久的，就导致了没能拦住
                    if (this.actionWidth <= 0) {
                         // eslint-disable-next-line vue/no-mutating-props
                        this.blockData[this.startTimeCol] = this.blockData[this.endTimeCol];
                    }

                    this.mousePosition.left = v;
                }
                //右边移动
                else if (direction == 1) {
                    //v右边拉长是增加
                    if (this.mousePosition.right > v) {
                        //如果是缩短，那么就可以进行width不为-1的判断了
                        if (this.actionWidth <= 0) {
                            return;
                        }
                    }
                    //分钟赋值后，设置成new Date
                     // eslint-disable-next-line vue/no-mutating-props
                    this.blockData[this.endTimeCol] = new Date(endTime + v * 1000 * 60);

                    if (this.actionWidth <= 0) {
                         // eslint-disable-next-line vue/no-mutating-props
                        this.blockData[this.endTimeCol] = this.blockData[this.startTimeCol];
                    }

                    this.mousePosition.right = v;
                }
            };

            window.onmouseup = () => {
                //执行回调，让回调的地方进行http调用
                this.$emit('moveSideChange', this.blockData);

                wordDom.onmousemove = null;
                window.onmouseup = null;
            };
        },
        //在多选情况下，拖拽这个元素的事件
        onMouseMoreFreeDown(e) {
            let box = this.$refs.box;
            //可滚动区域
            let wordDom = document.getElementById('ganttGrid');

            let disY = e.clientY + this.scrollTop - box.offsetTop;

            let top = 0;

            wordDom.onmousemove = ee => {
                top = ee.clientY + this.scrollTop - disY;

                //中间块移动位置的时候
                this.$emit('freeMove', top);
            };

            window.onmouseup = () => {
                wordDom.onmousemove = null;
                window.onmouseup = null;

                //批量放置航班条
                this.$emit('moreFreeMoveUp', this.activeArr);
            };
        },
        //鼠标自由拖拽用的点下
        onMouseFreeDown(e) {
            let box = this.$refs.box;

            let disX = e.clientX - box.offsetLeft;

            let disY = e.clientY + this.scrollTop - box.offsetTop;

            //可滚动区域
            let wordDom = document.getElementById('ganttGrid');
            wordDom.onmousemove = ee => {
                let left = ee.clientX - disX;
                let top = ee.clientY + this.scrollTop - disY;

                //时间块的宽度
                let domWidth = parseInt(box.style.width);

                //开始时间的数据  直接发生变化
                let startTime = new Date(this.choiceTime[0].getTime() + left * 1000 * 60);
                 // eslint-disable-next-line vue/no-mutating-props
                this.blockData[this.startTimeCol] = startTime;
                //结束时间的数据  直接发生变化
                 // eslint-disable-next-line vue/no-mutating-props
                this.blockData[this.endTimeCol] = new Date(startTime.getTime() + domWidth * 1000 * 60);

                //top只是用来做换行操作，磁性吸附就可应对，不需要对双向绑定的数据做处理
                box.style.top = top + 'px';
                //中间块移动位置的时候
                this.$emit('freeMove', top);
            };

            window.onmouseup = () => {
                //执行回调，让回调的地方进行http调用    中间块移动位置最后松开鼠标后
                this.$emit('freeMoveUp', this.blockData);

                wordDom.onmousemove = null;
                window.onmouseup = null;
            };
        }
    }
};
</script>

<style scoped lang="scss">
.action-block {
    position: absolute;
    height: 30px;
    min-height: 30px;
    max-height: 30px;
    background-color: #ffedb3;
    box-sizing: border-box;
    border: 1px solid gold;
    z-index: 3;
    border-radius: 4px;

    //激活状态的样式
    &.active {
        border: 2px solid #a88e00;
    }
    .action-box {
        height: 100%;
        display: flex;
        justify-content: space-between;
        .action-content {
            flex: 1;
        }
        .action {
            box-sizing: border-box;
            cursor: pointer;
            padding: 0 5px;
            opacity: 0;
            background-color: rgba(255, 255, 255, 0.5);
            &:hover {
                opacity: 1;
            }
        }
    }
}
</style>

import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Inventory'

//room下拉数据
export function GetMaterialLabelViewPageListByProId(data) {
    const api = '/api/MaterialLabelView/GetPageListByProId'
    return getRequestResources(baseURL, api, 'post', data);
}
//room下拉数据
export function MyGet_MaterialLabelData(data) {
    const api = '/api/MaterialLabelView/Get_MaterialLabelData'
    return getRequestResources(baseURL, api, 'post', data);
}
//room下拉数据
export function GetAddInventByWMSLabel(data) {
    const api = '/api/MaterialLabelView/AddInventByWMSLabel'
    return getRequestResources(baseURL, api, 'post', data);
}
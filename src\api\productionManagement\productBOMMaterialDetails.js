import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM'
// 列表
export function getSapSegmentMaterialStepList(data) {
    const api =  '/api/SapSegmentMaterialStep/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

export function getUnitList(data) {
    const api =  '/api/Equipment/GetLineMachineList'
    return getRequestResources(baseURL, api, 'post', data);
}

// 新增-编辑
export function saveSapSegmentMaterialStep(data) {
  const api =  '/api/SapSegmentMaterialStep/SaveForm'
  return getRequestResources(baseURL, api, 'post', data);
}

// 删除 
export function deleteSapSegmentMaterialStep(data) {
  const api =  '/api/SapSegmentMaterialStep/Delete'
  return getRequestResources(baseURL, api, 'post', data);
}

// =================== 工艺关键参数 API ===================

// 工艺关键参数列表
export function getProcessParametersList(data) {
  const api = '/api/SapSegmentParameter/GetPageList'
  return getRequestResources(baseURL, api, 'post', data);
}

// 工艺关键参数新增-编辑
export function saveProcessParameter(data) {
  const api = '/api/SapSegmentParameter/SaveForm'
  return getRequestResources(baseURL, api, 'post', data);
}

// 工艺关键参数删除
export function deleteProcessParameter(data) {
  const api = '/api/SapSegmentParameter/Delete'
  return getRequestResources(baseURL, api, 'post', data);
}

// =================== 工艺质检参数 API ===================

// 工艺质检参数列表
export function getQualityParametersList(data) {
  const api = '/api/SapSegmentQcParameter/GetPageList'
  return getRequestResources(baseURL, api, 'post', data);
}

// 工艺质检参数新增-编辑
export function saveQualityParameter(data) {
  const api = '/api/SapSegmentQcParameter/SaveForm'
  return getRequestResources(baseURL, api, 'post', data);
}

// 工艺质检参数删除
export function deleteQualityParameter(data) {
  const api = '/api/SapSegmentQcParameter/Delete'
  return getRequestResources(baseURL, api, 'post', data);
}
<template>
    <div class="dictionary-view">
        <!-- <TreeView :items="treeData" :title="$t('TPM_SBGL_SBTZGL._SBCX')" @clickClassTree="clickClassTree"></TreeView> -->
        <div class="dictionary-main">
            <SearchForm ref="search" :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <!-- <v-btn color="primary" v-has="'SBDJJH_ADD'" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ') }}</v-btn> -->
                    <v-btn color="primary" v-has="'SBDJJH_SCDJJH'" @click="createSpotCheck()">{{ $t('TPM_SBGL_SBDJJH._SCDJJH') }}</v-btn>
                    <v-btn color="primary" v-has="'SBDJJH_ALLREMOVE'" :disabled="!deleteList.length" @click="btnClickEvet('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn>
                    <v-btn color="primary" v-has="'SBDJJH_DC'" @click="handleExport">{{ $t('GLOBAL._EXPORT') }}</v-btn>
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    ref="Tables"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_SBDJJH"
                    :headers="keepListColum"
                    :clickFun="clickFun"
                    :desserts="desserts"
                    @selectePages="selectePages"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"
                ></Tables>
                <!-- <createRepast ref="createRepast" :type="type" :repastTypelist="repastTypelist" :dialogType="dialogType" :tableItem="tableItem"></createRepast> -->
            </v-card>
        </div>
        <el-drawer size="80%" :title="drawTitle" :wrapperClosable="false" :visible.sync="detailShow" direction="rtl">
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn color="primary" v-has="'SBDJJH_PLDJ'" :disabled="!Flag" @click="btnClickEvet('spotCheck')">{{ $t('TPM_SBGL_SBDJJH._PLDJ') }}</v-btn>
                    <!-- <v-btn color="primary" v-has="'SBDJJH_PLFJ'" :disabled="!selected.length" @click="btnClickEvet('recheck')">{{ $t('TPM_SBGL_SBDJJH._PLFJ') }}</v-btn> -->
                </div>
                <Tables
                    :page-options="pageOptions2"
                    :loading="loading2"
                    :btn-list="btnList2"
                    tableHeight="calc(100vh - 220px)"
                    table-name="TPM_SBGL_SBDJJH_XQ"
                    :headers="keepListdetilsColum"
                    :desserts="desserts2"
                    @selectePages="selectePages2"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems2"
                    @toggleSelectAll="SelectedItems2"
                ></Tables>
            </v-card>
        </el-drawer>
        <!-- <v-dialog v-model="isShowBatchEdit" persistent scrollable width="55%">
            <batchEdit @getdata="RepastInfoGetPage" @closePopup="isShowBatchEdit = false" :selected="deleteList" v-if="isShowBatchEdit" />
        </v-dialog>-->
        <v-dialog v-model="isShowcreatTable" persistent scrollable width="70%">
            <creatTable ref="creatTable" @closePopup="isShowcreatTable = false" @loadData="loadData" v-if="isShowcreatTable" />
        </v-dialog>
        <el-dialog :title="$t('GLOBAL._IMGINPORT')" :visible.sync="imgModel">
            <!-- <div v-if="ImgPath != ''"><img :src="ImgPath" style="width: 100%" /></div> -->
            <el-upload ref="upload" accept="jpg,png" class="upload-demo" :auto-upload="false" action="" :on-change="FileChange" :on-remove="FileRemove" :limit="1" :file-list="FileList">
                <el-button size="small" type="primary">点击上传</el-button>
                <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
            </el-upload>
            <span slot="footer" class="dialog-footer">
                <el-button class="tablebtn" icon="el-icon-folder-checked" @click="Save()">{{ $t('GLOBAL._BC') }}</el-button>
                <el-button @click="imgModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
        <createRepast2
            ref="createRepast2"
            :rowtableItem="rowtableItem"
            @loadData2="loadData2"
            :SpotCheckType="SpotCheckType"
            :DeviceCategoryId="DeviceCategoryId"
            :dialogType="dialogType"
            :tableItem="tableItem"
        ></createRepast2>
        <!-- <spotCheckPopup ref="spotCheckPopup" @update="RepastInfoLogGetPage" :operaObj="tableItem" :selected="selected" :type="type" :isBatch="isBatch" :MaintainId="rowtableItem.ID" /> -->
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';

import { GetListByLevel } from '@/api/common.js';
import { GetSpotCheckItemGetFileUrl } from '@/api/equipmentManagement/SpotCheckItem.js';
import {
    GetSpotCheckWoPageList,
    GetSpotCheckItemUploadFile,
    GetSpotCheckWoItemPageList,
    GetSpotCheckWoDelete,
    GetSpotCheckWoItemSaveForm,
    GetSpotCheckWoItemGetFileUrl,
    GetSpotCheckWoItemDelete,
    GetSpotCheckWoItemSpotCheck,
    GetSpotCheckWoItemSingleSpotCheck
} from '@/api/equipmentManagement/SpotCheckWo.js';
import { keepListColum, keepListdetilsColum } from '@/columns/equipmentManagement/upkeep.js';
import { configUrl } from '@/config';
import { Message } from 'element-ui';
import { GetExportData } from '@/api/equipmentManagement/Equip.js';
import { GetPersonList } from '@/api/equipmentManagement/Equip.js';

export default {
    name: 'RepastModel',
    components: {
        //createRepast: () => import('./components/createRepast.vue')
        // batchEdit: () => import('./components/batchEdit.vue'),
        creatTable: () => import('./components/creatTable.vue'),
        createRepast2: () => import('./components/createRepast2.vue')
        // spotCheckPopup: () => import('./components/spotCheckPopup.vue')
    },
    data() {
        return {
            detailShow: false,
            isBatch: false,
            selected: [],
            // treeData: [],
            type: 'spotCheck',
            isShowcreatTable: false,
            isShowBatchEdit: false,
            drawTitle: '',
            // tree 字典数据
            loading: true,
            loading2: false,
            showFrom: false,
            Flag: false,
            papamstree: {
                DeviceCode: '',
                AssetName: '',
                DeviceName: '',
                Status: '',
                CheckBy: '',
                pageIndex: 1,
                pageSize: 20,
                orderByFileds: 'CreateDate desc'
            },
            //查询条件
            keepListColum,
            keepListdetilsColum,
            desserts: [],
            desserts2: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            pageOptions2: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            hasChildren: {}, // 新增字典详情判断-子节点才能新增
            rowtableItem: {},
            imgModel: false,
            //就餐类型
            repastTypelist: [],
            lineCodeList: [],
            FileList: [],
            FilePath: '',
            ImgPath: '',
            SpotCheckWoStatus: [],
            SpotCheckType: [],
            DeviceMngData: [],
            DeviceByData: [],
            DeviceCategoryId: ''
        };
    },
    computed: {
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'DeviceCode',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipCode'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipCode')
                },
                {
                    value: '',
                    key: 'DeviceName',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Name'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Name')
                },
                {
                    value: '',
                    key: 'AssetName',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.AssetName'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.AssetName')
                },
                {
                    value: '',
                    key: 'CheckBy',
                    byValue: 'ItemValue',
                    selectData: this.DeviceByData,
                    type: 'select',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.PersonName'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.PersonName')
                },
                {
                    value: '',
                    key: 'Status',
                    selectData: this.SpotCheckWoStatus,
                    type: 'select',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Status'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.Status')
                },
                {
                    value: '',
                    key: 'Type',
                    selectData: this.SpotCheckType,
                    type: 'select',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipType'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL.EquipType')
                }
            ];
        },
        btnList() {
            return [
                // {
                //     text: '创建任务明细',
                //     code: 'newAdd',
                //     type: 'primary',
                //     icon: ''
                // },
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    showList: ['未点检', '点检中'],
                    showKey: 'Status',
                    authCode: 'SBDJJH_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    showList: ['未点检', '点检中'],
                    showKey: 'Status',
                    type: 'red',
                    icon: '',
                    authCode: 'SBDJJH_DELETE'
                }
            ];
        },
        btnList2() {
            return [
                {
                    text: this.$t('GLOBAL.Spotcheck'),
                    code: 'Spotcheck',
                    showList: ['未点检'],
                    showKey: 'Status',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBDJJH_DJ'
                },
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit2',
                    type: 'primary',
                    showList: ['未点检', '点检中'],
                    showKey: 'Status',
                    icon: '',
                    authCode: 'SBDJJH_DJ'
                },
                {
                    text: this.$t('DFM_JYLXTY._SCTP'),
                    code: 'img',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBDJJH_FJ'
                },
                {
                    text: this.$t('GLOBAL.CheckImg'),
                    code: 'openimg',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBDJJH_FJ'
                },
                {
                    text: this.$t('GLOBAL._CheckFile'),
                    code: 'file',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBDJJH_FJ'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete2',
                    type: 'red',
                    icon: '',
                    authCode: 'SBDJJH_MX_DELETE'
                }
            ];
        }
    },
    async created() {
        let DeviceMng = await GetPersonList('DeviceMng');
        this.DeviceMngData = DeviceMng.response[0].ChildNodes;
        this.DeviceMngData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        let DeviceBy = await GetPersonList('DeviceBy');
        this.DeviceByData = DeviceBy.response[0].ChildNodes;
        this.DeviceByData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.SpotCheckType = await this.$getNewDataDictionary('SpotCheckType');
        this.SpotCheckWoStatus = await this.$getNewDataDictionary('SpotCheckStatus');
        // this.GetFactorylineTree();
        await this.RepastInfoGetPage();
        await this.getLineCodeList();
    },
    methods: {
        loadData2() {
            this.RepastInfoLogGetPage();
        },
        async Save() {
            let obj = JSON.parse(JSON.stringify(this.tableItem));
            obj.CheckFile = this.FilePath;
            let res = await GetSpotCheckWoItemSaveForm(obj);
            if (res.success) {
                this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
                this.RepastInfoLogGetPage();
                this.imgModel = false;
            }
        },
        async FileChange(File, fileList) {
            const formData = new FormData();
            formData.append('file', File.raw);
            let res = await GetSpotCheckItemUploadFile(formData);
            this.FilePath = res.response.FilePath;
        },
        FileRemove(File, fileList) {
            this.FileList = fileList;
            this.FilePath = '';
        },
        loadData() {
            this.RepastInfoGetPage();
        },
        async handleExport() {
            let params = {
                ...this.papamstree
            };
            const baseUrl3 = configUrl[process.env.VUE_APP_SERVE]['baseURL_EQUIPMENT'] + `/api/SpotCheckWo/ExportData`;
            let res = await GetExportData(baseUrl3, params);
            let binaryData = [];
            binaryData.push(res);
            const url = window.URL.createObjectURL(new Blob(binaryData));
            console.log(url);
            const link = document.createElement('a');
            link.href = url;
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            let fileName = `设备点检任务${formattedDateTime}.xlsx`;
            document.body.appendChild(link);
            link.setAttribute('download', fileName);
            link.click();
            window.URL.revokeObjectURL(link.href);
        },
        async createSpotCheck() {
            this.isShowcreatTable = true;
            setTimeout(() => {
                this.$refs.creatTable.PlanStartDate = '';
            }, 500);
            // let resp = await createSpotCheckSchedule({});
            // this.$store.commit('SHOW_SNACKBAR', { text: resp.msg, color: 'success' });
            // this.RepastInfoGetPage();
        },
        // 获取列表
        async getLineCodeList() {
            const res = await GetListByLevel({ key: 'ProductLine' });
            const { success, response } = res || {};
            if (response && success) this.lineCodeList = response;
            else this.lineCodeList = [];
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.RepastInfoGetPage();
        },
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                ...this.papamstree
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading = true;
            const res = await GetSpotCheckWoPageList(params);
            let { success, response } = res;
            response.data.forEach(item => {
                this.SpotCheckWoStatus.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                this.DeviceMngData.forEach(it => {
                    if (item.Manager == it.ItemValue) {
                        item.Manager = it.ItemName;
                    }
                });
                this.DeviceByData.forEach(it => {
                    if (item.CheckBy == it.ItemValue) {
                        item.CheckBy = it.ItemName;
                    }
                });
            });
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        clickFun(data) {
            this.rowtableItem = data || {};
            this.drawTitle = this.rowtableItem.DeviceName + ' | ' + this.rowtableItem.DeviceCode;
            this.DeviceCategoryId = this.rowtableItem.DeviceCategoryId;
            this.detailShow = true;
            this.RepastInfoLogGetPage();
        },
        // 明细列表查询
        async RepastInfoLogGetPage() {
            if (!this.rowtableItem.ID) {
                return false;
            }
            let params = {
                WoId: this.rowtableItem.ID,
                pageIndex: this.pageOptions2.pageIndex,
                pageSize: this.pageOptions2.pageSize
            };
            this.loading2 = true;
            const res = await GetSpotCheckWoItemPageList(params);
            let { success, response } = res;
            response.forEach(item => {
                this.SpotCheckWoStatus.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                        item.StatusValue = it.ItemValue;
                    }
                });
            });
            if (success) {
                this.loading2 = false;
                this.desserts2 = response || {} || [];
                this.pageOptions2.total = response.dataCount;
                this.pageOptions2.page = response.page;
                this.pageOptions2.pageCount = response.pageCount;
                this.pageOptions2.pageSize = response.pageSize;
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            this.tableItem = {};
            switch (val) {
                case 'delete':
                    this.deltable(val);
                    break;
                case 'spotCheck':
                    this.spotCheck();
                    break;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'Spotcheck':
                    this.selected = [];
                    if (this.tableItem.InputType == '不录入') {
                        this.selected.push(this.tableItem);
                        this.spotCheck();
                    } else {
                        this.$prompt(`${this.$t('TPM_SBGL_SBDJJH.InputContext')}`, `${this.$t('GLOBAL._TS')}`, {
                            confirmButtonText: `${this.$t('GLOBAL._QD')}`,
                            cancelButtonText: `${this.$t('GLOBAL._QX')}`
                        })
                            .then(async ({ value }) => {
                                this.tableItem.Context = value;
                                let res = await GetSpotCheckWoItemSingleSpotCheck(this.tableItem);
                                if (res.success) {
                                    this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
                                    this.RepastInfoLogGetPage();
                                    this.RepastInfoGetPage();
                                }
                            })
                            .catch(() => {});
                    }
                    return;
                case 'edit':
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'edit2':
                    this.$refs.createRepast2.Fileform.FilePath = this.tableItem.FilePath;
                    this.$refs.createRepast2.Fileform.Name = this.tableItem.FileName;
                    this.$refs.createRepast2.Fileform.FileList = [];
                    this.$refs.createRepast2.clearFiles();
                    if (this.tableItem.Filename != '' && this.tableItem.Filename != null) {
                        this.$refs.createRepast2.Fileform.FileList = [
                            {
                                name: this.tableItem.Filename
                            }
                        ];
                    }
                    this.$refs.createRepast2.showDialog = true;
                    return;
                case 'openimg':
                    if (item.CheckFile == '' || item.CheckFile == null) {
                        Message({
                            message: `${this.$t('GLOBAL.NoFile')}`,
                            type: 'warning'
                        });
                        return false;
                    }
                    this.openImg();
                    return;
                case 'img':
                    this.getImg();
                    return;
                case 'file':
                    if (item.FilePath == '' || item.FilePath == null) {
                        Message({
                            message: `${this.$t('GLOBAL.NoFile')}`,
                            type: 'warning'
                        });
                        return false;
                    }
                    this.getFile();
                    return;
                case 'delete':
                    this.deltable(type);
                    return;
                case 'delete2':
                    this.deltable(type);
                    return;
            }
        },
        async openImg() {
            let res = await GetSpotCheckWoItemGetFileUrl('', this.tableItem.CheckFile);
            window.open(res.response);
        },
        clearFiles() {
            this.FileList = [];
            this.$refs.upload.clearFiles();
        },
        async getImg() {
            this.ImgPath = '';
            this.imgModel = true;
            this.clearFiles();
        },
        async getFile() {
            let res = await GetSpotCheckWoItemGetFileUrl('', this.tableItem.FilePath);
            window.open(res.response);
        },
        async spotCheck() {
            this.$confirms({
                title: this.$t('GLOBAL.Spotcheck'),
                message: this.$t('GLOBAL._COMFIRM_DJ'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await GetSpotCheckWoItemSpotCheck(this.selected);
                    if (res.success) {
                        this.selected = [];
                        this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
                        this.RepastInfoLogGetPage();
                        this.RepastInfoGetPage();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除
        deltable(type) {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                let list = type == 'delete' ? this.deleteList : this.selected;
                list.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    if (type == 'delete') {
                        let res = await GetSpotCheckWoDelete(params);
                        if (res.success) {
                            this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                            this.RepastInfoGetPage();
                        }
                    } else if (type == 'delete2') {
                        let res = await GetSpotCheckWoItemDelete(params);
                        if (res.success) {
                            this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                            this.RepastInfoLogGetPage();
                        }
                    }
                    this.tableItem = {};
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            this.deleteList = [...item];
        },
        SelectedItems2(item) {
            this.selected = [...item];
            if (this.selected.length == 0) {
                this.Flag = false;
            } else {
                this.Flag = this.selected.every(item => {
                    return item.Status == '未点检' && item.InputType == '不录入';
                });
            }
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        },
        selectePages2(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoLogGetPage();
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}
</style>

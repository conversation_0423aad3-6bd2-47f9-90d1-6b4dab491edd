<template>
    <v-dialog scrollable persistent v-model="showDialog" max-width="980px">
        <v-card class="" v-if="dialogType == 'repair'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('TPM_SBGL_SBWXJL._WX') }}({{ tableItem.RepairCode }})
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" class="pa-2">
                    <v-card class="px-2 mb-2">
                        <!-- <v-card-title class="text-h6 justify-space-between"> {{$t('TPM_SBGL_SBWXJL._WXMX')}}</v-card-title> -->
                        <v-card-text>
                            <v-row class="pt-2">
                                <v-col class="py-0 px-3" cols="12">
                                    <v-textarea v-model="form.ExceptionDesc" rows="3" required
                                        :rules="[v => !!v || '异常描述必填']" outlined dense
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.ExceptionDesc')"></v-textarea>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12">
                                    <v-textarea v-model="form.RepairProcess" rows="3" required
                                        :rules="[v => !!v || '维修记录必填']" outlined dense
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairProcess')"></v-textarea>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                                    <v-autocomplete v-model="form.Reasons1" @change="getReason" :items="ressionList"
                                        required :rules="[v => !!v || '问题原因必填']"
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.Reasons1')" item-value="value"
                                        item-text="label" return-object outlined dense></v-autocomplete>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                                    <v-select v-model="form.RepairStatus" :items="equipStatuslist" item-value="ItemValue"
                                        item-text="ItemName" required :rules="[v => !!v || '维修状态必填']" outlined dense
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairStatus')"></v-select>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                                    <v-autocomplete v-model="form.RepairUserCode" :items="peopleitems" item-value="Code"
                                        item-text="Name" :search-input="form.RepairUserCode" flat required
                                        :rules="[v => !!v || '承修人必填']" outlined dense
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairUser')">
                                        <template #item="data">
                                            <template v-if="(typeof data.item) !== 'object'">
                                                <v-list-item-content v-text="data.item"></v-list-item-content>
                                            </template>
                                            <template v-else>
                                                <v-list-item-content>
                                                    <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                                    <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                                </v-list-item-content>
                                            </template>
                                        </template>
                                    </v-autocomplete>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12">
                                    <v-textarea v-model="form.CurrentSituation" required :rules="[v => !!v || '原因分析必填']"
                                        outlined rows="3" dense
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.CurrentSituation')"></v-textarea>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                                    <v-text-field :disabled="!!t2Time && !!t2Time.length" v-model="form.Starttime"
                                        type="datetime-local" required @change="handelChangeTime"
                                        :rules="[v => !!v || '开始时间必填']" outlined dense
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.StartTime')"></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                                    <v-text-field v-model="form.Endtime" @change="handelChangeTime" type="datetime-local"
                                        required :rules="endTimeRule" outlined dense :max="getMinTime()"
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.EndTime')"></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                                    <v-text-field type="number" v-model="form.RepairHours" required :rules="RepairHoursRule"
                                        outlined dense
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairHours')"></v-text-field>
                                </v-col>
                                <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                                    <v-text-field v-model="form.RepairPrice" :rules="RepairPriceRule" type="number" required
                                        outlined dense
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairPrice')"></v-text-field>
                                </v-col>
                            </v-row>
                        </v-card-text>
                    </v-card>
                    <v-card class="px-2" v-if="form.RepairStatus != 1">
                        <!-- <v-card-title class="text-h6 justify-space-between">消耗备件</v-card-title> -->
                        <v-card-text>
                            <v-row class="pt-2">
                                <v-col class="py-0" cols="12" sm="7" md="7">
                                    <v-autocomplete v-model="form.PartsCode1" :items="PartsList" item-value="SparePartsCode"
                                        item-text="labelText" return-object outlined @change="handleParts" dense
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.Parts1')">
                                    </v-autocomplete>
                                </v-col>
                                <v-col class="py-0" cols="12" sm="3" md="3">
                                    <v-text-field type="number" v-model="form.Parts1Num" :rules="numRules" outlined dense
                                        :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.Parts1Num')"></v-text-field>
                                </v-col>
                                <v-col v-if="form.PartsCode1" class="py-0" cols="12" sm="2" md="2">
                                    <span style="height: 30px; line-height: 30px; margin-bottom: 20px"
                                        class="d-inline-block">{{ $t('TPM_SBGL_SBWXJL._DQKC') }}：{{ CurrentStock }}</span>
                                </v-col>
                            </v-row>
                        </v-card-text>
                    </v-card>
                    <v-row>
                        <v-col :cols="12" :lg="4">
                            <v-checkbox v-model="form.IsCase" :label="$t('TPM_SBGL_SBWXJL._SFCCWZSK')"></v-checkbox>
                        </v-col>
                        <v-col :cols="12" :lg="4">
                            <v-checkbox v-model="isAdjust" @change="handleAdjust" :label="$t('TPM_SBGL_SBWXJL._TZBYGZ')">
                                <template v-slot:label>
                                    {{ $t('TPM_SBGL_SBWXJL._TZBYGZ') }}
                                    <v-btn v-if="isAdjust" @click.stop="editAdjustRule()" text color="primary">{{
                                        $t('GLOBAL._BJ') }}</v-btn>
                                </template>
                            </v-checkbox>
                        </v-col>
                    </v-row>
                </v-form>
                <v-dialog scrollable persistent v-model="isShowAdjustPopup" width="50%">
                    <IsAdjustRule :ruleForm="ruleForm" @closePopup="closePopup" v-if="isShowAdjustPopup" />
                </v-dialog>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions pa-4 class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'debug'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('TPM_SBGL_SBWXJL._TS') }}({{ tableItem.RepairCode }})
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form1" v-model="valid">
                    <v-row class="pt-8">
                        <!-- <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="debugForm.Debugtype" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_TSMX.Debugtype')"></v-text-field>
                        </v-col> -->
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete v-model="debugForm.Maintenanceperson" :items="peopleitems" item-value="Code"
                                item-text="Name" :search-input="debugForm.Maintenanceperson" flat outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_TSMX.Maintenanceperson')">
                                <template #item="data">
                                    <template v-if="(typeof data.item) !== 'object'">
                                        <v-list-item-content v-text="data.item"></v-list-item-content>
                                    </template>
                                    <template v-else>
                                        <v-list-item-content>
                                            <v-list-item-title v-html="data.item.Code"></v-list-item-title>
                                            <v-list-item-subtitle v-html="data.item.Name"></v-list-item-subtitle>
                                        </v-list-item-content>
                                    </template>
                                </template>
                            </v-autocomplete>
                        </v-col>
                        <!-- <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="debugForm.Status" :items="equipStatuslist" item-value="ItemValue"
                                item-text="ItemName" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_TSMX.Status')"></v-select>
                        </v-col> -->

                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="debugForm.Dealtime" type="datetime-local" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_TSMX.Dealtime')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field :disabled="!!t4Time && !!t4Time.length" v-model="debugForm.Fixedtime"
                                type="datetime-local" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_TSMX.Fixedtime')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="debugForm.Failuredescription" outlined rows="3" dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_TSMX.Failuredescription')"></v-textarea>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="debugForm.Remark" outlined rows="2" dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_TSMX.Remark')"></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="adddebug">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
// import { GetReasontree } from '@/api/factoryPlant/reasonDetail.js';
import { CommissioningRecordsSaveForm, DeviceRepairSaveForm, getPostPrice, getMaintainTime } from '@/api/equipmentManagement/Repair.js';
import { StaffSiteGetList } from '@/api/peopleManagement/basicdata.js';
import { DeviceRepairProjectGetList } from '@/api/equipmentManagement/equipmentReasonTree.js';
import IsAdjustRule from './isAdjustRule.vue';
import dayjs from 'dayjs';
export default {
    components: {
        IsAdjustRule
    },
    props: {
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => { }
        },
        equipStatuslist: {
            type: Array,
            default: () => []
        },
        ressionList: {
            type: Array,
            default: () => []
        },
        peopleitems: {
            type: Array,
            default: () => []
        },
        PartsList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            RepairPriceRule: [v => {
                if (Number(v) > Number(this.maxCost)) {
                    return '人工成本过高'
                }
                return !!v || "人工成本必填"
            }],
            RepairHoursRule: [
                v => {
                    if (Number(v) > Number(this.maxWxTime)) {
                        return '维修时长不能大于结束时间减去开始时间'
                    }
                    return !!v || "维修时长必填"
                }
            ],
            maxWxTime: Infinity,
            endTimeRule: [
                v => {
                    if (this.t3Time && this.t3Time.length) {
                        if (dayjs(v).unix() > dayjs(this.t3Time[0].CreateDate).unix()) {
                            return '结束时间填写不符合规则'
                        }
                    }
                    return !!v || "结束时间必填"
                }],
            numRules: [
                v => {
                    if (this.form.PartsCode1 && v < 1) {
                        return '备件数量不能小于1';
                    }
                    if (!this.form.PartsCode1) {
                        return true;
                    }
                    return !!v || '备件数量不能小于1';
                }
            ],
            t2Time: [],
            t3Time: [],
            CurrentStock: 0,
            ruleForm: null,
            isShowAdjustPopup: false,
            costPrice: 0,
            maxCost: Infinity,
            isAdjust: false,
            valid: true,
            showDialog: false,
            classcheckbox: true,
            // peopleitems: [],
            // ressionList: [],
            strbatchNo: '',
            form: {
                AbnormalDesc: '',
                ExceptionDesc: '',
                RepairProcess: '',
                CurrentSituation: '',
                Reasons1: '',
                ReasonsId1: '',
                Parts1: '',
                Parts1Name: '',
                Parts1Code: '',
                Parts1Num: '',
                RepairStatus: '',
                RepairNature: '',
                RepairUserCode: '20313708',
                Starttime: '',
                Endtime: '',
                RepairHours: '',
                RepairPrice: '',
                IsCase: false
            },
            debugForm: {
                // Debugtype: '',
                Debugcode: '',
                Failuredescription: '',
                Maintenanceperson: '',
                Dealtime: '',
                Fixedtime: ''
                // Status: ''
            }
        };
    },
    computed: {
        editedItem() {
            const {
                AbnormalDesc,
                ExceptionDesc,
                RepairProcess,
                CurrentSituation,
                ReasonsId1,
                Reasons1,
                Parts1,
                PartsCode1,
                Parts1Num,
                RepairStatus,
                RepairNature,
                RepairUserCode,
                Starttime,
                Endtime,
                RepairHours,
                RepairPrice
            } = this.tableItem;
            return {
                AbnormalDesc,
                ExceptionDesc,
                RepairProcess,
                CurrentSituation,
                Reasons1,
                ReasonsId1,
                Parts1,
                PartsCode1,
                Parts1Num,
                RepairStatus,
                RepairNature,
                RepairUserCode,
                Starttime,
                Endtime,
                RepairHours,
                RepairPrice,
                IsCase: this.tableItem.IsCase == '1' ? true : false
            };
        }
    },
    watch: {
        'form.RepairHours': {
            handler(nv, ov) {
                this.handleChangeRepairHours(nv)
            }
        },
        showDialog: {
            handler(newa, olda) {
                if (newa) {
                    switch (this.dialogType) {
                        case 'repair':
                            this.form.RepairStatus = '2';
                            this.form.RepairUserCode = '20313708';
                            this.getCost()
                            this.initMaintainTime()
                            break;
                        case 'debug':
                            this.initMaintainTime()
                            break
                    }
                }
                if (olda) {
                    if (this.dialogType == 'repair') {
                        this.$refs.form.reset();
                    } else {
                        this.$refs.form1.reset();
                    }
                }
            },
            deep: true,
            immediate: true
        }
    },
    created() {
        //  this.queryPeoplelist();
        // this.getReasonTreeList();
    },
    methods: {
        async initMaintainTime() {
            let resp = await getMaintainTime({ eventNo: this.tableItem.EventNum })

            this.t2Time = resp.response.filter(item => item.Process === 'T2')
            this.t3Time = resp.response.filter(item => item.Process === 'T3')
            this.t4Time = resp.response.filter(item => item.Process === 'T4')
            if (this.dialogType == 'repair') {
                this.t2Time && this.t2Time.length && (this.form.Starttime = dayjs(this.t2Time[0].CreateDate).format('YYYY-MM-DD HH:mm'))
                this.t3Time && this.t3Time.length && (this.form.Endtime = dayjs(this.t3Time[0].CreateDate).format('YYYY-MM-DD HH:mm'))
                this.handelChangeTime()
            }

            if (this.dialogType == 'debug') {
                this.t3Time && this.t3Time.length && (this.debugForm.Dealtime = dayjs(this.t3Time[0].CreateDate.format('YYYY-MM-DD HH:mm')))
                this.t4Time && this.t4Time.length && (this.debugForm.Fixedtime = dayjs(this.t4Time[0].CreateDate.format('YYYY-MM-DD HH:mm')))
            }
        },
        async getCost() {
            let resp = await this.$getDataDictionary('Maintenance cost')
            this.costPrice = resp[0].ItemValue

            // let postName = ''
            // let resp = await getPostPrice({ key: postName })
            // this.costPrice = resp[0].PostPrice
        },
        handleChangeRepairHours(val) {
            this.form.RepairPrice = (val * this.costPrice).toFixed(2)
            this.maxCost = this.form.RepairPrice
        },
        handelChangeTime() {
            //  注意： dayjs转换的时间戳默认是秒 所以要 * 1000
            let diffTime = 0
            if (this.form.Starttime && this.form.Endtime) {
                diffTime = dayjs(this.form.Endtime).unix() - dayjs(this.form.Starttime).unix()
                if (diffTime < 0) {
                    this.$store.commit('SHOW_SNACKBAR', { text: "开始时间不能晚于结束时间", color: 'error' });
                    this.form.Endtime = ''
                    return false
                }
                let hours = 1000 * 60 * 60
                this.form.RepairHours = (diffTime * 1000 / hours).toFixed(2)
                this.maxWxTime = this.form.RepairHours
            }
        },
        getMinTime() {
            return dayjs().format('YYYY-MM-DD HH:mm')
        },
        async handleParts(val) {
            this.form.Parts1Num = '';
            this.CurrentStock = val.CurrentStock;
        },
        editAdjustRule() {
            this.isShowAdjustPopup = true;
        },
        closePopup(data) {
            if (data) {
                this.ruleForm = data;
            }
            this.isShowAdjustPopup = false;
        },
        handleAdjust() {
            if (this.isAdjust) this.isShowAdjustPopup = true;
            if (!this.isAdjust) this.ruleForm = null;
        },
        // 获取原因树型
        // async getReasonTreeList() {
        //     const res = await GetReasontree();
        //     const { success, response } = res;
        //     if (success) {
        //         const data = response;
        //         const datalist = data.find(item => item.name == '停机原因');
        //         this.ressionList = datalist.children;
        //     }
        // },
        // 获取原因分析
        async getReason(v) {
            if (v) this.form.CurrentSituation = '';
            let params = {
                personcode: v.id,
                pageIndex: 1,
                pageSize: 9999
            };
            const res = await DeviceRepairProjectGetList(params);
            let { success, response } = res;
            if (success) {
                response.forEach(item => {
                    this.form.CurrentSituation += item.Methods + '\n';
                });
                // console.log(response)
                // this.form.CurrentSituation = response
            }
        },
        closeEquip() {
            this.showDialog = false;
            this.$refs.form.reset();
        },
        // 获取人员
        async queryPeoplelist() {
            this.loading = true;
            const res = await StaffSiteGetList({ key: '' });
            let { success, response } = res;
            if (success) {
                //this.peopleitems = response;
                this.loading = false;
            }
        },
        // 维修
        async addSave() {
            let formSate = await this.$refs.form.validate();
            if (formSate) {
                const paramsKey = Object.keys(this.form);
                const paramsObj = this.form;
                let params = {};
                paramsKey.forEach(item => {
                    params[item] = paramsObj[item];
                });
                const { PartsCode1, Reasons1 } = paramsObj;
                let obj = {};
                if (PartsCode1?.SparePartsCode) {
                    const { SparePartsCode, SparePartsName } = PartsCode1;
                    obj = { PartsCode1: SparePartsCode, Parts1: SparePartsName };
                }
                if (Reasons1.value) {
                    const { value, label } = Reasons1;
                    obj = { ...obj, Reasons1: label, ReasonsId1: value };
                }
                params.WoId = this.tableItem.ID;
                params.WoCode = this.tableItem.RepairCode;
                params.DeviceName = this.tableItem.DeviceName;
                params.DeviceCode = this.tableItem.DeviceCode;
                params.RepairUser = this.peopleitems.find(item => item.Code == params.RepairUserCode)?.Name
                // params.Reasons1 = this.jionReson(paramsObj.Reasons1, 'name');
                // params.ReasonsId1 = this.jionReson(paramsObj.Reasons1, 'id');
                params.IsCase = paramsObj.IsCase ? '1' : '0';
                console.log({ ...params, ...obj });
                const res = await DeviceRepairSaveForm({ ...params, ...obj, maintainRule: this.ruleForm });
                let { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                    this.showDialog = this.classcheckbox ? false : true;
                    this.$parent.$parent.$refs.maintenanceDetails.RepastInfologGetPage(this.tableItem);
                }
            }
        },
        // 调试
        async adddebug() {
            if (dayjs(this.debugForm.Dealtime).unix() > dayjs(this.debugForm.Fixedtime).unix()) {
                this.$store.commit('SHOW_SNACKBAR', { text: '开始时间不能晚于结束时间', color: 'error' });
                return false
            }
            const paramsKey = Object.keys(this.debugForm);
            const paramsObj = this.debugForm;
            let params = {};
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            params.Equipid = this.tableItem.ID;
            const res = await CommissioningRecordsSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.showDialog = this.classcheckbox ? false : true;
                this.$parent.$parent.$refs.debuggingDetails.CommissioningRecords(this.tableItem);
            }
        },
        // 多原因；字符串拼接
        jionReson(Arr, str) {
            let strJoin = [];
            Arr.forEach(item => {
                strJoin.push(item[str]);
            });
            return strJoin.join();
        },

    }
};
</script>

<style lang="scss" scoped>
::v-deep .v-select__selections {
    input {
        position: absolute;
    }
}
</style>

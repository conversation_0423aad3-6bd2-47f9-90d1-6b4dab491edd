<template>
    <div :class="isFull ? 'device-kanban-full' : 'device-kanban-not'">
        <dv-full-screen-container>
            <div class="sbkb">
                <div class="all">
                    <div class="title">设备任务看板</div>
                    <div class="tabbox" id="mytableboxcenter">
                        <div class="tabboxrow">
                            <div class="tabboxbox" :style="{ width: item.width }" v-for="(item, index) in tablelist1" :key="index">
                                <div class="tabboxboxtitle">{{ item.title }}</div>
                                <div class="tabboxboxcenter">
                                    <el-table :data="item.data" stripe height="100%" style="width: 100%">
                                        <el-table-column
                                            v-for="(it, ind) in item.header"
                                            :key="ind"
                                            align="center"
                                            :prop="it.prop ? it.prop : it.value"
                                            :label="it.text"
                                            :width="it.width"
                                        ></el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </div>
                        <div class="tabboxrow">
                            <div class="tabboxbox" :style="{ width: item.width }" v-for="(item, index) in tablelist2" :key="index">
                                <div class="tabboxboxtitle">{{ item.title }}</div>
                                <div class="tabboxboxcenter">
                                    <el-table :data="item.data" stripe height="100%" style="width: 100%">
                                        <el-table-column
                                            v-for="(it, ind) in item.header"
                                            :key="ind"
                                            align="center"
                                            :prop="it.prop ? it.prop : it.value"
                                            :label="it.text"
                                            :width="it.width"
                                        ></el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </dv-full-screen-container>
    </div>
</template>
<script>
import { jdrwheader, byrwheader, dxrwheader, wxrwheader, jjrwheader } from '@/columns/equipmentdashboard/tableheader';
import { GetSpotCheckWoList, GetMaintainWoList, GetOverhaulWoList, GetRepairOrderList, GetMeasureCalibrateWoList } from '@/api/equipmentManagement/DeviceTasks';
import '@/views/equipmentdashboard/style.scss';
import { GetPersonList } from '@/api/equipmentManagement/Equip.js';
import $ from 'jquery';
import moment from 'moment';
export default {
    data() {
        return {
            tableId: 'SBKB_SBRW',
            Factory: '',
            isFull: false,
            tablelist1: [
                {
                    title: '点检任务看板',
                    width: '33%',
                    header: jdrwheader,
                    data: []
                },
                {
                    title: '保养任务看板',
                    width: '33%',
                    header: byrwheader,
                    data: []
                },
                {
                    title: '大修任务看板',
                    width: '33%',
                    header: dxrwheader,
                    data: []
                }
            ],
            tablelist2: [
                {
                    title: '维修任务看板',
                    width: 'calc(66% + 15px)',
                    header: wxrwheader,
                    data: []
                },
                {
                    title: '检验任务看板',
                    width: '33%',
                    header: jjrwheader,
                    data: []
                }
            ],
            SpotCheckWoStatus: [],
            DeviceByData: [],
            MaintainPlanStatus: [],
            MaintainByData: [],
            OverhaulWoStatus: [],
            RepairOrderStatus: [],
            CalibrationByData: [],
            start: null,
            end: null
        };
    },
    async mounted() {
        this.SpotCheckWoStatus = await this.$getNewDataDictionary('SpotCheckStatus');
        let DeviceBy = await GetPersonList('DeviceBy');
        this.DeviceByData = DeviceBy.response[0].ChildNodes;
        this.DeviceByData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
        this.MaintainPlanStatus = await this.$getNewDataDictionary('MaintainPlanStatus');
        let MaintainBy = await GetPersonList('MaintenanceGroup');
        this.MaintainByData = MaintainBy.response[0].ChildNodes;
        this.MaintainByData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });

        this.OverhaulWoStatus = await this.$getNewDataDictionary('OverhaulPlanStatus');
        this.RepairOrderStatus = await this.$getNewDataDictionary('RepairOrderStatus');

        this.MeasureCalibrateWoStatus = await this.$getNewDataDictionary('MeasureCalibrateWoStatus');
        let CalibrationBy = await GetPersonList('CalibrationBy');
        this.CalibrationByData = CalibrationBy.response[0].ChildNodes;
        this.CalibrationByData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.start = moment().startOf('week').format('YYYY-MM-DD');
        this.end = moment().endOf('week').format('YYYY-MM-DD');
        this.SpotCheckWoList();
        this.MaintainWoList();
        this.OverhaulWoList();
        this.RepairOrderList();
        this.MeasureCalibrateWoList();
        window.onresize = () => {
            let height = window.outerHeight;
            $('#mytableboxcenter').css('max-height', height);
        };
    },
    methods: {
        async SpotCheckWoList() {
            let params = {};
            params.Factory = this.Factory;
            params.queryStart = this.start;
            params.queryEnd = this.end;
            let res = await GetSpotCheckWoList(params);
            res.response.forEach(item => {
                this.SpotCheckWoStatus.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                this.DeviceByData.forEach(it => {
                    if (item.CheckBy == it.ItemValue) {
                        item.CheckBy = it.ItemName;
                    }
                });
            });
            this.tablelist1[0].data = res.response;
        },
        async MaintainWoList() {
            let params = {};
            params.Factory = this.Factory;
            params.queryStart = this.start;
            params.queryEnd = this.end;
            let res = await GetMaintainWoList(params);
            res.response.forEach(item => {
                this.MaintainPlanStatus.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                this.MaintainByData.forEach(it => {
                    if (item.MaintainBy == it.ItemValue) {
                        item.MaintainBy = it.ItemName;
                    }
                });
            });
            this.tablelist1[1].data = res.response;
        },
        async OverhaulWoList() {
            let params = {};
            params.Factory = this.Factory;
            params.queryStart = this.start;
            params.queryEnd = this.end;
            params.pageIndex = 1;
            params.pageSize = 1000000;
            let res = await GetOverhaulWoList(params);
            res.response.forEach(item => {
                this.OverhaulWoStatus.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                this.MaintainByData.forEach(it => {
                    if (item.TaskBy == it.ItemValue) {
                        item.TaskBy = it.ItemName;
                    }
                });
            });
            this.tablelist1[2].data = res.response.data;
        },
        async RepairOrderList() {
            let params = {};
            params.Factory = this.Factory;
            params.queryStart = this.start;
            params.queryEnd = this.end;
            let res = await GetRepairOrderList(params);
            for (let k in res.response) {
                let item = res.response[k];
                // res.response.forEach(item => {
                this.RepairOrderStatus.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                item.ReceiveBy = await this.$getPerson(item.ReceiveBy);
                // });
            }
            this.tablelist2[0].data = res.response;
        },
        async MeasureCalibrateWoList() {
            let params = {};
            params.Factory = this.Factory;
            params.queryStart = this.start;
            params.queryEnd = this.end;
            let res = await GetMeasureCalibrateWoList(params);
            res.response.forEach(item => {
                this.MeasureCalibrateWoStatus.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                let myVerifyBy = [];
                let myVerifyByValue = [];
                this.CalibrationByData.forEach(it => {
                    let VerifyByList = item.VerifyBy.split('|');
                    VerifyByList.forEach(it2 => {
                        if (it2 == it.ItemValue) {
                            myVerifyBy.push(it.ItemName);
                            myVerifyByValue.push(it.ItemValue);
                        }
                    });
                    item.myVerifyByValue = myVerifyByValue;
                    item.VerifyByName = myVerifyBy.join('|');
                });
            });
            this.tablelist2[1].data = res.response;
        }
    }
};
</script>

<style lang="scss" scoped>
.page_wrapper {
    padding: 0 !important;
}
</style>

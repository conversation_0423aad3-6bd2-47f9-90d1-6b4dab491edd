import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_30015'
const baseURL_Inventory = 'baseURL_Inventory2'

// 列表
export function getCookConfirmationList(data) {
  const api = '/api/CookConfirmationList/GetPageList'
  return getRequestResources(baseURL, api, 'post', data);
}
// 发送
export function performanceOrderConfCook(data) {
  const api = '/api/Performance/OrderCONF_Cook'
  return getRequestResources(baseURL, api, 'post', data);
}
// id获取实体详情 
export function getEntity(id) {
  const api = `/api/CookConfirmation/GetEntity/${id}`
  return getRequestResources(baseURL, api, 'get', null);
}

// 保存
export function Update(data) {
  const api = '/api/CookConfirmationList/Update'
  return getRequestResources(baseURL, api, 'post', data);
}

// 发送
export function GetSapPoRoutingInfo(data) {
  const api = '/ppm/Sappackorder/GetSapPoRoutingInfo'
  return getRequestResources(baseURL_Inventory, api, 'post', data);
}

// 保存
export function OrderCONFReverse_Cook(data) {
  const api = '/api/Performance/OrderCONFReverse_Cook'
  return getRequestResources(baseURL, api, 'post', data);
}
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM'

// 获取角色列表数据
export function getRoleList(data) {
    const api = '/api/Role/GetList'
    return getRequestResources(baseURL, api, 'post', data)
}

// 获取角色列表数据
export function saveRoleForm(data) {
    const api = '/api/Role/SaveForm'
    return getRequestResources(baseURL, api, 'post', data)
}

// 获取角色列表数据
export function deleteRole(data) {
    const api = '/api/Role/Delete'
    return getRequestResources(baseURL, api, 'post', data)
}

// 获取角色ID查询绑定用户列表
export function getUserListByRoleId(data) {
    const api = '/api/UserInfo/getUserListByRoleId'
    return getRequestResources(baseURL, api, 'post', data)
}

// 获取角色ID查询绑定用户列表
export function deleteRoleBindUser(data) {
    const api = '/api/UserRole/Delete'
    return getRequestResources(baseURL, api, 'post', data)
}

// 查询所有用户
export function getUserList(data) {
    const api = '/api/Userinfo/GetPageList'
    return getRequestResources(baseURL, api, 'post', data)
}

// 角色绑定用户
export function bindRoleAndUser(data) {
    const api = '/api/UserRole/SaveForm'
    return getRequestResources(baseURL, api, 'post', data)
}

// 角色绑定用户
export function getMenuTree(data) {
    const api = '/api/Menu/GetMenuTree'
    return getRequestResources(baseURL, api, 'post', data)
}

// 角色分配权限
export function saveRolePermission(data) {
    const api = '/api/RolePermission/SaveForm'
    return getRequestResources(baseURL, api, 'post', data)
}

// 根据角色查询绑定权限
export function getBindPermission(data) {
    const api = '/api/RolePermission/GetPermissionListByRoleId'
    return getRequestResources(baseURL, api, 'post', data)
}
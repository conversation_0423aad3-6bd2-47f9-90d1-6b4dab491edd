import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';
let DFM = 'baseURL_DFM';


// 维修工单列表
export function GetRepairOrderPageList(data) {
    const api = '/api/RepairOrder/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}
// 派工
export function GetRepairOrderAssign(data) {
    const api = '/api/RepairOrder/Assign';
    return getRequestResources(baseURL, api, 'post', data);
}
// 工单列表取消
export function GetRepairOrderCancel(data) {
    const api = '/api/RepairOrder/Cancel';
    return getRequestResources(baseURL, api, 'post', data);
}
// 维修记录
export function GetRepairOrderGetListByWoId(data) {
    const api = '/api/RepairRecord/GetListByWoId';
    return getRequestResources(baseURL, api, 'post', data);
}
// 备件
export function GetPartsHistoryDetailPageList(data) {
    const api = '/api/PartsHistoryDetail/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
// 备件删除
export function GetPartsHistoryDetailDelete(data) {
    const api = '/api/PartsHistoryDetail/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
// 备件申请
export function GetPartsHistoryDetailRequest(data) {
    const api = '/api/PartsHistoryDetail/Request';
    return getRequestResources(baseURL, api, 'post', data);
}
// 备件新增
export function GetPartsHistoryDetailSaveForm(data) {
    const api = '/api/PartsHistoryDetail/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
// 服务采购
export function GetRepairServiceGetList(data) {
    const api = '/api/RepairService/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
//工单状态
export function GetRepairOrderStatus(data) {
    const api = '/api/DataItemDetail/GetDetailTree?itemCode=RepairOrderStatus';
    return getRequestResources(DFM, api, 'post', data);
}
// 工单类型
export function GetRepairOrderType(data) {
    const api = '/api/DataItemDetail/GetDetailTree?itemCode=RepairOrderType';
    return getRequestResources(DFM, api, 'post', data);
}

// 工单来源
export function GetRepairOrderSource(data) {
    const api = '/api/DataItemDetail/GetDetailTree?itemCode=RepairOrderSource';
    return getRequestResources(DFM, api, 'post', data);
}


// 服务采购下方
export function GetRepairAcceptanceItem(data) {
    const api = '/api/RepairAcceptanceItem/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
<template>
    <!-- 处理告警 -->
    <v-dialog v-model="dialog" persistent max-width="1300px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ $t('ANDON_BJZY.jjqr') }}--{{ form.MainAlarmName }}
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="mt-6 alarm-home-pd text-show-ellipsis">
                <v-row>
                    <!-- 报警列表 -->
                    <v-col :cols="8" class="activ-style list-height alarm-bdmr activ-background" :lg="8">
                        <div class="d-flex justify-space-between mb-2 alarm-home-search">
                            <div style="width: 14%">
                                <v-menu v-model="startShow" :close-on-content-click="false" transition="scale-transition"
                                    offset-y max-width="290px" min-width="290px">
                                    <template #activator="{ on, attrs }">
                                        <v-text-field v-model="searchForm.startTime" dense label="开始时间" readonly
                                            v-bind="attrs" v-on="on" single-line class="white-bk"></v-text-field>
                                    </template>
                                    <v-date-picker v-model="searchForm.startTime" no-title
                                        @input="startShow = false"></v-date-picker>
                                </v-menu>
                            </div>
                            <div style="width: 14%">
                                <v-menu v-model="endShow" :close-on-content-click="false" transition="scale-transition"
                                    offset-y max-width="290px" min-width="290px">
                                    <template #activator="{ on, attrs }">
                                        <v-text-field v-model="searchForm.endTime" dense label="结束时间" readonly
                                            v-bind="attrs" v-on="on" single-line class="white-bk"></v-text-field>
                                    </template>
                                    <v-date-picker v-model="searchForm.endTime" no-title
                                        @input="endShow = false"></v-date-picker>
                                </v-menu>
                            </div>
                            <!-- 产线 -->
                            <div style="width: 17%">
                                <v-combobox v-model="searchForm.areaid" :items="areaList" class="white-bk"
                                    :search-input.sync="searchA" item-text="EquipmentName" :label="$t('ANDON_BJZY.areaid')"
                                    item-value="ID" persistent-hint dense single-line @change="i => selectItem(i.ID, 1)">
                                    <template #no-data>
                                        <v-list-item>
                                            <v-list-item-content>no data </v-list-item-content>
                                        </v-list-item>
                                    </template>
                                </v-combobox>
                            </div>
                            <!-- 工段 -->
                            <div style="width: 17%">
                                <v-combobox v-model="searchForm.productLineId" :items="productLineItems" class="white-bk"
                                    :search-input.sync="searchP" item-text="EquipmentName"
                                    :label="$t('ANDON_BJZY.ProductLine')" item-value="ID" persistent-hint dense single-line
                                    @change="i => selectItem(i.ID, 2)">
                                    <template #no-data>
                                        <v-list-item>
                                            <v-list-item-content> no data </v-list-item-content>
                                        </v-list-item>
                                    </template>
                                </v-combobox>
                            </div>
                            <!-- 工站 -->
                            <div style="width: 17%">
                                <v-combobox v-model="searchForm.unitId" :items="segmentItems" class="white-bk"
                                    :search-input.sync="searchS" item-text="EquipmentName" label="工站" item-value="ID"
                                    persistent-hint dense single-line @change="i => selectItem(i.ID, 3)">
                                    <template #no-data>
                                        <v-list-item>
                                            <v-list-item-content> no data </v-list-item-content>
                                        </v-list-item>
                                    </template>
                                </v-combobox>
                            </div>
                            <!-- 设备 -->
                            <div style="width: 17%">
                                <v-combobox v-model="searchForm.equipmentCode" :items="equipmentItems" class="white-bk"
                                    :search-input.sync="searchE" item-text="EquipmentName"
                                    :label="$t('ANDON_BJZY.EquipmentName')" item-value="ID" persistent-hint dense
                                    single-line>
                                    <template #no-data>
                                        <v-list-item>
                                            <v-list-item-content> no data </v-list-item-content>
                                        </v-list-item>
                                    </template>
                                </v-combobox>
                            </div>
                        </div>
                        <div class="d-flex justify-space-between mb-2">
                            <div style="width: 40%">
                                <v-text-field v-model="customizeSearch" append-icon="mdi-magnify" label="筛选"
                                    class="py-0 my-0 white-bk" single-line hide-details></v-text-field>
                            </div>
                            <div style="width: 20%;text-align: right">
                                <v-btn color="primary" @click="initData()" style="width: 5%" class="mr-5">{{
                                    $t('GLOBAL._CX') }}</v-btn>
                                <v-btn color="primary" @click="resetSearch()" style="width: 5%">{{ $t('GLOBAL._CZ')
                                }}</v-btn>
                            </div>
                        </div>
                        <Tables :click-fun="selectedPath" :current-select-id="currentSelectId" itemKey="ID"
                            :customizeSearch="customizeSearch" :showSelect="false" table-height="290px" :isSearch="false"
                            :loading="loading" :headers="alarmListColumns" :desserts="desserts" :footer="false"
                            :page-options="pageData"></Tables>
                    </v-col>
                    <!-- 报警信息 -->
                    <v-col :cols="4" :lg="4" class="activ-style list-height">
                        <v-row>                            
                            <!-- 二级分类 -->
                            <v-col :cols="12" class="activ-txt" :lg="12">
                                {{ $t('ANDON_BJZY.SubAlarmName') }}
                            </v-col>
                            <!-- 二级分类 -->
                            <v-col :cols="12" :lg="12" class="activ-height activ-background tb-border">
                                <v-text-field class="white-bk" v-model="form.SubAlarmName" disabled dense />
                            </v-col>
                            <!-- 问题等级 -->
                            <v-col :cols="12" class="activ-txt" :lg="12">
                                {{ $t('ANDON_BJZY.problemLevelName') }}
                            </v-col>
                            <!-- 问题等级 -->
                            <!-- <v-col :cols="6" :lg="6" class="activ-height activ-background">
                                            <v-text-field  class="white-bk" v-model="form.problemLevelName" disabled dense/>
                                        </v-col> -->
                            <!-- 问题等级 -->
                            <v-col :cols="12" :lg="12" class="activ-height activ-background tb-border">
                                <v-text-field class="white-bk" v-model="form.problemLevelName" disabled dense />
                            </v-col>
                            <!-- 工段 -->
                            <v-col :cols="12" class="activ-txt" :lg="12">
                                {{ $t('ANDON_BJZY.ProductLineName') }}
                            </v-col>
                            <!-- 工段 -->
                            <v-col :cols="12" class="activ-height activ-background tb-border" :lg="12">
                                <v-text-field class="white-bk" v-model="form.ProductLineName" disabled dense />
                            </v-col>
                            <!-- 设备 -->
                            <v-col :cols="12" class="activ-txt" :lg="12">
                                {{ $t('ANDON_BJZY.EquipmentName') }}
                            </v-col>
                            <!-- 设备 -->
                            <v-col :cols="12" :lg="12" class="activ-height activ-background tb-border">
                                <v-text-field class="white-bk" v-model="form.EquipmentName" disabled dense />
                            </v-col>
                            <!-- 预估完成时间 -->
                            <!-- <v-col :cols="12" class="activ-txt" :lg="12">
                                   {{ $t('ANDON_BJZY.predicttime') }}
                                </v-col> -->
                            <v-col :cols="6" :lg="6" class="activ-background activ-style alarm-bdmr" style="border: none">
                                <v-text-field class="white-bk" :label="$t('ANDON_BJZY.predicttime')"
                                    v-model="form.predicttime" dense outlined />
                            </v-col>
                            <v-col :cols="6" :lg="6" class="activ-background activ-style alarm-bdmr" style="border: none">
                                <v-btn color='primary' @click="showPreviewDialog">{{ $t('ANDON_BJZY.AlarmPic') }}</v-btn>
                            </v-col>
                        </v-row>
                    </v-col>
                    <v-col :cols="12" class="activ-style activ-txt" :lg="12">
                        {{ $t('ANDON_BJZY.AlarmContent') }}
                    </v-col>
                    <v-col :cols="8" :lg="8" class="activ-style opear-message alarm-bdmr activ-background">
                        <v-textarea v-model="form.AlarmContent" rows="4" class="white-bk" disabled dense />
                    </v-col>
                    <!-- 操作 -->
                    <v-col :cols="4" :lg="4" class="activ-style opear-message opear-btns activ-background">
                        <div class="white-bk" @click="closeForm">
                            <span class="iconfont icon-cuowu"></span>
                        </div>
                        <div class="agree-btn" @click="submitForm">
                            <span class="iconfont icon-zhengque"></span>
                        </div>
                    </v-col>
                </v-row>
            </v-card-text>
        </v-card>
        <v-dialog v-model="dialogVisible" max-width="60%">
            <v-card>
                <v-toolbar color="primary" dark="dark">
                    <v-toolbar-title>文件预览</v-toolbar-title>
                    <v-spacer></v-spacer>
                    <v-btn icon="icon" @click="dialogVisible = false">
                        <v-icon>mdi-close</v-icon>
                    </v-btn>
                </v-toolbar>
                <v-card-text>
                    <v-row :gutter="10">
                        <v-col
                            v-for="(file, index) in previewFiles"
                            :key="index"
                            cols="12"
                            sm="6"
                            md="4">
                            <v-card @click="showFullImagePreview(file)">
                                <v-img :src="file" aspect-ratio="1.75"></v-img>
                            </v-card>
                        </v-col>
                    </v-row>
                </v-card-text>
            </v-card>
        </v-dialog>
        <!-- 全屏图片预览对话框 -->
        <v-dialog
            v-model="fullImageDialogVisible"
            fullscreen="fullscreen"
            hide-overlay="hide-overlay"
            transition="dialog-bottom-transition">
            <v-card>
                <v-toolbar color="primary" dark="dark">
                    <v-toolbar-title>全屏预览</v-toolbar-title>
                    <v-spacer></v-spacer>
                    <v-btn icon="icon" @click="fullImageDialogVisible = false">
                        <v-icon>mdi-close</v-icon>
                    </v-btn>
                </v-toolbar>
                <v-card-text>
                    <v-img :src="fullImageUrl" contain="contain" height="100%"></v-img>
                </v-card-text>
            </v-card>
        </v-dialog>
    </v-dialog>
</template>

<script>
import { AlarmRecordRespond, AlarmRecordGetList } from '@/api/andonManagement/alarmHome.js';
import { GetListByAlarmId } from '@/api/andonManagement/alarmType.js';
import { alarmListColumns } from '@/columns/andonManagement/alarmHome.js';
import dayjs from 'dayjs';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => { }
        },
        productLineList: {
            type: Array,
            default: () => []
        },
        problemLevelList: {
            type: Array,
            default: () => []
        },
        areaList: {
            type: Array,
            default: () => []
        },
        equipmentList: {
            type: Array,
            default: () => []
        },
        segmentList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            customizeSearch: '',
            dialog: '',
            loading: false,
            alarmListColumns,
            currentSelectId: '',
            desserts: [],
            pageData: {
                pageSize: 99999,
            },
            pageIndex: 1,
            dataCount: 1,
            pageSize: 40,
            typeChildList: [],
            valid: true,
            searchForm: {
                areaid: '',
                productLineId: '',
                unitId: '',
                equipmentCode: '',
                startTime: '',
                endTime: ''
            },
            showImagePreview: false,
            srcList: [],
            startShow: false,
            endShow: false,
            searchP: null,
            searchA: null,
            searchS: null,
            searchE: null,
            productLineItems: [],
            segmentItems: [],
            equipmentItems: [],
            form: {
                ProductLineName: '',
                SubAlarmName: '',
                MainAlarmName: '',
                EquipmentName: '',
                AlarmContent: '',
                problemLevelName: '',
                predicttime: 30,
                AlarmPic:''
            },
            dialogVisible:false,
            fullImageDialogVisible: false,
            fullImageUrl:'',
            previewFiles: [],
            rules: {
                ProductLine: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            }
        };
    },
    watch: {
        dialog: {
            async handler(curVal) {
                this.desserts = []
                if (curVal) {

                    //监听处理
                    this.$nextTick(() => {
                        if (document.querySelector('.v-data-table__wrapper')) {
                            const selectWrap = document.querySelector('.v-data-table__wrapper');
                            selectWrap.addEventListener('scroll', this.scrollLoadMore);
                        }
                    });
                    this.initDate();
                    await this.getTypeChildList();
                    this.resetSearch();
                    this.initData();
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        selectItem(id, key) {
            switch (key) {
                case 1:
                    this.searchForm.productLineId = null;
                    this.productLineItems = this.productLineList.filter(i => i.ParentId == id);
                    this.segmentItems = [];
                    this.searchForm.unitId = null;
                    this.equipmentItems = [];
                    this.searchForm.equipmentCode = null;
                    break;

                case 2:
                    this.segmentItems = this.segmentList.filter(i => i.ParentId == id);
                    this.searchForm.unitId = null;
                    this.equipmentItems = [];
                    this.searchForm.equipmentCode = null;
                    break;

                case 3:
                    this.equipmentItems = this.equipmentList.filter(i => i.ParentId == id);
                    this.searchForm.equipmentCode = null;
                    break;

                default:
                    break;
            }
        },
        showPreviewDialog() {
            this.dialogVisible = true;
        },
        showFullImagePreview(file) {
            this.fullImageUrl = file;
            this.fullImageDialogVisible = true;
        },
        // 重置搜索栏
        resetSearch() {
            for (const key in this.searchForm) {
                if (Object.hasOwnProperty.call(this.searchForm, key)) {
                    this.searchForm[key] = '';
                }
            }
            this.initDate();
        },
        async initData() {
            this.currentSelectId = ''
            this.form.ProductLineName = ''
            this.form.SubAlarmName = ''
            this.form.AlarmContent = ''
            this.form.EquipmentName = ''
            this.form.problemLevelName = ''
            this.form.alarmPic = ''
            this.form.MainAlarmName = this.operaObj.AlarmName
            this.pageIndex = 1
            this.desserts = []
            this.getdata()
        },
        // 日期格式化
        initDate() {
            const nowDate = dayjs(new Date()).format('YYYY-MM-DD');
                this.searchForm.endTime = nowDate;
                this.searchForm.startTime = dayjs(new Date())
                    .subtract(30, 'day')
                    .format('YYYY-MM-DD');
        },
        showImage(alarmPic){
            this.showImagePreview = true;
            this.srcList = alarmPic.split(',');
        },
        /**
         * 监听滚动条
         * scrollTop为滚动条在Y轴上的滚动距离。
         * clientHeight为内容滚动区域的高度。
         * scrollHeight为内容可视区域的高度加上溢出（滚动）的距离。
        * */
        scrollLoadMore() {
            let scrollWrap = document.querySelector('.v-data-table__wrapper');
            var currentScrollTop = scrollWrap.scrollTop;
            var currentOffsetHeight = scrollWrap.scrollHeight;
            var currentClientHeight = scrollWrap.clientHeight;
            const h = currentOffsetHeight - currentScrollTop - currentClientHeight
            if (h < 1 && this.desserts.length < this.dataCount && this.desserts.length > 0) {
                //到底部了 重新请求数据
                this.pageIndex++; //页码++
                //TODO 执行加载数据方法
                this.getdata()
            }
        },
        // 获取子级
        async getTypeChildList() {
            this.typeChildList = [];
            const res = await GetListByAlarmId({ alarmId: this.operaObj.ID });
            const { success, response } = res || {};
            if (success) {
                this.typeChildList = response;
            }
        },
        async getdata() {
            if (!this.dialog) return;
            const { outerChain, ID, eventStatus, AlarmCode } = this.operaObj;
            let queryType = eventStatus
            const par = outerChain ? { ID } : eventStatus == 'ALARM' ? { eventStatus, mainAlarmType: AlarmCode } : { queryType, mainAlarmType: AlarmCode }
            const { areaid, productLineId, endTime, startTime, unitId, equipmentCode } = this.searchForm;
            const o = {
                areacode: areaid?.EquipmentCode,
                productLine: productLineId?.EquipmentCode,
                equipmentCode: equipmentCode?.EquipmentCode,
                unitcode: unitId?.EquipmentCode,
                endTime: endTime + ' 23:59:59',
                startTime: startTime + ' 00:00:00'
            }
            const res = await AlarmRecordGetList({ pageIndex: this.pageIndex, pageSize: this.pageSize, ...par, ...o });
            const { success, response } = res;
            if (success) {
                const { data, dataCount } = response
                this.dataCount = dataCount || 0;
                const arr = JSON.parse(JSON.stringify(this.desserts));
                this.desserts = [];
                data.forEach(e => {
                    const { SubAlarmType, ProductLine } = e;
                    e.SubAlarmType = this.$getDictionaryVal(SubAlarmType, this.typeChildList, 'AlarmCode', 'AlarmName');
                    e.ProductLineName = this.$getDictionaryVal(ProductLine, this.productLineList, 'EquipmentCode', 'EquipmentName');
                    arr.push(e)
                });
                this.desserts = arr;
            }
        },
        //点击表格行
        selectedPath(o) {
            const { SubAlarmType, ProductLineName, AlarmContent, EquipmentCode, ProblemLevel, AlarmPic } = o
            this.form.AlarmContent = AlarmContent
            this.form.SubAlarmName = SubAlarmType
            this.form.AlarmPic = AlarmPic
            if(this.form.AlarmPic){
                this.previewFiles = this.form.AlarmPic.split(',')
            }
            this.form.ProductLineName = ProductLineName
            const problemLevelName = this.$getDictionaryVal(ProblemLevel, this.problemLevelList, 'ItemValue', 'ItemName')
            this.form.problemLevelName = problemLevelName
            this.returnedItem = []
            this.form.EquipmentName = this.$getDictionaryVal(EquipmentCode, this.equipmentList, 'EquipmentCode', 'EquipmentName')
            this.currentSelectId = o.ID;
        },
        closeForm() {
            this.dialog = false;
        },
        // 提交
        async submitForm() {
            if (!this.currentSelectId) {
                return this.$store.commit('SHOW_SNACKBAR', { text: this.$t('ANDON_BJZY.tishi'), color: 'error' });
            }
            const res = await AlarmRecordRespond({ id: this.currentSelectId, predicttime: this.form.predicttime });
            const { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                this.$emit('handlePopup', 'refresh');
                this.closeForm()
            } else {
                this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'error' });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.activ-txt {
    line-height: 6px;
}

.activ-style {
    border: 1px solid #bdbdbd;
    border-bottom: none;
}

.tb-border {
    border-top: 1px solid #bdbdbd;
    border-bottom: 1px solid #bdbdbd;
}

.activ-height {
    height: 56px;
}

.alarm-message {
    height: 80px;
}

.opear-message {
    height: 130px;
    border-bottom: 1px solid #bdbdbd;
    margin-bottom: 10px;
}

.opear-btns {
    display: flex;
    justify-content: space-around;
    align-items: center;

    div {
        cursor: pointer;
        border: 1px solid gainsboro;
        height: 100%;
        width: 40%;
        display: flex;
        justify-content: space-around;
        align-items: center;
    }

    .agree-btn {
        background: #f2c85d;
    }
}

.white-bk {
    background: #fff;
}

.alarm-bdmr {
    border-right: none;
}

.alarm-bdr {
    border-right: 1px solid #bdbdbd;
    ;
}

// .activ-style:last-child{
//     border-bottom: 1px solid;
// }
.col-lg-6.col-12,
.col-lg-6.col-6,
.col-6,
.col-12 .col-lg-6,
.col-lg-12 {
    padding: 6x;
}

.list-height {
    height: 400px;
}
</style>
<style lang="scss">
.alarm-home-pd {
    .v-text-field.v-text-field--enclosed .v-text-field__details {
        margin-bottom: 0;
    }

    .v-text-field__details {
        display: none;
    }

    .activ-background {
        background: #f5f5f5
    }

    .iconfont {
        font-size: 80px;
    }

    // thead{
    //     display: none;
    // }
    .v-input--dense>.v-input__control>.v-input__slot {
        margin-bottom: 1px;
    }
}
</style>
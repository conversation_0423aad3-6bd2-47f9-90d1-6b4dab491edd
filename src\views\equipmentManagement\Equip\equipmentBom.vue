<template>
    <div>
        <div class="form-btn-list">
            <!-- <v-btn color="primary" @click="isfold = !isfold">{{ $t('GLOBAL.UnfoldFold') }}</v-btn> -->
            <v-btn color="primary" v-has="'SBTZGL_SBBOM_ADD'" @click="btnClickEvet('addBOM')">{{ $t('GLOBAL._XZ') }}</v-btn>
            <v-btn color="primary" v-has="'SBTZGL_SBBOM_ALLREMOVE'" :disabled="!deleteList.length" @click="btnClickEvet('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn>
        </div>
        <Tables
            :footer="false"
            :page-options="pageOptions"
            :loading="loading"
            :btn-list="btnList"
            tableHeight="calc(100vh - 220px)"
            table-name="TPM_SBGL_SBTZGL_SBBOM"
            :headers="EquipBOMColum"
            :desserts="desserts"
            @selectePages="selectePages"
            @tableClick="tableClick"
            @itemSelected="SelectedItems"
            @toggleSelectAll="SelectedItems"
        ></Tables>
        <!-- <tree-table
            ref="treeTable"
            max-height="auto"
            class="mb-4"
            style="height: calc(50vh - 135px); overflow-y: scroll"
            border
            stripe
            :is-fold="isfold"
            get-checked-prop
            :data="desserts"
            :columns="BOMTreeColum"
            :expand-type="false"
            :selection-type="false"
        >
            <template slot="actions" slot-scope="scope">
                <v-btn color="primary" v-has="'SBTZGL_SBBOM_EDIT'" text small class="mx-0 px-0" @click="tableClick(scope.row, 'editBOM')">{{ $t('GLOBAL._BJ') }}</v-btn>
                <v-btn color="red" v-has="'SBTZGL_SBBOM_DELETE'" text small class="mx-0 px-0" @click="tableClick(scope.row, 'delete')">{{ $t('GLOBAL._SC') }}</v-btn>
            </template>
        </tree-table> -->
        <createRepast ref="createRepast" :bomTreeList="desserts" :dialogType="dialogType" :tableItem="tableItem" :rowtableItem="rowtableItem"></createRepast>
    </div>
</template>
<script>
import { BomGetPageList, DeviceBomGetList, BomDelete, DeviceBomDelete } from '@/api/equipmentManagement/EquipParts.js';
import { EquipBOMColum } from '@/columns/equipmentManagement/Equip.js';

export default {
    components: {
        createRepast: () => import('./components/createRepast.vue')
    },
    props: {
        rowtableItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            isfold: true,
            loading: false,
            showFrom: false,
            papamstree: {
                key: null,
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            EquipBOMColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            MyFlag: false,
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            hasChildren: {} // 新增字典详情判断-子节点才能新增
        };
    },
    computed: {
        initHead() {
            return [];
        },
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'editBOM',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBTZGL_SBBOM_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBTZGL_SBBOM_DELETE'
                }
            ];
        }
    },
    async created() {
        // await this.RepastBOMlistTARGetPage();
    },
    methods: {
        // async getBomList() {
        //     let arr = this.equipList.filter(item => this.form.DeviceCode.includes(item.Code)).map(item => item.ID);
        //     let resp = await getBomTree({ eqid: arr.join(',') });
        //     this.bomTreeList = resp.response;
        // },
        // 设备列表查询
        async RepastBOMlistTARGetPage(itemrow, flag) {
            // this.getBomList();
            let params = {
                DeviceCategoryId: '',
                DeviceId: '',
                pageIndex: 1,
                pageSize: 1000
            };
            let res;
            this.loading = true;
            if (itemrow) {
                if (flag) {
                    this.MyFlag = true;
                    params.DeviceId = itemrow.ID;
                    res = await DeviceBomGetList(params);
                } else {
                    this.MyFlag = false;
                    params.DeviceCategoryId = itemrow.ID;
                    res = await BomGetPageList(params);
                }
            }
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = response || {} || [];
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'addBOM':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    for (let k in this.$refs.createRepast.BOMform) {
                        this.$refs.createRepast.BOMform[k] = '';
                    }
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'editBOM':
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除
        deltable() {
            let params = [];
            console.log(this.tableItem);
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res;
                    if (this.MyFlag) {
                        res = await DeviceBomDelete(params);
                    } else {
                        res = await BomDelete(params);
                    }
                    if (res.success) {
                        this.tableItem = {};
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.RepastBOMlistTARGetPage(this.rowtableItem, this.MyFlag);
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + item);
            this.deleteList = item;
            console.log(this.deleteList, 123);
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastBOMlistTARGetPage(this.rowtableItem, this.MyFlag);
        }
    }
};
</script>
<style lang="scss" scoped></style>

<template>
  <v-dialog
    style="z-index: 2;"
    v-model="showDialog"
    max-width="1080px"
  >
    <v-card>
      <v-card-title
        class="headline primary lighten-3"
        primary-title
      >
        {{ jtitle }}-详细信息
      </v-card-title>
      <v-form ref="form">
        <v-row class="ma-4">
          <el-col :span="10">
            <el-date-picker
              v-model="withdrawTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              :append-to-body="false"
            >
            </el-date-picker>
          </el-col>
          <el-col span="1">
            <v-btn
              style="margin-top: 5px;"
              color="primary"
              @click="search()"
            >{{ $t('GLOBAL._CX') }}</v-btn>
          </el-col>

        </v-row>
      </v-form>
      <el-table
        v-if="isSql == '1'"
        :data="tableData1"
        border
        :show-overflow-tooltip="true"
      >
        <el-table-column
          v-for="(header, index) in Object.keys(tableData1[0])"
          :key="index"
          :prop="header"
          :label="header"
          header-align="center"
          align="center"
          show-overflow-tooltip
        >
        </el-table-column>
      </el-table>
      <v-card-text
        style="padding: 20px;"
        v-if="isSql == '0'"
      >
        <v-stepper v-model="e1">
          <v-stepper-header style="justify-content: flex-start;height:none">
            <v-stepper-step
              :complete="e1 > 1"
              step="1"
              style="padding: 12px;"
            >
              分析数据
            </v-stepper-step>
            <v-stepper-step
              :complete="e1 > 2"
              step="2"
              style="padding: 12px;"
            >
              原始数据
            </v-stepper-step>
          </v-stepper-header>
          <v-stepper-items style="height: 480px;overflow: auto;">
            <v-stepper-content step="1">
              <!-- <div v-show="exhibitionType == '图表'">
                <div id="tcChart1"></div>
              </div>
              <div v-if="exhibitionType == '表格'">
               
              </div> -->
              <el-table
                :data="dataTagDat"
                border
                style="width: 100%"
                :header-cell-style="{background:'#fafafa',textAlign: 'center'}"
                :row-style="{height: '35px'}"
                show-overflow-tooltip
                :tooltip-effect="'dark'"
              >
                <el-table-column
                  align="center"
                  prop="Factory"
                  label="工厂"
                  min-width="90"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="ProductionLine"
                  label="车间"
                  min-width="90"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="Process"
                  label="产线"
                  min-width="90"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="Specification"
                  label="规格"
                  min-width="90"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="Unit"
                  label="单位"
                  min-width="70"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="DataTime"
                  label="数据时间"
                  min-width="120"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="DataValue"
                  label="实绩值"
                  min-width="70"
                  show-overflow-tooltip
                >
                </el-table-column>
              </el-table>
            </v-stepper-content>
            <v-stepper-content step="2">
              <!-- <div v-show="exhibitionType == '图表'">
                <div id="tcChart1"></div>
              </div>
              <div v-if="exhibitionType == '表格'">
               
              </div> -->
              <el-table
                v-if="isSql =='0'"
                :data="dataKpiDat"
                border
                style="width: 100%"
                :header-cell-style="{background:'#fafafa',textAlign: 'center'}"
                :row-style="{height: '35px'}"
              >
                <el-table-column
                  align="center"
                  prop="Factory"
                  label="工厂"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="ProductionLine"
                  label="车间"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="Process"
                  label="产线"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="Process"
                  label="工序"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="Team"
                  label="班组"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="Shift"
                  label="班次"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="KpiName"
                  label="KPI名称"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="TimeDimension"
                  label="时间维度"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="Unit"
                  label="单位"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="DataTime"
                  label="数据时间"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="DataValue"
                  label="实绩值"
                  show-overflow-tooltip
                >
                </el-table-column>
              </el-table>
            </v-stepper-content>
          </v-stepper-items>
        </v-stepper>

      </v-card-text>
      <v-card-actions style="justify-content: flex-end;">
        <v-btn
          v-if="isSql == '0'"
          color="primary"
          @click="change_back"
        >上一步</v-btn>
        <v-btn
          v-if="isSql == '0'"
          color="primary"
          @click="change_nextStep"
        >下一步</v-btn>
        <v-btn
          color="normal"
          @click="showDialog = false"
        >{{ $t('GLOBAL._GB') }}</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { getTablgetDodowneList, getDodown, getTableList } from '@/api/simConfig/simconfignew.js';

export default {
  props: {
    exhibitionType: {
      type: String,
      default: ''
    },
    jtitle: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    Order: {
      type: String,
      default: ''
    },
    isSql: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    barName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dataTagDat: [],
      dataKpiDat: [],
      e1: 1,
      withdrawTime: [],
      tableData1: [{}],
      tcChart: null,
      showDialog: false,
      columns: [],
      startTime: '',
      endTime: '',
    };
  },
  created() {
    this.tableData1 = [{}]
    this.tableData = []
    this.dataTagDat = []
    this.dataKpiDat = []
  },
  mounted() {
    this.tableData1 = [{}]
    this.tableData = []
    this.dataTagDat = []
    this.dataKpiDat = []
  },
  methods: {
    flagChange() {
      this.tableData1 = [{}]
      this.tableData = []
      this.dataTagDat = []
      this.dataKpiDat = []
    },
    change_back() {
      if (this.e1 > 1 && this.e1 <= 2) --this.e1
    },
    change_nextStep() {
      if (this.e1 <= 1) this.e1++
    },
    search() {
      if (this.isSql == '0') {
        this.searchKpi()
      }
      if (this.isSql == '1') {
        this.searchSql()
      }

    },
    async searchKpi() {
      // 时间清空后的处理
      if (this.withdrawTime && this.withdrawTime.length > 0) {
        this.startTime = this.withdrawTime[0]
        this.endTime = this.withdrawTime[1]
      } else {
        this.startTime = ''
        this.endTime = ''
      }
      const res = await getDodown(this.Order, this.simlevel, this.barName);
      if (res.success) {
        this.dataTagDat = res.response.dataTag
        this.dataKpiDat = res.response.dataKpi
      }
    },
    async searchSql() {
      // 时间清空后的处理
      if (this.withdrawTime && this.withdrawTime.length > 0) {
        this.startTime = this.withdrawTime[0]
        this.endTime = this.withdrawTime[1]
      } else {
        this.startTime = ''
        this.endTime = ''
      }
      let params =
      {
        "simLevel": "SIM1",
        "position": [
          this.Order
        ],
        "paramList": [
          this.simLevel,
          this.BaseTime
        ]
      }

      let res = await getTableList(params)
      if (res.success && res.response != null) {
        this.tableData1 = res.response[0].positionResult
      }
    },
    query() {
      this.tcChart = this.$echarts.init(document.getElementById('tcChart1'));
      this.$nextTick(() => {
        var option
        option = {
          title: {
            text: this.title,
            textStyle: { // 标题样式
              color: '#000000'
            }
          },
          tooltip: {
            trigger: "item"
          },
          legend: {
            data: [
              {
                name: "目标值",
                textStyle: {
                  color: "#000000"
                }
              },
              {
                name: "实际值",
                textStyle: {
                  color: "#000000"
                }
              },
            ]
          },
          grid: {
            top: '15%',
            bottom: '1%',
            right: '2%',
            left: '5%',
            containLabel: true
          },
          toolbox: {
            show: true,
          },
          calculable: true,
          xAxis: [
            {
              type: 'category',
              axisLine: {
                lineStyle: {
                  color: "#000000"
                }
              },
              splitLine: {
                show: false
              },
              data: ['10.10', '10.11', '10.12'],//['10.10', '10.11', '10.12', '10.13', '10.14', '10.15'],
              // data: this.nianData.map(item => item.MONTH),
              axisLabel: {
                show: true,
                textStyle: {
                  color: "#000000" //X轴文字颜色
                },
              },
            }
          ],
          yAxis: [
            {
              type: 'value',
              splitLine: {
                show: false
              },
              axisLine: {
                lineStyle: {
                  color: "#000000"
                }
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: "#000000" //X轴文字颜色
                },
              },
            },
            // {
            //   //右边百分比部分
            //   name: '百分比',
            //   type: "value",
            //   position: "right",
            //   axisLine: {
            //     lineStyle: {
            //       color: "#000000"
            //     }
            //   },
            //   axisTick: {
            //     show: false,
            //   },

            //   axisLabel: {
            //     textStyle: {
            //       color: "#000000",
            //     },
            //     show: true,
            //     interval: "auto",
            //     formatter: "{value}%",
            //   },
            //   show: true,
            //   splitLine: {  //网格线
            //     show: false
            //   }
            // }
          ],
          series: [

            {
              name: '目标值',
              tooltip: {
                show: false
              },
              type: 'bar',
              barWidth: 10,
              itemStyle: {
                normal: {
                  barBorderRadius: [4, 4, 4, 4],
                  color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: "#6B74E4" // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: "#6B74E4" // 100% 处的颜色
                  }], false)
                }
              },
              // data: this.nianData.map(item => item.PLANVALUE),
              data: [10, 20, 30, 40, 50, 60],
              barGap: '30%'

            },
            {
              name: '实际值',
              tooltip: {
                show: false
              },
              type: 'bar',
              barWidth: 10,
              itemStyle: {
                normal: {
                  barBorderRadius: [4, 4, 4, 4],
                  color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: "#4391F4" // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: "#4391F4" // 100% 处的颜色
                  }], false)
                }
              },
              // data: this.nianData.map(item => item.OUTNUM),
              data: [5, 20, 10, 18, 20, 20],
              barGap: '30%'
            },
          ]
        };
        this.tcChart.setOption(option, true);
        window.addEventListener("resize", () => {
          this.tcChart.resize()
        }, false);
      })
    },
  }


};
</script>
<style lang="less" scoped>
.el-table__body-wrapper .cell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.el-table__body-wrapper .cell:hover {
    overflow: visible;
    white-space: normal;
}
#tcChart1 {
    width: 100%;
    height: 400px;
    padding: 20px;
    box-sizing: border-box;
}
.chart .type-list {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    // height: 120px;
    margin-bottom: 10px;

    // border: 1px solid red;
    .type-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        cursor: pointer;
        padding: 0 10px;
        // height: 120px;
        width: 18%;
        border: 1px solid #ccc;

        &.active {
            border: 1px solid #007aff;

            .type-title {
                color: #007aff;
            }
        }

        .type-title {
            font-weight: 600;
            color: #151515;
            line-height: 30px;
            margin: 0;
            text-align: center;
        }

        img {
            // width: 100px;
            width: 80%;
            height: 100px;
        }
    }
}

.my-title {
    font-size: 14px;

    &-1 {
        display: block;
        margin-bottom: 0.5em;
    }

    &-2 {
        display: block;
        transform: translateX(-12px);
    }
}

.v-input--selection-controls {
    margin: 0;
}

.color-line {
    display: flex;
    align-items: baseline;
}

.time-list {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
}

.color-legend {
    width: 15px;
    height: 15px;
    margin-right: 5px;
    background: #ccc;
}

.yaxis-line {
    margin: 0;
}

.radio-box {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
}

.v-stepper__header {
    height: auto;
}
.v-card /deep/ .v-application .primary.lighten-3 {
    background-color: transparent !important;
}
</style>
<template>
    <div class="opc-page">
        <a-tabs type="card" size="small" v-model="activeKey">
            <a-tab-pane v-for="tab in tabList" :key="tab" :tab="tab">
                <TagAddress v-if="tab == 'Tag Address'" />
                <FunctionInstances v-if="tab == 'Function Instances'" />
                <Functions v-if="tab == 'Functions'" />
            </a-tab-pane>
        </a-tabs>
    </div>
</template>

<script>
import TagAddress from './components/TagAddress.vue'
import FunctionInstances from './components/FunctionInstances.vue'
import Functions from './components/Functions.vue'
export default {
    components: {
        TagAddress,
        FunctionInstances,
        Functions
    },
    data() {
        return {
            tabList: ['Tag Address', 'Function Instances', 'Functions'],
            activeKey: 'Tag Address'
        }
    },
    created() { },
    methods: {}
}
</script>

<style lang="scss" scoped>
.ant-tabs.ant-tabs-small.ant-tabs-card {
    overflow: inherit;
}

::v-deep .ant-tabs-bar {
    margin-bottom: 10px !important;
}
</style>
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';

export function GetOverhaulPlanPageList(data) {
    const api = '/api/OverhaulPlan/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetOverhaulPlanDelete(data) {
    const api = '/api/OverhaulPlan/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetOverhaulPlanSaveForm(data) {
    const api = '/api/OverhaulPlan/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetOverhaulPlanImportData(data, key) {
    const api = `/api/OverhaulPlan/ImportData?factory=${key}`;
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetOverhaulWoGetList(data) {
    const api = '/api/OverhaulWo/GetListByPlanId';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetOverhaulWoDelete(data) {
    const api = '/api/OverhaulWo/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetOverhaulWoSaveForm(data) {
    const api = '/api/OverhaulWo/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetEquipmentListByLevel(data) {
    const api = '/api/Equipment/GetListByLevel';
    return getRequestResources(baseURL, api, 'post', data);
}
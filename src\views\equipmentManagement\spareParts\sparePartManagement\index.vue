<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <!-- <v-card class="ma-1"> -->
            <div class="form-btn-list">
                <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                    <v-icon>{{ 'mdi-table-search' }}</v-icon>
                    {{ $t('GLOBAL._SSL') }}
                </v-btn>
                <v-btn icon color="primary" @click="RepastInfoGetPage">
                    <v-icon>mdi-cached</v-icon>
                </v-btn>
                <v-btn color="primary" v-has="'SBBJGL_BJCKSZ_ADD'" @click="btnClickEvet('add')">{{ $t('GLOBAL._XZ') }}</v-btn>
                <v-btn color="primary" v-has="'SBBJGL_BJCKSZ_ALLREMOVE'" :disabled="!deleteList.length" @click="btnClickEvet('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn>
                <v-btn color="primary"  v-has="'SBBJGL_BJCKSZ_ADD'">{{ $t('GLOBAL._SAPIMPORT') }}</v-btn>
                 <!-- v-has="'SBBJGL_BJCKSZ_SAPIMPORT'" -->
                <v-btn color="primary"  v-has="'SBBJGL_BJCKSZ_ADD'">{{ $t('GLOBAL._EXPORT') }}</v-btn>
                <!-- v-has="'SBBJGL_BJCKSZ_EXPORT'" -->
            </div>
            <Tables
                :page-options="pageOptions"
                :loading="loading"
                :btn-list="btnList"
                :tableHeight="showFrom ? 'calc(100vh - 285px)' : 'calc(100vh - 210px)'"
                table-name="TPM_SBGL_SBBJGL_CKSZ"
                :headers="sparePartManagementColum"
                :desserts="desserts"
                :clickFun="clickFun"
                @selectePages="selectePages"
                @tableClick="tableClick"
                @itemSelected="SelectedItems"
                @toggleSelectAll="SelectedItems"
            ></Tables>
            <createRepast ref="createRepast" :dialogType="dialogType" :tableItem="tableItem"></createRepast>
        </div>
    </div>
</template>
<script>
import { WarehouseManageGetPageList, WarehouseManageDelete } from '@/api/equipmentManagement/sparePart.js';
import { sparePartManagementColum } from '@/columns/equipmentManagement/sparePart.js';

export default {
    name: 'RepastModel',
    components: {
        createRepast: () => import('./components/createRepast.vue')
    },
    data() {
        return {
            equipmentSpareType: [],
            loading: true,
            showFrom: false,
            papamstree: {
                key: null,
                code: '',
                name: '',
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            sparePartManagementColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            rowtableItem: {},
            deleteList: [], //批量选中
            hasChildren: {} // 新增字典详情判断-子节点才能新增
        };
    },
    computed: {
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'code',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Code'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Code')
                },
                {
                    value: '',
                    key: 'name',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Name'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CKSZ.Name')
                },
                {
                    value: '',
                    key: 'type',
                    icon: 'mdi-account-check',
                    type: 'combobox',
                    selectData: this.$changeSelectItems(this.equipmentSpareType, 'ItemName', 'ItemName'),
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.SType'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL.SType')
                }
                // {
                //     value: '',
                //     key: 'ItemName',
                //     icon: 'mdi-account-check',
                //     label: '状态',
                //     placeholder: '状态'
                // }
            ];
        },
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBBJGL_BJCKSZ_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBBJGL_BJCKSZ_DELETE'
                }
            ];
        }
    },
    mounted() {
        this.RepastInfoGetPage();
        this.GetequipmentSpareType();
    },
    methods: {
        // 获取备件分类
        async GetequipmentSpareType() {
            const res = await this.$getDataDictionary('SpareType');
            this.equipmentSpareType = res || [];
        },
        // 查询数据
        searchForm(value) {
            this.papamstree.pageIndex = 1;
            this.papamstree.code = value.code;
            this.papamstree.name = value.name;
            this.RepastInfoGetPage();
        },
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                // key: '',
                // part: '',
                code: this.papamstree.code,
                name: this.papamstree.name,
                pageIndex: this.papamstree.pageIndex,
                pageSize: this.papamstree.pageSize
            };
            this.loading = true;
            const res = await WarehouseManageGetPageList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
                this.rowtableItem = this.desserts[0] || {};
                this.RepastInfoLogGetPage();
            }
        },
        clickFun(data) {
            console.log(data);
            this.rowtableItem = data || {};
            this.RepastInfoLogGetPage();
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await WarehouseManageDelete(params);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.RepastInfoGetPage();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}
</style>

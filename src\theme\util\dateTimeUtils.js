"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.weekNumber = weekNumber;
exports.isLeapYear = isLeapYear;

function createUTCDate(year) {
  var month = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
  var day = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;
  var date;

  if (year < 100 && year >= 0) {
    date = new Date(Date.UTC(year, month, day));

    if (isFinite(date.getUTCFullYear())) {
      date.setUTCFullYear(year);
    }
  } else {
    date = new Date(Date.UTC(year, month, day));
  }

  return date;
}

function firstWeekOffset(year, firstDayOfWeek, firstDayOfYear) {
  var firstWeekDayInFirstWeek = 7 + firstDayOfWeek - firstDayOfYear;
  var firstWeekDayOfYear = (7 + createUTCDate(year, 0, firstWeekDayInFirstWeek).getUTCDay() - firstDayOfWeek) % 7;
  return -firstWeekDayOfYear + firstWeekDayInFirstWeek - 1;
}

function dayOfYear(year, month, day, firstDayOfWeek) {
  var dayOfYear = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334][month];

  if (month > 1 && isLeapYear(year)) {
    dayOfYear++;
  }

  return dayOfYear + day;
}

function weeksInYear(year, firstDayOfWeek, firstDayOfYear) {
  var weekOffset = firstWeekOffset(year, firstDayOfWeek, firstDayOfYear);
  var weekOffsetNext = firstWeekOffset(year + 1, firstDayOfWeek, firstDayOfYear);
  var daysInYear = isLeapYear(year) ? 366 : 365;
  return (daysInYear - weekOffset + weekOffsetNext) / 7;
}

function weekNumber(year, month, day, firstDayOfWeek, localeFirstDayOfYear) {
  var weekOffset = firstWeekOffset(year, firstDayOfWeek, localeFirstDayOfYear);
  var week = Math.ceil((dayOfYear(year, month, day, firstDayOfWeek) - weekOffset) / 7);

  if (week < 1) {
    return week + weeksInYear(year - 1, firstDayOfWeek, localeFirstDayOfYear);
  } else if (week > weeksInYear(year, firstDayOfWeek, localeFirstDayOfYear)) {
    return week - weeksInYear(year, firstDayOfWeek, localeFirstDayOfYear);
  } else {
    return week;
  }
}

function isLeapYear(year) {
  return year % 4 === 0 && year % 100 !== 0 || year % 400 === 0;
}
//# sourceMappingURL=dateTimeUtils.js.map
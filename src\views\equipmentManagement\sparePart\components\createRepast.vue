<template>
    <v-dialog v-model="showDialog" max-width="960px">
        <v-card class="" v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                新增
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Code" outlined dense :label="$t('SparePart.Code') + '*'"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Name" outlined dense :label="$t('SparePart.Name') + '*'"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="form.Type" :items="typecodelist" item-text="ItemName" item-value="ItemName" :label="$t('SparePart.PartType') + '*'" clearable dense outlined></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Model" outlined dense :label="$t('SparePart.Model')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-autocomplete
                                clearable
                                v-model="form.SubjectValue"
                                :items="SubjectList"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="$t('SparePart.Subject') + '*'"
                                clear
                                dense
                                outlined
                            ></v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.LowerBound" outlined dense :label="$t('SparePart.LowerBound') + '*'"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.UpperBound" outlined dense :label="$t('SparePart.UpperBound')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.ServiceLife" outlined dense :label="$t('SparePart.ServiceLife')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.PurchaseCycle" outlined dense :label="$t('SparePart.PurchaseCycle') + '*'"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.MinPurchaseQty" outlined dense :label="$t('SparePart.MinPurchaseQty') + '*'"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="form.Unit" :items="PartUnit" item-text="ItemName" item-value="ItemName" :label="$t('SparePart.Unit') + '*'" clearable dense outlined></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="form.Remark" rows="2" outlined dense :label="$t('SparePart.Remark')"></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'edit'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                修改
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Code" outlined dense :label="$t('SparePart.Code') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Name" outlined dense :label="$t('SparePart.Name') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select v-model="editedItem.Type" :items="typecodelist" item-text="ItemName" item-value="ItemName" :label="$t('SparePart.Type') + '*'" clearable dense outlined></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Model" outlined dense :label="$t('SparePart.Model')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-autocomplete
                            clearable
                            v-model="editedItem.SubjectValue"
                            :items="SubjectList"
                            item-text="ItemName"
                            item-value="ItemValue"
                            :label="$t('SparePart.Subject') + '*'"
                            clear
                            dense
                            outlined
                        ></v-autocomplete>
                    </v-col>

                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.LowerBound" outlined dense :label="$t('SparePart.LowerBound') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.UpperBound" outlined dense :label="$t('SparePart.UpperBound')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.ServiceLife" outlined dense :label="$t('SparePart.ServiceLife')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.PurchaseCycle" outlined dense :label="$t('SparePart.PurchaseCycle') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select v-model="editedItem.Unit" :items="PartUnit" item-text="ItemName" item-value="ItemName" :label="$t('SparePart.Unit') + '*'" clearable dense outlined></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.MinPurchaseQty" outlined dense :label="$t('SparePart.MinPurchaseQty') + '*'"></v-text-field>
                    </v-col>

                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea v-model="editedItem.Remark" rows="2" outlined dense :label="$t('SparePart.Remark')"></v-textarea>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { GetPartSaveForm, GetPartType } from '@/api/equipmentManagement/Parts.js';
import { GetUnitList } from '@/api/Inventory/Inventory.js';
import { Message, MessageBox } from 'element-ui';

export default {
    props: {
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        },
        PartUnit: {
            type: Array,
            default: () => []
        },
        SubjectList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            QRcode: '',
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            equipmentSpareType: [],
            UnitList: [],
            typecodelist: [],
            form: {
                Code: '',
                Name: '',
                Type: '',
                Model: '',
                Subject:"",
                SubjectValue: '',
                LowerBound: 0,
                UpperBound: 0,
                ServiceLife: '',
                Unit: '',
                PurchaseCycle: 0,
                MinPurchaseQty: 0,
                Remark: ''
            }
        };
    },
    computed: {
        editedItem() {
            const { Code, Name, Type, Model, SubjectValue,Subject, LowerBound, UpperBound, MinPurchaseQty, ServiceLife, PurchaseCycle, Unit, Remark } = this.tableItem;
            return {
                Code,
                Name,
                Type,
                Model,
                Subject,
                SubjectValue,
                LowerBound,
                UpperBound,
                MinPurchaseQty,
                ServiceLife,
                PurchaseCycle,
                Unit,
                Remark
            };
        }
    },
    // async created() {
    //     // this.PartType = await this.$getNewDataDictionary('PartType');
    //     this.PartUnit = await this.$getNewDataDictionary('PartUnit');
    // },
    methods: {
        closeEquip() {
            this.showDialog = false;
            this.$refs.form.reset();
        },
        async addSave(type) {
            const paramsKey = Object.keys(this.form);
            const paramsObj = type == 'add' ? this.form : this.editedItem;
            paramsObj.Subject = paramsObj.SubjectValue
            let params = {};
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            if (params.Type == null) {
                params.Type = '';
            }
            if (params.Remark == null) {
                params.Remark = '';
            }
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            if (type == 'edit') {
                console.log(this.editedItem)
                if (
                    this.editedItem.PurchaseCycle == '' ||
                    this.editedItem.MinPurchaseQty == '' ||
                    this.editedItem.SubjectValue == '' ||
                    this.editedItem.SubjectValue == null ||
                    this.editedItem.Code == '' ||
                    this.editedItem.Name == '' ||
                    this.editedItem.PartType == '' ||
                    this.editedItem.LowerBound == '' ||
                    this.editedItem.Unit == ''
                ) {
                    Message({
                        message: `${this.$t('Inventory.ToOver')}`,
                        type: 'error'
                    });
                    return;
                }
                params.ID = this.tableItem.ID;
            } else {
                if (
                    this.form.PurchaseCycle == '' ||
                    this.form.MinPurchaseQty == '' ||
                    this.form.Code == '' ||
                    this.form.Name == '' ||
                    this.form.SubjectValue == '' ||
                    this.form.PartType == '' ||
                    this.form.LowerBound == '' ||
                    this.form.Unit == ''
                ) {
                    Message({
                        message: `${this.$t('Inventory.ToOver')}`,
                        type: 'error'
                    });
                    return;
                }
            }
            const res = await GetPartSaveForm(params);
            let { success, msg } = res;
            if (success) {
                for (let k in this.form) {
                    this.form[k] = '';
                }
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.$parent.RepastInfoGetPage();
                this.showDialog = this.classcheckbox ? false : true;
            }
        }
    }
};
</script>

<template>
  <div>
    <el-drawer class="drawer" :visible.sync="drawer" :direction="'rtl'" :before-close="handleClose"
               :append-to-body="false" size="50%">
      <div slot="title" class="title-box">
        <span>{{ drawerRow.ConsoleGroup }}</span>
      </div>
      <div class="InventorySearchBox">
        <div class="searchbox pd5">
          <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
            <!--            <el-form-item label="line">-->
            <!--              <el-select style="width: 100%" filterable v-model="searchForm.line" placeholder="">-->
            <!--                <el-option v-for="(item, index) in EquipmentLineList" :key="index" :label="item.EquipmentName" :value="item.EquipmentCode">-->
            <!--                </el-option>-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
            <el-form-item :label="$t('GLOBAL._SSL')">
              <el-input clearable v-model="searchForm.Key"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-search" @click="getTableData">{{ $t('GLOBAL._CX') }}</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="success" icon="el-icon-check" @click="saveSort">{{ $t('GLOBAL._BC') }}</el-button>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="success" icon="el-icon-circle-plus-outline" @click="showDialog({})">{{
                  $t('GLOBAL._XZ')
                }}
              </el-button>
            </el-form-item>
            <!--            <el-form-item>-->
            <!--              <el-button size="small" type="success" icon="el-icon-circle-check">{{-->
            <!--                  $t('GLOBAL._BC')-->
            <!--                }}-->
            <!--              </el-button>-->
            <!--            </el-form-item>-->
          </el-form>
        </div>
      </div>
      <div class="table-box">
        <el-table ref="sortTable" v-loading="loading" row-key="ID" :data="tableData" element-loading-text="拼命加载中"
                  element-loading-spinner="el-icon-loading" style="width: 100%" height="83vh">
          <el-table-column v-for="(item, index) in tableHead" :key="index" :prop="item.field" :label="item.label">
            <template slot-scope="scope">
              <span v-if="['PercentQuantity', 'AdjustPercentQuantity'].includes(item.field)">
                {{ scope.row[item.field] ? `${scope.row[item.field]}` : '-' }}
              </span>
              <span v-else> {{ scope.row[item.field] }} </span>
            </template>
          </el-table-column>
          <el-table-column prop="operation" width="160" :label="$t('GLOBAL._ACTIONS')" align="center">
            <template slot-scope="scope">
              <!--              <el-button size="mini" type="text" @click="showDialog(scope.row)">{{ $t('GLOBAL._BJ') }}</el-button>-->
              <el-button size="mini" type="text" @click="delRow(scope.row)">{{ $t('GLOBAL._SC') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination class="mt-8p" background :current-page="searchForm.pageIndex" :page-size="searchForm.pageSize"
                       layout="->, total, prev, pager, next" :total="total" @current-change="handleCurrentChange"/>
      </div>
    </el-drawer>
    <DrawerDialog @saveForm="getTableData" ref="DrawerDialog"/>
  </div>
</template>

<script>
import Sortable from 'sortablejs';
import {
  delEquipmentGroupEquip,
  getEquipmentGroupEquipList,
  saveEquipmentGroupEquipSort,
  getEquipmentLineList
} from "@/api/systemManagement/labelPrint";
import DrawerDialog from './drawer-dialog.vue'

export default {
  name: 'drawer',
  components: {
    DrawerDialog
  },
  data() {
    return {
      searchForm: {
        Key: '',
        pageIndex: 1,
        pageSize: 10,
      },
      drawer: false,
      tableData: [],
      tableHead: [],
      loading: false,
      hansObjDrawer: this.$t('ConsoleGroup.EquipmentGroupEquip'),
      drawerRow: {},
      total: 0,
      EquipmentLineList: []
    }
  },
  mounted() {
    // document.body.ondrop = function (event) {
    //   event.preventDefault();
    //   event.stopPropagation();
    // };
  },
  methods: {
    show(val) {
      this.drawerRow = val
      this.searchForm.EquipmentGroupRowId = val.ID
      this.drawer = true
      this.initTableHead()
      this.$nextTick(() => {
        this.getTableData()
      })
    },
    handleClose() {
      this.drawer = false
    },
    getTableData() {
      this.tableData = []
      getEquipmentGroupEquipList(this.searchForm).then(res => {
        this.tableData = res.response.data
        this.total = res.response.dataCount
        this.sortDrag()

      })
    },
    saveSort() {
      if(this.tableData.length){
        const ids = this.tableData.map(e=>e.ID)
        console.log(ids)
        console.log(this.tableData)
        const {msg} =  saveEquipmentGroupEquipSort(ids)
        this.$message.success(msg)
        this.getTableData()
      }
    },
    delRow({ID}) {
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        const {msg} = await delEquipmentGroupEquip([ID])
        this.$message.success(msg)
        this.getTableData()
      }).catch(err => {
        console.log(err);
      });
    },
    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },
    initTableHead() {
      this.tableHead = []
      for (let key in this.hansObjDrawer) {
        this.tableHead.push({field: key, label: this.hansObjDrawer[key]})
      }
    },
    showDialog(row) {
      this.$refs['DrawerDialog'].show({ID: this.searchForm.EquipmentGroupRowId})
    },
    sortDrag() {
      this.$nextTick(() => {
        // 通过ref获取Dom节点
        const el = this.$refs.sortTable.$el.querySelectorAll(
            ".el-table__body-wrapper > table > tbody"
        )[0];
        this.sortable = Sortable.create(el, {
          animation: 600, //拖拽动画(毫秒)
          setData: function (dataTransfer) {
            dataTransfer.setData("Text", "");
          },
          // 结束拖拽
          onEnd: (e) => {
            //e.oldIndex为拖动一行原来的位置；e.newIndex为拖动后新的位置
            const oldRow = this.tableData[e.oldIndex]; // 移动元素集合
            const newRow = this.tableData[e.newIndex]; // 新的元素集合
            this.tableData[e.oldIndex] = newRow
            this.tableData[e.newIndex] = oldRow
            // 调用接口传参即可
          },
        })
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer {
  :deep(.el-drawer__body) {
    padding-top: 10px;
    background-color: #FFFFFF;
    overflow-y: hidden
  }

  :deep(.el-form--inline) {
    height: 32px;
  }

  .title-box {
    font-size: 18px;
    color: #909399;
  }

  .pd5 {
    padding: 5px;
  }

  .table-box {
    padding: 0 10px;

    i {
      margin-right: 5px;
      font-size: 15px !important;
      color: #67c23a;
    }
  }
}
</style>

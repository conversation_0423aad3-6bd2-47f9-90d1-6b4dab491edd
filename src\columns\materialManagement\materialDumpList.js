// dictionary: true, isEditCell: true
export const materialDumpList = [
    { text: '序号', value: 'Index', width: '100px' },
    { text: 'WMS发料仓库', value: 'ReceivedWarehouseName', width: '180px' },
    { text: 'WMS领料仓库', value: 'WarehousePositionName', width: '140px' },
    { text: 'WMS领料仓库编码', value: 'WarehousePositionCode', width: '160px' },
    { text: 'WMS订单号', value: 'Orderid', width: '146px',},
    { text: '转储单流水', value: 'SN', width: '180px',},
    { text: 'WMS需求日期', value: 'DemandDate', width: '160px' },
    { text: 'WMS发料日期', value: 'ReceivedDate', width: '160px' },
    { text: 'WMS订单类型', value: 'OrderType', width: '160px' },
    { text: 'WMS批次号', value: 'WavesId', width: '146px',},
    { text: 'WMS预留号码', value: 'ReserveId', width: '160px' },
    { text: 'WMS发料仓库编码', value: 'ReceivedWarehouseCode', width: '180px' },
    { text: 'WMS创建日期', value: 'CreateDate', width: '160px' },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建时间', value: 'CreateDate', width: '160px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '160px'
    }
];
// , semicolonFormat: true
export const materialDumpDetail = [
    // { text: '序号', value: 'Index', width: '100px' },
    { text: '物料', value: 'MaterialCode', width: '160px' },
    { text: '物料描述', value: 'MaterialDescribe', width: '160px' },
    { text: '单位', value: 'Unit', width: '120px' },
    { text: '配送数量', value: 'Plan_Num', width: '140px', semicolonFormat: true },
    // { text: '实发数量', value: 'ActualNum', width: '140px', semicolonFormat: true},
    { text: '领用批次', value: 'Batchcode', width: '140px' },
    // { text: '货架', value: 'Shelves', width: '140px' },
    // { text: '批次剩余量', value: 'BatchcodeSurplusnum', width: '160px', semicolonFormat: true },
    // { text: '仓库总库存', value: 'Allnum', width: '140px', semicolonFormat: true},
    // { text: '超计划需求数', value: 'Plan_Num', width: '160px', semicolonFormat: true },
    // { text: '库位', value: 'PositionCode', width: '160px' },
    // { text: '托盘', value: 'TrayCode', width: '160px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '100px'
    }
];
// , semicolonFormat: true
export const dumpDetailsInDetails = [
    { text: '物料', value: 'MaterialCode', width: '160px' },
    { text: '物料描述', value: 'MaterialDescribe', width: '260px' },
    { text: '领用批次', value: 'Batchcode', width: '140px' },
    { text: '配送数量', value: 'Plan_Num', width: '140px', semicolonFormat: true },
    { text: '单位', value: 'Unit', width: '120px' },
    {
        text: '',
        align: 'center',
        value: 'noActions',
        width: '0'
    }
];
export const materialDumpDetailIn = [
    // { text: '原有/新增', value: 'OriginalOrNew', width: '120px' },
    { text: '物料', value: 'MaterialCode', width: '160px' },
    { text: '批次号', value: 'Batchcode', width: '140px' },
    { text: '追溯批次号', value: 'Backbatchcode', width: '160px' },
    { text: '数量', value: 'Num', width: '140px', semicolonFormat: true },
    // { text: '物料描述', value: 'MaterialDescribe', width: '160px' },
    { text: '单位', value: 'Unit', width: '120px' },
    { text: '生产时间', value: 'BringoutDate', width: '160px',},
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '100px'
    }
];
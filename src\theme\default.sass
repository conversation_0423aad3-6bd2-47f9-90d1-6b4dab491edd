/* ------------------------------------------------------------------
 * [Table of contents]
 */
// # overwrite some style of SASS

/*------------------------------------------------------------------
 * [FontAwesome]
 */

/*------------------------------------------------------------------
 * [vuetify]
 */
$color-pack: true
@import '~vuetify/src/styles/main.sass'

.app-drawer
  .v-navigation-drawer__content
    overflow-y: hidden

.v-input.search
  .v-input__slot
    margin-top : 8px
/*------------------------------------------------------------------
 * [Components / AppToolbar]
 */    
.admin_toolbar
  .v-toolbar__extension
    padding: 0 !important

// helper
.border-bottom
  border-bottom : 1px solid #eee !important

.center-align
  position: absolute
  left: 50%
  top: 50%
  transform: translateY(-50%) translateX(-50%)

<template>
    <v-dialog v-model="opraeDialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="headline primary lighten-2" primary-title>{{ `${materialObj.ID ?
                    $t('DFM_WLGL.editorMaterial') : $t('DFM_WLGL.addMaterial')}`
            }}</v-card-title>
            <v-card-text>
                <v-form v-if="opraeDialog" ref="form" v-model="valid" class="mt-8">
                    <v-row>
                        <v-col :cols="12" :lg="6">
                            <!-- 料号 -->
                            <v-text-field v-model="form.Code" :rules="rules.Code"
                                :label="$t('$vuetify.dataTable.DFM_WLGL.Code')" required dense outlined></v-text-field>
                        </v-col>
                        <v-col :cols="12" :lg="6">
                            <!-- 名称 -->
                            <v-text-field v-model="form.NAME" :label="$t('DFM_WLGL.Name')" required dense outlined>
                            </v-text-field>
                        </v-col>
                        <v-col :cols="12" :lg="6">
                            <!-- 单位 -->
                            <!-- <v-text-field v-model="form.UomOfDim" :label="$t('DFM_WLGL.UomOfDim')" dense outlined></v-text-field> -->
                            <v-select v-model="form.Unit" :items="unitList" item-text="Unit1" item-value="Value1"
                                no-data-text="no data" clearable dense required outlined
                                :label="$t('DFM_WLGL.UnitName')" />
                        </v-col>
                        <v-col :cols="12" :lg="6">
                            <!-- 版本号 -->
                            <v-text-field v-model="form.Version" :label="$t('DFM_WLGL.Version')" required dense
                                outlined></v-text-field>
                        </v-col>
                        <!-- <v-col :cols="12" :lg="6">
                            <v-select v-model="form.Plant" :items="factoryList" item-text="EquipmentName"
                                item-value="EquipmentCode" no-data-text="no data" clearable dense required outlined
                                :label="$t('$vuetify.dataTable.DFM_WLGL.Plant')" />
                        </v-col> -->
                        <v-col :cols="12" :lg="6">
                            <!-- label="物料分类" -->
                            <!-- <v-text-field v-model="form.VendorPn" required dense outlined></v-text-field> -->
                            <v-select v-model="form.Type" :items="classifyList" item-text="Name" item-value="Code"
                                no-data-text="no data" clearable dense required outlined
                                :label="$t('DFM_WLGL.VendorPn')" />
                        </v-col>
                        <!-- <v-col :cols="12" :lg="6">
                            <v-combobox v-model="form.Categorycode" :items="materialTeamList" item-text="Name"
                                item-value="Code" no-data-text="no data" clearable dense required outlined
                                :label="$t('DFM_WLGL.Categorycode')" />
                        </v-col> -->
                        <v-col :cols="12" :lg="12">
                            <!-- label="物料描述" -->
                            <v-textarea v-model="form.Description" :label="$t('DFM_WLGL.Description')" required
                                :rows="2" dense outlined></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-checkbox v-if="!materialObj.ID" v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { saveForm } from '@/api/factoryPlant/material.js';
import { getFactoryList, getUnitList } from '../service';
export default {
    props: {
        IsSpecial: {
            type: Array,
            default: () => []
        },
        materialTeamList: {
            type: Array,
            default: () => []
        },
        classifyList: {
            type: Array,
            default: () => []
        },
        materialObj: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            checkbox: true,
            opraeDialog: false,
            form: {
                ID: '',
                Plant: '',
                IsSpecial: '0',
                Code: '',
                NAME: '',
                Unit: '',
                Description: '',
                Type: '',
                Categorycode: '',
                Version: ''
            },
            rules: {
                Code: [v => !!v || this.$t('GLOBAL._MANDATORY')],
            },
            valid: true,
            items: [], //{ state: '所属二级分类', abbr: '' }
            factoryList: [],
            unitList: []
        };
    },
    watch: {
        materialObj: {
            handler(curVal) {
                for (const key in this.form) {
                    if (Object.hasOwnProperty.call(this.form, key)) {
                        this.form[key] = curVal[key];
                    }
                }
                this.form.IsSpecial = this.form.IsSpecial || '0';
                if (this.form.Categorycode) {

                    this.form.Categorycode = this.materialTeamList.find(item => item.Code == this.form.Categorycode)
                }
            },
            deep: true,
        }
    },
    created() {
        this.getFactoryList();
        this.getUnitList();
    },
    methods: {
        async getUnitList() {
            let resp = await getUnitList({});
            this.unitList = resp.response;
        },
        async getFactoryList() {
            let resp = await getFactoryList({});
            this.factoryList = resp.response;
        },
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                let param = JSON.parse(JSON.stringify(this.form))
              console.log(param)
                if(param.Categorycode)  param.Categorycode = param.Categorycode.Code
                const res = await saveForm(param);
                const { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    if (this.materialObj.ID || this.checkbox) {
                        this.$emit('handlePopup', 'opear');
                        this.opraeDialog = false;
                    } else {
                        this.$refs.form.reset();
                    }
                }
            }
        },
        closePopup() {
            this.opraeDialog = false;
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>

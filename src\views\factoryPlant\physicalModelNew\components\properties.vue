<template>
    <div class="properties">
        <a-tabs type="card" size="small" v-model="activeKey">
            <a-tab-pane v-for="item in properList" :key="item.EquipmentFunctionId" :tab="item.FunctionName">
                <div class="btn-row">
                    <a-button size="small" @click="save()" type="primary">Save</a-button>
                </div>
                <vxe-table ref="vxeTable" height="500px" style="margin-top:5px" size="mini" border resizable
                    :data="item.ActiveFunctionPropertyList">
                    <vxe-column :width="column.width" :field="column.field" :title="column.title"
                        v-for="(column, index) in properColumns" :key="index" :type="column.type">
                        <template #default="{ row }">
                            <span v-if="column.field == 'ActualValue'">
                                <vxe-input :type="row.PropertyType == '2' ? 'number' : 'text'"
                                    v-if="row.PropertyType != '4'" v-model="row[column.field]" size="mini"></vxe-input>
                                <vxe-select v-else v-model="row[column.field]" size="mini">
                                    <vxe-option v-for="(item, ind) in row.PropertyValueList" :key="ind" :value="item"
                                        :label="item"></vxe-option>
                                </vxe-select>
                            </span>
                            <span v-else>{{ row[column.field] }}</span>
                        </template>
                    </vxe-column>
                </vxe-table>
            </a-tab-pane>
        </a-tabs>
    </div>
</template>

<script>
import { properColumns } from '@/columns/factoryPlant/physicalModelNew.js'
import { savePropertyList } from '../service'
export default {
    props: {
        properList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            activeKey: this.properList[0].EquipmentFunctionId,
            properColumns
        }
    },
    mounted() {
        // this.$nextTick(() => {
        //     this.activeKey = this.properList[0].EquipmentFunctionId
        // })
    },
    methods: {
        async save() {
            let list = this.properList.find(item => item.EquipmentFunctionId == this.activeKey)?.ActiveFunctionPropertyList
            let resp = await savePropertyList(list)
            this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });
        }
    }
}
</script>

<style></style>
// 告警记录 dictionary: true
export const alarmRecordColumns = [
    {
        text: '序号',
        value: 'Index',
        width: 60
    },
    { text: '产品线', value: 'AreaName', width: 100 },
    { text: '工段', value: 'ProductLineName', width: 120 },
    { text: '工站', value: 'UnitName', width: 160 },
    { text: '设备', value: 'EquipmentName', width: 160 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '是否主告警', value: 'IsMain', width: 120, dictionary: true },
    { text: '一级分类', value: 'MainAlarmType', width: 100, dictionary: true },
    { text: '二级分类', value: 'SubAlarmType', width: 120, dictionary: true },
    { text: '问题等级', value: 'ProblemLevel', width: 100, dictionary: true },
    { text: '告警等级', value: 'EventLevel', width: 80 },
    { text: '当前状态', value: 'RecordStatus', width: 100, dictionary: true },
    { text: '事件号', value: 'EventNo', width: 100 },
    { text: '告警内容', value: 'AlarmContent', width: 320 },
    { text: '关警说明', value: 'Comment', width: 200 },
    { text: '预估完成时间(min)', value: 'Predicttime', width: 160 },
    // { text: '当前处置人', value: 'guid', width: 160 },
    // { text: '报警时间', value: 'CreateDate', width: 160 },
    { text: '接警人', value: 'Currentman', width: 120 },
    { text: '接警时间', value: 'Calldate', width: 160 },
    { text: '完成时间', value: 'Overdate', width: 160 },
    { text: '关警人', value: 'Closeman', width: 120 },
    { text: '关警时间', value: 'Closedate', width: 160 },
    { text: '工单', value: 'Wo', width: 160 },
    { text: '最近修改时间', value: 'ModifyDate', width: '160px' },
    { text: '最近修改人', value: 'ModifyUserId', width: '120px' },
    { text: '创建人', value: 'CreateUserId', width: '120px' },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: 160
    }
];

// 报警记录详情 dictionary: true
export const alarmRecordDetails = [
    {
        text: '序号',
        value: 'Index',
        width: 60
    },
    { text: '阶段', value: 'Process', width: 80 },
    { text: '描述', value: 'ActionType', width: 80 },
    { text: '通知对象', value: 'Receiver', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '开始时差', value: 'StartTimeDiff', width: 100 },
    // { text: '上一步时差', value: 'PreTimeDiff', width: 100 },
    {
        text: '',
        align: 'center',
        value: 'noActions',
        width: 0
    }
];

// 升级路线 dictionary: true
export const upgradeColumns = [
    {
        text: '序号',
        value: 'Index',
        width: 60
    },
    { text: '等级', value: 'EventLevel', width: 80 },
    { text: '安灯岗位', value: 'Dutydetail', width: 120 },
    // { text: '目标人员', value: 'Receiver', width: 120 },
    { text: '升级时间（min)', value: 'OutTime', width: 160 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    {
        text: '',
        align: 'center',
        value: 'noActions',
        width: 0
    }
];

// 损耗 dictionary: true
export const lossColumns = [
    {
        text: '序号',
        value: 'Index',
        width: 60
    },
    { text: '物料号', value: 'Process', width: 160 },
    { text: '物料名称', value: 'EquipmentCode', width: 160 },
    { text: 'Scada统计量', value: 'Receiver', width: 120 },
    { text: '最终损耗量', value: 'CreateDate', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    {
        text: '',
        align: 'center',
        value: 'noActions',
        width: 0
    }
];

// 报警记录处置详情 dictionary: true
export const handleDeatailColumns = [
    {
        text: '序号',
        value: 'Index',
        width: 60
    },
    { text: '一级分类', value: 'MainAlarm', width: 120 },
    { text: '二级分类', value: 'SubAlarm', width: 160 },
    { text: '排序', value: 'times', width: 80 },
    { text: '告警时间', value: 'CreateDate', width: 160 },
    // { text: '负责人', value: 'Currentduty', width: 120 },
    { text: '告警内容', value: 'AlarmContent', width: 470 },
    {
        text: '',
        align: 'center',
        value: 'noActions',
        width: 0
    }
];
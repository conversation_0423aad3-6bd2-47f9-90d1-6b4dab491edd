export const warningColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
}, {
    text: '备件编码',
    value: 'Code',
    Namevalue: "_bjbm",
    width: 150,
    sortable: true
}, {
    text: '备件名称',
    value: 'Name',
    Namevalue: "_bjmc",
    width: 150,
    sortable: true
}, {
    text: '备件类型',
    value: 'Type',
    Namevalue: "_bjlx",
    width: 150,
    sortable: true
}, {
    text: '品牌',
    value: 'Brand',
    Namevalue: "_pp",
    width: 150,
    sortable: true
}, {
    text: '规格型号',
    value: 'Model',
    Namevalue: "_ggxh",
    width: 150,
    sortable: true
}, {
    text: '库存',
    value: 'Stock',
    Namevalue: "_kc",
    width: 150,
    sortable: true
}, {
    text: '单位',
    value: 'Unit',
    Namevalue: "_dw",
    width: 150,
    sortable: true
}, {
    text: '库存安全上限',
    value: 'LowerBound',
    Namevalue: "_kcaqsx",
    width: 150,
    sortable: true
}, {
    text: '库存安全下限',
    value: 'UpperBound',
    Namevalue: "_kcaqxx",
    width: 150,
    sortable: true
}, {
    text: '采购周期',
    value: 'PurchaseCycle',
    Namevalue: "_cgzq",
    width: 130,
    sortable: true
}, {
    text: '最低采购数量',
    value: 'MinPurchaseQty',
    Namevalue: "_zdcgsl",
    width: 130,
    sortable: true
}, {
    text: '预警时间',
    value: 'AlarmDate',
    Namevalue: "_yjsj",
    width: 150,
    sortable: true
}, {
    text: '预警时间',
    value: 'Remark',
    Namevalue: "Remark",
    width: 150,
    sortable: true
}, {
    text: '',
    value: 'actions',
    Namevalue: "actions",
    width: 150,
    sortable: true
},]
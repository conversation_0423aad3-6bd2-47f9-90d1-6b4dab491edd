<template>
    <div class="edit-instance-property">
      <el-form  ref="dialogForm" :model="form" label-width="150px">
        <el-form-item label="Name">
          <el-input v-model="form.Name" placeholder="" disabled/>
        </el-form-item>
        <el-form-item label="Name">
          <el-input v-model="form.Name" placeholder="" disabled/>
        </el-form-item>
        <el-form-item label="Description">
          <el-input v-model="form.Description" placeholder="" disabled/>
        </el-form-item>
        <el-form-item label="DataType">
          <el-input v-model="form.DataType" placeholder="" disabled/>
        </el-form-item>
        <el-form-item label="Property Initial Value">
          <el-input v-model="form.NullValue" placeholder="" disabled/>
        </el-form-item>
        <el-form-item label="Instance Initial Value">
          <el-input v-model="form.InitialValue" placeholder="" />
        </el-form-item>
        <el-form-item label="Tag">
          <el-select show-search clearable style="width: 100%" filterable remote :remote-method="remoteMethod" v-model="form.OpcTagId" placeholder="">
            <el-option v-for="(item, index) in tagList" :key="index" :label="item.Name" :value="item.ID">
            </el-option>
          </el-select>
<!--          <el-input v-model="form.OpcTagId" placeholder="" />-->
        </el-form-item>
      </el-form>
<!--        <a-form :model="form" :label-col="{ span: 7, }" :wrapper-col="{ span: 15, }">-->
<!--            <a-form-item label="Name">-->
<!--                <a-input disabled v-model="form.Name" />-->
<!--            </a-form-item>-->
<!--            <a-form-item label="Description">-->
<!--                <a-input disabled v-model="form.Description" />-->
<!--            </a-form-item>-->
<!--            <a-form-item label="Data Type">-->
<!--                <a-input disabled v-model="form.DataType" />-->
<!--            </a-form-item>-->
<!--            <a-form-item label="Property Initial Value">-->
<!--                <a-input disabled v-model="form.NullValue" />-->
<!--            </a-form-item>-->
<!--            <a-form-item label="Instance Initial Value">-->
<!--                <a-input v-model="form.InitialValue" />-->
<!--            </a-form-item>-->
<!--            <a-form-item label="Tag">-->
<!--                <a-input v-model="form.OpcTagId" />-->
<!--            </a-form-item>-->
<!--        </a-form>-->
    </div>
</template>

<script>
import {getOpcAllTag, getOpcTag} from "@/views/factoryPlant/Opc/service";

export default {
    props: {
        currentProperty: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            form: {},
          tagForm: {
            key:'',
          },
          tagList:[]
        }
    },
    created() {
      this.tagList = []
      this.form = Object.assign({}, this.currentProperty)
      const  obj = {
          Name:this.form.OpcTagName,
        ID:this.form.OpcTagId
      }
      this.tagList.push(obj)
      console.log(this.form)
    },
    methods: {
      getOpcTag(){
        getOpcAllTag(this.tagForm).then(res=>{
          this.tagList = res.response
        })
      },
      remoteMethod(query){
        setTimeout(()=>{
          this.tagForm.key = query
          this.getOpcTag()
          console.log(query)
        },1000)
      }
    }
}
</script>

<style lang="scss" scoped>
.ant-row.ant-form-item {
    margin-bottom: 10px;
}
</style>

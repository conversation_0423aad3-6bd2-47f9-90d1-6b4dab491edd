"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.consoleInfo = consoleInfo;
exports.consoleWarn = consoleWarn;
exports.consoleError = consoleError;
exports.deprecate = deprecate;
exports.breaking = breaking;
exports.removed = removed;

var _framework = _interopRequireDefault(require("../framework"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

/* eslint-disable no-console */
function createMessage(message, vm, parent) {
  if (_framework.default.config.silent) return;

  if (parent) {
    vm = {
      _isVue: true,
      $parent: parent,
      $options: vm
    };
  }

  if (vm) {
    // Only show each message once per instance
    vm.$_alreadyWarned = vm.$_alreadyWarned || [];
    if (vm.$_alreadyWarned.includes(message)) return;
    vm.$_alreadyWarned.push(message);
  }

  return "[Vuetify] ".concat(message) + (vm ? generateComponentTrace(vm) : '');
}

function consoleInfo(message, vm, parent) {
  var newMessage = createMessage(message, vm, parent);
  newMessage != null && console.info(newMessage);
}

function consoleWarn(message, vm, parent) {
  var newMessage = createMessage(message, vm, parent);
  newMessage != null && console.warn(newMessage);
}

function consoleError(message, vm, parent) {
  var newMessage = createMessage(message, vm, parent);
  newMessage != null && console.error(newMessage);
}

function deprecate(original, replacement, vm, parent) {
  consoleWarn("[UPGRADE] '".concat(original, "' is deprecated, use '").concat(replacement, "' instead."), vm, parent);
}

function breaking(original, replacement, vm, parent) {
  consoleError("[BREAKING] '".concat(original, "' has been removed, use '").concat(replacement, "' instead. For more information, see the upgrade guide https://github.com/vuetifyjs/vuetify/releases/tag/v2.0.0#user-content-upgrade-guide"), vm, parent);
}

function removed(original, vm, parent) {
  consoleWarn("[REMOVED] '".concat(original, "' has been removed. You can safely omit it."), vm, parent);
}
/**
 * Shamelessly stolen from vuejs/vue/blob/dev/src/core/util/debug.js
 */


var classifyRE = /(?:^|[-_])(\w)/g;

var classify = function classify(str) {
  return str.replace(classifyRE, function (c) {
    return c.toUpperCase();
  }).replace(/[-_]/g, '');
};

function formatComponentName(vm, includeFile) {
  if (vm.$root === vm) {
    return '<Root>';
  }

  var options = typeof vm === 'function' && vm.cid != null ? vm.options : vm._isVue ? vm.$options || vm.constructor.options : vm || {};
  var name = options.name || options._componentTag;
  var file = options.__file;

  if (!name && file) {
    var match = file.match(/([^/\\]+)\.vue$/);
    name = match && match[1];
  }

  return (name ? "<".concat(classify(name), ">") : "<Anonymous>") + (file && includeFile !== false ? " at ".concat(file) : '');
}

function generateComponentTrace(vm) {
  if (vm._isVue && vm.$parent) {
    var tree = [];
    var currentRecursiveSequence = 0;

    while (vm) {
      if (tree.length > 0) {
        var last = tree[tree.length - 1];

        if (last.constructor === vm.constructor) {
          currentRecursiveSequence++;
          vm = vm.$parent;
          continue;
        } else if (currentRecursiveSequence > 0) {
          tree[tree.length - 1] = [last, currentRecursiveSequence];
          currentRecursiveSequence = 0;
        }
      }

      tree.push(vm);
      vm = vm.$parent;
    }

    return '\n\nfound in\n\n' + tree.map(function (vm, i) {
      return "".concat(i === 0 ? '---> ' : ' '.repeat(5 + i * 2)).concat(Array.isArray(vm) ? "".concat(formatComponentName(vm[0]), "... (").concat(vm[1], " recursive calls)") : formatComponentName(vm));
    }).join('\n');
  } else {
    return "\n\n(found in ".concat(formatComponentName(vm), ")");
  }
}
//# sourceMappingURL=console.js.map
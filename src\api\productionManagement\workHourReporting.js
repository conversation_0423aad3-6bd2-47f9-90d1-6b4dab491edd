import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_30015'
 
// 列表
export function getProductionOrderViewList(data) {
  const api = '/api/ProductionOrderView/GetPageList'
  return getRequestResources(baseURL, api, 'post', data);
}

// id获取实体详情 
export function getEntityByOrderId(data) {
  const api = '/api/Confirmation/GetEntityByOrderId'
  return getRequestResources(baseURL, api, 'post', data);
}

// id获取实体详情 
export function getEntity(id) {
  const api =  `/api/Confirmation/GetEntity/${id}`
  return getRequestResources(baseURL, api, 'get', null);
}

// id获取实体详情 
export function getEntity2(id) {
  const api =  `/api/Confirmation/GetEntity2/${id}`
  return getRequestResources(baseURL, api, 'get', null);
}

// id获取工时
export function getTimeList(data) {
  const api = `/api/Performance/GetTimeList/${data.confirmationId}`
  return getRequestResources(baseURL, api, 'get', null);
}

// 保存
export function confirmationSaveForm(data) {
  const api = '/api/Confirmation/SaveForm'
  return getRequestResources(baseURL, api, 'post', data);
}

// 保存
export function UpdateData(data) {
  const api = '/api/Confirmation/UpdateData'
  return getRequestResources(baseURL, api, 'post', data);
}

// 发送
export function performanceOrderConf(data) {
  const api = '/api/Performance/OrderCONF'
  return getRequestResources(baseURL, api, 'post', data);
}

// 保存
export function OrderCONFReverse(data) {
  const api = '/api/Performance/OrderCONFReverse'
  return getRequestResources(baseURL, api, 'post', data);
}
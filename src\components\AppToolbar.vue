<template>
    <div>
        <v-app-bar color="primary" dark app height="40">
            <v-app-bar-nav-icon @click="handleDrawerToggle" />
            <v-toolbar-items style="width: calc(100% - 380px)">
                <v-toolbar v-if="extended" slot="extension" height="40" tag="div" dense color="primary" dark flat>
                    <v-tabs v-model="tab" align-with-title center-active>
                        <!-- <v-tabs-slider color="yellow"></v-tabs-slider> -->
                        <v-tab v-for="(item, index) in [...items]" :key="index" :title="$t(item.name)"
                            @click="goToPage(item.path)">
                            {{ $t(item.name) }}
                            <v-icon v-if="item.iscolse !== 'Homedashboard'" class="pl-1" small
                                @click.stop="closeTab(index, item.path)">mdi-close</v-icon>
                        </v-tab>
                    </v-tabs>
                </v-toolbar>
            </v-toolbar-items>
            <v-spacer />

            <v-toolbar-items>
                <v-btn icon @click="clearList">
                    <v-icon>mdi-close</v-icon>
                </v-btn>
                <v-btn icon @click="handleFullScreen()" title="放大/缩小">
                    <v-icon>mdi-fullscreen</v-icon>
                </v-btn>
                <v-menu offset-y origin="center center" class="elelvation-1" transition="scale-transition">
                    <template #activator="{ on }">
                        <v-btn slot="activator" icon text v-on="on">
                            <div class="badge">
                                <v-badge dot offset-x="12" offset-y="" color="red" overlap v-if="noticeList.length">
                                    <!-- <span slot="badge">{{ getNotification.length }}</span> -->
                                </v-badge>
                            </div>
                            <v-icon medium>mdi-bell</v-icon>
                        </v-btn>
                    </template>
                    <notification-list v-show="noticeList.length > 0" :items="noticeList" />
                </v-menu>
                <!-- locale -->
                <LocaleSwitch />
                <v-menu class="ml-2" offset-y origin="center center" transition="scale-transition">
                    <template #activator="{ on }">
                        <v-btn slot="activator" icon v-on="on" title="系统设置" class="px-2">
                            {{ getUserinfolist[0].UserName }}
                            <!-- <c-avatar :size="36" :username="getUsername" status="online" /> -->
                        </v-btn>
                    </template>
                    <v-list class="pa-0 ma-0">
                        <v-list-item v-for="(item, index) in profileMenus" :key="index" class="py-0 my-0 userlist"
                            :to="!item.href ? { name: item.name } : null" :href="item.href" :disabled="item.disabled"
                            :target="item.target" rel="noopener" @click="item.click">
                            <v-list-item-action v-if="item.icon">
                                <v-icon>{{ item.icon }}</v-icon>
                            </v-list-item-action>
                            <v-list-item-content>
                                <v-list-item-title>{{ item.title }}</v-list-item-title>
                            </v-list-item-content>
                        </v-list-item>
                    </v-list>
                </v-menu>
                <v-btn icon @click="showCloseDialog" title="退出登录">
                    <v-icon>mdi-power</v-icon>
                </v-btn>
            </v-toolbar-items>
            <v-dialog v-model="closeDialog" width="400">
                <v-card>
                    <v-card-title class="headline">退出登录</v-card-title>
                    <v-card-text class="close-warn-txt"
                        style="font-size: 16px; margin: 10px 0">点击确认按钮将退出系统，是否继续？</v-card-text>
                    <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn color="primary" @click="handleLogut">确定</v-btn>
                        <v-btn @click="closeDialog = false">取消</v-btn>
                    </v-card-actions>
                </v-card>
            </v-dialog>
        </v-app-bar>
        <!-- setting drawer -->
        <v-navigation-drawer v-model="rightDrawer" temporary right hide-overlay fixed>
            <template v-if="showSetting">
                <theme-settings />
            </template>
        </v-navigation-drawer>
        <!-- UserCenter -->
        <UserCenter ref="UserCenter"></UserCenter>
    </div>
</template>
<script>
import { getAndonNotice } from '@/api/andonManagement/alarmHome.js'
import { getAlarmTypeRootList } from '@/api/andonManagement/alarmType.js';
import NotificationList from '@/components/list/NotificationList';
import LocaleSwitch from '@/components/locale/LocaleSwitch';
import UserCenter from '@/components/userCneter';
import ThemeSettings from '@/components/ThemeSettings';
import * as signalR from '@microsoft/signalr';
import Util from '@/util';
import { mapGetters } from 'vuex';
export default {
    name: 'AppToolbar',
    components: {
        LocaleSwitch,
        NotificationList,
        ThemeSettings,
        UserCenter
        // CAvatar
    },
    props: {
        extended: Boolean
    },
    data() {
        return {
            signaRApi: '/api2/chatHub',
            dataList: [],
            closeDialog: false,
            rightDrawer: false,
            showSetting: true,
            tab: null,
            routerFrom: [],
            items: [
                {
                    name: 'dashboard',
                    path: '/dashboard',
                    iscolse: 'Homedashboard'
                }
            ]
        };
    },
    computed: {
        profileMenus() {
            return [
                {
                    icon: 'mdi-cog',
                    href: '#',
                    title: '主题设置',
                    click: this.handleSetting
                },
                {
                    icon: 'mdi-cog',
                    title: '个人中心',
                    click: this.handleCenter
                }
            ];
        },
        ...mapGetters(['getAvatar', 'getUsername', 'getUserinfolist', 'getNotification']),
        noticeList() {
            return this.getNotification.filter(item => item.UNREAD !== 0)
        }
        // items() {
        //     return [
        //         {
        //             name: this.$t('dashboard'),
        //             path: '/dashboard',
        //             iscolse: 'Homedashboard'
        //         }
        //     ];
        // }
        // breadcrumbs() {
        //   const { matched } = this.$route
        //   return matched.map((route, index) => {
        //     const to = index === matched.length - 1 ? this.$route.path : route.path || route.redirect
        //     const text = this.$t(route.meta.title)
        //     debugger
        //     return {
        //       text: text,
        //       to: to,
        //       exact: true,
        //       disabled: false,
        //     }
        //   })
        // },
    },
    watch: {
        $route: 'getPath',
    },
    created() {
        this.getToolBar();
    },
    activated() {
    },
    mounted() {
        this.signalRinit()
    },
    deactivated() {
        this.connection.stop();
    },
    methods: {
        signalRinit() {
            // this.connection = new signalR.HubConnectionBuilder().withUrl(this.$signaRbaseURL + this.signaRApi,
            //     {
            //         skipNegotiation: true,
            //         transport: signalR.HttpTransportType.WebSockets,
            //         accessTokenFactory: () => this.$store.getters.getAccessToken    // 20240422, wzh, signalr test
            //     }).configureLogging(signalR.LogLevel.Information).build();
            this.connection = new signalR.HubConnectionBuilder().withUrl(this.$signaRbaseURL + this.signaRApi + "?userCode=" + this.$store.getters.getUserinfolist[0].LoginName,
                {
                    skipNegotiation: true,
                    transport: signalR.HttpTransportType.WebSockets,
                    accessTokenFactory: () => this.$store.getters.getAccessToken // 20240422, wzh, signalr test
                }).configureLogging(signalR.LogLevel.Information).build();
            this.connection.on("CornerMark", message => {
                try {
                    const o = JSON.parse(message);
                    this.dataList.forEach(i => {
                        i.RESPOND = 0;
                        i.ALARM = 0;
                        i.CLOSED = 0;
                        i.Upgrad = 0;
                        i.UNREAD = 0;
                    });
                    o.forEach(e => {
                        this.dataList.forEach(i => {
                            if (e.mainAlarmType == i.AlarmCode) i[e.recordStatus] = e.count;
                        });
                    });
                    let list = JSON.parse(JSON.stringify(this.dataList))
                    // list = list.filter(item => item.UNREAD !== 0)
                    this.$store.commit('UPDATE_NOTIFICATION', list)
                    this.$forceUpdate();
                } catch (error) {
                    console.log(error);
                }
            });
            this.connection.on("Unread", message => {
                try {
                    const o = JSON.parse(message);
                    o.forEach(e => {
                        this.dataList.forEach(i => {
                            if (e.mainAlarmType == i.AlarmCode) i.UNREAD = e.count;
                        });
                    });
                    let list = JSON.parse(JSON.stringify(this.dataList))
                    this.$store.commit('UPDATE_NOTIFICATION', list)
                    this.$forceUpdate();
                } catch (error) {
                    console.log(error);
                }
            });

            this.connection.on("receiveupdate", () => { })
            // 20240422, wzh, signalr test
            this.connection.on("SendMessageToAll", (msg) => { console.log(msg) })
            this.connection.on("SendMessageByGroup", (msg) => { console.log(msg) })
            this.connection.on("SendMessageByConnection", (msg) => { console.log(msg) })
            this.connection.on("SendMessageByUser", (msg) => { console.log(msg) })

            this.connection.start().then(() => {
                // 连接已建立，可以发送数据
                this.connection.invoke("AddToGroup", this.$store.getters.getUserinfolist[0].LoginName).then(() => {
                    this.getDataList()
                })
                    .catch(function (err) {
                        console.error(err);
                    });
            }).catch((err) => {
                // 处理连接过程中的错误
                console.error("Error while starting connection: ", err);
            });
        },
        // 获取大类列表
        async getDataList() {
            const res = await getAlarmTypeRootList({});
            const { success, response } = res || {};
            if (success) {
                this.dataList = response;
            } else {
                this.dataList = [];
            }
            getAndonNotice();
        },
        // 关闭全部列表
        clearList() {
            this.items = [
                {
                    name: 'dashboard',
                    path: '/dashboard',
                    iscolse: 'Homedashboard'
                }
            ];
            this.$router.push('/dashboard');
        },
        //获取toolbar列表（防止刷新tab列表不见）
        getToolBar() {
            let toolBar = sessionStorage.toolBarList;
            if (toolBar) {
                this.items = JSON.parse(toolBar);
                const path = this.$route.fullPath;
                const n = this.items.findIndex(i => i.path === path);
                this.tab = n;
            }
        },
        // 点击tab跳转
        goToPage(path) {
            this.$router.push(path);
        },
        // 监听切换Tab的路由
        getPath(to, from) {
            const { path, meta, name } = to;
            // 根据跳转的路由判断下标
            const v = this.items.findIndex(i => i.path === path);

            if (v > -1) {
                this.tab = v;
            } else {
                if(meta.isHide != 1){
                    this.items.push({
                        name: name,
                        path
                    });
                    this.tab = this.items.length - 1;
                }
            }
            sessionStorage.toolBarList = JSON.stringify(this.items);
        },
        // 删除Tab
        closeTab(index, path) {
            const len = this.items.length;
            const curPath = this.$route.path;
            this.items.splice(index, 1);
            // 如果关闭的是当前页面是tab最后一个则跳转前一个
            if (index === this.tab && curPath === path) {
                let goPath = this.items[len - 2].path;
                this.$router.push(goPath);
            } else {
                if (index < this.tab) this.tab--;
                sessionStorage.toolBarList = JSON.stringify(this.items);
            }
        },
        handleDrawerToggle() {
            this.$emit('side-icon-click');
        },
        handleFullScreen() {
            Util.toggleFullScreen();
        },
        showCloseDialog() {
            this.closeDialog = true;
        },
        handleCenter() {
            this.$refs.UserCenter.dialog = true;
        },
        handleLogut() {
            this.$store.dispatch('logout');
            window._VMA.$emit('SHOW_SNACKBAR', {
                text: '退出成功',
                color: 'success'
            });
            sessionStorage.clear();
            localStorage.clear();
            this.$router.replace('/auth/login');
        },
        handleSetting() {
            this.$vuetify.goTo(0);
            this.showSetting = true;
            this.rightDrawer = !this.rightDrawer;
        },
        handleGoBack() {
            this.$router.go(-1);
        }
    }
};
</script>
<style lang="scss" scoped>
.badge {
    top: -10px;
    right: 10px;
    position: absolute;
    z-index: 99;
}

.userlist {
    height: 24px !important;
}
</style>

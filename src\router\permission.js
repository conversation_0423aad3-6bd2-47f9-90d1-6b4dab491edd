import store from '../store/index'
import { protectedRoute } from './config';


export const asyncRoute = function () {
    let menuIdList = localStorage.getItem('menuIdList') ? JSON.parse(localStorage.getItem('menuIdList')) : [];

    let tempArr = filterRoute(protectedRoute, menuIdList);

    // store.dispatch('getRoute', tempArr)

    return tempArr
}


function filterRoute(arr, menuIdList) {
    if (!arr.length) return [];
    return arr.filter(item => {
        if (item.children && item.children.length) {
            item.children = filterRoute(item.children, menuIdList);
        }
        return (item.meta && item.meta.id && menuIdList.includes(item.meta.id)) || (item.children && item.children.length > 0)
    })
}

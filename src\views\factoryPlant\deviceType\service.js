import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM'

//获取设备类型列表数据
export function getDeviceTypeList(data) {
    const api = '/api/DeviceCategory/GetPageList'
    return getRequestResources(baseURL, api, 'post', data)
}

//获取设备类型树状结构数据
export function getDeviceTypeTreeData(data) {
    const api = '/api/DeviceCategory/GetCategoryTree'
    return getRequestResources(baseURL, api, 'post', data)
}

//提交设备类型表单
export function saveDeviceType(data) {
    const api = '/api/DeviceCategory/SaveForm'
    return getRequestResources(baseURL, api, 'post', data)
}

//提交设备类型表单
export function deleteDeviceType(data) {
    const api = '/api/DeviceCategory/Delete'
    return getRequestResources(baseURL, api, 'post', data)
}
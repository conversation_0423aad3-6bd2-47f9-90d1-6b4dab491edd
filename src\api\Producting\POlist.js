import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Inventory2'
const baseURL2 = 'baseURL_MATERIAL'
const DFM = 'baseURL_DFM';

//table数据
export function GetQAListViewList(data) {
    const api = '/ppm/BProductionOrderListView/GetQAPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//table数据
export function GetPackPageList(data) {
    const api = '/ppm/BProductionOrderListView/GetPackPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//table数据
export function GetListViewList(data) {
    const api = '/ppm/BProductionOrderListView/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//alltable数据
export function GetListViewAllList(data) {
    const api = '/ppm/BProductionOrderListView/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}

//toRelease
export function GettoRelease(data) {
    const api = '/api/RecipeCommon/BindPoRecipe'
    return getRequestResources(baseURL, api, 'post', data);
}

//Batchtable工序数据
export function GetSegmentList(data) {
    const api = '/api/Segmentlist/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}

//Batchtable工单数据
export function GetBatchList(data) {
    const api = '/ppm/BBatchListView/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetSegmentBatchList(data) {
    const api = '/ppm/BBatchListView/GetSegmentBatchList'
    return getRequestResources(baseURL, api, 'post', data);
}

//Batchtable物料数据
export function GettBatchMaterialList(data) {
    const api = '/ppm/BatchComsumeMaterialListView/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//Batch批次状态修改
export function UpdateBatchStatus(data) {
    const api = '/ppm/BBatchListView/UpdateBatchStatus'
    return getRequestResources(baseURL, api, 'post', data);
}

//工单执行数据
export function GetPoExecutionList(data) {
    const api = '/api/PoExecutionHistroy/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//工单完成
export function UpdatePoStatus(data) {
    const api = '/ppm/BProductionOrderListView/UpdatePoStatus'
    return getRequestResources(baseURL, api, 'post', data);
}


//重新解析构建批次
export function RebuildBatch(data) {
    const api = '/ppm/BProductionOrderListView/RebuildBatch'
    return getRequestResources(baseURL, api, 'post', data, false, true, true);
}

//重新绑定配方
export function BindPoRecipe(data) {
    const api = '/ppm/BProductionOrderListView/BindPoRecipe'
    return getRequestResources(baseURL, api, 'post', data);
}

//参数数据
export function GetPoRecipeParameterList(data) {
    const api = '/api/RecipeCommon/GetPoRecipeParameterList'
    return getRequestResources(baseURL2, api, 'post', data);
}
//消耗数据
export function GetConsumeMaterialList(data) {
    const api = '/ppm/PoConsumeMaterialListView/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}

//生产数据
export function GetProducedMaterialList(data) {
    const api = '/ppm/PoProducedMaterialListView/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//工艺长文本
export function GetLastProcessData(data, params) {
    const api = `/api/ProcessDataView/GetLastProcessDataByPoId/${params}`
    return getRequestResources(baseURL, api, 'get', data);
}
//工艺长文本状态更新
export function UpdateMaterialProcessDataStatus(data) {
    const api = '/api/ProcessDataView/UpdateMaterialProcessDataStatus'
    return getRequestResources(baseURL, api, 'post', data);
}
//工艺长文本保存
export function SaveMaterialProcessData(data) {
    const api = '/api/ProcessDataView/SaveMaterialProcessData'
    return getRequestResources(baseURL, api, 'post', data);
}
//工艺长文本放行
export function OperationPo(data) {
    const api = '/ppm/BProductionOrderListView/OperationPo'
    return getRequestResources(baseURL, api, 'post', data);
}
//配方工艺长文本 20241126 zhj 修改替换为DFM接口
export function GetLastProcessDataByContextVersion(data, params) {
    const api = `/api/ProcessDataMapping/GetLastProcessDataByContextVersion/${params}`
    return getRequestResources(DFM, api, 'get', data);
}

//工单属性
export function GetProperty(data) {
    const api = `/ppm/ProductionOrderProperty/GetProperty`
    return getRequestResources(baseURL, api, 'post', data);
}

//工单DCS信息
export function GetPoDcsInfo(data) {
    const api = `/ptm/Dcs/GetPoDcsInfo`
    return getRequestResources(baseURL, api, 'post', data);
}

//工单属性保存
export function PropertySaveForm(data) {
    const api = `/ppm/ProductionOrderProperty/SaveForm`
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetPoRecipeCommonDataNew(data) {
    const api = '/ptm/PoSegmentParameter/GetPoSegmentParameterList'
    return getRequestResources(baseURL, api, 'post', data);
}
//保存工单工艺参数值
export function SavePoSegmentParameter(data) {
    const api = '/ptm/PoSegmentParameter/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}

//新参数数据
export function GetLogSheetListByPoId(data) {
    const api = '/api/Logsheet/GetLogSheetListByPoId'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetProductionShiftSelect(data) {
    const api = '/ppm/BProductionOrderListView/GetShiftSelect '
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetProductionUpdateShift(data) {
    const api = '/ppm/BProductionOrderListView/UpdateShift '
    return getRequestResources(baseURL, api, 'post', data);
}

export function MyGetEquipmentsByOrderId(data) {
    const api = '/ppm/BProductionOrderListView/GetEquipmentsByOrderId'
    return getRequestResources(baseURL, api, 'post', data);
}

export function MySendLabelPrintToColos(data) {
    const api = '/ppm/BProductionOrderListView/SendLabelPrintToColos'
    return getRequestResources(baseURL, api, 'post', data);
}

export function MyGetQrCode(data, params, params2) {
    const api = `/ppm/BProductionOrderListView/GetQrCode/${params}/${params2}`
    return getRequestResources(baseURL, api, 'get', data);
}

//工艺长文本
export function GetLtexts(data) {
    const api = `/ppm/BProductionOrderListView/GetLtexts`
    return getRequestResources(baseURL, api, 'post', data);
}

//工艺长文本
export function GetCookieOrderLtexts(data) {
    const api = `/ppm/BProductionOrderListView/GetCookieOrderLtexts`
    return getRequestResources(baseURL, api, 'post', data);
}

//更新QA状态
export function UpdateQaStatus(data) {
    const api = `/ppm/BProductionOrderListView/UpdateQaStatus`
    return getRequestResources(baseURL, api, 'post', data);
}

//修改工单Text
export function UpdateLtexts(data) {
    const api = `/ppm/BProductionOrderListView/UpdateLtexts`
    return getRequestResources(baseURL, api, 'post', data);
}

//修改工单Text
export function toSendOrderInfoToSS(data) {
    const api = `/ppm/PoProducedExecution/SendOrderInfoToSS`
    return getRequestResources(baseURL, api, 'post', data, true);
}


//修改工单Text
export function ConsumeUpdateMaterial(data) {
    const api = `/ppm/PoConsumeMaterialListView/UpdateMaterial`
    return getRequestResources(baseURL, api, 'post', data,);
}

// 导出每日物料需求清单
export function EveryDayMaterialExport(data) {
    const api = '/ppm/PoConsumeMaterialListView/ExportData'
    return getRequestResources(baseURL, api, 'post', data);
}

// 消耗增加物料
export function MyAddPoConsumeRequirement(data) {
    const api = '/ppm/BProductionOrderListView/AddPoConsumeRequirement'
    return getRequestResources(baseURL, api, 'post', data);
}

// 消耗增加物料
export function GetPackReport(data) {
    const api = '/ppm/BProductionOrderListView/PackReport'
    return getRequestResources(baseURL, api, 'post', data);
}

export function SavePackReport(data) {
    const api = '/ppm/BProductionOrderListView/SavePackReport'
    return getRequestResources(baseURL, api, 'post', data);
}


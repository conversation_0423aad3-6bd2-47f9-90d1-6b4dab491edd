<template>
    <!-- 告警升级规则 -->
    <v-dialog v-model="dialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ operaObj.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ') }}
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" class="mt-8 mb-2">
                    <v-row>
                        <!-- 一级分类 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.Maintype"
                                :rules="rules.Maintype"
                                :items="typeRootList"
                                item-text="AlarmName"
                                item-value="AlarmCode"
                                :label="$t('$vuetify.dataTable.ANDON_BJCFGZ.Maintype')"
                                return-object
                                dense
                                outlined
                                @change="changeV"
                            ></v-select>
                        </v-col>
                        <!-- 二级分类 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.Subtype"
                                :rules="rules.Subtype"
                                :items="typeChildList"
                                item-text="AlarmName"
                                item-value="AlarmCode"
                                :label="$t('$vuetify.dataTable.ANDON_BJCFGZ.Subtype')"
                                dense
                                outlined
                            ></v-select>
                        </v-col>
                        <!-- 工段 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.Productlinecode"
                                :rules="rules.Productlinecode"
                                :items="productLineList"
                                item-text="EquipmentName"
                                item-value="EquipmentCode"
                                :label="$t('$vuetify.dataTable.ANDON_BJCFGZ.Productlinecode')"
                                @change="changeL"
                                return-object
                                dense
                                outlined
                            ></v-select>
                        </v-col>
                        <!-- 设备 -->
                        <v-col :cols="12" :lg="6">
                            <Treeselect noChildrenText="no data" noOptionsText="no data" v-model="form.Equipmentcode"
                                :rules="rules.Equipmentcode" :normalizer="normalizer" :options="returnedItem" />
                        </v-col>
                        <!-- 指标名称 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.Variable" :rules="rules.Variable" :label="$t('$vuetify.dataTable.ANDON_BJCFGZ.Variable')" required dense outlined />
                        </v-col>
                        <!-- 比较符 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.Syboml" :rules="rules.Syboml" :label="$t('$vuetify.dataTable.ANDON_BJCFGZ.Syboml')" required dense outlined />
                        </v-col>
                        <!-- 标准值 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.Standardvalue" :rules="rules.Standardvalue" :label="$t('$vuetify.dataTable.ANDON_BJCFGZ.Standardvalue')" required dense outlined />
                        </v-col>
                        <!-- 偏差百分比(%) -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.Deviationpercent" :rules="rules.Deviationpercent" :label="$t('$vuetify.dataTable.ANDON_BJCFGZ.Deviationpercent')" required dense outlined />
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn color="normal" @click="closeForm">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { MestoscadaAlarmconfigSaveForm } from '@/api/andonManagement/mestoscadaAlarmconfig.js';
import { getAlarmTypeRootList, GetListByAlarmId } from '@/api/andonManagement/alarmType.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        productLineList: {
            type: Array,
            default: () => []
        },
        equipmentList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            checkbox: true,
            valid: true,
            dialog: false,
            form: {
                ID: '',
                Maintype: [],
                Subtype: '',
                Productlinecode: '',
                Equipmentcode: '',
                Variable: '',
                Syboml: '',
                Standardvalue: '',
                Deviationpercent: ''
            },
            rules: {
                Maintype: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                Subtype: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                Productlinecode: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            },
            typeRootList: [],
            normalizer(node) {
                const { value, name, children } = node
                return {
                    id: value,
                    label: name,
                    children
                };
            },
            typeChildList: [],
            returnedItem: []
        };
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal) {
                    const { Maintype, Productlinecode } = this.operaObj
                    if(Maintype){
                        const o = this.typeRootList.find(i=>i.AlarmCode == Maintype)
                        if(o) this.changeV(o)
                    }
                    if(Productlinecode){
                        const o = this.productLineList.find(i=>i.EquipmentCode == Productlinecode)
                        if(o) this.changeL(o)
                    }
                    for (const key in this.form) {
                        if (Object.hasOwnProperty.call(this.form, key)) {
                            this.form[key] = this.operaObj[key];
                        }
                    }
                    console.log(this.form);
                }
            },
            deep: true,
            immediate: true
        }
    },
    created(){
        // this.getDutyList()
        this.getTypeRootList()
    },
    methods: {
        // 获取大类列表
        async getTypeRootList() {
            const res = await getAlarmTypeRootList({});
            const { success, response } = res || {};
            if (success) {
                this.typeRootList = response;
            } else {
                this.typeRootList = [];
            }
        },
        // 选择一级告警时获取子级
        async changeV(o) {
            this.typeChildList = []
            this.form.SubAlarmType = ''
            const res = await GetListByAlarmId({alarmId: o.ID});
            const { success, response } = res || {};
            if (success) {
                this.typeChildList = response;
            }
        },
        changeL(v) {
            this.form.Equipmentcode = null
            this.returnedItem = []
            this.findEqu(this.equipmentList, v.ID)
        },
        // 根据工段获取设备
        findEqu(arr, id) {
            //循环遍历
            arr.forEach((item) => {
                //判断递归结束条件
                if (item.id == id) {
                    this.returnedItem.push(item);
                }
                if (item.children != null) {
                    //递归调用
                    this.findEqu(item.children, id);
                }
            })
            console.log(this.returnedItem);
        },
        //关闭
        closeForm() {
            this.$emit('handlePopup', 'refresh');
            this.dialog = false;
        },
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                const { Productlinecode, Maintype } = this.form
                const { EquipmentCode } = Productlinecode || {}
                let obj = { ...this.form }
                if (EquipmentCode) {
                    obj = { ...obj, Productlinecode: EquipmentCode }
                }
                if (Maintype.AlarmCode) {
                    obj = { ...obj, Maintype: Maintype.AlarmCode }
                }
                const res = await MestoscadaAlarmconfigSaveForm({ ...this.form, ...obj});
                const { success, msg } = res;
                if(success){
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    this.$refs.form.reset();
                    this.$emit('handlePopup', 'refresh');
                    if (this.operaObj.ID || this.checkbox) {
                        this.closeForm()
                    }
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>
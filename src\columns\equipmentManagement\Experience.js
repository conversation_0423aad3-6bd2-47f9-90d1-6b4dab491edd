export const ExperienceColum = [
    {
        text: '设备分类名称',
        value: 'DeviceCategoryName',
        width: 150,
        sortable: true
    },
    // {
    //     text: '设备分类编号',
    //     value: 'DeviceCategoryId',
    //     width: 150,
    //     sortable: true
    // }, 
    {
        text: '来源',
        value: 'Source',
        width: 120,
        sortable: true
    }, {
        text: '类型',
        value: 'Type',
        width: 120,
        sortable: true
    }, {
        text: '问题描述',
        value: 'Description',
        width: 150,
        sortable: true
    }, {
        text: '是否停机',
        value: 'IsStop',
        width: 120,
        sortable: true
    }, {
        text: '紧急度',
        value: 'Urgency',
        width: 120,
        sortable: true
    }, {
        text: '维修时长（min）',
        value: 'RepairDuration',
        width: 150,
        sortable: true
    }, {
        text: '系统分类',
        value: 'FaultCategory',
        width: 150,
        sortable: true
    }, {
        text: '原因分类',
        value: 'ReasonCategory',
        width: 150,
        sortable: true
    }, {
        text: '维修过程描述',
        value: 'RepairRecordDesc',
        width: 150,
        sortable: true
    }, {
        text: '原因分析',
        value: 'Reason',
        width: 150,
        sortable: true
    }, {
        text: '原因分析结论',
        value: 'ReasonResult',
        width: 150,
        sortable: true
    }, {
        text: '维修性质',
        value: 'RepairNature',
        width: 120,
        sortable: true
    }, {
        text: '查询次数',
        value: 'QueryCount',
        width: 120,
        sortable: true
    }, {
        text: '关键字',
        value: 'Keyword',
        width: 120,
        sortable: true
    }, {
        text: '备注',
        value: 'Remark',
        width: 150,
        sortable: true
    }, {
        text: '操作',
        value: 'actions',
        width: 120,
        sortable: true
    },
];

export const ExperienceColum2 = [
    {
        text: '设备分类名称',
        value: 'DeviceCategoryName',
        width: 150,
        sortable: true
    },
    {
        text: '设备分类编号',
        value: 'DeviceCategoryId',
        width: 150,
        sortable: true
    }, {
        text: '来源',
        value: 'Source',
        width: 120,
        sortable: true
    }, {
        text: '类型',
        value: 'Type',
        width: 120,
        sortable: true
    }, {
        text: '问题描述',
        value: 'Description',
        width: 150,
        sortable: true
    }, {
        text: '是否停机',
        value: 'IsStop',
        width: 120,
        sortable: true
    }, {
        text: '紧急度',
        value: 'Urgency',
        width: 120,
        sortable: true
    }, {
        text: '维修时长（min）',
        value: 'RepairDuration',
        width: 150,
        sortable: true
    }, {
        text: '系统分类',
        value: 'FaultCategory',
        width: 150,
        sortable: true
    }, {
        text: '原因分类',
        value: 'ReasonCategory',
        width: 150,
        sortable: true
    }, {
        text: '维修过程描述',
        value: 'RepairRecordDesc',
        width: 150,
        sortable: true
    }, {
        text: '原因分析',
        value: 'Reason',
        width: 150,
        sortable: true
    }, {
        text: '原因分析结论',
        value: 'ReasonResult',
        width: 150,
        sortable: true
    }, {
        text: '维修性质',
        value: 'RepairNature',
        width: 120,
        sortable: true
    }, {
        text: '查询次数',
        value: 'QueryCount',
        width: 120,
        sortable: true
    }, {
        text: '关键字',
        value: 'Keyword',
        width: 120,
        sortable: true
    }, {
        text: '备注',
        value: 'Remark',
        width: 150,
        sortable: true
    }, 
];
export const EquipColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
        sortable: true
    },
    {
        text: '生产线编号',
        width: 120,
        Namevalue: "LineCode",
        value: 'LineCode',
        sortable: true
    },
    {
        text: '设备编号',
        width: 120,
        Namevalue: "EquipCode",
        value: 'Code',
        sortable: true
    },
    {
        text: '设备名称',
        Namevalue: "Name",
        value: 'Name',
        width: 130,
        sortable: true
    },
    {
        text: '设备别名',
        Namevalue: "SubName",
        value: 'Alias',
        width: 130,
        sortable: true
    },
    {
        text: '设备性质',
        Namevalue: "Equipnature",
        value: 'DeviceNature',
        width: 130,
        sortable: true
    },
    {
        text: '设备分类',
        Namevalue: "EquipType",
        value: 'DeviceCategory',
        width: 130,
        sortable: true
    },
    {
        text: '工厂',
        Namevalue: "Factory",
        value: 'Factory',
        width: 130,
        sortable: true
    },
    {
        text: '所属部门',
        Namevalue: "Department",
        value: 'Deparment',
        width: 130,
        sortable: true
    },
    {
        text: '区域',
        value: 'Area',
        Namevalue: "Area",
        width: 130,
        sortable: true
    },
    {
        text: '分组',
        value: 'Groups',
        Namevalue: "Group",
        width: 130,
        sortable: true
    },
    {
        text: '工作中心',
        value: 'WorkCenter',
        Namevalue: "WorkCenter",
        width: 130,
        sortable: true
    },
    {
        text: '成本中心',
        value: 'CostCenter',
        Namevalue: "CostCenter",
        width: 130,
        sortable: true
    },
    {
        text: '入账方式',
        Namevalue: "Accountingmethod",
        value: 'EntryType',
        width: 130,
        sortable: true
    },
    {
        text: '资产编号',
        Namevalue: "Assetnumber",
        value: 'AssetsNo',
        width: 130,
        sortable: true
    },
    {
        text: '资产名称',
        Namevalue: "AssetName",
        value: 'AssetsName',
        width: 130,
        sortable: true
    },
    {
        text: '旧编码',
        Namevalue: "OldCode",
        value: 'OldCode',
        width: 130,
        sortable: true
    },
    {
        text: '机器序列号',
        Namevalue: "serialnumber",
        value: 'Sn',
        width: 150,
        sortable: true
    },
    {
        text: '牌照号码',
        value: 'PlateNumber',
        Namevalue: "Licenseplatenumber",
        width: 150,
        sortable: true
    },
    {
        text: '出厂编码',
        width: 120,
        Namevalue: "Factorycode",
        value: 'FactoryNo',
        sortable: true,
        align: 'right'
    },
    {
        text: '设备位号',
        Namevalue: "EquipItem",
        value: 'DeviceBin',
        width: 150,
        sortable: true
    },
    {
        text: '设备分类',
        Namevalue: "EquipType",
        value: 'DeviceCategory',
        width: 150,
        sortable: true
    },
    {
        text: '设备类别',
        value: 'DeviceClass',
        Namevalue: "EquipGroup",
        width: 120,
        sortable: true
    },
    {
        text: '品牌',
        Namevalue: "brand",
        value: 'Brand',
        width: 120,
        sortable: true
    },
    {
        text: '规格型号',
        value: 'Model',
        Namevalue: "Specmodel",
        width: 150,
        sortable: true
    },
    {
        text: '数量',
        Namevalue: "Number",
        value: 'Qty',
        width: 150,
        sortable: true
    },
    {
        text: '单位',
        Namevalue: "Unit",
        value: 'Unit',
        width: 150,
        sortable: true
    },
    {
        text: '设备状态',
        value: 'Status',
        Namevalue: "EquipStatus",
        width: 150,
        sortable: true
    },
    {
        text: '使用状态',
        value: 'UsingStatus',
        Namevalue: "UseStatus",
        width: 150,
        sortable: true
    },
    {
        text: '入账日期',
        Namevalue: "IncomeDate",
        value: 'UseDate',
        width: 150,
        sortable: true
    },
    {
        text: '是否进口',
        Namevalue: "isImported",
        value: 'IsImport',
        width: 150,
        sortable: true
    },
    {
        text: '制造商',
        value: 'Manufacture',
        Namevalue: "manufacturer",
        width: 150,
        sortable: true
    }, {
        text: '供应商',
        value: 'Supplier',
        Namevalue: "supplier",
        width: 150,
        sortable: true
    }, {
        text: '产地',
        value: 'Madein',
        Namevalue: "producer",
        width: 150,
        sortable: true
    }, {
        text: '制造日期',
        value: 'ManufactureDate',
        Namevalue: "Manufacturingdate",
        width: 150,
        sortable: true
    }, {
        text: '出厂日期',
        value: 'MakeDate',
        Namevalue: "dateproduction",
        width: 150,
        sortable: true
    }, {
        text: '使用年限',
        Namevalue: "useYear",
        value: 'ServiceLife',
        width: 150,
        sortable: true
    }, {
        text: '预计报废日期',
        Namevalue: "Expectedscrapdate",
        value: 'EstimatedScrapDate',
        width: 150,
        sortable: true
    }, {
        text: '报废日期',
        Namevalue: "Scrapdate",
        value: 'ScrapDate',
        width: 150,
        sortable: true
    }, {
        text: '进厂日期',
        Namevalue: "factoryDate",
        value: 'EntryFactoryDate',
        width: 150,
        sortable: true
    }, {
        text: '调试日期',
        Namevalue: "TestDate",
        value: 'DebugDate',
        width: 150,
        sortable: true
    }, {
        text: '使用日期',
        value: 'UseDate',
        Namevalue: "UseDate",
        width: 150,
        sortable: true
    }, {
        text: '设备原值',
        Namevalue: "EquipValue",
        value: 'DeviceValue',
        width: 150,
        sortable: true
    }, {
        text: '设备尺寸',
        Namevalue: "EquipSize",
        value: 'DeviceSize',
        width: 150,
        sortable: true
    }, {
        text: '设备重量',
        value: 'DeviceWeight',
        Namevalue: "EquipWeight",
        width: 150,
        sortable: true
    }, {
        text: '保修期限',
        Namevalue: "FixLimit",
        value: 'WarrantyPeriod',
        width: 150,
        sortable: true
    }, {
        text: '存放地点',
        value: 'StorageLocation',
        Namevalue: "Storagelocation",
        width: 150,
        sortable: true
    }, {
        text: '存放位置',
        Namevalue: "position",
        value: 'StoragePosition',
        width: 150,
        sortable: true
    }, {
        text: '现存放地点',
        Namevalue: "Nowposition",
        value: 'CurrentStorageLocation',
        width: 150,
        sortable: true
    }, {
        text: '盘点情况',
        Namevalue: "Inventorysituation",
        value: 'CheckState',
        width: 150,
        sortable: true
    },
    // {
    //     text: '内部存放地点',
    //     Namevalue: "Insidelocation",
    //     value: 'xxx',
    //     width: 150,
    //     sortable: true
    // }, 
    {
        text: '关键设备统计',
        Namevalue: "Keyequipmentstatistics",
        value: 'KeyDeviceState',
        width: 150,
        sortable: true
    }, {
        text: '责任人',
        value: 'Header',
        Namevalue: "personresponsible",
        width: 150,
        sortable: true
    }, {
        text: '设备主管',
        value: 'Manager',
        Namevalue: "equipmentSupervisor",
        width: 150,
        sortable: true
    }, {
        text: '点检时间',
        value: 'SpotCheckTime',
        Namevalue: "Inspectiontime",
        width: 150,
        sortable: true
    }, {
        text: '首次保养时间',
        value: 'FirstMaintenanceDate',
        Namevalue: "FirstMaintenanceTime",
        width: 150,
        sortable: true
    }, {
        text: '上次保养时间',
        value: 'LastMaintenanceDate',
        Namevalue: "LastMaintenanceTime",
        width: 150,
        sortable: true
    }, {
        text: '备注',
        Namevalue: "Remark",
        value: 'Remark',
        width: 150,
        sortable: true
    }, {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: 200,
        sortable: true
    }
    // {
    //     text: '产线名称',
    //     value: 'ProductlineName',
    //     width: 150,
    //     sortable: true
    // },

    // {
    //     text: '工段名称',
    //     value: 'LineName',
    //     width: 120,
    //     sortable: true
    // },
    // {
    //     text: '工站名称',
    //     value: 'SegmentName',
    //     width: 120,
    //     sortable: true
    // },
    // {
    //     text: '设备类型',
    //     value: 'Type',
    //     width: 120,
    //     sortable: true
    // },

    // {
    //     text: '设备状态',
    //     value: 'Status',
    //     width: 100,
    //     sortable: true
    // },
    // // {
    // //     text: 'EAM编码',
    // //     width: 120,
    // //     value: 'EamCode',
    // //     sortable: true,
    // //     align: 'right'
    // // },
    // {
    //     text: 'EIP编码',
    //     width: 120,
    //     value: 'EipCode',
    //     sortable: true,
    //     align: 'right'
    // },
    // {
    //     text: '出厂编码',
    //     width: 120,
    //     value: 'LeaveCode',
    //     sortable: true,
    //     align: 'right'
    // },
    // {
    //     text: '型号',
    //     value: 'Eqmodel',
    //     width: 160,
    //     sortable: true
    // },
    // {
    //     text: '条形码',
    //     value: 'BarCode',
    //     width: 160,
    //     sortable: true,
    //     align: 'right'
    // },
    // {
    //     text: '购置日期',
    //     value: 'Purchasedate',
    //     width: 160,
    //     sortable: true
    // },
    // {
    //     text: '制造商',
    //     value: 'Manufacturer',
    //     width: 220,
    //     sortable: true
    // },
    // {
    //     text: '联系人',
    //     value: 'Contacts',
    //     width: 120,
    //     sortable: true
    // },
    // {
    //     text: '电话',
    //     value: 'Tel',
    //     width: 160,
    //     sortable: true,
    //     align: 'right'
    // },
    // {
    //     text: '负责人',
    //     value: 'PersonName',
    //     width: 120,
    //     sortable: true
    // },
    // {
    //     text: '首次保养时间',
    //     value: 'FirstMaintenanceTime',
    //     width: 160,
    //     sortable: true
    // },
    // {
    //     text: '上次保养时间',
    //     value: 'LastMaintenanceTime',
    //     width: 160,
    //     sortable: true
    // },
    // {
    //     text: '出厂日期',
    //     value: 'Productiondate',
    //     width: 160,
    //     sortable: true
    // },
    // {
    //     text: '使用年限',
    //     value: 'Age',
    //     width: 120,
    //     sortable: true,
    //     align: 'right'
    // },
    // {
    //     text: '备注',
    //     value: 'Remark',
    //     width: 160,
    //     sortable: true
    // },
    // {
    //     text: '成本',
    //     value: 'Cost',
    //     width: 120,
    //     sortable: true,
    //     align: 'right'
    // },
    // {
    //     text: '扫码枪',
    //     value: 'Smq',
    //     width: 100,
    //     sortable: true
    // },
    // {
    //     text: '打印机',
    //     value: 'Dyj',
    //     width: 100,
    //     sortable: true
    // },
    // {
    //     text: '喷码机',
    //     value: 'Pmj',
    //     width: 100,
    //     sortable: true
    // },

];
export const EquipListColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
        sortable: true
    },
    {
        text: '编号',
        value: 'Code',
        width: 130,
        sortable: true
    },
    {
        text: '备件名称',
        value: 'Name',
        width: 130,
        sortable: true
    },
    {
        text: '备件类型',
        value: 'Type',
        Namevalue: "Jigtype",
        width: 160,
        sortable: true
    },
    {
        text: '备件规格',
        value: 'Model',
        Namevalue: "Jigspec",
        width: 160,
        sortable: true
    },
    {
        text: '库存数量',
        value: 'Qty',
        width: 160,
        sortable: true,
    },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: 160,
        sortable: true
    }
];
export const ByEquipListColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
        sortable: true
    },
    {
        text: '编号',
        value: 'Code',
        width: 130,
        sortable: true
    },
    {
        text: '备件名称',
        value: 'Name',
        width: 130,
        sortable: true
    },
    {
        text: '备件类型',
        value: 'Type',
        Namevalue: "Jigtype",
        width: 160,
        sortable: true
    },
    {
        text: 'SAP库存数量',
        value: 'Stock',
        Namevalue: "SAPQty",
        width: 160,
        sortable: true,
    },
    {
        text: '批次号',
        value: 'BatchCode',
        Namevalue: "Batch",
        width: 160,
        sortable: true,
    }, 
    {
        text: '单位',
        value: 'Unit',
        Namevalue: "Unit",
        width: 160,
        sortable: true,
    },
    {
        text: '备件规格',
        value: 'Model',
        Namevalue: "Jigspec",
        width: 160,
        sortable: true
    },

    {
        text: '品牌',
        value: 'Brand',
        Namevalue: "brand",
        width: 160,
        sortable: true,
    }, {
        text: '供应商',
        value: 'Supplier',
        Namevalue: "supplier",
        width: 160,
        sortable: true,
    },{
        text: '出库仓库',
        value: 'Warehouse',
        Namevalue: "OutStock",
        width: 160,
        sortable: true,
    }, {
        text: '出库库位',
        value: 'StorageBin',
        Namevalue: "OutWarehouse",
        width: 160,
        sortable: true,
    }, 
];
export const EquipBOMTreeColum = [{
        label: '序号',
        prop: 'Index',
        width: 90,
    },
    { label: '编码', prop: 'value', width: '140px' },
    { label: '名称', prop: 'name', width: '140px' },
    { label: '规格型号', prop: 'title', width: '140px' },
    { label: '数量', prop: 'quantity', width: '140px' },
    {
        label: '操作',
        prop: 'actions',
        type: 'template',
        width: '80px',
        template: 'actions'
    },
]

export const EquipBOMColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
        sortable: true
    },
    {
        text: '编码',
        width: 120,
        value: 'Code',
        Namevalue: 'AccessoriesCode',
        sortable: true
    },
    {
        text: '名称',
        value: 'Name',
        Namevalue: "AccessoriesName",
        width: 130,
        sortable: true
    },
    {
        text: '类型',
        value: 'Type',
        Namevalue: "Type",
        width: 130,
        sortable: true
    },
    {
        text: '规格型号',
        value: 'Model',
        Namevalue: 'Specifications',
        width: 160,
        sortable: true
    },
    // {
    //     text: '数量',
    //     value: 'Qty',
    //     Namevalue: 'Quantity',
    //     width: 120,
    //     sortable: true
    // },
    {
        text: '备注',
        value: 'Remark',
        Namevalue: 'Remark',
        width: 120,
        sortable: true
    },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: 160,
        sortable: true
    }
];
export const EquipKeyColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
        sortable: true
    },
    {
        text: '件名称',
        value: 'Name',
        width: 130,
        sortable: true
    },
    {
        text: '件编码',
        width: 120,
        value: 'Code',
        sortable: true
    },
    {
        text: '设备编码',
        value: 'Eqmodel',
        width: 160,
        sortable: true
    },
    {
        text: '关键件',
        value: 'Iskey',
        width: 160,
        sortable: true
    },
    {
        text: '备注',
        value: 'Remark',
        width: 160,
        sortable: true
    },
    {
        text: '状态',
        value: 'Status',
        width: 160,
        sortable: true
    },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: 160,
        sortable: true
    }
];
export const EquipTargetColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
        sortable: true
    },
    {
        text: '指标名称',
        width: 120,
        value: 'RunningBeat',
        sortable: true
    },
    {
        text: '指标值',
        value: 'BeatSpeeding',
        width: 160,
        sortable: true,
        align: 'right'
    },
    {
        text: '阈值',
        value: 'MachineHour',
        width: 160,
        sortable: true,
        align: 'right'
    },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: 160,
        sortable: true
    }
];
export const EquipPropColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
},
{
    text: '编号',
    width: 150,
    value: 'Code',
    Namevalue: "Code",
    sortable: true
},
{
    text: '名称',
    value: 'Name',
    Namevalue: "Name",
    width: 150,
    sortable: true,
},
{
    text: '分组',
    value: 'GroupName',
    Namevalue: "GroupName",
    width: 100,
    sortable: true,
},
{
    text: '类型',
    value: 'DataType',
    Namevalue: "DataType",
    width: 100,
    sortable: true,
},
{
    text: '默认值',
    value: 'DefaultValue',
    Namevalue: "DefaultValue",
    width: 100,
    sortable: true,
},
{
    text: '备注',
    value: 'Remark',
    Namevalue: "Remark",
    width: 100,
    sortable: true,
},
{
    text: '操作',
    align: 'center',
    value: 'actions',
    width: 160,
    sortable: true
}
];
export const EquipFileColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
        sortable: true
    },
    {
        text: '文件名称',
        width: 150,
        value: 'Name',
        Namevalue: "FileName",
        sortable: true
    },
    {
        text: '版本',
        value: 'Version',
        Namevalue: "FileVersion",
        width: 100,
        sortable: true,
    },
    {
        text: '类型',
        value: 'Type',
        Namevalue: "Type",
        width: 100,
        sortable: true,
    },
    {
        text: '文件大小',
        value: 'Size',
        width: 100,
        sortable: true,
    },
    {
        text: '上传时间',
        value: 'Uploaddate',
        Namevalue: "CreaterTime",
        width: 100,
        sortable: true,
    },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: 160,
        sortable: true
    }
];
export const EquipRecordColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
        sortable: true
    },
    {
        text: '行为',
        value: 'Action',
        Namevalue: "Action",
        width: 140,
        sortable: false
    },
    {
        text: '备注',
        value: 'Content',
        Namevalue: "Behavior",
        width: 220,
        sortable: false
    },
    {
        text: '操作人',
        value: 'CreateUserId',
        Namevalue: "Creator",
        width: 140,
        sortable: false
    },
    {
        text: '操作时间',
        value: 'OperateDate',
        Namevalue: "CreateDate",

        width: 180,
        sortable: false
    },
]
export const EquipSpotCheckWoColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
    },
    {
        text: '点检状态',
        value: 'Status',
        Namevalue: "DJZT",
        width: 140,
    },
    {
        text: '点检计划时间',
        value: 'PlanCheckDate',
        Namevalue: "DJJHSJ",
        width: 220,
    },
    {
        text: '点检完成时间',
        value: 'FinishCheckDate',
        Namevalue: "DJWCSJ",
        width: 140,
    },
    {
        text: '点检人',
        value: 'CheckBy',
        Namevalue: "DJR",
        width: 180,
        sortable: false
    },
    {
        text: '点检结果',
        value: 'Context',
        Namevalue: "DJJG",
        width: 180,
    },
    {
        text: '设备主管',
        value: 'Manager',
        Namevalue: "SBZG",
        width: 180,
    },
    {
        text: '备注',
        value: 'Remark',
        Namevalue: "Remark",
        width: 180,
    },
]

export const EquipMaintainWoColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
    },
    {
        text: '保养状态',
        value: 'Status',
        Namevalue: "BYZT",
        width: 140,
    },
    {
        text: '保养计划时间',
        value: 'PlanMaintainDate',
        Namevalue: "BYJHSJ",
        width: 220,
    },
    {
        text: '保养完成时间',
        value: 'FinishMaintainDate',
        Namevalue: "BYWCSJ",
        width: 140,
    },
    {
        text: '责任人',
        value: 'MaintainBy',
        Namevalue: "ZRR",
        width: 180,
        sortable: false
    },
    {
        text: '保养结果',
        value: 'Context',
        Namevalue: "BYJG",
        width: 180,
    },
    {
        text: '设备主管',
        value: 'Manager',
        Namevalue: "SBZG",
        width: 180,
    },
    {
        text: '备注',
        value: 'Remark',
        Namevalue: "Remark",
        width: 180,
    },
]
export const EquipRepairRecordColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
    },
    {
        text: '维修单号',
        value: 'RepairWo',
        Namevalue: "WXDH",
        width: 140,
    },
    {
        text: '报修内容',
        value: 'Description',
        Namevalue: "BXNL",
        width: 220,
    },
    {
        text: '状态',
        value: 'Status',
        Namevalue: "ZT",
        width: 140,
    },
    {
        text: '紧急度',
        value: 'Urgency',
        Namevalue: "JJD",
        width: 180,
        sortable: false
    },
    {
        text: '维修状态',
        value: 'RepairStatus',
        Namevalue: "WXZT",
        width: 180,
    },
    {
        text: '是否停机',
        value: 'IsStop',
        Namevalue: "SFTJ",
        width: 180,
    },
    {
        text: '工单类型',
        value: 'Type',
        Namevalue: "GDLX",
        width: 180,
    }, {
        text: '工单来源',
        value: 'Source',
        Namevalue: "GDLY",
        width: 180,
    }, {
        text: '关联单号',
        value: 'ReferOrderNo',
        Namevalue: "GLDH",
        width: 180,
    },
]
export const EquipPartsHistoryDetailColum = [{
        text: '序号',
        value: 'Index',
        width: 90,
        sortable: true
    },
    {
        text: '备件编号',
        width: 160,
        Namevalue: "SparePartsCode",
        value: 'PartsCode',
        sortable: true
    }, {
        text: '备件名称',
        width: 160,
        Namevalue: "SparePartsName",
        value: 'PartsName',
        sortable: true
    }, {
        text: '状态',
        width: 120,
        Namevalue: "status",
        value: 'Status',
        sortable: true
    },
    {
        text: '申请单号',
        width: 120,
        Namevalue: "requestNo",
        value: "HistoryNo",
        sortable: true
    }, {
        text: '备件类型',
        width: 120,
        Namevalue: "SType",
        value: 'PartsType',
        sortable: true
    }, {
        text: '规格型号',
        width: 150,
        Namevalue: "SparePartsSpec",
        value: 'PartsModel',
        sortable: true
    }, {
        text: '批次号',
        width: 150,
        Namevalue: "Batch",
        value: 'BatchCode',
        sortable: true
    }, {
        text: '供应商',
        width: 100,
        Namevalue: "supplier",
        value: 'Supplier',
        sortable: true
    }, {
        text: '数量',
        width: 100,
        Namevalue: "number",
        value: 'Qty',
        sortable: true
    },
]
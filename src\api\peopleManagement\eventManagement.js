import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_SHIFT; // 配置服务url
//  事故列表
export function AccidentGetPageList(data) {
    return request({
        url: baseURL + '/shift/Accident/GetPageList',
        method: 'post',
        data
    });
}
// 事故列表-新增
export function AccidentSaveForm(data) {
    return request({
        url: baseURL + '/shift/Accident/SaveForm',
        method: 'post',
        data
    });
}
// 事故列表 -人员删除
export function AccidentDelete(data) {
    return request({
        url: baseURL + '/shift/Accident/Delete',
        method: 'post',
        data
    });
}






<template>
  <el-dialog title="物料"
             :visible.sync="MaterialTableVisible"
             width="800px"
             append-to-body
             :close-on-click-modal="false"
             :modal-append-to-body="false"
             :close-on-press-escape="false"
             @close="MaterialTableVisible=false">
    <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
      <el-form-item :label="$t('GLOBAL._SSL')">
        <el-input clearable v-model="searchForm.key"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="getSearchBtn()">{{ $t('GLOBAL._CX') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table class=""
              ref="multipleTable"
              :data="tableData"
              @selection-change="handleSelectionChange"
              style="width: 100%">
      <el-table-column
          type="selection"
          width="55" v-if="isMultiple">
      </el-table-column>
      
      <el-table-column prop="Code" label="编码" width="120"></el-table-column>
      <el-table-column prop="name" show-overflow-tooltip label="名称">
        <template slot-scope="scope">
          {{ scope.row.NAME }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="Description" label="配方号" width="130"></el-table-column> -->
      <el-table-column prop="Type" label="类型" width="100"></el-table-column>
      <el-table-column prop="UnitName" label="单位" width="100"></el-table-column>
      <el-table-column prop="operation" width="80" :label="$t('GLOBAL._ACTIONS')" align="center" v-if="!isMultiple">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="addRow(scope.row)">{{ $t('GLOBAL._XUANZE') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="width: 100%;height: 10px"></div>
    <el-pagination class="mt-8"
                   background
                   :current-page="searchForm.pageIndex"
                   :page-size="searchForm.pageSize"
                   layout="->, total, prev, pager, next"
                   :total="total"
                   @current-change="handleCurrentChange"/>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="MaterialTableVisible=false">取 消</el-button>
      <el-button v-loading="loading"
                 v-if="isMultiple"
                 :disabled="loading"
                 element-loading-spinner="el-icon-loading"
                 size="small"
                 @click="submit()">确定
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  addCookingloss,
  getMaterialPageList
} from '@/api/productionManagement/Formula';
export default {
  name: 'add',
  props:{
    isMultiple:{
      type:Boolean,
      default:false
    },
    isId:{
      type:Boolean,
      default:true
    }
  },
  data() {
    return {
      MaterialTableVisible:false,
      tableData:[],
      loading:false,
      searchForm: {
        pageIndex: 1,
        pageSize: 10,
      },
      total: 0,
      multipleSelection:[],
      MaterialObj:{}
    }
  },
  mounted() {
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    submit() {
      if(!this.multipleSelection.length && !this.MaterialObj){
        this.$message.error('至少选择一条记录！')
      }
      let matValue = undefined
      if(!this.isMultiple && this.isId){
        matValue = this.MaterialObj.ID
      }
      if(this.isMultiple && this.isId){
        matValue = this.multipleSelection.map(e=>e.ID)
      }
      if(!this.isMultiple && !this.isId){
        matValue = this.MaterialObj
      }
      if(this.isMultiple && !this.isId){
        matValue = this.multipleSelection
      }
      this.$emit('saveForm',matValue)
      this.MaterialTableVisible = false
    },
    show() {
      this.MaterialTableVisible = true
      this.multipleSelection = []
      this.MaterialObj = {}
      this.$nextTick(_ => {
        this.getTableData()
      })
    },
    addRow(row){
      this.MaterialObj = row
      this.submit()
    },
    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },
    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },
    getTableData(){
      getMaterialPageList(this.searchForm).then(res=>{
        this.tableData = res.response.data
        this.total = res.response.dataCount
      })
    }
  }
}
</script>

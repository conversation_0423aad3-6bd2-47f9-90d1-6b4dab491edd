<template>
    <div>
        <Tables
            :page-options="pageOptions"
            ref="recordTable"
            :footer="false"
            :showSelect="false"
            :loading="loading"
            :btn-list="btnList"
            tableHeight="calc(100vh - 220px)"
            table-name="TPM_SBGL_SBWXGD"
            :headers="wxjlColum"
            :desserts="desserts"
        ></Tables>
    </div>
</template>

<script>
import { wxjlColum } from '@/columns/equipmentManagement/Repair.js';
import { GetRepairOrderGetListByWoId } from '@/api/equipmentManagement/NewRepair.js';
import { GetExportData, GetPersonList } from '@/api/equipmentManagement/Equip.js';

export default {
    components: {},
    data() {
        return {
            wxjlColum,
            loading: false,
            desserts: [],
            RepairMngData: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            }
        };
    },
    computed: {
        btnList() {
            return [];
        }
    },
    async created() {
        let RepairMng = await GetPersonList('RepairMng');
        this.RepairMngData = RepairMng.response[0].ChildNodes;
        this.RepairMngData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
    },
    methods: {
        async MyGetRepairOrderGetListByWoId(row, StatusList,MaintenanceGroupData) {
            let params = {
                Factory: this.$route.query.Factory ? this.$route.query.Factory : '2010',
                RepairWo: ''
            };
            if (row) {
                params.RepairWo = row.RepairWo;
            }
            this.loading = true;
            let res = await GetRepairOrderGetListByWoId(params);
            res.response.forEach(item => {
                StatusList.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                this.RepairMngData.forEach(it => {
                    if (item.AssignBy == it.ItemValue) {
                        item.AssignBy = it.ItemName;
                        item.AssignByValue = it.ItemValue;
                    }
                });
                MaintenanceGroupData.forEach(it => {
                    if (item.ReceiveBy == it.ItemValue) {
                        item.ReceiveBy = it.ItemName;
                        item.ReceiveByValue = it.ItemValue;
                    }
                });
                

                
            });
            this.desserts = res.response;
            this.loading = false;
        }
    }
};
</script>

<style></style>

<template>
    <v-dialog v-model="showDialog" max-width="720px">
        <v-card class="" v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                新建
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form.Code" outlined dense label="件编码"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form.Name" outlined dense label="件名称"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form.Eqmodel" outlined dense label="设备编码"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form.Iskey" outlined dense label="是否为关键件"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form.Status" outlined dense label="状态  "></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form.Remark" outlined dense label="备注"></v-text-field>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'edit'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                修改
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.Code" outlined dense label="件编码"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.Name" outlined dense label="件名称"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.Eqmodel" outlined dense label="设备编码"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.Iskey" outlined dense label="是否为关键件"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.Status" outlined dense label="状态  "></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.Remark" outlined dense label="备注"></v-text-field>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions pa-4 class="lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { EquipKeyPartsSaveForm } from '@/api/equipmentManagement/Equip.js';

export default {
    props: {
        repastTypelist: {
            type: Array,
            default: () => []
        },
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            form: {
                Name: '',
                Code: '',
                Eqmodel: '',
                Iskey: null,
                Status: null,
                Remark: ''
            }
        };
    },
    computed: {
        editedItem() {
            return {
                Name: this.tableItem.Name,
                Code: this.tableItem.Code,
                Eqmodel: this.tableItem.Eqmodel,
                Iskey: this.tableItem.Iskey,
                Status: this.tableItem.Status,
                Remark: this.tableItem.Remark
            };
        }
    },

    methods: {
        closeEquip() {
            this.showDialog = false;
            this.$refs.form.reset();
        },
        async addSave(type) {
            const paramsObj = type == 'add' ? this.form : this.editedItem;
            let params = {
                Name: paramsObj.Name,
                Code: paramsObj.Code,
                Eqmodel: paramsObj.Eqmodel,
                Iskey: paramsObj.Iskey,
                Status: paramsObj.Status,
                Remark: paramsObj.Remark
            };
            if (type == 'edit') {
                params.ID = this.tableItem.ID;
            }
            const res = await EquipKeyPartsSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.$parent.RepastInfoGetPage();
                this.showDialog = this.classcheckbox ? false : true;
            }
        }
    }
};
</script>

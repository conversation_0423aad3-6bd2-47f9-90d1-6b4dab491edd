export const inventoryEntryAudit = [
    // { text: '序号', value: 'Index', width: '100px' },
    { text: '物料号', value: 'MaterialCode', width: '160px' },
    { text: '物料名称', value: 'MaterialName', width: '220px' },
    { text: '配送数量', value: 'Num', width: '140px', semicolonFormat: true },
    { text: '合计数量', value: 'IssNumTotal', width: '120px', semicolonFormat: true },
    { text: '已入库数量', value: 'IssNumYRK', width: '120px', semicolonFormat: true },
    { text: '待确认数量', value: 'IssNumDRK', width: '120px', semicolonFormat: true },
    { text: '单位', value: 'Unit', width: '120px' },
    {
        text: '',
        align: 'center',
        value: 'noActions',
        width: '0'
    }
];

export const IssueMaterial = [
    { text: '物料号', value: 'Materialcode', width: '160px' },
    { text: '物料名称', value: 'Materialname', width: '220px' },
    { text: '批次号', value: 'Batchcode', width: '140px' },
    { text: '追溯批次号', value: 'Backbatchcode', width: '160px' },
    { text: '数量', value: 'Num', width: '140px', semicolonFormat: true },
    { text: '状态', value: 'Status', width: '140px', dictionary: true },
    { text: '单位', value: 'Unit', width: '120px' },
    { text: '生产时间', value: 'BringoutDate', width: '160px', },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '100px'
    }
]
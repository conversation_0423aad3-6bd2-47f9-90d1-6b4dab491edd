<template>
	<el-dialog :title="'L-TEXT'" :visible.sync="dialogVisible" width="1000px" :close-on-click-modal="false"
		:modal-append-to-body="false" :close-on-press-escape="false" @close="dialogVisible = false">
		<el-form ref="dialogForm">
      <el-form-item>
        <el-input style="font-size: 16px" type="textarea" :rows="20" v-model="LTEXT" placeholder=""></el-input>
      </el-form-item>
		</el-form>
		<div slot="footer" class="dialog-footer">
			<el-button size="small" @click="dialogVisible = false">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
export default {
	data() {
		return {
			dialogVisible: false,
      LTEXT:''
		}
	},
	mounted() {
	},
	methods: {
		submit() {
      this.dialogVisible = false
		},
		show(data) {
			console.log(data)
			this.dialogVisible = true
			this.$nextTick(_ => {
			  this.LTEXT = data
			})
		}
	}
}
</script>

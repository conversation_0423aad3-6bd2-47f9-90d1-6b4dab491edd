.circle
  border-radius: 3px
  padding: 0.25em 0.4em
  font-weight: 400
  line-height: 1.3
  font-size: 85%

  &:empty
    display: inline-block
    vertical-align: inherit

.circle-pill
  color: #fff
  padding-right: 0.6em
  padding-left: 0.6em
  border-radius: 10rem

.circle-sm
  line-height: 1.2
  padding-top: 1px
  padding-bottom: 2px
  font-size: 75%

.circle-lg
  line-height: 1.5
  padding: 5px 7px
  font-size: 95%

.circle-xl
  line-height: 1.7
  padding: 7px 9px
  font-size: 100%

.circle-dot
  width: 8px
  height: 8px
  padding: 0
  border-radius: 100%
  vertical-align: middle

  &.circle-sm
    width: 6px
    height: 6px

  &.circle-lg
    width: 10px
    height: 10px

  &.circle-xl
    width: 12px
    height: 12px

.circle-ring
  position: relative
  width: 10px
  height: 10px
  padding: 0
  border-radius: 100%
  vertical-align: middle

  &::after
    content: ''
    position: absolute
    top: 2px
    left: 2px
    width: 6px
    height: 6px
    border-radius: 50%
    background-color: #fff
    -webkit-transform: scale(1)
    transform: scale(1)
    -webkit-transition: 0.3s
    transition: 0.3s

  &.circle-sm
    width: 8px
    height: 8px

    &::after
      width: 4px
      height: 4px

  &.circle-lg
    width: 12px
    height: 12px

    &::after
      width: 8px
      height: 8px

  &.circle-xl
    width: 14px
    height: 14px

    &::after
      width: 10px
      height: 10px

  &.fill
    &::after
      -webkit-transform: scale(0)
      transform: scale(0)

.circle-bold
  text-transform: uppercase
  font-weight: 500
  letter-spacing: 1px

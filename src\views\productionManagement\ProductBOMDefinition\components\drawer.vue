<template>
  <div>
    <el-drawer class="drawer" :visible.sync="drawer" :direction="'rtl'" :before-close="handleClose"
      :append-to-body="false" size="80%">
      <div slot="title" class="title-box">
        <span>{{ `${currentRow.MaterialCode}-${currentRow.MaterialName} |
          ${currentRow.ParentSegmentCode}-${currentRow.SegmentCode}` }}</span>
      </div>

      <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
        <el-tab-pane label="BOM" name="process">
          <div class="InventorySearchBox">
            <div class="searchbox pd5">
              <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
                <el-form-item :label="$t('GLOBAL._SSL')">
                  <el-input clearable v-model="searchForm.Key"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button icon="el-icon-search" @click="getTableData">{{ $t('GLOBAL._CX') }}</el-button>
                </el-form-item>
                <el-form-item>
                  <el-button size="small" type="success" icon="el-icon-circle-plus-outline" @click="showDialog({})">{{
                    $t('GLOBAL._XZ') }}
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
          <div class="table-box">
            <el-table :data="tableData" 
              element-loading-spinner="el-icon-loading" style="width: 100%" :height="tableHeight" border
              @sort-change="handleSortChange" ref="dataTable" :empty-text="$t('GLOBAL._NO_DATA')">
              <el-table-column v-for="(item, index) in tableHead" :key="index" :prop="item.field" :label="item.label"
                resizable sortable :min-width="getColumnWidth(item.field)" align="center">
                <template slot-scope="scope">
                  <span v-if="item.field == 'FeedType'">
                    {{ showName(FeedTypeList, scope.row[item.field]) }}
                  </span>
                  <span v-else-if="item.field == 'NeedPrepare'">
                    <el-tag :type="scope.row[item.field] === '1' ? 'success' : 'danger'">
                      {{ showName(DFMYesNoList, scope.row[item.field]) }}
                    </el-tag>
                  </span>
                  <span v-else-if="item.field == 'NeedAdvancePrepare'" class="text-ellipsis">
                    <el-tag :type="scope.row[item.field] === '1' ? 'success' : 'danger'">
                      {{ showName(DFMYesNoList, scope.row[item.field]) }}
                    </el-tag>
                  </span>
                  <span v-else-if="item.field == 'IsSubcontract'" class="text-ellipsis">
                   <el-tag :type="scope.row[item.field] === '1' ? 'success' : 'danger'">
                      {{ showName(DFMYesNoList, scope.row[item.field]) }}
                    </el-tag>
                  </span>
                  <span v-else> {{ scope.row[item.field] }} </span>
                </template>
              </el-table-column>
              <el-table-column prop="operation" width="160" :label="$t('GLOBAL._ACTIONS')" align="center" fixed="right">
                <template slot-scope="scope">
                  <el-button size="mini" type="text" @click="showDialog(scope.row)">{{ $t('GLOBAL._BJ') }}</el-button>
                  <el-button size="mini" type="text" @click="delRow(scope.row)">{{ $t('GLOBAL._SC') }}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <el-tab-pane label="工艺关键参数" name="parameters">
          <process-parameters :sapSegmentMaterialId="sapSegmentMaterialId" />
        </el-tab-pane>

        <el-tab-pane label="工艺质检参数" name="quality">
          <quality-parameters :sapSegmentMaterialId="sapSegmentMaterialId" />
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
    <FormDialogDetail @saveForm="getTableData" ref="detail" />
  </div>
</template>

<script>
import { getSapSegmentMaterialStepList, deleteSapSegmentMaterialStep } from '@/api/productionManagement/Formula'
import FormDialogDetail from './form-dialog-detail.vue'
import ProcessParameters from '@/views/productionManagement/ProductBOMMaterialDetails/process-parameters.vue'
import QualityParameters from '@/views/productionManagement/ProductBOMMaterialDetails/quality-parameters.vue'

export default {
  name: 'drawer',
  components: {
    FormDialogDetail,
    ProcessParameters,
    QualityParameters
  },
  data() {
    return {
      searchForm: {},
      drawer: false,
      tableData: [],
      tableHead: [],
      DFMYesNoList: [],
      FeedPortList: [],
      PlantList: [],
      FeedTypeList: [],
      loading: false,
      hansObjDrawer: this.$t('Formula.Product_BOM_Material_Details'),
      sapSegmentMaterialId: 0,
      currentRow: {},
      activeTab: 'process',
      tableHeight: 500 // 默认高度，将在mounted和窗口调整时更新
      
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.calculateTableHeight()
      // 监听窗口大小变化，重新计算表格高度
      window.addEventListener('resize', this.calculateTableHeight)
    })
  },
  beforeDestroy() {
    // 移除事件监听器
    window.removeEventListener('resize', this.calculateTableHeight)
  },
  methods: {
    show(val) {
      this.initDictList()
      this.currentRow = val
      this.sapSegmentMaterialId = val.ID
      this.drawer = true
      this.initTableHead()
      this.getTableData()
      this.$nextTick(() => {
        // 等待抽屉动画完成后计算表格高度
        setTimeout(() => {
          this.calculateTableHeight()
        }, 300)
      })
    },
    handleClose() {
      this.drawer = false
      this.tableData = []
      this.tableHeight = 500 // 重置为默认高度
      this.activeTab = 'process' // 重置为默认标签页
      this.searchForm = {} // 清空搜索条件
    },
    handleTabClick() {
      // 切换标签页时重新计算表格高度
      this.$nextTick(() => {
        this.calculateTableHeight()
      })
    },
    async initDictList() {
      this.DFMYesNoList = await this.$getDataDictionary('DFMYesNo')
      this.FeedPortList = await this.$getDataDictionary('FeedPort')
      this.PlantList = await this.$getDataDictionary('DFM_PLANT')
      this.FeedTypeList = await this.$getDataDictionary('DFM_FEED_TYPE')
    },
    //过滤行数据
    showName(list, val) {
      if (!Array.isArray(list)) return '';
      const obj = list.find(e => e.ItemValue === val);
      return obj ? obj.ItemName : '';
    },
    async getTableData() {
      this.loading = true
      try {
        const { response } = await getSapSegmentMaterialStepList({
          SapSegmentMaterialId: this.sapSegmentMaterialId,
          ...this.searchForm
        })
        this.tableData = response
        
        // 数据加载完成后重新计算表格高度
        this.$nextTick(() => {
          this.calculateTableHeight()
        })
      } catch (error) {
        console.error('获取表格数据出错:', error)
      } finally {
        this.loading = false
      }
    },
    delRow({ ID }) {
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        const { msg } = await deleteSapSegmentMaterialStep([ID])
        this.$message.success(msg)
        this.getTableData()
      }).catch(err => {
        console.log(err);
      });
    },
    initTableHead() {
      this.tableHead = []
      for (let key in this.hansObjDrawer) {
        this.tableHead.push({ field: key, label: this.hansObjDrawer[key] })
      }
    },
    showDialog(row) {
      row.SapSegmentMaterialId = this.sapSegmentMaterialId
      this.$refs.detail.show(row)
    },
    handleSortChange({ prop, order }) {
      // 处理表格排序
      if (!prop || !order) {
        this.tableData = [...this.tableData]
        return
      }

      this.tableData.sort((a, b) => {
        let aValue = a[prop] === undefined || a[prop] === null ? '' : a[prop]
        let bValue = b[prop] === undefined || b[prop] === null ? '' : b[prop]

        // 处理字典值的显示
        if (prop === 'FeedType') {
          aValue = this.showName(this.FeedTypeList, aValue) || ''
          bValue = this.showName(this.FeedTypeList, bValue) || ''
        } else if (['NeedPrepare', 'NeedAdvancePrepare', 'IsSubcontract'].includes(prop)) {
          aValue = this.showName(this.DFMYesNoList, aValue) || ''
          bValue = this.showName(this.DFMYesNoList, bValue) || ''
        }

        // 如果值是数字，进行数字比较
        if (!isNaN(Number(aValue)) && !isNaN(Number(bValue))) {
          return order === 'ascending' ? Number(aValue) - Number(bValue) : Number(bValue) - Number(aValue)
        }

        // 字符串比较
        aValue = String(aValue).toLowerCase()
        bValue = String(bValue).toLowerCase()
        
        if (order === 'ascending') {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
        } else {
          return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
        }
      })
    },
    
    getColumnWidth(field) {
      // 根据字段类型返回合适的列宽
      const widthMap = {
        // 字典类型字段
        'SortOrder':70,
        'Unit':70,
        'Plant':70,
        'FeedType': 120,
        'NeedPrepare': 100,
        'NeedAdvancePrepare': 120,
        'IsSubcontract': 100,
        // 代码和名称字段通常需要更宽的空间
        'MaterialCode': 150,
        'MaterialName': 200,
        'ParentSegmentCode': 150,
        'ParentSegmentName': 200,
        // 数字类型字段
        'Quantity': 100,
        'AdjustPercentQuantity': 100,
        'ParentQuantity': 100,
        'Price': 100,
        // 默认宽度
        'default': 150
      }
      
      return widthMap[field] || widthMap.default
    },
    
    calculateTableHeight() {
      this.$nextTick(() => {
        try {
          if (!this.drawer) return
          
          // 获取窗口可视区域高度
          const windowHeight = window.innerHeight
          
          // 获取抽屉内容区域的高度和位置
          const drawerBody = document.querySelector('.el-drawer__body')
          if (!drawerBody) return
          
          // 获取搜索框的高度
          const searchBox = document.querySelector('.InventorySearchBox')
          const searchBoxHeight = searchBox ? searchBox.offsetHeight : 0
          
          // 获取标签页头部的高度
          const tabsHeader = document.querySelector('.el-tabs__header')
          const tabsHeaderHeight = tabsHeader ? tabsHeader.offsetHeight : 0
          
          // 获取标题的高度
          const titleBox = document.querySelector('.title-box')
          const titleBoxHeight = titleBox ? titleBox.offsetHeight : 0
          
          // 获取表格容器的位置
          const tableBox = document.querySelector('.table-box')
          const tableBoxTop = tableBox ? tableBox.getBoundingClientRect().top : 0
          
          // 计算表格可用高度 (从表格顶部到窗口底部)
          const padding = 20 // 底部边距
          const availableHeight = windowHeight - tableBoxTop - padding
          
          // 设置表格高度，确保最小高度为300px
          this.tableHeight = Math.max(availableHeight, 300)
          
          // 如果表格数据为空，确保表格仍然填充到底部
          if (this.tableData.length === 0) {
            // 添加额外的空间以确保表格底部在界面底部
            const emptyTableMinHeight = windowHeight - tableBoxTop - padding
            this.tableHeight = Math.max(emptyTableMinHeight, this.tableHeight)
          }
          
          console.log('Table height calculated:', this.tableHeight)
        } catch (error) {
          console.error('计算表格高度时出错:', error)
          // 出错时使用默认高度
          this.tableHeight = 500
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer {
  ::v-deep .el-drawer__body {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-top: 10px;
    background-color: #FFFFFF;
    overflow: hidden;

    .el-tabs {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%;

      .el-tabs__content {
        flex: 1;
        height: 100%;
        overflow: hidden;

        .el-tab-pane {
          height: 100%;
          display: flex;
          flex-direction: column;
        }
      }
    }
  }

  ::v-deep .el-form--inline {
    height: 32px;
  }

  .title-box {
    font-size: 18px;
    color: #909399;
    margin-bottom: 16px;
  }

  .pd5 {
    padding: 5px;
  }

  .table-box {
    padding: 0 16px;
    height: 100%;
    display: flex;
    flex-direction: column;

    ::v-deep .el-table {
      flex: 1;
      border-radius: 6px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
      border: 1px solid #e8e8e8;
      height: 100%;

      th {
        background-color: #f7f8fa;
        font-weight: 500;
        color: #303133;
      }

      tr:hover > td {
        background-color: #f5f7fa !important;
      }

      // 确保空数据时表格仍然填充空间
      .el-table__empty-block {
        min-height: 300px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
      }
    }

    i {
      margin-right: 5px;
      font-size: 15px !important;
      color: #67c23a;
    }
  }

  // 优化抽屉内容区域的布局
  ::v-deep .el-drawer__body {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-top: 10px;
    background-color: #FFFFFF;
    overflow: hidden;

    .el-tabs {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%;

      .el-tabs__content {
        flex: 1;
        height: 100%;
        overflow: hidden;

        .el-tab-pane {
          height: 100%;
          display: flex;
          flex-direction: column;
        }
      }
    }
  }

  ::v-deep .el-button {
    border-radius: 4px;
    padding: 8px 16px;

    &--success {
      background-color: #52c41a;
      border-color: #52c41a;

      &:hover {
        background-color: #73d13d;
        border-color: #73d13d;
      }
    }
  }

  ::v-deep .el-table__fixed-right {
    height: 100% !important;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.06);
  }
}
</style>
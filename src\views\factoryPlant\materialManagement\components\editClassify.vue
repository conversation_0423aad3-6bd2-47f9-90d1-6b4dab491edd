<template>
    <v-card>
        <v-card-title class="headline primary lighten-2" primary-title>{{ $t('DFM_WLGL._BJWLFL') }}</v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid">
                <v-row class="mt-4">
                    <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_WLGL.Code')" :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" required dense outlined v-model="form.Code"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_WLGL.Name')" :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" required dense outlined v-model="form.Name"></v-text-field>
                    </v-col>
                    <!-- <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <v-text-field label="厂别" :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" required dense outlined v-model="form.Plant"></v-text-field>
                    </v-col> -->
                    <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <!-- <v-text-field label="特征" :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" required dense outlined v-model="form.Plant"></v-text-field> -->
                        <v-select
                            :items="traitList"
                            item-text="ItemName"
                            item-value="ItemValue"
                            clearable
                            dense
                            v-model="form.Identities"
                            outlined
                            :label="$t('DFM_WLGL.traitName')"
                        />
                    </v-col>
                    <v-col class="pt-0 pb-0" :cols="12" :lg="12">
                        <v-textarea :label="$t('DFM_WLGL.Descriptions')" v-model="form.Description" :value="form.Description" outlined height="90"></v-textarea>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
        <v-divider></v-divider>

        <v-card-actions>
            <!-- <v-spacer></v-spacer> -->
            <v-checkbox class="mr-auto" v-model="isChecked" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { saveClassify, getDataDictionary } from '../service';
export default {
    props: {
        editClassifyObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            valid: false,
            isChecked: true,
            form: {
                Code: '',
                Name: '',
                Plant: '',
                Description: '',
                Identities: ''
            },
            traitList: []
        };
    },
    computed: {
        locale() {
            return this.$store.state.app.locale || 'zh';
        }
    },
    async created() {
        await this.getTraitList();
        if (this.editClassifyObj && this.editClassifyObj.ID) {
            for (const key in this.form) {
                this.form[key] = this.editClassifyObj[key] || this.form[key];
            }
            this.form.ID = this.editClassifyObj.ID;
        }
    },
    methods: {
        async getTraitList() {
            let resp = await getDataDictionary({ lang: 'cn', itemCode: 'MaterialClass' });
            this.traitList = resp.response;
            console.log(this.traitList);
        },
        resetForm() {
            this.$refs.form.reset();
        },
        closePopup() {
            this.$emit('closePopup');
        },
        async submitForm() {
            if (!this.$refs.form.validate()) return false;

            let resp = await saveClassify({ ...this.form });
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
            this.resetForm();
            this.$emit('getdata');
            if (this.isChecked) {
                this.isChecked = !this.isChecked;
                this.$emit('closePopup');
            }
        }
    }
};
</script>

<style>
</style>
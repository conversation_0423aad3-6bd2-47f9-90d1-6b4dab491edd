<template>
  <el-dialog
    :style="backgroundVar"
    :title="titlebarline"
    :append-to-body="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="value"
    :before-close="handleClose1"
    lock-scroll
    :fullscreen="true"
  >
    <div
      :id="id1 + '-123'"
      style="width: 100%;height: 800px;"
    >
    </div>
  </el-dialog>
</template>
<script>
import { getChartStructure } from '@/api/simConfig/simconfignew.js';
import { title } from 'echarts/lib/theme/dark';

export default {
  props: {
    // 是否显示弹出框
    value: {
      type: Boolean,
      default: false
    },
    position: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    id1: {
      type: String,
      default: ''
    },
    titlebarline: {
      type: String,
      default: ''
    },
    backgroundImg: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      lineLegend: ['实际值', '目标值'],
      //当前时间颗粒度
      curShift: {
        KpiValues: []
      },
      myShiftList: [],
      chartBartc: null,
      yAxisOption: {
        type: 'value',
        // show: false
        axisLine: {
          show: false
        },
        axisLabel: {
          show: true
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      },
    }
  },
  computed: {
    backgroundVar() {
      return {
        '--background': this.backgroundImg
      }
    },
    //x轴配置
    xAxisOption() {
      let list = ['7', '6', '5', '4', '3', '2', '1']
      if (this.curShift.ChartData && this.curShift.ChartData.x) {
        list = this.curShift.ChartData.x.map(item => {
          // let month = dayjs(item).$M+1
          // let day = dayjs(item).$D
          if (['日', '周'].includes(this.curShift.TimeDimension)) {
            let month = item.split('-')[1]
            let day = item.split('-')[2]
            // 这里需要匹配颗粒度,来输出不同的x轴数据.
            // return `${month}-${day}`
            return `${month}-${day}`
          } else if (['月', '季度'].includes(this.curShift.TimeDimension)) {
            let label = item.split('年')[1]
            return label
          } else {
            return item
          }
        })
        // list.reverse()
        // list = this.curShift.ChartData.x
      }
      return {
        type: 'category',
        // boundaryGap: false,
        axisLine: {
          onZero: false,
          lineStyle: {
            color: '#fff'
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff'
        },
        data: list
      }
    },
    lineSeries() {
      //区分横竖
      let axisMarkLine = this.curConfig.ChartType === '4'
        ? [{ xAxis: this.curShift.TargetValue || '' }]
        : [{ yAxis: this.curShift.TargetValue || '' }]

      // let obj2 = {
      //   name: `${this.curShift.KpiName}实际值`,
      //   type: ['2','3'].includes(this.curConfig.ChartType)?'bar':'line',
      //   symbol: 'circle',
      //   symbolSize: 4,
      //   data: this.curShift.KpiValues.map(item=>item.DataValue),
      //   markLine: {//目标值线条
      //     silent: true,
      //     lineStyle: {
      //       color: this.curShift.TargetColor || 'gray'
      //       // color: 'red'
      //     },
      //     data: axisMarkLine
      //     // data: [{xAxis: 20 }]
      //   }
      // }
      let list = []
      Object.keys(this.curShift.ChartData).forEach(key => {
        if (['x', 'x', '目标值'].includes(key)) {
          return
        }
        let obj = {
          // name: `${this.curShift.KpiName}实际值`,
          // name: key.split(':')[1],
          name: `${key}实际值`,
          type: ['2', '3'].includes(this.curConfig.ChartType) ? 'bar' : 'line',
          symbol: 'circle',
          symbolSize: 4,
          barWidth: 10,
          itemStyle: {
            normal: {
              color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                offset: 0,
                color: "#4391F4" // 0% 处的颜色
              }, {
                offset: 1,
                color: "#6B74E4" // 100% 处的颜色
              }], false),
            }
          },
          // data: this.curShift.KpiValues.map(item=>item.DataValue),
          data: this.curShift.ChartData[key],
          markLine: {//目标值线条
            silent: true,
            lineStyle: {
              color: this.curShift.TargetColor || 'gray'
              // color: 'red'
            },
            data: axisMarkLine
            // data: [{xAxis: 20 }]
          }
        }
        list.push(obj)
      })
      return list
    },
  },
  created() {
    this.getBarList()
  },
  methods: {
    async getBarList() {
      let params = {
        "Position": this.position,
        "BaseTime": this.BaseTime,
        "TeamCode": this.simlevel,
        // "ProductionLineCode": this.ProductionLineCode,
        // "FactoryCode": this.FactoryCode
      }
      let { response } = await getChartStructure(params)
      this.curConfig = response
      if (this.curConfig.ChartConfigs.length > 0) {
        // this.id = this.curConfig.ID;
        // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
        this.curConfig.ChartConfigs.map(item => {
          item.KpiName = this.curConfig.ChartConfigs.KpiName
          if (item.KpiValues[0]) {
            item.KpiCode = item.KpiValues[0].KpiCode
            item.TargetValue = item.KpiValues[0].TargetValue || 0
          }
        })
        //图表配置整体赋值
        // this.curConfig = response
        //时间颗粒度列表
        this.myShiftList = this.curConfig.ChartConfigs.filter(item => {
          return item.TargetVisible === 1
        })
        //默认激活第一个时间颗粒度
        this.curShift = this.myShiftList[0]
        this.query1()
      }
    },
    query1() {
      if (this.chartBartc) {
        this.chartBartc.clear()
        return
      }
      this.chartBartc = this.$echarts.init(document.getElementById(this.id1 + '-123'));
      var option
      option = {
        symbol: 'circle',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          confine: true,
          extraCssText: 'max - width: none; overflow: visible;',
        },
        legend: {
          data: this.lineSeries.map(item => item.name),
          textStyle: {
            color: '#fff'
          }
        },
        grid: {
          left: '5%',
          right: '6%',
          bottom: '3%',
          top: '20%',
          containLabel: true
        },

        // visualMap: this.lineVisualMap,
        xAxis: this.xAxisOption,
        yAxis: {
          type: 'value',
          // show: false
          axisLine: {
            show: true,
            lineStyle: {
              color: '#fff'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff" //X轴文字颜色
            },
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        series: this.lineSeries
      }
      this.chartBartc.setOption(option, true);
      window.addEventListener("resize", () => {
        this.chartBartc.resize()
      }, false);
    },
    handleClose1() {
      this.$emit('showCheck3')
    }
  }
}
</script>
<style scoped>
/deep/ .el-dialog__title {
    color: #fff !important;
}
/deep/ .el-dialog {
    background: var(--background) no-repeat 0 0;
    background-size: 100% 100% !important;
    overflow: hidden;
}
.active {
    display: flex;
    justify-content: space-between;
    color: #fc5531;
    cursor: pointer;
}
.active1 {
    display: flex;
    justify-content: space-between;
    color: black;
    cursor: pointer;
}
/deep/ .el-textarea__inner {
    font-size: 16px !important;
}
/deep/ .el-dialog__headerbtn .el-dialog__close {
    font-size: 22px;
}
/deep/ .el-dialog__header {
    border: none !important;
}
/* /deep/.el-dialog.is-fullscreen {
  background: url("https://img0.baidu.com/it/u=1932479095,3119701408&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=400")
    no-repeat 0 0;
  background-size: 100% 100%;
} */
/deep/.el-textarea__inner {
    background-color: rgba(225, 225, 225, 0);
    border: none !important;
    color: black;
    font-size: 16px;
    /* font-weight: bold; */
    /* font-family: "Lucida Calligraphy", cursive, serif, sans-serif; */
}
.father {
    width: 100%;
    position: relative;
    /* background: url("../image/bk4.png") no-repeat 0 0;
  background-size: 100% 100%; */
}
.tBox {
    width: 100%;
    height: 10%;
    /* background-image: url('./default-bg_8e8654d.png'); */
    background-repeat: no-repeat;
    background-size: 150%;
    background-position: center center;
    padding: 15px 40px;
    box-sizing: border-box;
}
.cBox {
    width: 96%;
    height: 13%;
    /* border-radius: 12px; */
    /* box-shadow: 0px 0px 10px 0px #ccc; */
    font-size: 18px;
    word-wrap: break-word;
    word-break: break-all;
    margin: 10px auto;
    padding: 10px 10px;
    box-sizing: border-box;
}
.fBox {
    width: 96%;
    height: 50%;
    /* border-radius: 12px; */
    /* box-shadow: 0px 0px 10px 0px #ccc; */
    font-size: 20px;
    word-wrap: break-word;
    word-break: break-all;
    margin: 10px auto;
    padding: 10px 10px;
    box-sizing: border-box;
}
/* .fade-enter-active,
.fade-leave-active {
  transition: all 0.2s linear;
  transform: translate3D(0, 0, 0);
}
.fade-enter,
.fade-leave-active {
  transform: translate3D(100%, 0, 0);
} */
.img-view {
    position: relative;
    width: 100%;
    height: 100%;
}
.img-view .img-layer {
    position: fixed;
    z-index: 999;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.7);
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.img-view .img img {
    max-width: 100%;
    display: block;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    z-index: 1000;
    top: -1200px;
    /* margin-top: -1200px; */
}
.knoeBox {
    width: 97%;
    height: auto;
    margin: 0 auto;
}
.serachBox {
    width: 100%;
    height: 5%;
    /* position: fixed; */
    /* z-index: 999; */
    /* background-color: #fff; */
    padding-left: 5%;
    box-sizing: border-box;
}
.textBox {
    width: 100%;
    height: 95%;
    display: flex;
    /* justify-content: space-around; */
    /* padding-top: 45px; */
    box-sizing: border-box;
    padding-left: 6%;
}
.knoeBox_l {
    width: 26%;
    height: 100%;
    padding: 0px 5px;
    box-sizing: border-box;
    margin-right: 30px;
}
.knoeBox_c {
    width: 65%;
    height: 100%;
    padding: 5px 5px;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0px 0px 10px 0px #ccc;
}
/* .knoeBox_r {
  width: 22%;
  height: 100%;
} */
.knoeBox_l_t {
    /* width: 100%; */
    /* height: 50%; */
    padding: 5px 5px;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0px 0px 10px 0px #ccc;
    margin-bottom: 15px;
}
.knoeBox_l_b {
    width: 100%;
    height: 50%;
    padding: 5px 5px;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0px 0px 10px 0px #ccc;
    overflow: hidden;
}
.rmwz {
    width: 100%;
    height: 30px;
    line-height: 30px;
    background-color: #eaf5fd;
    font-size: 16px;
    font-weight: bold;
    padding-left: 5px;
    box-sizing: border-box;
    color: black;
}
.wztitle {
    width: 98.5%;
    height: 30px;
    font-size: 16px;
    line-height: 30px;

    /* font-weight: bold; */
}
/* .wztitle:hover {
  color: #fc5531;
  cursor: pointer;
} */
.knoeBox_c_title {
    width: 100%;
    height: 30px;
    font-size: 26px;
    font-weight: bold;
    line-height: 40px;
    text-align: center;
    color: black;
}
.knoeBox_c_fen_l {
    width: 100%;
    height: 40px;
    line-height: 40px;
    font-size: 20px;
    /* background-color: #ccc; */
    margin-top: 12px;
    color: black;

    /* font-weight: bold; */
    padding-left: 12px;
}
.knoeBox_c_nr {
    width: 100%;
    height: auto;
    font-size: 16px;
    margin-top: 20px;

    /* font-weight: bold; */
}
/deep/ .el-dialog__headerbtn .el-dialog__close {
    font-size: 22px;
}
/deep/ .el-dialog__header {
    border: none !important;
}
/* /deep/.el-dialog.is-fullscreen {
  background: url("https://img0.baidu.com/it/u=1932479095,3119701408&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=400")
    no-repeat 0 0;
  background-size: 100% 100%;
} */
/deep/.el-textarea__inner {
    /* background-color: rgba(225, 225, 225, 0); */
    background-color: #fff !important;
    border: none !important;
    color: black;
    font-size: 20px;
    /* font-weight: bold; */
}
/* .img-view {
  position: relative;
  width: 100%;
  height: 100%;
}
.img-view .img-layer {
  position: fixed;
  z-index: 99999;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.img-view .img img {
  max-width: 100%;
  display: block;
  position: absolute;
  left: 0;
  right: 0;
  z-index: 999999;
  margin: 0 auto;
  margin-top: -1600px;
} */
</style>
import Vue from 'vue';
import store from '../store';
import Sortable from 'sortablejs'
import VXETable from 'vxe-table'

//自定义指令 v-has进行权限判断
Vue.directive('has', {
  inserted: function (el, binding) {
    //按钮权限
    const data = store.getters.getBtnList || [];
    const value = binding.value;

    let btnState = data.find((item) => value == item.CnCode)
    // const hasPermissions =JSON.stringify(data).includes(value);
    if (!btnState) {
      console.log(el,2323)
      //隐藏按钮
      el.style.display = 'none'
      setTimeout(() => {
        el.parentNode.removeChild(el);
      }, 0);
    }
  }
});
Vue.directive('inputhas', {
  inserted: function (el, binding) {
    console.log(123)
    //按钮权限
    const data = store.getters.getBtnList || [];
    const value = binding.value;
    // data.forEach(item => {
    //   if (item.CnCode == 'KCMX_SL') {
    //     console.log(item)
    //   }
    // })
    let btnState = data.find((item) => value == item.CnCode)
    // const hasPermissions =JSON.stringify(data).includes(value);
    if (!btnState) {
      if (el.nodeName != 'INPUT') {
        console.log(el)
        el.classList.add('is-disabled notEvent');
        el.childNodes.forEach(item => {
          if (item.nodeName == 'INPUT') {
            item.disabled = 'disabled'
          }
        })
      } else {
        el.disabled = 'disabled'
      }
    }
  }
});
Vue.directive('drag', {
  //1.指令绑定到元素上回立刻执行bind函数，只执行一次
  //2.每个函数中第一个参数永远是el，表示绑定指令的元素，el参数是原生js对象
  //3.通过el.focus()是无法获取焦点的，因为只有插入DOM后才生效
  bind: function (el) {
    // el.style.cursor = "move"; //鼠标样式变move样式
  },
  //inserted表示一个元素，插入到DOM中会执行inserted函数，只触发一次
  inserted: function (el) {
    el.onmousedown = function (e) {
      const elp = el.parentNode.parentNode;
      elp.style.transition = 'none';
      var distX = e.pageX - elp.offsetLeft;
      var distY = e.pageY - elp.offsetTop;
      console.log("元素本身的高：" + elp.clientHeight + ",元素本身的宽：" + elp.clientWidth)

      if (e.preventDefault) {
        e.preventDefault();
      } else {
        e.returnValue = false;
      };
      //解决快速拖动滞后问题

      document.onmousemove = function (e) {
        // 用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
        let left = e.pageX - distX;
        let top = e.pageY - distY;
        if (left <= 0) {
          left = 5; //设置成5是为了不离边缘太近
        } else if (left > document.documentElement.clientWidth - el.clientWidth) {
          //document.documentElement.clientWidth 屏幕的可视宽度
          left = document.documentElement.clientWidth - el.clientWidth - 5
        }
        if (top <= 0) {
          top = 5;
        } else if (top > document.documentElement.clientHeight - el.clientHeight) {
          top = document.documentElement.clientHeight - el.clientHeight - 5
        }
        el.parentNode.parentNode.style.position = "absolute";
        el.parentNode.parentNode.style.left = left.toFixed(0) + 'px';
        el.parentNode.parentNode.style.top = top.toFixed(0) + 'px';
      }
      document.onmouseup = function () {
        document.onmousemove = document.onmouseup = null;
      }
    }
  },
  //当VNode更新的时候会执行updated，可以触发多次
  updated: function (el) { }
})


//自定义指令 v-hasbtn进行权限判断
Vue.directive('hasbtn', {
  inserted: function (el, binding) {
    //按钮权限
    const data = store.getters.getBtnList || [];
    const value = binding.value;
    let btnState = data.find((item) => value == item.CnCode)
    // const hasPermissions =JSON.stringify(data).includes(value);
    if (!btnState && btnState !== undefined) {
      //隐藏按钮
      el.disabled = 'true';
      // el.classList.add('v-btn--disabled')
      el.children[0].style.color = '#aaa'
    }
  }
}
)

// 自定义表格拖拽列指令
Vue.directive('drag-column', {
  inserted: function (el, binding, vnode) {
    Vue.nextTick(() => {
      const $table = vnode.elm.__vue__
      if (!($table.columnConfig && $table.columnConfig.useKey)) {
        console.error('由于直接操作了DOM节点，需要与Vue的数据同步，必须设置column-config.useKey为true,并且自行根据Vue的规则自行实现数据同步')
        return false
      }
      Sortable.create($table.$el.querySelector('.body--wrapper>.vxe-table--header .vxe-header--row'), {
        handle: '.vxe-header--column',
        onEnd: ({ item, newIndex, oldIndex }) => {
          const { fullColumn, tableColumn } = $table.getTableColumn()
          const targetThElem = item
          const wrapperElem = targetThElem.parentNode
          const newColumn = fullColumn[newIndex]
          if (newColumn.fixed || fullColumn[oldIndex].fixed) {
            const oldThElem = wrapperElem.children[oldIndex]
            // 错误的移动
            if (newIndex > oldIndex) {
              wrapperElem.insertBefore(targetThElem, oldThElem)
            } else {
              wrapperElem.insertBefore(targetThElem, oldThElem ? oldThElem.nextElementSibling : oldThElem)
            }
            VXETable.modal.message({ content: '固定列不允许拖动，即将还原操作！', status: 'error' })
            return
          }
          // 获取列索引 columnIndex > fullColumn
          const oldColumnIndex = $table.getColumnIndex(tableColumn[oldIndex])
          const newColumnIndex = $table.getColumnIndex(tableColumn[newIndex])
          // 移动到目标列
          const currRow = fullColumn.splice(oldColumnIndex, 1)[0]
          fullColumn.splice(newColumnIndex, 0, currRow)
          $table.loadColumn(fullColumn)
        }
      })
    }, 1000)
  }
})
export const combinebatchColum = [
    // {
    //     text: '序号',
    //     value: 'Index',
    //     sortable: true
    // },
    {
        text: '产线',
        value: 'FullLineName',
        width: 140,
        sortable: true
    },
    // {
    //     text: '产线',
    //     value: 'CompanyName',
    //     width: 140,
    //     sortable: true
    // },
    {
        text: '产品料号',
        value: 'MaterialCode',
        width: 150,
        sortable: true
    },
   
    {
        text: '产品描述',
        value: 'MaterialDescription',
        width: 200,
        sortable: true
    }, 
    {
        text: '班组',
        width: 90,
        value: 'TeamName',
        sortable: true
    },
    {
        text: '班次',
        width: 90,
        value: 'ShiftName',
        sortable: true
    },
    
    {
        text: '批次号',
        width: 100,
        value: 'BatchNo',
        sortable: true
    },
    {
        text: '批次数量(PCS)',
        width: 140,
        value: 'BatchQuantity',
        sortable: true
    },
    // {
    //     text: '类型',
    //     width: 80,
    //     value: 'TestGrade',
    //     sortable: true
    // },
    {
        text: '合批时间',
        value: 'CreateDate',
        width: 180,
        sortable: true
    },
    { text: '操作', width: 100, align: 'center', value: 'actions' }
];
export const combinebatchlistColum = [
    // {
    //     text: '序号',
    //     value: 'Index',
    //     sortable: true
    // },
    {
        text: '产线',
        value: 'CompanyName',
        width: 140,
        sortable: true
    },
    {
        text: '产品料号',
        value: 'MaterialCode',
        width: 120,
        sortable: true
    },
    {
        text: '班组',
        value: 'TeamName',
        width: 70,
        sortable: true
    },
    {
        text: '班次',
        value: 'ShiftName',
        width: 80,
        sortable: true
    },
    {
        text: '批次号',
        value: 'BatchNo',
        width: 100,
        sortable: true
    },
    {
        text: '类型',
        width: 100,
        value: 'TestGrade',
        sortable: true
    },
    {
        text: '批次数量(PCS)',
        width: 140,
        value: 'RemainQuantity',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: '合并数量(PCS)',
        isEditCell: true,
        width: 140,
        value: 'WoQuantity',
        sortable: true
    },
    { text: '操作',width: 100, align: 'center', value: 'actions', sortable: true }
];

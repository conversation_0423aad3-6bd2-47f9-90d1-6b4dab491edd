<template>
    <v-card>
        <v-card-title class="headline primary lighten-2" primary-title>{{ $t('DFM_GYLX._TJGYLXXX') }}</v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid">
                <v-row class="mt-5">
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX._GC')" disabled dense outlined v-model="form.Factory"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select
                            :items="RoutingTypeList"
                            item-text="ItemName"
                            item-value="ItemValue"
                            no-data-text="暂无数据"
                            clearable
                            dense
                            v-model="form.RoutingType"
                            outlined
                            :label="$t('DFM_GYLX._LX')"
                            placeholder="请选择工艺类型"
                        />
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX._LXDM')" :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" required dense outlined v-model="form.RoutingCode"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX._LXMC')" :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" required dense outlined v-model="form.RoutingName"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_GYLX._LXBB')" dense outlined v-model="form.Version"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-menu ref="menu1" v-model="menu1" :close-on-content-click="false" transition="scale-transition" offset-y max-width="290px" min-width="290px">
                            <template #activator="{ on, attrs }">
                                <v-text-field v-model="form.EffectStart" readonly :label="$t('DFM_GYLX.EffectStart')" persistent-hint outlined dense v-bind="attrs" v-on="on"></v-text-field>
                            </template>
                            <v-date-picker v-model="form.EffectStart" no-title @input="menu1 = false"></v-date-picker>
                        </v-menu>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-menu ref="menu2" v-model="menu2" :close-on-content-click="false" transition="scale-transition" offset-y max-width="290px" min-width="290px">
                            <template #activator="{ on, attrs }">
                                <v-text-field v-model="form.EffectEnd" readonly :label="$t('DFM_GYLX.EffectEnd')" persistent-hint outlined dense v-bind="attrs" v-on="on"></v-text-field>
                            </template>
                            <v-date-picker v-model="form.EffectEnd" no-title @input="menu2 = false"></v-date-picker>
                        </v-menu>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select
                            :items="RoutingStatusList"
                            item-text="ItemName"
                            item-value="ItemValue"
                            no-data-text="暂无数据"
                            clearable
                            dense
                            v-model="form.Status"
                            outlined
                            :label="$t('DFM_GYLX.Status')"
                            placeholder="请选择状态"
                        />
                    </v-col>
                    <v-col :cols="12" :lg="12" class="pt-0 pb-0">
                        <v-textarea :label="$t('DFM_GYLX.Description')" v-model="form.Description" outlined height="70"></v-textarea>
                    </v-col>
                    <v-col class="pt-0 pb-0" :cols="12" :lg="12">
                        <v-textarea :label="$t('DFM_GYLX.Notes')" v-model="form.Notes" outlined height="70"></v-textarea>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>

        <v-divider></v-divider>

        <v-card-actions>
            <!-- <v-spacer></v-spacer> -->
            <v-checkbox class="mr-auto" v-model="isChecked" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
// import DatePicker from '@/components/DatePicker.vue';
import { saveOperationalPath, getDataDictionary } from '../service';
export default {
    components: {},
    props: {
        selectPathObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            valid: false,
            menu1: false,
            menu2: false,
            isChecked: true,
            RoutingTypeList: [],
            RoutingStatusList: [],
            form: {
                Factory: '*********',
                RoutingType: '',
                RoutingCode: '',
                RoutingName: '',
                Version: '',
                EffectStart: '',
                EffectEnd: '',
                Status: '',
                Notes: '',
                Description: ''
            }
        };
    },
    created() {
        if (this.selectPathObj && this.selectPathObj.ID) {
            for (const key in this.form) {
                this.form[key] = this.selectPathObj[key];
            }
            this.form.ID = this.selectPathObj.ID;
        }
        this.getRoutingTypeList();
        this.getRoutingStatusList();
    },
    methods: {
        parseDate(date) {
            if (!date) return null;
            const [month, day, year] = date.split('/');
            return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        },
        closePopup() {
            this.$emit('handlePopup', false, 'path');
        },
        resetForm() {
            this.form = {
                Factory: '*********',
                RoutingType: '',
                RoutingCode: '',
                RoutingName: '',
                Version: '',
                EffectStart: '',
                EffectEnd: '',
                Status: '',
                Notes: '',
                Description: ''
            };
        },
        // 工艺类型列表
        async getRoutingTypeList() {
            let resp = await getDataDictionary({ itemCode: 'ROUTING_TYPE', lang: 'en' });
            this.RoutingTypeList = resp.response;
        },
        // 工艺状态列表
        async getRoutingStatusList() {
            let resp = await getDataDictionary({ itemCode: 'DFMActiveStatus', lang: 'en' });
            this.RoutingStatusList = resp.response;
        },
        async submitForm() {
            if (!this.$refs.form.validate()) return false;

            let resp = await saveOperationalPath({ ...this.form });
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
            this.resetForm();
            this.$emit('getdata');
            if (this.isChecked) {
                this.isChecked = !this.isChecked;
                this.$emit('handlePopup', false, 'path');
            }
        }
    }
};
</script>

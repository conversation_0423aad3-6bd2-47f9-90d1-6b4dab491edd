// 告警记录 dictionary: true
export const AnalysisofAlarmCausesColumns = [
    {
        text: '报警原因',
        value: 'SubAlarmType',
        width: 130
    },
    { text: '数量', value: 'Frequency', width: 80 },
    { text: '累计占比', value: 'CumulativePercentage', width: 100 },
    { text: '单项占比', value: 'SingleItemPercentage', width: 100 },
    { text: '平均响应时长', value: 'ResponseTime', width: 160 },
    { text: '平均处理时长', value: 'ProcessTime', width: 160 },
];


export const ClassificationColumns = [
    {
        text: '安灯类型',
        value: 'MainAlarmType',
        width: 150
    },
    // { text: '待接警', value: 'djj', width: 100 },
    // { text: '已接警', value: 'yjj', width: 100 },
    // { text: '已处理', value: 'ycl', width: 100 },
    // { text: '已关闭', value: 'ygb', width: 100 },
    // { text: '合计', value: 'hj', width: 160 },
];

<template>
    <v-dialog v-model="dialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                安灯事件列表
                <v-icon @click="closePopup">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="mt-2">
                <v-form ref="form" class="mt-8 mb-2">
                    <v-row>
                        <v-col :cols="12" :lg="6" v-for="(item, k) in list" :key='k'>
                            <v-text-field readonly v-model="item.value" :label="item.name" required dense outlined />
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script>
import { AlarmRecordGetEntity } from '@/api/andonManagement/alarmRecord.js';
import { getAlarmTypeTreetList } from '@/api/andonManagement/alarmType.js';
import { getReasonInfoList } from '@/api/factoryPlant/reasonInfo.js';
import { GetListByLevel, EquipmentGetEquipmentTree } from '@/api/common.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            dialog: false,
            problemLevelList: [],
            reasonList: [],
            equipmentList: [],
            productLine: [],
            alarmTypeList: [],
            list: [
                {name: '工段', value: '', key: 'ProductLine'},
                {name: '工站', value: '', key: 'EquipmentCode'},
                {name: '一级分类', value: '', key: 'MainAlarmType'},
                {name: '二级分类', value: '', key: 'SubAlarmType'},
                {name: '订单号', value: '', key: 'Wo'},
                {name: '事件等级', value: '', key: 'EventLevel'},
                {name: '问题等级', value: '', key: 'ProblemLevel'},
                {name: '告警内容', value: '', key: 'AlarmContent'},
                {name: '原因(损耗)', value: '', key: 'ReasonCode'},
                // {name: '人工数量(损耗)', value: '', key: ''},
                // {name: '设备数量(损耗)', value: '', key: ''},
                // {name: '单位(损耗)', value: '', key: ''},
                {name: '关警内容', value: '', key: 'Comment'}
            ]
        };
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal) {
                    // console.log(this.operaObj);
                    this.getData();
                }
            },
            deep: true,
            immediate: true
        }
    },
    async created(){
        this.problemLevelList = await this.$getDataDictionary('problemLevel');
        this.getReasonList();
        this.GetFactoryTree();
        this.GetListByLevel();
        this.getalarmTypeList();
    },
    methods: {
        async getData() {
            const res = await AlarmRecordGetEntity({ id: this.operaObj.AlarmEventId });
            const { success, response } = res;
            if (success) {
                this.list.forEach(e => {
                    let str = ''
                    switch (e.key) {
                        case 'ProductLine':
                            str = this.getValue(this.productLine, response[e.key], 'EquipmentCode', 'EquipmentName')
                            break;
                        case 'EquipmentCode':
                            str = this.getValue(this.equipmentList, response[e.key], 'value', 'name')
                            break;
                        case 'MainAlarmType':
                            str = this.getValue(this.alarmTypeList, response[e.key], 'AlarmCode', 'AlarmName')
                            break;
                        case 'SubAlarmType':
                            str = this.getValue(this.alarmTypeList, response[e.key], 'AlarmCode', 'AlarmName')
                            break;
                        case 'ProblemLevel':
                            str = this.getValue(this.problemLevelList, response[e.key], 'ItemValue', 'ItemName')
                            break;
                        case 'ReasonCode':
                            str = this.getValue(this.reasonList, response[e.key], 'ReasontreeCode', 'ReasontreeName')
                            break;
                    
                        default: 
                            break;
                    }
                    e.value = str || response[e.key]
                });
            }
        },
        closePopup() {
            this.dialog = false;
        },
        getValue(arr, k, key, value) {
            const val = this.$getDictionaryVal(k, arr, key, value)
            console.log(val);
            return val
        },
        // 获取原因树
        async getReasonList() {
             let params = {
                key: "",
                pageIndex: 1,
                pageSize: 999
            };
            const res = await getReasonInfoList(params);
            const { success, response } = res;
            if(success){
                this.reasonList = response.data || []
            }
        },
         // 获取树形数据
        async GetFactoryTree() {
            const res = await EquipmentGetEquipmentTree();
            let { success, response } = res;
            this.equipmentList = []
            if(response && success) {
                this.getChild(response)
            }
        },
        // 递归
        getChild(o) {
            o.forEach(e => {
                this.equipmentList.push(e);
                if (e.children && e.children.length) {
                    this.getChild(e.children);
                }
            });
        },
        // 获取工段
        async GetListByLevel() {
            let params = {
                key: 'ProductLine' // 工段
            };
            this.productLine = []
            const res = await GetListByLevel(params);
            let { success, response } = res;
            if (success) {
                this.productLine = response;
            }
        },
        // 获取告警类型列表
        async getalarmTypeList() {
            const res = await getAlarmTypeTreetList({});
            this.alarmTypeList = []
            const { success, response } = res || {};
            if(response && success) {
                response.forEach(e=>{
                    this.alarmTypeList.push(e)
                    const { children } = e
                    if(children && children.length){
                        children.forEach(i=>{
                            this.alarmTypeList.push(i)
                        })
                    }
                })
            }
        },
    }
};
</script>

<style lang="scss" scoped>
::v-deep .v-sheet.v-card:not(.v-sheet--outlined) {
    box-shadow: none;
}
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>
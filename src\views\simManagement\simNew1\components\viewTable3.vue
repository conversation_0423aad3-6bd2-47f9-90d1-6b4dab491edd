<template>
  <div class="list-box">
    <div
      style="position: relative;"
      v-for="(item, index) in cardData"
      :key="index"
      class="item-box"
    >
      <div style="display: flex;">
        <!-- <div
          v-if="item.title"
          style="width: 35px;height:35px;margin-top: 6px;margin-right: 10px;"
        >
          <img
            style="width: 100%;height: 100%;"
            src="../image/hb5.png"
          />
        </div> -->
        <div
          v-if="item.title"
          class="item-1"
          @click="openPopup(item)"
        >
          <p style="font-size: 18px;font-weight: bold;color: #409eff;">{{ item.title }}</p>
          <p style="font-weight:600;font-size:20px;color: #4CD137;">{{ item.num }}</p>
        </div>
        <div
          v-else
          class="item-2"
          @click="add(item)"
        >
          +
        </div>
      </div>

      <v-icon
        class="mr-2 delete-btn"
        v-if="item?.KpiChartId"
        style="cursor: pointer;"
        @click="handleDel(item,index)"
      >mdi-delete</v-icon>
      <v-icon
        class="mr-2 delete-btn"
        v-if="item?.KpiChartId"
        style="cursor: pointer;position: absolute;top:60%;left:82%;"
        @click="add(item,index)"
      >+</v-icon>
    </div>
    <!-- 新增 -->
    <v-dialog
      v-model="showAddDialog"
      scrollable
      persistent
      width="55%"
    >
      <AddDialog
        v-if="showAddDialog"
        :KpiChartId="KpiChartId"
        :searchFormObj="searchFormObj"
        @closePopup="closePopup"
        :Position="currentPosition"
        @addListFn="getdata"
      ></AddDialog>
    </v-dialog>
    <keyIndicatorslist
      ref="keyIndicatorsref"
      :exhibitionType="exhibitionType"
      :jtitle="jtitle"
      :simlevel="simlevel"
      :Order="Order1"
      :isSql="isSql"
      :BaseTime="BaseTime"
    ></keyIndicatorslist>

    <keyIndicatorslistnew
      ref="keyIndicatorsrefnews"
      v-if="keyIndicatorslistnewShow"
      v-model="showkeyIndicatorslistnew"
      :exhibitionType="exhibitionType"
      :jtitle="jtitle"
      :simlevel="simlevel"
      :Order="Order1"
      :isSql="isSql"
      :BaseTime="BaseTime"
      @keynew="heandleKey"
      :titlekey="titlekey"
      :backgroundImg="backgroundImg"
      :tbName="tbName"
    ></keyIndicatorslistnew>

    <viewTableAddnew
      v-if="viewTableAddnewshow"
      v-model="showviewTableAddnew"
      :KpiChartId="KpiChartId"
      :Order="Position"
      :searchFormObj="searchFormObj"
      @closePopup="closePopup"
      :Position="currentPosition"
      @addListFn="getdata"
      :fullscreen="fullscreen"
      :backgroundImg="backgroundImg"
    ></viewTableAddnew>
  </div>
</template>
<script>
import { GetKpiList, SaveConfig, GetChartStructure, delChartConfig } from '@/views/simManagement/components/chartsConfig/service.js';
import { QueryResultBySql, QuerypositionResultBySql } from '@/views/kpi/modelManagement/service.js';

export default {
  name: "ViewTable3",
  props: {
    list: {
      type: Array,
      default: () => []
    },
    searchFormObj: {
      type: Object,
      default: () => { }
    },
    Position: {
      type: String,
      default: ''
    },
    configFlag: {
      type: String,
      default: ''
    },
    exhibitionType: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    Order: {
      type: String,
      default: ''
    },
    fullscreen: {
      type: Boolean,
      default: false
    },
    titlekey: {
      type: String,
      default: ''
    },
    backgroundImg: {
      type: String,
      default: ''
    }
  },
  components: {
    AddDialog: () => import('@/views/simManagement/simNew1/components/viewTableAdd.vue'),
    keyIndicatorslist: () => import('@/views/simManagement/simNew1/components/keyIndicatorslist.vue'),
    keyIndicatorslistnew: () => import('@/views/simManagement/simNew1/components/keyIndicatorslistnew.vue'),
    viewTableAddnew: () => import('@/views/simManagement/simNew1/components/viewTableAddnew.vue'),
  },
  data() {
    return {
      tbName: '',
      itemPosition: '',
      viewTableAddnewshow: false,
      showviewTableAddnew: false,
      keyIndicatorslistnewShow: false,
      showkeyIndicatorslistnew: false,
      keyTitle: '',
      KpiChartId: '',
      currentPosition: '',
      cardData: [{}, {}, {}, {}, {}, {}, {}, {}, {}],
      showAddDialog: false,
      jtitle: '',
      Order1: '',
      isSql: ''
    }
  },
  watch: {
    searchFormObj: {
      handler(nv) {
        this.getdata()
      },
      deep: true
    }
  },
  computed: {

  },
  created() {
    this.cardData.map((item, index) => {
      item.Position = this.Position + (index + 1)
      return item
    })
    // this.getSQLFn()
    this.getdata()
  },
  methods: {
    heandleKey() {
      this.keyIndicatorslistnewShow = false
    },
    openPopup(item) {
      if (item.IsPopu == 1) {
        this.jtitle = item.title
        this.Order1 = item.Position
        this.isSql = item.IsSql
        this.tbName = '九宫格'
        // this.$refs.keyIndicatorsref.flagChange()
        // this.$refs.keyIndicatorsref.showDialog = true;
        // this.$refs.keyIndicatorsrefnew.flagChange()
        this.keyIndicatorslistnewShow = true
        this.showkeyIndicatorslistnew = true
      }
    },
    // 删除图标配置
    handleDel(item, index) {
      this.itemPosition = item.Position
      if (!item.KpiChartId) return false
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._SCTIPS'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      })
        .then(async () => {
          var that = this
          let resp = await delChartConfig([item.KpiChartId]);
          that.$store.commit('SHOW_SNACKBAR', { text: that.$t('GLOBAL._SCCG'), color: 'success' });
          // that.$set(that.cardData, index, that.itemPosition)
          that.$set(item, 'KpiChartId', '');
          that.$set(item, 'IsPopu', '');
          that.$set(item, 'IsSql', '');
          that.$set(item, 'title', '');
          that.$set(item, 'num', '');
          that.getdata()
        })
        .catch(() => { });

    },
    async getdata() {
      console.log(this.Order, 'this.Orderthis.Order');

      let positionList = this.cardData.map((item, index) => item.Position)
      // let positionList = this.cardData.map((item, index) => {
      //   if (item.includes('Position')) {
      //     return item.Position
      //   } else {
      //     return item
      //   }
      // })
      let params = {
        // simLevel: this.$route.name,
        simLevel: this.$route.path == '/simManagement/simSpot' ? 'SIM2' : this.Order.split('-')[0],
        position: positionList,
        paramList: [this.simlevel, this.BaseTime]
      }
      let resp = await QuerypositionResultBySql({ ...params })

      let result = resp.response
      if (!result || !result.length) return false
      this.cardData = this.cardData.map(item => {
        let target = result.find(itm => itm.Position == item.Position)
        if (target?.positionResult && target.positionResult.length > 0) {
          item.title = Object.keys(target.positionResult[0])[0]
          item.num = target.positionResult[0][item.title]
        }
        item.KpiChartId = target?.KpiChartId || ''
        item.IsPopu = target?.IsPopu
        item.IsSql = target?.IsSql
        return item
      })
    },
    //根据Position获取SQL
    async getSQLFn() {
      let params = {
        Position: this.Position
      }
      let { response } = await GetChartStructure(params)
      // this.id = response.ID
      //替换SQL占位符
      let curSQL = response.SqlText

      //测试用数据
      // curSQL = "select '班组数' as countName, count(1) as countNum from DFM_M_TEAM t where t.id = '#' and t.CREATEDATE < CONVERT(date, '$')"
      curSQL = this.replaceFirst(curSQL, '#', "'" + this.searchFormObj.PresentDepartmentId + "'")
      curSQL = this.replaceFirst(curSQL, '$', "'" + this.searchFormObj.date + "'")
      this.getListFn(curSQL)
    },
    //根据SQl获取表格数据
    async getListFn(curSQL) {
      let params = {
        "sqlText": curSQL
      }
      let { response } = await QueryResultBySql(params)
      // console.log('response==>', response);
      // this.$emit('getListFn', response);
      this.addListFn(response)
    },
    //新增弹窗获取列表
    addListFn(list) {
      this.closePopup()
      if (!list.length) return
      // let keyList = Object.keys(list[0])
      this.cardData = []
      for (let i = 0; i < 9; i++) {
        const element = list[i]
        if (element) {
          this.cardData.push({ title: element.countName, num: element.countNum })
          // break
        } else {
          this.cardData.push({})
        }
      }
    },
    add(item) {
      this.KpiChartId = item.KpiChartId == undefined ? '' : item.KpiChartId
      this.currentPosition = item.Position == undefined ? item : item.Position
      // this.showAddDialog = true
      this.viewTableAddnewshow = true
      this.showviewTableAddnew = true
    },
    closePopup() {
      // this.showAddDialog = false
      this.viewTableAddnewshow = false
      this.getdata()
    },

    replaceFirst(str, searchValue, replaceValue) {
      // return str.replace(new RegExp(searchValue), replaceValue);
      return str.replace(searchValue, replaceValue);
    }
  }
}
</script>
<style lang="less" scoped>
.list-box {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-content: space-between;
    height: 86%;
    margin-top: 10px;

    .item-box {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: calc(33% - 5px);
        color: #fff;
        height: 32%;
        // border: 1px solid #000;
        box-shadow: 0px 0px 10px #fff inset;
        // background: #eee;
        // background-color: rgba(67 145 244);
        text-align: center;
        cursor: pointer;
        border-radius: 5px;

        .delete-btn {
            display: none;
            position: absolute;
            right: -5px;
            top: 0;
        }

        &:hover .delete-btn {
            display: block;
        }

        // &:hover{
        //     background: #ccc;
        // }
        .item-1 {
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;

            p {
                margin-bottom: 0 !important;
                font-size: 18px;
            }
        }

        .item-2 {
            font-size: 36px;
            display: flex;
            align-items: center;
        }
    }
}
</style>
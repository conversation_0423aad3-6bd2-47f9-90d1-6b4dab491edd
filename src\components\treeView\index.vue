<template>
    <v-card class="tree-card">
        <span class="text--primary tree-title">{{ $t(title) }}</span>
        <v-spacer dark></v-spacer>
        <v-treeview  :v-model="tree" @update:active="updateActive" activatable dense :open="initiallyOpen" :active="[activeKey || (items[0] || {}).id]" :items="items" :open-all="false" item-key="id">
            <template #label="{ item, open }">
                <div style="cursor: pointer" :title="item.name" @click="clickClassTree(item)">
                    <v-icon v-if="item.parentId === '0'">
                        {{ open ? 'mdi-folder-open' : 'mdi-folder' }}
                    </v-icon>
                    <v-icon v-else>mdi-file-document-outline</v-icon>
                    <span>
                        {{ item.name }}
                    </span>
                </div>
            </template>
        </v-treeview>
    </v-card>
</template>
<script>
export default {
    props: {
        title: {
            type: String,
            default: '字典分类'
        },
        items: {
            type: Array,
            default: () => []
        },
        tree: {
            type: Array,
            default: () => []
        },
        initiallyOpen: {
            type: Array,
            default: () => []
        },
        activeKey: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            lastID: ''
        };
    },
    methods: {
        findTreeNode1(array) {
            let ids = [];
            // 利用while 循环  数据源
            while (array.length) {
                // 移除并返回第一个元素（改变原始数组）
                let item = array.shift();
                ids.push(item);
                if (item.children) {
                    // 存在子集 添加到数组 返回新的数组
                    array = array.concat(item.children);
                } //else 没有子集  array.length 为  undefined 结束循环
            }
            return ids;
        },
        updateActive(v) {
            // let myobj = {
            //     children: this.items
            // };
            // console.log(this.items);
            // this.findTreeNode1(this.items);
            // let obj = {};
            // this.items.forEach(item => {
            //     if (item.id == v[0]) {
            //         obj = item;
            //     }
            // });
            // this.$emit('clickClassTree', obj);
        },
        clickClassTree(v) {
            this.$emit('clickClassTree', v);
        }
    }
};
</script>
<style lang="scss" scoped>
.tree-card {
    min-height: calc(100vh - 64px);
    max-width: 250px;
    font-size: 0.9125rem;
    // min-width: 250px;
    width: 100%;
    // width: 256px;
    margin-right: 12px;
    // font-weight: 500;
    // line-height: 1rem;
    .tree-title {
        display: block;
        width: 100%;
        font-weight: 500;
        padding: 8px;
        border-bottom: 1px solid #aaa;
        .v-label {
            cursor: pointer !important;
        }
    }
}
</style>

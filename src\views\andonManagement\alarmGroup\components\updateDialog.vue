<template>
    <!-- 告警升级规则 -->
    <v-dialog v-model="dialog" persistent max-width="480px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ operaObj.ID ? '编辑接警组' : '新增接警组' }}
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" class="mt-8 mb-2">
                    <v-row>
                        <!-- 接警组名称 -->
                        <v-col :cols="12" :lg="12">
                            <v-text-field v-model="form.GroupName" :rules="rules.GroupName" label="接警组名称" required dense outlined />
                        </v-col>
                        <!-- 接警组编码 -->
                        <v-col :cols="12" :lg="12">
                            <v-text-field v-model="form.GroupCode" :rules="rules.GroupCode" label="接警组编码" required dense outlined />
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn color="normal" @click="closeForm">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { AlarmgroupSaveForm } from '@/api/andonManagement/alarmGroup.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        dutyList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            checkbox: true,
            valid: true,
            dialog: false,
            form: {
                ID: '',
                GroupName: '',
                GroupCode: ''
            },
            rules: {
                GroupName: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                GroupCode: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            }
        };
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal) {
                    for (const key in this.form) {
                        if (Object.hasOwnProperty.call(this.form, key)) {
                            this.form[key] = this.operaObj[key];
                        }
                    }
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        //关闭
        closeForm() {
            this.dialog = false;
        },
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                const res = await AlarmgroupSaveForm({ ...this.form });
                const { success, msg } = res;
                if(success){
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    this.$refs.form.reset();
                    this.$emit('handlePopup', 'main');
                    if (this.operaObj.ID || this.checkbox) {
                        this.closeForm()
                    }
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>
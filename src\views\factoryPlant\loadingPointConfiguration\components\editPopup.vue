<template>
    <v-card>
        <v-card-title class="headline primary lighten-2" primary-title>{{ editItemObj.ID ? $t('GLOBAL._BJ') :
            $t('GLOBAL._XZ') }} </v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid">
                <v-row class="mt-5">
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-autocomplete :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" :items="lineList"
                            :label="$t('DFM_SLDPZ.ProductlineName')" @change="handleChangeLine" item-text="EquipmentName"
                            item-value="EquipmentCode" required dense outlined v-model="form.ProductLine"></v-autocomplete>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-autocomplete :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" :items="segmentList"
                            :label="$t('DFM_SLDPZ.SegmentName')" @change="handleChangeSegment" item-text="EquipmentName"
                            item-value="EquipmentCode" required dense outlined v-model="form.SegmentCode"></v-autocomplete>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-autocomplete :rules="[v => !!v || $t('GLOBAL._MANDATORY')]" :items="workstationList"
                            :label="$t('DFM_SLDPZ.ProcessName')" item-text="EquipmentName" item-value="EquipmentCode"
                            required dense outlined v-model="form.ProcessId"></v-autocomplete>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-autocomplete :items="cpMaterialList" :label="$t('DFM_SLDPZ.MaterialName')" item-text="NAME"
                            item-value="Code" required dense outlined v-model="form.MaterialCode"></v-autocomplete>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-autocomplete :items="materialTeamList" item-text="Name" item-value="Name"
                            :label="$t('DFM_SLDPZ.MaterialType')" required dense outlined
                            v-model="form.MaterialType"></v-autocomplete>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_SLDPZ.TagId')" required dense outlined
                            v-model="form.TagId"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-autocomplete :items="unitList" item-text="Name" item-value="Name" :label="$t('DFM_SLDPZ.Uom')"
                            required dense outlined v-model="form.Uom"></v-autocomplete>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_SLDPZ.Tagert')" suffix="%" required dense outlined
                            v-model="form.Tagert"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_SLDPZ.BomQty')" type="number" required dense outlined
                            v-model="form.BomQty"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_SLDPZ.Price')" type="number" required dense outlined
                            v-model="form.Price"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_SLDPZ.Seq')" type="number" required dense outlined
                            v-model="form.Seq"></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-select :items="stateList" :label="$t('DFM_SLDPZ.Enable')" item-text="text" item-value="value"
                            required dense outlined v-model="form.Enable"></v-select>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-autocomplete :items="materialList" :label="$t('DFM_SLDPZ.ComponentName')" item-text="NAME"
                            item-value="Code" required dense outlined v-model="form.ComponentCode"></v-autocomplete>
                    </v-col>
                    <v-col :cols="12" :lg="6" class="pt-0 pb-0">
                        <v-text-field :label="$t('DFM_SLDPZ.ComponentClass')" required dense outlined
                            v-model="form.ComponentClass"></v-text-field>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
        <v-divider></v-divider>

        <v-card-actions>
            <!-- <v-spacer></v-spacer> -->
            <v-checkbox class="mr-auto" v-model="isChecked" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { getMaterialList, saveForm, getUnitList, getSelectClassifyList } from '../service'
import { EquipmentGetPageList } from '@/api/common.js';
const stateList = [{ value: 0, text: '否' }, { value: 1, text: '是' }]
export default {
    props: {
        materialList: {
            type: Array,
            default: () => []
        },
        editItemObj: {
            type: Object,
            default: () => { }
        },
        lineList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            materialTeamList: [],
            unitList: [],
            workstationList: [],
            stateList,
            segmentList: [],
            valid: false,
            isChecked: true,
            form: {
                ComponentCode: '',
                ComponentClass: '',
                MaterialType: '',
                Enable: '',
                ProductLine: '',
                SegmentCode: '',
                ProcessId: '',
                MaterialCode: '',
                TagId: '',
                Uom: '',
                Tagert: '',
                BomQty: '',
                Price: '',
                Seq: ''
            }
        }
    },
    computed: {
        cpMaterialList() {
            return this.materialList.filter(item => item.Type == 'CPWL')
        }
    },
    async created() {
        this.getUnit()
        this.getMaterialTeamList()
        if (this.editItemObj && this.editItemObj.ID) {
            for (const key in this.form) {
                this.form[key] = this.editItemObj[key];
            }
            this.form.Tagert = this.form.Tagert * 100
            this.form.ID = this.editItemObj.ID;
            if (this.form.ProductLine) {
                await this.handleChangeLine(this.form.ProductLine, 'init');
            }
            if (this.form.SegmentCode) {
                this.handleChangeSegment(this.form.SegmentCode, 'init');
            }
        }
    },
    methods: {
        async getMaterialTeamList() {
            let resp = await getSelectClassifyList({ Identities: 'MaterialGroup' });
            this.materialTeamList = resp.response;
        },
        // 获取单位列表
        async getUnit() {
            let resp = await getUnitList();
            this.unitList = resp.response;
        },
        async handleChangeLine(val, type) {
            if (!type) {
                this.form.SegmentCode = '';
                this.form.ProcessId = '';
                this.segmentList = [];
                this.workstationList = []
            }
            let obj = this.lineList.find(item => item.EquipmentCode == val);
            const { ID } = obj;
            const res = await EquipmentGetPageList({ DataItemCode: 'EquipmentLevel', ParentId: ID, pageIndex: 1, pageSize: 9999 });
            const { success, response } = res;
            if (success) {
                this.segmentList = response.data;
            }
        },
        async handleChangeSegment(val, type) {
            if (!type) {
                this.form.ProcessId = '';
                this.workstationList = [];
            }
            let obj = this.segmentList.find(item => item.EquipmentCode == val);
            const { ID } = obj;
            const res = await EquipmentGetPageList({ DataItemCode: 'EquipmentLevel', ParentId: ID, pageIndex: 1, pageSize: 9999 });
            const { success, response } = res;
            if (success) {
                this.workstationList = response.data;
            }
        },
        resetForm() {
            this.form = {
                ComponentCode: '',
                ComponentClass: '',
                ProductLine: '',
                SegmentCode: '',
                ProcessId: '',
                MaterialCode: '',
                TagId: '',
                Uom: '',
                Tagert: '',
                BomQty: '',
                Price: '',
                Seq: ''
            }
        },
        async submitForm() {
            if (!this.$refs.form.validate()) return false;
            let params = JSON.parse(JSON.stringify(this.form))
            params.ProductlineName = this.lineList.find(item => item.EquipmentCode == this.form.ProductLine)?.EquipmentName || ''
            // 需求要求SegmentName SegmentId 取Remark字段
            params.SegmentId = this.segmentList.find(item => item.EquipmentCode == this.form.SegmentCode)?.Remark || ''  // 后端要求segmentId字段传name
            params.SegmentName = params.SegmentId
            params.ProcessName = this.workstationList.find(item => item.EquipmentCode == this.form.ProcessId)?.EquipmentName || ''
            params.MaterialName = this.cpMaterialList.find(item => item.Code == this.form.MaterialCode)?.NAME || ''
            params.ComponentName = this.materialList.find(item => item.Code == this.form.ComponentCode)?.NAME || ''
            params.Tagert = (params.Tagert / 100).toFixed(4)
            let resp = await saveForm({ ...params });
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._BCCG'), color: 'success' });
            this.resetForm();
            this.$emit('getdata');
            if (this.isChecked) {
                this.$emit('closePopup');
            }
        },
        closePopup() {
            this.$emit('closePopup')
        }
    }
}
</script>

<style></style>
<template>
    <el-dialog :title="dialogForm.ID ? $t('GLOBAL._TD') : $t('GLOBAL._XZ')" :visible.sync="dialogVisible" width="800px"
      :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
      @close="dialogVisible = false" append-to-body>
      <el-form ref="dialogForm" :model="dialogForm" label-width="130px">

        <el-col :lg="12">
          <el-form-item label="物料代码" prop="MaterialCode">
            <el-input v-model="dialogForm.MaterialCode" placeholder="物料代码" disabled />
          </el-form-item>            
        </el-col>

        <el-col :lg="12">
          <el-form-item label="物料名称" prop="MaterialDescription">
            <el-input v-model="dialogForm.MaterialDescription" placeholder="物料名称" disabled />
          </el-form-item>            
        </el-col>
        
        <el-col :lg="12">
          <el-form-item label="数量" prop="Quantity">
            <el-input v-model="dialogForm.Quantity" placeholder="数量" />
          </el-form-item>            
        </el-col>

        <el-col :lg="12">
          <el-form-item label="物料批次" prop="MaterialLotNo">
            <el-input v-model="dialogForm.MaterialLotNo" placeholder="物料批次" disabled />
          </el-form-item>            
        </el-col>

        <el-col :lg="12">
            <el-form-item label="物料批次">
              <el-select placeholder="请选择物料批次" v-model="dialogForm.MaterialLotNo" @change="materialLotChange" clearable style="width:100%">
                <el-option v-for="item in materialLotList" :key="item.MaterialLotNo" :label="item.MaterialLotNo" :value="item.MaterialLotNo" />
              </el-select>
            </el-form-item>
          </el-col>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small" @click="submit()">确定</el-button>
      </div>
      <!-- <material-table :is-id="false" ref="materialTable" @saveForm="setMaterial"></material-table> -->
    </el-dialog>
  </template>
  

<script>
  import {
    getMaterialLotList,
    changePoMaterialLot
  } from "@/api/planManagement/weekSchedule";
  //import MaterialTable from '@/components/MaterialTable.vue';
  export default {
    components:{
      // MaterialTable
    },
    data() {
      return {
        dialogForm: {},
        dialogVisible: false,
        formLoading: false,
        currentRow: {},
        materialLotList:[],
        matInfo:{}
      }
    },
    created() {
      this.initDictList();
    },
    mounted() {
    },
    methods: {
      async initDictList(){
      },
      async initMaterialLotList(data) {
        await getMaterialLotList(data).then(res => {
              console.log(res)
              this.materialLotList = res.response
            });
      },      
      show(data) {
        this.dialogForm = {}
        this.currentRow = data
        this.dialogVisible = true
        this.$nextTick(_ => {
          if(data.ID){
            this.dialogForm = data
            console.log(this.dialogForm)
            this.initMaterialLotList(data)
          }
        })
      },
      materialLotChange(val) {
            console.log(val);
            this.dialogForm.MaterialLotNo = val;
            // this.materialLotList.forEach(item => {
            //     item.value = item.ID;
            //     if(item.MaterialCode == val) {
            //         this.dialogForm.MaterialId  = "";
            //         this.dialogForm.MaterialCode  = item.MaterialCode;
            //         this.dialogForm.MaterialName  = item.MaterialName;
            //     }
            // });
          },
      submit() {
        changePoMaterialLot(this.dialogForm).then(res=>{
          this.$message.success(res.msg)
          this.$emit('updateDialogInfo')
          this.dialogVisible = false
        })        
      },
    }
  }
  </script>
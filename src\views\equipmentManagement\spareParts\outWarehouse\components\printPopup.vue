<template>
    <v-card>
        <v-card-title class="headline primary lighten-2" primary-title>
            批量打印
        </v-card-title>
        <v-card-text>
            <Tables class="mt-3" :page-options="pageOptions" :loading="loading" :btn-list="btnList" tableHeight="400px"
                table-name="TPM_SBGL_SBBJGL_CK" :headers="RepairPlanColumQRcode" :desserts="desserts"
                @selectePages="selectePages" @tableClick="tableClick" @itemSelected="SelectedItems"
                @toggleSelectAll="SelectedItems">
            </Tables>
        </v-card-text>
        <v-divider></v-divider>

        <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="batchPrint">{{ $t('GLOBAL._PLDY') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { RepairPlanColumQRcode } from '@/columns/equipmentManagement/Repair.js';
import { GetPageSparePartsList } from '@/api/equipmentManagement/sparePart.js';
export default {
    props: {
        PrintTemplateFn: {
            type: Function,
            default: () => { }
        }
    },
    data() {
        return {
            papamstree: {
                key: null,
                pageIndex: 1,
                pageSize: 20
            },
            RepairPlanColumQRcode,
            selectList: [],
            loading: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
        }
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('TPM_SBGL_SBBJGL._DYLLD'),
                    code: 'printCode',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBBJGL_CK_DYLLD'
                }
            ];
        }
    },
    created() {
        this.RepastInfoGetPage()
    },
    methods: {
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                pageIndex: this.papamstree.pageIndex,
                pageSize: this.papamstree.pageSize
            };
            this.loading = true;
            const res = await GetPageSparePartsList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        },
        // 表单操作
        tableClick(item, type) {
            switch (type) {
                case 'printCode':
                    this.$nextTick(this.PrintTemplateFn({ table: [item] }));
                    return;
            }
        },
        batchPrint() {
            this.$nextTick(this.PrintTemplateFn({ table: [...this.selectList] }));
        },
        closePopup() {
            this.$emit('closePopup')
        },
        // 选额列表
        SelectedItems(item) {
            this.selectList = [...item];
        },
    }
}
</script>

<style></style>
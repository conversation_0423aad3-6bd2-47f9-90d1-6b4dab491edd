import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_SHIFT; // 配置服务url
//  考勤列表
export function AttendanceGetPageList(data) {
    return request({
        url: baseURL + '/shift/HrAttendance/GetPageList',
        method: 'post',
        data
    });
}
// // 列表-新增
// export function AttendanceSaveForm(data) {
//     return request({
//         url: baseURL + '/shift/HrAttendance/SaveForm',
//         method: 'post',
//         data
//     });
// }
// // 列表 -人员删除
// export function AttendanceDelete(data) {
//     return request({
//         url: baseURL + '/shift/HrAttendance/Delete',
//         method: 'post',
//         data
//     });
// }






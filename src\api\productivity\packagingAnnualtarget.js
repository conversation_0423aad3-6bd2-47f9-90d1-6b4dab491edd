import { getRequestResources } from '@/api/fetch';
const baseURL_30015 = 'baseURL_30015'
const DFM = 'baseURL_DFM'


//Losstgt新增
export function GetLosstgtControllerPakMatGroupSaveForm(data) {
    const api = '/api/LosstgtControllerPakMatGroup/SaveForm'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Losstgt类型
export function GetLosstgtControllerPakMatGroupItem(data) {
    const api = '/api/LosstgtControllerPakMatGroup/GetItem'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
export function GetLosstgtControllerPakMatGroupItemGroup(data) {
    const api = '/api/LosstgtControllerPakMatGroup/GetItemGroup'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Losstgt删除
export function GetLosstgtControllerPakMatGroupDelete(data) {
    const api = '/api/LosstgtControllerPakMatGroup/Delete'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Losstgt列表
export function GetLosstgtControllerPakMatGroupPageList(data) {
    const api = '/api/LosstgtControllerPakMatGroup/GetPageList'
    return getRequestResources(baseURL_30015, api, 'post', data);
}
//Losstgt导入
export function GetLosstgtControllerPakMatGroupImportData(data) {
    const api = '/api/LosstgtControllerPakMatGroup/ImportData'
    return getRequestResources(baseURL_30015, api, 'post', data);
}

import request from '@/util/request';
export function ExportData(url, data) {
    return request({
        url: url,
        method: 'post',
        data,
        responseType: 'blob'


    });}
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_MATERIAL'
// 原料备料单

//分页获取备料单列表
export function getPrepareOrderList(data) {
    const api =  '/materail/PrepareOrder/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑备料单
export function PrepareOrderSaveForm(data) {
    const api =  '/materail/PrepareOrder/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除备料单
export function DeletePrepareOrder(data) {
    const api =  '/materail/PrepareOrder/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}


//获取备料单物料明细列表
export function getPrepareMaterialList(data) {
    const api =  '/materail/PrepareMaterial/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//新增、编辑备料单物料明细
export function PrepareMaterialSaveForm(data) {
    const api =  '/materail/PrepareMaterial/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除备料单物料明细
export function DeletePrepareMaterial(data) {
    const api =  '/materail/PrepareMaterial/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

//获取物料明细列表
export function GetListStatus(data) {
    const api =  '/materail/IssueMaterial/GetListStatus'
    return getRequestResources(baseURL, api, 'post', data);
}
//根据扫描条码查询相应物料信息
export function IssueMaterialScanningCodeGetList(data) {
    const api = '/materail/IssueMaterial/ScanningCodeGetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//根据产线获取工单列表
export function PrepareOrderGetWorkOrderList(data) {
    const api = '/materail/PrepareOrder/GetWorkOrderList'
    return getRequestResources(baseURL, api, 'post', data);
}
//根据产线获取仓库
export function LineMesAgvModelingGetList(data) {
    const api = '/materail/LineMesAgvModeling/GetList'
    return getRequestResources(baseURL, api, 'post', data);
}
//根据工单获取BOMlist
export function PrepareOrderGetBomList(data) {
    const api = '/materail/PrepareOrder/GetBomList'
    return getRequestResources(baseURL, api, 'post', data);
}
//修改物料状态
export function IssueMaterialUpdateStatus(data) {
    const api = '/materail/IssueMaterial/UpdateStatus'
    return getRequestResources(baseURL, api, 'post', data);
}
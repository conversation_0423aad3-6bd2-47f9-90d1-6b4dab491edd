<template>
    <div>
        <div class="form-btn-list">
            <v-btn color="primary" v-has="'SBTZGL_BPQD_ADD'" @click="btnClickEvet('addList')">{{ $t('GLOBAL._XZ') }}</v-btn>
            <v-btn color="primary" v-has="'SBTZGL_BPQD_ALLREMOVE'" :disabled="!deleteList.length" @click="btnClickEvet('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn>
        </div>
        <Tables
            :footer="false"
            :page-options="pageOptions"
            :loading="loading"
            :btn-list="btnList"
            tableHeight="calc(100vh - 220px)"
            table-name="TPM_SBGL_SBTZGL_BPBJQD"
            :headers="EquipListColum"
            :desserts="desserts"
            @selectePages="selectePages"
            @tableClick="tableClick"
            @itemSelected="SelectedItems"
            @toggleSelectAll="SelectedItems"
        ></Tables>
        <createRepast ref="createRepast" :dialogType="dialogType" :tableItem="tableItem" :equipmentSpareType="equipmentSpareType" :rowtableItem="rowtableItem"></createRepast>
    </div>
</template>
<script>
import { SparepartGetPageList, DevicePartsGetList, DevicePartsDelete, SparepartDelete } from '@/api/equipmentManagement/EquipParts.js';
import { EquipListColum } from '@/columns/equipmentManagement/Equip.js';

export default {
    components: {
        createRepast: () => import('./components/createRepast.vue')
    },
    props: {
        rowtableItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            loading: false,
            showFrom: false,
            papamstree: {
                key: null,
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            EquipListColum,
            desserts: [],
            equipmentSpareType: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            MyFlag: false,
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            hasChildren: {} // 新增字典详情判断-子节点才能新增
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'editList',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBTZGL_BPQD_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBTZGL_BPQD_DELETE'
                }
            ];
        }
    },
    created() {
        // await this.GetSparepartGetPageList();
        this.GetequipmentSpareType();
    },
    methods: {
        // 设备列表查询
        async GetSparepartGetPageList(itemrow, flag) {
            let params = {
                DeviceCategoryId: '',
                DeviceId: '',
                pageIndex: 1,
                pageSize: 1000
            };
            this.loading = true;
            let res;
            if (itemrow) {
                if (flag) {
                    this.MyFlag = true;
                    params.DeviceId = itemrow.ID;
                    res = await DevicePartsGetList(params);
                } else {
                    this.MyFlag = false;
                    params.DeviceCategoryId = itemrow.ID;
                    res = await SparepartGetPageList(params);
                }
            }
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = response || {} || [];
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'addList':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    for (let k in this.$refs.createRepast.Listform) {
                        this.$refs.createRepast.Listform[k] = '';
                    }
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'editList':
                    this.$refs.createRepast.Listform.Part = this.tableItem.PartsId + '|' + this.tableItem.Name + '|' + this.tableItem.Code + '|' + this.tableItem.Model;
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res;
                    if (this.MyFlag) {
                        res = await DevicePartsDelete(params);
                    } else {
                        res = await SparepartDelete(params);
                    }
                    if (res.success) {
                        this.tableItem = {};
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.GetSparepartGetPageList(this.rowtableItem, this.MyFlag);
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            this.deleteList = item;
        },

        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.GetSparepartGetPageList(this.rowtableItem, this.MyFlag);
        },
        // 获取备件分类
        async GetequipmentSpareType() {
            const res = await this.$getDataDictionary('SpareType');
            this.equipmentSpareType = res || [];
        }
    }
};
</script>
<style lang="scss" scoped></style>

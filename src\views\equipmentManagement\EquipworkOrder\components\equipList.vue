<template>
    <div class="top-right ">
        <div class="title d-flex">
            <span class="title-item" v-for="(i, k) in titles" :key="k">{{i}}</span>
        </div>
        <div class="content-box d-flex">
            <div class="content-box-item" v-for="i in 4" :key='i'>
                <div :id="'chart' + i" style="width: 100%; height: 100%"></div>
            </div>
        </div>
    </div>
</template>

<script>
import { ApiDataByModel } from '@/api/kanbanManagement/DeviceKanban.js';
import dayjs from 'dayjs';
export default {
    data() {
        return {
            option: null,
            isShowChart: true,
            optionData1: [],
            optionData2: [],
            optionData3: [],
            optionData4: [],
            codeList: ['offline', 'reset', 'timing', 'countabnormal'],
            titles: ['离线状态', '清零统计', '校时统计', '计数统计'],
            datas: [570, 827, 300, 0]
        };
    },
    created() {
        
    },
    mounted() {
        for (let index = 1; index < 5; index++) {
            this.initEchart(index);
        }
    },
    methods: {
        getCurrentDate(num) {
            let newDate = new Date();
            if (typeof num == 'number') {
                newDate = dayjs(newDate.getTime() - num * 24 * 60 * 60 * 1000).format('YYYY-MM-DD HH:mm:ss').split(' ')[0]
            } else {
                newDate = newDate.toLocaleDateString();
            }
            return newDate.replace(/\//g, '-');
        },
        // 时间格式化
        formartDate(){
            const newDate = new Date().toLocaleTimeString();
            const arr = newDate.split(':')
            let t = ' 07:00:00', st = this.getCurrentDate(0);
            if (arr[0] < 7) {
                t = ' 19:00:00';
                st = this.getCurrentDate(1);
            }
            if (arr[0] > 18) {
                t = ' 19:00:00';
            }
            return st + t
        },
        async getData(k){
            let o = {mcode: this.codeList[k - 1]}
            if(this.codeList[k - 1] == 'countabnormal') {
                const start = this.formartDate();
                const end = dayjs().format('YYYY-MM-DD HH:mm:ss')
                const parameters = [
                    {
                    "name": "@starttime",
                    "value": start
                    },{
                    "name": "@endtime",
                    "value": end
                }]
                o = {  ...o, parameters }
            }
            const res = await ApiDataByModel(o)
            const { success, response } = res || {};
            if(success){
                const a = response?JSON.parse(response): [];
                const str = 'optionData' + k
                this[str] = [];
                a.forEach(e => {
                    const { num, reason } = e;
                    let o = {}
                    if (reason) {
                        o = {value: num, name: reason};
                        this[str].push(o);
                    } else {
                        for (const key in e) {
                            if (Object.hasOwnProperty.call(e, key)) {
                                o = {value: e[key], name: key};
                                this[str].push(o);
                            }
                        }
                    }
                });
            }
        },
        async initEchart(k) {
            await this.getData(k);
            var myChart = this.$echarts.init(document.getElementById('chart' + k));
            const str = 'optionData' + k
            var option = {
                    title: { },
                    tooltip: {
                        trigger: 'item'
                    },
                    legend: {
                        left: 'center',
                        top: '-4%',
                        textStyle: {
                            color: '#06a6b4',
                            fontSize: 16
                        }
                    },
                    color: ['#75bedc', '#91cd77', '#61a0a8', '#749f83'],// 饼图各块颜色
                    series: [
                        {
                            name: this.titles[k -1],
                            type: 'pie',
                            radius: ['25%', '55%'],
                            avoidLabelOverlap: false,
                            label: {
                                show: false,
                                position: 'inside',
                                fontSize: 14,
                                normal: {
                                    show: true,
                                    position: 'inner', // 数值显示在内部
                                    formatter: '{c}',
                                    fontSize: 14
                                }
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: 8,
                                    fontWeight: 'bold'
                                }
                            },
                            labelLine: {
                                show: true,
                            },
                            data: this[str],
                            top: '0'
                        }
                    ]
            };
            // 使用刚指定的配置项和数据显示图表。
            myChart.setOption(option);
        }
    }
};
</script>

<style lang="scss" scoped>
.top-right {
    // width: 75%;
    height: 100%;
    background: var(--v-primary-lighten5);
    .title {
        width: 100%;
        height: 20px;
        padding: 10px 0 50px;
        // color: #06a6b4;
        // font-size: 16px !important;
        // display: flex;
        .title-item{
            flex: auto;
            // width: 25%;
            color: #06a6b4;
            // height: 100%;
            text-align: center;
        }
    }
    .content-box {
        width: 100%;
        // height: 100%;
        padding: 0 15px;
        // display: flex;
        .content-box-item{
            // flex: 1;
            // display: flex;
            width: 25%;
            height: 100%;
            
        }
    }
}
</style>
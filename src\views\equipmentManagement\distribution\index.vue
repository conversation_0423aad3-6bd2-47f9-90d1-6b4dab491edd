<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <!-- :disabled="!deleteList.length" -->
                    <!-- <v-btn color="primary" v-has="'BJFFGL_FF'" :disabled="!deleteList.length" @click="btnClickEvet('ff')">{{ $t('TPM_SBGL_BJFFGL._FF') }}</v-btn>
                    <v-btn color="primary" v-has="'BJFFGL_TH'" :disabled="!deleteList.length" @click="btnClickEvet('th')">{{ $t('TPM_SBGL_BJFFGL._TH') }}</v-btn>
                    <v-btn color="primary" v-has="'BJFFGL_QX'" :disabled="!deleteList.length" @click="btnClickEvet('qs')">{{ $t('TPM_SBGL_BJFFGL._QX') }}</v-btn>
                    <v-btn color="primary" v-has="'BJFFGL_CM'" :disabled="!deleteList.length" @click="btnClickEvet('cm')">{{ $t('TPM_SBGL_BJFFGL._CM') }}</v-btn> -->
                    <v-btn color="primary" v-has="'BJFFGL_DC'" @click="handleExport">{{ $t('TPM_SBGL_BJFFGL._DC') }}</v-btn>
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_BJFFGL"
                    :headers="distributionColum"
                    :desserts="desserts"
                    @selectePages="selectePages"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"
                ></Tables>
            </v-card>
        </div>
        <el-dialog :close-on-click-modal="false" :title="addTitle" :visible.sync="addModel" width="30%">
            <div class="addForm">
                <v-text-field v-model="user" disabled outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_SBLL.Creator') + '*'"></v-text-field>
            </div>
            <div class="addForm" v-if="clickType == 'ff'">
                <v-autocomplete
                    clearable
                    v-model="Subject"
                    :items="SubjectList"
                    item-text="ItemName"
                    item-value="ItemValue"
                    :label="$t('$vuetify.dataTable.TPM_SBGL_BJFFGL._bjkm') + '*'"
                    clear
                    dense
                    outlined
                ></v-autocomplete>
            </div>
            <div class="addForm" v-if="clickType != 'cm'">
                <v-text-field v-model="userDate" :clearable="true" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_BJFFGL._slsj') + '*'" readonly></v-text-field>
                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="userDate" type="datetime" :placeholder="$t('$vuetify.dataTable.TPM_SBGL_BJFFGL._slsj') + '*'"></el-date-picker>
            </div>
            <div class="addForm" v-if="clickType == 'th'">
                <v-text-field v-model="ReturnQty" type="number" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_BJFFGL._thsl') + '*'"></v-text-field>
            </div>
            <div class="addForm" v-if="clickType != 'cm' && clickType != 'ff'">
                <v-text-field v-model="reason" outlined dense :label="$t('GLOBAL._REASON') + '*'"></v-text-field>
            </div>

            <div class="addForm" v-if="clickType == 'cm'">
                <v-text-field v-model="startDate" :clearable="true" outlined dense :label="$t('GLOBAL.StartTime') + '*'" readonly></v-text-field>
                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="startDate" type="datetime"></el-date-picker>
                <!-- <v-text-field v-model="startDate" :clearable="true" outlined dense :label="$t('GLOBAL.StartTime')" readonly></v-text-field>
                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="startDate" type="datetime" :placeholder="$t('GLOBAL.StartTime') + '*'"></el-date-picker> -->
            </div>

            <div class="addForm" v-if="clickType == 'cm'">
                <v-text-field v-model="endDate" :clearable="true" outlined dense :label="$t('GLOBAL.EndTime')" readonly></v-text-field>
                <el-date-picker
                    :picker-options="pickerOptionsEnd"
                    default-time="23:59:59"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    v-model="endDate"
                    type="datetime"
                    :placeholder="$t('GLOBAL.EndTime')"
                ></el-date-picker>
            </div>
            <div class="addForm">
                <v-text-field v-model="Qty" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Qty')"></v-text-field>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addModel = false">取 消</el-button>
                <el-button type="primary" @click="Save()">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';
import {
    GetPartsHistoryDetailtPageList,
    GetPartsHistoryDetailtDelete,
    GetPartsHistoryDetailtCancel,
    GetPartsHistoryDetailtRelease,
    GetPartsHistoryDetailtReturn,
    GetPartsHistoryDetailtPurchase,
    GetPartOutstockStatus
} from '@/api/equipmentManagement/Parts.js';
import { GetExportData } from '@/api/equipmentManagement/Equip.js';
import { distributionColum } from '@/columns/equipmentManagement/distribution.js';
import { configUrl } from '@/config';
import moment from 'moment';
import { Message, MessageBox } from 'element-ui';

export default {
    name: 'RepastModel',
    data() {
        return {
            // tree 字典数据
            loading: true,
            showFrom: false,
            papamstree: {
                ReferNo: '',
                HistoryNo: '',
                PartsName: '',
                PartsCode: '',
                StartPurchaseDate: '',
                EndPurchaseDate: '',
                Status: '',
                pageIndex: 1,
                pageSize: 20,
                orderByFileds: 'REQUEST_DATE DESC'
            },
            Subject: '',
            SubjectList: [],
            //查询条件
            distributionColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            typecodelist: [],
            Statuslist: [],
            clickType: '',
            addTitle: '',
            reason: '',
            startDate: '',
            endDate: '',
            Qty: 0,
            user: this.$store.getters.getUserinfolist[0].LoginName,
            userDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
            addModel: false,
            ReturnQty: 0,
            pickerOptionsEnd: {
                disabledDate: time => {
                    if (this.startDate) {
                        return time.getTime() < new Date(this.startDate).getTime() - 24 * 3600000;
                    }
                }
            }
        };
    },
    computed: {
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'HistoryNo',
                    label: this.$t('TPM_SBGL_BJFFGL._CKDH'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'PartsCode',
                    label: this.$t('TPM_SBGL_BJFFGL._BJBM'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'PartsName',
                    label: this.$t('TPM_SBGL_BJFFGL._BJMC'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    selectData: this.typecodelist,
                    type: 'select',
                    key: 'Type',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Jigtype'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBTZGL_BPBJQD.Jigtype')
                },
                     {
                    value: '',
                    selectData: this.Statuslist,
                    type: 'select',
                    key: 'Status',
                    icon: 'mdi-account-check',
                    label: this.$t('Feeding.Status'),
                    placeholder: this.$t('Feeding.Status')
                },
                {
                    value: '',
                    key: 'ReferNo',
                    label: this.$t('TPM_SBGL_BJFFGL._GLDH'),
                    icon: 'mdi-account-check',
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'StartPurchaseDate',
                    type: 'time',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_BJFFGL._CKSJKS'),
                    placeholder: this.$t('TPM_SBGL_BJFFGL._CKSJKS')
                },
                {
                    value: '',
                    key: 'EndPurchaseDate',
                    type: 'time',
                    icon: 'mdi-account-check',
                    label: this.$t('TPM_SBGL_BJFFGL._CKSJJS'),
                    placeholder: this.$t('TPM_SBGL_BJFFGL._CKSJJS')
                }
            ];
        },
        btnList() {
            return [
                {
                    text: this.$t('TPM_SBGL_BJFFGL._FF'),
                    code: 'ff',
                    type: 'primary',
                    showList: ['已申请'],
                    showKey: 'Status',
                    icon: '',
                    authCode: 'BJFFGL_FF' //SBDJGZ_EDIT
                },
                {
                    text: this.$t('TPM_SBGL_BJFFGL._TH'),
                    code: 'th',
                    showList: ['已发放', '已出库'],
                    showKey: 'Status',
                    type: 'primary',
                    icon: '',
                    authCode: 'BJFFGL_TH' //SBDJGZ_EDIT
                },
                {
                    text: this.$t('TPM_SBGL_BJFFGL._QX'),
                    code: 'qx',
                    showList: ['已申请', '未申请'],
                    showKey: 'Status',
                    type: 'primary',
                    icon: '',
                    authCode: 'BJFFGL_QX' //SBDJGZ_EDIT
                },
                {
                    text: this.$t('TPM_SBGL_BJFFGL._CM'),
                    code: 'cm',
                    showList: ['已申请', '采购中'],
                    showKey: 'Status',
                    type: 'primary',
                    icon: '',
                    authCode: 'BJFFGL_CM' //SBDJGZ_EDIT
                }
            ];
        }
    },
    async mounted() {
        this.SubjectList = await this.$getNewDataDictionary('PartSubject');
        this.Statuslist = await this.$getNewDataDictionary('PartOutstockStatus');
        this.typecodelist = await this.$getNewDataDictionary('PartType');
        this.RepastInfoGetPage();
    },
    methods: {
        async handleExport() {
            let params = {
                ...this.papamstree
            };
            const baseUrl3 = configUrl[process.env.VUE_APP_SERVE]['baseURL_EQUIPMENT'] + `/api/PartsHistoryDetail/ExportData`;
            let res = await GetExportData(baseUrl3, params);
            let binaryData = [];
            binaryData.push(res);
            const url = window.URL.createObjectURL(new Blob(binaryData));
            console.log(url);
            const link = document.createElement('a');
            link.href = url;
            const now = new Date();
            const formattedDateTime = `${now.getFullYear()}${now.getMonth() + 1}${now.getDate()}${now.getHours()}${now.getMinutes()}`;
            let fileName = `备件发放管理${formattedDateTime}.xlsx`;
            document.body.appendChild(link);
            link.setAttribute('download', fileName);
            link.click();
            window.URL.revokeObjectURL(link.href);
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.RepastInfoGetPage();
        },
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                ...this.papamstree
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading = true;
            const res = await GetPartsHistoryDetailtPageList(params);
            let { success, response } = res;
            response.data.forEach(item => {
                this.Statuslist.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                this.SubjectList.forEach(it => {
                    if (item.Subject == it.ItemValue) {
                        item.Subject = it.ItemName;
                        item.SubjectValue = it.ItemValue;
                    }
                });
            });
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        async Save() {
            let params = this.deleteList;
            let res;
            switch (this.clickType) {
                case 'ff':
                    if (this.user == '' || this.userDate == '' || this.Subject == ''|| this.Qty == '') {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }

                    params.forEach(item => {
                        item.Subject = this.Subject;
                        item.OutstockBy = this.user;
                        item.OutstockDate = this.userDate;
                        item.Qty = this.Qty;
                    });
                    res = await GetPartsHistoryDetailtRelease(params);
                    break;
                case 'th':
                    if (this.user == '' || this.userDate == '' || this.reason == '' || this.ReturnQty == ''|| this.Qty == '') {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    params.forEach(item => {
                        item.ReturnBy = this.user;
                        item.ReturnDate = this.userDate;
                        item.ReturnReason = this.reason;
                        item.Qty = this.Qty;
                        item.ReturnQty = this.ReturnQty;
                    });
                    res = await GetPartsHistoryDetailtReturn(params);
                    break;
                case 'qx':
                    if (this.user == '' || this.userDate == '' || this.reason == ''|| this.Qty == '') {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    params.forEach(item => {
                        item.CancelBy = this.user;
                        item.Qty = this.Qty;
                        item.CancelDate = this.userDate;
                        item.CancelReason = this.reason;
                    });
                    res = await GetPartsHistoryDetailtCancel(params);
                    break;
                case 'cm':
                    if (this.user == '' || this.startDate == ''|| this.Qty == '') {
                        Message({
                            message: `${this.$t('Inventory.ToOver')}`,
                            type: 'error'
                        });
                        return;
                    }
                    params.forEach(item => {
                        item.ModifyUserId = this.user;
                        item.Qty = this.Qty;
                        item.StartPurchaseDate = this.startDate;
                        item.EndPurchaseDate = this.endDate;
                    });
                    res = await GetPartsHistoryDetailtPurchase(params);
                    break;
            }
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.RepastInfoGetPage();
                this.addModel = false;
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            this.clickType = val;
            switch (this.clickType) {
                case 'ff':
                    this.Subject = '';
                    this.addTitle = this.$t('TPM_SBGL_BJFFGL._FF');

                    break;
                case 'th':
                    this.addTitle = this.$t('TPM_SBGL_BJFFGL._TH');
                    break;
                case 'qx':
                    this.addTitle = this.$t('TPM_SBGL_BJFFGL._QX');
                    break;
                case 'cm':
                    this.addTitle = this.$t('TPM_SBGL_BJFFGL._CM');
                    break;
            }
            this.user = this.$store.getters.getUserinfolist[0].LoginName;
            this.ReturnQty = 0;
            this.Qty = 0;
            this.userDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.startDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.addModel = true;
        },
        // 表单操作
        tableClick(item, type) {
            this.deleteList = [];
            this.clickType = type;
            this.tableItem = item;
            switch (this.clickType) {
                case 'ff':
                    this.Subject = this.tableItem.SubjectValue;
                    this.addTitle = this.$t('TPM_SBGL_BJFFGL._FF');
                    break;
                case 'th':
                    this.addTitle = this.$t('TPM_SBGL_BJFFGL._TH');
                    break;
                case 'qx':
                    this.addTitle = this.$t('TPM_SBGL_BJFFGL._QX');
                    break;
                case 'cm':
                    this.addTitle = this.$t('TPM_SBGL_BJFFGL._CM');
                    break;
            }
            this.deleteList.push(this.tableItem);
            this.ReturnQty = this.tableItem.ReturnQty;
            this.Qty = this.tableItem.Qty;
            this.user = this.$store.getters.getUserinfolist[0].LoginName;
            this.userDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.startDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.addModel = true;
        },
        // 删除列表
        SelectedItems(item) {
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        }
    }
};
</script>

<style lang="scss">
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}
.addForm {
    position: relative;
    .el-date-editor {
        width: 100%;
        position: absolute;
        opacity: 0;
        top: 0;
        .el-input__inner {
            font-size: 16px;
            height: 32.5px;
            color: rgba(0, 0, 0, 0.87);
            border: 1px solid #9e9e9e;
        }
        .el-input__icon {
            line-height: 32.5px;
        }
    }
}
.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

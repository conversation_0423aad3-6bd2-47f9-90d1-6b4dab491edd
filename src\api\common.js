import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM',
    baseMATERIAL = 'baseURL_MATERIAL',
    SIM = 'baseURL_KPI',
    SHIFT = 'baseURL_SHIFT'; // 配置服务url


// 数据字典新
export function GetNewDetailTree(data) {
    const api = '/api/DataItemDetail/GetList';
    return getRequestResources(baseURL, api, 'post', data, true);
}

// 公司信息tree
export function companyTree(data) {
    const api = '/api/Department/GetTree';
    return getRequestResources(baseURL, api, 'post', data);
}
//获取产线列表数据
export function GetListByLevel(data) {
    const api = '/api/Equipment/GetListByLevel';
    return getRequestResources(baseURL, api, 'post', data, true);
}
// 套件保存
export function ClassMappingSave(data) {
    const api = '/api/Equipment/SaveEquipmentClassMapping';
    return getRequestResources(baseURL, api, 'post', data);
}

// 修改用户密码
export function saveUserForm(data) {
    const api = '/api/Userinfo/UpdatePassword';
    return getRequestResources(baseURL, api, 'post', data);
}
// 根据itemCode获取数据字典
export function dataItemDetailGetList2(data) {
    const api = '/api/DataItemDetail/GetList';
    return getRequestResources(baseURL, api, 'post', data, true);
}

// 根据itemCode获取数据字典
export function dataItemDetailGetList(itemCode) {
    const api = '/api/DataItemDetail/GetList';
    return getRequestResources(baseURL, api, 'post', { itemCode }, true);
}

///获取所有数据字典信息
export function dataItemDetailGetAll() {
    const api = '/api/DataItemDetail/GetAllDict';
    return getRequestResources(baseURL, api, 'post');
}

// 获取班次
export function ShiftGetList(data) {
    const api = '/api/Shift/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
// 获取班组
export function TeamGetList(data) {
    const api = '/api/Team/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
// 获取负责人列表
export function getStaff(data) {
    const api = '/api/Staff/GetList'
    return getRequestResources(baseURL, api, 'post', data)
}
// 获取部门树形数据
export function getDepartment(data) {
    const api = '/api/Department/GetTree';
    return getRequestResources(baseURL, api, 'post', data);
}
// 获取物理模型
export function EquipmentGetEquipmentTree(data) {
    const api = '/api/Equipment/GetEquipmentTree';
    return getRequestResources(baseURL, api, 'post', data);
}
// 获取车间班组树形
export function EquipmentGetEquipmentTeamTree(data) {
    const api = '/simapi/PlantModel/GetEquipmentTeamTree';
    return getRequestResources(SIM, api, 'post', data);
}
export function GetEquipmentTeamTree(data) {
    const api = '/api/Equipment/GetEquipmentTeamTree';
    return getRequestResources(baseURL, api, 'post', data);
}
// 获取产线
export function EquipmentGetPageList(data) {
    const api = '/api/Equipment/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}
// 获取单位
export function UnitmanageGetList(data) {
    const api = '/api/Unitmanage/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
// 获取物料
export function MaterialGetList(data) {
    const api = '/api/Material/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
export function MaterialGetListPage(data) {
    const api = '/api/Material/GetPageList';
    return getRequestResources(baseURL, api, 'post', data);
}

// Plant:工厂 Area:车间 ProductLine:产线 Segment:工段 Unit:工作单元
export function EquipmenGetListByLevel(data) {
    const api = '/api/Equipment/GetListByLevel';
    return getRequestResources(baseURL, api, 'post', data, true);
}
// Group集团 Plant公司 Department部门 Post岗位 Team班组
export function DepartmentGetListByLevel(data) {
    const api = '/api/Department/GetListByLevel';
    return getRequestResources(baseURL, api, 'post', data, true);
}

// 获取工段简称
export function GetSegmentShortNames(data) {
    const api = '/api/Equipment/GetSegmentShortNames';
    return getRequestResources(baseURL, api, 'post', data);
}

// 获取SAP库位
export function GetSapWareHouseList(data) {
    const api = '/materail/WarehouseManage/GetSapWareHouseList';
    return getRequestResources(baseMATERIAL, api, 'post', data);
}
//获取时间维度
export function GetTimeDimension() {
    const api = '/simapi/Enum/GetTimeDimension';
    return getRequestResources(SIM, api, 'post');
}
export function getLineDataByFactory(data) {
    const api = '/simapi/PlantModel/GetDepartmentList?FactoryCode=' + data.FactoryCode;
    return getRequestResources(SIM, api, 'post');
}
<template>
    <el-dialog :title="dialogForm.ID ? $t('GLOBAL._TD') : $t('GLOBAL._XZ')" :visible.sync="dialogVisible" width="800px"
      :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
      @close="dialogVisible = false" append-to-body>
      <el-form ref="dialogForm" :model="dialogForm" label-width="130px">

        <el-col :lg="12">
            <el-form-item label="物料代码">
              <!-- {{ dialogForm.MaterialCode }} - {{ dialogForm.MaterialName }} -->
              <el-select placeholder="请选择新替代物料" v-model="dialogForm.MaterialCode" @change="materialChange" clearable style="width:100%">
                <el-option v-for="item in insteadMaterialList" :key="item.MaterialId" :label="item.MaterialName" :value="item.MaterialCode" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="替代物料代码">
              {{ dialogForm.InsteadMaterialCode }} - {{ dialogForm.InsteadMaterialName }}
            </el-form-item>
          </el-col>

          <!-- <el-col :lg="12">
            <el-form-item label="新替代物料">
              <el-select placeholder="请选择新替代物料" @change="materialChange" clearable style="width:100%">
                <el-option v-for="item in insteadMaterialList" :key="item.MaterialId" :label="item.MaterialName" :value="item.MaterialCode" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="">
              
            </el-form-item>
          </el-col> -->

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small" @click="submit()">确定</el-button>
      </div>
      <!-- <material-table :is-id="false" ref="materialTable" @saveForm="setMaterial"></material-table> -->
    </el-dialog>
  </template>
  

<script>
  import {
    getInsteadMaterialList,
    getWeekScheduleBomDetail,
    changeMaterial
  } from "@/api/planManagement/weekSchedule";
  //import MaterialTable from '@/components/MaterialTable.vue';
  export default {
    components:{
      // MaterialTable
    },
    data() {
      return {
        dialogForm: {},
        dialogVisible: false,
        formLoading: false,
        currentRow: {},
        insteadMaterialList:[],
        matInfo:{}
      }
    },
    created() {
      this.initDictList();
    },
    mounted() {
    },
    methods: {
      async initDictList(){
      },
      async initInsteadMaterialList(data) {
        await getInsteadMaterialList(data).then(res => {
              console.log("initInsteadMaterialList")
              console.log(res.response)
              this.insteadMaterialList = res.response
            });
      },      
      show(data) {
        this.dialogForm = {}
        this.currentRow = data
        this.dialogVisible = true
        this.$nextTick(_ => {
          if(data.ID){
            this.dialogForm = data
            this.initInsteadMaterialList(data)
          }
        })
      },
      materialChange(val) {
            console.log(val);
            this.insteadMaterialList.forEach(item => {
                item.value = item.ID;
                if(item.MaterialCode == val) {
                    this.dialogForm.MaterialId  = "";
                    this.dialogForm.MaterialCode  = item.MaterialCode;
                    this.dialogForm.MaterialName  = item.MaterialName;
                }
            });
          },
      submit() {
        console.log("bomDetailForm.submit")
        console.log(this.dialogForm)
        this.$emit('saveForm',this.dialogForm)
        this.dialogVisible = false
      },
      // setMaterial(val){
      //   // console.log("setMaterial")
      //   // console.log(val)        
      //   this.dialogForm.MaterialId = val.ID
      //   this.dialogForm.MaterialCode = val.Code
      //   this.dialogForm.MaterialName = val.NAME
      //   this.$forceUpdate()
      //   // this.matInfo = val        
      //   // console.log(this.dialogForm.MaterialCode)
      //   // console.log(this.dialogForm.MaterialName)
      // },
      // openMaterialTable(){
      //   this.$refs['materialTable'].show()
      // },
    }
  }
  </script>
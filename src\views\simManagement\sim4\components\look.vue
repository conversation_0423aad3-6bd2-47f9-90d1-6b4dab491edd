<template>
  <v-card>
    <v-card-title
      class="headline primary lighten-2"
      primary-title
    >
      查看
    </v-card-title>
    <v-card-text style="padding-top: 15px;box-sizing: border-box;">
      <Tables
        tableHeight="500"
        :page-options="pageOptions"
        :headers="headers"
        :desserts="columns"
      >
      </Tables>
    </v-card-text>
    <v-divider></v-divider>

    <v-card-actions>
      <v-btn
        style="float:right;"
        color="normal"
        @click="closePopup"
      >{{ $t('GLOBAL._GB') }}</v-btn>
    </v-card-actions>
  </v-card>
</template>

<script>
import { getProblemLog } from '@/api/simConfig/simconfignew.js';
import { problemLog } from '@/columns/factoryPlant/tableHeaders';

export default {
  props: {
    ProblemNumber: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      showDialoglook: false,
      columns: [],
      pageOptions: {
        total: 0,
        page: 1, // 当前页码
        pageSize: 20, // 一页数据
        pageCount: 1, // 页码分页数
        pageSizeitems: [10, 20, 50, 100, 500]
      },
      paramsList: {
        key: '',
        pageCount: 1,
        pageSize: 20
      },
    };
  },
  computed: {
    headers() {
      let headList = [];
      headList = problemLog.map(item => {
        if (item.value == 'data-table-expand') {
          item.text = ''; // 展开
        } else {
          item.text = this.$t(`$vuetify.dataTable.PROBLEM_LOG.${item.value}`); //  表表头对象名称
        }
        return item;
      });
      return headList;
    },
  },

  created() {
    this.getConfigList()
  },
  mounted() {
  },
  methods: {
    async getConfigList() {
      let params = {
        ProblemNumber: this.ProblemNumber,
      };
      const res = await getProblemLog(params);
      console.log(res, 'reskkkkk');

      if (res.success) {
        this.columns = res.response
        this.columns.map(el => {
          el.Status == true ? el.Status = '操作成功' : el.Status = '操作失败'
        })
        this.pageOptions.total = res.response.length
        this.pageOptions.pageSize = res.response.length
      }
    },
    closePopup() {
      this.$emit('showLook')
    }
  }

};
</script>

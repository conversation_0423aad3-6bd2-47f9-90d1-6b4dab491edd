import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url

//获取全部数据
export function getReasonInfoList(data) {
    return request({
        url: baseURL + '/api/Reason/GetPageList',
        method: 'post',
        data
    });
}

export function getAddReasonInfo(data) {
    return request({
        url: baseURL + '/api/Reason/SaveForm',
        method: 'post',
        data
    });
}

export function getDelReasonInfo(data) {
    return request({
        url: baseURL + '/api/Reason/Delete',
        method: 'post',
        data
    });
}

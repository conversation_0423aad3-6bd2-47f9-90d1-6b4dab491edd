import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_DFM'
// 工序相关接口

// 点击数据字典查询列表 分页
export function GetDataTreeList(data) {
    const api = '/api/DataItemDetail/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}
// 点击数据字典查询列表2 分页
export function GetDataItemList(data) {
    const api = '/api/DataItemDetail/GetList'
    return getRequestResources(baseURL, api, 'post', data, true);
}

//获取工序基础信息列表数据(分页)
export function GetPageList(data) {
    const api = '/api/Process/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

//获取工序基础信息列表数据（不分页）
export function GetProcessList(data) {
    const api = '/api/Process/GetList'
    return getRequestResources(baseURL, api, 'get', data);
}

//新增、编辑工序基础信息
export function SaveForm(data) {
    const api = '/api/Process/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}
//删除工序基础信息
export function DeleteProcess(data) {
    const api = '/api/Process/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}
import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TRACE; // 配置服务url
const baseURL_DFM = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM;
const baseURL_MATERIAL = configUrl[process.env.VUE_APP_SERVE].baseURL_MATERIAL;
//获取列表
export function traceGetPageList(data) {
    return request({
        url: baseURL + '/trace/Wo/GetPageList',
        method: 'post',
        data
    });
}
//获取物料消耗详情列表
export function GetWoMaterialPageList(data) {
    return request({
        url: baseURL + '/trace/WoMaterialConsumption/GetPageList',
        method: 'post',
        data
    });
}

//获取
export function GetPageList_ByNo(data) {
    return request({
        url: baseURL + '/trace/Wo/GetPageList_ByNo',
        method: 'post',
        data
    });
}

//获取详情列表
export function BatchGetPageList(data) {
    return request({
        url: baseURL + '/trace/BatchFlowRecord/GetWoPageList',
        method: 'post',
        data
    });
}

//
export function GetPageList_BySapCode(data) {
    return request({
        url: baseURL + '/trace/BatchFlowRecord/GetPageList_BySapCode',
        method: 'post',
        data
    });
}

//获取人员工时列表
export function GetUserWorkHours(data) {
    return request({
        url: baseURL + '/trace/Common/GetUserWorkHours',
        method: 'post',
        data
    });
}

//获取机台信息
export function GetPageListByCode(data) {
    return request({
        url: baseURL + '/trace/ProductionEquipment/GetList_By',
        method: 'post',
        data
    });
}

//获取质量信息
export function InspectionRecordGetPageList(data) {
    return request({
        url: baseURL + '/trace/InspectionRecord/GetPageList',
        method: 'post',
        data
    });
}

//获取投入信息
export function GetPageList_New(data) {
    return request({
        url: baseURL + '/trace/Feeding/GetPageList_New',
        method: 'post',
        data
    });
}
// 工单SN查询
export function getWorkSnList(data) {
    return request({
        url: baseURL + '/trace/WoSn/GetWoSnPageList',
        method: 'post',
        data
    })
}
// 查询产品追溯列表
export function getProductTraceList(data) {
    return request({
        url: baseURL + '/trace/BatchRelation/ProductRetrace',
        method: 'post',
        data
    })
}
// 查询产品追溯列表(胶水)
export function getProductTraceListByGlue(data) {
    return request({
        url: baseURL + '/trace/BatchRelation/ProductGlueRetrace',
        method: 'post',
        data
    })
}

// 查询胶水追溯列表
export function getGlueTracingList(data) {
    return request({
        url: baseURL_MATERIAL + '/materail/BatchRel/GetList',
        method: 'post',
        data
    })
}


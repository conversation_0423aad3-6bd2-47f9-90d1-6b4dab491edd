// 楼层人员主数据
export const personalColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '员工号',
        value: 'StaffCode',
        width: 100,
        sortable: true
    },
    {
        text: '姓名',
        value: 'StaffName',
        width: 100,
        sortable: true
    },
    {
        text: '工段',
        value: 'LineName',
        width: 130,
        sortable: true
    },
    {
        text: '班组',
        value: 'ShiftGroupName',
        width: 70,
        sortable: true
    },
    {
        text: '',
        width: 0,
        align: 'center',
        value: 'actions',
        sortable: true
    }
];
//排班实际到港
export const replacpersonalColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '员工号',
        value: 'Code',
        width: 90,
        sortable: true
    },
    {
        text: '姓名',
        value: 'Name',
        width: 90,
        sortable: true
    },
    {
        text: '产线',
        value: 'Line',
        width: 130,
        sortable: true
    },
    {
        text: '工段',
        value: 'Segment',
        width: 130,
        sortable: true
    },
    {
        text: '班组',
        value: 'ShiftGroup',
        width: 90,
        sortable: true
    },
    {
        text: '',
        width: 0,
        align: 'center',
        value: 'actions',
        sortable: true
    }
];

// 排班结果
export const personaResulterlColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '工单号',
        value: 'WoCode',
        width: 120,
        sortable: true
    },
    {
        text: '员工号',
        value: 'StaffCode',
        width: 100,
        sortable: true
    },
    {
        text: '姓名',
        value: 'StaffName',
        width: 100,
        sortable: true
    },
    {
        text: '班组',
        value: 'ShiftGroupName',
        width: 120,
        sortable: true
    },
    {
        text: '班次',
        value: 'ShiftName',
        width: 100,
        sortable: true
    },
    {
        text: '工段',
        value: 'LineName',
        width: 160,
        sortable: true
    },
    {
        text: '计划日期',
        value: 'PlanDate',
        width: 140,
        sortable: true
    },
    {
        text: '实际开始时间',
        value: 'ActualStarttime',
        width: 160,
        sortable: true
    },
    {
        text: '实际结束时间',
        value: 'ActualEndtime',
        width: 160,
        sortable: true
    },
    {
        text: '工作时长（h）',
        value: 'Duration',
        width: 140,
        sortable: true
    },
    {
        text: '良品数量',
        value: 'ProductGood',
        width: 140,
        sortable: true
    },
    {
        text: '次品数量',
        value: 'ProductDefective',
        width: 140,
        sortable: true
    },

    {
        text: '操作',
        width: 120,
        align: 'center',
        value: 'actions',
        sortable: true
    }
];

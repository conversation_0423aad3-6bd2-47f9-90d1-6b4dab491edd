export const ovenOperationColum = [
    // {
    //     text: '序号',
    //     value: 'Index',
    //     sortable: true
    // },
    {
        text: '工序',
        value: 'ProcName',
        width: 120,
        sortable: true
    },
    {
        text: '产品线',
        value: 'FullLineName',
        width: 140,
        sortable: true
    },
    {
        text: '产线',
        value: 'CompanyName',
        width: 140,
        sortable: true
    },
    {
        text: '班组',
        value: 'TeamName',
        width: 80,
        sortable: true
    },
    {
        text: '班次',
        value: 'ShiftName',
        width: 80,
        sortable: true
    },
    {
        text: '工单号',
        value: 'WoCode',
        width: 120,
        sortable: true
    },
    {
        text: '批号',
        value: 'BatchNo',
        width: 120,
        sortable: true
    },
    {
        text: '批次数量(PCS)',
        value: 'BatchQuantity',
        semicolonFormat: true,
        width: 170,
        sortable: true
    },
    {
        text: '设备',
        value: 'EquipmentName',
        width: 140,
        sortable: true
    },
    {
        text: '状态',
        value: 'Status',
        width: 120,
        sortable: true
    },
    {
        text: '作业开始时间',
        value: 'BatchStartTime',
        width: 170,
        sortable: true
    },
    {
        text: '作业结束时间',
        width: 170,
        value: 'BatchEndTime',
        sortable: true
    }
];

export const VirtualEntryColum = [
    // {
    //     text: '序号',
    //     value: 'Index',
    //     sortable: true
    // },
    {
        text: '工序',
        value: 'ProcName',
        width: 120
    },
    {
        text: '产品线',
        value: 'FullLineName',
        width: 140,
        sortable: true
    },
    {
        text: '产线',
        value: 'CompanyName',
        width: 140
    },
    {
        text: '班组',
        value: 'TeamName',
        width: 80,
        sortable: true
    },
    {
        text: '班次',
        value: 'ShiftName',
        width: 80,
        sortable: true
    },
    {
        text: '工单号',
        value: 'WoCode',
        width: 120,
        sortable: true
    },
    {
        text: '批号',
        value: 'BatchNo',
        width: 120,
        sortable: true
    },
    {
        text: '批次数量(PCS)',
        value: 'BatchQuantity',
        semicolonFormat: true,
        width: 170,
        sortable: true
    },
    {
        text: '状态',
        value: 'Status',
        width: 120,
        sortable: true
    },
    {
        text: '创建时间',
        width: 160,
        value: 'CreateDate'
    },
    {
        text: '创建人',
        width: 100,
        value: 'CreateUserId'
    },
    {
        text: '操作',
        width: 120,
        value: 'actions'
    },
];
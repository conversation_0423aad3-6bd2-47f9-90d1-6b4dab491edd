<template>
    <!-- 告警升级规则 -->
    <v-dialog v-model="dialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                订单详情
                <v-icon @click="closeForm">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" class="mt-8 mb-2">
                    <v-row>
                        <!-- 事件等级 -->
                        <v-col :cols="12" :lg="6" v-for="(item, k) in list" :key='k'>
                            <v-text-field readonly v-model="item.valName" :label="item.text" required dense outlined />
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script>
import { traceGetPageList } from '@/api/productionManagement/WorkOrderManagement.js';
import { workOrdeManagementColum } from '@/columns/productionManagement/WorkOrderManagement.js';
const owState = {
    10: '就绪',
    20: '进行中',
    30: '暂停',
    40: '终止',
    50: '完成'
};
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            dialog: false,
            list: workOrdeManagementColum
        };
    },
    watch: {
        dialog: {
            handler(curVal) {
                if (curVal) {
                    this.list = []
                    this.getData(this.operaObj.WoId)
                }
            },
            deep: true,
            immediate: true
        }
    },
    activated(){
        this.getHead()
    },
    methods: {   
        // 
        async getData(WoCode) {
            const o = {
                WoCode,
                pageIndex: 1,
                pageSize: 20
            }
            const res = await traceGetPageList(o);
            const { success, response } = res || {};
            if (success) {
                const obj = response.data[0] || {}
                workOrdeManagementColum.forEach(e => {
                    e.valName = e.value == 'WoStatus'? owState[obj[e.value]]: obj[e.value]
                    if (e.value != 'actions') {
                       this.list.push(e) 
                    }
                });
            }
        },
        // 
        closeForm() {
            this.$emit('handlePopup', 'refresh');
            this.dialog = false;
        },
        // 
        getHead() {
            workOrdeManagementColum.forEach(e => {
                e.valName = ''
                if (e.value != 'actions') {
                    this.list.push(e) 
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>
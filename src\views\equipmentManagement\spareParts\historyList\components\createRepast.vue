<template>
    <v-dialog v-model="showDialog" max-width="960px">
        <v-card class="" v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                新增
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Remark" outlined dense label="货架编号"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.SparePartsName" outlined dense label="备品备件名称"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select v-model="form.SType" :items="equipmentSpareType" item-text="ItemName" item-value="ItemName" label="备件类型" clearable dense outlined></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.SparePartsCode" outlined dense label="备品备件编号"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Unit" outlined dense label="单位"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.UnitPrice" outlined dense label="单价"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.CurrentStock" outlined dense label="当前库存"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.MaxStock" outlined dense label="最大库存"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.MinStock" outlined dense label="最小库存"></v-text-field>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'edit'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                修改
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Remark" outlined dense label="货架编号"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.SparePartsName" outlined dense label="备品备件名称"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select v-model="editedItem.SType" :items="equipmentSpareType" item-text="ItemName" item-value="ItemName" label="备件类型" clearable dense outlined></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.SparePartsCode" outlined dense label="备品备件编号"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Unit" outlined dense label="单位"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.UnitPrice" outlined dense label="单价"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.CurrentStock" outlined dense label="当前库存"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.MaxStock" outlined dense label="最大库存"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.MinStock" outlined dense label="最小库存"></v-text-field>
                    </v-col>
                    <!-- <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Remark" outlined dense label="货架编号"></v-text-field>
                    </v-col> -->
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'et11c'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                备件使用
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form2" v-model="valid">
                    <v-row class="pt-8">
                        <!-- <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form2.Usedlocation" outlined dense label="使用位置"></v-text-field>
                        </v-col> -->
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form2.Usednum" outlined dense label="消耗数量"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form2.Owner" outlined dense label="使用人"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form2.Buynum" outlined dense label="采购数量"></v-text-field>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="pa-5 lighten-3">
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave2">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card v-if="dialogType == 'QRcodes'">
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
                <!-- {{ $t('DFM_SJZD._FLGL') }} -->
                备件数量录入
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <!-- 表单内容 -->
            <v-card-text class="mt-4">
                <v-form ref="fromQR" v-model="valid">
                    <v-row no-gutters>
                        <v-col cols="24" lg="6" sm="6" md="6">
                            <v-text-field
                                label="请扫码备件二维码"
                                height="34"
                                v-model="QRcode"
                                outlined
                                dense
                                @click:prepend-inner="getQRcodes"
                                @keyup.enter.native="JsonparseQRcodes"
                                prepend-inner-icon="mdi-barcode"
                                clearable
                            ></v-text-field>
                        </v-col>
                        <v-col class="text-lg-left ml-4" cols="24" lg="3" sm="3" md="3">
                            <v-btn color="primary" @click="JsonparseQRcodes">确定</v-btn>
                        </v-col>
                    </v-row>
                    <v-row class="">
                        <v-col class="" cols="12" sm="6" md="6">
                            <v-text-field v-model="fromQR.Remark" disabled outlined dense label="货架编号"></v-text-field>
                        </v-col>
                        <v-col class="" cols="12" sm="6" md="6">
                            <v-text-field v-model="fromQR.Code" disabled outlined dense label="备件编号"></v-text-field>
                        </v-col>
                        <v-col class="" cols="12" sm="6" md="6">
                            <v-text-field v-model="fromQR.BuyNum" outlined dense label="备件数量"></v-text-field>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSparePartlist">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
            <QRcode ref="QRcode" @getQRcodesRes="getQRcodesRes"></QRcode>
        </v-card>
    </v-dialog>
</template>
<script>
import { SparepartSaveForm, SparepartuselogSaveForm, addSparePart } from '@/api/equipmentManagement/sparePart.js';
export default {
    props: {
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        }
        // equipmentSpareType: {
        //     type: Array,
        //     default: () => []
        // },
    },
    data() {
        return {
            QRcode: '',
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            equipmentSpareType: [],
            form: {
                SparePartsName: '',
                SType: '',
                SparePartsCode: '',
                Unit: '',
                UnitPrice: '',
                CurrentStock: '',
                MaxStock: '',
                MinStock: '',
                Remark: ''
            },
            form2: {
                Usedlocation: '',
                Usednum: '',
                Owner: '',
                Buynum: ''
            },
            fromQR: {
                Remark: '', //货架编号
                Code: '', // 备件编号
                BuyNum: null //入库数量
            }
        };
    },
    computed: {
        editedItem() {
            const { SparePartsName, SType, SparePartsCode, Unit, UnitPrice, CurrentStock, MaxStock, MinStock, Remark } = this.tableItem;
            return {
                SparePartsName,
                SType,
                SparePartsCode,
                Unit,
                UnitPrice,
                CurrentStock,
                MaxStock,
                MinStock,
                Remark
            };
        }
    },
    created() {
        this.GetequipmentSpareType();
    },
    methods: {
        // 扫码录入

        getQRcodes() {
            this.$refs.QRcode.getQRcode();
        },
        // 获取查询结果
        getQRcodesRes(value) {
            let val = JSON.parse(value.text);
            let { Remark, SparePartsCode } = val;
            this.QRcode = value.text;
            this.fromQR.Remark = Remark;
            this.fromQR.Code = SparePartsCode;
        },
        closeEquip() {
            this.showDialog = false;
            this.$refs.form.reset();
            this.$refs.form2.reset();
        },
        JsonparseQRcodes() {
            try {
                if (this.QRcode) {
                    let { Remark, SparePartsCode } = JSON.parse(this.QRcode);
                    this.fromQR.Remark = Remark;
                    this.fromQR.Code = SparePartsCode;
                }
            } catch (error) {
                console.log(error);
            }
        },
        // 扫码新增备件数量  addSparePart

        async addSparePartlist() {
            let params = {
                Remark: this.fromQR.Remark,
                Code: this.fromQR.Code,
                BuyNum: +this.fromQR.BuyNum
            };
            const res = await addSparePart(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '添加成功', color: 'success' });
                this.$parent.$parent.RepastInfoGetPage();
                this.$parent.$parent.RepastInfoLogGetPage();
                this.$refs.fromQR.reset();
                this.showDialog = this.classcheckbox ? false : true;
            }
        },
        async addSave(type) {
            const paramsKey = Object.keys(this.form);
            const paramsObj = type == 'add' ? this.form : this.editedItem;
            let params = {};
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            if (type == 'edit') {
                params.ID = this.tableItem.ID;
            }
            const res = await SparepartSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.$parent.RepastInfoGetPage();
                this.showDialog = this.classcheckbox ? false : true;
            }
        },

        async addSave2() {
            let params = {
                Sparepartid: this.tableItem.ID,
                Usedlocation: this.form2.Usedlocation,
                Usednum: this.form2.Usednum,
                Owner: this.form2.Owner,
                Buynum: this.form2.Buynum
            };
            const res = await SparepartuselogSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.$parent.RepastInfoLogGetPage();
                this.$refs.form2.reset();
                this.showDialog = this.classcheckbox ? false : true;
            }
        },
        // 获取备件分类
        async GetequipmentSpareType() {
            const res = await this.$getDataDictionary('SpareType');
            this.equipmentSpareType = res || [];
        }
    }
};
</script>

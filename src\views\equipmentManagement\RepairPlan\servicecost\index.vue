<template>
    <Tables :page-options="pageOptions" :loading="loading" :btn-list="btnList" tableHeight="calc(50vh - 150px)"
        table-name="TPM_SBGL_SBWXJL_WXCB" :headers="servicecostColum" :desserts="desserts" @selectePages="selectePages"
        @tableClick="tableClick" @itemSelected="SelectedItems" @toggleSelectAll="SelectedItems"></Tables>
</template>
<script>
import { GetPricePageList } from '@/api/equipmentManagement/Repair.js';
import { servicecostColum } from '@/columns/equipmentManagement/Repair.js';
export default {
    props: {
        rowtableItem: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            loading: true,
            //查询条件
            servicecostColum,
            desserts: [],
            papamstree: {
                key: null,
                pageIndex: 1,
                pageSize: 20
            },
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            hasChildren: {} // 新增字典详情判断-子节点才能新增
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: ''
                }
            ];
        }
    },
    created() { },
    mounted() {
        // this.RepastInfologGetPage();
    },
    methods: {
        // 维修记录列表查询
        async GetPricePageList(item) {
            let params = {
                woid: item?.ID || this.rowtableItem.ID,
                pageIndex: this.papamstree.pageIndex,
                pageSize: this.papamstree.pageSize
            };
            this.loading = true;
            const res = await GetPricePageList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = [response] || [];
                this.pageOptions.total = this.desserts.length;
                // this.pageOptions.page = response.page;
                // this.pageOptions.pageCount = response.pageCount;
                // this.pageOptions.pageSize = response.pageSize;
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'add':
                    this.dialogType = val;
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.GetPricePageList();
        }
    }
};
</script>
<style lang="scss" scoped></style>
import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url

//获取套件配置列表数据
export function classGetPageList(data) {
    return request({
        url: baseURL + '/api/Class/GetPageList',
        method: 'post',
        params: data
    });
}
// 新增套件
export function clssSaveClass(data) {
    return request({
        url: baseURL + '/api/Class/SaveClass',
        method: 'post',
        data
    });
}
// 删除套件
export function clssDelete(data) {
    return request({
        url: baseURL + '/api/Class/Delete',
        method: 'post',
        data
    });
}

// 树形配置

//  查询

export function PropertyGetPageList(data) {
    return request({
        url: baseURL + '/api/Property/GetPageList',
        method: 'post',
        data
    });
}
// 新增 修改

export function PropertySaveForm(data) {
    return request({
        url: baseURL + '/api/Property/SaveForm',
        method: 'post',
        data
    });
}
// 删除

export function PropertyDelete(data) {
    return request({
        url: baseURL + '/api/Property/Delete',
        method: 'post',
        data
    });
}

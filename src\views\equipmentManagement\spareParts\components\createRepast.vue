<template>
    <v-dialog v-model="showDialog" max-width="1080px">
        <v-card>
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ dialogType == 'edit' ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text class="card-text">
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" :sm="item.sm ? item.sm : 3" :md="item.sm ? item.sm : 3" v-for="(item, index) in SbxxList" :key="index">
                            <v-text-field v-if="item.type == 'input'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                            <v-text-field v-if="item.type == 'number'" type="number" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                            <el-select
                                :placeholder="item.label"
                                v-model="item.value"
                                v-if="item.type == 'searchselect'"
                                filterable
                                remote
                                reserve-keyword
                                :remote-method="remoteMethod"
                                :loading="partsloading"
                            >
                                <el-option v-for="it in PartList" :key="it.ID" :label="it.Name" :value="it.ItemValue"></el-option>
                            </el-select>
                            <v-autocomplete
                                v-if="item.type == 'select'"
                                clearable
                                v-model="item.value"
                                :items="item.options"
                                item-text="ItemName"
                                item-value="ItemName"
                                :label="item.label"
                                clear
                                dense
                                outlined
                            ></v-autocomplete>
                            <v-menu
                                v-if="item.type == 'date' || item.type == 'datetime'"
                                :ref="'menu' + index"
                                v-model="menu[index]"
                                :close-on-content-click="false"
                                :nudge-right="40"
                                transition="scale-transition"
                                offset-y
                                max-width="290px"
                                min-width="290px"
                            >
                                <template #activator="{ on, attrs }">
                                    <v-text-field
                                        v-model="item.value"
                                        :clearable="item.isClearable ? item.isClearable : true"
                                        outlined
                                        dense
                                        :label="item.label"
                                        readonly
                                        v-bind="attrs"
                                        v-on="on"
                                    ></v-text-field>
                                </template>
                                <v-date-picker :locale="locale" v-model="item.value" no-title @input="closeDatePicker(index)"></v-date-picker>
                            </v-menu>
                            <div class="textfieldbox">
                                <v-text-field
                                    v-model="item.value"
                                    :clearable="item.isClearable ? item.isClearable : true"
                                    outlined
                                    dense
                                    v-if="item.type == 'time'"
                                    :label="item.label"
                                    readonly
                                ></v-text-field>
                                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-if="item.type == 'time'" v-model="item.value" type="datetime" :placeholder="item.label"></el-date-picker>
                            </div>
                            <el-radio-group v-model="item.value" v-if="item.type == 'radio'">
                                <div class="textlabel">{{ item.label }}:</div>
                                <el-radio :label="it.value" :key="ind" v-for="(it, ind) in item.radiolist">{{ it.label }}</el-radio>
                            </el-radio-group>
                        </v-col>

                        <!-- <v-col class="py-0 px-3" cols="12" :sm="6" :md="6">
                            <div class="textlabel">{{ $t('$vuetify.dataTable.TPM_SBGL_FWDGL.spjg') }}:</div>
                            <el-radio v-model="spjg" label="1">{{ $t('GLOBAL._TG') }}</el-radio>
                            <el-radio v-model="spjg" label="0">{{ $t('GLOBAL._BH') }}</el-radio>
                        </v-col> -->
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { GetPartsInventorySaveForm } from '@/api/equipmentManagement/Parts.js';
import { fwcgdownColum } from '@/columns/equipmentManagement/Repair.js';
import { GetPartsPageList } from '@/api/equipmentManagement/Parts.js';
import { Message, MessageBox } from 'element-ui';

export default {
    components: {},
    props: {
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            fwcgdownColum,
            loading2: false,
            desserts2: [],
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            spjg: '',
            menu: [],
            partsloading: false,
            PartList: [],
            SbxxList: [
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_BJKZGL.SparePartsName') + '*',
                    value: '',
                    id: 'Part',
                    require: true,
                    sm: 6,
                    type: 'searchselect'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_BJKZGL.SType') + '*',
                    value: '',
                    id: 'Type',
                    require: true,
                    sm: 6,
                    type: 'select',
                    options: []
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_BJKZGL.BatchCode'),
                    value: '',
                    sm: 6,
                    id: 'BatchCode',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_BJKZGL.InstoreDate'),
                    value: '',
                    sm: 6,
                    id: 'InstoreDate',
                    type: 'time'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_BJKZGL.Warehouse'),
                    value: '',
                    sm: 6,
                    id: 'Warehouse',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_BJKZGL.StorageBin'),
                    value: '',
                    sm: 6,
                    id: 'StorageBin',
                    type: 'input'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_BJKZGL.Stock') + '*',
                    value: '',
                    sm: 6,
                    type: 'number',
                    require: true,
                    id: 'Stock'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_BJKZGL.Unit') + '*',
                    value: '',
                    sm: 6,
                    type: 'select',
                    options: [],
                    require: true,
                    id: 'Unit'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_BJKZGL.Brand'),
                    value: '',
                    sm: 6,
                    type: 'input',
                    id: 'Brand'
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_BJKZGL.Supplier'),
                    value: '',
                    sm: 6,
                    type: 'input',
                    id: 'Supplier'
                }
            ]
        };
    },
    computed: {
        locale() {
            return this.$store.state.app.locale || 'zh';
        }
    },
    watch: {},
    mounted() {
        this.getPartList();
    },
    methods: {
        async getPartList() {
            let params = {
                pageIndex: 1,
                pageSize: 20
            };
            let res = await GetPartsPageList(params);
            this.PartList = res.response.data;
            this.PartList.forEach(item => {
                item.ItemValue = item.ID + '|' + item.Name + '|' + item.Code + '|' + item.Model;
            });
        },
        async remoteMethod(query) {
            this.partsloading = true;
            let params = {
                Name: query,
                pageIndex: 1,
                pageSize: 20
            };
            let res = await GetPartsPageList(params);
            this.PartList = res.response.data;
            this.PartList.forEach(item => {
                item.ItemValue = item.ID + '|' + item.Name + '|' + item.Code + '|' + item.Model;
            });
            this.partsloading = false;
        },
        async addSave() {
            let obj = {};
            let flag = this.SbxxList.some(item => {
                if (item.require) {
                    return item.value == '';
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            this.SbxxList.forEach(item => {
                obj[item.id] = item.value;
            });
            obj.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            if (this.dialogType == 'edit') {
                obj.ID = this.tableItem.ID;
            }
            obj.PartsId = this.SbxxList[0].value.split('|')[0];
            obj.Name = this.SbxxList[0].value.split('|')[1];
            obj.Code = this.SbxxList[0].value.split('|')[2];
            obj.Model = this.SbxxList[0].value.split('|')[3];
            const res = await GetPartsInventorySaveForm(obj);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$emit('loadData');
                // this.$parent.$parent.RepastInfoGetPage();
                this.showDialog = this.classcheckbox ? false : true;
            }
        },

        closeEquip() {
            this.showDialog = false;
            // this.$refs.form.reset();
        },
        closeDatePicker(index) {
            this.$set(this.menu, index, false);
        }
    }
};
</script>
<style lang="scss">
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
    .textlabel {
        display: inline-flex;
        font-size: 16px;
        margin-right: 25px;
    }
    .el-radio-group {
        height: 40px;
        margin-top: 10px;
    }
    .el-radio__input.is-checked + .el-radio__label {
        color: #3dcd58;
    }
    .el-radio__input.is-checked .el-radio__inner {
        border-color: #3dcd58;
        background: #3dcd58;
    }
    .el-radio__label {
        font-size: 16px;
    }
    .el-date-editor {
        width: 100%;
        position: absolute;
        opacity: 0;
        top: 0;
        .el-input__inner {
            font-size: 16px;
            height: 32.5px;
            color: rgba(0, 0, 0, 0.87);
            border: 1px solid #9e9e9e;
        }
        .el-input__icon {
            line-height: 32.5px;
        }
    }
}
</style>

<style lang="scss" scoped>
.card-text {
    display: block;
    max-height: 500px;
    min-height: 320px;
    overflow: auto;
    .textfieldbox {
        position: relative;
    }
}

.ant-calendar-picker {
    ::v-deep input {
        border-color: #9e9e9e;
    }

    ::v-deep i {
        color: #757575;
    }
}
</style>

// import i18n from '@/plugins/i18n';
export const departmentManagementColum = [
    {
        text: '序号',
        value: 'Index',
        width: 80,
        sortable: true
    },
    { text: '组织名称', value: 'Fullname', width: 155 },
    { text: '组织编码', value: 'Encode', width: 155 },
    { text: '组织简称', value: 'Shortname', width: 185 },
    { text: '组织性质', value: 'LEVEL', width: 155 },
    { text: '电话号', value: 'Outerphone', width: 100 },
    { text: '备注', value: 'Description', width: 150 },
    { text: '属性', align: 'center', value: 'attribute', width: 150 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', align: 'center', value: 'actions', width: 150, sortable: true }
];
export const attributeDialog = [
    { text: '编码', value: 'PropertyCode' },
    { text: '值', value: 'PropertyValue' },
    { text: '描述', value: 'Remark' },
    { text: '有效', value: 'Enable' },
    { text: '操作', align: 'center', value: 'actions', sortable: true }
];

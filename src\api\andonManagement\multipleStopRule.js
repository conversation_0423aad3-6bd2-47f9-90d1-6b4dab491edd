import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_ANDON'
//多次停机规则

//获取分页列表
export function MultistopRuleGetPageList(data) {
    const api =  '/andon/MultistopRule/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

//删除
export function MultistopRuleDelete(data) {
    const api =  '/andon/MultistopRule/Delete'
    return getRequestResources(baseURL, api, 'post', data);
}

//新增/编辑
export function MultistopRuleSaveForm(data) {
    const api =  '/andon/MultistopRule/SaveForm'
    return getRequestResources(baseURL, api, 'post', data);
}

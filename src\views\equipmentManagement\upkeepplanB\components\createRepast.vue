<template>
    <v-dialog v-model="showDialog" max-width="720px">
        <v-card class="" v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-autocomplete v-model="form.MaintainProjectId" :items="equipmentGroup" item-text="ItemName"
                                item-value="ItemValue" :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYGZ.MaintainProject')"
                                clearable dense outlined></v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field type="number" v-model="form.MaintenancePeriod" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYGZ.MaintenancePeriod')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-select v-model="form.Isenable" :items="stateList" item-text="name" item-value="value"
                                outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYGZ.Isenable')"></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form.Duration" type="number" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYGZ.Duration')"></v-text-field>
                        </v-col>
                        <!-- <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form.Startime" type="datetime-local" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYGZ.Startime')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="form.Endtime" type="datetime-local" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYGZ.Endtime')"></v-text-field>
                        </v-col> -->
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="form.Remark" rows="2" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYGZ.Remark')"></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'edit'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._BJ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-autocomplete v-model="editedItem.MaintainProjectId" :items="equipmentGroup"
                                item-text="ItemName" item-value="ItemValue"
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYGZ.MaintainProject')" clearable dense
                                outlined></v-autocomplete>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field type="number" v-model="editedItem.MaintenancePeriod" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYGZ.MaintenancePeriod')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-select v-model="editedItem.Isenable" :items="stateList" item-text="name" item-value="value"
                                outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYGZ.Isenable')"></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                            <v-text-field v-model="editedItem.Duration" type="number" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYGZ.Duration')"></v-text-field>
                        </v-col>
                        <!-- <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.Startime" type="datetime-local" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYGZ.Startime')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="6" md="6">
                        <v-text-field v-model="editedItem.Endtime" type="datetime-local" outlined dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYGZ.Endtime')"></v-text-field>
                    </v-col> -->
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="editedItem.Remark" rows="2" outlined dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYGZ.Remark')"></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { DeviceMcProjectGetList, MaintainRuleSavesForm } from '@/api/equipmentManagement/upkeep.js';
export default {
    props: {
        mcCyclelist: {
            type: Array,
            default: () => []
        },
        repastTypelist: {
            type: Array,
            default: () => []
        },
        equipmentGroup: {
            type: Array,
            default: () => []
        },
        dialogType: {
            type: String,
            default: ''
        },
        tableItem: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            stateList: [{ name: '禁用', value: 1 }, { name: '启用', value: 0 }],
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            items: [],
            form: {
                MaintenancePeriod: '',
                MaintainProjectId: '',
                // Startime: '',
                // Endtime: '',
                Duration: '',
                Isenable: '',
                Remark: ''
            }
        };
    },
    computed: {
        editedItem() {
            const { MaintenancePeriod, Isenable, Duration, MaintainProjectId, Remark } = this.tableItem;
            return {
                MaintenancePeriod,
                MaintainProjectId,
                Remark,
                Duration,
                Isenable
            };
        }
    },
    created() {
        // this.querySelections();
    },
    methods: {
        closeEquip() {
            this.showDialog = false;
            this.$refs.form.reset();
        },
        async addSave(type) {
            const paramsKey = Object.keys(this.form);
            const paramsObj = type == 'add' ? this.form : this.editedItem;
            let params = {};
            paramsKey.forEach(item => {
                params[item] = paramsObj[item];
            });
            if (type == 'edit') {
                params.ID = this.tableItem.ID;
            }
            params.MaintainProject = this.equipmentGroup.find(item => item.ItemValue == params.MaintainProjectId)?.ItemName
            const res = await MaintainRuleSavesForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$parent.$parent.RepastInfoGetPage();
                this.$refs.form.reset();
                this.showDialog = this.classcheckbox ? false : true;
            }
        }
    }
};
</script>

<template>
    <div v-if="show" class="dialogwrapper">
        <div class="overlay"></div>
        <v-card class="dialog">
            <v-card-title class="text-h5">{{ titles }}</v-card-title>
            <v-card-text class="text-h6">{{ message | getMessage }}</v-card-text>
            <v-card-actions>
                <v-col class="text-lg-right">
                    <v-btn :color="confirmStyle" class="mx-2 request-loading" @click="ok">{{ confirmText }}</v-btn>
                    <v-btn :color="cancelStyle" @click="cancel">{{ cancelText }}</v-btn>
                </v-col>
            </v-card-actions>
        </v-card>
    </div>
</template>
<script>
import store from '../store/index';
export default {
    props: {
        type: {
            type: String,
            default: ''
        },
        title: {
            type: String,
            default: ''
        },
        message: null,
        confirmText: {
            type: String,
            default: '确定'
        },
        cancelText: {
            type: String,
            default: '取消'
        },
        confirmStyle: {
            type: String,
            default: 'primary'
        },
        cancelStyle: {
            type: String,
            default: 'normal'
        }
    },
    filters: {
        // 处理数据，如果数组则拼接
        getMessage(v) {
            let str = '';
            if (typeof v == 'object') {
                str = '你将要操作的选项：';
                for (const key in v) {
                    str += `[${v[key]}];`
                }
            } else {
                str = v;
            }
            return str
        }
    },
    data() {
        return {
            promiseStatus: null,
            show: false
        };
    },
    computed: {
        locale() {
            return store.state.app.locale || 'zh';
        },
        titles() {
            if (this.title) {
                return this.title;
            } else {
                if (store.state.app.locale == 'en') {
                    return 'Tips';
                } else {
                    return '提示';
                }
            }
        }
    },
    methods: {
        confirm() {
            let _this = this;
            this.show = true;
            return new Promise(function (resolve, reject) {
                _this.promiseStatus = { resolve, reject };
            });
        },
        ok() {
            this.show = false;
            this.promiseStatus && this.promiseStatus.resolve();
        },
        cancel() {
            this.show = false;
            this.promiseStatus && this.promiseStatus.reject();
        }
    }
};
</script>
<style lang="scss" scoped>
.dialogwrapper {
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    left: 0px;
    pointer-events: none;
    position: fixed;
    top: 0px;
    width: 100%;
    z-index: 19999 !important;
    transition: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1) 0s, z-index 1ms ease 0s;
    outline: none;
}

.dialog {
    overflow-y: auto;
    pointer-events: auto;
    width: 100%;
    z-index: inherit;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 11px 15px -7px, rgba(0, 0, 0, 0.14) 0px 24px 38px 3px, rgba(0, 0, 0, 0.12) 0px 9px 46px 8px;
    border-radius: 4px;
    padding: auto;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
    max-width: 500px;
}

.overlay {
    align-items: center;
    border-radius: inherit;
    display: flex;
    justify-content: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: auto;
    background: #000;
    opacity: 0.46;
}
</style>

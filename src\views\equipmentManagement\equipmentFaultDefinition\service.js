
import { getRequestResources } from '@/api/fetch';
const TPM = 'baseURL_TPM', DFM = 'baseURL_DFM'

// 根据产线查询设备列表
export function getEquipByLine(data) {
    const api = '/tpm/Equip/GetList'
    return getRequestResources(TPM, api, 'post', data)
}

// 根据设备查询bom 树
export function getBomTree(data) {
    const api = '/tpm/DeviceAccessories/GetDeviceAccessoriesTree'
    return getRequestResources(TPM, api, 'post', data)
}

// 保存故障定义表单
export function saveFaultForm(data) {
    const api = '/tpm/CauseOfFailure/SaveForm'
    return getRequestResources(TPM, api, 'post', data)
}

// 查询故障分类树
export function getFaultTree(data) {
    const api = '/tpm/CauseOfFailure/GetCauseOfFailureTree'
    return getRequestResources(TPM, api, 'post', data)
}

// 删除故障分类
export function delFault(data) {
    const api = '/tpm/CauseOfFailure/Delete'
    return getRequestResources(TPM, api, 'post', data)
}

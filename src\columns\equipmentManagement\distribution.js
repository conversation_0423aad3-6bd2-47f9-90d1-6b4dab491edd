export const distributionColum = [{
    text: '序号',
    value: 'Index',
    width: 90,
    sortable: true
}, {
    text: '出库单号',
    value: 'HistoryNo',
    Namevalue: "_ckdh",
    width: 150,
    sortable: true
}, {
    text: '关联单号',
    value: 'ReferNo',
    Namevalue: "_gldh",
    width: 150,
    sortable: true
}, {
    text: '状态',
    value: 'Status',
    Namevalue: "_zt",
    width: 80,
    sortable: true
},  {
    text: '出库数量',
    value: 'Qty',
    Namevalue: "_cksl",
    width: 100,
    sortable: true
},{
    text: '出库类型',
    value: 'Type',
    Namevalue: "_cklx",
    width: 150,
    sortable: true
},  {
    text: '备件编码',
    value: 'PartsCode',
    Namevalue: "_bjbm",
    width: 150,
    sortable: true
}, {
    text: '备件名称',
    value: 'PartsName',
    Namevalue: "_bjmc",
    width: 150,
    sortable: true
},{
    text: '备件科目',
    value: 'Subject',
    Namevalue: "_bjkm",
    width: 150,
    sortable: true
},  {
    text: '规格型号',
    value: 'PartsModel',
    Namevalue: "_ggxh",
    width: 150,
    sortable: true
}, {
    text: '备件类型',
    value: 'PartsType',
    Namevalue: "_bjlx",
    width: 150,
    sortable: true
}, {
    text: '批次号',
    value: 'BatchCode',
    Namevalue: "_pch",
    width: 150,
    sortable: true
}, {
    text: '出库仓库',
    value: 'Warehouse',
    Namevalue: "_ckck",
    width: 150,
    sortable: true
}, {
    text: '出库库位',
    value: 'StorageBin',
    Namevalue: "_ckkw",
    width: 150,
    sortable: true
}, {
    text: '退库数量',
    value: 'ReturnQty',
    Namevalue: "_thsl",
    width: 100,
    sortable: true
},{
    text: '申领人',
    value: 'Requester',
    Namevalue: "_slr",
    width: 100,
    sortable: true
}, {
    text: '申领时间',
    value: 'RequestDate',
    Namevalue: "_slsj",
    width: 150,
    sortable: true
}, {
    text: '管理人员',
    value: 'Manager',
    Namevalue: "_glry",
    width: 100,
    sortable: true
}, {
    text: '出库时间',
    value: 'OutstockDate',
    Namevalue: "_cksj",
    width: 150,
    sortable: true
}, {
    text: '采购开始时间',
    value: 'StartPurchaseDate',
    Namevalue: "_cgsksj",
    width: 150,
    sortable: true
}, {
    text: '采购结束时间',
    value: 'EndPurchaseDate',
    Namevalue: "_cgjssj",
    width: 150,
    sortable: true
}, {
    text: '操作',
    value: 'actions',
    Namevalue: "action",
    width: 200,
    sortable: true
}, ]
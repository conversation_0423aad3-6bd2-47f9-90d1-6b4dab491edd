import request from '@/util/request';
import Util from '@/util';
import { companyTree, EquipmentGetEquipmentTeamTree, companyList, getStaff, GetTimeDimension, getLineDataByFactory } from '@/api/common.js';
// import { UpgradeRuleSaveForm, getDepartment } from '@/api/andonManagement/upgradeRule.js';
import { StaffSchedulingList } from '@/api/personnelData/personnelScheduling.js'
import { getRequestResources } from '@/api/fetch';
import { GetEquipmentTree } from '@/api/factoryPlant/physicalModel.js';
const state = {
    simLvList: [],//SIM等级
    eventLevelList: [],//事件等级
    closeStatusList: [],//是否关闭
    // staffList: [],//公司的员工列表

    companyTree: [{}],//部门树
    EquipmentTeamTree: [{}],//车间产线树(Team)
    OriEquipmentTeamTree: [{}], //车间产线树源数据
    // EquipmentAreaTree: [{}], //车间产线树(Area)
    UnitList: [],//工序
    LineList: [],//产线下拉
    SegmentList: [],//工段
    TargetEDRList: [],//问题类型
    DepartmentList: [],//部门列表
    TeamList: [],//班组列表
    StaffList: [],//人员列表
    ClassfyCodeList: [],//事故类型列表
    TimeDimension: [],//时间维度
}

const getters = {
    // 将部门树打平
    flatCompany(state) {
        function getChildren(obj, list) {
            list.push(obj)
            if (obj.children && obj.children.length) {
                obj.children.forEach(element => {
                    getChildren(element, list)
                });
            }
            return list
        }
        let myList = []
        return getChildren(state.companyTree[0], myList)
    },
    // 将部门树打平
    flatEquipmentTeam(state) {
        function getChildren(obj, list) {
            list.push(obj)
            if (obj.children && obj.children.length) {
                obj.children.forEach(element => {
                    getChildren(element, list)
                });
            }
            return list
        }
        let myList = []
        return getChildren({ children: state.EquipmentTeamTree }, myList)
    },
    //子节点为产线的树
    EquipmentProductLineTree(state) {
        //递归处理叶子节点为undefined
        function setChildren(nodeList) {
            for (let i = 0; i < nodeList.length; i++) {
                const node = nodeList[i];
                if (node.extendField && node.extendField == 'ProductLine') {
                    node.children = undefined
                } else if (node.children && node.children.length == 0) {
                    node.children = undefined
                } else if (node.children && node.children.length > 0) {
                    setChildren(node.children)
                }
            }
        }
        let tree = JSON.parse(JSON.stringify(state.OriEquipmentTeamTree))
        setChildren(tree)
        return tree
    }
}

const mutations = {
    setSimLvList(state, payload) {
        state.simLvList = payload
    },
    setEventLevelList(state, payload) {
        state.eventLevelList = payload
    },
    setCloseStatusList(state, payload) {
        state.closeStatusList = payload
    },
    setCompanyList(state, payload) {
        state.staffList = payload
    },
    setCompanyTree(state, payload) {
        state.companyTree = payload
    },
    setOriEquipmentTeamTree(state, response) {
        //递归处理叶子节点
        function setChildren(nodeList) {
            if (nodeList) {
                for (let i = 0; i < nodeList.length; i++) {
                    const node = nodeList[i];
                    if (node.children && node.children.length == 0) {
                        node.children = undefined
                    } else {
                        setChildren(node.children)
                    }
                }
            }
        }
        setChildren(response)
        state.OriEquipmentTeamTree = response
    },
    setEquipmentTeamTree(state, { response, params }) {
        //递归处理叶子节点
        function setChildren(nodeList) {
            if (nodeList) {
                for (let i = 0; i < nodeList.length; i++) {
                    const node = nodeList[i];
                    if (node.extendField && node.extendField == params) {
                        node.children = undefined
                    } else if (node.children && node.children.length == 0) {
                        node.children = undefined
                    } else {
                        setChildren(node.children)
                    }
                }
            }
        }
        setChildren(response)
        state.EquipmentTeamTree = response
    },
    // setEquipmentAreaTree(state, {response,params}) {
    //     //递归处理叶子节点
    //     function setChildren(nodeList){
    //         for (let i = 0; i < nodeList.length; i++) {
    //             const node = nodeList[i];
    //             if(node.extendField && node.extendField == params){
    //                 node.children = undefined
    //             }else if(node.children && node.children.length == 0){
    //                 node.children = undefined
    //             }else{
    //                 setChildren(node.children)
    //             }
    //         }
    //     }
    //     setChildren(response)
    //     state.EquipmentAreaTree = response
    // },
    setUnitList(state, payload) {
        state.UnitList = payload
    },
    setLineList(state, payload) {
        state.LineList = payload
    },
    setSegmentList(state, payload) {
        state.SegmentList = payload
    },
    setTargetEDRList(state, payload) {
        state.TargetEDRList = payload
    },
    setDepartmentList(state, payload) {
        state.DepartmentList = payload
    },
    setTeamList(state, payload) {
        state.TeamList = payload
    },
    setStaffList(state, payload) {
        state.StaffList = payload
    },
    setClassfyCodeList(state, payload) {
        state.ClassfyCodeList = payload
    },
    setTimeDimension(state, payload) {
        state.TimeDimension = payload
    },
};

const actions = {
    async getEventLevelList({ commit }, params) {
        let res = await Util.getDataDictionary('eventLevel')
        commit('setEventLevelList', res)
    },
    async getSimLvlist({ commit }, params) {
        let res = await Util.getDataDictionary('SIMLevel')
        commit('setSimLvList', res)
    },
    async getCloseStatuslist({ commit }, params) {
        let res = await Util.getDataDictionary('closeStatus')
        commit('setCloseStatusList', res)
    },
    async getCompanyList({ commit }, params) {
        let { response } = await companyList()
        commit('setCompanyList', response.filter(item => item.LEVEL == 'Employee'))
    },
    //获取组织建模树
    async getCompanyTree({ commit }, params) {
        let { response } = await companyTree()
        commit('setCompanyTree', response)
    },
    async getEquipmentTeamTree({ commit }, params) {
        try {
            const result = await EquipmentGetEquipmentTeamTree(); // 确保这里有 await
            if (result && result.response) {
                // 保存源数据
                commit('setOriEquipmentTeamTree', JSON.parse(JSON.stringify(result.response)));
                commit('setEquipmentTeamTree', { response: result.response, params });
            } else {
                // 处理 result 或 result.response 为 undefined 的情况
                console.log('EquipmentGetEquipmentTeamTree returned undefined or does not have a response property.');
            }
        } catch (error) {
            // 处理异步操作中的错误
            console.log('Error while fetching equipment team tree:', error);
        }
    },
    //Plant:工厂 Area:车间 ProductLine:产线 Segment:工段 Unit:工作单元
    async getUnitList({ commit }, params) {
        let res = await Util.GetEquipmenByLevel('Unit');
        commit('setUnitList', res)
    },
    //工序
    async getLineList({ commit }, params) {
        let res = await getLineDataByFactory(params);
        commit('setLineList', res.response)
    },
    async getSegmentList({ commit }, params) {
        let res = await Util.GetEquipmenByLevel('Segment');
        commit('setSegmentList', res)
    },
    async getTargetEDRList({ commit }, params) {
        let res = await Util.getDataDictionary('TargetEDR')
        commit('setTargetEDRList', res)
    },
    // 获取部门列表
    async getDepartmentList({ commit }, params) {
        let res = await Util.GetDepartmentByLevel('Department')
        commit('setDepartmentList', res)
    },
    // 获取班组列表
    async getTeamList({ commit }, params) {
        let res = await Util.GetDepartmentByLevel('Team')
        commit('setTeamList', res)
    },
    // 获取人员列表
    async getStaffList({ commit }, params) {
        let { response } = await getStaff()
        commit('setStaffList', response)
    },

    //获取事故类型
    async getClassfyCodeList({ commit }, params) {
        let res = await Util.getDataDictionary('safeType')
        commit('setClassfyCodeList', res)
    },
    // 获取时间维度
    async getTimeDimension({ commit }, params) {
        let res = await GetTimeDimension();
        commit('setTimeDimension', res.response)
    },
}
export default {
    namespace: true,
    state,
    getters,
    mutations,
    actions,
}
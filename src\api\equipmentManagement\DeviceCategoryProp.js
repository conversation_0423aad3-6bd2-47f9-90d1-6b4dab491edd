import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_EQUIPMENT';

// 点检任务看板
export function DeviceCategoryPropGetList(data) {
    const api = '/api/DeviceCategoryProp/GetList';
    return getRequestResources(baseURL, api, 'post', data);
}
// 点检任务看板
export function DeviceCategoryPropSaveForm(data) {
    const api = '/api/DeviceCategoryProp/SaveForm';
    return getRequestResources(baseURL, api, 'post', data);
}
// 点检任务看板
export function DeviceCategoryPropDelete(data) {
    const api = '/api/DeviceCategoryProp/Delete';
    return getRequestResources(baseURL, api, 'post', data);
}
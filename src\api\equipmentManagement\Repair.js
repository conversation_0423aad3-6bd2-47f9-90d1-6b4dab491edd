import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TPM; // 配置服务url
const shiftUrl = configUrl[process.env.VUE_APP_SERVE].baseURL_SHIFT; // 配置服务url
const dfmUrl = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url
const andonUrl = configUrl[process.env.VUE_APP_SERVE].baseURL_ANDON; // 配置服务url
//获取设备修理工单
export function DeviceRepairWoGetPageList(data) {
    return request({
        url: baseURL + '/tpm/DeviceRepairWo/GetPageList',
        method: 'post',
        data
    });
}
//新增&保存
export function DeviceRepairWoSaveForm(data) {
    return request({
        url: baseURL + '/tpm/DeviceRepairWo/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function DeviceRepairWoDelete(data) {
    return request({
        url: baseURL + '/tpm/DeviceRepairWo/Delete',
        method: 'post',
        data
    });
}
//维修记录  不传ID 查全部
export function DeviceRepairGetPageList(data) {
    return request({
        url: baseURL + '/tpm/DeviceRepair/GetPageList',
        method: 'post',
        data
    });
}
//新增&保存
export function DeviceRepairSaveForm(data) {
    return request({
        url: baseURL + '/tpm/DeviceRepair/SaveForm',
        method: 'post',
        data
    });
}

// 维修状态同步
export function UpdateStatusForm(data) {
    return request({
        url: baseURL + '/tpm/DeviceRepairWo/UpdateStatusForm',
        method: 'post',
        data
    });
}

// 删除
export function DeviceRepairDelete(data) {
    return request({
        url: baseURL + '/tpm/DeviceRepair/Delete',
        method: 'post',
        data
    });
}

// 调试明细
export function CommissioningRecordsGetPageList(data) {
    return request({
        url: baseURL + '/tpm/CommissioningRecords/GetPageList',
        method: 'post',
        data
    });
}
//新增&保存
export function CommissioningRecordsSaveForm(data) {
    return request({
        url: baseURL + '/tpm/CommissioningRecords/SaveForm',
        method: 'post',
        data
    });
}
// 删除
export function CommissioningRecordsDelete(data) {
    return request({
        url: baseURL + '/tpm/CommissioningRecords/Delete',
        method: 'post',
        data
    });
}

// 维修价格
export function GetPricePageList(data) {
    return request({
        url: baseURL + '/tpm/DeviceRepair/GetPricePageList',
        method: 'post',
        data
    });
}
// 查询设备BOM
export function DeviceAccessoriesGetList(data) {
    return request({
        url: baseURL + '/tpm/DeviceAccessories/GetList',
        method: 'post',
        data
    });
}

//  获取人员列表
export function getStaff(data) {
    return request({
        url: dfmUrl + '/api/Staff/GetList',
        method: 'post',
        data
    });
}

export function SparepartGetList(data) {
    return request({
        url: baseURL + '/tpm/SparePartsStock/GetList',
        method: 'post',
        data
    });
}
// 获取保养规则列表
export function getMaintainRuleList(data) {
    return request({
        url: baseURL + '/tpm/MaintainRule/GetList',
        method: 'post',
        data
    });
}
// 查询备件
export function getSpareParts(data) {
    return request({
        url: baseURL + '/tpm/SparePartsStock/GetList',
        method: 'post',
        data
    });
}

// 获取岗位成本
export function getPostPrice(data) {
    return request({
        url: dfmUrl + '/api/Post/GetList',
        method: 'post',
        data
    })
}

//  获取维修时间
export function getMaintainTime(data) {
    return request({
        url: andonUrl + '/andon/AlarmTrace/GetListByEventNo?eventNo=' + data.eventNo,
        method: 'post',
        data
    })
}
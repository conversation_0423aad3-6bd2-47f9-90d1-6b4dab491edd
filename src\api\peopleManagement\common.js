import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_SHIFT; // 配置服务url
//  考计件薪资
export function WageRecordGetPageList(data) {
    return request({
        url: baseURL + '/shift/WageRecord/GetPageList',
        method: 'post',
        data
    });
}
// 人员工时
export function StaffWorktimesGetPageList(data) {
    return request({
        url: baseURL + '/shift/StaffWorktimes/GetPageList',
        method: 'post',
        data
    });
}

// UWB人员工时明细
export function GetWorkTimesDetail(data) {
    return request({
        url: baseURL + '/shift/StaffWorktimes/GetWorkTimesDetail',
        method: 'post',
        data
    });
}

// 人员工时同步
export function SyncStaffWorkTimes(data) {
    return request({
        url: baseURL + '/shift/StaffWorktimes/SyncStaffWorkTimes',
        method: 'post',
        data
    });
}
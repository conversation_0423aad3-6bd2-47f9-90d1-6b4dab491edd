<template>
  <v-card>
    <v-card-title
      class="headline primary lighten-2"
      primary-title
    >
      SIM看板配置
    </v-card-title>
    <v-card-text style="padding: 0;">
      <v-stepper v-model="e1">
        <v-stepper-header style="justify-content: flex-start;height:none">
          <v-stepper-step
            :complete="e1 > 1"
            step="1"
            style="padding: 12px;"
          >
            主体配置
          </v-stepper-step>
          <v-stepper-step
            :complete="e1 > 2"
            step="2"
            style="padding: 12px;"
          >
            查询条件配置
          </v-stepper-step>
          <v-stepper-step
            :complete="e1 > 3"
            step="3"
            style="padding: 12px;"
          >
            左侧区域配置
          </v-stepper-step>
          <v-stepper-step
            :complete="e1 > 4"
            step="4"
            style="padding: 12px;"
          >
            中间区域配置
          </v-stepper-step>
          <v-stepper-step
            :complete="e1 > 5"
            step="5"
            style="padding: 12px;"
          >
            右侧区域配置
          </v-stepper-step>
        </v-stepper-header>
        <v-stepper-items style="height: 100%;overflow: auto;">
          <v-stepper-content step="1">
            <steupOne
              v-if="oneFlag || dialogType == 'add'"
              :oneList1="oneList"
              :OneId="OneId"
              ref="steupOneref"
              :dialogType="dialogType"
              @savesteupOne="steupOneList"
            />
          </v-stepper-content>
          <v-stepper-content step="2">
            <steupTwo
              v-if="oneFlag || dialogType == 'add'"
              :oneList1="oneList"
              :TwoId="TwoId"
              :Moudelname="dialogType == 'add' ? listZ?.Moudelname?.Fullname : listZ.Moudelname"
              ref="steupTworef"
              :dialogType="dialogType"
              @savesteupTwo="steupTwoList"
            />
          </v-stepper-content>
          <v-stepper-content step="3">
            <steupThr
              :dutyListData2="dutyListData2"
              :dutyListData22="dutyListData22"
              :simlevel="simlevel"
              :Moudelname="dialogType == 'add' ? listZ?.Moudelname?.Fullname : (listZ.Moudelname + '-' + listZ.selectCode)"
              :OneId="OneId"
              :dialogType="dialogType"
              ref="steupThrref"
              :selectCode="selectCode"
              :listData1="listData"
              @savesteupThr="steupThrList"
            />
          </v-stepper-content>
          <v-stepper-content step="4">
            <steupFou
              :dutyListData2="dutyListData2"
              :dutyListData22="dutyListData22"
              :selectCode="selectCode"
              :simlevel="simlevel"
              :Moudelname="dialogType == 'add' ? listZ?.Moudelname?.Fullname : (listZ.Moudelname + '-' + listZ.selectCode)"
              :OneId="OneId"
              :dialogType="dialogType"
              ref="steupFouref"
              :xlNum3="xlNum"
              :listData1="listData"
              @savesteupFou="steupFouList"
            />
          </v-stepper-content>
          <v-stepper-content step="5">
            <steupFir
              :dutyListData2="dutyListData2"
              :dutyListData22="dutyListData22"
              :selectCode="selectCode"
              :simlevel="simlevel"
              :Moudelname="dialogType == 'add' ? listZ?.Moudelname?.Fullname : (listZ.Moudelname + '-' + listZ.selectCode)"
              :OneId="OneId"
              :dialogType="dialogType"
              ref="steupFirref"
              :xlNum5="xlNum4"
              :listData1="listData"
              @savesteupFir="steupFirList"
            />
          </v-stepper-content>

        </v-stepper-items>
      </v-stepper>

    </v-card-text>
    <v-divider></v-divider>

    <v-card-actions style="justify-content: flex-end;">
      <v-btn
        color="primary"
        @click="change_back"
      >上一步</v-btn>
      <v-btn
        :disabled="!xflag"
        color="primary"
        @click="change_nextStep"
      >下一步</v-btn>
      <v-btn
        :disabled="e1 < 2"
        color="primary"
        @click="e1Change(e1)"
      >{{ $t('GLOBAL._QD') }}</v-btn>
      <v-btn
        color="normal"
        @click="closeChange"
      >{{ $t('GLOBAL._GB') }}</v-btn>
    </v-card-actions>
  </v-card>
</template>

<script>
import { getInsert, getInsert1, getDataSourceList, getEditEntityByCode, getEditEntityByCodeone } from '@/api/simConfig/simconfignew.js';
import { mapGetters } from 'vuex';
export default {
  components: {
    steupOne: () => import('./steupOne.vue'),
    steupTwo: () => import('./steupTwo.vue'),
    steupThr: () => import('./steupThr.vue'),
    steupFou: () => import('./steupFou.vue'),
    steupFir: () => import('./steupFir.vue')
  },
  props: {
    selectList: {
      type: Array,
      default: () => []
    },
    tableItem: {
      type: Object,
      default: () => { }
    },
    dialogType: {
      type: String,
      default: ''
    },
    departmentData: {
      type: Array,
      default: () => []
    },
    Searchconfig1: {
      type: Array,
      default: () => []
    },
    OneId: {
      type: String,
      default: ''
    },
    TwoId: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dutyListData22: [],
      listData: {},
      save3Id: '',
      save2Id: '',
      selectCode: '',
      oneImgList: [],
      rightImgList: [],
      centerImgList: [],
      leftImgList: [],
      thrId: '',
      showDialog: false,
      e1: 1,
      valid: false,
      listZ: {},
      listLeft: [],
      listRight: [],
      listCenter: [],
      rightWh: {},
      leftWh: {},
      centerWh: {},
      xlNum: 0,
      xlNum4: 0,
      xflag: true,
      dutyListData1: [],
      dutyListData2: [],
      oneList: {},
      oneFlag: false
    };
  },
  computed: {
    EquipmentProductLineTree() {
      return this.$store.getters.EquipmentProductLineTree
    },
    ...mapGetters(['getUserinfolist']),
    curAuth() {
      return this.$store.getters.getUserinfolist[0]
    },
  },
  created() {
    // this.querySelectList()
    if (this.dialogType == 'edit') {
      this.queryEditListone()
      this.queryEditList()
    }
  },
  methods: {
    async queryEditListone() {
      this.deailFileList = []
      const params = {
        code: this.OneId
      }
      const res = await getEditEntityByCodeone(params);

      if (res.success) {
        this.oneList = res.response
        this.oneFlag = true
        // this.form.Moudelname = res.response.Moudelname
        // this.form.Olinetit = res.response.Olinetit
        // this.form.selectCode = res.response.Simlevel
        // this.selectName = res.response.Simlevel
        // if (res.response.Baroundimg) {
        //   const res2 = await getImageUel(res.response.Baroundimg);
        //   this.form.img = res.response.Baroundimg
        //   this.deailFileList.push({
        //     name: res.response.Baroundimg,
        //     url: res2.response
        //   })

        // }
      }
    },
    async queryEditList() {
      // this.deailFileList1 = []
      const params = {
        code: this.OneId
      }
      const res = await getEditEntityByCode(params);
      if (res.success) {
        this.listData = res.response
        // if (JSON.parse(res.response.Leftjosn).listChart.length > 0) {
        //   this.cardNum = JSON.parse(res.response.Leftjosn).listChart
        //   this.form.leftWidth = JSON.parse(res.response.Leftjosn).leftWidth
        //   this.form.leftHeight = JSON.parse(res.response.Leftjosn).leftHeight
        //   this.form.RegionNum = JSON.parse(res.response.Leftjosn).listChart.length
        //   this.xlNum = JSON.parse(res.response.Leftjosn).listChart.length
        //   // this.deailFileList1 = JSON.parse(res.response.Leftjosn).deailFileList || []
        //   if (JSON.parse(res.response.Leftjosn).img1) {
        //     this.form.img1 = JSON.parse(res.response.Leftjosn).img1
        //     const res2 = await getImageUel(JSON.parse(res.response.Leftjosn).img1);
        //     this.deailFileList1.push({
        //       name: JSON.parse(res.response.Leftjosn).img1,
        //       url: res2.response
        //     })
        //   }

        //   this.cardNum.map(async (a, b) => {
        //     if (a.deailFileList.length > 0) {
        //       const resh3 = await getImageUel(a.deailFileList[0].name);
        //       a.deailFileList[0].push({
        //         name: a.deailFileList[0].name,
        //         url: resh3.response
        //       })

        //     }
        //     a.tabCardNum.map(async (a1, b1) => {
        //       if (a1.deailFileList.length > 0) {
        //         const resh34 = await getImageUel(a1.deailFileList[0].name);
        //         a1.deailFileList[0].push({
        //           name: a1.deailFileList[0].name,
        //           url: resh34.response
        //         })
        //       }
        //     })
        //   })
        //   this.$emit('savesteupThr', this.cardNum, this.form, this.xlNum, this.thrId, this.deailFileList1, this.zlistData)
        //   this.thrId = res.response.ID
        //   if (this.dialogType == 'edit')
        //     this.Pageid = res.response.Pageid
        // }
      }

    },
    async querySelectList() {
      this.dutyListData2 = []
      const params = {
        "SIMLevel": this.simlevel == '' ? this.Moudelname : this.simlevel,
        "KpiType": ''
      }
      const res = await getDataSourceList(params);
      if (res != undefined) {
        this.dutyListData22 = res.response
        res.response.map(el => {
          this.dutyListData2.push({
            "TagName": el.KpiName,
            "TagCode": el.KpiCode
          })
        })
      }
    },
    change_back() {
      if (this.e1 > 1 && this.e1 <= 5) --this.e1
    },
    // getOneList() {
    //   this.$refs.steupOneref.save()
    // },
    change_nextStep() {
      this.queryEditList()
      if (this.e1 <= 4) {
        this.$refs.steupOneref.save()
        this.$refs.steupOneref.save2()
        this.$refs.steupTworef.save()
        this.$refs.steupThrref.save()
        this.$refs.steupFouref.save()
        this.$refs.steupFirref.save()

        // 注释开始
        // this.$refs.steupOneref.save()
        // this.$refs.steupOneref.save2()
        // this.$refs.steupTworef.save()
        // this.$refs.steupThrref.save()
        // this.$refs.steupFouref.save()
        // this.$refs.steupFirref.save()
        // 注释结束




        // if (this.e1 >= 2) {
        //   setTimeout(() => {
        //     this.$refs.steupThrref.save()
        //     this.$refs.steupFouref.save()
        //     this.$refs.steupFirref.save()
        //   })
        // }
        this.e1++


      }
      // if (this.dialogType == 'edit') {
      //   this.$refs.steupOneref.save()
      //   this.$refs.steupOneref.save2()
      //   this.$refs.steupTworef.save()
      //   this.$refs.steupThrref.save()
      //   this.$refs.steupFouref.save()
      //   this.$refs.steupFirref.save()
      //   // if (this.e1 == 1) {
      //   //   this.$refs.steupOneref.save()
      //   //   this.$refs.steupOneref.save2()
      //   //   this.$refs.steupTworef.save()
      //   //   this.$refs.steupFirref.save()
      //   // }
      //   // if (this.e1 == 2) {
      //   //   this.$refs.steupThrref.save()
      //   //   this.$refs.steupFouref.save()
      //   //   this.$refs.steupFirref.save()
      //   // }
      //   // if (this.e1 == 3) {
      //   //   this.$refs.steupFouref.save()
      //   // }
      //   // if (this.e1 == 4) {
      //   //   this.$refs.steupFirref.save()
      //   // }
      // } else {
      //   this.$refs.steupOneref.save()
      //   this.$refs.steupOneref.save2()
      //   this.$refs.steupTworef.save()
      //   this.$refs.steupThrref.save()
      //   this.$refs.steupFouref.save()
      //   this.$refs.steupFirref.save()
      // }
    },
    async submitForm1() {
      this.$refs.steupOneref.save()
      this.$refs.steupTworef.save()
      if (this.dialogType == 'add') {
        if (this.listZ.Moudelname.Fullname == '' || this.listZ.Moudelname.Fullname == undefined || this.listZ.Moudelname.Fullname == null) {
          this.$store.commit('SHOW_SNACKBAR', { text: this.$t('主体配置中的SIM层级不能为空'), color: 'red' });
          return
        }
      }
      if (this.dialogType == 'edit') {
        if (this.listZ.Moudelname == '' || this.listZ.Moudelname == null || this.listZ.Moudelname == undefined) {
          this.$store.commit('SHOW_SNACKBAR', { text: this.$t('主体配置中的SIM层级不能为空'), color: 'red' });
          return
        }
      }
      if (this.listZ.Olinetit == '' || this.listZ.Olinetit == undefined || this.listZ.Olinetit == null) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('主体配置中的看板标题不能为空'), color: 'red' });
        return
      }
      if (this.listZ.img == '' || this.listZ.img == undefined || this.listZ.img == null) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('主体配置中的背景图片不能为空'), color: 'red' });
        return
      }

      if (this.selectCode == '' || this.selectCode == undefined || this.selectCode == null) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('查询条件配置中的成本中心不能为空'), color: 'red' });
        return
      }
      try {
        const params = {
          "Id": this.dialogType == 'edit' ? this.OneId : '',
          "Simcode": (this.simlevel == '' ? this.listZ.Moudelname.Fullname : this.simlevel) + '-' + this.selectCode,  // this.listZ.Moudelname
          "Moudelname": this.listZ.Moudelname.Fullname,
          "Olinetit": this.listZ.Olinetit,
          "Baroundimg": this.listZ.img,
          "CreateUserId": this.getUserinfolist[0].UserNo,
          "Simlevel": this.selectCode,
          // "deailFileList": this.oneImgList
        }
        const res = await getInsert(params);
        this.save2Id = res.response

        if (res.success) {
          this.xflag = true
          this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });
        }
      } catch (error) {
        this.xflag = false
      }
    },
    async submitForm2(e) {
      this.$refs.steupThrref.save3()
      this.$refs.steupFouref.save3()
      this.$refs.steupFirref.save3()
      console.log(this.listCenter, 'this.listCenterthis.listCenterthis.listCenterthis.listCenter');


      if ((this.listData?.ID != null || this.listData?.ID != '' || this.listData?.ID != undefined) && this.dialogType == 'edit') {
        this.listCenter.map(el => {
          el.tabCardNum.sort((a, b) => a.tabChartIndex - b.tabChartIndex);
        })
        if (this.listCenter.length > 0) {
          if (this.listCenter[0].tabCardNum.length > 0) {
            let names = this.listCenter[0].tabCardNum.map(item => item["Order"]);
            let nameSet = new Set(names)
            if (nameSet.size == names.length) {
              console.log("没有重复值");
            } else {
              this.$store.commit('SHOW_SNACKBAR', { text: '位置存在重复,请检查', color: 'red' });
              return
            }
          }
        }
        // var flag = this.listCenter[0].tabCardNum.some(el => {
        //   return el.Order == el.Order
        // })
        // console.log(flag, '9999');

        // if (flag) {
        //   this.$store.commit('SHOW_SNACKBAR', { text: '位置存在重复,请检查', color: 'red' });
        //   return
        // }
        // var flag
        // const targetField = "Order";
        // const valuesSet = new Set();
        // for (const obj of this.listCenter[0].tabCardNum) {
        //   if (valuesSet.has(obj[targetField])) {
        //     flag = true;
        //     return;
        //   }
        //   valuesSet.add(obj[targetField]);
        // }
        // flag = false;
        // if (flag) {
        //   this.$store.commit('SHOW_SNACKBAR', { text: '位置存在重复,请检查', color: 'red' });
        //   return
        // }

        const params1 = {
          "Simlevel": (this.simlevel == '' ? this.listZ.Moudelname.Fullname : this.simlevel),
          "PageId": this.listData == null ? this.oneList.ID : this.listData.Pageid,
          "Id": this.dialogType == 'add' ? this.save3Id : (this.listData == null ? this.save3Id : this.listData.ID),
          "CreateUserId": this.getUserinfolist[0].UserNo,
          "Rightjosn": JSON.stringify(
            {
              rightWidth: this.rightWh.rightWidth,
              rightHeight: this.rightWh.rightHeight,
              img1: this.rightWh.img1,
              deailFileList: this.rightImgList,
              listChart: this.listRight
            }
          ),
          "Conterjosn": JSON.stringify(
            {
              centerWidth: this.centerWh.centerWidth,
              centerHeight: this.centerWh.centerHeight,
              img1: this.centerWh.img1,
              deailFileList: this.centerImgList,
              listChart: this.listCenter
            }
          ),
          "Leftjosn": JSON.stringify(
            {
              leftWidth: this.leftWh.leftWidth,
              leftHeight: this.leftWh.leftHeight,
              img1: this.leftWh.img1,
              deailFileList: this.leftImgList,
              listChart: this.listLeft
            }
          ),
        }
        const res1 = await getInsert1(params1);
        if (res1.success) {
          this.save3Id = res1.response
          this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });
          this.queryEditList()
          if (this.e1 == 5) {
            this.$emit('checkCrea')
            this.e1 = 1
          }
        }
      }
      if (this.dialogType == 'add') {
        if (this.e1 == 3) {
          this.$refs.steupThrref.save3()
          if (this.leftWh.leftWidth == '' || this.leftWh.leftWidth == undefined || this.leftWh.leftWidth == null) {
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('*号为必填项'), color: 'red' });
            return
          }
          if (this.leftWh.leftHeight == '' || this.leftWh.leftHeight == undefined || this.leftWh.leftHeight == null) {
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('*号为必填项'), color: 'red' });
            return
          }
          var flagModularName = this.listLeft.some(el => {
            return (el.ModularName == "" || null || undefined) && el.OlineType?.Fullname != 'tab切换'
          })
          if (flagModularName) {
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('*号为必填项'), color: 'red' });
            return
          }
          var flagwidth = this.listLeft.some(el => {
            return el.style.width == "" || null || undefined
          })
          if (flagwidth) {
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('*号为必填项'), color: 'red' });
            return
          }
          var flagheight = this.listLeft.some(el => {
            return el.style.height == "" || null || undefined
          })
          if (flagheight) {
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('*号为必填项'), color: 'red' });
            return
          }
        }

        if (this.e1 == 4) {
          this.$refs.steupFouref.save3()
          if (this.centerWh.centerWidth == '' || this.centerWh.centerWidth == undefined || this.centerWh.centerWidth == null) {
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('*号为必填项'), color: 'red' });
            return
          }
          if (this.centerWh.centerHeight == '' || this.centerWh.centerHeight == undefined || this.centerWh.centerHeight == null) {
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('*号为必填项'), color: 'red' });
            return
          }
          var flagModularNamecen = this.listCenter.some(el => {
            return (el.ModularName == "" || null || undefined) && el.OlineType?.Fullname != 'tab切换'
          })
          if (flagModularNamecen) {
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('*号为必填项'), color: 'red' });
            return
          }
          var flagwidthcen = this.listCenter.some(el => {
            return el.style.width == "" || null || undefined
          })
          if (flagwidthcen) {
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('*号为必填项'), color: 'red' });
            return
          }
          var flagheightcen = this.listCenter.some(el => {
            return el.style.height == "" || null || undefined
          })
          if (flagheightcen) {
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('*号为必填项'), color: 'red' });
            return
          }
        }
        if (this.e1 == 5) {
          this.$refs.steupFirref.save3()
          if (this.rightWh.rightWidth == '' || this.rightWh.rightWidth == undefined || this.rightWh.rightWidth == null) {
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('*号为必填项'), color: 'red' });
            return
          }
          if (this.rightWh.rightHeight == '' || this.rightWh.rightHeight == undefined || this.rightWh.rightHeight == null) {
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('*号为必填项'), color: 'red' });
            return
          }
          var flagModularNamerig = this.listRight.some(el => {
            return (el.ModularName == "" || null || undefined) && el.OlineType?.Fullname != 'tab切换'
          })
          if (flagModularNamerig) {
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('*号为必填项'), color: 'red' });
            return
          }
          var flagwidthrig = this.listRight.some(el => {
            return el.style.width == "" || null || undefined
          })
          if (flagwidthrig) {
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('*号为必填项'), color: 'red' });
            return
          }
          var flagheightrig = this.listRight.some(el => {
            return el.style.height == "" || null || undefined
          })
          if (flagheightrig) {
            this.$store.commit('SHOW_SNACKBAR', { text: this.$t('*号为必填项'), color: 'red' });
            return
          }
        }
        const params1 = {
          "Simlevel": (this.simlevel == '' ? this.listZ.Moudelname.Fullname : this.simlevel),
          "PageId": this.save2Id,
          "Id": this.save3Id,
          "CreateUserId": this.getUserinfolist[0].UserNo,
          "Rightjosn": JSON.stringify(
            {
              rightWidth: this.rightWh.rightWidth,
              rightHeight: this.rightWh.rightHeight,
              img1: this.rightWh.img1,
              deailFileList: this.rightImgList,
              listChart: this.listRight
            }
          ),
          "Conterjosn": JSON.stringify(
            {
              centerWidth: this.centerWh.centerWidth,
              centerHeight: this.centerWh.centerHeight,
              img1: this.centerWh.img1,
              deailFileList: this.centerImgList,
              listChart: this.listCenter
            }
          ),
          "Leftjosn": JSON.stringify(
            {
              leftWidth: this.leftWh.leftWidth,
              leftHeight: this.leftWh.leftHeight,
              img1: this.leftWh.img1,
              deailFileList: this.leftImgList,
              listChart: this.listLeft
            }
          ),
        }
        const res1 = await getInsert1(params1);
        if (res1.success) {
          this.save3Id = res1.response
          this.$store.commit('SHOW_SNACKBAR', { text: '保存成功', color: 'success' });
          if (this.e1 == 5) {
            this.showDialog = false
            this.e1 = 1
          }
        }
      }
    },
    e1Change(e) {
      if (e == 2) {
        this.submitForm1()
      } else {
        this.submitForm2()
      }
    },
    steupOneList(data, deailFileList) {
      this.listZ = data
      this.oneImgList = deailFileList
    },
    steupTwoList(data) {
      console.log(data, 'data9999');

      this.selectCode = data
    },
    steupThrList(data, data1, data2, data3, deailFileList1, zlistData, data4) {

      this.listLeft = data
      this.leftWh = data1
      this.xlNum = data2
      this.thrId = data3
      this.leftImgList = deailFileList1
      // this.listData = zlistData
      if (this.dialogType == 'edit') {
        this.save2Id = data4
      }

    },
    steupFouList(data, data1, data2, deailFileList1) {
      this.listCenter = data
      this.centerWh = data1
      this.xlNum4 = data2 + 1
      this.centerImgList = deailFileList1

    },
    steupFirList(data, data1, deailFileList1) {
      this.listRight = data
      this.rightWh = data1
      this.rightImgList = deailFileList1
    },
    closeChange() {
      this.$emit('checkCrea')

      // this.showDialog = false
      // this.e1 = 1
      // this.$refs.steupOneref.save1()
      // this.$refs.steupTworef.save1()
      // this.$refs.steupThrref.save1()
      // this.$refs.steupFouref.save1()
      // this.$refs.steupFirref.save1()
    }
  }


};
</script>
<style lang="less" scoped>
.type-list {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    // height: 120px;
    margin-bottom: 10px;

    // border: 1px solid red;
    .type-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        cursor: pointer;
        padding: 0 10px;
        // height: 120px;
        width: 18%;
        border: 1px solid #ccc;

        &.active {
            border: 1px solid #007aff;

            .type-title {
                color: #007aff;
            }
        }

        .type-title {
            font-weight: 600;
            color: #151515;
            line-height: 30px;
            margin: 0;
            text-align: center;
        }

        img {
            // width: 100px;
            width: 80%;
            height: 100px;
        }
    }
}

.my-title {
    font-size: 14px;

    &-1 {
        display: block;
        margin-bottom: 0.5em;
    }

    &-2 {
        display: block;
        transform: translateX(-12px);
    }
}

.v-input--selection-controls {
    margin: 0;
}

.color-line {
    display: flex;
    align-items: baseline;
}

.time-list {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
}

.color-legend {
    width: 15px;
    height: 15px;
    margin-right: 5px;
    background: #ccc;
}

.yaxis-line {
    margin: 0;
}

.radio-box {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
}

.v-stepper__header {
    height: auto;
}
</style>
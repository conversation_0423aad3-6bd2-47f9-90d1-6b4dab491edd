import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Inventory'
const baseURL2 = 'baseURL_MATERIAL'

export function GetPageList(data) {
    const api = '/api/VerifiyDetail/GetPageList_HT'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetList(data) {
    const api = '/api/VerifiyDetail/GetHT_DetailByID'
    return getRequestResources(baseURL, api, 'post', data);
}
export function AddDetail_HT(data) {
    const api = '/api/VerifiyDetail/AddDetail_HT'
    return getRequestResources(baseURL, api, 'post', data);
}
export function ModifyDetail_HT(data) {
    const api = '/api/VerifiyDetail/ModifyDetail_HT'
    return getRequestResources(baseURL, api, 'post', data);
}
export function UpInVentAll(data) {
    const api = '/api/VerifiyDetail/UpInVentAll'
    return getRequestResources(baseURL, api, 'post', data);
}
export function SapReportWork(data) {
    const api = '/api/VerifiyDetail/SapReportWork'
    return getRequestResources(baseURL, api, 'post', data);
}
export function Add_HT(data) {
    const api = '/api/VerifiyDetail/Add_HT'
    return getRequestResources(baseURL, api, 'post', data);
}
export function Delete_HT(data) {
    const api = '/api/VerifiyDetail/Delete_HT'
    return getRequestResources(baseURL, api, 'post', data);
}
export function ModifyDetailQS_HT(data) {
    const api = '/api/VerifiyDetail/ModifyDetailQS_HT'
    return getRequestResources(baseURL, api, 'post', data);
}

export function NewAddDetail_HT(data) {
    const api = '/api/VerifiyDetail/NewAddDetail_HT'
    return getRequestResources(baseURL, api, 'post', data);
}
// export function Reverse(data) {
//     const api = '/api/ProductionSummaryDetailsView/Reverse'
//     return getRequestResources(baseURL, api, 'post', data);
// }
// export function GetReasonCode(data) {
//     const api = '/api/Dataitemdetail/GetReasonCode'
//     return getRequestResources(baseURL, api, 'post', data);
// }
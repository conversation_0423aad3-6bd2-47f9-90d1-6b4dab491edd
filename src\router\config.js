import {LayoutAuth, LayoutDefault, RouteWrapper} from '@/components/layouts';

export const publicRoute = [{
    path: '*',
    component: () =>
        import('@/views/error/NotFound.vue')
},
    {
        path: '/auth',
        component: LayoutAuth,
        meta: {
            title: 'Login'
        },
        redirect: '/auth/login',
        hidden: true,
        children: [{
            path: 'login',
            name: 'login',
            meta: {
                title: 'Login'
            },
            component: () =>
                import('@/views/auth/Login.vue')
        }]
    },

    {
        path: '/404',
        name: '404',
        meta: {
            title: 'Not Found'
        },
        component: () =>
            import('@/views/error/NotFound.vue')
    },
    {
        path: '/500',
        name: '500',
        meta: {
            title: 'Server Error'
        },
        component: () =>
            import('@/views/error/Error.vue')
    }
    // {
    //     path: '/dashboard',
    //     name: 'iframe',
    //     meta: {
    //         title: 'iframe',
    //         icon: 'mdi-view-dashboard'
    //     },
    //     component: () => import('@/views/iframe')
    // },
];

export const protectedRoute = [{
    path: '/',
    component: LayoutDefault,
    meta: {
        title: 'home',
        icon: ''
    },
    redirect: '/dashboard',
    children: [
        // 首页
        {
            path: '/dashboard',
            name: 'dashboard',
            meta: {
                title: 'dashboard',
                icon: 'mdi-view-dashboard'
            },
            component: () =>
                import('@/views/Dashboard.vue')
            // component: () => import('@/views/iframe')
        },
        {
            path: '/demo',
            name: 'iframe',
            meta: {
                title: 'iframe',
                icon: 'mdi-view-dashboard'
            },
            component: () =>
                import('@/views/demo')
        },
        {
            path: '/qualityManagement',
            meta: {
                title: 'DFM_JHGL',
                icon: 'mdi-drag-variant'
            },
            name: '质量管理',
            component: RouteWrapper,
            redirect: '/qualityManagement/disassembleDefectiveProducts',
            children: [{
                path: '/qualityManagement/disassembleDefectiveProducts',
                name: '次品拆解',
                meta: {
                    title: 'DFM_CPCJ._CPCJ',
                    icon: 'mdi-alpha-a'
                },
                component: () =>
                    import('@/views/qualityManagement/disassembleDefectiveProducts/index.vue')
            },
                {
                    path: '/qualityManagement/inspectionItem',
                    name: '检验项目',
                    meta: {
                        title: 'DFM_JYXM._JYXM',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/inspectionItem/index.vue')
                },
                {
                    path: '/qualityManagement/inspectionSection',
                    name: '检验组',
                    meta: {
                        title: '检验组',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/inspectionSection/index.vue')
                },
                {
                    path: '/qualityManagement/inspectionTemplate',
                    name: '检验模板',
                    meta: {
                        title: '检验模板',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/inspectionTemplate/index.vue')
                },
                {
                    path: '/qualityManagement/inspectionPlan',
                    name: '检验计划',
                    meta: {
                        title: 'DFM_JYJH._JYJH',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/inspectionPlan/index.vue')
                },
                {
                    path: '/qualityManagement/firstInspection',
                    name: '首检记录',
                    meta: {
                        title: 'DFM_SJJL._SJJL',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/firstInspection/index.vue')
                },
                {
                    path: '/qualityManagement/routingInspection',
                    name: '巡检记录',
                    meta: {
                        title: 'DFM_XJJL._XJJL',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/routingInspection/index.vue')
                },
                {
                    path: '/qualityManagement/spotCheck',
                    name: '过程抽检',
                    meta: {
                        title: 'DFM_GCCJ._GCCJ',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/spotCheck/index.vue')
                },
                {
                    path: '/qualityManagement/finalInspection',
                    name: '末检记录',
                    meta: {
                        title: 'DFM_MJJL._MJJL',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/finalInspection/index.vue')
                },
                {
                    path: '/qualityManagement/spotCheckRegistration',
                    name: '抽检登记',
                    meta: {
                        title: '抽检登记',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/spotCheckRegistration/index.vue')
                },
                {
                    path: '/qualityManagement/isolationProducts',
                    name: '产品隔离',
                    meta: {
                        title: 'DFM_CPGL._CPGL',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/isolationProducts/index.vue')
                },
                {
                    path: '/qualityManagement/isolationDispose',
                    name: '隔离处置',
                    meta: {
                        title: '隔离处置',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/isolationDispose/index.vue')
                },
                {
                    path: '/qualityManagement/productionTraceability',
                    name: '生产追溯',
                    meta: {
                        title: '生产追溯',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/productionTraceability/index.vue')
                },
                {
                    path: '/qualityManagement/yieldQuery',
                    name: '良率查询',
                    meta: {
                        title: '良率查询',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/yieldQuery/index.vue')
                },
                {
                    path: '/qualityManagement/primaryYieldAnalysis',
                    name: '一次良率分析',
                    meta: {
                        title: 'DFM_YCLLFX._YCLLFX',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/primaryYieldAnalysis/index.vue')
                },
                {
                    path: '/qualityManagement/performanceTest',
                    name: '性能测试',
                    meta: {
                        title: '性能测试',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/performanceTest/index.vue')
                },
                {
                    path: '/qualityManagement/appearanceInspection',
                    name: '外观检测处理',
                    meta: {
                        title: '外观检测处理',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/appearanceInspection/index.vue')
                },
                {
                    path: '/qualityManagement/anomalousEvent',
                    name: '异常事件通知',
                    meta: {
                        title: 'DFM_YCSJTZ._YCSJTZ',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/anomalousEvent/index.vue')
                },
                {
                    path: '/qualityManagement/isolationSignOff',
                    name: '隔离签核',
                    meta: {
                        title: '隔离签核',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/isolationSignOff/index.vue')
                },
                {
                    path: '/qualityManagement/productionLinekanban',
                    name: '产线质量监控看板',
                    meta: {
                        title: 'DFM_CXZLJKKB._CXZLJKKB',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/productionLinekanban/index.vue')
                },
                {
                    path: '/qualityManagement/workstationDetail',
                    name: '工站详细信息',
                    meta: {
                        title: '工站详细信息',
                        icon: 'mdi-alpha-a',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/qualityManagement/workstationDetail/index.vue')
                },
                {
                    path: '/qualityManagement/qualityControlKanban',
                    name: '质量管理看板',
                    meta: {
                        title: 'DFM_ZLGLKB._ZLGLKB',
                        icon: 'mdi-alpha-a',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/qualityManagement/qualityControlKanban/index.vue')
                },
                {
                    path: '/qualityManagement/pointConfiguration',
                    name: '质量监控点位配置',
                    meta: {
                        title: 'DFM_ZLJKDWPZ._ZLJKDWPZ',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/pointConfiguration/index.vue')
                },
                {
                    path: '/qualityManagement/SPCFlatPatternmaking',
                    name: 'SPC报表',
                    meta: {
                        title: 'DFM_SPCZST._SPCZST',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/SPCFlatPatternmaking/index.vue')
                },
                {
                    path: '/qualityManagement/vehicleSPCReport',
                    name: '车载SPC报表',
                    meta: {
                        title: 'DFM_CZSPCZST._CZSPCZST',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/qualityManagement/SPCFlatPatternmaking/vehicleSPCReport.vue')
                }
            ]
        },
        {
            path: '/planManagement',
            meta: {
                title: 'DFM_JHGL',
                icon: 'mdi-drag-variant'
            },
            name: '计划管理',
            component: RouteWrapper,
            redirect: '/planManagement/orderManagement',
            children: [{
                path: '/planManagement/PlannedWork',
                name: '制造计划工单管理',
                meta: {
                    title: 'PlanWork.title.PlannedWork',
                    icon: 'mdi-alpha-a'
                },
                component: () =>
                    import('@/views/planManagement/PlannedWork/index.vue')
            },
                {
                    path: '/planManagement/workOrderManagement',
                    name: '工单管理',
                    meta: {
                        title: 'ProductionWork.title.workOrderManagement',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/workOrderManagement/index.vue')
                },
                {
                    path: '/planManagement/productionWork',
                    name: '制造生产工单管理',
                    meta: {
                        title: 'ProductionWork.title.ProductionWork',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/productionWork/index.vue')
                },
                {
                    path: '/planManagement/LineRelation',
                    name: '产线关系维护',
                    meta: {
                        title: 'ProductionWork.title.LineRelation',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/LineRelation/index.vue')
                },
                {
                    path: '/planManagement/packagingWorkOrder',
                    name: '包装工单管理',
                    meta: {
                        title: 'PackagingWorkOrder.title.packagingWorkOrder',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/packagingWorkOrder/index.vue')
                },
                {
                    path: '/planManagement/orderManagement',
                    name: '订单管理',
                    meta: {
                        title: 'DFM_DDGL._DDGL',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/orderManagement/index.vue')
                },
                // {
                //     path: '/planManagement/productionWorkOrder',
                //     name: '生产工单',
                //     meta: {
                //         title: 'DFM_SCGD._SCGD',
                //         icon: 'mdi-alpha-a'
                //     },
                //     component: () => import('@/views/planManagement/productionWorkOrder/index.vue')
                // },
                {
                    path: '/planManagement/dayOrderDetail',
                    name: '日工单详情',
                    meta: {
                        title: 'DFM_RGDXQ._RGDXQ',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/dayOrderDetail/index.vue')
                },
                {
                    path: '/planManagement/orderSearch',
                    name: '工单查询',
                    meta: {
                        title: 'DFM_GDCX._GDCX',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/woOrder/index.vue')
                },
                {
                    path: '/planManagement/productionSchedule',
                    name: '生产工单甘特图',
                    meta: {
                        title: 'DFM_SCGDGTT._SCGDGTT',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/productionSchedule/index.vue')
                },
                {
                    path: '/planManagement/productionKanban',
                    name: '生产进度看板',
                    meta: {
                        title: 'DFM_SCJDKB._SCJDKB',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/productionKanban/index.vue')
                },
                {
                    path: '/planManagement/yearPlan',
                    name: '年计划',
                    meta: {
                        title: 'DFM_NJH._NJH',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/yearPlan/index.vue')
                },
                {
                    path: '/planManagement/monthPlan',
                    name: '月计划',
                    meta: {
                        title: 'DFM_YJH._YJH',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/monthPlan/index.vue')
                },
                {
                    path: '/planManagement/dayPlan',
                    name: '日计划',
                    meta: {
                        title: 'DFM_RJH._RJH',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/dayPlan/index.vue')
                },
                {
                    path: '/planManagement/BoilingTankOutput',
                    name: '配方煮缸排产量',
                    meta: {
                        title: 'BoilingTankOutput.title',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/BoilingTankOutput/index.vue')
                },
                {
                    path: '/planManagement/ProductionSequence',
                    name: '生产顺序',
                    meta: {
                        title: 'ProductionSequence.title',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/ProductionSequence/index.vue')
                },
                {
                    path: '/planManagement/Packspeed',
                    name: '包装生产速度',
                    meta: {
                        title: 'Packspeed.title',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/Packspeed/index.vue')
                },
                {
                    path: '/planManagement/FormulaTankCapacity',
                    name: '配方产线标准缸重',
                    meta: {
                        title: 'FormulaTankCapacity.title',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/FormulaTankCapacity/index.vue')
                },
                {
                    path: '/planManagement/RecipeSorting',
                    name: '配方排序',
                    meta: {
                        title: 'RecipeSorting.title',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/RecipeSorting/index.vue')
                },
                {
                    path: '/planManagement/handPackSort',
                    name: '无灌装包装工单排序',
                    meta: {
                        title: 'handPackSort.title',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/handPackSort/index.vue')
                },
                {
                    path: '/planManagement/OrderThroat',
                    name: '工单喉头信息',
                    meta: {
                        title: 'OrderThroat.title',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/planManagement/OrderThroat/index.vue')
                },
                {
                    path: '/productionManagement/OilFormula',
                    name: '加热油工单规则',
                    meta: {
                        title: 'OilFormula.title',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/productionManagement/OilFormula/index.vue')
                },
                {
                    path: '/productionManagement/MaterialSchedulingAttribute',
                    name: '物料排产属性',
                    meta: {
                        title: 'MaterialSchedulingAttribute.title',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/productionManagement/MaterialSchedulingAttribute/index.vue')
                },
                {
                    path: '/productionManagement/MaterialRequirementInquiry',
                    name: '物料需求查询',
                    meta: {
                        title: 'MaterialRequirementInquiry.title',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/productionManagement/MaterialRequirementInquiry/index.vue')
                },
            ]
        },
        // 系统管理
        {
            path: '/systemManagement',
            meta: {
                title: 'DFM_XTGL',
                icon: 'mdi-drag-variant'
            },
            name: '系统管理',
            component: RouteWrapper,
            redirect: '/systemManagement/dataDictionary',
            children: [{
                path: '/systemManagement/printing',
                name: 'Printing',
                meta: {
                    title: 'DFM_PRINT.title',
                    icon: 'mdi-alpha-b'
                },
                component: () =>
                    import('@/views/systemManagement/printing/index.vue')
            }, {
                path: '/systemManagement/dataDictionary',
                name: '数据字典',
                meta: {
                    title: 'DFM_SJZD._SJZD',
                    icon: 'mdi-alpha-a'
                },
                component: () =>
                    import('@/views/systemManagement/dataDictionary/index.vue')
            },
                {
                    path: '/systemManagement/demo',
                    name: 'demo',
                    meta: {
                        title: 'demo',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/systemManagement/demo/index.vue')
                },
                {
                    path: '/systemManagement/userManagement',
                    name: '用户管理',
                    meta: {
                        title: 'DFM_YHGL._YHGL',
                        icon: 'mdi-alpha-b'
                    },
                    component: () =>
                        import('@/views/systemManagement/userManagement/index.vue')
                },
                {
                    path: '/systemManagement/menusManagement',
                    name: '菜单管理',
                    meta: {
                        title: 'DFM_CDGL._CDGL',
                        icon: 'mdi-alpha-c'
                    },
                    component: () =>
                        import('@/views/systemManagement/menusManagement/index.vue')
                },
                {
                    path: '/systemManagement/roleManagement',
                    name: '角色管理',
                    meta: {
                        title: 'DFM_JSGL._JSGL',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/systemManagement/roleManagement/index.vue')
                },
                {
                    path: '/systemManagement/taskBackGround',
                    name: '后台任务',
                    meta: {
                        title: 'DFM_HTRW._HTRW',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/systemManagement/taskBackGround/index.vue')
                },
                {
                    path: '/systemManagement/printTemplate',
                    name: '模板管理',
                    meta: {
                        title: 'DFM_MBGL._MBGL',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/systemManagement/printTemplate/index.vue')
                },
                {
                    path: '/systemManagement/printTemplateCreate',
                    name: 'printTemplateCreate',
                    meta: {
                        title: '模板设计',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/print/custom/index.vue')
                },
                {
                    path: '/systemManagement/dataImport',
                    name: 'dataImport',
                    meta: {
                        title: 'DFM_SJDR._SJDR',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/systemManagement/dataImport/index.vue')
                },
                {
                    path: '/systemManagement/helpGuide',
                    name: 'helpGuide',
                    meta: {
                        title: 'DFM_SJDR._SJDR',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/systemManagement/helpGuide/index.vue')
                },
                {
                    path: '/systemManagement/helpSetup',
                    name: 'helpSetup',
                    meta: {
                        title: 'DFM_BZPZ._BZPZ',
                        icon: 'mdi-alpha-a',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/systemManagement/helpGuide/helpSetup.vue')
                },
                {
                    path: '/factoryPlant/dataAPI',
                    name: '数据API',
                    meta: {
                        title: 'DFM_DATAAPI._DATAAPI',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/factoryPlant/dataAPI/index.vue')
                },
                {
                    path: '/systemManagement/barCodeManagement',
                    name: '条码管理',
                    meta: {
                        title: 'DFM_TMGL._TMGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/systemManagement/barCodeManagement/index.vue')
                },
                {
                    path: '/systemManagement/useAid',
                    name: '使用辅助',
                    meta: {
                        title: 'DFM_SYFZ._SYFZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/systemManagement/useAid/index.vue')
                },
                {
                    path: '/systemManagement/operationLog',
                    name: '操作日志',
                    meta: {
                        title: 'DFM_CZRZ._CZRZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/systemManagement/operationLog/index.vue')
                },
                {
                    path: '/systemManagement/printing/country',
                    name: 'country',
                    meta: {
                        title: 'DFM_PRINT.country',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/systemManagement/printing/country/index.vue')
                },
                {
                    path: '/systemManagement/printing/labelFormat',
                    name: 'labelFormat',
                    meta: {
                        title: 'DFM_PRINT.labelFormat',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/systemManagement/printing/labelFormat/index.vue')
                },
                {
                    path: '/systemManagement/printing/labelSize',
                    name: 'labelSize',
                    meta: {
                        title: 'DFM_PRINT.labelSize',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/systemManagement/printing/labelSize/index.vue')
                },
                {
                    path: '/systemManagement/printing/labelTemplate',
                    name: 'labelTemplate',
                    meta: {
                        title: 'DFM_PRINT.labelTemplate',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/systemManagement/printing/labelTemplate/index.vue')
                },
                {
                    path: '/systemManagement/printing/printer',
                    name: 'printer',
                    meta: {
                        title: 'DFM_PRINT.printer',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/systemManagement/printing/printer/index.vue')
                },
                {
                    path: '/systemManagement/printing/printerMapping',
                    name: 'printerMapping',
                    meta: {
                        title: 'DFM_PRINT.printerMapping',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/systemManagement/printing/printerMapping/index.vue')
                },
                {
                    path: '/systemManagement/printing/SAPLabelTemplate',
                    name: 'SAPLabelTemplate',
                    meta: {
                        title: 'DFM_PRINT.SAPLabelTemplate',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/systemManagement/printing/SAPLabelTemplate/index.vue')
                },
                {
                    path: '/systemManagement/printing/printHistory',
                    name: 'printHistory',
                    meta: {
                        title: 'DFM_PRINT.printHistory',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/systemManagement/printing/printHistory/index.vue')
                },
            ]
        },
        // 工厂建模
        {
            path: '/factoryPlant ',
            component: RouteWrapper,
            meta: {
                title: 'DFM_GCJM',
                icon: 'mdi-drag-variant'
            },
            name: '工厂建模',
            children: [{
                path: '/factoryPlant/materialManagement',
                name: '物料管理',
                meta: {
                    title: 'DFM_WLGL.WLGL',
                    icon: 'mdi-alpha-a'
                },
                component: () =>
                    import('@/views/factoryPlant/materialManagement/index.vue')
            },
                {
                    path: '/factoryPlant/ConsoleGroup',
                    name: '控制台分组',
                    meta: {
                        title: 'ConsoleGroup.title',
                        icon: 'mdi-alpha-c'
                    },
                    component: () =>
                        import('@/views/factoryPlant/ConsoleGroup/index.vue')
                },
                {
                    path: '/factoryPlant/materialManagementNew',
                    name: '物料管理(新)',
                    meta: {
                        title: 'DFM_WLGL.WLGL',
                        icon: 'mdi-alpha-a'
                    },
                    component: () =>
                        import('@/views/factoryPlant/materialManagementNew/index.vue')
                },
                {
                    path: '/factoryPlant/materialBOM',
                    name: '物料BOM管理',
                    meta: {
                        title: 'DFM_WLBOMGL.WLBOMGL',
                        icon: 'mdi-alpha-b'
                    },
                    component: () =>
                        import('@/views/factoryPlant/materialBOM/index.vue')
                },
                {
                    path: '/factoryPlant/supplier',
                    name: '供应商管理',
                    meta: {
                        title: 'DFM_GYSGL._GYSGL',
                        icon: 'mdi-alpha-c'
                    },
                    component: () =>
                        import('@/views/factoryPlant/supplier/index.vue')
                },
                {
                    path: '/factoryPlant/workforceManagement',
                    name: '楼层管理',
                    meta: {
                        title: 'DFM_LCGL._LCGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/factoryPlant/workforceManagement/index.vue')
                },
                {
                    path: '/factoryPlant/physicalModel',
                    name: '物理模型',
                    meta: {
                        title: 'DFM_WLMX._WLMX',
                        icon: 'mdi-alpha-d'
                    },
                    component: () =>
                        import('@/views/factoryPlant/physicalModel/index.vue')
                },
                {
                    path: '/factoryPlant/physicalModelNew',
                    name: '物理模型(新)',
                    meta: {
                        title: 'DFM_XWLMX._XWLMX',
                        icon: 'mdi-alpha-d'
                    },
                    component: () =>
                        import('@/views/factoryPlant/physicalModelNew/index.vue')
                },
                {
                    path: '/factoryPlant/Opc',
                    name: 'OPC',
                    meta: {
                        title: 'DFM_OPC._OPC',
                        icon: 'mdi-alpha-d'
                    },
                    component: () =>
                        import('@/views/factoryPlant/Opc/index.vue')
                },
                {
                    path: '/factoryPlant/tagData',
                    name: 'tagData',
                    meta: {
                        title: 'DFM_TAGDATA._TAGDATA',
                        icon: 'mdi-alpha-d'
                    },
                    component: () =>
                        import('@/views/factoryPlant/tagData/index.vue')
                },
                {
                    path: '/factoryPlant/unitConversion',
                    name: 'unitConversion',
                    meta: {
                        title: 'DFM_DWZH._DWZH',
                        icon: 'mdi-alpha-d'
                    },
                    component: () =>
                        import('@/views/factoryPlant/unitConversion/index.vue')
                },
                {
                    path: '/factoryPlant/suiteConfiguration',
                    name: '套件配置',
                    meta: {
                        title: 'DFM_TJPZ._TJPZ',
                        icon: 'mdi-alpha-e'
                    },
                    component: () =>
                        import('@/views/factoryPlant/suiteConfiguration/index.vue')
                },
                {
                    path: '/factoryPlant/departmentManagement',
                    name: '组织建模',
                    meta: {
                        title: 'DFM_ZZJM._ZZJM',
                        icon: 'mdi-alpha-f'
                    },
                    component: () =>
                        import('@/views/factoryPlant/departmentManagement/index.vue')
                },
                {
                    path: '/factoryPlant/postManagement',
                    name: '岗位管理',
                    meta: {
                        title: 'DFM_GWGL',
                        icon: 'mdi-alpha-f'
                    },
                    component: () =>
                        import('@/views/factoryPlant/postManagement/index.vue')
                },
                {
                    path: '/factoryPlant/processWorkshopsection',
                    name: '工序工段',
                    meta: {
                        title: 'DFM_GXGD',
                        icon: 'mdi-alpha-f'
                    },
                    component: () =>
                        import('@/views/factoryPlant/processWorkshopsection/index.vue')
                },
                {
                    path: '/factoryPlant/processRoute',
                    name: '工艺路线(新)',
                    meta: {
                        title: 'DFM_GYLX._NEW',
                        icon: 'mdi-alpha-g'
                    },
                    component: () =>
                        import('@/views/factoryPlant/processRoute/index.vue')
                },
                {
                    path: '/factoryPlant/operationalpath',
                    name: '工艺路线维护',
                    meta: {
                        title: 'DFM_GYLX._GYLX',
                        icon: 'mdi-alpha-h'
                    },
                    component: () =>
                        import('@/views/factoryPlant/operationalPath/index.vue')
                },

                {
                    path: '/factoryPlant/reasonTree',
                    name: '原因树管理',
                    meta: {
                        title: 'DFM_YYSGL._YYSGL',
                        icon: 'mdi-alpha-i'
                    },
                    component: () =>
                        import('@/views/factoryPlant/reasonTree/index.vue')
                },
                {
                    path: '/factoryPlant/reasonInfo',
                    name: '原因信息管理',
                    meta: {
                        title: 'DFM_YYXXGL._YYXXGL',
                        icon: 'mdi-alpha-j'
                    },
                    component: () =>
                        import('@/views/factoryPlant/reasonInfo/index.vue')
                },
                {
                    path: '/factoryPlant/reasonDetail',
                    name: '原因明细管理',
                    meta: {
                        title: 'DFM_YYMXGL._YYMXGL',
                        icon: 'mdi-alpha-k'
                    },
                    component: () =>
                        import('@/views/factoryPlant/reasonDetail/index.vue')
                },
                {
                    path: '/factoryPlant/processResourceManagement',
                    name: '物料工艺建模',
                    meta: {
                        title: 'DFM_WLGYJM._WLGYJM',
                        icon: 'mdi-alpha-l'
                    },
                    component: () =>
                        import('@/views/factoryPlant/processResourceManagement/index.vue')
                },
                {
                    path: '/factoryPlant/processResources',
                    name: '工序资源',
                    meta: {
                        title: 'DFM_GXZY._GXZY',
                        icon: 'mdi-alpha-l'
                    },
                    component: () =>
                        import('@/views/factoryPlant/processResources/index.vue')
                },
                {
                    path: '/factoryPlant/basicProcessInformation',
                    name: '工序基础信息',
                    meta: {
                        title: 'DFM_GYGL._GYJCXX',
                        icon: 'mdi-alpha-m'
                    },
                    component: () =>
                        import('@/views/factoryPlant/basicProcessInformation/index.vue')
                },
                {
                    path: '/factoryPlant/productionlineProcessRoute',
                    name: '产线工艺路线',
                    meta: {
                        title: 'DFM_CXGYLX._CXGYLX',
                        icon: 'mdi-alpha-m'
                    },
                    component: () =>
                        import('@/views/factoryPlant/ProductionlineProcessRoute/index.vue')
                },
                {
                    path: '/factoryPlant/processDocumentation',
                    name: '工艺文件管理',
                    meta: {
                        title: 'DFM_GYWJGL._GYWJGL',
                        icon: 'mdi-alpha-m'
                    },
                    component: () =>
                        import('@/views/factoryPlant/processDocumentation/index.vue')
                },

                {
                    path: '/factoryPlant/deviceType',
                    name: '设备类型',
                    meta: {
                        title: 'DFM_SBLX._SBLX',
                        icon: 'mdi-alpha-n'
                    },
                    component: () =>
                        import('@/views/factoryPlant/deviceType/index.vue')
                },
                // {
                //     path: '/factoryPlant/processParameter',
                //     name: '机加工程序台账',
                //     meta: {
                //         title: '机加工程序台账',
                //         icon: 'mdi-alpha-c'
                //     },
                //     component: () => import('@/views/factoryPlant/processParameter/index.vue')
                // },
                {
                    path: '/factoryPlant/calendar',
                    name: '日历',
                    meta: {
                        title: 'DFM_RL._RL',
                        icon: 'mdi-alpha-o'
                    },
                    component: () =>
                        import('@/views/factoryPlant/calendar/index.vue')
                },
                {
                    path: '/factoryPlant/workCalendar',
                    name: '工作日历',
                    meta: {
                        title: 'DFM_GZRL._GZRL',
                        icon: 'mdi-alpha-o'
                    },
                    component: () =>
                        import('@/views/factoryPlant/workCalendar/index.vue')
                },
                {
                    path: '/factoryPlant/lineSideShelfPosition',
                    name: '线边货架位',
                    meta: {
                        title: 'DFM_XBHJW.XBHJW',
                        icon: 'mdi-alpha-p'
                    },
                    component: () =>
                        import('@/views/factoryPlant/lineSideShelfPosition/index.vue')
                },
                {
                    path: '/factoryPlant/lineSideShelfPositionMaterial',
                    name: '线边货架位物料',
                    meta: {
                        title: 'DFM_XBHJWL.XBHJWL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/factoryPlant/lineSideShelfPositionMaterial/index.vue')
                },
                {
                    path: '/factoryPlant/unitManagement',
                    name: '单位管理',
                    meta: {
                        title: 'DFM_DWGL._DWGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/factoryPlant/unitManagement/index.vue')
                },
                {
                    path: '/factoryPlant/physicalResource',
                    name: '工序物理资源',
                    meta: {
                        title: 'DFM_GXWLZY._GXWLZY',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/factoryPlant/physicalResource/index.vue')
                },
                {
                    path: '/factoryPlant/materialRelationshipMapping',
                    name: '物料关系映射',
                    meta: {
                        title: 'DFM_WLGXYS._WLGXYS',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/factoryPlant/materialRelationshipMapping/index.vue')
                },
                {
                    path: '/factoryPlant/productionLineIndexValue',
                    name: '产线KPI目标设定',
                    meta: {
                        title: 'DFM_CXKPIMBSD._CXKPIMBSD',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/factoryPlant/productionLineIndexValue/index.vue')
                },
                {
                    path: '/factoryPlant/KPIGoaSetting',
                    name: '试产KPI目标设定',
                    meta: {
                        title: 'DFM_SCKPIMBSD._SCKPIMBSD',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/factoryPlant/KPIGoaSetting/index.vue')
                },
                {
                    path: '/factoryPlant/substituteMaterial',
                    name: '替代料管理',
                    meta: {
                        title: 'DFM_TDLGL._TDLGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/factoryPlant/substituteMaterial/index.vue')
                },
                {
                    path: '/factoryPlant/reportBaseModel',
                    name: '报表基础模型配置',
                    meta: {
                        title: 'DFM_BBJCMX._BBJCMX',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/factoryPlant/reportBaseModel/index.vue')
                },
                {
                    path: '/factoryPlant/collectionPointConfiguration',
                    name: '工段收盘站采集点配置',
                    meta: {
                        title: 'DFM_GDSPZCJDPZ._GDSPZCJDPZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/factoryPlant/collectionPointConfiguration/index.vue')
                },
                {
                    path: '/factoryPlant/loadingPointConfiguration',
                    name: '上料点配置',
                    meta: {
                        title: 'DFM_SLDPZ._SLDPZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/factoryPlant/loadingPointConfiguration/index.vue')
                },
                {
                    path: '/factoryPlant/communicationStation',
                    name: '通讯站点管理',
                    meta: {
                        title: 'CommunicationStation.title.communicationStation',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/factoryPlant/communicationStation/index.vue')
                },
                {
                    path: '/factoryPlant/collectionPoint',
                    name: '采集点管理',
                    meta: {
                        title: 'CollectionPoint.title.collectionPoint',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/factoryPlant/collectionPoint/index.vue')
                }
            ]
        },
        {
            path: '/kpi',
            meta: {
                title: 'DFM_KPIPZ',
                icon: 'mdi-drag-variant'
            },
            name: 'KPI配置',
            component: RouteWrapper,
            redirect: '/kpi/tag',
            children: [{
                path: '/kpi/tag',
                name: '标签配置',
                meta: {
                    title: 'DFM_BQPZ._BQPZ',
                    icon: 'mdi-alpha-q'
                },
                component: () =>
                    import('@/views/kpi/tag/index.vue')
            },
                {
                    path: '/kpi/modelManagement',
                    name: '模型管理',
                    meta: {
                        title: 'DFM_MXGL._MXGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/kpi/modelManagement/index.vue')
                },
                {
                    path: '/kpi/modelEntering',
                    name: '模型录入',
                    meta: {
                        title: 'DFM_MXLR._MXLR',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/kpi/modelEntering/index.vue')
                }
            ]
        },
        // 生产管理
        {
            path: '/productionManagement',
            meta: {
                title: '生产管理',
                icon: 'mdi-drag-variant'
            },
            name: '生产管理',
            component: RouteWrapper,
            redirect: '/productionManagement/FeedingManagement',
            children: [{
                path: '/productionManagement/FormulaScheduling',
                name: '配方排产',
                meta: {
                    title: 'Formula.title.Production_scheduling',
                    icon: 'mdi-alpha-q'
                },
                component: () =>
                    import('@/views/productionManagement/FormulaScheduling/index.vue')
            },
                {
                    path: '/productionManagement/workSearch',
                    name: '工单查询',
                    meta: {
                        title: 'Formula.title.workSearch',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/workSearch/index.vue')
                },
                {
                    path: '/productionManagement/cylinderWeightMaintenance',
                    name: '特殊配方对应缸重维护',
                    meta: {
                        title: 'Formula.title.CylinderWeightMaintenance',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/cylinderWeightMaintenance/index.vue')
                },
                {
                    path: '/productionManagement/CIPCeaning',
                    name: 'CIP清洗时间维护',
                    meta: {
                        title: 'Formula.title.CIPCeaning',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/CIPCeaning/index.vue')
                },
                {
                    path: '/productionManagement/CipSwitch',
                    name: 'CIP切换方式',
                    meta: {
                        title: 'Formula.title.CipSwitch',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/CipSwitch/index.vue')
                },
                {
                    path: '/productionManagement/CookTimeModel',
                    name: '煮制时间维护',
                    meta: {
                        title: 'CookTimeModel.title',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/CookTimeModel/index.vue')
                },
                {
                    path: '/productionManagement/BoilingTankCapacity',
                    name: '产线的煮缸容量',
                    meta: {
                        title: 'Formula.title.BoilingTankCapacity',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/BoilingTankCapacity/index.vue')
                },
                {
                    path: '/productionManagement/RecommendedFormulaCylinderCapacity',
                    name: '产线配方以及缸容量推荐',
                    meta: {
                        title: 'Formula.title.RecommendedFormulaCylinderCapacity',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/RecommendedFormulaCylinderCapacity/index.vue')
                },
                {
                    path: '/productionManagement/Throataddition',
                    name: '喉头添加基础表',
                    meta: {
                        title: 'Formula.title.Throataddition',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/Throataddition/index.vue')
                },
                {
                    path: '/productionManagement/CookingLoss',
                    name: '标准损耗维护',
                    meta: {
                        title: 'Formula.title.CookingLoss',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/CookingLoss/index.vue')
                },
                {
                    path: '/productionManagement/ChangeModel',
                    name: '换型时间维护',
                    meta: {
                        title: 'Formula.title.ChangeModel',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/ChangeModel/index.vue')
                },
                {
                    path: '/productionManagement/ProductSpeed',
                    name: '生产速度维护',
                    meta: {
                        title: 'Formula.title.ProductSpeed',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/ProductSpeed/index.vue')
                }
                ,
                {
                    path: '/productionManagement/FormulaLoss',
                    name: '配方损耗计划表',
                    meta: {
                        title: 'Formula.title.FormulaLoss',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/FormulaLoss/index.vue')
                },
                {
                    path: '/productionManagement/BOMInjection',
                    name: 'BOMInjection',
                    meta: {
                        title: 'Formula.title.BOMInjection',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/BOMInjection/index.vue')
                },
                {
                    path: '/productionManagement/ResourceDefinition',
                    name: '工作中心配置',
                    meta: {
                        title: 'Formula.title.ResourceDefinition',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/ResourceDefinition/index.vue')
                },
                {
                    path: '/productionManagement/SectionProcessDefinition',
                    name: '工段工序定义',
                    meta: {
                        title: 'Formula.title.SectionProcessDefinition',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/SectionProcessDefinition/index.vue')
                },
                {
                    path: '/productionManagement/ProcessAndEquipmentAssociation',
                    name: '工序与设备关联',
                    meta: {
                        title: 'Formula.title.ProcessAndEquipmentAssociation',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/ProcessAndEquipmentAssociation/index.vue')
                },
                {
                    path: '/productionManagement/ProductBOMDefinition',
                    name: '产品BOM定义',
                    meta: {
                        title: 'Formula.title.ProductBOMDefinition',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/productionManagement/ProductBOMDefinition/index.vue')
                },
                {
                    path: '/productionManagement/ProductBOMMaterialDetails',
                    name: '产品BOM物料明细',
                    meta: {
                        title: 'Formula.title.ProductBOMMaterialDetails',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/ProductBOMMaterialDetails/index.vue')
                },
                {
                    path: '/productionManagement/WorkOrderManagement',
                    name: '工单管理',
                    meta: {
                        title: 'TRACE_GDGL._GDGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/WorkOrderManagement/index.vue')
                },
                {
                    path: '/productionManagement/TostartChecking',
                    name: '开工检查',
                    meta: {
                        title: 'TRACE_KGJC._KGJC',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/TostartChecking/index.vue')
                },
                {
                    path: '/productionManagement/defaultBatchSize',
                    name: '产线配置',
                    meta: {
                        title: 'TRACE_CXPZ._CXPZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/defaultBatchSize/index.vue')
                },
                {
                    path: '/productionManagement/FeedingManagement',
                    name: '上料管理',
                    meta: {
                        title: 'TRACE_SlGL._SlGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/FeedingManagement/index.vue')
                },
                {
                    path: '/productionManagement/GlueLoadingUnloadingManagement',
                    name: '胶水上下料管理',
                    meta: {
                        title: 'TRACE_JSSXL._JSSXL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/GlueLoadingUnloadingManagement/index.vue')
                },
                {
                    path: '/productionManagement/combinebatch',
                    name: '批次合并',
                    meta: {
                        title: 'TRACE_PCHB._PCHB',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/combinebatch/index.vue')
                },
                {
                    path: '/productionManagement/plasmaWork',
                    name: '等离子作业',
                    meta: {
                        title: 'TRACE_DLZZY._DLZZY',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/plasmaWork/index.vue')
                },
                {
                    path: '/productionManagement/puretoneTest',
                    name: '纯音测试',
                    meta: {
                        title: 'TRACE_CYCS._CYCS',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/puretoneTest/index.vue')
                },
                {
                    path: '/productionManagement/CZPureTest',
                    name: '装配过站',
                    meta: {
                        title: 'TRACE_ZPGZ._ZPGZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/CZPureTest/index.vue')
                },
                {
                    path: '/productionManagement/carPureTone',
                    name: '车载纯音',
                    meta: {
                        title: 'TRACE_ZPGZ._CZCY',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/carPureTone/index.vue')
                },
                {
                    path: '/productionManagement/vehicleAppearance',
                    name: '车载外观',
                    meta: {
                        title: 'TRACE_ZPGZ._CZWG',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/vehicleAppearance/index.vue')
                },
                {
                    path: '/productionManagement/productTraceability',
                    name: '产品过站追溯',
                    meta: {
                        title: 'TRACE_ZPGZ._CPGZZS',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/productTraceability/index.vue')
                },
                {
                    path: '/productionManagement/visualInspection',
                    name: '外观检测',
                    meta: {
                        title: 'TRACE_WGJC._WGJC',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/visualInspection/index.vue')
                },
                {
                    path: '/productionManagement/ovenOperation',
                    name: '烘箱作业',
                    meta: {
                        title: 'TRACE_HXZY._HXZY',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/ovenOperation/index.vue')
                },
                {
                    path: '/productionManagement/magnetizingOperation',
                    name: '充磁作业',
                    meta: {
                        title: 'TRACE_CCZY._CCZY',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/magnetizingOperation/index.vue')
                },
                {
                    path: '/productionManagement/OQC',
                    name: 'OQC',
                    meta: {
                        title: 'OQC',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/OQC/index.vue')
                },
                {
                    path: '/productionManagement/OQCNew',
                    name: 'OQCNew',
                    meta: {
                        title: 'TRACE_OQC._OQC',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/OQCNew/index.vue')
                },
                {
                    path: '/productionManagement/packaging',
                    name: '包装',
                    meta: {
                        title: 'TRACE_BZ._BZ',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/productionManagement/packaging/index.vue')
                },
                {
                    path: '/productionManagement/packingStorage',
                    name: '包装入库',
                    meta: {
                        title: 'TRACE_BZRK._BZRK',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/productionManagement/packingStorage/index.vue')
                },
                {
                    path: '/productionManagement/vehiclePackaging',
                    name: '车载包装',
                    meta: {
                        title: 'TRACE_CZBZ._CZBZ',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/productionManagement/packaging/index.vue')
                },
                {
                    path: '/productionManagement/performancetesting',
                    name: '性能测试',
                    meta: {
                        title: 'TRACE_XNCS._XNCS',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/performancetesting/index.vue')
                },
                {
                    path: '/productionManagement/materialTraceability',
                    name: '物料追溯',
                    meta: {
                        title: 'TRACE_WLZS._WLZS',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/materialTraceability/index.vue')
                },
                {
                    path: '/productionManagement/productionTraceability',
                    name: '生产追溯',
                    meta: {
                        title: 'TRACE_SCZS._SCZS',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/productionTraceability/index.vue')
                },
                {
                    path: '/productionManagement/ABFileSNQuery',
                    name: 'AB档SN查询',
                    meta: {
                        title: 'TRACE_ABDSNCX._ABDSNCX',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/ABFileSNQuery/index.vue')
                },
                {
                    path: '/productionManagement/RFIDandCCDdataQuery',
                    name: '工单SN查询',
                    meta: {
                        title: 'TRACE_GDSNCX._GDSNCX',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/RFIDandCCDdataQuery/index.vue')
                },
                {
                    path: '/productionManagement/processMapping',
                    name: '工序映射',
                    meta: {
                        title: 'TRACE_GXYS._GXYS',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/processMapping/index.vue')
                },
                {
                    path: '/productionManagement/workReport',
                    name: '工单报工',
                    meta: {
                        title: 'TRACE_GDBG._GDBG',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/workReport/index.vue')
                },
                {
                    path: '/productionManagement/VirtualEntry',
                    name: '虚拟入库',
                    meta: {
                        title: 'TRACE_XNRK._XNRK',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/VirtualEntry/index.vue')
                },
                {
                    path: '/productionManagement/singleVehicleBinding',
                    name: '载具单体绑定',
                    meta: {
                        title: 'TRACE_ZJDTBD._ZJDTBD',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/productionManagement/singleVehicleBinding/index.vue')
                },
                {
                    path: '/productionManagement/vehicleProductBinding',
                    name: '载具产品绑定',
                    meta: {
                        title: 'TRACE_ZJCPBD._ZJCPBD',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/productionManagement/vehicleProductBinding/index.vue')
                },
                {
                    path: '/productionManagement/twoInOneProductBinding',
                    name: '二合一产品绑定',
                    meta: {
                        title: 'TRACE_EHYCPBD._EHYCPBD',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/productionManagement/vehicleProductBinding/twoInOne.vue')
                },
                {
                    path: '/productionManagement/vehicleList',
                    name: 'Box产品码绑定记录',
                    meta: {
                        title: 'TRACE_ZJLB._ZJLB',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/vehicleList/index.vue')
                },
                {
                    path: '/productionManagement/SNResolutionConfiguration',
                    name: 'SN解析配置',
                    meta: {
                        title: 'TRACE_SNJXPZ._SNJXPZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/SNResolutionConfiguration/index.vue')
                },
                {
                    path: '/productionManagement/vehicleTraceability',
                    name: '车载生产追溯',
                    meta: {
                        title: 'TRACE_CZSCZS._CZSCZS',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/productionManagement/vehicleTraceability/index.vue')
                },
                {
                    path: '/productionManagement/machinePersonnelManagement',
                    name: '机台人员管理',
                    meta: {
                        title: 'TRACE_JTRYGL._JTRYGL',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/productionManagement/machinePersonnelManagement/index.vue')
                },
                {
                    path: '/productionManagement/SFCProductMapping',
                    name: 'SFC产品映射',
                    meta: {
                        title: 'TRACE_SFCCPYS._SFCCPYS',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/productionManagement/SFCProductMapping/index.vue')
                },
                {
                    path: '/productionManagement/SNPackingQuery',
                    name: 'SN装箱信息查询',
                    meta: {
                        title: 'TRACE_SNZXCX._SNZXCX',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/productionManagement/SNPackingQuery/index.vue')
                },
                {
                    path: '/productionManagement/productMonomerBinding',
                    name: '产品单体绑定',
                    meta: {
                        title: 'TRACE_CPDTBD._CPDTBD',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/productionManagement/productMonomerBinding/index.vue')
                },
                {
                    path: '/productionManagement/printTemplateBinding',
                    name: '打印模板绑定',
                    meta: {
                        title: 'TRACE_DYMBBD._DYMBBD',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/productionManagement/printTemplateBinding/index.vue')
                },
                {
                    path: '/productionManagement/productionDataQuery',
                    name: '1525生产数据查询',
                    meta: {
                        title: 'TRACE_SCSJBD._SCSJBD',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/productionManagement/productionDataQuery/index.vue')
                },
                {
                    path: '/productionManagement/eventConfiguration',
                    name: '事件配置',
                    meta: {
                        title: 'TRACE_SCSJBD._SCSJBD',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/productionManagement/eventConfiguration/index.vue')
                },
                {
                    path: '/productionManagement/workHourReporting',
                    name: '工时报工',
                    meta: {
                        title: 'TRACE_SCSJBD._SCSJBD',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/productionManagement/workHourReporting/index.vue')
                },
                {
                    path: '/productionManagement/auxiliaryWorkingHours',
                    name: '辅助工时',
                    meta: {
                        title: 'TRACE_SCSJBD._SCSJBD',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/productionManagement/auxiliaryWorkingHours/index.vue')
                },
                {
                    path: '/productionManagement/EnteringLightweight',
                    name: '入轻重量',
                    meta: {
                        title: 'EnteringLightweight.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/productionManagement/EnteringLightweight/index.vue')
                }
            ]
        },
        // 人员基础数据
        {
            path: '/personnelData',
            meta: {
                title: '人员数据',
                icon: 'mdi-drag-variant'
            },
            name: '人员数据',
            component: RouteWrapper,
            redirect: '/personnelData/basicdata',
            children: [{
                path: '/personnelData/basicdata',
                name: '人员主数据',
                meta: {
                    title: 'SHIFT_RYSJ_EYZSJ._RYZSJ',
                    icon: 'mdi-alpha-q'
                },
                component: () =>
                    import('@/views/personnelData/basicdata/index.vue')
            },
                {
                    path: '/personnelData/attendanceManagement',
                    name: '人员出勤',
                    meta: {
                        title: 'SHIFT_RYSJ_RYCQ._RYCQ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/personnelData/attendanceManagement/index.vue')
                },
                {
                    path: '/personnelData/personnelPiecework',
                    name: '人员计件',
                    meta: {
                        title: 'SHIFT_RYSJ_RYJJ._RYJJ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/personnelData/personnelPiecework/index.vue')
                },
                {
                    path: '/personnelData/personnelScheduling',
                    name: '人员排班',
                    meta: {
                        title: 'SHIFT_RYSJ_RYPB._RYCQ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/personnelData/personnelScheduling/index.vue')
                },
                {
                    path: '/personnelData/pieceBasis',
                    name: '计件标准',
                    meta: {
                        title: 'SHIFT_RYSJ_JJBZ._JJBZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/personnelData/pieceBasis/index.vue')
                },
                {
                    path: '/personnelData/personWorkMap',
                    name: '人员产线配置',
                    meta: {
                        title: 'SHIFT_RYSJ_RYCXPZ._RYCXPZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/personnelData/personWorkMap/index.vue')
                }
            ]
        },
        // 人员管理
        {
            path: '/peopleManagement',
            meta: {
                title: '人员管理',
                icon: 'mdi-drag-variant'
            },
            name: '人员管理',
            component: RouteWrapper,
            redirect: '/peopleManagement/basicdata',
            children: [{
                path: '/peopleManagement/basicdata',
                name: '基础数据',
                meta: {
                    title: '基础数据',
                    icon: 'mdi-alpha-q'
                },
                component: () =>
                    import('@/views/peopleManagement/basicdata/index.vue')
            },

                {
                    path: '/peopleManagement/personnelScheduling',
                    name: '人员建模',
                    meta: {
                        title: '人员建模',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/peopleManagement/personnelScheduling/index.vue')
                },
                {
                    path: '/peopleManagement/personnelSchedulingreplace',
                    name: '人员排班调整',
                    meta: {
                        title: '人员排班调整',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/peopleManagement/personnelSchedulingreplace/index.vue')
                },
                {
                    path: '/peopleManagement/schedulingResults',
                    name: '排班结果',
                    meta: {
                        title: '排班结果',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/peopleManagement/schedulingResults/index.vue')
                },
                {
                    path: '/peopleManagement/employeeHours',
                    name: '员工工时',
                    meta: {
                        title: '员工工时',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/peopleManagement/employeeHours/index.vue')
                },
                {
                    path: '/peopleManagement/pieceworkWage',
                    name: '计件薪资',
                    meta: {
                        title: '计件薪资',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/peopleManagement/pieceworkWage/index.vue')
                },
                {
                    path: '/peopleManagement/UWBpersonnelHours',
                    name: 'UWB人员工时',
                    meta: {
                        title: 'UWB人员工时',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/peopleManagement/UWBpersonnelHours/index.vue')
                },
                {
                    path: '/peopleManagement/UWBpersonWorkMap',
                    name: 'UWB工序工段映射',
                    meta: {
                        title: 'UWB工序工段映射',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/peopleManagement/UWBpersonWorkMap/index.vue')
                },
                {
                    path: '/peopleManagement/conferenceManagement',
                    name: '会议管理',
                    meta: {
                        title: '会议管理',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/peopleManagement/conferenceManagement/index.vue')
                },
                {
                    path: '/peopleManagement/mertingDetails',
                    name: '会议管理',
                    meta: {
                        title: '会议详情',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/peopleManagement/conferenceManagement/mertingDetails.vue')
                },

                {
                    path: '/peopleManagement/trainingManagement',
                    name: '培训管理',
                    meta: {
                        title: '培训管理',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/peopleManagement/trainingManagement/index.vue')
                },
                {
                    path: '/peopleManagement/repastManagement',
                    name: '就餐查看',
                    meta: {
                        title: '就餐查看',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/peopleManagement/repastManagement/index.vue')
                },
                {
                    path: '/peopleManagement/onguardManagemet',
                    name: '在岗统计',
                    meta: {
                        title: '在岗统计',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/peopleManagement/onguardManagemet/index.vue')
                },
                {
                    path: '/peopleManagement/attendanceManagement',
                    name: '考勤管理',
                    meta: {
                        title: '考勤管理',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/peopleManagement/attendanceManagement/index.vue')
                },
                {
                    path: '/peopleManagement/eventManagement',
                    name: '事故管理',
                    meta: {
                        title: '事故管理',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/peopleManagement/eventManagement/index.vue')
                },
                {
                    path: '/peopleManagement/payScale',
                    name: '日工资标准',
                    meta: {
                        title: '日工资标准',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/peopleManagement/payScale/index.vue')
                },
                {
                    path: '/peopleManagement/pieceBasis',
                    name: '计件标准',
                    meta: {
                        title: '计件标准',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/peopleManagement/pieceBasis/index.vue')
                }
            ]
        },
        // 物料管理
        {
            path: '/materialManagement',
            meta: {
                title: 'MATERIAL_WLGL',
                icon: 'mdi-drag-variant'
            },
            name: '物料管理',
            component: RouteWrapper,
            redirect: '/materialManagement/warehouseMaterialRules',
            children: [{
                path: '/materialManagement/warehouseMaterialRules',
                name: '仓库物料规则',
                meta: {
                    title: 'MATERIAL_CKWLGZ._CKWLGZ',
                    icon: 'mdi-alpha-q'
                },
                component: () =>
                    import('@/views/materialManagement/warehouseMaterialRules/index.vue')
            },
                {
                    path: '/materialManagement/warehouseModeling',
                    name: 'MES仓库建模',
                    meta: {
                        title: 'MATERIAL_MESCKJM._MESCKJM',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/materialManagement/warehouseModeling/index.vue')
                },
                {
                    path: '/materialManagement/rawMaterialWarehousing',
                    name: '原材料入库',
                    meta: {
                        title: '原材料入库',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/materialManagement/rawMaterialWarehousing/index.vue')
                },
                {
                    path: '/materialManagement/lineLibraryManagement',
                    name: '原料库存管理',
                    meta: {
                        title: 'MATERIAL_YLKCGL._YLKCGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/materialManagement/lineLibraryManagement/index.vue')
                },
                {
                    path: '/materialManagement/rawMaterialPreparation',
                    name: '原材料备料管理',
                    meta: {
                        title: '原材料备料管理',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/materialManagement/rawMaterialPreparation/index.vue')
                },
                {
                    path: '/materialManagement/materialPrint',
                    name: '原料分线单打印',
                    meta: {
                        title: 'MATERIAL_YLFXDDY._YLFXDDY',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/materialManagement/materialPrint/index.vue')
                },
                {
                    path: '/materialManagement/scrapManagement',
                    name: '原材料报废管理',
                    meta: {
                        title: '原材料报废管理',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/materialManagement/scrapManagement/index.vue')
                },
                {
                    path: '/materialManagement/workInProcessInventory',
                    name: 'WIP库存管理',
                    meta: {
                        title: 'MATERIAL_WIPKCGL._WIPKCGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/materialManagement/workInProcessInventory/index.vue')
                },
                {
                    path: '/materialManagement/agvWarehouse',
                    name: 'AGV仓库建模',
                    meta: {
                        title: 'MATERIAL_AGVCK._AGVCK',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/materialManagement/agvWarehouse/index.vue')
                },
                {
                    path: '/materialManagement/warehouseRelationshipModeling',
                    name: 'AGV&仓库关系建模',
                    meta: {
                        title: 'MATERIAL_CKGXJM._CKGXJM',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/materialManagement/warehouseRelationshipModeling/index.vue')
                },
                {
                    path: '/materialManagement/productionLineWarehouseModeling',
                    name: '产线&仓库关系建模',
                    meta: {
                        title: 'MATERIAL_CXCKJM._CXCKJM',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/materialManagement/ProductionLineWarehouseModeling/index.vue')
                },
                {
                    path: '/materialManagement/productionLineAGVModeling',
                    name: '产线上料点管理',
                    meta: {
                        title: 'MATERIAL_CXSLD._CXSLD',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/materialManagement/productionLineAGVModeling/index.vue')
                },
                {
                    path: '/materialManagement/materialDumpList',
                    name: '领料转储单管理',
                    meta: {
                        title: 'MATERIAL_LLZCD._LLZCD',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/materialManagement/materialDumpList/index.vue')
                },
                {
                    path: '/materialManagement/inventoryEntryAudit',
                    name: '原材料入库管理',
                    meta: {
                        title: '原材料入库管理',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/materialManagement/inventoryEntryAudit/index.vue')
                },
                {
                    path: '/materialManagement/glueManagement',
                    name: '胶水管理',
                    meta: {
                        title: 'MATERIAL_JSGL._JSGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/materialManagement/glueManagement/index.vue')
                },
                {
                    path: '/materialManagement/materialGroup',
                    name: '物料分组',
                    meta: {
                        title: 'DFM_WLGL._WLFZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/materialManagement/materialGroup/index.vue')
                }
            ]
        },
        // Andon管理
        {
            path: '/andonManagement',
            meta: {
                title: 'Andon_ADGL',
                icon: 'mdi-drag-variant'
            },
            name: 'Andon管理',
            component: RouteWrapper,
            redirect: '/andonManagement/alarmType',
            children: [{
                path: '/andonManagement/alarmType',
                name: '报警类型管理',
                meta: {
                    title: 'ANDON_BJLXGL._BJLXGL',
                    icon: 'mdi-alpha-q'
                },
                component: () =>
                    import('@/views/andonManagement/alarmType/index.vue')
            },
                {
                    path: '/andonManagement/upgradeRule',
                    name: '报警升级规则管理',
                    meta: {
                        title: 'ANDON_BJSJGZ._BJSJGZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/andonManagement/upgradeRule/index.vue')
                },
                {
                    path: '/andonManagement/noticeRecord',
                    name: '报警通知记录查询',
                    meta: {
                        title: 'ANDON_BJTZJL._BJTZJL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/andonManagement/noticeRecord/index.vue')
                },
                {
                    path: '/andonManagement/alarmHome',
                    name: 'Andon报警主页',
                    meta: {
                        title: 'ANDON_BJZY._BJZY',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/andonManagement/alarmHome/index.vue')
                },
                {
                    path: '/andonManagement/alarmRecord',
                    name: '报警记录查询',
                    meta: {
                        title: 'ANDON_BJJL._BJJL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/andonManagement/alarmRecord/index.vue')
                },
                {
                    path: '/andonManagement/toBeprocessed',
                    name: '报警记录处置',
                    meta: {
                        title: 'ANDON_BJJL._BJJLCZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/andonManagement/toBeprocessed/index.vue')
                },
                {
                    path: '/andonManagement/multipleStopRule',
                    name: '多次停机报警规则',
                    meta: {
                        title: 'ANDON_DCTJBJ._DCTJBJ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/andonManagement/multipleStopRule/index.vue')
                },
                {
                    path: '/andonManagement/alarmGroup',
                    name: '接警组管理',
                    meta: {
                        title: '接警组管理',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/andonManagement/alarmGroup/index.vue')
                },
                {
                    path: '/andonManagement/mestoscadaAlarmconfig',
                    name: '报警触发规则管理',
                    meta: {
                        title: 'ANDON_BJCFGZ._BJCFGZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/andonManagement/mestoscadaAlarmconfig/index.vue')
                },
                {
                    path: '/andonManagement/lossRecords',
                    name: '报警损耗记录查询',
                    meta: {
                        title: '报警损耗记录查询',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/andonManagement/lossRecords/index.vue')
                }
            ]
        },
        // 设备管理
        {
            path: '/equipmentManagement',
            meta: {
                title: 'TPM_SBGL',
                icon: 'mdi-drag-variant'
            },
            name: '设备管理',
            component: RouteWrapper,
            redirect: '/equipmentManagement/Equip',
            children: [{
                path: '/equipmentManagement/backlog',
                name: '设备待办事项',
                meta: {
                    title: 'TPM_SBGL_SBDBSX._SBDBSX',
                    icon: 'mdi-alpha-q'
                },
                component: () =>
                    import('@/views/equipmentManagement/backlog/index.vue')
            },
                {
                    path: '/equipmentManagement/operatingState',
                    name: '设备运行状态',
                    meta: {
                        title: 'TPM_SBGL_SBYXZT._SBYXZT',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/operatingState/index.vue')
                },
                {
                    path: '/equipmentManagement/Equip',
                    name: '设备台账管理',
                    meta: {
                        title: 'TPM_SBGL_SBTZGL._SBTZGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/Equip/index.vue')
                },
                {
                    path: '/equipmentManagement/EquipworkOrder',
                    name: '设备管理主页',
                    meta: {
                        title: 'TPM_SBGL_SBGLZY._SBGLZY',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/EquipworkOrder/index.vue')
                },
                {
                    path: '/equipmentManagement/Repair',
                    name: '设备维修工单',
                    meta: {
                        title: 'TPM_SBGL_SBWXGD._SBWXGD',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/Repair/index.vue')
                },
                {
                    path: '/equipmentManagement/RepairPlan',
                    name: '设备维修记录',
                    meta: {
                        title: 'TPM_SBGL_SBWXJL._SBWXJL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/RepairPlan/index.vue')
                },
                // {
                //     path: '/equipmentManagement/sparePart',
                //     name: '设备备件',
                //     meta: {
                //         title: '设备备件',
                //         icon: 'mdi-alpha-q'
                //     },
                //     component: () => import('@/views/equipmentManagement/sparePart/index.vue')
                // },
                {
                    path: '/equipmentManagement/spareParts',
                    name: '设备备件管理',
                    meta: {
                        title: 'TPM_SBGL_SBBJGL._SBBJGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/spareParts/index.vue')
                },
                {
                    path: '/equipmentManagement/upkeep',
                    name: '设备保养项目',
                    meta: {
                        title: 'TPM_SBGL_SBBYXM._SBBYXM',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/upkeep/index.vue')
                },
                {
                    path: '/equipmentManagement/spotCheck',
                    name: '设备点检项目',
                    meta: {
                        title: 'TPM_SBGL_SBDJXM._SBDJXM',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/spotCheck/index.vue')
                },

                {
                    path: '/equipmentManagement/upkeeplist',
                    name: '设备点检计划',
                    meta: {
                        title: 'TPM_SBGL_SBDJJH._SBDJJH',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/upkeeplist/index.vue')
                },
                {
                    path: '/equipmentManagement/upkeepplan',
                    name: '设备点检规则',
                    meta: {
                        title: 'TPM_SBGL_SBDJGZ._SBDJGZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/upkeepplan/index.vue')
                },
                {
                    path: '/equipmentManagement/upkeeplistB',
                    name: '设备保养计划',
                    meta: {
                        title: 'TPM_SBGL_SBBYJH._SBBYJH',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/upkeeplistB/index.vue')
                },
                {
                    path: '/equipmentManagement/upkeepplanB',
                    name: '设备保养规则',
                    meta: {
                        title: 'TPM_SBGL_SBBYGZ._SBBYGZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/upkeepplanB/index.vue')
                },
                {
                    path: '/equipmentManagement/upkeepworkB',
                    name: '设备保养任务',
                    meta: {
                        title: 'TPM_SBGL_SBDJXM._SBBYRW',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/upkeepworkB/index.vue')
                },
                {
                    path: '/equipmentManagement/classification',
                    name: '设备分类管理',
                    meta: {
                        title: 'TPM_SBGL_SBDJXM._SBFLGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/classification/index.vue')
                },
                {
                    path: '/equipmentManagement/distribution',
                    name: '备件发放管理',
                    meta: {
                        title: 'TPM_SBGL_SBDJXM._BJFFGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/distribution/index.vue')
                },
                {
                    path: '/equipmentManagement/equipmentReasonTree',
                    name: '故障知识库',
                    meta: {
                        title: 'TPM_SBGL_GZZSK._GZZSK',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/equipmentReasonTree/index.vue')
                },
                {
                    path: '/equipmentManagement/maticMaintenance',
                    name: '智能运维配置',
                    meta: {
                        title: 'DFM_ZNYWPZ._ZNYWPZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/maticMaintenance/index.vue')
                },
                {
                    path: '/equipmentManagement/equipmentShutdownRecord',
                    name: '设备停机记录',
                    meta: {
                        title: 'DFM_SBTJJL._SBTJJL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/equipmentShutdownRecord/index.vue')
                },
                {
                    path: '/equipmentManagement/equipmentFaultDefinition',
                    name: '设备故障定义',
                    meta: {
                        title: 'DFM_SBGZDY._SBGZDY',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/equipmentFaultDefinition/index.vue')
                },
                {
                    path: '/equipmentManagement/warning',
                    name: '备件预警查询',
                    meta: {
                        title: 'TPM_SBGL_SBDJXM._BJYJCX',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/warning/index.vue')
                },
                {
                    path: '/equipmentManagement/MyRepair',
                    name: '我的报修',
                    meta: {
                        title: 'TPM_SBGL_SBDJXM._WDBX',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/MyRepair/index.vue')
                }, {
                    path: '/equipmentManagement/MyWorkorder',
                    name: '我的工单',
                    meta: {
                        title: 'TPM_SBGL_SBDJXM._WDGD',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/MyWorkorder/index.vue')
                }, {
                    path: '/equipmentManagement/ServicOrder',
                    name: '服务单管理',
                    meta: {
                        title: 'TPM_SBGL_SBDJXM._FWDGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/ServicOrder/index.vue')
                }, {
                    path: '/equipmentManagement/Overhaulplan',
                    name: '设备大修计划',
                    meta: {
                        title: 'TPM_SBGL_SBDJXM._SBDXJH',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/Overhaulplan/index.vue')
                }, {
                    path: '/equipmentManagement/Overhaultask',
                    name: '设备大修任务',
                    meta: {
                        title: 'TPM_SBGL_SBDJXM._SBDXRW',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/Overhaultask/index.vue')
                }, {
                    path: '/equipmentManagement/instrument',
                    name: '计量器具台账',
                    meta: {
                        title: 'TPM_SBGL_SBDJXM._JLYQTZ',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/instrument/index.vue')
                }, {
                    path: '/equipmentManagement/Inspectiontask',
                    name: '检验任务管理',
                    meta: {
                        title: 'TPM_SBGL_SBDJXM._JYRWGL',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/Inspectiontask/index.vue')
                }, {
                    path: '/equipmentManagement/Inspectionremind',
                    name: '检验提醒查询',
                    meta: {
                        title: 'TPM_SBGL_SBDJXM._YJTXCX',
                        icon: 'mdi-alpha-q'
                    },
                    component: () =>
                        import('@/views/equipmentManagement/Inspectionremind/index.vue')
                },
            ]
        },
        // 看板管理
        {
            path: '/equipmentdashboard',
            meta: {
                title: '_SBKB',
                icon: 'mdi-drag-variant'
            },
            component: RouteWrapper,
            redirect: '/equipmentdashboard',
            name: '设备看板',
            children: [{
                path: '/equipmentdashboard/DeviceTasks',
                name: '设备任务',
                meta: {
                    title: 'SBKB.SBRW',
                    icon: 'mdi-alpha-q'
                },
                component: () =>
                    import('@/views/equipmentdashboard/DeviceTasks/index.vue')
            }, {
                path: '/equipmentdashboard/FaultAnalysis',
                name: '故障分析看板',
                meta: {
                    title: 'SBKB.GZFXKB',
                    icon: 'mdi-alpha-q'
                },
                component: () =>
                    import('@/views/equipmentdashboard/FaultAnalysis/index.vue')
            }, {
                path: '/equipmentdashboard/FaultAnalysis2',
                name: '故障分析',
                meta: {
                    title: 'SBKB.GZFX',
                    icon: 'mdi-alpha-q'
                },
                component: () =>
                    import('@/views/equipmentdashboard/FaultAnalysis2/index.vue')
            },]
        },
        // 看板管理
        {
            path: '/kanbanManagement',
            meta: {
                title: 'KANBAN_KBGL',
                icon: 'mdi-drag-variant'
            },
            name: '设备看板',
            component: RouteWrapper,
            redirect: '/kanbanManagement/DeviceKanban',
            children: [{
                path: '/kanbanManagement/DeviceKanban',
                name: '设备看板',
                meta: {
                    title: 'KANBAN_SBKB._SBKB',
                    icon: 'mdi-alpha-q'
                },
                component: () =>
                    import('@/views/kanbanManagement/DeviceKanban/index.vue')
            },
                {
                    path: '/kanbanManagement/maintenanceKanban',
                    name: '运维看板',
                    meta: {
                        title: 'DFM_YWKB._YWKB',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/kanbanManagement/maintenanceKanban/index.vue')
                },
                {
                    path: '/kanbanManagement/workOrderKanban',
                    name: '工单看板',
                    meta: {
                        title: 'DFM_YWKB._GDKB',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/kanbanManagement/maintenanceKanban/workOrderKanban.vue')
                },
                {
                    path: '/kanbanManagement/publicDeviceKanban',
                    name: '公共设备看板',
                    meta: {
                        title: 'DFM_YWKB._GGSBKB',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/kanbanManagement/maintenanceKanban/publicDeviceKanban.vue')
                },
                {
                    path: '/kanbanManagement/FactoryKanban',
                    name: '工厂看板',
                    meta: {
                        title: 'KANBAN_GCKB._GCKB',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/kanbanManagement/FactoryKanban/index.vue')
                },
                {
                    path: '/kanbanManagement/FailureStatistics',
                    name: '故障统计',
                    meta: {
                        title: 'KANBAN_GZTJ._GZTJ',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/kanbanManagement/FailureStatistics/index.vue')
                },
            ]
        },
        {
            path: '/simMangment',
            meta: {
                title: 'SIM.Mang',
                icon: 'mdi-alpha-b-circle-outline'
            },
            name: 'SIM管理',
            component: RouteWrapper,
            redirect: '/simMangment',
            children: [{
                path: '/simMangment/sim1',
                name: 'SIM1',
                meta: {
                    title: 'SIM1',
                    icon: 'mdi-alpha-b-circle-outline'
                },
                component: () =>
                    import('@/views/simManagement/sim1/index.vue')
            },
                {
                    path: '/simMangment/sim2',
                    name: 'SIM2',
                    meta: {
                        title: 'SIM2',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/simManagement/sim2/index.vue')
                },
                {
                    path: '/simMangment/sim3',
                    name: 'SIM3',
                    meta: {
                        title: 'SIM3',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/simManagement/sim3/index.vue')
                },
                {
                    path: '/simMangment/sim4',
                    name: 'SIM4Outside',
                    meta: {
                        title: 'SIM4Outside',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/simManagement/sim4/index.vue')
                },
                {
                    path: '/simMangment/sim5',
                    name: 'SIM5',
                    meta: {
                        title: 'SIM5',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/simManagement/sim5/index.vue')
                },
                {
                    path: '/simMangment/inspection',
                    name: 'Inspection',
                    meta: {
                        title: '5S',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/simManagement/inspection/index.vue')
                }
            ]
        },
        {
            path: '/Inventory',
            meta: {
                title: 'WLKCGL_WLKCGL',
                icon: 'mdi-alpha-b-circle-outline'
            },
            name: '库存管理',
            component: RouteWrapper,
            redirect: '/Inventory',
            children: [{
                path: '/Inventory/Listing',
                name: 'WLKCGL_WLKCGLMENU.LISTING',
                meta: {
                    title: 'WLKCGL_WLKCGLMENU.LISTING',
                    icon: 'mdi-alpha-b-circle-outline'
                },
                component: () =>
                    import('@/views/Inventory/Listing/index.vue')
            },
                {
                    path: '/Inventory/MaterialLabel',
                    name: 'WLKCGL_WLKCGLMENU.YLBQ',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.YLBQ',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/MaterialLabel/index.vue')
                }, {
                    path: '/Inventory/MaterialLabelcopy',
                    name: 'WLKCGL_WLKCGLMENU.YLBQ',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.YLBQ',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/MaterialLabelcopy/index.vue')
                }, {
                    path: '/Inventory/MaterialLabelTable',
                    name: 'WLKCGL_WLKCGLMENU.YLBQ',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.YLBQ',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/MaterialLabelTable/index.vue')
                }, {
                    path: '/Inventory/MaterialLabelTablecopy',
                    name: 'WLKCGL_WLKCGLMENU.YLBQ',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.YLBQ',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/MaterialLabelTablecopy/index.vue')
                },
                {
                    path: '/Inventory/MaterialPreparation',
                    name: 'WLKCGL_WLKCGLMENU.MaterialPreparation',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.MaterialPreparation',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/MaterialPreparation/index.vue')
                },
                {
                    path: '/Inventory/MaterialPreparationByBatch',
                    name: 'WLKCGL_WLKCGLMENU.MaterialPreparationByBatch',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.MaterialPreparationByBatch',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/MaterialPreparationByBatch/index.vue')
                },
                {
                    path: '/Inventory/MaterialPreparationBulid',
                    name: 'WLKCGL_WLKCGLMENU.MaterialPreparationBulid',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.MaterialPreparationBulid',
                        hidden: true
                    },
                    component: () =>
                        import('@/views/Inventory/MaterialPreparationBulid/index.vue')
                },
                {
                    path: '/Inventory/ProductionHistory',
                    name: 'WLKCGL_WLKCGLMENU.ProductionHistory',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.ProductionHistory',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/ProductionHistory/index.vue')
                },
                {
                    path: '/Inventory/ProductionSummary',
                    name: 'WLKCGL_WLKCGLMENU.ProductionSummary',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.ProductionSummary',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/ProductionSummary/index.vue')
                },
                {
                    path: '/Inventory/ConsumptionHistory',
                    name: 'WLKCGL_WLKCGLMENU.ConsumptionHistory',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.ConsumptionHistory',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/ConsumptionHistory/index.vue')
                },
                {
                    path: '/Inventory/TransferHistory',
                    name: 'WLKCGL_WLKCGLMENU.TransferHistory',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.TransferHistory',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/TransferHistory/index.vue')
                },
                {
                    path: '/Inventory/Feeding',
                    name: 'WLKCGL_WLKCGLMENU.Feeding',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.Feeding',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/Feeding/index.vue')
                },

                {
                    path: '/Inventory/ContainerManagement',
                    name: 'WLKCGL_WLKCGLMENU.ContainerManagement',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.ContainerManagement',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/ContainerManagement/index.vue')
                },
                {
                    path: '/Inventory/PalletList',
                    name: 'WLKCGL_WLKCGLMENU.PalletList',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.PalletList',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/PalletList/index.vue')
                }, {
                    path: '/Inventory/BatchPallets',
                    name: 'WLKCGL_WLKCGLMENU.BatchPallets',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.BatchPallets',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/BatchPallets/index.vue')
                }, {
                    path: '/Inventory/PrecheckFeeding',
                    name: 'WLKCGL_WLKCGLMENU.PrecheckFeeding',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.PrecheckFeeding',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/PrecheckFeeding/index.vue')
                }, {
                    path: '/Inventory/precheck',
                    name: 'WLKCGL_WLKCGLMENU.precheck',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.precheck',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/precheck/index.vue')
                }, {
                    path: '/Inventory/MaterialPreparationPrecheck',
                    name: 'WLKCGL_WLKCGLMENU.MaterialPreparationPrecheck',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.MaterialPreparationPrecheck',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Inventory/MaterialPreparationPrecheck/index.vue')
                },
            ]
        },
        {
            path: '/Producting',
            meta: {
                title: 'WLKCGL_SCZX',
                icon: 'mdi-alpha-b-circle-outline'
            },
            name: '生产执行',
            component: RouteWrapper,
            redirect: '/Producting',
            children: [{
                path: '/Producting/OperatorConsoleListing',
                name: 'WLKCGL_WLKCGLMENU.OperatorConsoleListing',
                meta: {
                    title: 'WLKCGL_WLKCGLMENU.OperatorConsoleListing',
                    icon: 'mdi-alpha-b-circle-outline'
                },
                component: () =>
                    import('@/views/Producting/OperatorConsoleListing/index.vue')
            }, {
                path: '/Producting/POlist',
                name: 'WLKCGL_WLKCGLMENU.PoList',
                meta: {
                    title: 'WLKCGL_WLKCGLMENU.PoList',
                    icon: 'mdi-alpha-b-circle-outline'
                },
                component: () =>
                    import('@/views/Producting/POlist/index.vue')
            }, {
                path: '/Producting/QualityResult',
                name: 'WLKCGL_WLKCGLMENU.QualityResult',
                meta: {
                    title: 'WLKCGL_WLKCGLMENU.QualityResult',
                    icon: 'mdi-alpha-b-circle-outline'
                },
                component: () =>
                    import('@/views/Producting/QualityResult/index.vue')
            },
                {
                    path: '/Producting/EnergyConfiguration',
                    name: 'WLKCGL_WLKCGLMENU.NYPZ',
                    meta: {
                        title: 'WLKCGL_WLKCGLMENU.NYPZ',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Producting/EnergyConfiguration/index.vue')
                },
                {
                    path: '/Producting/CIPRecord',
                    name: 'CIP记录',
                    meta: {
                        title: 'CIPRecord.title',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Producting/CIPRecord/index.vue')
                },
                {
                    path: '/Producting/MMILog',
                    name: 'MMI日志',
                    meta: {
                        title: 'CIPRecord.title',
                        icon: 'mdi-alpha-b-circle-outline'
                    },
                    component: () =>
                        import('@/views/Producting/MMILog/index.vue')
                },
            ]
        },
        // 看板管理
        {
            path: '/productivity',
            meta: {
                title: '生产绩效',
                icon: 'mdi-drag-variant'
            },
            component: RouteWrapper,
            redirect: '/productivity/Dashboard',
            name: '生产绩效',
            children: [
                {
                    path: '/productivity/Dashboard/RawMaterialTask',
                    name: '原料任务看板',
                    meta: {
                        title: 'RawMaterialTask.title',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/productivity/Dashboard/RawMaterialTask/index.vue')
                },
                {
                    path: '/productivity/Dashboard/ColdStorageInventory',
                    name: '3-4冷库库存',
                    meta: {
                        title: 'RawMaterialTask.title',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/productivity/Dashboard/ColdStorageInventory/index.vue')
                },
                {
                    path: '/productivity/Dashboard/MaterialPreparationDashboard',
                    name: '备料看板',
                    meta: {
                        title: 'RawMaterialTask.title',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/productivity/Dashboard/MaterialPreparationDashboard/index.vue')
                },
                {
                    path: '/productivity/Dashboard/CookingIngredients',
                    name: '煮料看板',
                    meta: {
                        title: 'CookingIngredients.title',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/productivity/Dashboard/CookingIngredients/index.vue')
                },
                {
                    path: '/productivity/Dashboard/PackageConsole',
                    name: '包装看板',
                    meta: {
                        title: 'PackageConsole.title',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/productivity/Dashboard/PackageConsole/index.vue')
                },
                {
                    path: '/productivity/Dashboard/LineFeeding',
                    name: '产线投料',
                    meta: {
                        title: 'LineFeeding.title',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/productivity/Dashboard/LineFeeding/index.vue')
                },
                {
                    path: '/productivity/Dashboard/DashBoardMenu',
                    name: '看板菜单',
                    meta: {
                        title: 'LineFeeding.title',
                        icon: 'mdi-alpha-q',
                        isNoKeepAlive: true
                    },
                    component: () =>
                        import('@/views/productivity/Dashboard/DashBoardMenu/index.vue')
                }
            ]
        },
        // 报表
        {
            path: '/report',
            meta: {
                title: '报表',
                icon: 'mdi-drag-variant'
            },
            component: RouteWrapper,
            redirect: '/report',
            name: '报表管理',
            children: [
                {
                    path: '/report/MonthProduction',
                    name: '包装三厂月产量表',
                    meta: {
                        title: 'MonthProduction.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/MonthProduction/index.vue')
                },
                {
                    path: '/report/MonthProductionDetail',
                    name: '产量明细表',
                    meta: {
                        title: 'MonthProductionDetail.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/MonthProductionDetail/index.vue')
                },
                {
                    path: '/report/Throughput',
                    name: '三厂每月产出率',
                    meta: {
                        title: 'Throughput.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/Throughput/index.vue')
                },
                {
                    path: '/report/OneCompletion',
                    name: '工单一次性完成率',
                    meta: {
                        title: 'OneCompletion.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/OneCompletion/index.vue')
                },
                {
                    path: '/report/ReportOee',
                    name: 'OEE数据 ',
                    meta: {
                        title: 'ReportOee.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/ReportOee/index.vue')
                },
                {
                    path: '/report/NonStandardProducts',
                    name: '非标产品工时及产量',
                    meta: {
                        title: 'NonStandardProducts.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/NonStandardProducts/index.vue')
                },
                {
                    path: '/report/ReworkWorkOrder',
                    name: '返工工单',
                    meta: {
                        title: 'ReworkWorkOrder.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/ReworkWorkOrder/index.vue')
                },
                {
                    path: '/report/UnitPersonHour',
                    name: '单位人时劳动力',
                    meta: {
                        title: 'UnitPersonHour.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/UnitPersonHour/index.vue')
                },
                {
                    path: '/report/CookingTankAdjustment',
                    name: '煮缸调节率',
                    meta: {
                        title: 'CookingTankAdjustment.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/CookingTankAdjustment/index.vue')
                },
                {
                    path: '/report/RawMaterialLoss',
                    name: '原料损耗率',
                    meta: {
                        title: 'RawMaterialLoss.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/RawMaterialLoss/index.vue')
                },
                {
                    path: '/report/PackagingMaterialLoss',
                    name: '包材损耗率',
                    meta: {
                        title: 'PackagingMaterialLoss.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/PackagingMaterialLoss/index.vue')
                },
                {
                    path: '/report/PackagingLineEquipment',
                    name: '包装线设备',
                    meta: {
                        title: 'PackagingLineEquipment.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/PackagingLineEquipment/index.vue')
                },
                {
                    path: '/report/ShutdownTimeAnalysis',
                    name: '停机时间分析',
                    meta: {
                        title: 'ShutdownTimeAnalysis.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/ShutdownTimeAnalysis/index.vue')
                },
                {
                    path: '/report/EquipmentFailureAnalysis',
                    name: '设备故障分析',
                    meta: {
                        title: 'EquipmentFailureAnalysis.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/EquipmentFailureAnalysis/index.vue')
                },
                {
                    path: '/report/KPIReport',
                    name: 'KPI报表',
                    meta: {
                        title: 'KPIReport.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/KPIReport/index.vue')
                },
                {
                    path: '/report/SIM2Package',
                    name: 'SIM2Package',
                    meta: {
                        title: 'SIM2Package.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/SIM2Package/index.vue')
                },
                {
                    path: '/report/SIM2',
                    name: 'SIM2',
                    meta: {
                        title: 'SIM2.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/SIM2/index.vue')
                },
                {
                    path: '/report/SIM3',
                    name: 'SIM3',
                    meta: {
                        title: 'SIM3.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/SIM3/index.vue')
                },
                {
                    path: '/report/SIM4Outside',
                    name: 'SIM4Outside',
                    meta: {
                        title: 'SIM4Outside.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/SIM4Outside/index.vue')
                },
                {
                    path: '/report/SIM3Package',
                    name: 'SIM3Package',
                    meta: {
                        title: 'SIM3Package.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/SIM3Package/index.vue')
                },
                {
                    path: '/report/SIM4',
                    name: 'SIM4',
                    meta: {
                        title: 'SIM4.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/SIM4/index.vue')
                },
                {
                    path: '/report/UnitPersonHourLine',
                    name: 'UnitPersonHourLine',
                    meta: {
                        name: '单位人时劳动力-制造',
                        title: 'UnitPersonHourLine.title',
                        icon: 'mdi-alpha-q',
                    },
                    component: () =>
                        import('@/views/report/UnitPersonHourLine/index.vue')
                }
            ]
        },
        {
            path: '/print',
            meta: {
                title: 'DFM_PRINT',
                icon: 'mdi-drag-variant'
            },
            name: '打印设置',
            component: RouteWrapper,
            redirect: '/print',
            children: [],
        },
        {
            path: '/403',
            name: 'Forbidden',
            meta: {
                title: 'access_denied',
                hidden: true
            },
            component: () =>
                import('@/views/error/Deny.vue')
        }
    ]
}];

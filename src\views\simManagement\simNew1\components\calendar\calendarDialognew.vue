<template>
  <el-dialog
    :style="backgroundVar"
    title="事故列表"
    :append-to-body="!fullscreen"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="value"
    :before-close="handleClose1"
    lock-scroll
    :fullscreen="true"
  >
    <div class="styleTable">
      <a-button @click="add()">新增</a-button>
      <a-button @click="edit()">修改</a-button>
      <a-button @click="del()">删除</a-button>
      <el-table
        :data="tableData"
        border
        style="width: 100%; margin-top: 20px; color: #fff; font-size: 16px; font-weight: bold"
        :header-cell-style="{ background: '#fafafa', textAlign: 'center',fontSize:'22px' }"
        :row-style="{ height: '35px' }"
        ref="vxeTable"
        @selection-change="handleSelectionChange"
        :height="tableHeight"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="PresentDate"
          label="发起日期"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="Team"
          label="班组"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="PresentUser"
          label="提出人"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="ResponsibleDepartment"
          label="责任部门"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="ResponsibleUser"
          label="责任人"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="PlanFinishDate"
          label="计划完成日期"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="ActualFinishDate"
          label="实际完成日期"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="ClassfyCode1"
          label="事故类型"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="AccidentDesc"
          label="事故描述"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="PresentDepartment"
          label="事故所在产线"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>
    </div>
    <addDialognew
      v-if="addDialogVisablenew"
      v-model="showaddDialogVisablenew"
      :fullscreen="fullscreen"
      :editItemObj="editItemObj"
      :searchFormObj="searchFormObj"
      :curTeamTreeObj="curTeamTreeObj"
      :backgroundImg="backgroundImg"
      :currentYearMonth1="currentYearMonth1"
      @addDown="addDown1"
      @closePopup1="closeDialog1"
      :key="addkey"
    />
  </el-dialog>
</template>
<script>
import addDialognew from './addDialognew.vue';
import { GetSafePageList, DeleteSafeAccident, UpdateSafeAccident } from '@/views/simManagement/sim1/service.js';
import dayjs from 'dayjs';
import { mapGetters } from 'vuex';


export default {
  components: {
    addDialognew
  },
  props: {
    // 是否显示弹出框
    value: {
      type: Boolean,
      default: false
    },
    curDay: {
      type: Object,
      default: () => { }
    },
    backgroundImg: {
      type: String,
      default: ''
    },
    searchFormObj: {
      type: Object,
      default: () => { }
    },
    curTeamTreeObj: {
      type: Object,
      default: () => { }
    },
    fullscreen: {
      type: Boolean,
      default: false
    },
    currentYearMonth1: {
      type: String,
      default: ''
    },
    bcode: {
      type: String,
      default: ''
    }
  },
  computed: {
    backgroundVar() {
      return {
        '--background': this.backgroundImg
      };
    },
    ...mapGetters(['getUserinfolist']),
    curAuth() {
      return this.$store.getters.getUserinfolist[0]
    },
    ClassfyCodeList() {
      return this.$store.state.sim.ClassfyCodeList
    },
  },
  data() {
    return {
      addkey: 1,
      tableHeight: 0,
      addDialogVisablenew: false,
      showaddDialogVisablenew: false,
      editItemObj: {},
      tableData: [],
      searchForm: {
        start: '',
        end: ''
      }
    };
  },
  created() {
    this.$store.dispatch('getClassfyCodeList');
    this.getList();
  },
  mounted() {
    this.$nextTick(function () {
      this.tableHeight = window.innerHeight - 170;
      let self = this;
      window.onresize = function () {
        self.tableHeight = window.innerHeight - 170;
      };
    });
  },
  methods: {
    async getList() {
      let params = {
        // "TeamId": this.searchFormObj.PresentDepartmentId,
        QueryStartDate: this.searchForm.start,
        QueryEndDate: this.searchForm.end,
        pageIndex: 1,
        pageSize: 10
      };
      // if (this.$route.name === 'SIM1') {
      //   params.TeamId = this.$store.getters.getUserinfolist[0].Departmentid;
      // } else if (this.$route.name === 'SIM2') {
      //   params.DepartmentId = this.$store.getters.getUserinfolist[0].Departmentid;
      // } else {
      //   params.PlantId = this.$store.getters.getUserinfolist[0].Departmentid;
      // }
      // params.DepartmentId = this.curTeamTreeObj.ProductionLineCode;
      params.DepartmentId = this.bcode
      let { response } = await GetSafePageList(params);
      // let { response } = await GetMonthStatistics(params)
      response.data.forEach(item => {
        item.PlanFinishDate = dayjs(item.PlanFinishDate).format('YYYY-MM-DD');
        item.PresentDate = dayjs(item.PresentDate).format('YYYY-MM-DD');
        item.CreateDate = dayjs(item.CreateDate).format('YYYY-MM-DD');
        item.ModifyDate = dayjs(item.ModifyDate).format('YYYY-MM-DD');
        if (item.ActualFinishDate) {
          item.ActualFinishDate = dayjs(item.ActualFinishDate).format('YYYY-MM-DD');
        }
      });
      this.tableData = response.data;
      console.log(this.ClassfyCodeList, 'this.$store.state.sim.ClassfyCodeList');

      this.tableData.map(el => {
        this.ClassfyCodeList.map(item => {
          console.log(item, 99999);

          if (el.ClassfyCode == item.ItemValue) {
            el.ClassfyCode1 = item.ItemName
          }
        })
      })
    },
    handleSelectionChange(val) {
      if (val.length > 1) {
        this.$store.commit('SHOW_SNACKBAR', { text: '只能勾选一条数据', color: 'error' });
        // this.$message({
        //   message: '只能勾选一条数据',
        //   type: "error",
        //   customClass: 'messageIndex'
        // })
        return;
      }
      this.multipleSelection = val;
    },
    add() {
      this.addkey++
      this.addDialogVisablenew = true;
      this.showaddDialogVisablenew = true;
    },
    edit() {
      // let selectRecords = this.$refs.vxeTable.getCheckboxRecords()
      if (this.multipleSelection.length !== 1) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
        return false;
      }
      this.editItemObj = this.multipleSelection[0];
      this.addkey++
      this.addDialogVisablenew = true;
      this.showaddDialogVisablenew = true;
    },
    update() {
      // let selectRecords = this.$refs.vxeTable.getCheckboxRecords()
      if (this.multipleSelection.length == 0) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
        return false;
      }
      this.$confirms({
        message: this.$t('请确认是否升级'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        let params = {
          id: this.multipleSelection[0].ID
        };
        let res = await UpdateSafeAccident(params);
        this.$store.commit('SHOW_SNACKBAR', { text: res.msg, color: 'success' });
        this.getList();
      });
    },
    //删除
    del() {
      // let selectRecords = this.$refs.vxeTable.getCheckboxRecords()
      if (this.multipleSelection.length == 0) {
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SELECT'), color: 'blue' });
        return false;
      }
      this.$confirms({
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        let params = this.multipleSelection.map(item => item.ID);
        let res = await DeleteSafeAccident(params);
        this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
        this.getList();
      });
    },
    handleClose1() {
      this.$emit('checkcalendarDialognew');
    },
    closeDialog1() {
      this.addDialogVisablenew = false;
      this.editItemObj = {};
    },
    addDown1() {
      this.addDialogVisablenew = false;
      this.showaddDialogVisablenew = false;
      this.getList();
    }
  }
};
</script>
<style scoped>
/deep/ .el-dialog {
    background: var(--background) no-repeat 0 0;
    background-size: 100% 100% !important;
    overflow: hidden;
}
.styleTable /deep/.el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}
.styleTable /deep/ .el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}

.styleTable /deep/ .el-table tr {
    background-color: transparent !important;
    border: none;
}
.styleTable /deep/ .el-table--enable-row-transition .el-table__body td,
.el-table .cell {
    background-color: transparent !important;
}
.styleTable /deep/ .el-table th.el-table__cell {
    background-color: transparent !important;
    color: #fff;
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}
/deep/ .el-dialog__title {
    color: #fff !important;
}
/deep/ .theme--light.v-input input,
.theme--light.v-input textarea {
    color: #fff !important;
}
/deep/ .theme--light.v-input {
    color: #fff !important;
}
/deep/ .el-table__header-wrapper {
    background-color: #4391f4 !important;
}
</style>

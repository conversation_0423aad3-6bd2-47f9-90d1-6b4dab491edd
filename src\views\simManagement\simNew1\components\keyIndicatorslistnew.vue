<template>
  <el-dialog
    :style="backgroundVar"
    :title="jtitle"
    :append-to-body="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="value"
    :before-close="handleClosekeynew"
    lock-scroll
    :fullscreen="true"
  >
    <v-form ref="form">
      <v-row class="ma-4">
        <el-col :span="24">
          <el-date-picker
            v-if="tabId == 0 &&isSql =='0'"
            v-model="value1"
            type="date"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="handleDateChange"
          >
          </el-date-picker>
          <el-select
            style="margin-left: 12px;"
            v-if="tabId == 0 &&isSql =='0'"
            v-model="materialName"
            placeholder="请选择物料"
            filterable
            clearable
            @change="selectChange"
          >
            <el-option
              v-for="item in legendData"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
          <el-radio
            v-if="tabId == 0 &&isSql =='0'"
            v-model="radio"
            label="白"
            style="color:#fff;margin-left: 20px;"
            @change="radioChange"
          >白班</el-radio>
          <el-radio
            v-if="tabId == 0 &&isSql =='0'"
            v-model="radio"
            label="夜"
            style="color:#fff;"
            @change="radioChange"
          >夜班</el-radio>
          <el-date-picker
            v-if="isSql =='1' && tbName != '九宫格'"
            v-model="withdrawTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            :append-to-body="false"
            @change="handleDateChange"
          >
          </el-date-picker>
        </el-col>
        <!-- <el-col span="1">
            <v-btn
              style="margin-top: 5px;"
              @click="search()"
            >{{ $t('GLOBAL._CX') }}</v-btn>
          </el-col> -->

      </v-row>
    </v-form>
    <div v-if="isSql == '0'">
      <div style="display: flex;margin-top: 40px;">
        <div
          class="titName"
          v-for="(item,index) in list1"
          :key="index"
          :class="{'active':tabId == index}"
          @click="tabClick(item)"
        >{{item.tabName}}</div>
      </div>
    </div>
    <div class="styleTable">
      <el-table
        style="margin-top: 20px;color:#fff; font-size: 16px;font-weight: bold;overflow-y: auto;"
        v-if="isSql == '1'"
        :data="tableData1"
        border
        :show-overflow-tooltip="true"
        :height="tableHeight"
        :header-cell-style="{background:'#fafafa',textAlign: 'center',color:'#409eff',fontSize:'22px'}"
      >
        <el-table-column
          v-for="(header, index) in Object.keys(tableData1[0])"
          :key="index"
          :prop="header"
          :label="header"
          header-align="center"
          align="center"
          show-overflow-tooltip
        >
        </el-table-column>
      </el-table>

      <div class="styleTable">
        <el-table
          v-if="tabId == 1 && isSql == '0'"
          :data="dataTagDat"
          border
          style="width: 100%;margin-top: 20px;color:#fff; font-size: 16px;font-weight: bold;overflow-y: auto;"
          :header-cell-style="{background:'#fafafa',textAlign: 'center',color:'#409eff',fontSize:'22px'}"
          :row-style="{height: '35px'}"
          show-overflow-tooltip
          :tooltip-effect="'dark'"
          :height="tableHeight1"
          default-expand-all
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
          row-key="id"
        >
          <el-table-column
            align="center"
            prop="Factory"
            label="工厂"
            min-width="90"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="ProductionLine"
            label="车间"
            min-width="90"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="Process"
            label="产线"
            min-width="90"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="Specification"
            label="规格"
            min-width="90"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="Unit"
            label="单位"
            min-width="70"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="DataTime"
            label="数据时间"
            min-width="120"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="DataValue"
            label="实绩值"
            min-width="70"
            show-overflow-tooltip
          >
          </el-table-column>
        </el-table>
      </div>
      <div class="styleTable">
        <el-table
          v-if="tabId == 0 &&isSql =='0'"
          :data="dataKpiDat"
          border
          style="width: 100%;margin-top: 20px;color:#fff; font-size: 16px;font-weight: bold;overflow-y: auto;"
          :header-cell-style="{background:'#fafafa',textAlign: 'center',fontSize:'22px',color:'#409eff'}"
          :row-style="{height: '35px'}"
          :height="tableHeight1"
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
          row-key="ID"
          :default-expand-all="false"
        >
          <el-table-column
            align="center"
            prop="CODENAME"
            label="指标名称"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="CODE"
            label="指标编码"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="DATA_TIME"
            label="产出日期"
            show-overflow-tooltip
            min-width="150"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="TEAM"
            label="班组"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="MARK"
            label="物料名称"
            show-overflow-tooltip
            min-width="150"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="MACHINE"
            label="机台"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="SHIFT"
            label="班次"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="SPECIFICATION"
            label="规格"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="DATA_VALUE"
            label="实际值"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="TIME_DIMENSION"
            label="时间维度"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="EXPRESSION"
            label="计算公式"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="EXPRESSIONTOSTRING"
            label="计算方式"
            show-overflow-tooltip
          >
          </el-table-column>
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { getTablgetDodowneList, getDodown, getTableList, getMaterialList } from '@/api/simConfig/simconfignew.js';
import dayjs from 'dayjs'
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    exhibitionType: {
      type: String,
      default: ''
    },
    jtitle: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    Order: {
      type: String,
      default: ''
    },
    isSql: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    barName: {
      type: String,
      default: ''
    },
    backgroundImg: {
      type: String,
      default: ''
    },
    Particle: {
      type: String,
      default: ''
    },
    legendData: {
      type: Array,
      default: () => []
    },
    BaseTime2: {
      type: String,
      default: ''
    },
    tbName: {
      type: String,
      default: ''
    },
    seriesName1: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      materialName: '',
      options: [],
      radio: '',
      value1: '',
      tableHeight: 0,
      tableHeight1: 0,
      // list1: [{ tabName: "分析数据", id: 0 }, { tabName: "原始数据", id: 1 }],
      list1: [{ tabName: "分析数据", id: 0 }],
      dataTagDat: [],
      dataKpiDat: [],
      e1: 1,
      withdrawTime: [],
      tableData1: [{}],
      tcChart: null,
      showDialog: false,
      columns: [],
      startTime: '',
      endTime: '',
      tabId: 0,
    }
  },
  computed: {
    backgroundVar() {
      return {
        '--background': this.backgroundImg
      }
    },
  },
  created() {
    this.tableData1 = [{}]
    this.tableData = []
    this.dataTagDat = []
    this.dataKpiDat = []
    if (this.isSql == '0') {
      this.searchKpi()
    }
    if (this.isSql == '1') {
      this.searchSql()
    }
  },
  mounted() {
    this.value1 = this.BaseTime2
    this.materialName = this.seriesName1
    this.tableData1 = [{}]
    this.tableData = []
    this.dataTagDat = []
    this.dataKpiDat = []
    this.$nextTick(function () {
      this.tableHeight = window.innerHeight - 190;
      this.tableHeight1 = window.innerHeight - 290;
      let self = this;
      window.onresize = function () {
        self.tableHeight = window.innerHeight - 190
        self.tableHeight1 = window.innerHeight - 290
      }
    })
  },
  methods: {
    selectChange() {
      this.searchKpi()
    },
    radioChange() {
      this.searchKpi()
    },
    handleDateChange(e) {
      if (this.isSql == '0') {
        this.searchKpi()
      }
      if (this.isSql == '1') {
        this.searchSql()
      }
    },
    tabClick(item) {
      this.tabId = item.id
    },
    handleClosekeynew() {
      this.$emit('keynew')
    },
    flagChange() {
      this.tableData1 = [{}]
      this.tableData = []
      this.dataTagDat = []
      this.dataKpiDat = []
    },
    change_back() {
      if (this.e1 > 1 && this.e1 <= 2) --this.e1
    },
    change_nextStep() {
      if (this.e1 <= 1) this.e1++
    },
    // search() {
    //   if (this.isSql == '0') {
    //     this.searchKpi()
    //   }
    //   if (this.isSql == '1') {
    //     this.searchSql()
    //   }

    // },
    async searchKpi() {
      // console.log(12345);

      var time
      // time = dayjs('24-12-20').format('YYYY-MM-dd');
      // console.log(time, 'timetime');

      // if (this.Particle == '年') {
      //   time = this.BaseTime2.split('-')[0] + '-01-01'
      // }
      if (this.Particle == '月') {
        time = '20' + this.BaseTime.split('月')[0] + '-01'
      }
      // this.Particle == '日' || this.Particle == '周'
      if (this.Particle == '日') {
        time = '20' + this.BaseTime
      }
      if (this.Particle == '周') {
        time = '20' + this.BaseTime
      }
      if (this.Particle == '小时') {
        time = this.BaseTime2 + ' ' + this.BaseTime
      }


      const h = this.$createElement;
      // 时间清空后的处理
      if (this.withdrawTime && this.withdrawTime.length > 0) {
        this.startTime = this.withdrawTime[0]
        this.endTime = this.withdrawTime[1]
      } else {
        this.startTime = ''
        this.endTime = ''
      }
      let params =
      {
        "simLevel": this.Order.split('-')[0],
        "position": this.Order,
        "chktime": this.value1 == '' ? time : this.value1,
        "TimeDimension": this.Particle,
        "TeamCode": this.simlevel,
        "Mark": this.materialName == '' ? this.barName : this.materialName,
        "Shift": this.radio
      }
      const res = await getDodown(params);
      let msg1 = res.msg
      if (res.success) {
        // this.dataTagDat = res.response.dataTag || []
        // this.dataKpiDat = res.response.dataKpi || []
        this.dataKpiDat = res.response
      } else {
        this.$notify({
          title: '提示',
          message: h('i', { style: 'color: #000000' }, msg1)
        });
      }
    },
    async searchSql() {
      const h = this.$createElement;
      // 时间清空后的处理
      if (this.withdrawTime && this.withdrawTime.length > 0) {
        this.startTime = this.withdrawTime[0]
        this.endTime = this.withdrawTime[1]
      } else {
        this.startTime = ''
        this.endTime = ''
      }
      let params =
      {
        "simLevel": this.$route.path == '/simManagement/simSpot' ? 'SIM2' : this.Order.split('-')[0],
        "position": [
          this.Order + '-1'
        ],
        "paramList": [
          this.simlevel,
          this.BaseTime
        ],
        "TimeDimension": this.Particle
      }
      let res = await getTableList(params)
      let msg1 = res.msg
      if (res.success && res.response != null) {
        this.tableData1 = res.response[0].positionResult
      } else {
        this.$notify({
          title: '提示',
          message: h('i', { style: 'color: #000000' }, msg1)
        });
      }
    },
    query() {
      this.tcChart = this.$echarts.init(document.getElementById('tcChart1'));
      this.$nextTick(() => {
        var option
        option = {
          title: {
            text: this.title,
            textStyle: { // 标题样式
              color: '#000000'
            }
          },
          tooltip: {
            trigger: "item"
          },
          legend: {
            data: [
              {
                name: "目标值",
                textStyle: {
                  color: "#000000"
                }
              },
              {
                name: "实际值",
                textStyle: {
                  color: "#000000"
                }
              },
            ]
          },
          grid: {
            top: '15%',
            bottom: '1%',
            right: '2%',
            left: '5%',
            containLabel: true
          },
          toolbox: {
            show: true,
          },
          calculable: true,
          xAxis: [
            {
              type: 'category',
              axisLine: {
                lineStyle: {
                  color: "#000000"
                }
              },
              splitLine: {
                show: false
              },
              data: ['10.10', '10.11', '10.12'],//['10.10', '10.11', '10.12', '10.13', '10.14', '10.15'],
              // data: this.nianData.map(item => item.MONTH),
              axisLabel: {
                show: true,
                textStyle: {
                  color: "#000000" //X轴文字颜色
                },
              },
            }
          ],
          yAxis: [
            {
              type: 'value',
              splitLine: {
                show: false
              },
              axisLine: {
                lineStyle: {
                  color: "#000000"
                }
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: "#000000" //X轴文字颜色
                },
              },
            },
            // {
            //   //右边百分比部分
            //   name: '百分比',
            //   type: "value",
            //   position: "right",
            //   axisLine: {
            //     lineStyle: {
            //       color: "#000000"
            //     }
            //   },
            //   axisTick: {
            //     show: false,
            //   },

            //   axisLabel: {
            //     textStyle: {
            //       color: "#000000",
            //     },
            //     show: true,
            //     interval: "auto",
            //     formatter: "{value}%",
            //   },
            //   show: true,
            //   splitLine: {  //网格线
            //     show: false
            //   }
            // }
          ],
          series: [

            {
              name: '目标值',
              tooltip: {
                show: false
              },
              type: 'bar',
              barWidth: 10,
              itemStyle: {
                normal: {
                  barBorderRadius: [4, 4, 4, 4],
                  color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: "#6B74E4" // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: "#6B74E4" // 100% 处的颜色
                  }], false)
                }
              },
              // data: this.nianData.map(item => item.PLANVALUE),
              data: [10, 20, 30, 40, 50, 60],
              barGap: '30%'

            },
            {
              name: '实际值',
              tooltip: {
                show: false
              },
              type: 'bar',
              barWidth: 10,
              itemStyle: {
                normal: {
                  barBorderRadius: [4, 4, 4, 4],
                  color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0,
                    color: "#4391F4" // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: "#4391F4" // 100% 处的颜色
                  }], false)
                }
              },
              // data: this.nianData.map(item => item.OUTNUM),
              data: [5, 20, 10, 18, 20, 20],
              barGap: '30%'
            },
          ]
        };
        this.tcChart.setOption(option, true);
        window.addEventListener("resize", () => {
          this.tcChart.resize()
        }, false);
      })
    },
  }

}
</script>
<style scoped>
.titName {
    width: 160px;
    height: 10%;
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    text-align: center;
    cursor: pointer;
    margin-right: 20px;
}
.active {
    color: #409eff;
    font-weight: bold;
    border-right: 2px solid #3b9af8;
    box-shadow: 0px 0px 10px #fff;
    border-radius: 5px;
    font-size: 22px;
}
/deep/ .el-dialog {
    background: var(--background) no-repeat 0 0;
    background-size: 100% 100% !important;
    overflow: hidden;
}
.styleTable /deep/.el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}
.styleTable /deep/ .el-table,
.el-table__expanded-cell {
    background-color: transparent !important;
}

.styleTable /deep/ .el-table tr {
    background-color: transparent !important;
    border: none;
}
.styleTable /deep/ .el-table--enable-row-transition .el-table__body td,
.el-table .cell {
    background-color: transparent !important;
}
.styleTable /deep/ .el-table th.el-table__cell {
    background-color: transparent !important;
    color: #fff;
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
    width: 15px;
    height: 0px;
}
/deep/ .el-dialog__title {
    color: #fff !important;
}

/deep/ .el-table tbody tr {
    background-color: transparent !important;
}
/deep/ .el-table__expand-icon--expanded {
    color: #fff !important;
}
/deep/ .el-table__body tr:hover > td {
    background-color: inherit !important;
}
/deep/ .el-table__expand-icon {
    color: #fff !important;
}
/deep/ .el-radio__label {
    font-size: 16px !important;
}
/deep/ .el-table__header-wrapper {
    /* background-color: #4391f4 !important; */
    color: #409eff !important;
}
</style>
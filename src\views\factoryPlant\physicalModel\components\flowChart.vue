// 
<template>
    <div class="process-route-view">
        <div class="process-route-info">
            <span class="process-route-info-title">{{ $t('DFM_WLMX._WLMX') }}</span>
            <v-btn
                v-for="(item, k) in dataList"
                :key="k"
                class="process-route-info-item rounded-0 justify-start pl-6"
                :class="params.routingId === item.id ? 'active-process-route' : ''"
                @click="selectProcessRoute(item)"
            >
                <span :title="item.Remark || item.name" class="text-ellipsis">{{ item.Remark || item.name }}</span>
            </v-btn>
        </div>
        <div class="process-route-main overflow-auto">
            <v-card outlined>
                <div class="super-flow-title">
                    <!-- 流程图 -->
                    <div>
                        <span>主流程图</span>
                        <span v-if="operaObj.PName"> - {{ operaObj.PName }}</span>
                    </div>
                    <div>
                        <!-- 切换 -->
                        <v-btn text color="primary" @click="changeView">切换视图</v-btn>
                        <!-- 保存 -->
                        <v-btn text color="primary" @click="operaClick('save')">{{ $t('GLOBAL._BC') }}</v-btn>
                        <!-- 重置 -->
                        <v-btn text color="primary" @click="operaClick()">{{ $t('GLOBAL._CZ') }}</v-btn>
                    </div>
                </div>
                <div class="process-route-main-flow">
                    <VueSuperFlow
                        :showMenuList="showMenuList"
                        ref="vueSuperFlow"
                        :isGetId="false"
                        :modeList="primaryModeList"
                        :node-list="nodeList"
                        :link-list="linkList"
                        @fromVueSuperFlow="getVueSuperFlow"
                    />
                </div>
                <div class="process-route-second-flow">
                    <v-tabs class="pa-0 ma-0" v-if="operaObj.CName">
                        <v-tab>子流程图
                            <span> - {{ operaObj.CName }}</span>
                        </v-tab>
                    </v-tabs>
                    <v-tabs class="pa-0 ma-0" v-else>
                        <v-tab>子流程图</v-tab>
                    </v-tabs>
                    <VueSuperFlow
                        ref="secondSuperFlow"
                        :isGetId="false"
                        :showMenuList="showSecondMenu"
                        :modeList="secondModeList"
                        :node-list="secondNodeList"
                        :link-list="sencondLinkList"
                        @fromVueSuperFlow="getVueSuperFlow"
                    />
                </div>
            </v-card>
        </div>
    </div>
</template>
<script>
import { EquipLinkSaveAll, EquipLinkGetGraphList } from '@/api/factoryPlant/physicalModel.js';
export default {
    name: 'ProcessRoute',
    components: {
        VueSuperFlow: () => import('@/components/vue_super_flow/index.vue')
    },
    props: {
        treeData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            dataList: [],
            activeNum: 1,
            operaObj: { PName: '', CName: ''},
            // 初始化的父级node节点
            originNodeList: [],
            nodeList: [],
            // 初始化的父级连线
            originLinkList: [],
            linkList: [],
            // 初始化的子级node节点
            originSecondNode: [],
            secondNodeList: [],
            // 初始化的子级连线
            sencondLinkList: [],
            originSecondLink: [],
            // 模板样式
            nodeItemList: {
                label: 'start',
                value: () => ({
                    width: 90,
                    height: 30,
                    meta: {
                        label: 'start',
                        name: 'start',
                        type: 'start'
                    }
                })
            },
            // 列表
            primaryModeList: [],
            secondModeList: [],
            sendSecondModelList: [],
            showMenuList: ['addChild', 'delete'],
            showSecondMenu: ['delete'],
            params: { routingId: '', secondId: '' }
        };
    },
    created() {
        this.initData();
    },
    mounted() {},
    methods: {
        // 切换视图
        changeView() {
            this.$emit('changeView');
        },
        async initData() {
            this.dataList = this.treeData[0].children;
            this.selectProcessRoute(this.dataList[0]);
        },
        // 点击产线
        selectProcessRoute(i) {
            const { id, name } = i;
            this.getModelList(i.children, 'primaryModeList');
            this.secondModeList = [];
            this.nodeList = [];
            this.linkList = [];
            this.params.routingId = id;
            this.params.secondId = '';
            this.originSecondNode = [];
            this.secondNodeList = [];
            this.sencondLinkList = [];
            this.originSecondLink = [];
            this.operaObj.PName= name;
            this.operaObj.CName= '';
            this.getGraphList(1);
        },
        // 获取模块列表
        async getModelList(arr1, str) {
            const arr2 = [];
            arr1.forEach(e => {
                const { label } = this.nodeItemList;
                const { name, value, parentId, id, children, remark } = e;
                const arr = name?.split(' ') || [];
                const a = str=='primaryModeList'?remark || arr[1] || arr[0] : arr[1] || arr[0] || remark;
                e = {
                    label: a,
                    value: () => ({
                        width: 90,
                        height: 30,
                        type: label,
                        id,
                        meta: {
                            parentId,
                            id,
                            label: a,
                            name: a,
                            value,
                            type: label,
                            children
                        }
                    })
                };
                arr2.push(e);
                this[str] = arr2;
            });
        },
        // 获取产线、工段详情数据
        async getGraphList(n) {
            const { routingId, secondId } = this.params;
            const res = await EquipLinkGetGraphList(n == 1 ? routingId : secondId);
            const arr1 = this.dataList.find(i => i.id == routingId);
            const { success, response } = res || {};
            if (success && response) {
                const { linkList, nodeList } = response;
                if (n == 2) {
                    this.originSecondNode = [];
                    this.secondNodeList = [];
                    this.sencondLinkList = [];
                    this.originSecondLink = [];
                } else {
                    this.linkList = [];
                    this.nodeList = [];
                    this.originNodeList = [];
                    this.originLinkList = [];
                }
                nodeList?.forEach(e => {
                    const { Coordinate, EquipmentName, ID, ParentId, Width, Height, Remark } = e;
                    const arr = EquipmentName?.split(' ') || [];
                    const a = n==1?Remark || arr[1] || arr[0] : arr[1] || arr[0] || Remark;
                    const o = arr1.children.find(i => i.id == ID);
                    const { children } = o || {};
                    const obj = {
                        id: ID,
                        coordinate: Coordinate.split(',').map(Number),
                        width: Width,
                        height: Height,
                        meta: {
                            ParentId,
                            id: ID,
                            label: a,
                            name: a,
                            type: 'start',
                            children
                        }
                    };
                    if (n == 2) {
                        this.originSecondNode.push(obj);
                        this.secondNodeList.push(obj);
                    } else {
                        this.nodeList.push(obj);
                        this.originNodeList.push(obj);
                    }
                });
                linkList?.forEach(e => {
                    const { StartId, EndId, EndAt, StartAt, ID } = e;
                    const obj = {
                        id: ID,
                        startAt: StartAt.split(',').map(Number),
                        startId: StartId,
                        endAt: EndAt.split(',').map(Number),
                        endId: EndId,
                        meta: null
                    };
                    if (n == 2) {
                        this.sencondLinkList.push(obj);
                        this.originSecondLink.push(obj);
                    } else {
                        this.linkList.push(obj);
                        this.originLinkList.push(obj);
                    }
                });
            }
        },
        // 流程图子组件回传数据
        getVueSuperFlow(type, node, link, t) {
            const { meta, id } = node || {}; // 打开子流程和保存操作的node不一样
            switch (type) {
                case 'save':
                    this.saveFlowData(node, link, t);
                    break;

                case 'delete':
                    this.sendSecondModelList = [];
                    this.secondNodeList = [];
                    this.originSecondLink = [];
                    this.sencondLinkList = [];
                    this.originSecondNode = [];
                    this.operaObj.CName = '';
                    break;

                case 'addChild':
                    this.secondModeList = [];
                    this.secondNodeList = [];
                    this.sencondLinkList = [];
                    this.params.secondId = id;
                    this.operaObj.CName = meta.name
                    this.getModelList(meta.children, 'secondModeList');
                    this.getGraphList(2);
                    break;

                default:
                    break;
            }
        },

        // 保存流程图
        async saveFlowData(node, link, t) {
            const nodeList = [],
                linkList = [];
            const { routingId, secondId } = this.params;
            let obj = {};
            node.forEach(e => {
                const { coordinate, meta, height, width } = e || {};
                const { id } = meta || {};
                obj = { EquipId: id, GroupId: t == 1 ? routingId : secondId, Coordinate: coordinate.join(','), Width: width, Height: height };
                nodeList.push(obj);
            });
            link.forEach(e => {
                const { endAt, startAt, id, endId, startId } = e || {};
                obj = { EquipId: id, EndAt: endAt.join(','), StartAt: startAt.join(','), GroupId: t == 1 ? routingId : secondId, StartId: startId, EndId: endId };
                linkList.push(obj);
            });
            obj = { GroupId: t == 1 ? routingId : secondId, nodeList, linkList };
            const res = await EquipLinkSaveAll({ ...obj });
            const { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '操作成功', color: 'success' });
                // this.getGraphList(t);
            }
        },

        // 保存/重置
        operaClick(type) {
            switch (type) {
                case 'save':
                    this.$refs.vueSuperFlow.saveFlow();
                    this.$refs.secondSuperFlow.saveFlow();
                    break;

                default:
                    this.nodeList = [];
                    this.originNodeList.forEach(e => {
                        this.nodeList.push(e);
                    });
                    this.linkList = [];
                    this.originLinkList.forEach(e => {
                        this.linkList.push(e);
                    });
                    this.secondNodeList = [];
                    this.originSecondNode.forEach(e => {
                        this.secondNodeList.push(e);
                    });
                    this.sencondLinkList = [];
                    this.originSecondLink.forEach(e => {
                        this.sencondLinkList.push(e);
                    });
                    this.secondModeList = [];
                    this.operaObj.CName= '';
                    break;
            }
        }
    }
};
</script>
<style lang="scss">
.v-dialog:not(.v-dialog--fullscreen) {
    max-height: 100% !important;
}
.process-route-second-flow {
    .v-tabs-bar {
        height: 42px !important;
    }
}
.process-route-content {
    .v-tabs-bar {
        height: 42px !important;
    }
}
</style>
<style lang="scss" scoped>
.v-dialog:not(.v-dialog--fullscreen) {
    max-height: 100% !important;
}
.process-route-view {
    display: flex;
    width: 100%;
    .process-route-info {
        min-height: calc(100vh - 72px);
        width: 260px;
        margin-right: 12px;
        font-size: 0.8125rem;
        font-weight: 500;
        line-height: 36px;
        border: solid 1px #ddd;
        border-radius: 3px;
        display: flex;
        flex-direction: column;
        .process-route-info-title {
            width: 100%;
            font-weight: 700;
            border-bottom: 1px solid #ddd;
            padding-left: 10px;
            .v-icon {
                font-size: 16px;
            }
        }
        .process-route-info-item {
            width: 100%;
            border-bottom: 1px solid #ddd;
            text-align: left;
            // position: relative;
            .process-route-minus {
                position: absolute;
                right: 5px;
                button {
                    margin-right: 2px;
                    font-size: 14px;
                    background: rgb(61 205 88 / 0.7);
                    color: white;
                    padding: 2px;
                }
            }
            .v-btn__content span {
                width: 240px;
            }
        }
        .active-process-route {
            background: #bdbdbd;
        }
    }
    .process-route-main {
        flex: 1;
        width: 100%;
        .super-flow-title {
            line-height: 40px;
        }
        .process-route-main-flow {
            height: 30vh;
            .process-route-content {
                border-top: 1px solid gainsboro;
                // div{
                //     cursor: pointer;
                //     word-wrap: break-word;
                //     word-break: normal;
                //     border: 1px solid gainsboro;
                //     display: flex;
                //     align-items: center;
                //     justify-content: center;
                //     height: 40px;
                //     width: 120px;
                // }
            }
        }
        .process-route-second-flow {
            height: 30vh;
        }
    }
}
</style>

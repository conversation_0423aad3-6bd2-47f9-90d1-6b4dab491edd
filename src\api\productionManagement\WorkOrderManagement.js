import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_TRACE; // 配置服务url
//获取工单列表
export function traceGetPageList(data) {
    return request({
        url: baseURL + '/trace/Wo/GetPageList',
        method: 'post',
        data
    });
}

//获取详情列表
export function BatchGetPageList(data) {
    return request({
        url: baseURL + '/trace/BatchFlowRecord/GetWoPageList',
        method: 'post',
        data
    });
}
// 修改详情数量
export function updateBatchQuantity(data) {
    return request({
        url: baseURL + '/trace/Batch/UpdateLastBatchQty',
        method: 'post',
        data
    });
}

//更改工单状态
export function UpdateWOStatus(data) {
    return request({
        url: baseURL + '/trace/Wo/UpdateWOStatus',
        method: 'post',
        data
    });
}

//获取工单
export function GetSerialPCNumber(data) {
    return request({
        url: baseURL + '/trace/Batch/CreateBatchNo',
        method: 'post',
        data
    });
}

//保存批次/打印
export function traceSaveForm(data) {
    return request({
        url: baseURL + '/trace/Batch/SaveForm',
        method: 'post',
        data
    });
}
//删除
export function WoDelete(data) {
    return request({
        url: baseURL + '/trace/Wo/Delete',
        method: 'post',
        data
    });
}


//开工检查
export function ToStartChecking(data) {
    return request({
        url: baseURL + '/trace/Wo/ToStartChecking',
        method: 'post',
        data
    });
}

//消耗列表查询
export function GetWoConfirmList(data) {
    return request({
        url: baseURL + '/trace/WoMaterialConsumption/GetWoConfirmList',
        method: 'post',
        data
    });
}
//工单结单提交
export function SendWoConfirmList(data) {
    return request({
        url: baseURL + '/trace/WoMaterialConsumption/SendWoConfirmList',
        method: 'post',
        data
    });
}
//不良录入
export function WoBadRecordSaveData(data) {
    return request({
        url: baseURL + '/trace/WoBadRecord/SaveData',
        method: 'post',
        data
    });
}

//工单报工
export function SapSendSapWorkReport(data) {
    return request({
        url: baseURL + '/trace/Sap/SendSapWorkReport',
        method: 'post',
        data
    });
}

//新增
export function WoMaterialConsumptionSaveForm(data) {
    return request({
        url: baseURL + '/trace/WoMaterialConsumption/SaveDetail',
        method: 'post',
        data
    });
}

//物料列表
export function GetWoNeedMaterialList(data) {
    return request({
        url: baseURL + '/trace/WoMaterialConsumption/GetWoNeedMaterialList',
        method: 'post',
        data
    });
}
// 查询不良原因
export function getReasonList(data) {
    return request({
        url: baseURL + '/trace/Common/GetReasonList',
        method: 'post',
        data
    })
}
// 返工工单&SN 绑定
export function doBindReworkSN(data) {
    return request({
        url: baseURL + '/trace/WoSn/BindReWorkWoSn',
        method: 'post',
        data
    })
}
// 查询返工工单绑定SN 记录列表(分页)
export function getReworkBindSnList(data) {
    return request({
        url: baseURL + '/trace/WoSn/GetWoSnPageList',
        method: 'post',
        data
    })
}
// 查询物料消耗明细列表
export function getMaterialExpendDetailList(data) {
    return request({
        url: baseURL + '/trace/WoMaterialConsumption/GetDetailsPageList',
        method: 'post',
        data
    })
}
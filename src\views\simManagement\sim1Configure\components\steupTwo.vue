<template>
  <div>
    <v-form ref="form">
      <v-row class="ma-4">
        <span style="color: red;">*</span>
        <v-col v-if="Moudelname == 'SIM1' || Moudelname == 'SIM1.5'">
          <el-input
            v-model="form.selectName"
            disabled
            placeholder="请选择成本中心"
            style="margin-bottom: 10px;"
          ></el-input>
          <el-input
            v-model="filterText"
            placeholder="输入过滤关键词"
          ></el-input>
          <el-tree
            class="filter-tree"
            :data="formattedTreeData"
            :props="defaultProps"
            :filter-node-method="filterNode"
            ref="tree"
            @node-click="handleCheck"
          ></el-tree>
        </v-col>
        <v-col v-if="Moudelname == 'SIM2'">
          <!-- <Treeselect
            v-model="form.PresentProdProcessId"
            placeholder="所属部门"
            noChildrenText="暂无数据"
            noOptionsText="暂无数据"
            :default-expand-level="4"
            :normalizer="normalizer"
            :options="EquipmentProductLineTree"
            disableBranchNodes
          /> -->
          <el-input
            v-model="form.selectName"
            disabled
            placeholder="请选择部门"
            style="margin-bottom: 10px;"
          ></el-input>
          <el-input
            v-model="filterText"
            placeholder="输入过滤关键词"
          ></el-input>
          <el-tree
            class="filter-tree"
            :data="EquipmentProductLineTree"
            :props="defaultProps"
            :filter-node-method="filterNode"
            ref="tree"
            @node-click="handleCheck"
          ></el-tree>
        </v-col>
      </v-row>
    </v-form>
  </div>
</template>
<script>
import { getEditEntityByCodeone, getTree, getImageUel } from '@/api/simConfig/simconfignew.js';
import { mapGetters } from 'vuex';

export default {
  props: {
    TwoId: {
      type: String,
      default: ''
    },
    dialogType: {
      type: String,
      default: ''
    },
    oneList1: {
      type: Object,
      default: () => { }
    },
    Moudelname: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      normalizer(node) {
        return {
          id: node.id,
          label: node.name,
          children: node.children
        };
      },
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      treeData: [],
      form: {
        selectName: '',
        selectCode: '',
        selectCode1: '',
        PresentProdProcessId: ''
      },
      // selectName: '',
      // selectCode: '',
      filterText: '',
      rules: {
        Olinetit: [v => !!v || this.$t('GLOBAL.Olinetit')],
      },
    };
  },
  computed: {
    ...mapGetters(['getUserinfolist']),
    curAuth() {
      return this.$store.getters.getUserinfolist[0]
    },
    EquipmentProductLineTree() {
      return this.$store.getters.EquipmentProductLineTree
    },
    formattedTreeData() {
      function formatNode(node) {
        const formattedNode = {
          ...node,
          label: node.name
        };
        if (node.children && node.children.length > 0) {
          formattedNode.children = node.children.map(child => formatNode(child));
        }
        return formattedNode;
      }
      return this.treeData.map(node => formatNode(node));
    },
  },
  created() {
    // this.getTreeList()
    this.$store.dispatch('getEquipmentTeamTree', "Team");
    this.form.PresentProdProcessId = this.getUserinfolist[0].Departmentid;
    this.form.selectCode = this.oneList1?.Simlevel
  },
  mounted() {

  },
  methods: {
    async queryEditListone() {
      this.deailFileList = []
      const params = {
        code: this.TwoId
      }
      const res = await getEditEntityByCodeone(params);
      if (res.success) {
        this.form.selectCode = res.response.Simlevel
        this.form.selectName = res.response.Simlevel
      }
    },
    async getTreeList() {
      const res = await getTree();
      if (res.success) {
        this.treeData = res.response
      }
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleCheck(data) {
      this.form.selectName = data.name
      this.form.selectCode = data.id
      // this.form.selectCode1 = data.id
      // this.oneList1.Simlevel = data.id
    },
    save() {
      this.getTreeList()
      // this.form.selectCode = this.oneList1.Simlevel
      this.form.selectName = this.oneList1.Simlevel
      // this.form.selectCode = this.form.selectCode1
      // this.$emit('savesteupTwo', this.form.selectCode)
      if (this.dialogType == 'edit') {
        this.$emit('savesteupTwo', this.form.selectCode)
      } else {
        this.$emit('savesteupTwo', this.form.selectCode)
      }
    },
    save1() {
      this.form.selectName = ''
      this.form.selectCode = ''
    }
  },
}
</script>
<style lang="scss" scoped>
</style>
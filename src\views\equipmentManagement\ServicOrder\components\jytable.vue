<template>
    <div>
        <div class="form-btn-list">
            <v-btn color="primary" @click="add()">{{ $t('GLOBAL._XZ') }}</v-btn>
            <v-btn color="primary" :disabled="!deleteList.length" @click="deltable()">{{ $t('GLOBAL._PLSC') }}</v-btn>
        </div>
        <Tables
            :page-options="pageOptions"
            ref="recordTable"
            :footer="false"
            :loading="loading"
            :btn-list="btnList"
            @tableClick="tableClick"
            :tableHeight="tableHeight"
            table-name="TPM_SBGL_SBWXGD"
            :headers="fwcgdownColum"
            :desserts="desserts"
            @itemSelected="SelectedItems"
            @toggleSelectAll="SelectedItems"
        ></Tables>
        <el-dialog :title="dialogType == 'add' ? $t('GLOBAL._XZ') : $t('GLOBAL._BJ')" :visible.sync="addModel" width="30%">
            <div class="addForm" v-for="(item, index) in SaveFormList" :key="index">
                <v-text-field v-if="item.type == 'input'" :id="item.id + 'SbxxList'" v-model="item.value" outlined dense :label="item.label"></v-text-field>
                <v-autocomplete
                    v-if="item.type == 'select'"
                    clearable
                    v-model="item.value"
                    :items="item.option"
                    item-text="ItemName"
                    item-value="ItemValue"
                    :label="item.label"
                    clear
                    dense
                    outlined
                ></v-autocomplete>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addModel = false">取 消</el-button>
                <el-button type="primary" @click="SaveFormSave()">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { GetRepairAcceptanceItemGetPageList, GetRepairAcceptanceItemSaveForm, GetRepairAcceptanceItemDelete } from '@/api/equipmentManagement/ServicOrder.js';
import { fwcgdownColum } from '@/columns/equipmentManagement/Repair.js';
import { Message, MessageBox } from 'element-ui';

export default {
    props: {
        tableHeight:{
            type: String,
            default: 'calc(100vh - 220px) !important'
        },
        SupplierServiceId: {
            type: String,
            default: ''
        }
    },
    components: {},
    data() {
        return {
            fwcgdownColum,
            loading: false,
            desserts: [],
            tableItem: {},
            deleteList: [],
            addModel: false,
            dialogType: '',
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            SaveFormList: [
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBWXGD._JYXM') + ' *',
                    type: 'input',
                    id: 'AcceptanceItem',
                    value: ''
                },
                {
                    require: true,
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBWXGD._SFHG') + ' *',
                    type: 'select',
                    id: 'Result',
                    option: [
                        {
                            ItemName: '是',
                            ItemValue: '是'
                        },
                        {
                            ItemName: '否',
                            ItemValue: '否'
                        }
                    ],
                    value: ''
                },
                {
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBWXGD._BHGLY'),
                    type: 'input',
                    id: 'FailDesc',
                    value: ''
                }
            ]
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: ''
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: ''
                }
            ];
        }
    },
    created() {},
    methods: {
        async SaveFormSave() {
            let flag = this.SaveFormList.some(item => {
                if (item.require) {
                    return item.value == '' || item.value == null;
                }
            });
            if (flag) {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            let obj = {};
            this.SaveFormList.forEach(item => {
                obj[item.id] = item.value;
            });
            obj.SupplierServiceId = this.SupplierServiceId;
            if (this.dialogType == 'edit') {
                obj.ID = this.tableItem.ID;
            }
            let res = await GetRepairAcceptanceItemSaveForm(obj);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.getData();
                this.addModel = false;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.SaveFormList.forEach(item => {
                        for (let k in this.tableItem) {
                            if (item.id == k) {
                                item.value = this.tableItem[k];
                            }
                        }
                    });
                    this.addModel = true;
                    return;
                case 'delete':
                    this.deltable(type);
                    return;
            }
        },

        add() {
            this.SaveFormList.forEach(item => {
                item.value = '';
            });
            this.dialogType = 'add';
            this.addModel = true;
        },
        // 删除列表
        SelectedItems(item) {
            this.deleteList = [...item];
        },
        async deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                let list = this.deleteList;
                list.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await GetRepairAcceptanceItemDelete(params);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.getData();
                    }
                    this.tableItem = {};
                })
                .catch(err => {
                    console.log(err);
                });
        },
        async getData() {
            console.log(1)
            let params = {
                SupplierServiceId: this.SupplierServiceId
            };
            this.loading = true;
            const res = await GetRepairAcceptanceItemGetPageList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = response || {} || [];
            }
        }
    }
};
</script>

<style></style>

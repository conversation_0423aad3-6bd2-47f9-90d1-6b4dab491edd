<template>
  <el-dialog :title="dialogForm.ID ? '编辑' : '新增'" :visible.sync="dialogVisible" width="600px"
    :close-on-click-modal="false" append-to-body :modal-append-to-body="false" :close-on-press-escape="false"
    @close="dialogVisible = false">
    <el-form ref="dialogForm" :model="dialogForm" label-width="120px">
      <el-form-item label="Material Type">
        <el-select style="width: 100%" v-model="mappingType" clearable placeholder="请选择类型">
          <el-option v-for="(item, index) in materialTypeOptions" :key="index" :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="mappingType === 'Material Group'" label="Material Group" prop="GroupId">
        <el-select style="width: 100%" v-model="dialogForm.GroupId" clearable placeholder="请选择">
          <el-option v-for="(item, index) in materialGroupList" :key="index" :label="item.MaterialGroupName"
            :value="item.ID">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="mappingType === 'Material'" label="Material Class" prop="MaterialId">
        <div>
          <el-button icon="el-icon-plus" type="text" @click="openMaterialTable">{{ $t('GLOBAL._CX') }}</el-button>
        </div>
        <div v-if="matInfo&&matInfo.NAME">
          {{ matInfo.NAME }}&nbsp; &nbsp; {{ matInfo.Code }}
        </div>
<!--        <el-select filterable style="width: 100%" v-model="dialogForm.MaterialId" clearable placeholder="请选择">-->
<!--          <el-option v-for="(item, index) in materialList" :key="index" :label="item.NAME +'-'+ item.Code" :value="item.ID">-->
<!--          </el-option>-->
<!--        </el-select>-->
      </el-form-item>
      <el-form-item label="Type" prop="Type">
        <el-select style="width: 100%" v-model="dialogForm.Type" placeholder="请选择">
          <el-option v-for="(item, index) in includeTypeOptions" :key="index" :label="item.ItemName" :value="item.ItemValue">
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="Status" prop="Status">
        <el-select style="width: 100%" v-model="dialogForm.Status" placeholder="请选择">
          <el-option v-for="(item, index) in statusOptions" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item> -->
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogVisible = false">取 消</el-button>
      <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small"
        @click="submit()">确定
      </el-button>
    </div>
    <material-table :is-id="false" ref="materialTable" @saveForm="getMaterial"></material-table>
  </el-dialog>
</template>
<script>
import {
  getMaterial,
  getMaterialGroup,
} from '@/api/productionManagement/Formula';
import { materialMappingSaveForm, getDataDictionary } from '../service'
import MaterialTable from '@/components/MaterialTable.vue';
export default {
  components:{
    MaterialTable
  },
  data() {
    return {
      mappingType: '',
      dialogForm: {
      },
      dialogVisible: false,
      formLoading: false,
      materialGroupList: [],
      materialList: [],
      statusOptions: [{
        label: 'Active',
        value: 'Active'
      }, {
        label: 'InActive',
        value: 'InActive'
      }],
      materialTypeOptions:[{
        label: 'Material',
        value: 'Material'
      },{
        label: 'Material Group',
        value: 'Material Group'
      }],
      includeTypeOptions:[],
      matInfo:{}
    }
  },
  mounted() {
  },
  methods: {
    submit() {
      if (this.mappingType === 'Material' && this.dialogForm.GroupId !== void(0)) {
       delete this.dialogForm.GroupId
      }
      if (this.mappingType === 'Material Group' && this.dialogForm.MaterialId !== void(0)) {
       delete this.dialogForm.MaterialId
      }
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          materialMappingSaveForm(this.dialogForm).then(res => {
            this.$message.success(res.msg)
            this.$emit('saveForm')
            this.dialogVisible = false
          })
        }
      });
    },
    show(data, EquipmentId) {
      this.mappingType = data.MappingType === void(0) ? '' : data.MappingType
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.getIncludeType()
        this.getMaterialGroup()
        // this.getMaterial()
        this.dialogForm = {
          EquipmentId,
          ...data
        }
        this.matInfo =  {...data,NAME:data.Description}
        console.log(this.dialogForm);
        this.$refs.dialogForm.resetFields()
      })
    },
    async getIncludeType(){
      const { response } = await getDataDictionary({
        itemCode: 'StorageMaterialIncludeType',
        pageIndex: 1,
        pageSize: 1000
      })
      this.includeTypeOptions = response.data
    },
    getMaterialGroup() {
      getMaterialGroup().then(res => {
        this.materialGroupList = res.response
      })
    },
    // getMaterial() {
    //   getMaterial().then(res => {
    //     this.materialList = res.response
    //   })
    // },
    getMaterial(val){
      this.dialogForm.MaterialId = val.ID
      this.matInfo = val
      console.log(val)
    },
    openMaterialTable(){
      this.$refs['materialTable'].show()
    }
  }
}
</script>
<style lang="scss" scoped></style>

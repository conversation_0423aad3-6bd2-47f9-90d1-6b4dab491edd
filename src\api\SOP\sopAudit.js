import request from '@/util/request'
import { configUrl } from '@/config'
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM

/**
 * 文档变更审计表分页查询
 * @param {查询条件} data
 */
export function getSopAuditList(data) {
    return request({
        url: baseURL + '/api/SopAudit/GetPageList',
        method: 'post',
        data
    })
}

/**
 * 保存文档变更审计表
 * @param data
 */
export function saveSopAuditForm(data) {
    return request({
        url: baseURL + '/api/SopAudit/SaveForm',
        method: 'post',
        data
    })
}

/**
 * 获取文档变更审计表详情
 * @param {Id}
 */
export function getSopAuditDetail(id) {
    return request({
        url: baseURL + '/api/SopAudit/GetEntity',
        method: 'post',
        data: id
    })
}

/**
 * 删除文档变更审计表
 * @param {主键} data
 */
export function delSopAudit(data) {
    return request({
        url: baseURL + '/api/SopAudit/Delete',
        method: 'post',
        data
    })
}

import i18n from '@/plugins/i18n';

// SOP审核页面列配置 - 包含与SOP文件管理一致的文档信息栏位加上操作栏
export const sopAuditColumn = [
    { text: () => i18n.t('SOP.DocName'), value: 'DocName', width: '300px' },
    { text: () => i18n.t('SOP.DocCode'), value: 'DocCode', width: '160px' },
    { text: () => i18n.t('SOP.DocVersion'), value: 'DocVersion', width: '120px' },
    { text: () => i18n.t('SOP.FilePath'), value: 'FilePath', width: '200px' },
    { text: () => i18n.t('SOP.FileSize'), value: 'FileSize', width: '120px' },
    { text: () => i18n.t('SOP.DocStatus'), value: 'DocStatus', width: '100px' },
    // { text: () => i18n.t('SOP.OperationType'), value: 'OperationType', width: '120px' },
    { text: () => i18n.t('SOP.OperatorId'), value: 'OperatorId', width: '120px' },
    { text: () => i18n.t('SOP.OperateTime'), value: 'OperateTime', width: '160px' },
    { text: () => i18n.t('SOP.AuditComment'), value: 'AuditComment', width: '120px' },
    // { text: () => i18n.t('SOP.AuditResult'), value: 'AuditResult', width: '120px' }
];



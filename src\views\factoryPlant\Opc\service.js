import { getRequestResources } from '@/api/fetch';
const DFM = 'baseURL_DFM'

// 查询成品料号列表
export function getOpcFunc(data) {
    const api = '/api/OpcFunction/GetPageList'
    return getRequestResources(DFM, api, 'post', data)
}
export function addServer(data) {
    const api = '/api/OpcServer/SaveForm'
    return getRequestResources(DFM, api, 'post', data)
}
export function addGroup(data) {
    const api = '/api/OpcGroup/SaveForm'
    return getRequestResources(DFM, api, 'post', data)
}
export function addTag(data) {
    const api = '/api/OpcTag/SaveForm'
    return getRequestResources(DFM, api, 'post', data)
}
export function addFunctionInstance(data) {
    const api = '/api/OpcFuncInstance/SaveForm'
    return getRequestResources(DFM, api, 'post', data)
}
export function getFunctionInstanceList(data) {
    const api = '/api/OpcFuncInstance/GetListModel'
    return getRequestResources(DFM, api, 'post', data)
}
export function getFunctionInstanceListById(data) {
    const api = '/api/OpcFuncInstance/GetListModel'
    return getRequestResources(DFM, api, 'post', data)
}
export function delFunctionInstance(data) {
    const api = '/api/OpcFuncInstance/Delete'
    return getRequestResources(DFM, api, 'post', data)
}
export function getTagAddressTree(data) {
    const api = '/api/OpcServer/GetTreeList'
    return getRequestResources(DFM, api, 'post', data)
}
export function delServer(data) {
    const api = '/api/OpcServer/Delete'
    return getRequestResources(DFM, api, 'post', data)
}
export function delGroup(data) {
    const api = '/api/OpcGroup/Delete'
    return getRequestResources(DFM, api, 'post', data)
}
export function delTag(data) {
    const api = '/api/OpcTag/Delete'
    return getRequestResources(DFM, api, 'post', data)
}
export function getServerList(data) {
    const api = '/api/OpcServer/GetList'
    return getRequestResources(DFM, api, 'post', data)
}
export function getGroupList(data) {
    const api = '/api/OpcGroup/GetList'
    return getRequestResources(DFM, api, 'post', data)
}
export function getInstanceProperty(data) {
    const api = '/api/OpcFuncPropInstance/GetList?key=' + data.key
    return getRequestResources(DFM, api, 'post', data)
}
export function getNewInstanceProperty(data) {
    const api = '/api/OpcFuncPropInstance/GetListExt'
    return getRequestResources(DFM, api, 'post', data)
}
export function addOpcFunction(data) {
    const api = '/api/OpcFunction/SaveForm'
    return getRequestResources(DFM, api, 'post', data)
}
export function delOpcFunction(data) {
    const api = '/api/OpcFunction/Delete'
    return getRequestResources(DFM, api, 'post', data)
}
export function getActionClass(data) {
    const api = '/api/OpcActionClass/GetList'
    return getRequestResources(DFM, api, 'post', data)
}
export function getFunctionNoPage(data) {
    const api = '/api/OpcFunction/GetList'
    return getRequestResources(DFM, api, 'post', data)
}
export function getFunctionProperty(data) {
    const api = '/api/OpcFuncProp/GetListModel'
    return getRequestResources(DFM, api, 'post', data)
}
export function getFunctionTrigger(data) {
    const api = '/api/OpcFuncTrigger/GetListModel'
    return getRequestResources(DFM, api, 'post', data)
}
export function getServerById(data) {
    const api = '/api/OpcServer/GetEntity/' + data.id
    return getRequestResources(DFM, api, 'get', data)
}
export function getGroupById(data) {
    const api = '/api/OpcGroup/GetEntity/' + data.id
    return getRequestResources(DFM, api, 'get', data)
}
export function getTagById(data) {
    const api = '/api/OpcTag/GetEntity/' + data.id
    return getRequestResources(DFM, api, 'get', data)
}
export function updateFunctionInstanceProperty(data) {
    const api = '/api/OpcFuncPropInstance/UpdateList'
    return getRequestResources(DFM, api, 'post', data)
}
export function updateFunctionInstance(data) {
    const api = '/api/OpcFuncInstance/UpdateList'
    return getRequestResources(DFM, api, 'post', data)
}
export function addFunctionProperty(data) {
    const api = '/api/OpcFuncProp/SaveForm'
    return getRequestResources(DFM, api, 'post', data)
}
export function delFunctionProperty(data) {
    const api = '/api/OpcFuncProp/Delete'
    return getRequestResources(DFM, api, 'post', data)
}
export function addFunctionTrigger(data) {
    const api = '/api/OpcFuncTrigger/SaveForm'
    return getRequestResources(DFM, api, 'post', data)
}
export function delFunctionTrigger(data) {
    const api = '/api/OpcFuncTrigger/Delete'
    return getRequestResources(DFM, api, 'post', data)
}
export function getOpcTag(data) {
    const api = '/api/OpcTag/GetPageList'
    return getRequestResources(DFM, api, 'post', data)
}
export function getOpcAllTag(data) {
    const api = '/api/OpcTag/GetList'
    return getRequestResources(DFM, api, 'post', data)
}

{"version": 3, "sources": ["../../src/util/mergeData.ts"], "names": [], "mappings": ";;;;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,OAAO,GAAG;AACd,EAAA,SAAS,EAAE,eADG;AAEd,EAAA,SAAS,EAAE;AAFG,CAAhB;;AAKA,SAAS,UAAT,CAAqB,KAArB,EAAkC;AAChC,MAAM,QAAQ,GAAoB,EAAlC;;AADgC,6CAGhB,KAAK,CAAC,KAAN,CAAY,OAAO,CAAC,SAApB,CAHgB;AAAA;;AAAA;AAGhC,wDAAgD;AAAA,UAArC,CAAqC;;AAAA,qBAC7B,CAAC,CAAC,KAAF,CAAQ,OAAO,CAAC,SAAhB,CAD6B;AAAA;AAAA,UACzC,GADyC;AAAA,UACpC,GADoC;;AAE9C,MAAA,GAAG,GAAG,GAAG,CAAC,IAAJ,EAAN;;AACA,UAAI,CAAC,GAAL,EAAU;AACR;AACD,OAL6C,CAM9C;;;AACA,UAAI,OAAO,GAAP,KAAe,QAAnB,EAA6B;AAC3B,QAAA,GAAG,GAAG,GAAG,CAAC,IAAJ,EAAN;AACD;;AACD,MAAA,QAAQ,CAAC,uBAAS,GAAT,CAAD,CAAR,GAA0B,GAA1B;AACD;AAd+B;AAAA;AAAA;AAAA;AAAA;;AAgBhC,SAAO,QAAP;AACD;;AAQa,SAAU,SAAV,GAAmB;AAC/B,MAAM,WAAW,GAAgC,EAAjD;AACA,MAAI,CAAC,GAAW,SAAS,CAAC,MAA1B;AACA,MAAI,IAAJ,CAH+B,CAK/B;;AACA,SAAO,CAAC,EAAR,EAAY;AACV;AACA;AACA,qCAAa,MAAM,CAAC,IAAP,CAAY,SAAS,CAAC,CAAD,CAArB,CAAb,oCAAwC;AAAnC,MAAA,IAAmC;;AACtC,cAAQ,IAAR;AACE;AACA,aAAK,OAAL;AACA,aAAK,YAAL;AACE,cAAI,SAAS,CAAC,CAAD,CAAT,CAAa,IAAb,CAAJ,EAAwB;AACtB,YAAA,WAAW,CAAC,IAAD,CAAX,GAAoB,YAAY,CAAC,WAAW,CAAC,IAAD,CAAZ,EAAoB,SAAS,CAAC,CAAD,CAAT,CAAa,IAAb,CAApB,CAAhC;AACD;;AACD;;AACF,aAAK,OAAL;AACE,cAAI,SAAS,CAAC,CAAD,CAAT,CAAa,IAAb,CAAJ,EAAwB;AACtB,YAAA,WAAW,CAAC,IAAD,CAAX,GAAoB,WAAW,CAAC,WAAW,CAAC,IAAD,CAAZ,EAAoB,SAAS,CAAC,CAAD,CAAT,CAAa,IAAb,CAApB,CAA/B;AACD;;AACD;AACF;;AACA,aAAK,aAAL;AACE,cAAI,CAAC,SAAS,CAAC,CAAD,CAAT,CAAa,IAAb,CAAL,EAAyB;AACvB;AACD;;AACD,cAAI,WAAW,CAAC,IAAD,CAAX,KAAsB,SAA1B,EAAqC;AACnC,YAAA,WAAW,CAAC,IAAD,CAAX,GAAoB,EAApB;AACD;;AACD,cAAI,WAAW,CAAC,IAAD,CAAf,EAAuB;AACrB;AACA,YAAA,WAAW,CAAC,IAAD,CAAX,IAAqB,GAArB;AACD;;AACD,UAAA,WAAW,CAAC,IAAD,CAAX,IAAqB,SAAS,CAAC,CAAD,CAAT,CAAa,IAAb,EAAmB,IAAnB,EAArB;AACA;AACF;AACA;AACA;AACA;AACA;;AACA,aAAK,IAAL;AACA,aAAK,UAAL;AACE,cAAI,SAAS,CAAC,CAAD,CAAT,CAAa,IAAb,CAAJ,EAAwB;AACtB,YAAA,WAAW,CAAC,IAAD,CAAX,GAAoB,cAAc,CAAC,WAAW,CAAC,IAAD,CAAZ,EAAoB,SAAS,CAAC,CAAD,CAAT,CAAa,IAAb,CAApB,CAAlC;AACD;;AACD;AACF;;AACA,aAAK,OAAL;AACA,aAAK,OAAL;AACA,aAAK,UAAL;AACA,aAAK,aAAL;AACA,aAAK,aAAL;AACA,aAAK,MAAL;AACA,aAAK,YAAL;AACE,cAAI,CAAC,SAAS,CAAC,CAAD,CAAT,CAAa,IAAb,CAAL,EAAyB;AACvB;AACD;;AACD,cAAI,CAAC,WAAW,CAAC,IAAD,CAAhB,EAAwB;AACtB,YAAA,WAAW,CAAC,IAAD,CAAX,GAAoB,EAApB;AACD;;AACD,UAAA,WAAW,CAAC,IAAD,CAAX,mCAAyB,SAAS,CAAC,CAAD,CAAT,CAAa,IAAb,CAAzB,GAAgD,WAAW,CAAC,IAAD,CAA3D;AACA;AACF;;AACA;AAAS;AACP,cAAI,CAAC,WAAW,CAAC,IAAD,CAAhB,EAAwB;AACtB,YAAA,WAAW,CAAC,IAAD,CAAX,GAAoB,SAAS,CAAC,CAAD,CAAT,CAAa,IAAb,CAApB;AACD;;AA1DL;AA4DD;AACF;;AAED,SAAO,WAAP;AACD;;AAEK,SAAU,WAAV,CACJ,MADI,EAEJ,MAFI,EAE0C;AAE9C,MAAI,CAAC,MAAL,EAAa,OAAO,MAAP;AACb,MAAI,CAAC,MAAL,EAAa,OAAO,MAAP;AAEb,EAAA,MAAM,GAAG,0BAAY,OAAO,MAAP,KAAkB,QAAlB,GAA6B,UAAU,CAAC,MAAD,CAAvC,GAAkD,MAA9D,CAAT;AAEA,SAAQ,MAAmB,CAAC,MAApB,CAA2B,OAAO,MAAP,KAAkB,QAAlB,GAA6B,UAAU,CAAC,MAAD,CAAvC,GAAkD,MAA7E,CAAR;AACD;;AAEK,SAAU,YAAV,CAAwB,MAAxB,EAAqC,MAArC,EAAgD;AACpD,MAAI,CAAC,MAAL,EAAa,OAAO,MAAP;AACb,MAAI,CAAC,MAAL,EAAa,OAAO,MAAP;AAEb,SAAO,MAAM,GAAG,0BAAY,MAAZ,EAAoB,MAApB,CAA2B,MAA3B,CAAH,GAAwC,MAArD;AACD;;AAEK,SAAU,cAAV,GAGL;AACC,MAAI,mDAAJ,EAAc;AACd,MAAI,mDAAJ,EAAc;AAEd,MAAM,IAAI,GAA6C,EAAvD;;AAEA,OAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,EAAjB,GAAsB;AACpB,QAAM,GAAG,GAAQ,CAAR,4BAAQ,CAAR,yBAAQ,CAAR,CAAT;;AACA,SAAK,IAAM,KAAX,IAAoB,GAApB,EAAyB;AACvB,UAAI,CAAC,GAAG,CAAC,KAAD,CAAR,EAAiB;;AAEjB,UAAI,IAAI,CAAC,KAAD,CAAR,EAAiB;AACf;AACA;AACA,QAAA,IAAI,CAAC,KAAD,CAAJ,GAAe,GAAkB,MAAlB,CAAyB,GAAG,CAAC,KAAD,CAA5B,EAAqC,IAAI,CAAC,KAAD,CAAzC,CAAf;AACD,OAJD,MAIO;AACL;AACA,QAAA,IAAI,CAAC,KAAD,CAAJ,GAAc,GAAG,CAAC,KAAD,CAAjB;AACD;AACF;AACF;;AAED,SAAO,IAAP;AACD", "sourcesContent": ["/**\n * @copyright 2017 <PERSON>\n * @license MIT\n * @see https://github.com/alexsasharegan/vue-functional-data-merge\n */\n/* eslint-disable max-statements */\nimport { VNodeData } from 'vue'\nimport { camelize, wrapInArray } from './helpers'\n\nconst pattern = {\n  styleList: /;(?![^(]*\\))/g,\n  styleProp: /:(.*)/,\n} as const\n\nfunction parseStyle (style: string) {\n  const styleMap: Dictionary<any> = {}\n\n  for (const s of style.split(pattern.styleList)) {\n    let [key, val] = s.split(pattern.styleProp)\n    key = key.trim()\n    if (!key) {\n      continue\n    }\n    // May be undefined if the `key: value` pair is incomplete.\n    if (typeof val === 'string') {\n      val = val.trim()\n    }\n    styleMap[camelize(key)] = val\n  }\n\n  return styleMap\n}\n\n/**\n * Intelligently merges data for createElement.\n * Merges arguments left to right, preferring the right argument.\n * Returns new VNodeData object.\n */\nexport default function mergeData (...vNodeData: VNodeData[]): VNodeData\nexport default function mergeData (): VNodeData {\n  const mergeTarget: VNodeData & Dictionary<any> = {}\n  let i: number = arguments.length\n  let prop: string\n\n  // Allow for variadic argument length.\n  while (i--) {\n    // Iterate through the data properties and execute merge strategies\n    // Object.keys eliminates need for hasOwnProperty call\n    for (prop of Object.keys(arguments[i])) {\n      switch (prop) {\n        // Array merge strategy (array concatenation)\n        case 'class':\n        case 'directives':\n          if (arguments[i][prop]) {\n            mergeTarget[prop] = mergeClasses(mergeTarget[prop], arguments[i][prop])\n          }\n          break\n        case 'style':\n          if (arguments[i][prop]) {\n            mergeTarget[prop] = mergeStyles(mergeTarget[prop], arguments[i][prop])\n          }\n          break\n        // Space delimited string concatenation strategy\n        case 'staticClass':\n          if (!arguments[i][prop]) {\n            break\n          }\n          if (mergeTarget[prop] === undefined) {\n            mergeTarget[prop] = ''\n          }\n          if (mergeTarget[prop]) {\n            // Not an empty string, so concatenate\n            mergeTarget[prop] += ' '\n          }\n          mergeTarget[prop] += arguments[i][prop].trim()\n          break\n        // Object, the properties of which to merge via array merge strategy (array concatenation).\n        // Callback merge strategy merges callbacks to the beginning of the array,\n        // so that the last defined callback will be invoked first.\n        // This is done since to mimic how Object.assign merging\n        // uses the last given value to assign.\n        case 'on':\n        case 'nativeOn':\n          if (arguments[i][prop]) {\n            mergeTarget[prop] = mergeListeners(mergeTarget[prop], arguments[i][prop])\n          }\n          break\n        // Object merge strategy\n        case 'attrs':\n        case 'props':\n        case 'domProps':\n        case 'scopedSlots':\n        case 'staticStyle':\n        case 'hook':\n        case 'transition':\n          if (!arguments[i][prop]) {\n            break\n          }\n          if (!mergeTarget[prop]) {\n            mergeTarget[prop] = {}\n          }\n          mergeTarget[prop] = { ...arguments[i][prop], ...mergeTarget[prop] }\n          break\n        // Reassignment strategy (no merge)\n        default: // slot, key, ref, tag, show, keepAlive\n          if (!mergeTarget[prop]) {\n            mergeTarget[prop] = arguments[i][prop]\n          }\n      }\n    }\n  }\n\n  return mergeTarget\n}\n\nexport function mergeStyles (\n  target: undefined | string | object[] | object,\n  source: undefined | string | object[] | object\n) {\n  if (!target) return source\n  if (!source) return target\n\n  target = wrapInArray(typeof target === 'string' ? parseStyle(target) : target)\n\n  return (target as object[]).concat(typeof source === 'string' ? parseStyle(source) : source)\n}\n\nexport function mergeClasses (target: any, source: any) {\n  if (!source) return target\n  if (!target) return source\n\n  return target ? wrapInArray(target).concat(source) : source\n}\n\nexport function mergeListeners (...args: [\n  { [key: string]: Function | Function[] } | undefined,\n  { [key: string]: Function | Function[] } | undefined\n]) {\n  if (!args[0]) return args[1]\n  if (!args[1]) return args[0]\n\n  const dest: { [key: string]: Function | Function[] } = {}\n\n  for (let i = 2; i--;) {\n    const arg = args[i]\n    for (const event in arg) {\n      if (!arg[event]) continue\n\n      if (dest[event]) {\n        // Merge current listeners before (because we are iterating backwards).\n        // Note that neither \"target\" or \"source\" must be altered.\n        dest[event] = ([] as Function[]).concat(arg[event], dest[event])\n      } else {\n        // Straight assign.\n        dest[event] = arg[event]\n      }\n    }\n  }\n\n  return dest\n}\n"], "sourceRoot": "", "file": "mergeData.js"}
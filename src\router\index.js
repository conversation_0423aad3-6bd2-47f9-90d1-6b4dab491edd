import Vue from 'vue';
import Router from 'vue-router';
import store from '../store/index';
import { publicRoute, protectedRoute } from './config';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { LayoutAuth, LayoutDefault, RouteWrapper } from '@/components/layouts';
import { initMenu } from './addRoter'
import { indexOf } from 'xe-utils';

// const routes = publicRoute.concat(protectedRoute);
//  处理重复Push
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location, onResolve, onReject) {
    if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject);
    return originalPush.call(this, location).catch(err => err);
};
Vue.use(Router);
const router = new Router({
    mode: 'hash',
    linkActiveClass: 'active',
    routes: publicRoute
});
// router gards
// let flag = true;
const whiteMuens = ['/dashboard', '/', '/userCneter']; //白名单
router.beforeEach((to, from, next) => {
    // to.meta.Factory = '2010';
    NProgress.start();
    const token = store.getters.getAccessToken;
    const muneslist = store.getters.getMenuList;
    if (to.name !== 'login') {
        if (token) {
            if (JSON.stringify(muneslist).includes(to.path) || whiteMuens.includes(to.path)) {
                if (store.getters.getFlag) {
                    try {
                        initMenu(router, store.getters.getMenuList);
                        store.commit('SETFLAG', false)
                        next({ ...to });
                    } catch {
                        // next({ name: 'login', query: { redirect: to.path } });
                        next({ name: 'login' });
                    }
                } else {
                    next();
                }
            } else {
                next();
                // next({ name: '404', replace: true });
            }
            // 域登录账号跳转回来的链接单独处理
        } else if (window.location.search.split('=')[0] == '?token') {
            let url = `${window.location.origin}/#/auth/login${window.location.search}`
            window.location.href = url
        } else {
            next({
                // par:外链跳转过来带的参数
                name: 'login',
                query: {
                    redirect: to.path,
                    par: JSON.stringify(to.query)
                }
            });
        }
    } else {
        next();
    }
});

router.afterEach(() => {
    NProgress.done();
});

export default router;
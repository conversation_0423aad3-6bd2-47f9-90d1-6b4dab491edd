<template>
  <div class="view-table-1-box">
    <!-- <vxe-toolbar>
      <template #buttons>
        <vxe-button size="mini" @click="add">配置</vxe-button>
      </template>
</vxe-toolbar> -->
    <div class="cloaking">
      <vxe-icon
        name="setting"
        @click="add"
      ></vxe-icon>
      <vxe-icon
        name="delete"
        @click="handleDel()"
      ></vxe-icon>
    </div>
    <vxe-table
      class="mytable-style"
      :align="allAlign"
      height="auto"
      :header-cell-class-name="headerCellClassName"
      :border=true
      :data="tableData"
    >
      <!-- <vxe-column type="seq" width="60"></vxe-column> -->
      <vxe-column
        v-for="(column, index) in columns"
        :key="index"
        :field="column"
        :title="column"
        resizable
      ></vxe-column>
      <!-- <vxe-column field="sex" title="实际"></vxe-column>
        <vxe-column field="age" title="计划"></vxe-column>
        <vxe-column field="age" title="不良数量"></vxe-column> -->
    </vxe-table>
    <!-- 新增 -->
    <v-dialog
      v-model="showAddDialog"
      scrollable
      persistent
      width="55%"
    >
      <AddDialog
        v-if="showAddDialog"
        :searchFormObj="searchFormObj"
        @closePopup="closePopup"
        :Position="Position"
        @addListFn="getdata"
      ></AddDialog>
    </v-dialog>
  </div>
</template>
<script>
import { GetKpiList, SaveConfig, GetChartStructure, delChartConfig } from '@/views/simManagement/components/chartsConfig/service.js';
import { QueryResultBySql, QuerypositionResultBySql } from '@/views/kpi/modelManagement/service.js';

export default {
  name: "ViewTable",
  props: {
    searchFormObj: {
      type: Object,
      default: () => { }
    },
    // curDay:{
    //     type: Object,
    //     default: ()=>{}
    // },
    Position: {
      type: String,
      default: ''
    },
  },
  components: {
    AddDialog: () => import('@/views/simManagement/components/viewTableAdd.vue'),
  },

  data() {
    return {
      KpiChartId: '',
      allAlign: null,
      columns: ["-", "-", '-', '-'],
      tableData: [
        // { id: 10001, name: 'Test1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc' },
        // { id: 10002, name: 'Test2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
        // { id: 10003, name: 'Test3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
        // { id: 10004, name: 'Test4', role: 'Designer', sex: 'Women', age: 24, address: 'Shanghai' },
      ],
      showAddDialog: false
    }
  },
  watch: {
    searchFormObj: {
      handler(nv) {
        this.getdata()
        // this.getSQLFn()
      },
      deep: true
    },
    Position: {
      handler(nv) {
        this.getdata()
      },
      immediate: true
    },
  },
  // watch: {
  //   'searchFormObj': {
  //     handler(nv, ov) {
  //       // this.getListFn()
  //       this.getChartConfig()
  //     },
  //     deep: true,
  //   },
  //   Position: {
  //     handler(nv) {
  //       this.getChartConfig()
  //     },
  //     immediate: true
  //   },
  //   curShift: {
  //     handler(nv) {
  //       this.initCharts()
  //     },
  //     deep: true
  //   }
  // },
  created() {
    this.getdata()
    // this.getSQLFn()
  },
  methods: {
    // 删除图表配置
    handleDel() {
      if (!this.KpiChartId) return false
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._SCTIPS'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      })
        .then(async () => {
          let resp = await delChartConfig([this.KpiChartId]);
          this.$store.commit('SHOW_SNACKBAR', { text: this.$t('GLOBAL._SCCG'), color: 'success' });
          this.getdata();
        })
        .catch(() => { });
    },
    async getdata() {
      let params = {
        simLevel: this.$route.name,
        position: [this.Position],
        paramList: [this.searchFormObj.PresentDepartmentId, this.searchFormObj.date]
      }
      let resp = await QuerypositionResultBySql({ ...params })

      if (resp.response != null && resp.response.length > 0) {
        this.$emit('tableHandle1', resp.response[0].KpiName)
        this.KpiChartId = resp.response[0].KpiChartId
        let list = resp.response[0]?.positionResult
        this.columns = Object.keys(list[0])
        this.tableData = list


      } else {
        this.$emit('tableHandle1', '')
        this.KpiChartId = ''
        this.columns = []
        this.tableData = []
      }
    },
    add() {
      this.showAddDialog = true
    },
    closePopup() {
      this.showAddDialog = false
    },
    //根据Position获取SQL
    async getSQLFn() {
      let params = {
        Position: this.Position
      }
      let { response } = await GetChartStructure(params)
      // this.id = response.ID
      //替换SQL占位符
      let curSQL = response.SqlText

      //测试用数据
      // curSQL = "select t.NAME as '班组名称', t.BOX_TEAM_CODE as 'Box班组代码', t.SHORTNAME as '简称' from DFM_M_TEAM t where t.id = '#' and t.CREATEDATE < CONVERT(date, '$')"
      //替换占位符
      curSQL = this.replaceFirst(curSQL, '#', "'" + this.searchFormObj.PresentDepartmentId + "'")
      curSQL = this.replaceFirst(curSQL, '$', "'" + this.searchFormObj.date + "'")
      this.getListFn(curSQL)
    },
    //根据SQl获取表格数据
    async getListFn(curSQL) {
      let params = {
        "sqlText": curSQL
      }
      let { response } = await QueryResultBySql(params)
      // console.log('response==>', response);
      // this.$emit('getListFn', response);
      this.addListFn(response)
    },
    //新增弹窗获取列表
    addListFn(list) {
      this.closePopup()
      if (!list.length) return
      this.columns = Object.keys(list[0])
      this.tableData = list
    },
    headerCellClassName({ column }) {
      return 'col-blue'
    },
    replaceFirst(str, searchValue, replaceValue) {
      // return str.replace(new RegExp(searchValue), replaceValue);
      return str.replace(searchValue, replaceValue);
    }
  }
}
</script>
<style scoped>
.view-table-1-box {
    position: relative;
    height: 100%;
}

.cloaking {
    position: absolute;
    top: 5px;
    left: 0;
    width: 100%;
    height: 30px;
    z-index: 90;
}

.cloaking:hover .vxe-icon-setting,
.cloaking:hover .vxe-icon-delete {
    display: block;
}

.vxe-icon-delete {
    display: none;
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 99;
    cursor: pointer;
}

.vxe-icon-setting {
    display: none;
    position: absolute;
    top: 10px;
    right: 40px;
    z-index: 99;
    cursor: pointer;
}

::v-deep .vxe-header--row:hover .vxe-icon-setting {
    display: block;
}

.mytable-style {
    overflow: hidden;
    height: 100%;
}

::v-deep(.mytable-style.vxe-table .vxe-header--column.col-blue) {
    background-color: #2db7f5;
    color: #fff;
}
</style>
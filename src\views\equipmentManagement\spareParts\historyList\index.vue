<template>
    <div class="dictionary-view">
        <div class="dictionary-main">
            <div class="card-text ma-2">
                <v-card class="mx-2 card-1" width="100%">
                    <v-card-title>
                        {{ $t('TPM_SBGL_SBBJGL._RKJL') }}
                    </v-card-title>
                    <v-card-text class="text--primary">
                        <div>
                            {{ $t('TPM_SBGL_SBBJGL._ZSL') }}:
                            <span>{{ inAllNum }}</span>
                        </div>
                        <div class="my-2">
                            {{ $t('TPM_SBGL_SBBJGL._RQ') }}:
                            <span>{{ papamstree.startime }}</span>
                            ~
                            <span>{{ papamstree.endtime }}</span>
                        </div>
                    </v-card-text>
                </v-card>
                <v-card class="mx-2 card-2" width="100%">
                    <v-card-title>{{ $t('TPM_SBGL_SBBJGL._CKJL') }}</v-card-title>
                    <v-card-text class="text--primary">
                        <div>
                            {{ $t('TPM_SBGL_SBBJGL._ZSL') }}:
                            <span>{{ outAllNum }}</span>
                        </div>
                        <div class="my-2">
                            {{ $t('TPM_SBGL_SBBJGL._RQ') }}:
                            <span>{{ papamstree.startime }}</span>
                            ~
                            <span>{{ papamstree.endtime }}</span>
                        </div>
                    </v-card-text>
                </v-card>
            </div>
            <SearchForm class="ma-2" :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <!-- <v-card class="ma-1"> -->
            <div class="form-btn-list">
                <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                    <v-icon>{{ 'mdi-table-search' }}</v-icon>
                    {{ $t('GLOBAL._SSL') }}
                </v-btn>
                <v-btn icon color="primary" @click="RepastInfoGetPage">
                    <v-icon>mdi-cached</v-icon>
                </v-btn>
                <!-- <v-btn color="primary" :disabled="!deleteList.length" @click="btnClickEvet('printAll')">导出</v-btn> -->
            </div>
            <Tables :page-options="pageOptions" :loading="loading" :btn-list="btnList"
                :tableHeight="showFrom ? 'calc(100vh - 430px)' : 'calc(100vh - 355px)'" table-name="TPM_SBGL_SBBJGL_CRK"
                :headers="historyspareParttColum" :desserts="desserts" @selectePages="selectePages" @tableClick="tableClick"
                @itemSelected="SelectedItems" @toggleSelectAll="SelectedItems">
                <template #Buynum="{ item }">
                    <div>
                        {{ item.Buynum ? '入库' : '出库' }}
                    </div>
                </template>
                <template #Usednum="{ item }">
                    <div>
                        {{ item.Usednum ? item.Usednum : item.Buynum }}
                    </div>
                </template>
            </Tables>
            <createRepast ref="createRepast" :dialogType="dialogType" :tableItem="tableItem"></createRepast>
        </div>
    </div>
</template>
<script>
import { GetPageSparePartsInOutList } from '@/api/equipmentManagement/sparePart.js';
import { historyspareParttColum } from '@/columns/equipmentManagement/sparePart.js';
const Typelist = [
    {
        label: '出库',
        value: '0'
    },
    {
        label: '入库',
        value: '1'
    }
];
export default {
    name: 'HistorylistModel',
    components: {
        createRepast: () => import('./components/createRepast.vue')
    },
    data() {
        return {
            inAllNum: 0,
            outAllNum: 0,
            Typelist,
            loading: true,
            showFrom: false,
            papamstree: {
                key: null,
                startime: new Date(new Date().getTime() - 6 * 24 * 60 * 60 * 1000).toISOString().substr(0, 10),
                endtime: new Date(Date.now() - new Date().getTimezoneOffset() * 60000).toISOString().substr(0, 10),
                type: '',
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            historyspareParttColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            rowtableItem: {},
            deleteList: [] //批量选中
        };
    },
    computed: {
        searchinputs() {
            return [
                // {
                //     value: '',
                //     key: 'key',
                //     icon: 'mdi-account-check',
                //     label: 'key',
                //     placeholder: 'key'
                // },
                {
                    value: new Date(new Date().getTime() - 6 * 24 * 60 * 60 * 1000).toISOString().substr(0, 10),
                    type: 'date',
                    key: 'startime',
                    icon: 'mdi-account-check',
                    label: this.$t('GLOBAL.StartTime'),
                    placeholder: this.$t('GLOBAL.StartTime')
                },
                {
                    value: new Date(Date.now() - new Date().getTimezoneOffset() * 60000).toISOString().substr(0, 10),
                    type: 'date',
                    key: 'endtime',
                    icon: 'mdi-account-check',
                    label: this.$t('GLOBAL.EndTime'),
                    placeholder: this.$t('GLOBAL.EndTime')
                },
                {
                    value: '',
                    selectData: this.Typelist,
                    type: 'select',
                    key: 'type',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CRK.Buynum'),
                    placeholder: this.$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_CRK.Buynum')
                }
            ];
        },
        btnList() {
            return [
                // {
                //     text: '查看',
                //     code: 'edit',
                //     type: 'primary',
                //     icon: ''
                // }
            ];
        }
    },
    mounted() {
        this.RepastInfoGetPage();
    },
    methods: {
        // 查询数据
        searchForm(value) {
            this.papamstree.pageIndex = 1;
            this.papamstree.type = value.type;
            this.papamstree.startime = value.startime;
            this.papamstree.endtime = value.endtime;
            this.RepastInfoGetPage();
        },
        // 列表查询
        async RepastInfoGetPage() {
            this.inAllNum = 0;
            this.outAllNum = 0;
            let params = {
                startime: this.papamstree.startime,
                endtime: this.papamstree.endtime,
                type: this.papamstree.type,
                pageIndex: this.papamstree.pageIndex,
                pageSize: this.papamstree.pageSize
            };
            this.loading = true;
            const res = await GetPageSparePartsInOutList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;

                this.desserts.forEach(item => {
                    this.inAllNum += Number(item.Buynum);
                    this.outAllNum += Number(item.Usednum);
                });
            }
        },
        // 按钮操作
        btnClickEvet(val) { },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;

        .card-1 {
            background: var(--v-primary-lighten4);
        }

        .card-2 {
            background: var(--v-error-lighten4);
        }

        .card-text {
            display: flex;
            max-height: 360px;
        }
    }
}
</style>

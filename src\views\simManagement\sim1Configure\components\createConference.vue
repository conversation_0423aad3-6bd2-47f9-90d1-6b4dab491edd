<template>
  <v-dialog
    v-model="showDialog"
    max-width="1080px"
  >
    <v-card>
      <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2">
        {{ dialogType == 'edit' ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ') }}
        <v-icon @click="initdata">mdi-close</v-icon>
      </v-card-title>
      <div>
        <v-form
          v-model="valid"
          ref="form"
        >
          <div style="padding-left: 2%;box-sizing: border-box;font-size: 14px;font-weight: bold;">主体配置:</div>
          <v-row class="ma-4">
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <!-- <v-text-field
                v-model="form.ModularName"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.ModularName')"
              >
              </v-text-field> -->
              <v-select
                v-model="form.ModularName"
                :items="dutyList"
                item-text="Fullname"
                item-value="Encode"
                label="模块名称"
                return-object
                dense
                outlined
              >
              </v-select>
            </v-col>
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="form.Olinetit"
                :rules="rules.Olinetit"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.Olinetit')"
              >
                <!-- <template #append>
                  <v-fade-transition leave-absolute>
                    <v-icon @click="uploadImg">mdi-account</v-icon>
                  </v-fade-transition>
                </template> -->
              </v-text-field>
            </v-col>
            <!-- <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="form.OlineType"
                :rules="rules.OlineType"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.OlineType')"
              ></v-text-field>
            </v-col> -->
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="form.RegionNum"
                :rules="rules.RegionNum"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.RegionNum')"
                @input="RegionNumChange"
              ></v-text-field>
            </v-col>
            <!-- <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="form.Width"
                :rules="rules.Width"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.Width')"
              ></v-text-field>
            </v-col> -->
            <!-- <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="form.Height"
                :rules="rules.Height"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.Height')"
              ></v-text-field>
            </v-col> -->
            <!-- <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="form.BorderImg"
                :rules="rules.BorderImg"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.BorderImg')"
              ></v-text-field>
            </v-col> -->

          </v-row>
          <v-row class="ma-4">
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <!-- <v-text-field
                v-model="form.backgroundImage"
                :rules="rules.backgroundImage"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.backgroundImage')"
              ></v-text-field> -->
              <v-file-input
                :label="$t('$vuetify.dataTable.SIM_CONFIG.backgroundImage')"
                counter
                multiple
                show-size
                variant="outlined"
              ></v-file-input>
            </v-col>
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <!-- <v-text-field
                v-model="form.ModularName"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.ModularName')"
              >
              </v-text-field> -->
              <v-select
                v-model="form.ModularName"
                :items="dutyList"
                item-text="Fullname"
                item-value="Encode"
                label="页面结构"
                return-object
                dense
                outlined
              >
              </v-select>
            </v-col>

          </v-row>
          <div style="padding-left: 2%;box-sizing: border-box;font-size: 14px;font-weight: bold;">条件配置:</div>
          <v-row class="ma-4">
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="form.ConditionName"
                :rules="rules.ConditionName"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.ConditionName')"
              >
                <!-- <template #append>
                  <v-fade-transition leave-absolute>
                    <v-icon @click="uploadImg">mdi-account</v-icon>
                  </v-fade-transition>
                </template> -->
              </v-text-field>
            </v-col>
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <!-- <v-text-field
                v-model="form.ConditionType"
                :rules="rules.ConditionType"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.ConditionType')"
                @input="RegionNumChange"
              ></v-text-field> -->
              <v-select
                v-model="form.ConditionType"
                :items="dutyList"
                item-text="Fullname"
                item-value="Encode"
                label="条件类型"
                return-object
                dense
                outlined
              >
              </v-select>
            </v-col>
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="form.ConditionData"
                :rules="rules.ConditionData"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.ConditionData')"
              ></v-text-field>
            </v-col>
          </v-row>

        </v-form>
      </div>
      <div
        v-for="(item,index) in cardNum"
        :key="index"
      >
        <v-form
          v-model="validlist"
          ref="form"
        >
          <div style="padding-left: 2%;box-sizing: border-box;font-size: 14px;font-weight: bold;">图表{{index+1}}</div>
          <v-row class="ma-4">
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="item.ModularName"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.ModularName')"
              >
              </v-text-field>
            </v-col>
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="item.Width"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.Width')"
              >
              </v-text-field>
            </v-col>
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="item.Height"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.Height')"
              >
              </v-text-field>
            </v-col>
          </v-row>
          <v-row class="ma-4">
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <!-- <v-text-field
                v-model="form.OlineType"
                :rules="rules.OlineType"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.OlineType')"
              >
              </v-text-field> -->
              <v-select
                v-model="item.OlineType"
                :items="dutyList"
                item-text="Fullname"
                item-value="Encode"
                label="图表类型"
                return-object
                dense
                outlined
                @change="OlineChange(item.OlineType,index)"
              >
              </v-select>
            </v-col>
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <!-- <v-text-field
                v-model="item.BorderImg"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.BorderImg')"
              >
              </v-text-field> -->
              <v-file-input
                :label="$t('$vuetify.dataTable.SIM_CONFIG.BorderImg')"
                counter
                multiple
                show-size
                variant="outlined"
              ></v-file-input>
            </v-col>
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="item.Order"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.Order')"
              >
              </v-text-field>
            </v-col>
          </v-row>
          <v-row class="ma-4">
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-select
                v-model="item.DataSource"
                :items="dutyList"
                item-text="Fullname"
                item-value="Encode"
                label="数据源"
                return-object
                dense
                outlined
              >
              </v-select>
            </v-col>
          </v-row>
          <div
            v-show="tabFlag && tabIndex == index"
            style="padding-left: 2%;box-sizing: border-box;font-size: 14px;font-weight: bold;"
          >tab域配置:</div>
          <v-row
            class="ma-4"
            v-show="tabFlag && tabIndex == index"
          >

            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <!-- <v-text-field
                v-model="form.ConditionType"
                :rules="rules.ConditionType"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.ConditionType')"
                @input="RegionNumChange"
              ></v-text-field> -->
              <v-select
                v-model="item.tabName"
                :items="dutyList"
                item-text="Fullname"
                item-value="Encode"
                label="tab数据源"
                return-object
                dense
                outlined
              >
              </v-select>
            </v-col>
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="item.tabNum"
                :rules="rules.tabNum"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.tabNum')"
                @input="tabNumChange"
              >
                <!-- <template #append>
                  <v-fade-transition leave-absolute>
                    <v-icon @click="uploadImg">mdi-account</v-icon>
                  </v-fade-transition>
                </template> -->
              </v-text-field>
            </v-col>
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="item.tabWidth"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.tabWidth')"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row
            class="ma-4"
            v-show="tabFlag && tabIndex == index"
          >
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="item.tabHeight"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.tabHeight')"
              >
                <!-- <template #append>
                 <v-fade-transition leave-absolute>
                   <v-icon @click="uploadImg">mdi-account</v-icon>
                 </v-fade-transition>
               </template> -->
              </v-text-field>
            </v-col>
            <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="item.tablineName"
                :rules="rules.tablineName"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.tablineName')"
              ></v-text-field>
            </v-col>
            <!-- <v-col
              class="py-0 px-3"
              cols="12"
              sm="6"
              md="4"
            >
              <v-text-field
                v-model="item.relationName"
                outlined
                dense
                disabled
                :label="$t('$vuetify.dataTable.SIM_CONFIG.relationName')"
              ></v-text-field>
            </v-col> -->
          </v-row>
          <div
            v-for="(item1,index1) in tabCardNum"
            :key="index1"
          >
            <v-form
              v-model="validlist"
              ref="form"
            >
              <div
                v-show="tabFlag && tabIndex == index"
                style="padding-left: 2%;box-sizing: border-box;font-size: 14px;font-weight: bold;"
              >tab域图表{{index1+1}}</div>
              <v-row
                class="ma-4"
                v-show="tabFlag && tabIndex == index"
              >
                <v-col
                  class="py-0 px-3"
                  cols="12"
                  sm="6"
                  md="4"
                >
                  <v-text-field
                    v-model="item1.ModularName"
                    outlined
                    dense
                    :label="$t('$vuetify.dataTable.SIM_CONFIG.ModularName')"
                  >
                  </v-text-field>
                </v-col>
                <v-col
                  class="py-0 px-3"
                  cols="12"
                  sm="6"
                  md="4"
                >
                  <v-text-field
                    v-model="item1.Width"
                    outlined
                    dense
                    :label="$t('$vuetify.dataTable.SIM_CONFIG.Width')"
                  >
                  </v-text-field>
                </v-col>
                <v-col
                  class="py-0 px-3"
                  cols="12"
                  sm="6"
                  md="4"
                >
                  <v-text-field
                    v-model="item1.Height"
                    outlined
                    dense
                    :label="$t('$vuetify.dataTable.SIM_CONFIG.Height')"
                  >
                  </v-text-field>
                </v-col>
              </v-row>
              <v-row
                class="ma-4"
                v-show="tabFlag && tabIndex == index"
              >
                <v-col
                  class="py-0 px-3"
                  cols="12"
                  sm="6"
                  md="4"
                >
                  <!-- <v-text-field
                v-model="form.OlineType"
                :rules="rules.OlineType"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.OlineType')"
              >
              </v-text-field> -->
                  <v-select
                    v-model="item1.OlineType"
                    :items="dutyList"
                    item-text="Fullname"
                    item-value="Encode"
                    label="图表类型"
                    return-object
                    dense
                    outlined
                  >
                  </v-select>
                </v-col>
                <v-col
                  class="py-0 px-3"
                  cols="12"
                  sm="6"
                  md="4"
                >
                  <!-- <v-text-field
                v-model="item.BorderImg"
                outlined
                dense
                :label="$t('$vuetify.dataTable.SIM_CONFIG.BorderImg')"
              >
              </v-text-field> -->
                  <v-file-input
                    :label="$t('$vuetify.dataTable.SIM_CONFIG.BorderImg')"
                    counter
                    multiple
                    show-size
                    variant="outlined"
                  ></v-file-input>
                </v-col>
                <v-col
                  class="py-0 px-3"
                  cols="12"
                  sm="6"
                  md="4"
                >
                  <v-text-field
                    v-model="item1.Order"
                    outlined
                    dense
                    :label="$t('$vuetify.dataTable.SIM_CONFIG.Order')"
                  >
                  </v-text-field>
                </v-col>
              </v-row>
              <v-row
                class="ma-4"
                v-show="tabFlag && tabIndex == index"
              >
                <v-col
                  class="py-0 px-3"
                  cols="12"
                  sm="6"
                  md="4"
                >
                  <v-select
                    v-model="item1.DataSource"
                    :items="dutyList"
                    item-text="Fullname"
                    item-value="Encode"
                    label="数据源"
                    return-object
                    dense
                    outlined
                  >
                  </v-select>
                </v-col>
              </v-row>
            </v-form>
          </div>
        </v-form>
      </div>
      <v-card-actions class="lighten-3">
        <v-checkbox
          v-model="classcheckbox"
          :label="$t('GLOBAL._QDBGBTC')"
        ></v-checkbox>
        <v-spacer></v-spacer>
        <v-btn
          color="primary"
          @click="addSave('add')"
        >{{ $t('GLOBAL._QD') }}</v-btn>
        <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
      </v-card-actions>
    </v-card>

    <user-img
      ref="userImg"
      :tableItem="tableItem"
      @getImgData="getImgData"
    ></user-img>
  </v-dialog>
</template>
<script>
import { StaffSaveForm, StaffSiteGetList } from '@/api/simConfig/basicdata.js';
// import { GetListByLevel } from '@/api/common.js';
export default {
  components: { userImg: () => import('./userImg.vue') },
  props: {
    selectList: {
      type: Array,
      default: () => []
    },
    tableItem: {
      type: Object,
      default: () => { }
    },
    dialogType: {
      type: String,
      default: ''
    },
    departmentData: {
      type: Array,
      default: () => []
    },
  },

  data() {
    return {
      dutyList: [
        {
          Fullname: 'tab',
          Encode: 'tab1'
        },
        {
          Fullname: 909,
          Encode: 234
        }
      ],
      cardNum: [],
      tabCardNum: [],
      preopleList: [],
      classcheckbox: true,
      form: {
        Olinetit: '',
        OlineType: '',
        RegionNum: '',
        Width: '',
        Height: '',
        BorderImg: '',
        backgroundImage: '',
        DataSource: '',
        ConditionType: '',
        ModularName: '',
        tablineName: '',
        tabHeight: '',
        tabName: '',
        tabNum: '',
        tabWidth: '',
        relationName: ''
      },
      rules: {
        Olinetit: [v => !!v || this.$t('GLOBAL.Olinetit')],
        OlineType: [v => !!v || this.$t('GLOBAL.OlineType')],
        RegionNum: [v => !!v || this.$t('GLOBAL.RegionNum')],
        Width: [v => !!v || this.$t('GLOBAL.Width')],
        Height: [v => !!v || this.$t('GLOBAL.Height')],
        BorderImg: [v => !!v || this.$t('GLOBAL.BorderImg')],
        backgroundImage: [v => !!v || this.$t('GLOBAL.backgroundImage')],
        DataSource: [v => !!v || this.$t('GLOBAL.DataSource')]
      },
      valid: true,
      validlist: true,
      showDialog: false,
      tabFlag: false,
    };
  },
  watch: {
    showDialog: {
      handler(curVal) {
        if (curVal) {
          for (const key in this.form) {
            if (Object.hasOwnProperty.call(this.form, key)) {
              this.form[key] = this.tableItem[key];
            }
          }
          this.form.LeaderCode = this.tableItem.LeaderCode || ''
        }
      },
      deep: true,
      immediate: true
    },
  },
  created() {
    // 获取人员
    this.GetStaffSiteGetList();
  },
  methods: {
    OlineChange(e, index) {
      if (e.Fullname == 'tab') {
        this.tabFlag = true
        this.tabIndex = index
      } else {
        this.tabFlag = false
        this.tabIndex = -1
      }
    },
    tabNumChange(e) {
      this.tabCardNum = []
      let num = parseFloat(e)
      for (let i = 0; i < num; i++) {
        this.tabCardNum.push({
          ModularName: '',
          Width: '',
          Height: '',
          OlineType: '',
          BorderImg: '',
          Order: '',
          DataSource: '',
        })
      }
    },
    RegionNumChange(e) {
      this.cardNum = []
      let num = parseFloat(e)
      for (let i = 0; i < num; i++) {
        this.cardNum.push({
          ModularName: '',
          Width: '',
          Height: '',
          OlineType: '',
          BorderImg: '',
          Order: '',
          DataSource: '',
          tabList: '',
          tabNum: '',
          tabWidth: '',
          tabHeight: '',
          tablineName: '',
          relationName: ''
        })
      }
    },
    initdata() {
      this.form.UserAvatar = '';
      this.showDialog = false;
    },
    selectLeader(value) {
      console.log(value);
    },

    uploadImg(type, data) {
      this.$refs.userImg.visible = true;
      if (type == 'editIcon') {
        this.$refs.userImg.urlImgLoad(data);
      }
    },
    getImgData(bs64data) {
      this.form.UserAvatar = bs64data;
    },
    async addSave() {
      if (this.$refs.form.validate()) {
        let params = {};
        const o = this.form.LeaderCode;
        if (o?.LeaderCode) {
          const { LeaderName, LeaderCode } = o || {};
          params = { LeaderName, LeaderCode }
        }
        if (!o) {
          params = { LeaderName: '', LeaderCode: '' }
        }
        const res = await StaffSaveForm({ ...this.form, ...params });
        let { success, msg } = res;
        if (success) {
          this.$refs.form.reset();
          this.$store.commit('SHOW_SNACKBAR', { text: msg || 'success', color: 'success' });
          this.showDialog = this.classcheckbox ? false : true;
          this.$parent.$parent.staffGetPageList();
        }
      }
    },
    // 获取人员
    async GetStaffSiteGetList() {
      const res = await StaffSiteGetList({ key: '' });
      const { success, response } = res || {};
      if (response && success) this.preopleList = response;
      else this.preopleList = [];
    }
  }
};
</script>
<style lang="scss" scoped>
.card-text {
    display: block;
    height: 500px;
    overflow: auto;
}

.ant-calendar-picker {
    ::v-deep input {
        border-color: #9e9e9e;
    }

    ::v-deep i {
        color: #757575;
    }
}

::v-deep input::-webkit-input-placeholder {
    color: #757575;
}
</style>

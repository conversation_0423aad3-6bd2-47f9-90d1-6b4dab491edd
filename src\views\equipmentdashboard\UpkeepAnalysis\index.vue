<template>
    <div :class="isFull ? 'device-kanban-full' : 'device-kanban-not'">
        <dv-full-screen-container>
            <div class="sbkb">
                <div class="all">
                    <div class="title">
                        保养分析看板
                        <div class="searchbox" style="top: 10px">
                            <div class="dashboardinputbox" style="width: 270px">
                                <el-date-picker value-format="yyyy-MM-dd" v-model="time" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                            </div>
                        </div>
                        <div class="searchbox">
                            <div class="dashboardinputbox" v-for="(item, index) in inputlist" :key="index">
                                <el-input v-model="item.value" v-if="item.type == 'input'" :placeholder="item.name"></el-input>
                                <el-select clearable v-if="item.type == 'select'" v-model="item.value" filterable :placeholder="item.name">
                                    <el-option v-for="(it, ind) in item.option" :key="ind" :label="it.ItemName" :value="it.ItemValue"></el-option>
                                </el-select>
                            </div>
                            <div class="dashboardinputbox"><el-button icon="el-icon-search" size="mini" @click="search">查询</el-button></div>
                            <div class="nowtimebox">
                                {{ nowTime }}
                            </div>
                        </div>
                    </div>
                    <div class="tabbox">
                        <div class="tabboxrow">
                            <div class="tabboxbox" :style="{ width: item.width }" v-for="(item, index) in tablelist1" :key="index">
                                <div class="tabboxboxtitle">{{ item.title }}</div>
                                <div class="tabboxboxcenter" :id="item.id" v-loading="item.loading"></div>
                            </div>
                            <div class="tabboxbox" style="width: 50%">
                                <div class="tabboxboxtitle">近期待保养任务</div>
                                <div class="tabboxboxcenter">
                                    <el-table :data="byrwdata" stripe height="100%" style="width: 100%">
                                        <el-table-column
                                            v-for="(it, ind) in jqbyrwheader"
                                            :key="ind"
                                            align="center"
                                            :prop="it.prop ? it.prop : it.value"
                                            :label="it.text"
                                            :width="it.width"
                                        ></el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </div>
                        <div class="tabboxrow">
                            <div class="tabboxbox" :style="{ width: item.width }" v-for="(item, index) in tablelist2" :key="index">
                                <div class="tabboxboxtitle">{{ item.title }}</div>
                                <div class="tabboxboxcenter" :id="item.id" v-loading="item.loading"></div>
                            </div>
                            <div class="tabboxbox" style="width: 33%">
                                <div class="tabboxboxtitle">超期维保设备列表</div>
                                <div class="tabboxboxcenter">
                                    <el-table :data="cqsbdata" stripe height="100%" style="width: 100%">
                                        <el-table-column
                                            v-for="(it, ind) in cqsbheader"
                                            :key="ind"
                                            align="center"
                                            :prop="it.prop ? it.prop : it.value"
                                            :label="it.text"
                                            :width="it.width"
                                        ></el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </dv-full-screen-container>
    </div>
</template>
<script>
import {
    GetMaintainWoList,
    GetDeviceChartMaintainOverdue,
    GetDeviceChartGetBarChartOverdue,
    GetDeviceChartGetMaintainQty,
    GetDeviceChartGetMaintainRate
} from '@/api/equipmentManagement/UpkeepAnalysis';
import { jqbyrwheader, cqsbheader } from '@/columns/equipmentdashboard/tableheader';
import { getbarstack } from '@/components/echarts/stackBar.js';
import { getlinebydata } from '@/components/echarts/Line.js';
import { getCircleBar } from '@/components/echarts/CircleBar.js';
import { getsimpleBar } from '@/components/echarts/simpleBar.js';
import { GetPersonList } from '@/api/equipmentManagement/Equip.js';
import moment from 'moment';
import '@/views/equipmentdashboard/style.scss';
export default {
    data() {
        return {
            isFull: false,
            myChart1: null,
            myChart2: null,
            myChart3: null,
            byrwdata: [],
            jqbyrwheader: jqbyrwheader,
            cqsbdata: [],
            cqsbheader: cqsbheader,
            inputlist: [
                {
                    id: 'DeviceCode',
                    type: 'input',
                    value: '',
                    name: '设备编码'
                },
                {
                    id: 'DeviceCategroy',
                    type: 'select',
                    option: [],
                    value: '',
                    name: '设备类型'
                },
                {
                    id: 'Department',
                    type: 'input',
                    value: '',
                    name: '工作区'
                }
            ],
            time: [moment().startOf('week').format('YYYY-MM-DD'), moment().endOf('week').format('YYYY-MM-DD')],
            tablelist1: [
                {
                    title: '保养设备数量',
                    width: '50%',
                    id: 'chart1',
                    loading: true
                }
            ],
            tablelist2: [
                {
                    title: '设备保养率',
                    width: '33%',
                    id: 'chart2',
                    loading: true
                },
                {
                    title: '超期维保设备数量',
                    width: '33%',
                    id: 'chart3',
                    loading: true
                }
            ],
            Timeinterval: null,
            statusList: [],
            MaintainByData: [],
            nowTime: '',
            SearchParams: {}
        };
    },
    async mounted() {
        this.statusList = await this.$getNewDataDictionary('MaintainPlanStatus');
        this.inputlist.forEach(item => {
            this.SearchParams[item.id] = item.value;
        });
        let MaintainBy = await GetPersonList('MaintenanceGroup');
        this.MaintainByData = MaintainBy.response[0].ChildNodes;
        this.MaintainByData.forEach(item => {
            item.ItemName = item.name;
            item.ItemValue = item.value;
        });
        this.SearchParams.queryStart = this.time[0];
        this.SearchParams.queryEnd =  this.time[1] + ' 23:59:59';
        this.inputlist[1].option = await this.$getNewDataDictionary('DeviceCategory');
        this.gettime();
        this.MyGetMaintainWoList();
        this.MyGetDeviceChartMaintainOverdue();
        setTimeout(() => {
            this.getChart1();
            this.getChart2();
            this.getChart3();
            window.addEventListener('resize', this.handleResize);
        }, 500);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.myChart1) {
            this.myChart1.dispose(); // 清理图表实例
            this.myChart2.dispose(); // 清理图表实例
            this.myChart3.dispose(); // 清理图表实例
        }
        this.Timeinterval = null;
    },
    methods: {
        search() {
            if (this.time == null) {
                this.time = [];
            }
            this.inputlist.forEach(item => {
                this.SearchParams[item.id] = item.value;
            });
            this.SearchParams.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.SearchParams.queryStart = this.time[0];
            this.SearchParams.queryEnd =  this.time[1] + ' 23:59:59';
            this.MyGetMaintainWoList();
            this.MyGetDeviceChartMaintainOverdue();
            this.getChart1();
            this.getChart2();
            this.getChart3();
        },
        async MyGetMaintainWoList() {
            let res = await GetMaintainWoList(this.SearchParams);
            this.byrwdata = res.response;
            this.byrwdata.forEach(item => {
                this.statusList.forEach(it => {
                    if (item.Status == it.ItemValue) {
                        item.Status = it.ItemName;
                    }
                });
                this.MaintainByData.forEach(it => {
                    if (item.MaintainBy == it.ItemValue) {
                        item.MaintainBy = it.ItemName;
                        item.MaintainByValue = it.ItemValue;
                    }
                });
            });
        },
        async MyGetDeviceChartMaintainOverdue() {
            let res = await GetDeviceChartMaintainOverdue(this.SearchParams);
            this.cqsbdata = res.response;
            this.cqsbdata.forEach(item => {
                this.MaintainByData.forEach(it => {
                    if (item.MaintainBy == it.ItemValue) {
                        item.MaintainBy = it.ItemName;
                        item.MaintainByValue = it.ItemValue;
                    }
                });
            });
        },
        gettime() {
            this.nowTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.Timeinterval = setInterval(() => {
                this.nowTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            }, 1000);
        },
        async getChart1() {
            let chartDom = document.getElementById('chart1');
            this.myChart1 = this.$echarts.init(chartDom);
            let res = await GetDeviceChartGetMaintainQty(this.SearchParams);
            let data = res.response.series;
            let option = getCircleBar(data);
            this.myChart1.setOption(option, true);
        },
        async getChart2() {
            let chartDom = document.getElementById('chart2');
            this.myChart2 = this.$echarts.init(chartDom);
            let res = await GetDeviceChartGetMaintainRate(this.SearchParams);
            let data = res.response;
            let data1 = {
                xdata: data.categorydata,
                data: data.series[0].data
            };
            let option = getsimpleBar(data1, '%', '#5EF02B');
            this.myChart2.setOption(option, true);
        },
        async getChart3() {
            let chartDom = document.getElementById('chart3');
            this.myChart3 = this.$echarts.init(chartDom);
            let res = await GetDeviceChartGetBarChartOverdue(this.SearchParams);
            let data = res.response;
            let data1 = {
                xdata: data.categorydata,
                data: data.series[0].data
            };
            let option = getsimpleBar(data1, '个', '#E5D53D');
            this.myChart3.setOption(option, true);
        },
        handleResize() {
            if (this.myChart1) {
                this.myChart1.resize();
                this.myChart2.resize();
                this.myChart3.resize();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.page_wrapper {
    padding: 0 !important;
}
</style>

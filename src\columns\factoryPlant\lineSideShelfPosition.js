// import i18n from '@/plugins/i18n';
// 线边货架位物料
export const lineSideShelfPositionMaterial = [
    {
        text: '序号',
        value: 'Index',
        width: '60px',
        sortable: true
    },
    { text: '物料代码', value: 'MaterialCode', width: '140px' },
    { text: '有效标志', value: 'Status', width: '140px' },
    { text: '备注', value: 'Remark', width: '140px' },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    {
        text: '操作',
        align: 'center',
        value: 'actions',
        width: '140px'
    }
];

// 线边货架
export const lineSideShelfColumns = [
    {
        text: '序号',
        value: 'Index',
        width: '60px',
        sortable: true
    },
    { text: '货架代码', value: 'RackingCode', width: '120px', sortable: true },
    { text: '货架名称', value: 'RackingName', width: '120px', sortable: true },
    { text: '有效标志', value: 'Status', width: '140px' },
    { text: '货架位置描述', value: 'Description', width: '260px' },
    { text: '备注', value: 'Remark', width: '140px' },
    { text: '最近更新人', value: 'ModifyUserId', width: '140px' },
    { text: '最近更新时间', value: 'ModifyDate', width: '180px' },
    {
        text: '操作',
        value: 'actions',
        align: 'center',
        width: '140px',
        sortable: true
    }
];

// 线边货架位
export const lineSideShelfPosition = [
    {
        text: '序号',
        value: 'Index',
        width: '60px',
        sortable: true
    },
    { text: '货架位代码', value: 'BinCode', width: '120px' },
    { text: '货架位名称', value: 'BinName', width: '120px' },
    { text: '位置描述', value: 'BinDescription', width: '120px' },
    { text: '货架位类型', value: 'BinType', width: '120px' },
    { text: '有效标志', value: 'Status', width: '140px' },
    { text: '备注', value: 'Remark', width: '140px' },
    { text: '最近更新人', value: 'ModifyUserId', width: '140px' },
    { text: '最近更新时间', value: 'ModifyDate', width: '180px' },
    {
        text: '操作',
        value: 'actions',
        align: 'center',
        width: '140px',
        sortable: true
    }
];

// 货架对应设备机台
export const machineEquipmentColumns = [
    {
        text: '序号',
        value: 'Index',
        width: '60px',
        sortable: true
    },
    { text: '设备代码', value: 'DeviceCode', width: '120px', sortable: true },
    { text: '备注', value: 'Remark', width: '140px' },
    { text: '最近更新人', value: 'ModifyUserId', width: '120px' },
    { text: '最近更新时间', value: 'ModifyDate', width: '180px' },
    {
        text: '操作',
        value: 'actions',
        align: 'center',
        width: '140px',
        sortable: true
    }
];

<template>
    <v-dialog v-model="showDialog" max-width="980px">
        <v-card class="" v-if="dialogType == 'add'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._XZ') }}
                <v-icon @click="closeEquip">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" style="height: 450px">
                    <v-row class="pt-8">
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Name" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.ProjectName') + '*'"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Code" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.ProjectCode') + '*'"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select
                                v-model="form.Type"
                                outlined
                                dense
                                :items="itemType"
                                item-value="ItemName"
                                item-text="ItemName"
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYXM.ProjectCategory') + '*'"
                            ></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Frequency" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYXM.Insfrequency') + '*'"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select
                                v-model="form.Cycle"
                                :items="Inscycle"
                                item-text="ItemName"
                                item-value="ItemName"
                                outlined
                                dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYXM.Inscycle') + '*'"
                            ></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select
                                v-model="form.IsStop"
                                :items="IsStopList"
                                item-text="ItemName"
                                item-value="ItemName"
                                outlined
                                dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYXM.IsStop')"
                            ></v-select>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.CheckStandard" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.CheckStandard')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.Method" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Methods')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-select
                                v-model="form.InputType"
                                :items="InputTypeList"
                                item-text="ItemName"
                                item-value="ItemName"
                                outlined
                                dense
                                :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.InputType') + '*'"
                            ></v-select>
                            <!-- <v-text-field v-model="form.InputType" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.InputType')"></v-text-field> -->
                        </v-col>
                        <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                            <v-text-field v-model="form.UsingTime" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYXM.UsingTime')"></v-text-field>
                        </v-col>
                        <v-col class="py-0 px-3" cols="12">
                            <v-textarea v-model="form.Remark" outlined rows="2" dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Remark')"></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3">
                <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('add')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="closeEquip">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>

        <v-card class="" v-if="dialogType == 'edit2'">
            <v-card-title class="text-h6 justify-space-between primary lighten-2">
                {{ $t('GLOBAL._BJ') }}
                <v-icon @click="showDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-row class="pt-8">
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Name" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.ProjectName') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Code" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.ProjectCode') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select
                            v-model="editedItem.Type"
                            outlined
                            dense
                            :items="itemType"
                            item-value="ItemName"
                            item-text="ItemName"
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYXM.ProjectCategory') + '*'"
                        ></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Frequency" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYXM.Insfrequency') + '*'"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select
                            v-model="editedItem.Cycle"
                            :items="Inscycle"
                            item-text="ItemName"
                            item-value="ItemName"
                            outlined
                            dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYXM.Inscycle') + '*'"
                        ></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select
                            v-model="editedItem.IsStop"
                            :items="IsStopList"
                            item-text="ItemName"
                            item-value="ItemName"
                            outlined
                            dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYXM.IsStop')"
                        ></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.CheckStandard" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.CheckStandard')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.Method" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Methods')"></v-text-field>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-select
                            v-model="editedItem.InputType"
                            :items="InputTypeList"
                            item-text="ItemName"
                            item-value="ItemName"
                            outlined
                            dense
                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.InputType') + '*'"
                        ></v-select>
                    </v-col>
                    <v-col class="py-0 px-3" cols="12" sm="4" md="4">
                        <v-text-field v-model="editedItem.UsingTime" outlined dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBBYXM.UsingTime')"></v-text-field>
                    </v-col>

                    <v-col class="py-0 px-3" cols="12">
                        <v-textarea v-model="editedItem.Remark" outlined rows="2" dense :label="$t('$vuetify.dataTable.TPM_SBGL_SBDJXM.Remark')"></v-textarea>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions class="lighten-3 py-3">
                <!-- <v-checkbox v-model="classcheckbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox> -->
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="addSave('edit')">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn @click="showDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
import { StaffSiteGetList } from '@/api/peopleManagement/basicdata.js';
import { GetMaintainWoItemSaveForm } from '@/api/equipmentManagement/myupkeep.js';
import { Message, MessageBox } from 'element-ui';

const planType = ['点检'];
export default {
    props: {
        DeviceCategoryId: {
            type: String,
            default: ''
        },
        mcCyclelist: {
            type: Array,
            default: () => []
        },
        itemType: {
            type: Array,
            default: () => []
        },
        Insfrequency: {
            type: Array,
            default: () => []
        },
        statusId: {
            type: String,
            default: ''
        },
        Inscycle: {
            type: Array,
            default: () => []
        },
        InputTypeList: {
            type: Array,
            default: () => []
        },
        repastTypelist: {
            type: Array,
            default: () => []
        },
        dialogType: {
            type: String,
            default: ''
        },
        rowtableItem: {
            type: Object,
            default: () => {}
        },
        tableItem: {
            type: Object,
            default: () => {}
        },
        maintenanceType: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            stateList: [
                { name: '启用', value: '1' },
                { name: '禁用', value: '0' }
            ],
            IsStopList: [
                {
                    ItemName: '是'
                },
                {
                    ItemName: '否'
                }
            ],
            valid: false,
            showDialog: false,
            classcheckbox: true,
            strbatchNo: '',
            planType,
            peopleitems: [],
            form: {
                Name: '',
                Code: '',
                Type: '',
                Cycle: '',
                Frequency: '',
                IsStop: '',
                CheckStandard: '',
                Method: '',
                InputType: '',
                Remark: '',
                UsingTime: ''
            }
        };
    },
    computed: {
        editedItem() {
            const { Name, Code, Type,StatusValue, Cycle, UsingTime, Frequency, IsStop, CheckStandard, Method, InputType, Remark } = this.tableItem;
            return {
                Name,
                Code,
                Type,
                StatusValue,
                Cycle,
                Frequency,
                IsStop,
                CheckStandard,
                Method,
                InputType,
                Remark,
                UsingTime
            };
        }
    },
    async created() {
        // this.itemType = await this.$getNewDataDictionary('MaintainItemType');
        // this.Insfrequency = await this.$getNewDataDictionary('MaintainFrequency');
        // this.Inscycle = await this.$getNewDataDictionary('MaintainCycle');
        // this.InputTypeList = await this.$getNewDataDictionary('MaintainInputType');
    },
    methods: {
        closeEquip() {
            this.showDialog = false;
            this.$refs.form.reset();
        },
        reset() {
            this.$refs.form.reset();
        },
        async addSave(type) {
            const params = type == 'add' ? this.form : this.editedItem;
            if (params.Name == '' || params.IsStop == '' || params.Code == '' || params.Type == '' || params.Cycle == '' || params.Frequency == '' || params.InputType == '') {
                Message({
                    message: `${this.$t('Inventory.ToOver')}`,
                    type: 'error'
                });
                return;
            }
            params.WoId = this.rowtableItem.ID;
            params.Wo = this.rowtableItem.Wo;
            params.DeviceId = this.rowtableItem.DeviceId;
            params.DeviceName = this.rowtableItem.DeviceName;
            params.DeviceCode = this.rowtableItem.DeviceCode;
            params.ItemStatusId = this.statusId;
            params.PlanMaintainDate = this.rowtableItem.PlanMaintainDate;
            params.ItemId = this.rowtableItem.ID;
            params.Status = this.tableItem.StatusValue;
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            if (type == 'edit') {
                params.ID = this.tableItem.ID;
            }
            const res = await GetMaintainWoItemSaveForm(params);
            let { success, msg } = res;
            if (success) {
                this.$store.commit('SHOW_SNACKBAR', { text: msg || '创建成功', color: 'success' });
                this.$emit('loadData2');
                this.showDialog = this.classcheckbox ? false : true;
            }
        }
    }
};
</script>

<style lang="scss">
.py-0 {
    position: relative;
    .el-date-editor {
        width: 100%;
        position: absolute;
        opacity: 0;
        top: 0;
        .el-input__inner {
            font-size: 16px;
            height: 32.5px;
            color: rgba(0, 0, 0, 0.87);
            border: 1px solid #9e9e9e;
        }
        .el-input__icon {
            line-height: 32.5px;
        }
    }
}
</style>

// Andon报警主页
<template>
    <div class="alarm-home-view">
        <div class="alarm-home-main overflow-auto">
            <v-card outlined>
                <div class="alarm-home-title">
                    <h3 class="ml-4">{{ this.$t('ANDON_BJZY._BJZY') }}</h3>
                    <v-btn icon color="primary">
                        <v-icon @click="sendAndonNotice">mdi-cached</v-icon>
                    </v-btn>
                </div>
                <div class="alarm-home-content">
                    <v-row>
                        <v-col :cols="12" :sm="6" :md="4" :lg="3" v-for="(i, k) in alarmTypeRootList" :key="k">
                            <div class="alarm-content-box">
                                <!-- @click="setAlarm(i)" -->
                                <div class="alarm-home-content-item" @click="setAlarm(i)">
                                    <span v-if="i.AlarmCode.indexOf('UNCERTAIN') == -1 && i.Icon" class="iconfont" :class="`icon-${i.Icon.toLowerCase()}`"></span>
                                    <span v-else class="iconfont icon-daiding"></span>
                                    <h3>{{ i.AlarmName }}</h3>
                                </div>
                                <template v-if="i.ALARM">
                                    <span @click="processingAlarm(i, 'ALARM')" class="alarm-home-content-item-triangle left-top-triangle"></span>
                                    <span @click="processingAlarm(i, 'ALARM')" class="left-top-count">{{ i.ALARM }}</span>
                                </template>
                                <template v-if="i.RESPOND">
                                    <span @click="disposalAlarm(i, 'RESPOND')" class="alarm-home-content-item-triangle right-top-triangle"></span>
                                    <span @click="disposalAlarm(i, 'RESPOND')" class="right-top-count">{{ i.RESPOND }}</span>
                                </template>
                                <template v-if="i.Upgrad">
                                    <span @click="processingAlarm(i, 'upgrad')" class="alarm-home-content-item-triangle left-bottom-triangle"></span>
                                    <span @click="processingAlarm(i, 'upgrad')" class="left-bottom-count">{{ i.Upgrad }}</span>
                                </template>
                                <template v-if="i.CLOSED">
                                    <span @click="disposalAlarm(i, 'UNREAD')" class="alarm-home-content-item-triangle right-bottom-triangle"></span>
                                    <span @click="disposalAlarm(i, 'UNREAD')" class="right-bottom-count">{{ i.CLOSED > 9999 ? '9999+' : i.CLOSED }}</span>
                                </template>
                            </div>
                        </v-col>
                    </v-row>
                </div>
            </v-card>
            <!-- 20240418, wzh, add the deal type -->
            <update-dialog
                ref="updateDialog"
                :problemLevelList="problemLevelList"
                :opera-obj="operaObj"
                :AreaList="AreaList"
                :productLineList="productLine"
                :equipmentTree="equipmentTree"
                :dealTypeList="dealTypeList"
                @handlePopup="handlePopup"
            ></update-dialog>
            <!-- 20240418, wzh, add the deal type -->
            <processingDialog
                ref="processingDialog"
                :opera-obj="operaObj"
                :problemLevelList="problemLevelList"
                :areaList="AreaList"
                :productLineList="productLine"
                :equipmentList="equipmentList"
                :dealTypeList="dealTypeList"
                :segmentList="SegmentList"
                @handlePopup="handlePopup"
            />
            <!-- 20240418, wzh, add the deal type -->
            <disposalDialog
                ref="disposalDialog"
                @initOpenUnreadDialog="initOpenUnreadDialog"
                :problemLevelList="problemLevelList"
                :opera-obj="operaObj"
                :areaList="AreaList"
                :equipmentList="equipmentList"
                :segmentList="SegmentList"
                :dealTypeList="dealTypeList"
                :productLineList="productLine"
                @handlePopup="handlePopup"
            />
        </div>
    </div>
</template>
<script>
import { getAlarmTypeRootList } from '@/api/andonManagement/alarmType.js';
import { getCornerMark, SendCornerMark, getAndonNotice } from '@/api/andonManagement/alarmHome.js';
import { getDepartment } from '../../systemManagement/userManagement/service.js';
import { EquipmentGetEquipmentTree } from '@/api/common.js';
import Util from '@/util';
import * as signalR from '@microsoft/signalr';
export default {
    name: 'UpgradeRule',
    components: {
        UpdateDialog: () => import('./components/updateDialog.vue'),
        disposalDialog: () => import('./components/disposalDialog.vue'),
        processingDialog: () => import('./components/processingDialog.vue')
    },
    data() {
        return {
            operaObj: {},
            desserts: [],
            searchParams: {},
            // alarmTypeRootList: [],
            productLine: [],
            cornerMarkList: [],
            equipmentTree: [],
            equipmentList: [],
            SegmentList: [],
            problemLevelList: [],
            postInfoList: [],
            dealTypeList: [], // 20240418, wzh, add the deal type
            AreaList: [],
            signaRApi: '/api2/chatHub',
            connection: '',
            departmentData: []
        };
    },
    computed: {
        alarmTypeRootList() {
            return this.$store.getters.getNotification || [];
        },
        andonCodeType() {
            return this.$store.getters.getAndonCodeType || '';
        }
    },
    watch: {
        $route: 'isOuterChain',
        andonCodeType: {
            handler(nv) {
                if (nv) {
                    let i = this.alarmTypeRootList.find(item => item.AlarmCode == nv);
                    this.$nextTick(() => {
                        this.disposalAlarm(i, 'UNREAD');
                        this.$store.commit('SETANDONCODETYPE', { code: null, path: this.$route.path });
                    });
                }
            }
        }
    },
    async created() {
        this.problemLevelList = await this.$getDataDictionary('problemLevel');
        this.dealTypeList = await this.$getDataDictionary('andon_DealType'); // 20240418, wzh, add the deal type
        this.postInfoList = await this.$getDataDictionary('Andon_PostType');
        // await this.getDataList();
        this.GetListByLevel();
        this.GetFactoryTree();
        this.isOuterChain();
        // this.getDepartmentData();
    },
    mounted() {
        this.andonCodeType;
        // this.signalRinit();
        this.GetListByLevel();
        //    this.getDataList();
        this.GetFactoryTree();
        this.isOuterChain();
    },
    activated() {
        // this.signalRinit();
    },
    deactivated() {
        // this.connection.stop();
    },
    methods: {
        initOpenUnreadDialog() {
            if (this.andonCodeType) {
                let i = this.alarmTypeRootList.find(item => item.AlarmCode == this.andonCodeType);
                this.$nextTick(() => {
                    this.disposalAlarm(i, 'UNREAD');
                    this.$store.commit('SETANDONCODETYPE', { code: null, path: this.$route.path });
                });
            }
        },
        sendAndonNotice() {
            getAndonNotice();
        },
        signalRinit() {
            this.connection = new signalR.HubConnectionBuilder()
                .withUrl(this.$signaRbaseURL + this.signaRApi, {
                    skipNegotiation: true,
                    transport: signalR.HttpTransportType.WebSockets,
                    accessTokenFactory: () => this.$store.getters.getAccessToken // 20240422, wzh, signalr test
                })
                .configureLogging(signalR.LogLevel.Information)
                .build();
            this.connection.on('CornerMark', message => {
                try {
                    const o = JSON.parse(message);
                    this.alarmTypeRootList.forEach(i => {
                        i.RESPOND = 0;
                        i.ALARM = 0;
                        i.CLOSED = 0;
                        i.Upgrad = 0;
                    });
                    o.forEach(e => {
                        this.alarmTypeRootList.forEach(i => {
                            if (e.mainAlarmType == i.AlarmCode) i[e.recordStatus] = e.count;
                        });
                    });
                    this.$forceUpdate();
                } catch (error) {
                    console.log(error);
                }
            });
            this.connection.on('receiveupdate', () => {});
            // 20240422, wzh, signalr test
            this.connection.on('SendMessageToAll', msg => {
                console.log(msg);
            });
            this.connection.on('SendMessageByGroup', msg => {
                console.log(msg);
            });
            this.connection.on('SendMessageByConnection', msg => {
                console.log(msg);
            });
            this.connection.on('SendMessageByUser', msg => {
                console.log(msg);
            });

            this.connection
                .start()
                .then(() => console.log('andon signalR Connected!'))
                .catch(err => {
                    console.log(err);
                });
        },
        async getDepartmentData() {
            let resp = await getDepartment();
            this.departmentData = resp.response;
        },
        // 根据地址栏信息判断是否外链跳转
        // http://localhost:8080/#/andonManagement/alarmHome?type=jj&ID=02304040-9500-4605-4c77-9ea500000000&AlarmName=%E6%99%BA%E8%83%BD%E8%BF%90%E7%BB%B4&_DLZH=admin&_DLMM=000000
        isOuterChain() {
            const { type, ID, AlarmName } = this.$route.query;
            if (type && ID) {
                this.$nextTick(() => {
                    this.processingAlarm({ ID, AlarmName, outerChain: true });
                });
            }
        },
        // 获取设备树形数据
        async GetFactoryTree() {
            const res = await EquipmentGetEquipmentTree();
            let { success, response } = res;
            if (success) {
                this.equipmentTree = response || [];
            }
        },
        // 获取角标数据
        async getCornerMarkList() {
            const res = await getCornerMark({});
            let { success, response } = res;
            if (success) {
                this.alarmTypeRootList.forEach(i => {
                    i.RESPOND = 0;
                    i.ALARM = 0;
                });
                response.forEach(e => {
                    this.alarmTypeRootList.forEach(i => {
                        if (e.MainAlarmType == i.AlarmCode) i[e.RecordStatus] = e.Count;
                    });
                });
                this.$forceUpdate();
            }
        },
        // 获取工段
        async GetListByLevel() {
            this.productLine = await Util.GetEquipmenByLevel('Line');

            this.AreaList = await Util.GetEquipmenByLevel('Area');
            // 获取工站
            this.SegmentList = await Util.GetEquipmenByLevel('Segment');
            // 设备
            this.equipmentList = await Util.GetEquipmenByLevel('Unit');
        },
        // 获取大类列表
        async getDataList() {
            const res = await getAlarmTypeRootList({});
            const { success, response } = res || {};
            if (success) {
                this.alarmTypeRootList = response;
            } else {
                this.alarmTypeRootList = [];
            }
            SendCornerMark();
        },
        //新增报警
        setAlarm(o) {
            this.operaObj = o || {};
            this.$refs.updateDialog.dialog = true;
        },
        // 处理告警
        processingAlarm(i, eventStatus) {
            this.operaObj = { ...i, eventStatus };
            this.$refs.processingDialog.dialog = true;
        },
        // 处置告警列表
        disposalAlarm(i, eventStatus) {
            console.log('diaoloh', this.$refs.disposalDialog);
            this.operaObj = { ...i, eventStatus };
            this.$refs.disposalDialog.dialog = true;
        },
        handlePopup(type) {
            setTimeout(() => {
                getAndonNotice();
            }, 1 * 1000);
            switch (type) {
                case 'listDialog':
                    break;

                default:
                    getAndonNotice();
                    break;
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.alarm-home-view {
    display: flex;

    .alarm-home-main {
        flex: 1;
        width: 100%;

        .alarm-home-title {
            line-height: 60px;
            width: 100%;
            border-bottom: 1px solid gainsboro;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-right: 20px;
        }

        .alarm-home-content {
            padding: 30px 60px;

            // height: calc(100vh - 140px);
            .alarm-home-content-item {
                position: relative;
                border: 1px solid gainsboro;
                border-radius: 6px;
                cursor: pointer;
                display: flex;
                flex-direction: column;
                text-align: center;
                padding-bottom: 16px;
                box-shadow: 2px 2px 8px 5px #e0e0e0;

                .iconfont {
                    font-size: 5rem;
                }
            }

            .alarm-home-content-item-triangle {
                border-radius: 6px;
                position: absolute;
                cursor: pointer;
                border: 30px solid red;
                height: 0;
                width: 0;
            }

            .left-top-triangle {
                top: 0;
                left: 0;
                border: 30px solid orange;
                border-right-color: transparent;
                border-bottom-color: transparent;
            }

            .right-top-triangle {
                top: 0;
                right: 0;
                border: 30px solid red;
                border-left-color: transparent;
                border-bottom-color: transparent;
            }

            .right-bottom-triangle {
                bottom: 0;
                right: 0;
                border: 30px solid rgba($color: #000000, $alpha: 0.4);
                border-left-color: transparent;
                border-top-color: transparent;
            }

            .left-bottom-triangle {
                bottom: 0;
                left: 0;
                border: 30px solid #fff500;
                border-right-color: transparent;
                border-top-color: transparent;
            }

            .left-top-count {
                position: absolute;
                cursor: pointer;
                top: 8px;
                left: 8px;
            }

            .left-bottom-count {
                position: absolute;
                cursor: pointer;
                bottom: 8px;
                left: 8px;
            }

            .right-top-count {
                position: absolute;
                cursor: pointer;
                top: 8px;
                right: 8px;
            }

            .right-bottom-count {
                position: absolute;
                cursor: pointer;
                bottom: 8px;
                right: 8px;
            }

            .alarm-content-box {
                position: relative;
                margin: 10px 30px;
            }
        }
    }
}
</style>

<style lang="scss">
.text-show-ellipsis {
    td {
        overflow: hidden;
        white-space: normal;
        text-overflow: ellipsis;
    }
}

.alarm-home-search {
    legend {
        display: none;
    }

    .v-text-field--outlined fieldset {
        top: -1px;
    }

    input {
        font-size: 14px;
    }
}
</style>
<template>
  <div class="ExhibitionBox">
    <div class="ExhibitionBox_l">
      <!-- <div
              class="ExhibitionBox_l_n"
              v-for="(item,index) in cardNum"
              :key="index"
            >{{index}}</div> -->
    </div>
    <div class="ExhibitionBox_c"></div>
    <div class="ExhibitionBox_r"></div>
  </div>
</template>
<script>
export default {
  data: () => ({})
}
</script>
<style lang="scss" scoped>
.ExhibitionBox {
    width: 240px;
    height: 100px;
    border: 1px solid #ccc;
    display: flex;
    padding: 10px;
    box-sizing: border-box;
    margin-left: 30px;
    margin-top: -10px;
    gap: 10px;
}
.ExhibitionBox_l {
    flex: 1;
    border: 1px solid green;
    border-radius: 5px;
    padding: 3px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
    overflow-y: auto;
    justify-content: space-between;
}
.ExhibitionBox_c,
.ExhibitionBox_r {
    flex: 1;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 3px;
    box-sizing: border-box;
    overflow: hidden;
    overflow-y: auto;
}
.ExhibitionBox_l_n {
    width: 20px;
    height: 20px;
    border: 1px solid green;
    border-radius: 3px;
    text-align: center;
}
</style>
{"version": 3, "sources": ["../../src/util/colorUtils.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;AAkBM,SAAU,UAAV,CAAsB,KAAtB,EAA4C;AAChD,SAAO,CAAC,CAAC,KAAF,IAAW,CAAC,CAAC,KAAK,CAAC,KAAN,CAAY,4BAAZ,CAApB;AACD;;AAEK,SAAU,UAAV,CAAsB,KAAtB,EAAkC;AACtC,MAAI,GAAJ;;AAEA,MAAI,OAAO,KAAP,KAAiB,QAArB,EAA+B;AAC7B,IAAA,GAAG,GAAG,KAAN;AACD,GAFD,MAEO,IAAI,OAAO,KAAP,KAAiB,QAArB,EAA+B;AACpC,QAAI,CAAC,GAAG,KAAK,CAAC,CAAD,CAAL,KAAa,GAAb,GAAmB,KAAK,CAAC,SAAN,CAAgB,CAAhB,CAAnB,GAAwC,KAAhD;;AACA,QAAI,CAAC,CAAC,MAAF,KAAa,CAAjB,EAAoB;AAClB,MAAA,CAAC,GAAG,CAAC,CAAC,KAAF,CAAQ,EAAR,EAAY,GAAZ,CAAgB,UAAA,IAAI;AAAA,eAAI,IAAI,GAAG,IAAX;AAAA,OAApB,EAAqC,IAArC,CAA0C,EAA1C,CAAJ;AACD;;AACD,QAAI,CAAC,CAAC,MAAF,KAAa,CAAjB,EAAoB;AAClB,2CAAgB,KAAhB;AACD;;AACD,IAAA,GAAG,GAAG,QAAQ,CAAC,CAAD,EAAI,EAAJ,CAAd;AACD,GATM,MASA;AACL,UAAM,IAAI,SAAJ,2DAAiE,KAAK,IAAI,IAAT,GAAgB,KAAhB,GAAwB,KAAK,CAAC,WAAN,CAAkB,IAA3G,cAAN;AACD;;AAED,MAAI,GAAG,GAAG,CAAV,EAAa;AACX,oEAA2C,KAA3C;AACA,IAAA,GAAG,GAAG,CAAN;AACD,GAHD,MAGO,IAAI,GAAG,GAAG,QAAN,IAAkB,KAAK,CAAC,GAAD,CAA3B,EAAkC;AACvC,yCAAgB,KAAhB;AACA,IAAA,GAAG,GAAG,QAAN;AACD;;AAED,SAAO,GAAP;AACD;;AAEK,SAAU,UAAV,CACJ,KADI,EAEJ,MAFI,EAGJ,YAHI,EAGsC;AAAA,8BAEP,KAAK,CACrC,QADgC,GACrB,IADqB,GACd,OADc,CACN,GADM,EACD,EADC,EACG,KADH,CACS,GADT,EACc,CADd,CAFO;AAAA;AAAA,MAEnC,SAFmC;AAAA,MAExB,aAFwB;;AAK1C,MAAI,QAAQ,GAAG,EAAf;;AACA,MAAI,SAAS,IAAI,SAAS,IAAI,MAA9B,EAAsC;AACpC,QAAI,aAAa,IAAI,aAAa,IAAI,MAAM,CAAC,SAAD,CAA5C,EAAyD;AACvD,MAAA,QAAQ,GAAG,MAAM,CAAC,SAAD,CAAN,CAAkB,aAAlB,CAAX;AACD,KAFD,MAEO,IAAI,UAAU,MAAM,CAAC,SAAD,CAApB,EAAiC;AACtC,MAAA,QAAQ,GAAG,MAAM,CAAC,SAAD,CAAN,CAAkB,IAA7B;AACD;AACF,GAND,MAMO,IAAI,SAAS,IAAI,SAAS,IAAI,YAA9B,EAA4C;AACjD,IAAA,QAAQ,GAAG,YAAY,CAAC,SAAD,CAAvB;AACD;;AAED,SAAO,QAAP;AACD;;AAEK,SAAU,QAAV,CAAoB,KAApB,EAAmC;AACvC,MAAI,QAAQ,GAAW,KAAK,CAAC,QAAN,CAAe,EAAf,CAAvB;AAEA,MAAI,QAAQ,CAAC,MAAT,GAAkB,CAAtB,EAAyB,QAAQ,GAAG,IAAI,MAAJ,CAAW,IAAI,QAAQ,CAAC,MAAxB,IAAkC,QAA7C;AAEzB,SAAO,MAAM,QAAb;AACD;;AAEK,SAAU,UAAV,CAAsB,KAAtB,EAAkC;AACtC,SAAO,QAAQ,CAAC,UAAU,CAAC,KAAD,CAAX,CAAf;AACD;AAED;;;;AAIG;;;AACG,SAAU,UAAV,CAAsB,IAAtB,EAAgC;AAAA,MAC5B,CAD4B,GACb,IADa,CAC5B,CAD4B;AAAA,MACzB,CADyB,GACb,IADa,CACzB,CADyB;AAAA,MACtB,CADsB,GACb,IADa,CACtB,CADsB;AAAA,MACnB,CADmB,GACb,IADa,CACnB,CADmB;;AAEpC,MAAM,CAAC,GAAG,SAAJ,CAAI,CAAC,CAAD,EAAc;AACtB,QAAM,CAAC,GAAG,CAAC,CAAC,GAAI,CAAC,GAAG,EAAV,IAAiB,CAA3B;AACA,WAAO,CAAC,GAAG,CAAC,GAAG,CAAJ,GAAQ,IAAI,CAAC,GAAL,CAAS,IAAI,CAAC,GAAL,CAAS,CAAT,EAAY,IAAI,CAAhB,EAAmB,CAAnB,CAAT,EAAgC,CAAhC,CAAnB;AACD,GAHD;;AAKA,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAD,CAAF,EAAO,CAAC,CAAC,CAAD,CAAR,EAAa,CAAC,CAAC,CAAD,CAAd,EAAmB,GAAnB,CAAuB,UAAA,CAAC;AAAA,WAAI,IAAI,CAAC,KAAL,CAAW,CAAC,GAAG,GAAf,CAAJ;AAAA,GAAxB,CAAZ;AAEA,SAAO;AAAE,IAAA,CAAC,EAAE,GAAG,CAAC,CAAD,CAAR;AAAa,IAAA,CAAC,EAAE,GAAG,CAAC,CAAD,CAAnB;AAAwB,IAAA,CAAC,EAAE,GAAG,CAAC,CAAD,CAA9B;AAAmC,IAAA,CAAC,EAAD;AAAnC,GAAP;AACD;AAED;;;;AAIG;;;AACG,SAAU,UAAV,CAAsB,IAAtB,EAAgC;AACpC,MAAI,CAAC,IAAL,EAAW,OAAO;AAAE,IAAA,CAAC,EAAE,CAAL;AAAQ,IAAA,CAAC,EAAE,CAAX;AAAc,IAAA,CAAC,EAAE,CAAjB;AAAoB,IAAA,CAAC,EAAE;AAAvB,GAAP;AAEX,MAAM,CAAC,GAAG,IAAI,CAAC,CAAL,GAAS,GAAnB;AACA,MAAM,CAAC,GAAG,IAAI,CAAC,CAAL,GAAS,GAAnB;AACA,MAAM,CAAC,GAAG,IAAI,CAAC,CAAL,GAAS,GAAnB;AACA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAL,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CAAZ;AACA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAL,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CAAZ;AAEA,MAAI,CAAC,GAAG,CAAR;;AAEA,MAAI,GAAG,KAAK,GAAZ,EAAiB;AACf,QAAI,GAAG,KAAK,CAAZ,EAAe;AACb,MAAA,CAAC,GAAG,MAAM,IAAK,CAAC,CAAC,GAAG,CAAL,KAAW,GAAG,GAAG,GAAjB,CAAX,CAAJ;AACD,KAFD,MAEO,IAAI,GAAG,KAAK,CAAZ,EAAe;AACpB,MAAA,CAAC,GAAG,MAAM,IAAK,CAAC,CAAC,GAAG,CAAL,KAAW,GAAG,GAAG,GAAjB,CAAX,CAAJ;AACD,KAFM,MAEA,IAAI,GAAG,KAAK,CAAZ,EAAe;AACpB,MAAA,CAAC,GAAG,MAAM,IAAK,CAAC,CAAC,GAAG,CAAL,KAAW,GAAG,GAAG,GAAjB,CAAX,CAAJ;AACD;AACF;;AAED,MAAI,CAAC,GAAG,CAAR,EAAW,CAAC,GAAG,CAAC,GAAG,GAAR;AAEX,MAAM,CAAC,GAAG,GAAG,KAAK,CAAR,GAAY,CAAZ,GAAgB,CAAC,GAAG,GAAG,GAAP,IAAc,GAAxC;AACA,MAAM,GAAG,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAAZ;AAEA,SAAO;AAAE,IAAA,CAAC,EAAE,GAAG,CAAC,CAAD,CAAR;AAAa,IAAA,CAAC,EAAE,GAAG,CAAC,CAAD,CAAnB;AAAwB,IAAA,CAAC,EAAE,GAAG,CAAC,CAAD,CAA9B;AAAmC,IAAA,CAAC,EAAE,IAAI,CAAC;AAA3C,GAAP;AACD;;AAEK,SAAU,UAAV,CAAsB,IAAtB,EAAgC;AAAA,MAC5B,CAD4B,GACb,IADa,CAC5B,CAD4B;AAAA,MACzB,CADyB,GACb,IADa,CACzB,CADyB;AAAA,MACtB,CADsB,GACb,IADa,CACtB,CADsB;AAAA,MACnB,CADmB,GACb,IADa,CACnB,CADmB;AAGpC,MAAM,CAAC,GAAG,CAAC,GAAI,CAAC,GAAG,CAAJ,GAAQ,CAAvB;AAEA,MAAM,MAAM,GAAG,CAAC,KAAK,CAAN,IAAW,CAAC,KAAK,CAAjB,GAAqB,CAArB,GAAyB,CAAC,CAAC,GAAG,CAAL,IAAU,IAAI,CAAC,GAAL,CAAS,CAAT,EAAY,IAAI,CAAhB,CAAlD;AAEA,SAAO;AAAE,IAAA,CAAC,EAAD,CAAF;AAAK,IAAA,CAAC,EAAE,MAAR;AAAgB,IAAA,CAAC,EAAD,CAAhB;AAAmB,IAAA,CAAC,EAAD;AAAnB,GAAP;AACD;;AAEK,SAAU,UAAV,CAAsB,GAAtB,EAA+B;AAAA,MAC3B,CAD2B,GACZ,GADY,CAC3B,CAD2B;AAAA,MACxB,CADwB,GACZ,GADY,CACxB,CADwB;AAAA,MACrB,CADqB,GACZ,GADY,CACrB,CADqB;AAAA,MAClB,CADkB,GACZ,GADY,CAClB,CADkB;AAGnC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAL,CAAS,CAAT,EAAY,IAAI,CAAhB,CAAlB;AAEA,MAAM,MAAM,GAAG,CAAC,KAAK,CAAN,GAAU,CAAV,GAAc,IAAK,IAAI,CAAJ,GAAQ,CAA1C;AAEA,SAAO;AAAE,IAAA,CAAC,EAAD,CAAF;AAAK,IAAA,CAAC,EAAE,MAAR;AAAgB,IAAA,CAAC,EAAD,CAAhB;AAAmB,IAAA,CAAC,EAAD;AAAnB,GAAP;AACD;;AAEK,SAAU,SAAV,CAAqB,IAArB,EAA+B;AACnC,wBAAe,IAAI,CAAC,CAApB,eAA0B,IAAI,CAAC,CAA/B,eAAqC,IAAI,CAAC,CAA1C,eAAgD,IAAI,CAAC,CAArD;AACD;;AAEK,SAAU,QAAV,CAAoB,IAApB,EAA8B;AAClC,SAAO,SAAS,iCAAM,IAAN;AAAY,IAAA,CAAC,EAAE;AAAf,KAAhB;AACD;;AAEK,SAAU,SAAV,CAAqB,IAArB,EAA+B;AACnC,MAAM,KAAK,GAAG,SAAR,KAAQ,CAAC,CAAD,EAAc;AAC1B,QAAM,CAAC,GAAG,IAAI,CAAC,KAAL,CAAW,CAAX,EAAc,QAAd,CAAuB,EAAvB,CAAV;AACA,WAAO,CAAC,KAAK,MAAL,CAAY,CAAZ,EAAe,IAAI,CAAC,CAAC,MAArB,IAA+B,CAAhC,EAAmC,WAAnC,EAAP;AACD,GAHD;;AAKA,oBAAW,CACT,KAAK,CAAC,IAAI,CAAC,CAAN,CADI,EAET,KAAK,CAAC,IAAI,CAAC,CAAN,CAFI,EAGT,KAAK,CAAC,IAAI,CAAC,CAAN,CAHI,EAIT,KAAK,CAAC,IAAI,CAAC,KAAL,CAAW,IAAI,CAAC,CAAL,GAAS,GAApB,CAAD,CAJI,EAKT,IALS,CAKJ,EALI,CAAX;AAMD;;AAEK,SAAU,SAAV,CAAqB,GAArB,EAA6B;AACjC,MAAM,IAAI,GAAG,oBAAM,GAAG,CAAC,KAAJ,CAAU,CAAV,CAAN,EAAoB,CAApB,EAAuB,GAAvB,CAA2B,UAAC,CAAD;AAAA,WAAe,QAAQ,CAAC,CAAD,EAAI,EAAJ,CAAvB;AAAA,GAA3B,CAAb;AAEA,SAAO;AACL,IAAA,CAAC,EAAE,IAAI,CAAC,CAAD,CADF;AAEL,IAAA,CAAC,EAAE,IAAI,CAAC,CAAD,CAFF;AAGL,IAAA,CAAC,EAAE,IAAI,CAAC,CAAD,CAHF;AAIL,IAAA,CAAC,EAAE,IAAI,CAAC,KAAL,CAAY,IAAI,CAAC,CAAD,CAAJ,GAAU,GAAX,GAAkB,GAA7B,IAAoC;AAJlC,GAAP;AAMD;;AAEK,SAAU,SAAV,CAAqB,GAArB,EAA6B;AACjC,MAAM,GAAG,GAAG,SAAS,CAAC,GAAD,CAArB;AACA,SAAO,UAAU,CAAC,GAAD,CAAjB;AACD;;AAEK,SAAU,SAAV,CAAqB,IAArB,EAA+B;AACnC,SAAO,SAAS,CAAC,UAAU,CAAC,IAAD,CAAX,CAAhB;AACD;;AAEK,SAAU,QAAV,CAAoB,GAApB,EAA+B;AACnC,MAAI,GAAG,CAAC,UAAJ,CAAe,GAAf,CAAJ,EAAyB;AACvB,IAAA,GAAG,GAAG,GAAG,CAAC,KAAJ,CAAU,CAAV,CAAN;AACD;;AAED,EAAA,GAAG,GAAG,GAAG,CAAC,OAAJ,CAAY,eAAZ,EAA6B,GAA7B,CAAN;;AAEA,MAAI,GAAG,CAAC,MAAJ,KAAe,CAAf,IAAoB,GAAG,CAAC,MAAJ,KAAe,CAAvC,EAA0C;AACxC,IAAA,GAAG,GAAG,GAAG,CAAC,KAAJ,CAAU,EAAV,EAAc,GAAd,CAAkB,UAAA,CAAC;AAAA,aAAI,CAAC,GAAG,CAAR;AAAA,KAAnB,EAA8B,IAA9B,CAAmC,EAAnC,CAAN;AACD;;AAED,MAAI,GAAG,CAAC,MAAJ,KAAe,CAAnB,EAAsB;AACpB,IAAA,GAAG,GAAG,qBAAO,GAAP,EAAY,CAAZ,EAAe,GAAf,CAAN;AACD,GAFD,MAEO;AACL,IAAA,GAAG,GAAG,qBAAO,qBAAO,GAAP,EAAY,CAAZ,CAAP,EAAuB,CAAvB,EAA0B,GAA1B,CAAN;AACD;;AAED,SAAO,WAAI,GAAJ,EAAU,WAAV,GAAwB,MAAxB,CAA+B,CAA/B,EAAkC,CAAlC,CAAP;AACD;;AAEK,SAAU,aAAV,CACJ,QADI,EAEJ,MAFI,EAGJ,YAHI,EAGsC;AAE1C,SAAO,QAAQ,CAAC,OAAT,CAAiB,oCAAjB,EAAuD,UAAA,CAAC,EAAG;AAChE,WAAO,UAAU,CAAC,CAAD,EAAI,MAAJ,EAAY,YAAZ,CAAV,IAAuC,CAA9C;AACD,GAFM,EAEJ,OAFI,CAEI,2BAFJ,EAEiC,UAAA,CAAC,EAAG;AAC1C,WAAO,UAAU,MAAM,CAAC,MAAP,CAAc,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAF,CAAU,QAAV,EAAoB,EAApB,CAAD,CAAT,CAAvB,EAA4D,KAA5D,CAAkE,CAAlE,EAAqE,CAArE,EAAwE,IAAxE,CAA6E,GAA7E,CAAjB;AACD,GAJM,CAAP;AAKD;;AAEK,SAAU,QAAV,CAAoB,IAApB,EAA8B;AAClC,SAAO,CAAC,IAAI,CAAC,CAAL,IAAU,EAAX,KAAkB,IAAI,CAAC,CAAL,IAAU,CAA5B,IAAiC,IAAI,CAAC,CAA7C;AACD;AAED;;;;;AAKG;;;AACG,SAAU,aAAV,CAAyB,EAAzB,EAAmC,EAAnC,EAA2C;AAAA,eAChC,0BAAM,QAAQ,CAAC,EAAD,CAAd,CADgC;AAAA;AAAA,MACtC,EADsC;;AAAA,gBAEhC,0BAAM,QAAQ,CAAC,EAAD,CAAd,CAFgC;AAAA;AAAA,MAEtC,EAFsC;;AAI/C,SAAO,CAAC,IAAI,CAAC,GAAL,CAAS,EAAT,EAAa,EAAb,IAAmB,IAApB,KAA6B,IAAI,CAAC,GAAL,CAAS,EAAT,EAAa,EAAb,IAAmB,IAAhD,CAAP;AACD", "sourcesContent": ["// Utilities\nimport { consoleWarn } from './console'\nimport { chunk, padEnd } from './helpers'\nimport { toXYZ } from './color/transformSRGB'\n\n// Types\nimport { VuetifyThemeVariant } from 'types/services/theme'\n\nexport type ColorInt = number\nexport type XYZ = [number, number, number]\nexport type LAB = [number, number, number]\nexport type HSV = { h: number, s: number, v: number }\nexport type HSVA = HSV & { a: number }\nexport type RGB = { r: number, g: number, b: number }\nexport type RGBA = RGB & { a: number }\nexport type HSL = { h: number, s: number, l: number }\nexport type HSLA = HSL & { a: number }\nexport type Hex = string\nexport type Hexa = string\nexport type Color = string | number | {}\n\nexport function isCssColor (color?: string | false): boolean {\n  return !!color && !!color.match(/^(#|var\\(--|(rgb|hsl)a?\\()/)\n}\n\nexport function colorToInt (color: Color): ColorInt {\n  let rgb\n\n  if (typeof color === 'number') {\n    rgb = color\n  } else if (typeof color === 'string') {\n    let c = color[0] === '#' ? color.substring(1) : color\n    if (c.length === 3) {\n      c = c.split('').map(char => char + char).join('')\n    }\n    if (c.length !== 6) {\n      consoleWarn(`'${color}' is not a valid rgb color`)\n    }\n    rgb = parseInt(c, 16)\n  } else {\n    throw new TypeError(`Colors can only be numbers or strings, recieved ${color == null ? color : color.constructor.name} instead`)\n  }\n\n  if (rgb < 0) {\n    consoleWarn(`Colors cannot be negative: '${color}'`)\n    rgb = 0\n  } else if (rgb > 0xffffff || isNaN(rgb)) {\n    consoleWarn(`'${color}' is not a valid rgb color`)\n    rgb = 0xffffff\n  }\n\n  return rgb\n}\n\nexport function classToHex (\n  color: string,\n  colors: Record<string, Record<string, string>>,\n  currentTheme: Partial<VuetifyThemeVariant>,\n): string {\n  const [colorName, colorModifier] = color\n    .toString().trim().replace('-', '').split(' ', 2) as (string | undefined)[]\n\n  let hexColor = ''\n  if (colorName && colorName in colors) {\n    if (colorModifier && colorModifier in colors[colorName]) {\n      hexColor = colors[colorName][colorModifier]\n    } else if ('base' in colors[colorName]) {\n      hexColor = colors[colorName].base\n    }\n  } else if (colorName && colorName in currentTheme) {\n    hexColor = currentTheme[colorName] as string\n  }\n\n  return hexColor\n}\n\nexport function intToHex (color: ColorInt): string {\n  let hexColor: string = color.toString(16)\n\n  if (hexColor.length < 6) hexColor = '0'.repeat(6 - hexColor.length) + hexColor\n\n  return '#' + hexColor\n}\n\nexport function colorToHex (color: Color): string {\n  return intToHex(colorToInt(color))\n}\n\n/**\n * Converts HSVA to RGBA. Based on formula from https://en.wikipedia.org/wiki/HSL_and_HSV\n *\n * @param color HSVA color as an array [0-360, 0-1, 0-1, 0-1]\n */\nexport function HSVAtoRGBA (hsva: HSVA): RGBA {\n  const { h, s, v, a } = hsva\n  const f = (n: number) => {\n    const k = (n + (h / 60)) % 6\n    return v - v * s * Math.max(Math.min(k, 4 - k, 1), 0)\n  }\n\n  const rgb = [f(5), f(3), f(1)].map(v => Math.round(v * 255))\n\n  return { r: rgb[0], g: rgb[1], b: rgb[2], a }\n}\n\n/**\n * Converts RGBA to HSVA. Based on formula from https://en.wikipedia.org/wiki/HSL_and_HSV\n *\n * @param color RGBA color as an array [0-255, 0-255, 0-255, 0-1]\n */\nexport function RGBAtoHSVA (rgba: RGBA): HSVA {\n  if (!rgba) return { h: 0, s: 1, v: 1, a: 1 }\n\n  const r = rgba.r / 255\n  const g = rgba.g / 255\n  const b = rgba.b / 255\n  const max = Math.max(r, g, b)\n  const min = Math.min(r, g, b)\n\n  let h = 0\n\n  if (max !== min) {\n    if (max === r) {\n      h = 60 * (0 + ((g - b) / (max - min)))\n    } else if (max === g) {\n      h = 60 * (2 + ((b - r) / (max - min)))\n    } else if (max === b) {\n      h = 60 * (4 + ((r - g) / (max - min)))\n    }\n  }\n\n  if (h < 0) h = h + 360\n\n  const s = max === 0 ? 0 : (max - min) / max\n  const hsv = [h, s, max]\n\n  return { h: hsv[0], s: hsv[1], v: hsv[2], a: rgba.a }\n}\n\nexport function HSVAtoHSLA (hsva: HSVA): HSLA {\n  const { h, s, v, a } = hsva\n\n  const l = v - (v * s / 2)\n\n  const sprime = l === 1 || l === 0 ? 0 : (v - l) / Math.min(l, 1 - l)\n\n  return { h, s: sprime, l, a }\n}\n\nexport function HSLAtoHSVA (hsl: HSLA): HSVA {\n  const { h, s, l, a } = hsl\n\n  const v = l + s * Math.min(l, 1 - l)\n\n  const sprime = v === 0 ? 0 : 2 - (2 * l / v)\n\n  return { h, s: sprime, v, a }\n}\n\nexport function RGBAtoCSS (rgba: RGBA): string {\n  return `rgba(${rgba.r}, ${rgba.g}, ${rgba.b}, ${rgba.a})`\n}\n\nexport function RGBtoCSS (rgba: RGBA): string {\n  return RGBAtoCSS({ ...rgba, a: 1 })\n}\n\nexport function RGBAtoHex (rgba: RGBA): Hex {\n  const toHex = (v: number) => {\n    const h = Math.round(v).toString(16)\n    return ('00'.substr(0, 2 - h.length) + h).toUpperCase()\n  }\n\n  return `#${[\n    toHex(rgba.r),\n    toHex(rgba.g),\n    toHex(rgba.b),\n    toHex(Math.round(rgba.a * 255)),\n  ].join('')}`\n}\n\nexport function HexToRGBA (hex: Hex): RGBA {\n  const rgba = chunk(hex.slice(1), 2).map((c: string) => parseInt(c, 16))\n\n  return {\n    r: rgba[0],\n    g: rgba[1],\n    b: rgba[2],\n    a: Math.round((rgba[3] / 255) * 100) / 100,\n  }\n}\n\nexport function HexToHSVA (hex: Hex): HSVA {\n  const rgb = HexToRGBA(hex)\n  return RGBAtoHSVA(rgb)\n}\n\nexport function HSVAtoHex (hsva: HSVA): Hex {\n  return RGBAtoHex(HSVAtoRGBA(hsva))\n}\n\nexport function parseHex (hex: string): Hex {\n  if (hex.startsWith('#')) {\n    hex = hex.slice(1)\n  }\n\n  hex = hex.replace(/([^0-9a-f])/gi, 'F')\n\n  if (hex.length === 3 || hex.length === 4) {\n    hex = hex.split('').map(x => x + x).join('')\n  }\n\n  if (hex.length === 6) {\n    hex = padEnd(hex, 8, 'F')\n  } else {\n    hex = padEnd(padEnd(hex, 6), 8, 'F')\n  }\n\n  return `#${hex}`.toUpperCase().substr(0, 9)\n}\n\nexport function parseGradient (\n  gradient: string,\n  colors: Record<string, Record<string, string>>,\n  currentTheme: Partial<VuetifyThemeVariant>,\n) {\n  return gradient.replace(/([a-z]+(\\s[a-z]+-[1-5])?)(?=$|,)/gi, x => {\n    return classToHex(x, colors, currentTheme) || x\n  }).replace(/(rgba\\()#[0-9a-f]+(?=,)/gi, x => {\n    return 'rgba(' + Object.values(HexToRGBA(parseHex(x.replace(/rgba\\(/, '')))).slice(0, 3).join(',')\n  })\n}\n\nexport function RGBtoInt (rgba: RGBA): ColorInt {\n  return (rgba.r << 16) + (rgba.g << 8) + rgba.b\n}\n\n/**\n * Returns the contrast ratio (1-21) between two colors.\n *\n * @param c1 First color\n * @param c2 Second color\n */\nexport function contrastRatio (c1: RGBA, c2: RGBA): number {\n  const [, y1] = toXYZ(RGBtoInt(c1))\n  const [, y2] = toXYZ(RGBtoInt(c2))\n\n  return (Math.max(y1, y2) + 0.05) / (Math.min(y1, y2) + 0.05)\n}\n"], "sourceRoot": "", "file": "colorUtils.js"}
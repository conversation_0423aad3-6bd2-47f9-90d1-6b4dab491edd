<template>
    <v-dialog v-model="updateDialog" persistent max-width="720px">
        <v-card>
            <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
                {{ operaObj.ID ? $t('DFM_GYGL.editProcess') : $t('DFM_GYGL.addProcess') }}
                <v-icon @click="updateDialog = false">mdi-close</v-icon>
            </v-card-title>
            <v-card-text>
                <v-form ref="form" v-model="valid" class="mt-8">
                    <v-row>
                        <!-- 工序名称 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.ProcName" :rules="rules.ProcName" :label="$t('$vuetify.dataTable.DFM_GYGL.ProcName')" required dense outlined></v-text-field>
                        </v-col>
                        <!-- 工序编码 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.ProcCode" :rules="rules.ProcCode" :label="$t('$vuetify.dataTable.DFM_GYGL.ProcCode')" required dense outlined></v-text-field>
                        </v-col>
                        <!-- 工序组 -->
                        <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.ParentId"
                                :rules="rules.ParentId"
                                :items="processTypeList"
                                item-text="ItemName"
                                item-value="ItemValue"
                                :label="$t('$vuetify.dataTable.DFM_GYGL.ParentId')"
                                persistent-hint
                                dense
                                outlined
                            ></v-select>
                            <!-- <v-text-field v-model="form.ProcType" :rules="rules.ProcType" :label="$t('$vuetify.dataTable.DFM_GYGL.ProcType')" required dense outlined></v-text-field> -->
                        </v-col>
                        <!-- 是否创建工单 -->
                        <!-- <v-col :cols="12" :lg="6">
                            <v-select
                                v-model="form.IsCreateOrder"
                                :rules="rules.IsCreateOrder"
                                :items="isCreateOrderList"
                                item-text="name"
                                item-value="type"
                                :label="$t('$vuetify.dataTable.DFM_GYGL.IsCreateOrder')"
                                dense
                                outlined
                            ></v-select>
                        </v-col> -->
                        <!-- 标准加工时间 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.StandardTime" :rules="rules.StandardTime" :label="$t('$vuetify.dataTable.DFM_GYGL.StandardTime')" required dense outlined></v-text-field>
                        </v-col>
                        <!-- 标准加工单位 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.StandardUom" :rules="rules.StandardUom" :label="$t('$vuetify.dataTable.DFM_GYGL.StandardUom')" required dense outlined></v-text-field>
                        </v-col>
                         <!-- 准备时间 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.PrepareTime" :rules="rules.PrepareTime" :label="$t('$vuetify.dataTable.DFM_GYGL.PrepareTime')" required dense outlined></v-text-field>
                        </v-col>
                        <!-- 运输时间 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.TransTime" :rules="rules.TransTime" :label="$t('$vuetify.dataTable.DFM_GYGL.TransTime')" required dense outlined></v-text-field>
                        </v-col>
                        <!-- 标准用工人数 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model.number="form.StandardPeronNum" :rules="rules.StandardPeronNum" :label="$t('$vuetify.dataTable.DFM_GYGL.StandardPeronNum')" required dense outlined></v-text-field>
                        </v-col>
                         <!-- 设计产能 -->
                        <v-col :cols="12" :lg="6">
                            <v-text-field v-model="form.UnitCapacity" :rules="rules.UnitCapacity" :label="$t('$vuetify.dataTable.DFM_GYGL.UnitCapacity')" required dense outlined></v-text-field>
                        </v-col>
                        <!-- 描述  -->
                        <v-col :cols="12" :lg="12">
                            <v-textarea v-model="form.Description" :label="$t('$vuetify.dataTable.DFM_GYGL.Description')" :rows="2" dense outlined></v-textarea>
                        </v-col>
                    </v-row>
                </v-form>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
                <v-btn color="normal" @click="updateDialog = false">{{ $t('GLOBAL._GB') }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { SaveForm } from '@/api/factoryPlant/process.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        processTypeList: {
            type: Array,
            default: () => []
        },
        isCreateOrderList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            checkbox: true,
            valid: true,
            updateDialog: false,
            form: {
                ID: '',
                ProcName: '',
                ProcCode: '',
                ParentId: '',
                Description: '',
                StandardTime: '',
                StandardUom: '',
                PrepareTime: '',
                TransTime: '',
                StandardPeronNum: 0,
                // IsCreateOrder: '0',
                UnitCapacity: ''
            },
            rules: {
                ProcName: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                ParentId: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                ProcCode: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                StandardPeronNum: [v => (typeof v == "number" && v > -1 && v%1 == 0) || "请输入大于-1的整数"]
            },
            parentCodeList: []
        };
    },
    watch: {
        updateDialog: {
            handler(curVal) {
                if(curVal) {
                    for (const key in this.form) {
                        if (Object.hasOwnProperty.call(this.form, key)) {
                            this.form[key] = this.operaObj[key];
                        }
                    }
                    this.form.StandardPeronNum = this.operaObj.StandardPeronNum || 0;
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                const { StandardPeronNum } = this.form
                const res = await SaveForm({...this.form, ProcType: 2, StandardPeronNum: StandardPeronNum || 0});
                const { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    this.updateDialog = !this.checkbox;
                    this.$emit('handlePopup', 'refresh'); 
                    this.getDataList();
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>

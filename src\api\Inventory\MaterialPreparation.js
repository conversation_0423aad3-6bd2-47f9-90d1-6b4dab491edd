import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Inventory'

//room下拉数据
export function GetRoomSelectList(data) {
    const api = '/api/MaterialPreparationView/GetRoomSelectList'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetRoomSelectListCLBL(data) {
    const api = '/api/MaterialPreparationView/GetRoomSelectListCLBL'
    return getRequestResources(baseURL, api, 'post', data);
}
//url传参获取
export function GetRoomSelectListCLBLByUrl(data) {
    const api = '/api/MaterialPreparationView/GetRoomCLBLByEquipmentID'
    return getRequestResources(baseURL, api, 'post', data);
}
//room下拉数据PG
export function GetRoomSelectListByPG(data) {
    const api = '/api/MaterialPreparationView/GetRoomSelectListByPG'
    return getRequestResources(baseURL, api, 'post', data);
}
//原料加工厂
export function GetRoomSelectListByYLJG(data) {
    const api = '/api/MaterialPreparationView/GetRoomSelectListByPG'
    return getRequestResources(baseURL, api, 'post', data);
}
//首页table数据 拼锅
export function GetMaterialPreparationView(data) {
    const api = '/api/MaterialPreparationView/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

//首页table数据 称量备料
export function GetMaterialPreparationViewCLBL(data) {
    const api = '/api/MaterialPreparationView/GetPageList_CLBL'
    return getRequestResources(baseURL, api, 'post', data);
}

export function Get_CLBLNew(data) {
    const api = '/api/MaterialPreparationView/Get_CLBLNew'
    return getRequestResources(baseURL, api, 'post', data);
}

export function Get_PGNew(data) {
    const api = '/api/MaterialPreparationView/Get_CLBLNew'
    return getRequestResources(baseURL, api, 'post', data);
}

export function Get_YLBQNew(data) {
    const api = '/api/MaterialPreparationView/Get_YLLabelNew'
    return getRequestResources(baseURL, api, 'post', data);
}

//跳转tab工序数据 pg
export function GetSegmentList(data) {
    const api = '/api/MaterialPreparationView/GetSegmentList'
    return getRequestResources(baseURL, api, 'post', data);
}
//跳转tab工序数据 clbl
export function GetSegmentListCLBL(data) {
    const api = '/api/MaterialPreparationView/GetSegmentList_CLBL'
    return getRequestResources(baseURL, api, 'post', data);
}


//跳转tab批次数据pg
export function GetByProOrderID(data) {
    const api = '/api/MaterialPreparationView/GetByProOrderID'
    return getRequestResources(baseURL, api, 'post', data, true);
}
//跳转tab批次数据 clbl
export function GetByProOrderIDCLBL(data) {
    const api = '/api/MaterialPreparationView/GetByProOrderID_CLBL'
    return getRequestResources(baseURL, api, 'post', data, true);
}
//跳转tab物料数据 pg
export function GetListByBatchID(data) {
    const api = '/api/MaterialPreparationView/GetListByBatchID'
    return getRequestResources(baseURL, api, 'post', data, true);
}
//跳转tab物料数据 clbl
export function GetListByBatchIDCLBL(data) {
    const api = '/api/MaterialPreparationView/GetListByBatchID_CLBL'
    return getRequestResources(baseURL, api, 'post', data, true);
}
//根据物料跳转Table数据 pg
export function GetMPreparationII(data) {
    const api = '/api/MaterialPreparationView/GetMPreparationII'
    return getRequestResources(baseURL, api, 'post', data);
}
//根据物料跳转Table数据 clbl
export function GetMPreparationIICLBL(data) {
    const api = '/api/MaterialPreparationView/GetMPreparationII_CLBL'
    return getRequestResources(baseURL, api, 'post', data);
}
//拼锅根据物料跳转Table数据
export function GetPageListByMaterialII(data) {
    const api = '/api/MaterialPreparationView/GetPageListByMaterialII'
    return getRequestResources(baseURL, api, 'post', data);
}

//拼锅根据批次跳转Table数据
export function GetPageListByBatchIDSII(data) {
    const api = '/api/MaterialPreparationView/GetPageListByBatchIDSII'
    return getRequestResources(baseURL, api, 'post', data);
}

//称量备料根据物料跳转Table数据
export function GetPageListByMaterial(data) {
    const api = '/api/MaterialPreparationView/GetPageListByMaterial_CLBL'
    return getRequestResources(baseURL, api, 'post', data);
}

//称量备料根据批次跳转Table数据
export function GetPageListByBatchIDS(data) {
    const api = '/api/MaterialPreparationView/GetPageListByBatchIDS_CLBL'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetPageListByBatchIDSByID(data) {
    const api = '/api/MaterialPreparationView/GetPageListByBatchIDS_CLBLBYID'
    return getRequestResources(baseURL, api, 'post', data);
}

//最后一个页面 获取容器以及上下选择
export function GetConSelectList(data) {
    const api = '/api/MaterialPreparationView/ContainerSelectList'
    return getRequestResources(baseURL, api, 'post', data, true);
}

//按批次最后一个页面（上部表格）
export function GetPageListMaterialPreTop(data) {
    const api = '/api/MaterialPreparationView/GetPageListMaterialPreTop'
    return getRequestResources(baseURL, api, 'post', data);
}
//按物料最后一个页面（上部表格）
export function GetPageListNewMaterialPreTop(data) {
    const api = '/api/MaterialPreparationView/GetPageListNewMaterialPreTop'
    return getRequestResources(baseURL, api, 'post', data);
}
//按批次最后一个页面（下部表格）
export function GetPageListMaterialPreDown(data) {
    const api = '/api/MaterialPreparationView/GetDown'
    return getRequestResources(baseURL, api, 'post', data);
}
//按批次最后一个页面（下部表格） pg
export function GetPageListNewMaterialPreDown(data) {
    const api = '/api/MaterialPreparationView/GetNewDown'
    return getRequestResources(baseURL, api, 'post', data);
}
//按批次最后一个页面（下部表格） clbl
export function GetPageListNewMaterialPreDownCLBL(data) {
    const api = '/api/MaterialPreparationView/GetNewDown_CLBL'
    return getRequestResources(baseURL, api, 'post', data);
}
//按批次添加托盘
export function FirstAddPallet(data) {
    const api = '/api/MaterialPreparationView/AddPallet'
    return getRequestResources(baseURL, api, 'post', data);
}

export function TransferFullBag(data) {
    const api = '/api/MaterialPreparationView/MPreparationTransfer_FullBag'
    return getRequestResources(baseURL, api, 'post', data);
}
export function TransferFullAmount(data) {
    const api = '/api/MaterialPreparationView/MPreparationTransfer_FullAmount'
    return getRequestResources(baseURL, api, 'post', data);
}
export function TransferFullAmountMaterial(data) {
    const api = '/api/MaterialPreparationView/MPreparationTransfer_NewFullAmount'
    return getRequestResources(baseURL, api, 'post', data);
}
export function TransferPartialBag(data) {
    const api = '/api/MaterialPreparationView/MPreparationTransfer_PartialBag'
    return getRequestResources(baseURL, api, 'post', data);
}
export function PartialBagMerge(data) {
    const api = '/api/MaterialPreparationView/MPreparationTransfer_Merge'
    return getRequestResources(baseURL, api, 'post', data);
}

export function CompletePallet(data) {
    const api = '/api/MaterialPreparationView/MPreparationTransfer_CompletePallet'
    return getRequestResources(baseURL, api, 'post', data);
}
export function OpenPallet(data) {
    const api = '/api/MaterialPreparationView/MPreparationTransfer_OpenPallet'
    return getRequestResources(baseURL, api, 'post', data);
}

export function DeletePallet(data) {
    const api = '/api/MaterialPreparationView/MPreparationTransfer_DeletePallet'
    return getRequestResources(baseURL, api, 'post', data);
}
export function RemovePallet(data) {
    const api = '/api/MaterialPreparationView/MPreparationTransfer_Remove'
    return getRequestResources(baseURL, api, 'post', data);
}
export function PalletIsFinish_PG(data) {
    const api = '/api/MaterialPreparationView/IsFinish_PG'
    return getRequestResources(baseURL, api, 'post', data);
}
export function RemovePalletMaterial(data) {
    const api = '/api/MaterialPreparationView/MPreparationNewTransfer_Remove'
    return getRequestResources(baseURL, api, 'post', data);
}
//读取电子秤数据
export function InventoryReadCallData(data) {
    const api = '/api/MaterialInventory/ReadCallData'
    return getRequestResources(baseURL, api, 'post', data, true, true);
}

//电子称下拉框
export function GetScaleSelect(data) {
    const api = '/api/MaterialInventory/GetScaleList'
    return getRequestResources(baseURL, api, 'post', data, true);
}

export function GetConsumlList(data) {
    const api = '/api/MaterialPreparationView/GetMateriaConsumlList'
    return getRequestResources(baseURL, api, 'post', data);
}


export function GetReprintSave(data) {
    const api = '/api/MaterialPreparationView/ReprintSave'
    return getRequestResources(baseURL, api, 'post', data);
}

//原料加工厂
export function GetBatchCodeByProLine(data) {
    const api = '/ppm/PoProducedExecution/GetBatchCodeByProLine'
    return getRequestResources(baseURL, api, 'post', data);
}
//原料加工厂列表
export function GetPageListByProIds(data) {
    const api = '/api/MaterialLabelView/GetPageListByProIds'
    return getRequestResources(baseURL, api, 'post', data);
}

//原料加工厂工单列表
export function Get_SelectPro(data) {
    const api = '/api/MaterialPreparationView/Get_SelectPro'
    return getRequestResources(baseURL, api, 'post', data);
}
//原料加工厂物料列表
export function Get_SelectMaterial(data) {
    const api = '/api/MaterialPreparationView/Get_SelectMaterial'
    return getRequestResources(baseURL, api, 'post', data);
}
//拼锅最后界面的下拉框
export function GetSelectPrinit_PalletPG(data) {
    const api = '/api/MaterialInventory/GetSelectPrinit_Pallet'
    return getRequestResources(baseURL, api, 'post', data, true);
}
//拼锅最后界面的下拉框筛选节点
export function SelectPrinit_PalletPG(data) {
    const api = '/api/MaterialInventory/GetSelectPrinit_PalletByPG'
    return getRequestResources(baseURL, api, 'post', data, true);
}
export function MygetSSCC(data) {
    const api = '/api/MaterialPreparationView/GetInventState'
    return getRequestResources(baseURL, api, 'post', data);
}


export function GetPreparation_PrintMinLable(data) {
    const api = '/api/MaterialPreparationView/Preparation_PrintMinLable'
    return getRequestResources(baseURL, api, 'post', data);
}
export function GetMateriaConsumlListPG(data) {
    const api = '/api/MaterialPreparationView/GetMateriaConsumlListPG'
    return getRequestResources(baseURL, api, 'post', data);
}
export function PGReprintSavePG(data) {
    const api = '/api/MaterialPreparationView/ReprintSavePG'
    return getRequestResources(baseURL, api, 'post', data);
}

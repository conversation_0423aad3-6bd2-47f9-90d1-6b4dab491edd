import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_ORDER'


/**
 * 周计划分页查询
 * @param {查询条件} data
 */
export function getWeekScheduleList(data) {
    const api = '/ppm/WeekSchedule/GetPageList'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 周计划Bom查询
 * @param {查询条件} data
 */
export function getWeekScheduleBomList(data) {
    const api = '/ppm/WeekSchedule/GetBomList'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 保存周计划
 * @param data
 */
export function saveWeekScheduleForm(data) {
    const api = '/ppm/WeekSchedule/SaveForm'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 获取周计划详情
 * @param {Id}
 */
export function getWeekScheduleDetail(id) {
    const api = '/ppm/WeekSchedule/GetEntity/'+id;
    return getRequestResources(baseURL, api, 'get')
}

/**
 * 获取周计划详情
 * @param {Id}
 */
export function getWeekScheduleBomDetail(id) {
    const api = '/ppm/WeekSchedule/GetBomEntity/'+id;
    return getRequestResources(baseURL, api, 'get')
}
/**
 * 删除周计划
 * @param {主键} data
 */
export function delWeekSchedule(data) {
    const api = '/ppm/WeekSchedule/Delete'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 获取工单列表
 * @param {data}
 */
export function getPOPageList(data) {
    const api = '/ppm/WeekSchedule/GetPOPageList';
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 获取工单物料需求列表
 * @param {data}
 */
export function getPOConsumeRequirementlList(data) {
    const api = '/ppm/WeekSchedule/GetPOConsumeRequirementlList';
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 更新批次工单
 * @param {主键} data
 */
export function updatelProductionOrder(data) {
    const api = '/ppm/WeekSchedule/UpdateBatch'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 取消批次工单
 * @param {主键} data
 */
export function cancelProductionOrder(data) {
    const api = '/ppm/WeekSchedule/CancelBatch'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * BOM物料替换
 * @param {主键} data
 */
export function changeMaterial(data) {
    const api = '/ppm/WeekSchedule/ChangeMaterial'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 获取物料批次清单
 * @param {主键} data
 */
export function getMaterialLotList(data) {
    const api = '/ppm/WeekSchedule/GetMaterialLotList'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 获取物料批次清单
 * @param {主键} data
 */
export function changePoMaterialLot(data) {
    const api = '/ppm/WeekSchedule/ChangePOMaterial'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 导出入周计划
 */
export function importWeekSchedule(data) {
    console.log(baseURL);
    const api = '/ppm/WeekSchedule/ImportData';
    return getRequestResources(baseURL, api, 'post', data);
}

/**
 * 导出周计划
 */
export function exportWeekSchedule(data) {
    const api = '/ppm/WeekSchedule/ExportData';
    return getRequestResources(baseURL, api, 'post', data);
}

// 产线
export function getLineList(data) {
    const api = `/ppm/StandardPeriodLot/GetLineList?areaCode=${data.areaCode}`
    return getRequestResources(baseURL, api, 'post', null);
}

/**
 * ProductionOrder查询
 * @param {查询条件} data
 */
export function getProductionOrderList(data) {
    const api = '/ppm/WeekSchedule/GetProductionOrderList'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 获取周计划详情
 * @param {Id}
 */
export function getProductionOrderDetail(id) {
    const api = '/ppm/WeekSchedule/GetProductionOrderEntity/'+id;
    return getRequestResources(baseURL, api, 'get')
}

/**
 * 下发DCS
 * @param {data}
 */
export function downloadDCS(data) {
    const api = '/ppm/WeekSchedule/DownloadDCS';
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 获取批次拆分信息
 * @param {data}
 */
export function getSplitBatchInfo(data) {
    const api = '/ppm/WeekSchedule/GetSplitBatchInfo';
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 批次拆分
 * @param {data}
 */
export function splitBatch(data) {
    const api = '/ppm/WeekSchedule/SplitBatch';
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 获取新增批次工单信息
 * @param {data}
 */
export function getAddBatchInfo(data) {
    const api = '/ppm/WeekSchedule/GetAddBatchInfo';
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 新增批次工单
 * @param {data}
 */
export function addBatch(data) {
    const api = '/ppm/WeekSchedule/AddBatch';
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 替代物料查询
 * @param {查询条件} data
 */
export function getInsteadMaterialList(data) {
    const api = '/ppm/WeekSchedule/GetInsteadMaterialList'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 查询PO投入列表
 * @param {查询条件} data
 */
export function getProductionOrderConsumeList(data) {
    const api = '/ppm/WeekSchedule/GetPOConsumeActualList'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 查询PO产出列表
 * @param {查询条件} data
 */
export function getProductionOrderProduceList(data) {
    const api = '/ppm/WeekSchedule/GetPOProducedActualList'
    return getRequestResources(baseURL, api, 'post', data)
}
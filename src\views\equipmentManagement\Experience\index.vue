<template>
    <div class="dictionary-view">
        <TreeView :items="treeData" :title="$t('TPM_SBGL_SBTZGL._SBFL')" @clickClassTree="clickClassTree"></TreeView>
        <div class="dictionary-main">
            <SearchForm :searchinput="searchinputs" :show-from="showFrom" @searchForm="searchForm" />
            <v-card class="ma-1">
                <div class="form-btn-list">
                    <v-btn icon class="float-left mx-4" @click="showFrom = !showFrom">
                        <v-icon>{{ 'mdi-table-search' }}</v-icon>
                        {{ $t('GLOBAL._SSL') }}
                    </v-btn>
                    <v-btn icon color="primary" @click="RepastInfoGetPage">
                        <v-icon>mdi-cached</v-icon>
                    </v-btn>
                    <!-- <v-btn color="primary" @click="$refs.createRepast.showDialog = true">{{ $t('GLOBAL._XZ') }}</v-btn> -->
                </div>
                <Tables
                    :page-options="pageOptions"
                    :loading="loading"
                    :btn-list="btnList"
                    :tableHeight="showFrom ? 'calc(100vh - 230px)' : 'calc(100vh - 180px)'"
                    table-name="TPM_SBGL_WXJY"
                    :headers="ExperienceColum"
                    :desserts="desserts"
                    @selectePages="selectePages"
                    @tableClick="tableClick"
                    @itemSelected="SelectedItems"
                    @toggleSelectAll="SelectedItems"
                ></Tables>
                <createRepast ref="createRepast" :dialogType="dialogType" :rowtableItem="tableItem"></createRepast>
            </v-card>
        </div>

        <div class="loading-box" v-if="importLoading">
            <a-spin tip="导入中..." :spinning="importLoading"></a-spin>
        </div>
    </div>
</template>
<script>
import '@/views/equipmentManagement/Style.css';
import { ExperienceColum } from '@/columns/equipmentManagement/Experience.js';
import { GetDeviceCategoryTree } from '@/api/equipmentManagement/SpotCheckItem.js';
import { GetExperiencePageList, ExperienceDelete } from '@/api/equipmentManagement/Experience.js';
import { configUrl } from '@/config';
import { Message } from 'element-ui';
import equipment from '@/mixins/equipment';
export default {
    name: 'RepastModel',
    components: {
        createRepast: () => import('./components/createRepast.vue')
    },
    mixins: [equipment],
    data() {
        return {
            importLoading: false,
            // tree 字典数据
            loading: true,
            showFrom: false,
            treeData: [],
            papamstree: {
                DeviceCategoryId: '',
                Description: '',
                RepairRecordDesc: '',
                Keyword: '',
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            ExperienceColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            TypeList: [],
            SourceList: [],
            // 弹窗数据
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            FaultPosition: [],
            FaultProperty: [],
            RepairProperty: [],
            hasChildren: {} // 新增字典详情判断-子节点才能新增
        };
    },
    computed: {
        searchinputs() {
            return [
                {
                    value: '',
                    key: 'Description',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.ExceptionDesc'),
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'RepairRecordDesc',
                    icon: 'mdi-account-check',
                    label: this.$t('$vuetify.dataTable.TPM_SBGL_SBWXJL_WXMX.RepairProcess'),
                    placeholder: ''
                },
                {
                    value: '',
                    key: 'Keyword',
                    icon: 'mdi-account-check',
                    label: this.$t('GLOBAL._KEY'),
                    placeholder: ''
                }
            ];
        },
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'edit',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBGLZY_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBGLZY_DELETE'
                }
            ];
        }
    },
    async mounted() {
        this.TypeList = await this.$getNewDataDictionary('RepairOrderType');
        this.SourceList = await this.$getNewDataDictionary('RepairSource');
        let RepairUrgency = await this.$getNewDataDictionary('RepairUrgency');
        this.FaultPosition = await this.$getNewDataDictionary('FaultPosition');
        this.FaultProperty = await this.$getNewDataDictionary('FaultProperty');
        this.RepairProperty = await this.$getNewDataDictionary('Repair Property');
        this.$refs.createRepast.SbxxList.forEach(item => {
            switch (item.id) {
                case 'Type':
                    item.option = this.TypeList;
                    break;
                case 'Source':
                    item.option = this.SourceList;
                    break;
                case 'Urgency':
                    item.option = RepairUrgency;
                    break;
                case 'FaultCategory':
                    item.option = this.FaultPosition;
                    break;
                case 'RepairNature':
                    item.option = this.RepairProperty;
                    break;
            }
        });

        this.GetFactorylineTree();
    },
    methods: {
        // 获取树形数据
        async GetFactorylineTree() {
            let params = {};
            params.factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            const res = await GetDeviceCategoryTree(params);
            let { success, response } = res;
            if (success) {
                this.treeData = response || [];
                this.papamstree.DeviceCategoryId = this.treeData[0].id;
                this.RepastInfoGetPage();
            }
        },
        clickClassTree(val) {
            this.papamstree.pageIndex = 1;
            if (val.id == this.papamstree.DeviceCategoryId) {
                this.papamstree.DeviceCategoryId = '';
            } else {
                this.papamstree.DeviceCategoryId = val.id;
            }
            this.RepastInfoGetPage();
        },
        // 查询数据
        searchForm(value) {
            this.papamstree = Object.assign({}, this.papamstree, value);
            this.papamstree.pageIndex = 1;
            this.RepastInfoGetPage();
        },
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                ...this.papamstree
            };
            params.Factory = this.$route.query.Factory ? this.$route.query.Factory : '2010';
            this.loading = true;
            const res = await GetExperiencePageList(params);
            let { success, response } = res;
            response.data.forEach(item => {
                this.FaultPosition.forEach(it => {
                    if (item.FaultCategory == it.ItemValue) {
                        item.FaultCategory = it.ItemName;
                        item.FaultCategoryValue = it.ItemValue;
                    }
                });
                this.FaultProperty.forEach(it => {
                    if (item.FaultNature == it.ItemValue) {
                        item.FaultNature = it.ItemName;
                        item.FaultNatureValue = it.ItemValue;
                    }
                });
            });
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'edit':
                    this.$refs.createRepast.SbxxList.forEach(item => {
                        for (let k in this.tableItem) {
                            if (item.id == k) {
                                if (k == 'FaultCategory') {
                                    item.value = this.tableItem.FaultCategoryValue;
                                } else if (k == 'FaultNature') {
                                    item.value = this.tableItem.FaultNatureValue;
                                } else {
                                    item.value = this.tableItem[k];
                                }
                            }
                        }
                    });
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res = await ExperienceDelete(params);
                    if (res.success) {
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.RepastInfoGetPage();
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + [...item]);
            this.deleteList = [...item];
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        }
    }
};
</script>
<style lang="scss" scoped>
.dictionary-view {
    display: flex;

    .dictionary-main {
        width: 100%;
        overflow: auto;
    }
}

.loading-box {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($color: #000000, $alpha: 0.2);
    z-index: 999;
}
</style>

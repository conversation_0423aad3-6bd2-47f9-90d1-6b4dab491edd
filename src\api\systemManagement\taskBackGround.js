import request from '@/util/request';
import { configUrl } from '@/config';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_JOB; // 配置服务url

//获取定时任务列表
export function SchedulerCenterGetJobs(data) {
    return request({
        url: baseURL + '/job/SchedulerCenter/GetJobs',
        method: 'post',
        data
    });
}
// 新增
export function SchedulerCenterAddJob(data) {
    return request({
        url: baseURL + '/job/SchedulerCenter/AddJob',
        method: 'post',
        data
    });
}

// 更新
export function SchedulerCenterUpdateJob(data) {
    return request({
        url: baseURL + '/job/SchedulerCenter/UpdateJob',
        method: 'post',
        data
    });
}
// 启动
export function SchedulerCenterStartJob(data) {
    return request({
        url: baseURL + '/job/SchedulerCenter/StartJob',
        method: 'post',
        data
    });
}
// 暂停
export function SchedulerCenterPauseJob(data) {
    return request({
        url: baseURL + '/job/SchedulerCenter/PauseJob',
        method: 'post',
        data
    });
}
// 立即执行
export function SchedulerCenterRunJob(data) {
    return request({
        url: baseURL + '/job/SchedulerCenter/RunJob',
        method: 'post',
        data
    });
}
// 删除
export function SchedulerCenterDeleteJob(data) {
    return request({
        url: baseURL + '/job/SchedulerCenter/DeleteJob',
        method: 'post',
        data
    });
}

// 日志
export function SchedulerCenterGetJobLogs(data) {
    return request({
        url: baseURL + '/job/SchedulerCenter/GetJobLogs',
        method: 'post',
        data
    });
}
<template>
    <div class="">
        <v-tabs>
            <v-tab>
                {{ $t('TPM_SBGL_SBBJGL._CKQD') }}
            </v-tab>
            <v-tab-item>
                <v-card flat>
                    <v-row>
                        <v-col cols="12" md="8">
                            <div class="form-btn-list">
                                <v-btn icon color="primary" @click="RepastInfoGetPage">
                                    <v-icon>mdi-cached</v-icon>
                                </v-btn>

                                <v-btn color="primary" v-has="'SBBJGL_CK_PLDY'" @click="openPrint()">{{
                                    $t('GLOBAL._PLDY')
                                }}</v-btn>
                            </div>
                            <Tables singleSelect :page-options="pageOptions" :loading="loading" :btn-list="btnList"
                                tableHeight="calc(100vh - 245px)" table-name="TPM_SBGL_SBBJGL_CK"
                                :headers="RepairPlanColumQRcode" :desserts="desserts" @selectePages="selectePages"
                                @tableClick="tableClick" @itemSelected="SelectedItems" @toggleSelectAll="SelectedItems">
                            </Tables>
                        </v-col>
                        <v-col cols="12" md="4">
                            <v-form ref="fromQR" v-model="valid" class="pt-2">
                                <v-row>
                                    <v-col class="" cols="12">
                                        <v-text-field v-model="fromQR.Remark" outlined dense
                                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_RK.Remark')"></v-text-field>
                                    </v-col>
                                    <v-col class="" cols="12">
                                        <v-text-field v-model="fromQR.Code" outlined dense
                                            :label="$t('$vuetify.dataTable.TPM_SBGL_SBBJGL_RK.SparepartCode')"></v-text-field>
                                    </v-col>
                                    <v-col class="" cols="12">
                                        <v-text-field v-model="fromQR.BuyNum" type="Number" outlined dense
                                            :label="$t('TPM_SBGL_SBBJGL._BJSL')"></v-text-field>
                                    </v-col>
                                    <v-col class="text-right">
                                        <v-btn class="ml-2" color="primary" :disabled="!submitStatue" large fab
                                            @click="addSparePartlist">{{ $t('GLOBAL._QD') }}</v-btn>
                                    </v-col>
                                </v-row>
                            </v-form>
                        </v-col>
                    </v-row>
                </v-card>
                <createRepasts ref="createRepast"></createRepasts>
            </v-tab-item>
        </v-tabs>
        <QRcode ref="QRcode" @getQRcodesRes="getQRcodesRes"></QRcode>
        <v-dialog v-model="isShowPrint" persistent scrollable width="55%">
            <printPopup v-if="isShowPrint" @closePopup="isShowPrint = false" :PrintTemplateFn="PrintTemplateFn" />
        </v-dialog>
    </div>
</template>
<script>
import { mixins } from '@/util/mixins.js';
import { GetPageSparePartsList, addSparePart, getOutStorageData } from '@/api/equipmentManagement/sparePart.js';
import { RepairPlanColumQRcode } from '@/columns/equipmentManagement/Repair.js';
import { PrintTplGetPageList } from '@/api/systemManagement/printTemplate.js';
export default {
    name: 'CreateRepasts',
    components: {
        createRepasts: () => import('./components/createRepasts.vue'),
        printPopup: () => import('./components/printPopup.vue'),
    },
    mixins: [mixins],
    data() {
        return {
            isShowPrint: false,
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            papamstree: {
                key: null,
                pageIndex: 1,
                pageSize: 20
            },
            valid: false,
            desserts: [],
            selectList: [],
            loading: false,
            RepairPlanColumQRcode,
            dialogType: '',
            fromQR: {
                Remark: '', //货架编号
                Code: '', // 备件编号
                BuyNum: null //入库数量
            }
        };
    },
    computed: {
        submitStatue() {
            return (this.fromQR.Remark || this.fromQR.Remark) && +this.fromQR.BuyNum > 0;
        },
        btnList() {
            return [
                {
                    text: this.$t('TPM_SBGL_SBBJGL._DYLLD'),
                    code: 'printCode',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBBJGL_CK_DYLLD'
                }
            ];
        }
    },
    created() {
        this.GetPrintTpl();
    },
    mounted() {
        this.RepastInfoGetPage();
    },
    activated() {
        this.GetPrintTpl();
    },
    methods: {
        openPrint() {
            this.isShowPrint = true
        },
        // 扫码录入
        getQRcodes() {
            this.$refs.QRcode.getQRcode();
        },
        // 获取查询结果
        getQRcodesRes(value) {
            let val = JSON.parse(value.text);
            let { Remark, SparePartsCode } = val;
            this.fromQR.Remark = Remark;
            this.fromQR.Code = SparePartsCode;
        },
        QRCcodeckick() {
            this.$refs.createRepast.showDialog = true;
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoGetPage();
        },
        // 列表查询
        async RepastInfoGetPage() {
            let params = {
                pageIndex: this.papamstree.pageIndex,
                pageSize: this.papamstree.pageSize
            };
            this.loading = true;
            const res = await GetPageSparePartsList(params);
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = (response || {}).data || [];
                this.pageOptions.total = response.dataCount;
                this.pageOptions.page = response.page;
                this.pageOptions.pageCount = response.pageCount;
                this.pageOptions.pageSize = response.pageSize;
            }
        },
        // 扫码出件数量
        async addSparePartlist() {
            let params = {
                Type: 2,
                Remark: this.fromQR.Remark,
                Code: this.fromQR.Code,
                WoId: this.fromQR.WoId,
                BuyNum: +this.fromQR.BuyNum
            };
            const res = await addSparePart(params);
            let { success, msg } = res;
            if (success) {
                this.RepastInfoGetPage();
                this.$refs.fromQR.reset();
            }
            this.$store.commit('SHOW_SNACKBAR', { text: msg || (success ? '操作成功' : '操作失败'), color: 'success' });
        },
        // 选额列表
        SelectedItems(item) {
            this.selectList = [...item];
            console.log(this.selectList[0]);
            this.fromQR.Remark = this.selectList[0]?.Parts1Unit;
            this.fromQR.Code = this.selectList[0]?.PartsCode1;
            this.fromQR.BuyNum = this.selectList[0]?.Parts1Num;
            this.fromQR.WoId = this.selectList[0]?.ID;
        },
        // 表单操作
        tableClick(item, type) {
            switch (type) {
                case 'printCode':
                    // item.QRCode = JSON.stringify({
                    //     Remark: item.Remark,
                    //     SparePartsCode: item.SparePartsCode
                    // });
                    this.$nextTick(this.PrintTemplateFn({ table: [item] }));
                    return;
            }
        },
        async GetPrintTpl() {
            let params = {
                type: 'batch_print_material_requisition',
                // type: 'maintainCode',
                pageIndex: 1,
                pageSize: 10
            };
            const res = await PrintTplGetPageList(params);
            let { success, response } = res;
            if (success) {
                // 处理有效模板
                let data = response?.data.filter(item => item.Status == 1) ?? [];
                let datas = data[0]?.TplJson;
                this.dataTemplate = JSON.parse(datas);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.connet {
    height: calc(100vh - 160px);
    overflow: auto;

    .text-right {
        text-align: right;
    }
}
</style>
export const scrapManagementColumn = [
    { text: '序号', value: 'Index', width: 60 },
    { text: '原料报废单号', value: 'UselessOrderid', width: 140 },
    { text: '楼层', value: 'Floor', width: 100, dictionary: true },
    { text: '仓库编号', value: 'WarehouseId', width: 200 },
    { text: '仓库名称', value: 'WarehouseName', width: 160 },
    { text: '最近修改时间', value: 'ModifyDate', width: 160 },
    { text: '最近修改人', value: 'ModifyUserId', width: 120 },
    { text: '创建时间', value: 'CreateDate', width: 160 },
    { text: '创建人', value: 'CreateUserId', width: 120 },
    { text: '操作', value: 'actions', width: 180, sortable: true },
]

export const materialColumn = [
    { text: '序号', value: 'Index', width: 90 },
    { text: '物料编码', value: 'MaterialCode', width: 140 },
    { text: '物料名称', value: 'Materialname', width: 140 },
    { text: '数量', value: 'Num', width: 90, semicolonFormat: true },
    { text: '计量单位', value: 'Unit', width: 100 },
    { text: '物料批次', value: 'Batchcode', width: 140 },
    { text: '生成日期', value: 'Bringoutdate', width: 140 },
    { text: '追溯批次', value: 'Backbatchcode', width: 140 },
]
export const materialPopupColumn = [
    { text: '序号', value: 'Index', width: 90 },
    { text: '物料编码', value: 'Materialcode', width: 140 },
    { text: '物料名称', value: 'Materialname', width: 140 },
    { text: '数量', value: 'Num', width: 90, semicolonFormat: true },
    { text: '计量单位', value: 'Unit', width: 100 },
    { text: '物料批次', value: 'Batchcode', width: 140 },
    { text: '生成日期', value: 'Bringoutdate', width: 140 },
    { text: '追溯批次', value: 'Backbatchcode', width: 140 },
    { text: '操作', value: 'actions', width: 100, sortable: true },
]
import request from '@/util/request';
import { configUrl } from '@/config';
import { getRequestResources } from '@/api/fetch';
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM; // 配置服务url

//保存流程图明细
export function EquipLinkSaveAll(data) {
    return request({
        url: baseURL + '/api/EquipLink/SaveAll',
        method: 'post',
        data
    });
}
//获取流程图详情
export function EquipLinkGetGraphList(data) {
    return request({
        url: baseURL + '/api/EquipLink/GetGraphList?GroupId=' + data,
        method: 'get',
        data
    });
}
//获取数物理模型Tree数据新接口
export function GetEquipmentTreeNew(data) {
    return request({
        url: baseURL + '/api/Equipment/GetEquipmentTreeNew',
        method: 'post',
        data
    });
}
//获取数物理模型Tree数据
export function GetEquipmentTree(data) {
    return request({
        url: baseURL + '/api/Equipment/GetEquipmentTree',
        method: 'post',
        data
    });
}
// 点击Tree获取详情table
export function GetEquipmentPageList(data) {
    return request({
        url: baseURL + '/api/Equipment/GetPageList',
        method: 'post',
        data
    });
}
// 新增物理模型组织
export function EquipmentSaveForm(data) {
    return request({
        url: baseURL + '/api/Equipment/SaveForm',
        method: 'post',
        data
    });
}
// 删除列表
export function EquipmentDelete(data) {
    return request({
        url: baseURL + '/api/Equipment/Delete',
        method: 'post',
        data
    });
}

// 属性扩展

// 列表查询
export function EquipmentGetPropertyValuePageList(data) {
    return request({
        url: baseURL + '/api/Equipment/GetPropertyValuePageList',
        method: 'post',
        data
    });
}
// 新增& 修改
export function EquipmentAttrSaveForm(data) {
    return request({
        url: baseURL + '/api/Equipment/SaveEquipmentPropertyValue',
        method: 'post',
        data
    });
}
// 删除
export function EquipmentAttrDelete(data) {
    return request({
        url: baseURL + '/api/Equipment/DeleteEquipment',
        method: 'post',
        data
    });
}

// 导出
export function EquipmentAttrExport(data) {
    const base = 'baseURL_DFM'
    const api = '/api/EquipmentView/ExportEquipmentView'
    return getRequestResources(base, api, 'post', data);
}

// 套件配置 查询

export function GetEquipmentClassList(data) {
    return request({
        url: baseURL + '/api/Equipment/GetEquipmentClassList',
        method: 'post',
        params: data
    });
}

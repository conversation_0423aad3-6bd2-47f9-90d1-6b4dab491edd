<template>
    <v-card>
        <v-card-title class="d-flex text-h6 justify-space-between primary lighten-2" primary-title>
            {{ operaObj.ID ? $t('DFM_XBHJW.editPosition') : $t('DFM_XBHJW.addPosition') }}
            <v-icon @click="closePopup">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
            <v-form ref="form" v-model="valid" class="mt-8">
                <v-row>
                    <v-col :cols="12" :lg="6">
                         <!-- label="货架位代码" -->
                        <v-text-field v-model="form.BinCode" :rules="rules.BinCode" :label="$t('$vuetify.dataTable.DFM_XBHJW.BinCode')" required dense outlined></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                         <!-- label="货架位名称" -->
                        <v-text-field v-model="form.BinName" :rules="rules.BinName" :label="$t('$vuetify.dataTable.DFM_XBHJW.BinName')" dense outlined></v-text-field>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                         <!-- label="货架位类型" -->
                        <v-select v-model="form.BinType" :rules="rules.BinType" :label="$t('$vuetify.dataTable.DFM_XBHJW.BinType')" :items="binTypeList" required dense outlined></v-select>
                    </v-col>
                    <v-col :cols="12" :lg="6">
                         <!-- label="有效标志" -->
                        <v-select v-model="form.Status" :rules="rules.Status" :label="$t('$vuetify.dataTable.DFM_XBHJW.Status')" :items="statusItems" required dense outlined></v-select>
                    </v-col>
                    <v-col :cols="12" :lg="12">
                         <!-- label="货架位位置描述" -->
                        <v-textarea v-model="form.BinDescription" :label="$t('$vuetify.dataTable.DFM_XBHJW.BinDescription')" rows="2" dense outlined></v-textarea>
                    </v-col>
                    <v-col :cols="12" :lg="12">
                         <!-- label="备注" -->
                        <v-textarea v-model="form.Remark" :label="$t('$vuetify.dataTable.DFM_XBHJW.Remark')" rows="2" dense outlined></v-textarea>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>

        <v-divider></v-divider>

        <v-card-actions>
            <v-checkbox v-model="checkbox" :label="$t('GLOBAL._QDBGBTC')"></v-checkbox>
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="submitForm">{{ $t('GLOBAL._QD') }}</v-btn>
            <v-btn color="normal" @click="closePopup">{{ $t('GLOBAL._GB') }}</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { saveRackingBinForm } from '@/api/factoryPlant/sideLine.js';
export default {
    props: {
        operaObj: {
            type: Object,
            default: () => {}
        },
        currentSelectId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            checkbox: true,
            valid: true,
            form: {
                ID: '',
                BinCode: '',
                BinName: '',
                BinType: '',
                Status: '',
                Remark: '',
                BinDescription: ''
            },
            // 工装/夹具/刀具/辅料
            binTypeList: ['工装', '夹具', '刀具', '辅料'],
            statusItems: ['Y', 'N'],
            rules: {
                BinCode: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                BinName: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                BinType: [v => !!v || this.$t('GLOBAL._MANDATORY')],
                Status: [v => !!v || this.$t('GLOBAL._MANDATORY')]
            }
        };
    },
    watch: {
        operaObj: {
            handler(curVal) {
                for (const key in this.form) {
                    if (Object.hasOwnProperty.call(this.form, key)) {
                        this.form[key] = curVal[key];
                    }
                }
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        // 表单提交
        async submitForm() {
            if (this.$refs.form.validate()) {
                const res = await saveRackingBinForm({ ...this.form, RackingId: this.currentSelectId });
                const { success, msg } = res;
                if (success) {
                    this.$store.commit('SHOW_SNACKBAR', { text: msg, color: 'success' });
                    if (this.operaObj.ID || this.checkbox) {
                        this.$emit('handlePopup', 'refresh');
                    }else{
                        this.$refs.form.reset();
                    }
                }
                return false;
            }
        },
        closePopup() {
            this.$emit('handlePopup', 'close');
        }
    }
};
</script>

<style lang="scss" scoped>
.col-lg-6.col-12,
.col-lg-12 {
    padding: 0 12px;
}
</style>

// 计件薪资
export const pieceworkWageColums = [
    {
        text: '序号',
        value: 'Index',
        width: 70,
        sortable: true
    },
    {
        text: '员工号',
        value: 'StaffCode',
        width: 120,
        sortable: true
    },
    {
        text: '姓名',
        value: 'StaffName',
        width: 120,
        sortable: true
    },
    {
        text: '工段',
        value: 'Segment',
        width: 140,
        sortable: true
    },
    {
        text: '产线',
        value: 'ProductLine',
        width: 140,
        sortable: true
    },
    {
        text: '物料号',
        value: 'MaterialCode',
        width: 120,
        sortable: true
    },
    {
        text: '记录日期',
        value: 'RecordDate',
        width: 160,
        sortable: true
    },
    {
        text: '开始时间',
        value: 'BeginTime',
        width: 160,
        sortable: true
    },
    {
        text: '结束时间',
        value: 'EndTime',
        width: 160,
        sortable: true
    },
    {
        text: '班组',
        value: 'Taem',
        width: 120,
        sortable: true
    },
    {
        text: '班次',
        value: 'Shift',
        width: 120,
        sortable: true
    },
    {
        text: '产量',
        value: 'Yield',
        width: 120,
        sortable: true
    },
    {
        text: '计件工资',
        value: 'PieceRate',
        width: 120,
        sortable: true
    },
    {
        text: '计件超产奖励',
        value: 'PieceAward',
        width: 120,
        sortable: true
    },
    {
        text: '计件岗位',
        value: 'PieceRatePosition',
        width: 160,
        sortable: true
    },
    {
        text: '有效工时',
        value: 'WorkTimes',
        width: 120,
        sortable: true
    },
    {
        text: '员工小时基准产量',
        value: 'BasicOutputHours',
        width: 140,
        sortable: true
    },
    {
        text: 'UWB 区域编码',
        value: 'GeoNum',
        width: 130,
        sortable: true
    }
]
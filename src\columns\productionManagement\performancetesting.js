export const TestingColum = [
    // {
    //     text: '序号',
    //     value: 'Index',
    //     sortable: true
    // },
    {
        text: '工序',
        value: 'CurrentProcName',
        sortable: true,
        width: 100
    },
    {
        text: '产品线',
        value: 'FullLineName',
        width: 140,
        sortable: true
    },
    {
        text: '工段',
        value: 'CompanyName',
        width: 140,
        sortable: true
    },
    {
        text: '工单号',
        value: 'WoCode',
        width: 100,
        sortable: true
    },
    {
        text: '产品料号',
        value: 'MaterialCode',
        width: 100,
        sortable: true
    },
    {
        text: '产品描述',
        value: 'MaterialDescription',
        width: 200,
        sortable: true
    },
    {
        text: '班组',
        width: 90,
        value: 'TeamName',
        sortable: true
    },
    {
        text: '班次',
        width: 90,
        value: 'ShiftName',
        sortable: true
    },
    {
        text: '生产批次',
        value: 'BatchNo',
        width: 140,
        sortable: true
    },
    {
        text: '批次数量(PCS)',
        width: 140,
        value: 'BatchQuantity',
        semicolonFormat: true,
        sortable: true
    },
   
    {
        text: '状态',
        width: 140,
        value: 'CurrentProcStatus',
        sortable: true
    },
    {
        text: '接受时间',
        value: 'ModifyDate',
        width: 170,
        sortable: true
    },
    { text: '操作', align: 'center',width: 200, value: 'actions', sortable: true }
];

export const TestingdetaisColum = [
    // {
    //     text: '序号',
    //     value: 'Index',
    //     sortable: true
    // },
    {
        text: '产线',
        value: 'CompanyName',
        width: 140,
        sortable: true
    },
    {
        text: '工单号',
        value: 'WoCode',
        width: 100,
        sortable: true
    },
    // {
    //     text: '产品料号',
    //     value: 'MaterialCode',
    //     width: 100,
    //     sortable: true
    // },
    // {
    //     text: '产品描述',
    //     value: 'MaterialDescription',
    //     width: 260,
    //     sortable: true
    // },
    {
        text: '测试批次',
        value: 'BatchNo',
        width: 100,
        sortable: true
    },
    {
        text: '档次',
        value: 'TestGrade',
        width: 90,
        sortable: true
    },
    {
        text: '批次数量(PCS)',
        width: 150,
        value: 'BatchQuantity',
        semicolonFormat: true,
        sortable: true
    },
    {
        text: '创建时间',
        value: 'CreateDate',
        width: 160,
        sortable: true
    },
    // {
        
    //     text: '状态',
    //     width: 140,
    //     value: 'CurrentProcStatus',
    //     sortable: true
    // }
    { text: '操作', align: 'center',width: 100, value: 'actions', sortable: true }
];

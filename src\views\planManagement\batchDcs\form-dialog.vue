<template>
    <el-dialog :title="dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')" :visible.sync="dialogVisible" width="700px"
      :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
      @close="dialogVisible = false">
      <el-form ref="dialogForm" :model="dialogForm" label-width="130px">
       

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="Id">{{dialogForm.id}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="工单ID">{{dialogForm.productionOrderId}}</el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="工单号" prop="poNo">
              <el-input v-model="dialogForm.poNo" placeholder="请输入工单号" />
            </el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="批次ID">{{dialogForm.batchtId}}</el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="批次号" prop="batchtNo">
              <el-input v-model="dialogForm.batchtNo" placeholder="请输入批次号" />
            </el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="物料ID">{{dialogForm.materialId}}</el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="物料代码" prop="materialCode">
              <el-input v-model="dialogForm.materialCode" placeholder="请输入物料代码" />
            </el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="物料名称">{{dialogForm.materialName}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="物料版本ID">{{dialogForm.materialVer}}</el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="单位" prop="unit">
              <el-input v-model="dialogForm.unit" placeholder="请输入单位" />
            </el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="单位ID">{{dialogForm.unitId}}</el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="标准需求数量" prop="standardQuantity">
              <el-input v-model="dialogForm.standardQuantity" placeholder="请输入标准需求数量" />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="计划数量" prop="planQuantity">
              <el-input v-model="dialogForm.planQuantity" placeholder="请输入计划数量" />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="dialogForm.status">
                <el-radio v-for="item in statusOptions" :key="item.dictValue" :label="item.dictValue">{{item.dictLabel}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :lg="24">
            <el-form-item label="下发数据" prop="sendData">
              <el-input type="textarea" v-model="dialogForm.sendData" placeholder="请输入下发数据"/>
            </el-form-item>
          </el-col>

          <el-col :lg="24">
            <el-form-item label="返回数据" prop="responseData">
              <el-input type="textarea" v-model="dialogForm.responseData" placeholder="请输入返回数据"/>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="dialogForm.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="创建时间">{{dialogForm.createdate}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="创建者">{{dialogForm.createuserid}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="更新时间">{{dialogForm.modifydate}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="更新者">{{dialogForm.modifyuserid}}</el-form-item>
          </el-col>

          <el-col :lg="12" v-if="opertype == 2">
            <el-form-item label="更新戳">{{dialogForm.updatetimestamp}}</el-form-item>
          </el-col>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small"
          @click="submit()">确定
        </el-button>
      </div>
    </el-dialog>
  </template>
  

<script>
  import {
    getBatchDcsDetail,
    saveBatchDcsForm
  } from "@/api/planManagement/batchDcs";

  export default {
    components:{
      
    },
    data() {
      return {
        dialogForm: {},
        dialogVisible: false,
        formLoading: false,
        lineOptions: [],
        targetLineOptions: [],
        currentRow: {},
        matInfo:{}
      }
    },
    mounted() {
    },
    methods: {
      submit() {
        saveBatchDcsForm(this.dialogForm).then(res=>{
          this.$message.success(res.msg)
          this.$emit('saveForm')
          this.dialogVisible = false
        })
      },
      show(data) {
        this.dialogForm = {}
        this.currentRow = data
        this.dialogVisible = true
        this.$nextTick(_ => {
          if(data.ID){
            this.getDialogDetail(data.ID)
          }
        })
      },
      getDialogDetail(id){
        getBatchDcsDetail(id).then(res => {
          this.dialogForm = res.response
        })
      },
    }
  }
  </script>
<template>
    <div>
        <div class="form-btn-list">
            <v-btn color="primary" v-has="'SBTZGL_SBWJ_ADD'" :disabled="!rowtableItem.ID" @click="btnClickEvet('addFile')">{{ $t('GLOBAL._XZ') }}</v-btn>
            <v-btn color="primary" v-has="'SBTZGL_SBWJ_ALLREMOVE'" :disabled="!deleteList.length" @click="btnClickEvet('delete')">{{ $t('GLOBAL._PLSC') }}</v-btn>
        </div>
        <Tables
            :footer="false"
            :page-options="pageOptions"
            :loading="loading"
            :btn-list="btnList"
            tableHeight="calc(100vh - 220px)"
            table-name="TPM_SBGL_SBTZGL_SBWJ"
            :headers="EquipFileColum"
            :desserts="desserts"
            @selectePages="selectePages"
            @tableClick="tableClick"
            @itemSelected="SelectedItems"
            @toggleSelectAll="SelectedItems"
        ></Tables>
        <createRepast ref="createRepast" :dialogType="dialogType" :tableItem="tableItem" :rowtableItem="rowtableItem"></createRepast>
    </div>
</template>
<script>
import { DocGetPageList, DeviceDocGetList, DeviceDocDelete, DocDelete, DeviceGetFileUrl } from '@/api/equipmentManagement/EquipParts.js';
import { EquipFileColum } from '@/columns/equipmentManagement/Equip.js';
import { Message } from 'element-ui';

export default {
    components: {
        createRepast: () => import('./components/createRepast.vue')
    },
    props: {
        rowtableItem: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            loading: false,
            showFrom: false,
            papamstree: {
                key: null,
                pageIndex: 1,
                pageSize: 20
            },
            //查询条件
            EquipFileColum,
            desserts: [],
            pageOptions: {
                total: 0,
                page: 1, // 当前页码
                pageSize: 20, // 一页数据
                pageCount: 1, // 页码分页数
                pageSizeitems: [10, 20, 50, 100, 500]
            },
            // 弹窗数据
            MyFlag: false,
            dialogType: '', // 弹窗类型
            tableItem: {}, // 选择操作数据
            deleteList: [], //批量选中
            hasChildren: {} // 新增字典详情判断-子节点才能新增
        };
    },
    computed: {
        btnList() {
            return [
                {
                    text: this.$t('GLOBAL._CK'),
                    code: 'check',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBTZGL_SBWJ_CHECK'
                },
                {
                    text: this.$t('GLOBAL._BJ'),
                    code: 'editFile',
                    type: 'primary',
                    icon: '',
                    authCode: 'SBTZGL_SBWJ_EDIT'
                },
                {
                    text: this.$t('GLOBAL._SC'),
                    code: 'delete',
                    type: 'red',
                    icon: '',
                    authCode: 'SBTZGL_SBWJ_DELETE'
                }
            ];
        }
    },
    async created() {
        // await this.RepastInfoTARGetPage();
    },
    methods: {
        // 设备target列表查询
        async RepastInfoTARGetPage(itemrow, flag) {
            let params = {
                DeviceCategoryId: '',
                DeviceId: '',
                pageIndex: 1,
                pageSize: 1000
            };
            let res;
            this.loading = true;
            if (itemrow) {
                if (flag) {
                    this.MyFlag = true;
                    params.DeviceId = itemrow.ID;
                    res = await DeviceDocGetList(params);
                } else {
                    this.MyFlag = false;
                    params.DeviceCategoryId = itemrow.ID;
                    res = await DocGetPageList(params);
                }
            }
            let { success, response } = res;
            if (success) {
                this.loading = false;
                this.desserts = response || {} || [];
            }
        },
        // 按钮操作
        btnClickEvet(val) {
            switch (val) {
                case 'addFile':
                    this.dialogType = val;
                    for (let k in this.$refs.createRepast.Fileform) {
                        if (k == 'FileList') {
                            this.$refs.createRepast.Fileform[k] = [];
                        } else {
                            this.$refs.createRepast.Fileform[k] = '';
                        }
                    }
                    this.$refs.createRepast.showDialog = true;
                    this.$refs.createRepast.clearFiles();
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        // 表单操作
        tableClick(item, type) {
            this.dialogType = type;
            this.tableItem = item;
            switch (type) {
                case 'editFile':
                    for (let k in this.$refs.createRepast.Fileform) {
                        for (let i in this.tableItem) {
                            if (k == i) {
                                this.$refs.createRepast.Fileform[k] = this.tableItem[i];
                            }
                        }
                    }
                    this.$refs.createRepast.Fileform.Size = this.tableItem.Size;
                    this.$refs.createRepast.Fileform.Uploaddate = this.tableItem.Uploaddate;
                    this.$refs.createRepast.Fileform.FilePath = this.tableItem.FilePath;
                    if (this.tableItem.Name != '' || this.tableItem.Name != null) {
                        this.$refs.createRepast.Fileform.FileList = [
                            {
                                name: this.tableItem.Name
                            }
                        ];
                    }
                    this.$refs.createRepast.showDialog = true;
                    return;
                case 'check':
                    if (item.FilePath == '' || item.FilePath == null) {
                        Message({
                            message: `${this.$t('GLOBAL.NoFile')}`,
                            type: 'warning'
                        });
                        return false;
                    }
                    this.getFile();
                    return;
                case 'delete':
                    this.deltable();
                    return;
            }
        },
        async getFile() {
            let params = {
                fileName: this.tableItem.FilePath
            };

            let res = await DeviceGetFileUrl(params);
            window.open(res.response);
        },
        // 删除
        deltable() {
            let params = [];
            // eslint-disable-next-line no-prototype-builtins
            if (this.tableItem.hasOwnProperty('ID')) {
                params = [this.tableItem.ID];
            } else {
                this.deleteList.forEach(item => {
                    params.push(item.ID);
                });
            }
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    let res;
                    console.log(this.MyFlag);
                    if (this.MyFlag) {
                        res = await DeviceDocDelete(params);
                    } else {
                        res = await DocDelete(params);
                    }
                    if (res.success) {
                        this.tableItem = {};
                        this.$store.commit('SHOW_SNACKBAR', { text: '刪除成功', color: 'success' });
                        this.RepastInfoTARGetPage(this.rowtableItem, this.MyFlag);
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },
        // 删除列表
        SelectedItems(item) {
            console.log('删除列表数组' + item);
            this.deleteList = item;
        },
        selectePages(v) {
            this.papamstree.pageIndex = v.pageCount;
            this.papamstree.pageSize = v.pageSize;
            this.RepastInfoTARGetPage(this.rowtableItem, this.MyFlag);
        }
    }
};
</script>
<style lang="scss" scoped></style>
